package com.dcits.ensemble.rb.business.api.component.fx;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbExchangeTranType;
import com.dcits.ensemble.rb.business.model.cm.restful.tae.TaeAcctMaintradesRow;
import com.dcits.ensemble.rb.business.model.cm.restful.tae.TaeAcctSubtradesRow;
import com.dcits.ensemble.rb.business.model.fx.MbExchangeModel;
import com.dcits.ensemble.rb.business.model.fx.MbExchangeOutModel;

import java.math.BigDecimal;
import java.util.List;

/**
 * The interface Mb exchange service.
 * <AUTHOR>
 */
public interface IMbExchangeService {

    /**
     * Change quote mb exchange out model.
     *
     * @param mbExchangeModels the mb exchange models
     * @return the mb exchange out model
     */
    MbExchangeOutModel changeQuote(List<MbExchangeModel> mbExchangeModels);

    /**
     * Execute mb exchange out model.
     *
     * @param mbExchangeModel the mb exchange model
     * @return the mb exchange out model
     */
    MbExchangeOutModel execute(MbExchangeModel mbExchangeModel);


    /**
     * 获取结售汇交易类型信息
     * @param mbExchangeModel mbExchangeModel
     * @return 结售汇交易类型
     */
    RbExchangeTranType getRbExchangeTranType(MbExchangeModel mbExchangeModel);

    /**
     * 构建账户信息
     * @param mbExchangeModel model
     */
    void buildRbAcctStandardModel(MbExchangeModel mbExchangeModel);

    /**
     * 构建结售汇主流水
     * @param tranAmt  交易金额
     * @return TaeAcctMainTradesRow
     */
    TaeAcctMaintradesRow buildMainTradesRow(BigDecimal tranAmt);

    /**
     * tae调用借方子流水组织
     * @param mbExchangeModel model
     * @param mainTradesRow maintradesRow
     * @param subtradesRowList subtradesRowList
     */
    void buildCrSubTaeRow(MbExchangeModel mbExchangeModel, TaeAcctMaintradesRow mainTradesRow, List<TaeAcctSubtradesRow> subtradesRowList);

    /**
     * tae与现金交易相关的流水组织
     * @param mbExchangeModel mbExchangeModel
     * @param mainTradesRow tae主流水
     * @param subtradesRowList 子流水集合
     */
    void buildCashSubTaeRow(MbExchangeModel mbExchangeModel, TaeAcctMaintradesRow mainTradesRow, List<TaeAcctSubtradesRow> subtradesRowList);

    /**
     * 账户余额检查
     * @param mbExchangeModel mbExchangeModel
     */
    void acctBalanceCheck(MbExchangeModel mbExchangeModel);

    /**
     * tae调用贷方子流水组织
     * @param mbExchangeModel mbExchangeModel
     * @param mainTradesRow tae主流水
     * @param subtradesRowList 子流水集合
     */
    void buildDrSubTaeRow(MbExchangeModel mbExchangeModel, TaeAcctMaintradesRow mainTradesRow, List<TaeAcctSubtradesRow> subtradesRowList);

    /**
     * 补充结售汇内部户本外币借贷流水
     * @param mbExchangeModel mbExchangeModel
     * @param mainTradesRow tae主流水
     * @param subtradesRowList 子流水集合
     */
    void buildInnerCrDrSubTaeRow(MbExchangeModel mbExchangeModel, TaeAcctMaintradesRow mainTradesRow, List<TaeAcctSubtradesRow> subtradesRowList);

    /**
     * 添加平盘，损益处理
     * @param mbExchangeModel mbExchangeModel
     * @param mainTradesRow tae主流水
     * @param subtradesRowList 子流水集合
     */
     void exchangeUncCalcExcute(TaeAcctMaintradesRow mainTradesRow, List<TaeAcctSubtradesRow> subtradesRowList, MbExchangeModel mbExchangeModel);


}
