package com.dcits.ensemble.rb.business.api.component.pcp.plan;

import com.dcits.ensemble.rb.business.common.constant.PcpDownEnum;
import com.dcits.ensemble.rb.business.model.pcp.PcpDownTranModel;
import com.dcits.ensemble.rb.business.model.pcp.PcpTranControlModel;
import com.dcits.ensemble.rb.business.model.pcp.PcpTranOutBaseModel;


/**
 * The interface Pcp down plan application.
 */
public interface IPcpDownPlanApplication {

    /**
     * Gets down plan type.
     *
     * @return the down plan type
     */
    PcpDownEnum.DownPlan getDownPlanType();

    /**
     * Down plan application pcp tran out base model.
     *
     * @param downTranModel    the down tran model
     * @param tranControlModel the tran control model
     * @return the pcp tran out base model
     */
    PcpTranOutBaseModel downPlanApplication(PcpDownTranModel downTranModel, PcpTranControlModel tranControlModel);
}
