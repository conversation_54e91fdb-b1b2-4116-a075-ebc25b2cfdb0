package com.dcits.ensemble.rb.business.api.component.cm;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbTranDef;
import com.dcits.ensemble.rb.business.model.cm.common.RbAcctStandardModel;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcct;

/**
 * The interface Common check.
 *
 * <AUTHOR>
 */
public interface ICommonCheck {


    /**
     * Tran in out acct check bean result.
     * 转入转出账户有效性检验
     *
     * @param mbacct    the mbacct
     * @param othMbacct the oth mbacct
     * @param rbTranDef the mb tran def
     * @return the bean result
     */
    void tranInOutAcctCheck(RbAcct mbacct, RbAcct othMbacct, RbTranDef rbTranDef);

    void tranInOutAcctCheck(RbAcctStandardModel acctStandardModel, RbAcctStandardModel othAcctStandardModel, RbTranDef rbTranDef);


}
