package com.dcits.ensemble.rb.business.api.unit.acct.amend;

import com.dcits.ensemble.rb.business.model.cm.amend.ProdAmendModel;

/**
 * <p>Name:  com.dcits.ensemble.rb.business.api.business.amend</p>
 * <p>Description: </p>
 *
 * @author: fangmt
 * @date 2018/3/19
 */
public interface IMbAcctProdAmend {
    /**

     * @param prodAmendModel
     */
    void prodAmend(ProdAmendModel prodAmendModel);

    /**

     * @param prodAmendModel
     */
    void check(ProdAmendModel prodAmendModel);
}
