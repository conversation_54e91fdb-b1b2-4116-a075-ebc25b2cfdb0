package com.dcits.ensemble.rb.business.api.component.pcp.reg;

import com.dcits.ensemble.rb.business.model.pcp.PcpTranInBaseModel;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpTranHist;

import java.math.BigDecimal;
import java.util.List;

/**
 * The interface Pcp tran hist reg.
 */
public interface IPcpTranHistReg {

    /**
     * Creat pcp tran reg list.
     *
     * @param transactionModel the transaction model
     * @param tranStatus       the tran status
     * @param tranAmt          the tran amt
     * @param message          the message
     * @return the list
     */
    List<RbPcpTranHist> creatPcpTranReg(PcpTranInBaseModel transactionModel, String tranStatus, BigDecimal tranAmt, String message);


    List<RbPcpTranHist> creatDrPcpTranReg(PcpTranInBaseModel transactionModel, String tranStatus, BigDecimal tranAmt, String message);

    List<RbPcpTranHist> creatCrPcpTranReg(PcpTranInBaseModel transactionModel, String tranStatus, BigDecimal tranAmt, String message);

    /**
     * Db r submit.
     *
     * @param list the list
     */
    void dbRSubmit(List<RbPcpTranHist> list);

    /**
     * Db t submit.
     *
     * @param list the list
     */
    void dbTSubmit(List<RbPcpTranHist> list);
}
