package com.dcits.ensemble.rb.business.api.component.td.tran;


import com.dcits.ensemble.rb.business.model.cm.transaction.AcctWtdInModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.BalAgrTypeEnum;
import com.dcits.ensemble.rb.business.model.cm.transaction.TermTranControlModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.TermTranOutModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.TransactionOutModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.bal.AcctTransactionInModel;

/**
 * The interface Term tran application.
 */
public interface ITermTranApplication {

    /**
     * Gets bal agr type.
     *
     * @return the bal agr type
     */
    BalAgrTypeEnum getBalAgrType();

    /**
     * Debt transaction term tran out model.
     *
     * @param acctWtdInModel the acct wtd in model
     * @param controlModel   the control model
     * @return the term tran out model
     */
    TermTranOutModel debtTransaction(AcctWtdInModel acctWtdInModel, TermTranControlModel controlModel);


    /**
     * Cret transaction transaction out model.
     *
     * @param inModel      the in model
     * @param controlModel the control model
     * @return the transaction out model
     */
    TransactionOutModel cretTransaction(AcctTransactionInModel inModel, TermTranControlModel controlModel);
}
