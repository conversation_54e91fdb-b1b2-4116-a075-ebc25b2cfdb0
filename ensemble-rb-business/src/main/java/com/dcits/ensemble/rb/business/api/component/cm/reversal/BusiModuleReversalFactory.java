package com.dcits.ensemble.rb.business.api.component.cm.reversal;

import com.dcits.ensemble.annotation.BusiUnit;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description:
 * @author: Chengliang
 * @date: 2018/04/08 15:11
 */
@Component
@BusiUnit
public class BusiModuleReversalFactory implements ApplicationContextAware {

    private static Map<String, IBusiModuleReversal> busiModuleReversalMap;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, IBusiModuleReversal> map = applicationContext.getBeansOfType(IBusiModuleReversal.class);
        busiModuleReversalMap = new HashMap<>(5);
        for (IBusiModuleReversal value : map.values()) {
            busiModuleReversalMap.put(value.getBusiSourceModule(), value);
        }
    }

    /**
     * 冲正业务实现
     * 按照模块划分
     * @param sourceModule 模块
     * @return
     */
    public static IBusiModuleReversal getBusiModuleReversal(String sourceModule) {
        return busiModuleReversalMap.get(sourceModule);
    }
}
