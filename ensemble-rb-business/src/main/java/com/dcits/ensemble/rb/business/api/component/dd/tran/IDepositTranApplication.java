package com.dcits.ensemble.rb.business.api.component.dd.tran;

import com.dcits.ensemble.rb.business.model.cm.transaction.BalAgrTypeEnum;
import com.dcits.ensemble.rb.business.model.cm.transaction.TransactionControlModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.TransactionOutModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.bal.AcctTransactionInModel;

/**
 * The interface Deposit tran application.
 *
 * <AUTHOR>
 */
public interface IDepositTranApplication {

    /**
     * Gets bal agr type.
     *
     * @return the bal agr type
     */
    BalAgrTypeEnum getBalAgrType();

    /**
     * Debt transaction transaction out model.
     *
     * @param inModel      the in model
     * @param controlModel the control model
     * @return the transaction out model
     */
    TransactionOutModel debtTransaction(AcctTransactionInModel inModel, TransactionControlModel controlModel);


    /**
     * Cret transaction transaction out model.
     *
     * @param inModel      the in model
     * @param controlModel the control model
     * @return the transaction out model
     */
    TransactionOutModel cretTransaction(AcctTransactionInModel inModel, TransactionControlModel controlModel);


}
