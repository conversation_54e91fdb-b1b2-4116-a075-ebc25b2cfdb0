package com.dcits.ensemble.rb.business.api.component.cm.transaction;

import com.dcits.ensemble.rb.business.model.cm.transaction.BalAgrTypeEnum;
import com.dcits.ensemble.rb.business.model.cm.BaseControlModel;

/**
 * The interface Deposit tran application 2.
 *
 * @param <T> the type parameter
 */
public interface IDepositTranApplication2<T> {
    /**
     * Gets bal agr type.
     *
     * @return the bal agr type
     */
    BalAgrTypeEnum getBalAgrType();

    /**
     * Before cret t.
     *
     * @param inModel      the in model
     * @param controlModel the control model
     * @return the t
     */
    T beforeCret(T inModel, BaseControlModel controlModel);

    /**
     * Before debt t.
     *
     * @param inModel      the in model
     * @param controlModel the control model
     * @return the t
     */
    T beforeDebt(T inModel, BaseControlModel controlModel);


    /**
     * After cret t.
     *
     * @param inModel      the in model
     * @param controlModel the control model
     * @return the t
     */
    T afterCret(T inModel, BaseControlModel controlModel);

    /**
     * After debt t.
     *
     * @param inModel      the in model
     * @param controlModel the control model
     * @return the t
     */
    T afterDebt(T inModel, BaseControlModel controlModel);
}
