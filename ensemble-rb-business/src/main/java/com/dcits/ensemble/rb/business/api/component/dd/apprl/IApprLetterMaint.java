package com.dcits.ensemble.rb.business.api.component.dd.apprl;

import com.dcits.ensemble.rb.business.model.acct.apprl.MbApprLetterModel;
import com.dcits.ensemble.rb.business.model.acct.apprl.MbApprSubLimitAmtModel;

import java.util.List;

/**
 * The interface Appr letter maint.
 */
public interface IApprLetterMaint {

    /**
     * Execute bean result.
     *
     * @param mbApprLetterModel       the mb appr letter model
     * @param mbApprSubLimitAmtModels the mb appr sub limit amt models
     * @param dealType                the deal type
     * @return the bean result
     */
    void execute(MbApprLetterModel mbApprLetterModel, List<MbApprSubLimitAmtModel> mbApprSubLimitAmtModels, String dealType);

}


