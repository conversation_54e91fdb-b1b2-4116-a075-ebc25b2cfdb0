package com.dcits.ensemble.rb.business.api.component.cm.reversal;

import com.dcits.ensemble.annotation.BusiUnit;
import com.dcits.ensemble.rb.business.common.constant.reversal.ReversalTypeEnum;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description:
 * @author: Chengliang
 * @date: 2018/04/08 15:11
 */
@Component
@BusiUnit
public class BusiTranReversalFactory implements ApplicationContextAware {

    private static Map<String, IBusiTranReversal> busiReversalMap;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, IBusiTranReversal> map = applicationContext.getBeansOfType(IBusiTranReversal.class);
        busiReversalMap = new HashMap<>(10);
        for (IBusiTranReversal value : map.values()) {
            busiReversalMap.put(value.getBusiClass().name(), value);
        }
    }

    /**
     * 获取冲正实现
     * 按照业务类型划分
     * @param reversalType 冲正业务类型
     * @return
     */
    public static IBusiTranReversal getBusiReversal(ReversalTypeEnum reversalType) {
        return busiReversalMap.get(reversalType.name());
    }
}
