package com.dcits.ensemble.rb.business.api.component.dd.dos;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcct;
import com.dcits.ensemble.rb.business.model.cm.common.RbAcctStandardModel;
import lombok.NonNull;

/**
 * 转久悬，转营业外 增加不收不付限制
 * 账户激活 解除不收不付限制
 */
public interface IDossAcctRestraints {

    // 增加不收不付限制
    void addRestraints(RbAcct rbAcct);

    // 解除不收不付限制
    void deleteRestraints(RbAcctStandardModel rbAcctStandardModel);

    // 增加只收不付限制
    void addRestraints054(RbAcct rbAcct);

    // 增加个人不动户只收不付限制
    void addRestraints021(RbAcct rbAcct);
    // 解除个人不动户只收不付限制
    void deleteRestraints021(RbAcctStandardModel rbAcctStandardModel);

    /**
     * 增加13个月未发生交易 未核实
     * @param mbAcct
     * @param batchNo
     */
    void insertUnConterRes(RbAcct mbAcct,String batchNo);

}
