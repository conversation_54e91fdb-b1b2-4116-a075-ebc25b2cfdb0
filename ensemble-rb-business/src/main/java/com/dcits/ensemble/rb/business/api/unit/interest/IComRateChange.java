package com.dcits.ensemble.rb.business.api.unit.interest;

import com.dcits.ensemble.rb.business.model.interest.RateAmendModel;

/**
 * Created by duanal on 2018/3/5.
 */
public interface IComRateChange {

    public enum OperateType {
        SAVE, UPDATE, DELETE
    }

    /**
     * 利率变更
     * @param internalKey
     * @param operateType
     * @param rateAmendModel
     * @return
     */

    void rateChange(Long internalKey, OperateType operateType, RateAmendModel rateAmendModel);
}
