package com.dcits.ensemble.rb.business.api.component.cm.transaction;


import com.dcits.ensemble.rb.business.model.cm.transaction.FinancialRegTransactionOutModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.TransactionControlModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.bal.FinancialRegTransactionInModel;


/**
 * 理财登记薄: 贷记事件EVENT
 *
 * <AUTHOR>
 * @date 2020/05/28
 */
public interface IFinancialRegCretEvent {

    /**
     * 理财登记薄: 贷记事件EVENT
     *
     * @param financialRegTransactionInModel 输入模型
     * @param transactionControlModel 控制模型
     * @return FinancialRegTransactionOutModel
     */
    FinancialRegTransactionOutModel execute(FinancialRegTransactionInModel financialRegTransactionInModel, TransactionControlModel transactionControlModel);
}
