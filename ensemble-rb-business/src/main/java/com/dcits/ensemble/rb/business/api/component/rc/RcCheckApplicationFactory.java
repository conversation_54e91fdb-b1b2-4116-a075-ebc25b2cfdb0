package com.dcits.ensemble.rb.business.api.component.rc;

import com.dcits.ensemble.rb.business.model.rc.RcRuleTypeEnum;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * The type Acct impound application factory.
 * <AUTHOR>
 */
@Component

public class RcCheckApplicationFactory implements ApplicationContextAware {

    private static Map<RcRuleTypeEnum, IRcCheckApplication> ruleCheckMap;

    /**
     * Gets ruleCheck application.
     *
     * @param type the type
     * @return the ruleCheck application
     */
    public static IRcCheckApplication getRcRuleApplication(RcRuleTypeEnum type) {
        return ruleCheckMap.get(type);
    }


    /**
     * @Description: 获取所有接口实现的class付给ruleCheckMap
     * @author: zj
     * @date: 2018/08/02``
     * @param applicationContext
     */
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, IRcCheckApplication> map = applicationContext.getBeansOfType(IRcCheckApplication.class);
        ruleCheckMap = new HashMap<>();
        for (IRcCheckApplication value : map.values()) {
            ruleCheckMap.put(value.getRcRuleTypeClass(), value);
        }
    }

}
