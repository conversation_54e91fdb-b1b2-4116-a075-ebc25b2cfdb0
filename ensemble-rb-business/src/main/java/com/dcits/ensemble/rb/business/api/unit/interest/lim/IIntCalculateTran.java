package com.dcits.ensemble.rb.business.api.unit.interest.lim;


import com.dcits.ensemble.rb.business.model.acct.MbAcctIntModel;
import com.dcits.ensemble.rb.business.model.acct.RbAcctMasterModel;
import com.dcits.ensemble.rb.business.model.interest.MbInterestModel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * ensemble Created by cheng.liang on 2016/3/15.
 */
public interface IIntCalculateTran {
    
    MbInterestModel getIntCalculate(MbAcctIntModel mbIntAccrModel);
    
    MbInterestModel getIntCalculate(RbAcctMasterModel rbAcctMasterModel);
    /**
     * 利息试算
     *
     * @param tranAmt
     * @param prodType
     * @param acctCcy
     * @param intClass
     * @param term
     * @param periodFreq
     * @param startDate
     * @param endDate
     * @param clientNo
     * @return
     */
    BigDecimal getIntCalculate(BigDecimal tranAmt, String prodType, String acctCcy, String intClass, String term, String periodFreq, Date startDate, Date endDate, String clientNo);


}
