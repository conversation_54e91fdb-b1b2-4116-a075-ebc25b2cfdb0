package com.dcits.ensemble.rb.business.api.unit.acct.maint;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbBaseAcct;
import com.dcits.ensemble.rb.business.model.acct.MbContactModel;
import com.dcits.ensemble.rb.business.model.acct.MbMaintEventModel;
import com.dcits.ensemble.rb.business.model.cm.pwd.MbAcctWithdrawTypeModel;
import com.dcits.ensemble.rb.business.model.cm.pwd.MbPwdModel;
import com.dcits.ensemble.rb.business.model.acct.schedule.MbAcctScheduleModel;
import com.dcits.ensemble.rb.business.model.acct.settle.MbAcctSettleModel;
import com.dcits.ensemble.rb.business.model.cm.common.BaseOutEventModel;
import com.dcits.ensemble.rb.business.model.cm.common.RbAcctStandardModel;

import java.util.List;

/**
 * <AUTHOR>
 * @name AcctMaintEvent
 * @description
 * @create 2018/3/5 :20:32
 **/
public interface IAcctMaintEvent {


    /**
     * 账户信息维护事件
     * @param mbMaintEventModel
     * @param mbContactModels
     * @param mbAcctSettleModels
     * @param mbAcctWtdTypeModels
     * @param mbPwdModels
     * @param mbAcctScheduleModels
     * @return
     */
    BaseOutEventModel process(MbMaintEventModel mbMaintEventModel, List<MbContactModel> mbContactModels, List<MbAcctSettleModel> mbAcctSettleModels,
                              List<MbAcctWithdrawTypeModel> mbAcctWtdTypeModels, List<MbPwdModel> mbPwdModels, List<MbAcctScheduleModel> mbAcctScheduleModels) ;
    /*
    * baseService登记mbAmend使用
    * @param mbAmendModel 业务模型
     */

    public void acctActive(RbAcctStandardModel acctStandardModel, String passwordType, String password, String passwordEffectDate);

    /**
     * 更新支付方式
     * @param mbMaintEventModel
     * @param mbAcctWtdTypeModels
     * @param mbPwdModels
     * @param rbBaseAcct
     */
    void updateWithdrawTypeInfo(MbMaintEventModel mbMaintEventModel, List<MbAcctWithdrawTypeModel> mbAcctWtdTypeModels, List<MbPwdModel> mbPwdModels, RbBaseAcct rbBaseAcct, List<MbContactModel> mbContactModels);
}

