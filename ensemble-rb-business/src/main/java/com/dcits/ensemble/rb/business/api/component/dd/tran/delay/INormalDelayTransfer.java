package com.dcits.ensemble.rb.business.api.component.dd.tran.delay;

import com.dcits.ensemble.rb.business.model.cm.transaction.TransactionControlModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.bal.AcctTransactionInModel;

/**
 *
 */

/**
 * <p>name: INormalDelayTransfer</p>
 * <p>description: <p>
 * 功能实现:普通到账登记接口
 * 功能范围:
 * 1.如果到帐方式选择普通到账，则活期转账会调用本接口进行普通转账信息登记
 *
 * <AUTHOR>
 * @date 2020/03/30
 */

public interface INormalDelayTransfer {
    /**
     * 普通到账的转账记录进行登记
     *
     * <AUTHOR>
     * @param acctTransactionInModel
     * @param transactionControlModel
     */
    void normalDelayTransfer(AcctTransactionInModel acctTransactionInModel, TransactionControlModel transactionControlModel);
}
