package com.dcits.ensemble.rb.business.api.component.fee;

import com.dcits.ensemble.rb.business.model.fee.MbServDetailModel;
import com.dcits.ensemble.rb.business.model.fee.FeeQueryIn;

import java.util.List;

/**
 * The interface Fee inquiry.
 */
public interface IFeeInquiry {

    /**
     * Fee inquiry core IFeeInquiry out.
     *
     * @param in the in
     * @return the core IFeeInquiry out
     */
    List<MbServDetailModel> feeInquiry(FeeQueryIn in);

}
