package com.dcits.ensemble.rb.business.api.component.cd.corpcd;

import com.dcits.ensemble.rb.business.model.cm.SettleCardModel;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbSettleCardReal;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbSettleCardRealHist;

import java.util.List;

/**
 * The interface Settle card service.
 */
public interface ISettleCardService {
    /**
     * Sets card renew.
     *
     * @param settleCardModel the settle card model
     */
    void settleCardRenew(SettleCardModel settleCardModel,String clientNo);

    /**
     * Gets core settle card.
     *
     * @param cardNo the card no
     * @return the core settle card
     */
    SettleCardModel getCoreSettleCard(String cardNo,String clientNo);

    /**
     * Register settle card tran hist.
     *
     * @param settleCardModel the settle card model
     */
    void registerSettleCardTranHist(SettleCardModel settleCardModel);

    /**
     * Convert to settle card hist list.
     *
     * @param settleCardRealList the settle card real list
     * @return the list
     */
    List<RbSettleCardRealHist> convertToSettleCardHist(List<RbSettleCardReal> settleCardRealList);

    /**
     * Sets card default acct change.
     *
     * @param mbSettleCardReal the mb settle card real
     */
    void settleCardDefaultAcctChange(RbSettleCardReal mbSettleCardReal);
}
