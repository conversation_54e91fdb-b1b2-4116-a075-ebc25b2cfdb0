package com.dcits.ensemble.rb.business.api.component.cd.corpcd;

import com.dcits.ensemble.rb.business.model.cm.SettleCardModel;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbSettleCardReal;
import java.util.List;

/**
 * The interface Settle card common check.
 * @author: admin
 */
public interface ISettleCardCommonCheck {
    /**
     * Is settle card.
     *
     * @param cardNo the card no
     * @param clientNo the clientNo
     */
    void isSettleCard(String cardNo, String clientNo);

    /**
     * Associate base acct no info check.
     *
     * @param cardNo the card no
     * @param list   the list
     * @param client1 the client1
     */
    void associateBaseAcctNoInfoCheck(String cardNo, List<SettleCardModel> list,String client1);

    /**
     * Check tran status.
     *
     * @param mbSettleCardReal the mb settle card real
     * @param tranType         the tran type
     */
    void checkTranStatus(RbSettleCardReal mbSettleCardReal, String tranType);
    /**
     * Check tran status.
     * @param cardNo the card no
     * @param clientNo the clientNo
     */
    public void checkCardStatus(String cardNo,String clientNo) ;
    /**
     * Check card lost.
     *
     * @param cardNo the card no
     * @param clientNo the clientNo
     */
    void checkCardLost(String cardNo, String clientNo);

    /**
     *  collect No check
     * @param autoCollectFlag  the autoCollectFlag
     * @param autoCollectOrder the autoCollectOrder
     * @param associateInfo the associateInfo
     */
    void collectNocheck(String autoCollectFlag,String autoCollectOrder, List<SettleCardModel> associateInfo);
    /**
     * 校验是否是开卡行.
     *
     * @param tranBranch
     */
    void checkOpenBranch(String tranBranch);

    /**
     * 商用卡检验是否已挂失
     *
     * @param cardNo the card no
     * @param clientNo  clientNo
     */
    void checkCardLostKey(String cardNo,String clientNo);
    /**
     * 一卡多户和一户多卡不能交叉开
     *
     * @param cardNo the card no
     * @param clientNo the clientNo
     */
    void checkMulCard(String cardNo, String clientNo);

    /**
     * 检查转账对象是否预设账户
     * @param cardNo
     * @param clientNo
     * @param othBaseAcctNo
     */
    void checkPresetAcctNo(String cardNo,String clientNo, String othBaseAcctNo);
}
