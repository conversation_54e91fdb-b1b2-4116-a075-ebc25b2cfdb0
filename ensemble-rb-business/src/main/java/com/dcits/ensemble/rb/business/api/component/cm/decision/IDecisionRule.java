package com.dcits.ensemble.rb.business.api.component.cm.decision;


import com.dcits.ensemble.rb.business.entity.dbmodel.RbTranDef;

/**
 * The interface Decision rule.
 *
 * <AUTHOR>
 */
public interface IDecisionRule {

    /**
     * Is cash boolean.
     *
     * @param rbTranDef the mb tran def
     * @return the boolean
     */
    boolean isCash(RbTranDef rbTranDef);


    /**
     * Is tra boolean.
     *
     * @param rbTranDef the mb tran def
     * @return the boolean
     */
    boolean isTra(RbTranDef rbTranDef);
}
