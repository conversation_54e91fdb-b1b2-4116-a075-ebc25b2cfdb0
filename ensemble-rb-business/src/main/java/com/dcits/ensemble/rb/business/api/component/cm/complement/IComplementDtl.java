package com.dcits.ensemble.rb.business.api.component.cm.complement;


import com.dcits.ensemble.rb.business.model.cm.complement.ComplementEnum;
import com.dcits.ensemble.rb.business.model.cm.common.RbAcctStandardModel;

/**
 * The interface Complement dtl.
 */
public interface IComplementDtl {

    /**
     * Gets complement.
     *
     * @return the complement
     */
    ComplementEnum getComplement();

    /**
     * Complement process.
     *
     * @param standardModel the mb acct
     */
    void complementProcess(RbAcctStandardModel standardModel);
}
