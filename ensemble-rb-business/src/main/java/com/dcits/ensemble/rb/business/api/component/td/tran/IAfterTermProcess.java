package com.dcits.ensemble.rb.business.api.component.td.tran;

import com.dcits.ensemble.rb.business.model.cm.transaction.TermTranControlModel;
import com.dcits.ensemble.rb.business.model.cm.common.RbAcctStandardModel;

/**
 * The interface After term process.
 *
 * @param <T> the type parameter
 */
public interface IAfterTermProcess<T> {
    /**
     * Process.
     *
     * @param mode                 the mode
     * @param standardModel               the mb acct
     * @param termTranControlModel the term tran control model
     */
    void process(T mode, RbAcctStandardModel standardModel, TermTranControlModel termTranControlModel);

    /**
     * Gets sub type.
     *
     * @return the sub type
     */
    TermAfterUtil getSubType();

    /**
     * Gets type.
     *
     * @return the type
     */
    TermAfterUtil getType();
}
