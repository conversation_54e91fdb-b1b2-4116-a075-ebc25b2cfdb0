package com.dcits.ensemble.rb.business.api.unit.acct.settle;


import com.dcits.ensemble.rb.business.model.acct.settle.MbAcctSettleModel;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcct;

import java.util.List;

/**
 * Created by daiduan on 2016/10/20.
 */
public interface ISettleProcessor {


    /**
     * 结算处理过程
     *
     * @param settleModels  结算信息列表
     * @param reference 交易参考号
     * @param rbAcctSub 借据账户模型
     */
    void process(List<MbAcctSettleModel> settleModels, String reference, RbAcct rbAcctSub);

}
