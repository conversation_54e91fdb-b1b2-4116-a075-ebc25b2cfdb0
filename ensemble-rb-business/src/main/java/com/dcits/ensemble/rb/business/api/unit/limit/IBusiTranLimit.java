//package com.dcits.ensemble.rb.business.api.unit.limit;
//
//import com.dcits.ensemble.rb.business.model.limit.LmBusiModel;
//import com.dcits.ensemble.rb.business.model.limit.LmCheckOutModel;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//import java.util.Map;
//
///**
// * 限额检查/累计服务
// *
// * @author: Chengliang
// * @date: 2018/11/06 18:14
// */
//@Component
//public interface IBusiTranLimit {
//
//    /**
//     * 限额检查
//     *
//     * @param lmBusiModel 额度基础模型
//     * @param extendMap   额度扩展模型
//     * @param map         map
//     * @return TwoTuple first:ThrowExceptionDef second:List<String> 限额信息
//     */
//    LmCheckOutModel checkLimit(LmBusiModel lmBusiModel, Map extendMap, Map map);
//
//
//    /**
//     * 累计限额
//     *
//     * @param lmBusiModel 额度基础模型
//     * @param limitDefs   限额信息
//     */
//    void totalLimit(LmBusiModel lmBusiModel, List<String> limitDefs);
//
//    /**
//     * 累计临时限额
//     *
//     * @param lmBusiModel 额度基础模型
//     * @param limitDefs   限额信息
//     */
//    void totalLimitTemp(LmBusiModel lmBusiModel, List<String> limitDefs);
//}
