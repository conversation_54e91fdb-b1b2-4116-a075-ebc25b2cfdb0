package com.dcits.ensemble.rb.business.api.component.dd.dos;

import com.dcits.ensemble.rb.business.model.acct.close.MbAcctDossModel;
import com.dcits.comet.rpc.api.model.head.Result;

import java.util.List;

/**
 * The interface Acct hang service.
 */
public interface IAcctHangService {

    /**
     * Acct hang bean result.
     *
     * @param mbAcctDossModels the mb acct doss models
     * @param tranBranch       the tran tranBranch
     * @return the bean result
     */
    List<MbAcctDossModel> acctHang(List<MbAcctDossModel> mbAcctDossModels, String tranBranch);

    List<MbAcctDossModel> acctHang2(List<MbAcctDossModel> mbAcctDossModels, String tranBranch);

    /**
     * Check list.
     *
     * @param tranList   the tran list
     * @param tranBranch the tran tranBranch
     * @return the list
     */
    List<Result> check(List<MbAcctDossModel> tranList, String tranBranch);


}
