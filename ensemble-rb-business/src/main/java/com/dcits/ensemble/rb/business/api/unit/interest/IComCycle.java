package com.dcits.ensemble.rb.business.api.unit.interest;

import com.dcits.ensemble.rb.business.model.acct.MbAcctIntModel;
import com.dcits.ensemble.rb.business.model.cm.common.RbAcctStandardModel;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by duanal on 2018/3/5.
 */
public interface  IComCycle {


    /**
     * 调用统一计价平台联机结息
     * @param standardModel        //分户信息
     * @param lastCycleDate //上一结息日
     * @param intClass      //计息类型
     * @param movtStatus    //交易类型
     * @param tranAmt       //交易金额
     * @return
     */
    void cycleOnline(RbAcctStandardModel standardModel, String lastCycleDate, String intClass, String movtStatus,
                           BigDecimal tranAmt, BigDecimal intAccrued, BigDecimal intAdj, BigDecimal payInt, BigDecimal recInt, BigDecimal taxAmt,
                           List<MbAcctIntModel> mbAcctIntModels, String reference);
}
