package com.dcits.ensemble.rb.business.api.component.td;

import com.dcits.ensemble.rb.business.model.td.certtd.StageProdClassEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbDcPrecontract;

/**
 * The interface Stage prod class.
 */
public interface IStageProdClass {
    /**
     * Auto open rec dc precontract.
     *
     * @param dcPrecontracts the dc precontracts
     * @return the dc precontract
     */
    RbDcPrecontract autoOpenRec(RbDcPrecontract dcPrecontracts);

    /**
     * Gets stage prod class.
     *
     * @return the stage prod class
     */
    StageProdClassEnum getStageProdClass();

    /**
     * Insert open info dc precontract.
     *
     * @param dcPrecontract the dc precontract
     * @param message       the message
     * @return the dc precontract
     */
    RbDcPrecontract insertOpenInfo(RbDcPrecontract dcPrecontract, String message);

}
