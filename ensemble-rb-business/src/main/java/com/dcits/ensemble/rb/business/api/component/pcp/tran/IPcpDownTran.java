package com.dcits.ensemble.rb.business.api.component.pcp.tran;

import com.dcits.ensemble.rb.business.model.pcp.PcpDownTranModel;
import com.dcits.ensemble.rb.business.model.pcp.PcpTranControlModel;
import com.dcits.ensemble.rb.business.model.pcp.PcpTranOutBaseModel;


/**
 * The interface Pcp down tran.
 */
public interface IPcpDownTran {
    /**
     * Down tran pcp tran out base model.
     *
     * @param downTranModel    the down tran model
     * @param tranControlModel the tran control model
     * @return the pcp tran out base model
     */
    PcpTranOutBaseModel downTran(PcpDownTranModel downTranModel, PcpTranControlModel tranControlModel);
}
