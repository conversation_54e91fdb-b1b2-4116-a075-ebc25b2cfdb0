package com.dcits.ensemble.rb.business.api.component.cm.transaction;

import com.dcits.ensemble.rb.business.model.acct.RbAcctBalanceHistModel;
import com.dcits.ensemble.rb.business.model.cm.common.RbAcctStandardModel;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctIntDetail;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctEventRegister;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBalanceHist;

import java.util.List;


/**
 * 账户信息查询
 * <AUTHOR>
 */
public interface IRbAcctDetailInqComponent {

	RbAcctStandardModel getRbAcctDetails(String cardBaseAcctNo, String prodType, String ccy, String acctSeqNo, String clientNo);

	RbAcctStandardModel getRbAcctDetails(Long internalKey, String clientNo);
	
	List<RbAcctIntDetail> getActiveRbAcctIntDetailList(Long internalKey, String clientNo);

	List<RbAcctEventRegister> getAllRbAcctCycleHist(Long internalKey, String clientNo, String userId, String captDate);


	List<RbAcctBalanceHist> getAllBalanceHist(RbAcctBalanceHistModel model);
}
