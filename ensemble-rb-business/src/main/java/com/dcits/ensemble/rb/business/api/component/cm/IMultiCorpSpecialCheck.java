package com.dcits.ensemble.rb.business.api.component.cm;

import com.dcits.comet.rpc.api.model.BaseRequest;
import com.dcits.ensemble.business.ThrowExceptionDef;

import java.util.List;

/**
 * The interface Multi corp special check.
 */
public interface IMultiCorpSpecialCheck {
    /**
     * Gets company by special condition.
     *
     * @param baseRequest   the base request
     * @param exceptionDefs the exception defs
     */
    void getCompanyBySpecialCondition(BaseRequest baseRequest, List<ThrowExceptionDef> exceptionDefs);
}
