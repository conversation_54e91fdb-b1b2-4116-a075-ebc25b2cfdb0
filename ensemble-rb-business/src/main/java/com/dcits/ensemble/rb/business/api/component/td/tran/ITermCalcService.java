package com.dcits.ensemble.rb.business.api.component.td.tran;

import com.dcits.ensemble.rb.business.model.cm.transaction.TermTranControlModel;
import com.dcits.ensemble.rb.business.model.acct.FixedCallEnum;

/**
 * The interface Term calc service.
 */
public interface ITermCalcService {

    /**
     * Term calc.
     *
     * @param termTranControlModel the term tran control model
     */
    void termCalc(TermTranControlModel termTranControlModel);

    /**
     * Sets event movt.
     *
     * @param termTranControlModel the term tran control model
     */
    void setEventMovt(TermTranControlModel termTranControlModel);

    /**
     * Gets fixed call.
     *
     * @return the fixed call
     */
    FixedCallEnum getFixedCall();
}
