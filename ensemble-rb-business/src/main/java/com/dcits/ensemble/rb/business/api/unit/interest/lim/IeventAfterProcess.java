package com.dcits.ensemble.rb.business.api.unit.interest.lim;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcct;
import lombok.NonNull;

import java.math.BigDecimal;

/***
 * 核心调用利率倒起息借口
 */
public interface IeventAfterProcess {

    /***
     * 核心调用利率倒起息借口
     * @param isReversal
     * @param effectDate
     * @param reference
     * @param rbAcct
     * @param drCrFlag
     * @param tranAmt
     * @param tranCcy
     */
    void prevIntAdjust(@NonNull String isReversal, @NonNull String effectDate, @NonNull String reference, @NonNull RbAcct rbAcct, @NonNull String drCrFlag, @NonNull BigDecimal tranAmt, @NonNull String tranCcy);
}
