package com.dcits.ensemble.rb.business.api.unit.interest.transfer;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcct;

import java.math.BigDecimal;
import java.util.Map;

/**
 * Created by duanal on 2018/4/19.
 */
public interface ITransferUtil {

    /**
     *功能说明：查询账户截止指定日（封包日）日期的应计利息、罚息、复利  应收利息、罚息、复利
     * @param rbAcct
     * @param packDate
     * @param intFlag
     * @param odpFlag
     * @param odiFlag
     * @return
     */
    Map<String, BigDecimal> getIntAmtByDate(RbAcct rbAcct, String packDate, String intFlag, String odpFlag, String odiFlag);
}
