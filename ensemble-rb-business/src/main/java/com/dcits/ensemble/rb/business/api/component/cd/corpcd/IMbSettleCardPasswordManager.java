package com.dcits.ensemble.rb.business.api.component.cd.corpcd;

import com.dcits.ensemble.rb.business.model.cm.pwd.MbPwdModel;

/**
 * The interface Mb settle card password manager.
 */
public interface IMbSettleCardPasswordManager {

    /**
     * Check.
     *
     * @param mbPwdModel the mb pwd model
     * @param option     the option
     * @param prodType   the prod type
     */
    void check(MbPwdModel mbPwdModel, String option,String prodType);

    /**
     * Pwd modify.
     *
     * @param mbPwdModel the mb pwd model
     * @param prodType   the prod type
     */
    void pwdModify(MbPwdModel mbPwdModel,String prodType);

    /**
     * Pwd reset.
     *
     * @param mbPwdModel the mb pwd model
     * @param prodType   the prod type
     */
    void pwdReset(MbPwdModel mbPwdModel,String prodType);

    /**
     * Pwd un lock.
     *
     * @param mbPwdModel the mb pwd model
     * @param prodType   the prod type
     */
    void pwdUnLock(MbPwdModel mbPwdModel,String prodType);

    /**
     * Pwd active.
     *
     * @param mbPwdModel the mb pwd model
     * @param prodType   the prod type
     */
    void pwdActive(MbPwdModel mbPwdModel,String prodType);

    /**
     * Validate pwd.
     *
     * @param mbPwdModel the mb pwd model
     * @param prodType   the prod type
     */
    void validatePwd(MbPwdModel mbPwdModel,String prodType);
}
