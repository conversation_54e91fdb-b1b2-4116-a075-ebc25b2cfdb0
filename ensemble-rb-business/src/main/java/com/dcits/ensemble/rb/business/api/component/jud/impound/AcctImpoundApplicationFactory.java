package com.dcits.ensemble.rb.business.api.component.jud.impound;

import com.dcits.ensemble.rb.business.common.constant.AcctImpoundTypeEnum;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * The type Acct impound application factory.
 */
@Component

public class AcctImpoundApplicationFactory implements ApplicationContextAware {

    private static Map<AcctImpoundTypeEnum, IAcctImpoundApplication> acctImpoundMap;

    /**
     * Gets acct impound application.
     *
     * @param type the type
     * @return the acct impound application
     */
    public static IAcctImpoundApplication getAcctImpoundApplication(AcctImpoundTypeEnum type) {
        return acctImpoundMap.get(type);
    }

    /**
     * Gets all acct close application.
     *
     * @return the all acct close application
     */
    public static Map<AcctImpoundTypeEnum, IAcctImpoundApplication> getAllAcctCloseApplication() {
        return acctImpoundMap;
    }
    /**
     * @Description: 获取所有接口实现的class付给acctCloseMap
     * @author: wanglpg
     * @date: 2018/05/02``
     * @param applicationContext
     */
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, IAcctImpoundApplication> map = applicationContext.getBeansOfType(IAcctImpoundApplication.class);
        acctImpoundMap = new HashMap<>();
        for (IAcctImpoundApplication value : map.values()) {
            acctImpoundMap.put(value.getAcctImpoundClass(), value);
        }
    }

}
