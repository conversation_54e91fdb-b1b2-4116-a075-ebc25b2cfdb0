package com.dcits.ensemble.rb.business.api.component.rc;

import com.dcits.ensemble.rb.business.model.rc.RcRuleTypeEnum;
import com.dcits.ensemble.rb.business.model.rc.RcStandardModel;

/**
 * 黑名单校验对外接口
 * <AUTHOR>
 * @description 黑名单校验对外接口
 * @version  1.0
 * @update 20200812
 *
 **/
public interface IRcCheckApplication {

    /**
     * 黑名单校验对外接口
     * @param   null
     * @return  RcRuleTypeEnum
     *
     **/
    RcRuleTypeEnum getRcRuleTypeClass();



    /**
     * 黑名单校验对外接口
     * @param   rcStandardModel
     * @return  boolean
     *
     **/
    boolean execute(RcStandardModel rcStandardModel);
}
