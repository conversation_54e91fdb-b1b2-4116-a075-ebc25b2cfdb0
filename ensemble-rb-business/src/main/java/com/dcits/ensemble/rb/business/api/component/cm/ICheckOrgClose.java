package com.dcits.ensemble.rb.business.api.component.cm;

import com.dcits.comet.rpc.api.model.head.Result;

import java.util.List;

/**
 * The interface Check org close.
 */
public interface ICheckOrgClose {
    /**
     * Check list.
     *
     * @param branch the tranBranch
     * @param userId the user id
     * @return the list
     */
    List<Result> check(String branch, String userId);

    /**
     * Check user list.
     *
     * @param branch the tranBranch
     * @param userId the user id
     * @return the list
     */
    List<Result> checkUser(String branch, String userId);
}
