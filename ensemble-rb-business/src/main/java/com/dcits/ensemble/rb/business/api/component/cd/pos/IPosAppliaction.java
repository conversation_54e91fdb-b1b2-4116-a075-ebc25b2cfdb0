package com.dcits.ensemble.rb.business.api.component.cd.pos;

import com.dcits.ensemble.rb.business.entity.dbmodel.CdPosAuthReg;
import com.dcits.ensemble.rb.business.model.channel.pos.PosModel;
import com.dcits.ensemble.rb.business.model.channel.pos.RbPosConsumerTranModel;
import com.dcits.ensemble.rb.business.model.channel.pos.UCReverseModel;
import com.dcits.ensemble.rb.business.model.cm.common.RbAcctStandardModel;

/**
 * The interface
 * <AUTHOR>
 */
public interface IPosAppliaction {

    /**
     * pos预授权
     * @param posModel
     * @param rbAcctStandardModel
     * @return
     */
    CdPosAuthReg preAuth(PosModel posModel, RbAcctStandardModel rbAcctStandardModel)  ;
    /**
     * pos预授权模块检查
     * @param posModel
     * @param rbAcctStandardModel
     */
    void check(PosModel posModel, RbAcctStandardModel rbAcctStandardModel) ;

    /**
     * pos消费模块检查
     * @param mbPosConsumerTranModel
     */
    void posConsumeCheck(RbPosConsumerTranModel mbPosConsumerTranModel);


    public void reverse(UCReverseModel uCReverseModel);


}
