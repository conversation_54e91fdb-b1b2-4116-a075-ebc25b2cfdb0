package com.dcits.ensemble.rb.business.api.component.cm;

import com.dcits.comet.flow.component.annotation.Check;

import com.dcits.gravity.api.annotation.GravityComponent;
import java.util.List;

/**
 * The interface Rb cif check.
 */
public interface IRbCifCheck {

    /**
     * Check client bean result.
     *
     * @param clientNo the client no
     * @return the bean result
     */
    void checkClient(String clientNo);

    /**
     * Check block bean result.
     *
     * @param clientNo the client no
     * @return the bean result
     */
    void checkBlock(String clientNo);

    /**
     * Check client cards num bean result.
     *
     * @param clientNo the client no
     * @param prodType the prod type
     * @return the bean result
     */
//    @Check
//    @GravityComponent(navigationMenu = "no-group", name = "卡数量检查53")
    void checkClientCardsNum(String clientNo,String prodType);

    /**
     * Check cards num accredit bean result.
     *
     * @param clientNo the client no
     * @return the bean result
     */
//补换卡申请，客户下超过4张借记卡时，授权
	void checkCardsNumAccredit(String clientNo);

    /**
     * Check client expiry date bean result.
     *
     * @param clientNo the client no
     * @return the bean result
     */
    void checkClientExpiryDate(String clientNo);

    /**
     * Check client expiry date 1 bean result.
     *
     * @param clientNo   the client no
     * @param sourceType the source type
     * @param msgType    the msg type
     * @return the bean result
     */
    /*检查客户证件到期日是否到了提醒的范围
	*  *
	 * @param clientNo
	 * @return
	 * @description
	 * @version 1.0
	 * <AUTHOR>
	 * @update 2017年3月21日*/
	void checkClientExpiryDate1(String clientNo, String sourceType, String msgType);

    /**
     * Check client contact tel bean result.
     *
     * @param contacts the contacts
     * @return the bean result
     */
    //BeanResult checkClientContactTel(List<CifClientContactTbl> contacts);

}