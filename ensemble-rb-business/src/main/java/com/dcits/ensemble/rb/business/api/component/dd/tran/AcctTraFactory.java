package com.dcits.ensemble.rb.business.api.component.dd.tran;

import com.dcits.ensemble.rb.business.model.cm.transaction.BalAgrTypeEnum;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * The type Acct tra factory.
 */
@Component

public class AcctTraFactory implements ApplicationContextAware {

    private static Map<BalAgrTypeEnum, IAcctTraApplication> tranBeanMap;

    /**
     * Gets tran application.
     *
     * @return the tran application
     */
    public static IAcctTraApplication getTranApplication() {
        Iterator iterator = tranBeanMap.values().iterator();
        if (iterator.hasNext()) {
            return (IAcctTraApplication) iterator.next();
        }
        return null;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        Map<String, IAcctTraApplication> map = applicationContext.getBeansOfType(IAcctTraApplication.class);
        tranBeanMap = new HashMap<>(5);
        for (IAcctTraApplication value : map.values()) {
            tranBeanMap.put(value.getBalAgrType(), value);
        }
    }
}
