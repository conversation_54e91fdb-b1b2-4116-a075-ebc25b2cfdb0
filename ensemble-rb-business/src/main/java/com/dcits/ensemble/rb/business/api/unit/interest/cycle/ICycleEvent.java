package com.dcits.ensemble.rb.business.api.unit.interest.cycle;

import com.dcits.ensemble.rb.business.model.interest.CycleControlModel;
import com.dcits.ensemble.rb.business.model.interest.CycleTypeEnum;
import com.dcits.ensemble.rb.business.model.interest.MbIntCycleModel;

/**
 * Created by furongb on 2016/9/27.
 */
public interface ICycleEvent {

    CycleTypeEnum getCycleEventBusiness();

    /**
     * @param mbIntCycleModel
     * @param controlModel
     * @return
     */

    MbIntCycleModel execute(MbIntCycleModel mbIntCycleModel, CycleControlModel controlModel);
}
