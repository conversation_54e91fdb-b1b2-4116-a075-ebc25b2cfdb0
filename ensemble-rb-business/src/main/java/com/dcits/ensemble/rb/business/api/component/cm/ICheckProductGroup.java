package com.dcits.ensemble.rb.business.api.component.cm;

import com.dcits.comet.flow.component.annotation.Check;
import com.dcits.ensemble.rb.business.model.cm.common.RbProduct;
import com.dcits.gravity.api.annotation.GravityComponent;

import java.util.List;

public interface ICheckProductGroup {

    @Check
    @GravityComponent(navigationMenu = "no-group", name = "检查活期开户产品组")
    void checkProductGroupDefault(String mainProdType, List<RbProduct> subProdTypeList) ;
}
