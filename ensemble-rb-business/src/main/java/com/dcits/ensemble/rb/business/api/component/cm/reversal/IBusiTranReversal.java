package com.dcits.ensemble.rb.business.api.component.cm.reversal;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbTranHist;
import com.dcits.ensemble.rb.business.common.constant.reversal.ReversalTypeEnum;
import lombok.NonNull;

/**
 * @Description: 冲正业务接口
 * @author: cheng.liang
 * @date: 2016/6/3
 */
public interface IBusiTranReversal {

    /**
     * 获取限制类型枚举分类类型
     *
     * @return 业务类型
     */
    ReversalTypeEnum getBusiClass();

    /**
     * 检查
     *
     * @param rbTranHist 交易明细信息
     */
    boolean check(@NonNull RbTranHist rbTranHist, String clientNo);

    /**
     * 明细冲正
     *
     * @param rbTranHist 交易明细信息
     */
    void reversalDtl(@NonNull RbTranHist rbTranHist, String clientNo);

}
