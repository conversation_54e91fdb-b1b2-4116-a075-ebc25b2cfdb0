package com.dcits.ensemble.rb.business.api.unit.interest.lim;


import com.dcits.ensemble.rb.business.model.interest.MbIntAccrModel;
import com.dcits.ensemble.rb.business.model.interest.MbInterestModel;

/**
 * ensemble Created by cheng.liang on 2016/3/15.
 */
public interface IIntAccrTran {

    /**
     * 利息计算，已有分户的情况，重新计算利息
     * 适用定期支取，手工结息
     *
     * @param mbIntAccrModel
     * @return
     */
    MbInterestModel getIntAccr(MbIntAccrModel mbIntAccrModel);


}
