package com.dcits.ensemble.rb.business.api.unit.interest;

import com.dcits.ensemble.rb.business.model.entity.dbmodel.MbOrder;
import com.dcits.ensemble.rb.business.model.cm.prod.ProductAmt;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcct;
import com.dcits.ensemble.rb.business.model.acct.MbAcctIntModel;
import com.dcits.ensemble.rb.business.common.constant.BaseEvent;
import com.dcits.ensemble.rb.business.model.interest.MbIntRateModel;
import com.dcits.ensemble.rb.business.model.interest.RateAmendInfoModel;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/3/5
 */
public interface IComGetIntRate {

    /**
     * 获取执行利率开户
     * @param eventType
     * @param mbIntModel
     * @param productAmt
     * @return
     */
    List<MbIntRateModel> execute(BaseEvent eventType, MbAcctIntModel mbIntModel, ProductAmt productAmt);

    /**
     * 贷款利率变更查询
     *
     * @param
     */
    List<RateAmendInfoModel> execute(RbAcct rbAcct, List<MbOrder> mbOrders);

    /**
     * 签约阶梯利率查询
     * @param prodType
     * @param periodFreq
     * @param ccy
     * @param branch
     * @param effectDate
     * @return
     */
    List<MbIntRateModel> getMatrix(String prodType, String periodFreq, String ccy, String branch, String effectDate);
}
