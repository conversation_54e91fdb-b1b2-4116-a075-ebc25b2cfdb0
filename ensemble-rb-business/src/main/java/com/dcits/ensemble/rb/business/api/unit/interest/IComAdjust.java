package com.dcits.ensemble.rb.business.api.unit.interest;

import com.dcits.ensemble.base.data.EnsSysHead;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctIntDetail;
import com.dcits.ensemble.rb.business.model.cm.common.RbAcctStandardModel;
import com.dcits.ensemble.rb.business.model.cm.restful.tae.TaeAcctMaintradesRow;
import com.dcits.ensemble.rb.business.model.cm.restful.tae.TaeAcctSubtradesRow;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by duanal on 2018/3/5.
 */
public interface IComAdjust {

  /**
   * 计提调整
   */
  void updateAcctAdjust(RbAcctStandardModel acctStandardModel, RbAcctIntDetail rbAcctIntDetail, BigDecimal intAdj,String reason);

  /**
   * 计提调整
   */
  List<TaeAcctSubtradesRow> updateAcctAdjust(RbAcctStandardModel acctStandardModel, RbAcctIntDetail rbAcctIntDetail, BigDecimal intAdj, String reason, TaeAcctMaintradesRow maintradesRow);

  /**
   * 补计提 根据起始日期，终止日期及计息金额进行计提调整
   */

  BigDecimal addAdjust(Long internalKey, String startDate, String endDate, String intClass, BigDecimal intAmt, String remark);

//    void innerProcess(BusinessProcess businessProcess, EnsRequest request);

  EnsSysHead getLimSysHead(String messageCode, String messageType, String branch);
}
