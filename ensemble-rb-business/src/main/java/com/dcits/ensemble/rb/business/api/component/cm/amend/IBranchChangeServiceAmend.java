package com.dcits.ensemble.rb.business.api.component.cm.amend;

import java.util.List;
import java.util.Map;

/**
 * The interface Branch change service amend.
 */
public interface IBranchChangeServiceAmend {

    /**
     * Execute.
     *
     * @param braChangeMap the bra change map
     */
    void execute(Map<String, Object> braChangeMap,String stepName);

    /**
     * 查询撤并机构列表
     * @return
     */
    List<String> getChangeBranchs();

    /**
     * 查询撤并机构map关系表
     * @branchChangeType acct:单账户变更 branch:全机构撤并 file:批量变更
     * @return  key:oldBranch value:newBranch
     */
    Map<String, String> getChangeBranchMap(String branchChangeType);

    /**
     * 校验机构是否被撤并
     * @param branch
     */
    void checkChangeBranch(String branch);

}
