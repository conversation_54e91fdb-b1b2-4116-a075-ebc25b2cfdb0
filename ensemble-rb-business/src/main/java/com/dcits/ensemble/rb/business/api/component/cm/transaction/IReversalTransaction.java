package com.dcits.ensemble.rb.business.api.component.cm.transaction;

import com.dcits.ensemble.rb.business.model.cm.transaction.TransactionControlModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.TransactionOutModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.reversal.TransactionReversalInModel;

/**
 * The interface Reversal transaction.
 */
public interface IReversalTransaction {


    /**
     * Reversal transaction out model.
     *
     * @param transactionReversalInModel the transaction reversal in model
     * @param transactionControlModel    the transaction control model
     * @return the transaction out model
     */
    TransactionOutModel reversal(TransactionReversalInModel transactionReversalInModel, TransactionControlModel transactionControlModel) ;

    /**
     * 起小事物冲正，目前仅支持资金池归集冲正
     * @param transactionReversalInModel
     * @param transactionControlModel
     * @return
     */
    TransactionOutModel reversalForUp(TransactionReversalInModel transactionReversalInModel, TransactionControlModel transactionControlModel) ;
}
