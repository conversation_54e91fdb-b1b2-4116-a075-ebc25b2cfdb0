package com.dcits.ensemble.rb.business.api.unit.acct.settle;

import com.dcits.ensemble.rb.business.model.acct.settle.MbAcctSettleModel;
import com.dcits.ensemble.rb.business.model.acct.settle.SettleClassEnum;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 结算信息(增删改)操作接口
 *
 * <AUTHOR>
 * @date 2025/1/13 9:06
 */
public interface IRbSettleOperate {

    /**
     * 获取[结算信息操作组件]对应的[结算类别枚举]
     */
    SettleClassEnum getRbSettleClass();

    /**
     * 增加账户的结算信息(单条)
     *
     * @param rbAcctSettleModel 待增加结算信息模型
     */
    void addSettle(MbAcctSettleModel rbAcctSettleModel);

    /**
     * 增加账户的结算信息(多条)
     *
     * @param rbAcctSettleModels 待增加结算信息模型列表
     */
    void addSettle(List<MbAcctSettleModel> rbAcctSettleModels);

    /**
     * 删除账户的结算信息
     *
     * @param internalKey 账户内部键值
     * @param clientNo    客户号
     */
    void delSettle(Long internalKey, String clientNo);

    /**
     * 修改账户的结算信息
     *
     * @param rbAcctSettleModels 待更新结算信息模型列表
     */
    void updSettle(List<MbAcctSettleModel> rbAcctSettleModels);

    /**
     * 组装对应结算账户类别下的结算信息
     *
     * @param rbAcctSettleModel 结算信息模型
     */
    void assembleRbSettleInfo(MbAcctSettleModel rbAcctSettleModel);
}
