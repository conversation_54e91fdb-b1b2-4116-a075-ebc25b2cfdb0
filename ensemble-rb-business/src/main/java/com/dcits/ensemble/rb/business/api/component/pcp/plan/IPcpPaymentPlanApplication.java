package com.dcits.ensemble.rb.business.api.component.pcp.plan;

import com.dcits.ensemble.rb.business.common.constant.PcpPaymentEnum;
import com.dcits.ensemble.rb.business.model.pcp.PcpDownTranModel;
import com.dcits.ensemble.rb.business.model.pcp.PcpTranControlModel;
import com.dcits.ensemble.rb.business.model.pcp.PcpTranOutBaseModel;


/**
 * The interface Pcp down plan application.
 */
public interface IPcpPaymentPlanApplication {

    /**
     * Gets down plan type.
     *
     * @return the down plan type
     */
    PcpPaymentEnum.PaymentPlan getPaymentPlanType();

    /**
     * Down plan application pcp tran out base model.
     *
     * @param downTranModel    the down tran model
     * @param tranControlModel the tran control model
     * @return the pcp tran out base model
     */
    PcpTranOutBaseModel paymentPlanApplication(PcpDownTranModel downTranModel, PcpTranControlModel tranControlModel);
}
