package com.dcits.ensemble.rb.business.api.component.cm.complement;

import com.dcits.ensemble.rb.business.model.cm.complement.ComplementEnum;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * The type Complement factory.
 */
@Component

public class ComplementFactory implements ApplicationContextAware {

    private static Map<ComplementEnum, IComplementDtl> complementMap;

    private static ComplementEnum[] complementOrder = {ComplementEnum.PREV,
            ComplementEnum.MATURITY,ComplementEnum.ACCORD};

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, IComplementDtl> map = applicationContext.getBeansOfType(IComplementDtl.class);
        complementMap = new HashMap<>(3);
        for (IComplementDtl value : map.values()) {
            complementMap.put(value.getComplement(), value);
        }
    }

    /**
     * Get complement service list.
     *
     * @return the list
     */
    public static List<IComplementDtl> getComplementService(){
        List<IComplementDtl> list = new ArrayList<>();
        for(ComplementEnum order:complementOrder){
            list.add(complementMap.get(order));
        }
        return list;
    }
}
