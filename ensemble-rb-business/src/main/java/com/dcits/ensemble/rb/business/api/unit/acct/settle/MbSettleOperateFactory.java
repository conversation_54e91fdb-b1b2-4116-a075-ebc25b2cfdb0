package com.dcits.ensemble.rb.business.api.unit.acct.settle;

import com.dcits.ensemble.rb.business.model.acct.settle.SettleClassEnum;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by suwu on 2018/3/14.
 */
@Component

public class MbSettleOperateFactory implements ApplicationContextAware {
    private   static  Map<SettleClassEnum, com.dcits.ensemble.rb.business.api.unit.acct.settle.IMbSettleOperate> mbSettleBeanMap;
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, com.dcits.ensemble.rb.business.api.unit.acct.settle.IMbSettleOperate> map =applicationContext.getBeansOfType(com.dcits.ensemble.rb.business.api.unit.acct.settle.IMbSettleOperate.class);
        mbSettleBeanMap = new HashMap<>(8);
        for(com.dcits.ensemble.rb.business.api.unit.acct.settle.IMbSettleOperate value : map.values()){
            mbSettleBeanMap.put(value.getMbSettleClass(),value);

        }

    }
    public  static com.dcits.ensemble.rb.business.api.unit.acct.settle.IMbSettleOperate getMbSettleBusiness(SettleClassEnum typeEnum){
        return  mbSettleBeanMap.get(typeEnum);
    }
    public  static  Map<SettleClassEnum, com.dcits.ensemble.rb.business.api.unit.acct.settle.IMbSettleOperate> getAllMbSettleBusiness(){return  mbSettleBeanMap;}
    public  static  Map<SettleClassEnum, IMbSettleOperate> getNotAllMbSettleBusiness(SettleClassEnum... typeEnums){
        for(SettleClassEnum typeEnum :typeEnums){
            mbSettleBeanMap.remove(typeEnum);
        }
        return  mbSettleBeanMap;
    }
}
