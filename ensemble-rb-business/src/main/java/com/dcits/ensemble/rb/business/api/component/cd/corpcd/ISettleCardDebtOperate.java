package com.dcits.ensemble.rb.business.api.component.cd.corpcd;

import com.dcits.ensemble.rb.business.model.cm.SettleCardDebtModel;
import com.dcits.ensemble.rb.business.model.cm.SettleCardListModel;
import com.dcits.ensemble.rb.business.model.fee.MbServChargeModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.TransactionOutModel;
import com.dcits.comet.rpc.api.model.head.AppHead;
import com.dcits.comet.rpc.api.model.head.SysHead;

import java.util.List;

/**
* <AUTHOR>
* @Description /
* @Date 2020/6/12
* @Param
* @return
**/
public interface ISettleCardDebtOperate {

    /**
     * Excute transaction out model.
     *
     * @param settleCardDebtModel the settle card debt model
     * @return the transaction out model
     */
    TransactionOutModel excute(SettleCardDebtModel settleCardDebtModel, SysHead sysHead, AppHead appHead, SettleCardListModel debtSettleCardListModel, SettleCardListModel certSettleCardListModel, List<MbServChargeModel> mbServChargeModels);

    /**
     * Check.
     *
     * @param settleCardDebtModel the settle card debt model
     */
    void  check(SettleCardDebtModel settleCardDebtModel);
}
