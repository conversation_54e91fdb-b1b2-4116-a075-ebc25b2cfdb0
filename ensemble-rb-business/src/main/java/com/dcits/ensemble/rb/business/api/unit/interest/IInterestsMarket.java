package com.dcits.ensemble.rb.business.api.unit.interest;

import com.dcits.comet.util.json.JSONArray;
import com.dcits.ensemble.rb.business.model.acct.MbAcctIntModel;
import com.dcits.ensemble.rb.business.model.interest.MbIntAmtModel;
import com.dcits.ensemble.rb.business.model.interest.MbIntRateModel;
import com.dcits.ensemble.rb.business.model.interest.MbInterestModel;
import com.dcits.ensemble.rb.business.model.cm.prod.ProductAmt;
import com.dcits.ensemble.rb.business.model.interest.RbRevIntInput;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2015/9/11
 */
public interface IInterestsMarket {

    /**
     * 获取开户利率
     *
     * @param mbIntModel
     * @param productAmt
     * @return
     */
    List<MbIntRateModel> getOpenRate(MbAcctIntModel mbIntModel, ProductAmt productAmt);



    /**
     * 获取开户利率
     *
     * @param mbIntModel
     * @param productAmt
     * @return
     */
    List<MbIntRateModel> getAcrRate(MbAcctIntModel mbIntModel, ProductAmt productAmt);

    /**
     * 获取提前支取利率
     *
     * @param mbIntModel
     * @param productAmt
     * @return
     */
    List<MbIntRateModel> getPreRate(MbAcctIntModel mbIntModel, ProductAmt productAmt);

    /**
     * 获取分户利息
     *
     * @param mbAcctIntModel
     * @param productAmt
     * @return
     */
    MbInterestModel getCalcIntRate(MbAcctIntModel mbAcctIntModel, ProductAmt productAmt);

    /**
     * 获取利息
     *
     * @param mbIntModel
     * @return
     */
    List<MbIntAmtModel> calcInterests(MbAcctIntModel mbIntModel);

    /**
     * 冲正分户利息
     *
     * @param mbRevIntModels
     * @return
     */
    List<MbInterestModel> revInterests(JSONArray mbRevIntModels);

    /**
     * 获取冲正事件类型规则
     *
     * @param eventType
     * @return
     */
    String getReverseFlag(String eventType);

    /**
     * 获取阶梯利率
     * @param prodType
     * @param periodFreq
     * @param ccy
     * @param branch
     * @param effectDate
     * @return
     */
    List<MbIntRateModel> getMatrixRate(String prodType, String periodFreq, String ccy, String branch, String effectDate);

    /**
     * 冲正分户利息
     *
     * @param rbRevIntInputList
     * @return
     */
    List<MbInterestModel> revInterests2(List<RbRevIntInput> rbRevIntInputList);
}
