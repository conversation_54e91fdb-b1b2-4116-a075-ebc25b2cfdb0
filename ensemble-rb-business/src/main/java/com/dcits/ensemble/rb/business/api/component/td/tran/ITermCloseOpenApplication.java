package com.dcits.ensemble.rb.business.api.component.td.tran;


import com.dcits.ensemble.rb.business.model.acct.AcctOpenModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.MbTransactionModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.TermTranControlModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.TransactionOutModel;
import com.dcits.ensemble.rb.business.model.cm.restful.tae.TaeAcctMaintradesRow;
import com.dcits.ensemble.rb.business.model.cm.restful.tae.TaeAcctSubtradesRow;
import com.dcits.ensemble.rb.business.model.cm.transaction.AcctWtdInModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.BalAgrTypeEnum;
import com.dcits.ensemble.rb.business.model.cm.transaction.TermTranOutModel;

import java.util.List;
import java.util.Map;

/**
 * The interface Term tran application.
 */
public interface ITermCloseOpenApplication {

    /**
     * Gets bal agr type.
     *
     * @return the bal agr type
     */
    BalAgrTypeEnum getBalAgrType();

    /**
     * Debt transaction term tran out model.
     *
     * @param acctWtdInModel the acct wtd in model
     * @param controlModel   the control model
     * @return the term tran out model
     */
    TermTranOutModel debtTransaction(AcctWtdInModel acctWtdInModel, TermTranControlModel controlModel,
                                     TaeAcctMaintradesRow maintradesRow, List<TaeAcctSubtradesRow> subtradesRowList);


    /**
     * Cret transaction transaction out model.
     *
     * @param acctOpenModel      the in model
     * @param map the control model
     * @return the transaction out model
     */
    TransactionOutModel openTransaction(AcctOpenModel acctOpenModel, TaeAcctMaintradesRow maintradesRow, List<TaeAcctSubtradesRow> subtradesRowList,
                                        List<MbTransactionModel> mbTrans, Map<String,Object> map);
}
