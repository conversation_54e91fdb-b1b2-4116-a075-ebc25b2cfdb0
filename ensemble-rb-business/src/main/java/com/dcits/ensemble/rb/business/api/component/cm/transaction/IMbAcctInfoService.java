package com.dcits.ensemble.rb.business.api.component.cm.transaction;


import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcct;
import com.dcits.ensemble.rb.business.model.acct.amend.AcctBenefitModel;
import com.dcits.ensemble.rb.business.model.cm.common.RbAcctStandardModel;

import java.util.List;

/**
 * The interface Mb acct info service.
 *
 * <AUTHOR>
 */
public interface IMbAcctInfoService {


    /**
     * 查询活动账户信息
     * @param internalKey
     * @param clientNo
     * @return RbAcctStandardModel rbAcctStandardModel
     */
    RbAcctStandardModel getRbAcctInfo(Long internalKey, String clientNo);

    /**
     * 查询销户账户信息
     * @param internalKey
     * @param clientNo
     * @return RbAcctStandardModel rbAcctStandardModel
     */
    RbAcctStandardModel getCloseRbAcctInfo(Long internalKey, String clientNo);

    /**
     * 查询账户信息
     * @param internalKey
     * @param clientNo
     * @return RbAcctStandardModel rbAcctStandardModel
     */
    RbAcctStandardModel getRbAcctInfo(Long internalKey, String clientNo, boolean isClose);

    /**
     * 查询账户信息 对手账号匹配专用 add by yanghuip 20200824
     * @param internalKey
     * @param clientNo
     * @param contraAcctNo
     * @return RbAcctStandardModel rbAcctStandardModel
     */
    RbAcctStandardModel getRbAcctInfo(Long internalKey, String clientNo, String contraAcctNo);

    /***
     * 单位结算卡专用
     * @param cardNo
     * @return
     */
    RbAcctStandardModel getModelByCardNo(String cardNo);

    /***
     * 查询交易主体账户
     * @param cardBaseAcctNo
     * @param prodType
     * @param ccy
     * @param acctSeqNo
     * @return RbAcctStandardModel rbAcctStandardModel
     */
    RbAcctStandardModel getPriAcctStd(String cardBaseAcctNo, String prodType, String ccy, String acctSeqNo);

    /***
     * 查询交易主体账户
     * @param cardBaseAcctNo
     * @param prodType
     * @param ccy
     * @param acctSeqNo
     * @return RbAcctStandardModel rbAcctStandardModel
     */
    RbAcctStandardModel getPriAcctStdByStatusFlag(String cardBaseAcctNo, String prodType, String ccy, String acctSeqNo,String statusFlag);


    /***
     * 查询交易对手账户
     * @param cardBaseAcctNo
     * @param prodType
     * @param ccy
     * @param acctSeqNo
     * @return RbAcctStandardModel rbAcctStandardModel
     */
    RbAcctStandardModel getCpAcctStd(String cardBaseAcctNo, String prodType, String ccy, String acctSeqNo);

    /**
     * 查询账户信息
     * @param cardBaseAcctNo
     * @param prodType
     * @param ccy
     * @param acctSeqNo
     * @return RbAcctStandardModel rbAcctStandardModel
     */
    RbAcctStandardModel getRbAcctInfo(String cardBaseAcctNo, String prodType, String ccy, String acctSeqNo);
    /**
     * 查询汇丰活期账户信息
     *
     * @param cardBaseAcctNo cardBaseAcctNo
     * @param prodType       prodType
     * @param ccy            ccy
     * @param acctSeqNo      acctSeqNo
     * @param acctBranch     acctBranch
     * @return RbAcctStandardModel rbAcctStandardModel
     */
    RbAcctStandardModel getHsbcRbAcctInfo(String cardBaseAcctNo, String prodType, String ccy, String acctSeqNo, String acctBranch);

    /**
     *
     * @param actualBaseAcctNo
     * @param prodType
     * @param ccy
     * @param acctSeqNo
     * @param statusFlag 0-未销户 1-全部  2-销户
     * @return
     */
    RbAcctStandardModel getRbAcctInfoByStatus(String actualBaseAcctNo, String prodType, String ccy, String acctSeqNo, String statusFlag);

    /**
     * 查询主账户信息
     * @param cardBaseAcctNo
     * @return RbAcctStandardModel rbAcctStandardModel
     */
    RbAcctStandardModel getLeadRbAcctInfo(String cardBaseAcctNo);

    /**
     * 查询主账户信息
     * @param cardBaseAcctNo
     * @return RbAcctStandardModel rbAcctStandardModel
     */
    RbAcctStandardModel getLeadRbAcctInfoByStatus(String cardBaseAcctNo,String statusFlag);

    /**
     * 查询主账户信息
     * @param cardBaseAcctNo
     * @return RbAcctStandardModel rbAcctStandardModel
     */
    RbAcctStandardModel getLeadRbAcctInfoByStatus(String cardBaseAcctNo,String statusFlag,boolean queryLeadAcctOnly);

    /**
     * 查询销户信息
     * @param cardBaseAcctNo
     * @return RbAcctStandardModel rbAcctStandardModel
     */
    RbAcctStandardModel getCloseAcctInfo (String cardBaseAcctNo);

    /**
     * 查询全账户信息
     * @param cardBaseAcctNo
     * @param prodType
     * @param ccy
     * @param acctSeqNo
     * @return RbAcctStandardModel rbAcctStandardModel
     */
    RbAcctStandardModel getRbAcctCloseInfo(String cardBaseAcctNo, String prodType, String ccy, String acctSeqNo);
    /**
     * 根据证件信息查询全账户信息
     * @param documentType
     * @param documentId
     * @return RbAcctStandardModel rbAcctStandardModel
     */
    RbAcctStandardModel getRbAcctInfoDocument(String documentType,String documentId);

    /**
     * 简单查询账户信息
     * <AUTHOR>
     * @param cardBaseAcctNo
     * @param prodType
     * @param ccy
     * @param acctSeqNo
     * @return RbAcctStandardModel rbAcctStandardModel
     */
    RbAcctStandardModel getSimpleRbAcctInfo(String cardBaseAcctNo, String prodType, String ccy, String acctSeqNo);

    /**
     * 描述: 获取所有状态的账户信息
     * @param baseAcctNo
     * @param prodType
     * @param ccy
     * @param seqNo
     * @return
     */
    RbAcctStandardModel getRbAcctSimple(String baseAcctNo, String prodType,String ccy ,String seqNo);

    /**
     * 描述：获取所有状态的账户信息
     * @param internalKey
     * @param clientNo
     * @return
     */
    RbAcctStandardModel getRbAcctInfoAll(Long internalKey, String clientNo);

    /**
     * 描述：获取所有状态的账户信息(IC卡专用)
     * @param baseAcctNo
     * @return
     */
    RbAcctStandardModel getRbAcctInfo(String baseAcctNo);

    /**
     * 描述：获取所有状态的账户信息,机构变更使用
     * @param baseAcctNo
     * @return
     */
    List<RbAcctStandardModel> getRbAcctByBaseAcctNo(String baseAcctNo);

    /**
     * 根据客户号和联合账户标识查询账户信息
     * @param clientNo
     * @param joinAcctFlag
     * @return
     */
//    List<RbAcct> getRbAcctByJoinAcctFlag(String clientNo, String joinAcctFlag);

    List<RbAcctStandardModel> getRbAcctByJoinAcctFlag(String clientNo, String joinAcctFlag);
    /***
     * 单位结算卡专用
     * @param cardNo cardNo
     * @return RbAcctStandardModel RbAcctStandardModel
     */
    RbAcctStandardModel getModelByCardNoNotThrow(String cardNo);



    /**
     * 查询账户受益人信息
     * @param clientNo    客户号
     * @param internalKey 账户主键
     * @return com.dcits.ensemble.rb.business.model.acct.amend.AcctBenefitModel
     * @Author: danglx
     * @Date: 2025/2/11 14:38
     */
    List<AcctBenefitModel> getBenefitOwnerInfo(String clientNo, Long internalKey);
}
