package com.dcits.ensemble.rb.business.api.component.dd.tran;

import java.math.BigDecimal;

/**
 * The interface Correction tran.
 */
public interface ICorrectionTran {

    /**
     * Correction tran bean result.
     *
     * @param reference        the reference
     * @param tranAmt          the tran amt
     * @param crDrMaintInd     the cr dr maint ind
     * @param referenceCorrect the reference correct
     * @param effectDate       the effect date
     * @param intInd           the int ind
     * @return the bean result
     */
    public void correctionTran(String reference, BigDecimal tranAmt, String crDrMaintInd, String referenceCorrect, String effectDate, String intInd);


}
