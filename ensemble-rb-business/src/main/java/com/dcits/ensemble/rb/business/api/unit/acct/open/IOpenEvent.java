package com.dcits.ensemble.rb.business.api.unit.acct.open;


import com.dcits.ensemble.rb.business.model.acct.AcctOpenBusinessModel;
import com.dcits.ensemble.rb.business.model.acct.AcctOpenBusinessOutModel;
import com.dcits.ensemble.rb.business.model.acct.OpenEnum;

/**
 * The interface Open event.
 *
 * @Description 开户接口
 * <AUTHOR>
 * @Date 2018 /3/5:20:22
 * @Address 基地
 */
public interface IOpenEvent {

    /**
     * Gets open model.
     * 开户类型
     *
     * @return the open model
     */
    OpenEnum getOpenModel();

    /**
     * 存款开户接口
     *
     * @param acctOpenBusinessModel
     * @return
     */
    AcctOpenBusinessOutModel executeRb(AcctOpenBusinessModel acctOpenBusinessModel);
}
