package com.dcits.ensemble.rb.business.api.component.cm.reversal;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbTranControlHist;
import lombok.NonNull;

/**
 * @Description: 冲正业务接口
 * @author: cheng.liang
 * @date: 2016/6/3
 */
public interface IBusiModuleReversal {

    /**
     * 获取限制类型枚举分类类型
     *
     * @return 业务类型
     */
    String getBusiSourceModule();

    /**
     * 检查
     *
     * @param rbTranControlHist 交易明细信息
     */
    void checkModule(@NonNull RbTranControlHist rbTranControlHist, @NonNull String clientNo);

    /**
     * 明细冲正
     *
     * @param rbTranControlHist 交易明细信息
     */
    void reversalModuleDtl(@NonNull RbTranControlHist rbTranControlHist, @NonNull String clientNo,String wipeAccount,String narrative);

}
