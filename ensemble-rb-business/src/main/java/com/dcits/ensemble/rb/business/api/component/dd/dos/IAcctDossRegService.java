package com.dcits.ensemble.rb.business.api.component.dd.dos;

import com.dcits.comet.commons.Context;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcct;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctDossReg;
import com.dcits.ensemble.rb.business.model.cm.common.RbAcctStandardModel;

public interface IAcctDossRegService {

    /**
     * 保存转久悬账户信息至久悬登记簿
     * @param rbAcct
     * @param context
     * @return
     */
    RbAcctDossReg createRbAcctDossReg(RbAcct rbAcct, Context context);

    RbAcctDossReg createRbAcctDossRegOfDormant(RbAcct rbAcct, Context context);

    RbAcctDossReg dormantAcctToActive(RbAcctStandardModel rbAcct);
}
