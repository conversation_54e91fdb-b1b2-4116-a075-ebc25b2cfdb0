package com.dcits.ensemble.rb.business.api.component.agr;


import com.dcits.ensemble.rb.business.model.agr.MbAgreementModel;

/**
 * The interface Mb agreement.
 */
public interface IMbAgreement {


    /**
     * Execute bean result.
     *
     * @param mbAgreementModel the mb agreement model
     * @param dealType         the deal type
     * @return the bean result
     */
//    @Check
//    @GravityComponent(navigationMenu = "no-group", name = "检查")
    MbAgreementModel execute(MbAgreementModel mbAgreementModel, String dealType);
}
