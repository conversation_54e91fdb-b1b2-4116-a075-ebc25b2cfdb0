package com.dcits.ensemble.rb.business.api.component.pcp.tran;

import com.dcits.ensemble.rb.business.model.pcp.PcpGroupModel;
import com.dcits.ensemble.rb.business.model.pcp.PcpGroupSubAcctModel;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbPcpAgreement;

import java.util.List;

/**
 * <p>Title: IUpTransactionalImpl.java</p>
 * <p>Description: IUpTransactionalImpl</p>
 * <p>Copyright: Copyright (c) 2014-2019</p>
 * <p>Company: dcits</p>
 * <p>2019/11/16 14:55</p>
 *
 * <AUTHOR>
 * @version v1.0
 */
public interface IUpTransactionalImpl {

    /**
     *
     * @param pcpGroupModelSub
     * @param pcpGroupModel
     * @param mbPcpAgreement
     * @param upGroupId
     */
    void upTransactional(PcpGroupSubAcctModel pcpGroupModelSub, PcpGroupModel pcpGroupModel, List<RbPcpAgreement> mbPcpAgreement, String upGroupId, String clientNo);
}
