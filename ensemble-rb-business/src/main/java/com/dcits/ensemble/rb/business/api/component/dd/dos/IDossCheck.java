package com.dcits.ensemble.rb.business.api.component.dd.dos;

import com.dcits.ensemble.rb.business.common.constant.DossOperateTypeEnum;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcct;

/**
 * 转睡眠，转久悬，转营业外 账户检查
 */
public interface IDossCheck {

    /**
     * 检查账户限制，签约情况，账户绑定情况，卡种，币种
     */
    void check(RbAcct rbAcct, DossOperateTypeEnum dossOperateTypeEnum);

    void checkEod(RbAcct rbAcct, DossOperateTypeEnum dossOperateTypeEnum);

    /**
     * 对公账户转久悬检查
     * @param rbacct
     */
    void checkCorporateAcctToDoss(RbAcct rbacct);

    /**
     * 根据internalKey检查账户是否是定期账户的结算户
     * @param internalKey
     * @return
     */
    boolean checkFixedIntSettleAcct(Long internalKey);

    /**
     * 检查账户是否存在司法冻结限制
     * @param internalKey
     * @param clientNo
     * @return
     */
    boolean checkJudicatureRestraints(Long internalKey,String clientNo);

    /**
     * 久悬销户检查
     * @param rbacct
     * @param dossOperateTypeEnum
     */
    void checkRestraint(RbAcct rbacct, DossOperateTypeEnum dossOperateTypeEnum);


}
