package com.dcits.ensemble.rb.business.api.component.dd.apprl;

import com.dcits.ensemble.rb.business.model.acct.apprl.MbApprLetterModel;
import com.dcits.ensemble.rb.business.model.acct.apprl.MbApprSubLimitAmtModel;

import java.util.List;

/**
 * The interface Appr letter maint flow.
 */
public interface IApprLetterMaintFlow {

    /**
     * Check bean result.
     *
     * @param mbApprLetterModel       the mb appr letter model
     * @param mbApprSubLimitAmtModels the mb appr sub limit amt models
     * @return the bean result
     */
    void check(MbApprLetterModel mbApprLetterModel, List<MbApprSubLimitAmtModel> mbApprSubLimitAmtModels);

    /**
     * Process bean result.
     *
     * @param mbApprLetterModel       the mb appr letter model
     * @param mbApprSubLimitAmtModels the mb appr sub limit amt models
     * @return the bean result
     */
    void process(MbApprLetterModel mbApprLetterModel, List<MbApprSubLimitAmtModel> mbApprSubLimitAmtModels);
}
