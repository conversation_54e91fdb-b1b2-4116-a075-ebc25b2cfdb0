package com.dcits.ensemble.rb.business.api.component.cm.transaction;


import com.dcits.ensemble.rb.business.model.inneracct.GlTransactionModel;

import java.util.List;

/**
 * The interface Gl deposit tran application.
 */
public interface IGlDepositTranApplicationImpl {
    /**
     * Process bean result.
     *
     * @param mbTransactionLst the mb transaction lst
     * @return the bean result
     */
    void process(List<GlTransactionModel> mbTransactionLst);

    /**
     * 出票日期必须大于等于支票出售日期
     *
     * @param baseAcctNo
     * @param chequeDate
     */
    void checkChequeDate(String baseAcctNo, String voucherNo, String docType, String chequeDate);
}
