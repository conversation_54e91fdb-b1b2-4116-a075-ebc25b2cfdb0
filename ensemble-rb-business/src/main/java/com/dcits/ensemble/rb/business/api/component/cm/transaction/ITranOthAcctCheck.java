package com.dcits.ensemble.rb.business.api.component.cm.transaction;

import com.dcits.ensemble.rb.business.bc.unit.acct.transaction.model.AcctTransactionUnitModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.TransactionControlModel;

public interface ITranOthAcctCheck {
    /**
     * 2、3类户交易对手检查
     * @param acctTransactionUnitModel
     * @param transactionControlModel
     */
    void tranOthAcctCheckFor23ClassAcct(AcctTransactionUnitModel acctTransactionUnitModel, TransactionControlModel transactionControlModel);
}
