package com.dcits.ensemble.rb.business.api.component.cm.reversal;

import lombok.NonNull;

/**
 * @Description: 费用冲正业务接口
 * @author: cheng.liang
 * @date: 2016/6/3
 */
public interface IFeeReversal {



    /**
     * 检查
     *
     * @param reference 交易
     */
    void check(@NonNull String reference);

    /**
     * 费用明细冲正
     * @param eventType--FEE事件
     * @param reference
     */
    void feeReversalDtl(String eventType ,@NonNull String reference ,String clientNo);

}
