package com.dcits.ensemble.rb.business.api.component.td.tran;

import com.dcits.ensemble.rb.business.model.cm.transaction.TermTranControlModel;
import com.dcits.ensemble.rb.business.model.acct.FixedCallEnum;
import com.dcits.ensemble.rb.business.model.interest.MbInterestModel;

/**
 * The interface Term get interests.
 */
public interface ITermGetInterests {

    /**
     * Gets interests.
     *
     * @param termTranControlModel the term tran control model
     * @return the interests
     */
    MbInterestModel getInterests(TermTranControlModel termTranControlModel);

    /**
     * Gets interests no calc.
     *
     * @param termTranControlModel the term tran control model
     * @return the interests no calc
     */
    MbInterestModel getInterestsNoCalc(TermTranControlModel termTranControlModel);

    /**
     * Gets fixed call.
     *
     * @return the fixed call
     */
    FixedCallEnum getFixedCall();
}
