package com.dcits.ensemble.rb.business.api.component.agr;


/**
 * The interface Pcp agreement application.
 */
public interface IPcpAgreementApplication {
    /**
     * Impound pcp stop rew.
     *
     * @param baseAcctNo the base acct no
     * @param prodType   the prod type
     * @param ccy        the acctCcy
     * @param seqNo      the seq no
     */
    void impoundPcpStopRew(String baseAcctNo,String prodType,String ccy,String seqNo);

    /**
     * Is pcpagreement boolean.
     *
     * @param baseAcctNo the base acct no
     * @param prodType   the prod type
     * @param ccy        the acctCcy
     * @param seqNo      the seq no
     * @return the boolean
     */
    boolean isPcpagreement(String baseAcctNo,String prodType,String ccy,String seqNo);

    /**
     * Is pcpagreement main boolean.
     *
     * @param baseAcctNo the base acct no
     * @param prodType   the prod type
     * @param ccy        the acctCcy
     * @param seqNo      the seq no
     * @return the boolean
     */
    boolean isPcpagreementMain(String baseAcctNo,String prodType,String ccy,String seqNo);

    /**
     * Pcp restraints deal boolean.
     *
     * @param baseAcctNo   the base acct no
     * @param prodType     the prod type
     * @param ccy          the acctCcy
     * @param seqNo        the seq no
     * @param restrainType the restrain type
     * @return the boolean
     */
    boolean pcpRestraintsDeal(String baseAcctNo,String prodType,String ccy,String seqNo,String restrainType);
}
