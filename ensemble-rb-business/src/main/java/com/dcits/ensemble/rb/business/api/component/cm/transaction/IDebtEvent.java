package com.dcits.ensemble.rb.business.api.component.cm.transaction;

import com.dcits.ensemble.rb.business.model.cm.transaction.TransactionControlModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.TransactionOutModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.bal.AcctTransactionInModel;

/**
 * <p>Name:  com.dcits.ensemble.rb.business.api.business.transaction</p>
 * <p>Description: 借记记账事件接口 基础实现</p>
 * 功能实现 账户借记记账
 * 参数定义
 * -输入模型 AcctTransactionInModel
 * 包含账户交易主体账户信息模型mbAcct 交易类型对应模型mbTranDef 对手交易信息othInternalKey等
 * 交易关键信息 交易金额tranAmt 交易币种ccy 交易机构tranBranch
 * 以及登记流水相关信息 tranDate 操作柜员
 * -控制模型 TransactionControlModel
 * 包含是否强制更新尾箱等标志
 * * 功能范围：
 * 组件层的基础赋值和检查，保证调用时的流程完整性。
 * 调用产品工厂进行借贷记事件指标，属性的装配。
 * 调用余额更新接口，IAcctTransaction。
 * 在扣款时，检查账户可用余额
 * 根据产品工厂参数AMT_CALC_TYPE等计算相关金额
 * 记录mb_tran_hist交易流水
 * 更新mb_acct_balance账户余额信息
 * 更新mb_acct 账户状态信息
 * 根据控制参数判断，更新柜员现金尾箱
 * 根据控制参数判断，更新柜员凭证尾箱
 *
 * <AUTHOR>
 * @date_ 2018/3/2
 */
public interface IDebtEvent  {

    TransactionOutModel execute(AcctTransactionInModel acctTransactionInModel, TransactionControlModel transactionControlModel);
}
