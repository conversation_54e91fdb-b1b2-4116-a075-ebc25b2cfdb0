package com.dcits.ensemble.rb.business.api.component.td.tran;


import com.dcits.ensemble.rb.business.model.cm.transaction.BalAgrTypeEnum;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * The type Term tran factory.
 */
@Component

public class TermTranFactory implements ApplicationContextAware {

    private static Map<BalAgrTypeEnum, ITermTranApplication> tranBeanMap;

    /**
     * Gets tran application.
     *
     * @param type the type
     * @return the tran application
     */
    public static ITermTranApplication getTranApplication(BalAgrTypeEnum type) {
        return tranBeanMap.get(type);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, ITermTranApplication> map = applicationContext.getBeansOfType(ITermTranApplication.class);
        tranBeanMap = new HashMap<>(5);
        for (ITermTranApplication value : map.values()) {
            tranBeanMap.put(value.getBalAgrType(), value);
        }
    }
}
