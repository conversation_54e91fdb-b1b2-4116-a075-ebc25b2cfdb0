package com.dcits.ensemble.rb.business.api.component.jud.impound;

import com.dcits.ensemble.rb.business.model.jud.impound.MbImpoundInfoModel;
import com.dcits.ensemble.rb.business.common.constant.AcctImpoundTypeEnum;
import com.dcits.ensemble.rb.business.model.cm.common.RbAcctStandardModel;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcct;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbRestraints;
import com.dcits.ensemble.rb.business.model.jud.impound.MbImpoundOutModel;
import com.dcits.ensemble.rb.business.model.cm.restful.rb.AsynUnresDebtSimpleIn;

import java.math.BigDecimal;

/**
 * The interface Acct impound application.
 */
public interface IAcctImpoundApplication {

    /**
     * Gets acct impound class.
     *
     * @return the acct impound class
     */
    AcctImpoundTypeEnum getAcctImpoundClass();

    /**
     * Check bean result.
     *
     * @param rbAcct the mb acct
     * @return the bean result
     */
    void check(RbAcct rbAcct);

    /**
     * Execute.
     *  @param mbImpoundInfoModel the mb impound info model
     * @param acctStandardModel  the wtd acct
     * @param ref                the ref
     * @param balanceIsEnough
     */
    MbImpoundOutModel execute(MbImpoundInfoModel mbImpoundInfoModel, RbAcctStandardModel acctStandardModel, String ref, RbRestraints rbRestraints, boolean balanceIsEnough);

    /**
     * Delete restraint.
     *
     * @param resSeqNo the res seq no
     */
    void deleteRestraint(String resSeqNo,String clientNo);

    /**
     * Clean restraint.
     *
     * @param resSeqNo the res seq no
     */
    void cleanRestraint(String resSeqNo,String clientNo);

    /**
     * Create restraint.
     *
     * @param mbRestraints the mb restraints
     * @param tranAmt      the tran amt
     * @param ref          the ref
     */
    void createRestraint(RbRestraints mbRestraints, BigDecimal tranAmt, String ref);

    /**
     * Delete restraint.
     * @param in the AsynUnresDebtSimpleIn
     * @param clientNo the clientNo
     */
    void deleteRestraint(AsynUnresDebtSimpleIn in, String clientNo);
}
