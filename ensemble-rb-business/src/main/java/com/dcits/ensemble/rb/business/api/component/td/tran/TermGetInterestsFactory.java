package com.dcits.ensemble.rb.business.api.component.td.tran;

import com.dcits.ensemble.rb.business.model.acct.FixedCallEnum;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.HashMap;
import java.util.Map;

/**
 * The type Term get interests factory.
 */
public class TermGetInterestsFactory implements ApplicationContextAware {
    private static Map<FixedCallEnum, ITermGetInterests> tranBeanMap;

    /**
     * Gets i term get interests.
     *
     * @param fixedCall the fixed call
     * @return the i term get interests
     */
    public static ITermGetInterests getITermGetInterests(String fixedCall) {
        return tranBeanMap.get(FixedCallEnum.valueOf(fixedCall));
    }
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, ITermGetInterests> map = applicationContext.getBeansOfType(ITermGetInterests.class);
        tranBeanMap = new HashMap<>(5);
        for (ITermGetInterests value : map.values()) {
            tranBeanMap.put(value.getFixedCall(), value);
        }
    }
}
