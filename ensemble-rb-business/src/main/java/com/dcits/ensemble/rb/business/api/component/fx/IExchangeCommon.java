package com.dcits.ensemble.rb.business.api.component.fx;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbExchangeDistantReg;
import com.dcits.ensemble.rb.business.model.cm.restful.prod.MbsdCore14009442Out;
import com.dcits.ensemble.rb.business.model.cm.restful.prod.PfTimerRateOut;
import com.dcits.ensemble.rb.business.model.cm.transaction.TransactionOutModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.bal.AcctTransactionInModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.bal.RbAcctTransactionOutModel;
import com.dcits.ensemble.rb.business.model.fx.MbExchangeModel;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbExchangeTranAttach;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbExchangeTranHist;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbExchangeTranType;

import com.dcits.ensemble.rb.business.model.cm.common.BaseOutEventModel;

import java.math.BigDecimal;
import java.util.List;

/**
 * The interface Exchange common.
 */
public interface IExchangeCommon {


    /**
     * Gets op type.
     *
     * @param tranType the tran type
     * @return the op type
     */
    public String getOpType(String tranType);

    RbExchangeTranType getRbExchangeTranType(String tranType);


    /**
     * Org mb tran data.
     *
     * @param mbExchangeModel the mb exchange model
     * @param crDrMaintInd    the cr dr maint ind
     */
    public void orgMbTranData(MbExchangeModel mbExchangeModel, String crDrMaintInd);

    /**
     * Org mb exchange tran hist data.
     *
     * @param mbExchangeModel      the mb exchange model
     * @param mbExchangeTranHist   the mb exchange tran hist
     * @param mbExchangeTranAttach the mb exchange tran attach
     */
    public void orgMbExchangeTranHistData(MbExchangeModel mbExchangeModel, RbExchangeTranHist mbExchangeTranHist, RbExchangeTranAttach mbExchangeTranAttach);

    /**
     * Org bal change model.
     *
     * @param mbExchangeModel the mb exchange model
     * @param crDrMaintInd    the cr dr maint ind
     */
    public void orgBalChangeModel(MbExchangeModel mbExchangeModel, String crDrMaintInd);

    /**
     * Exchange misc additional bean result.
     *
     * @param mbExchangeModel the mb exchange model
     * @return the bean result
     */
    public BaseOutEventModel exchangeMiscAdditional(MbExchangeModel mbExchangeModel);


    /**
     * Gets exchange tran type.
     *
     * @return the exchange tran type
     */
    public List<RbExchangeTranType> getExchangeTranType();

    /**
     * Gets exchange tran type by ex type.
     *
     * @param exType the ex type
     * @param opType the op type
     * @return the exchange tran type by ex type
     */
    public RbExchangeTranType getExchangeTranTypeByExType(String exType, String opType);

    /**
     * Gets exchange tran type
     * @param acctCcy acctCcy
     * @param othAcctCcy othAcctCcy
     * @param baseAcctNo baseAcctNo
     * @param othBaseAcctNo othBaseAcctNo
     * @return tranType
     */
    String getExchangeTranType(String acctCcy, String othAcctCcy, String baseAcctNo, String othBaseAcctNo);

    /**
     * @desc 模型转换
     * @param mbExchangeModel
     * @return
     */
    public void converTranModel(MbExchangeModel mbExchangeModel);

    /**
     * 组织通用记账流水并登记
     * @param dealRecAcct
     * @param rbExchangeDistantReg
     * @param flag
     * @return
     */
    public TransactionOutModel tranMqSend(String dealRecAcct, RbExchangeDistantReg rbExchangeDistantReg, String flag);

    /**
     * 组装跨币种交易tae流水
     * @param acctTransactionInModel acctTransactionInModel
     * @return RbAcctTransactionOutModel
     */
    public RbAcctTransactionOutModel assembleTransactionTaeRowByDiffCcy(AcctTransactionInModel acctTransactionInModel);

    /**
     * 结售汇模型转换
     * @param inModel inModel
     * @return MbExchangeModel
     */
    public MbExchangeModel convertMbExchangeModel(AcctTransactionInModel inModel);

    /**
     * 等值金额计算
     * @param branch 机构
     * @param buyCcy 买入币种
     * @param sellCcy 卖出币种
     * @param buyAmount 买入金额
     * @param sellAmount 卖出金额
     * @param rateType  利率类型
     * @return com.dcits.ensemble.rb.business.model.cm.restful.prod.MbsdCore14009442Out
     */
    MbsdCore14009442Out getExchangeRate(String branch, String buyCcy, String sellCcy, BigDecimal buyAmount, BigDecimal sellAmount, String rateType);


    /**
     * 获取最新牌价类型
     *
     * @param rateType   汇率类型
     * @param branch     机构
     * @param fromCcy    源币种
     * @param toCcy      目标币种
     * @return BigDecimal 中间价
     */
    String getQuoteType(String rateType, String branch, String fromCcy, String toCcy);

    /**
     * 构建结售汇模型
     * @param inModel inModel
     */
    public MbExchangeModel buildMbExchangeModel(AcctTransactionInModel inModel);

}
