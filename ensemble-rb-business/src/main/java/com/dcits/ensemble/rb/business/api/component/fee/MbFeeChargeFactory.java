package com.dcits.ensemble.rb.business.api.component.fee;

import com.dcits.ensemble.rb.business.model.fee.MbChargeTypeEnum;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * The type Mb fee charge factory.
 */
@Component

public class MbFeeChargeFactory implements ApplicationContextAware {

    private static Map<MbChargeTypeEnum, IMbFeeCharge> tranBeanMap;

    /**
     * Gets charge.
     *
     * @param mbChargeTypeEnum the mb charge type enum
     * @return the charge
     */
    public static IMbFeeCharge getCharge(MbChargeTypeEnum mbChargeTypeEnum) {
        return tranBeanMap.get(mbChargeTypeEnum);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, IMbFeeCharge> map = applicationContext.getBeansOfType(IMbFeeCharge.class);
        tranBeanMap = new HashMap<>(5);
        for (IMbFeeCharge value : map.values()) {
            tranBeanMap.put(value.getChargeType(), value);
        }
    }
}
