package com.dcits.ensemble.rb.business.api.component.dd.tran;

import com.dcits.ensemble.rb.business.api.component.cm.transaction.IDepositTranApplication2;
import com.dcits.ensemble.rb.business.model.cm.transaction.BalAgrTypeEnum;
import com.dcits.ensemble.rb.business.model.acct.SourceModuleEnum;
import com.dcits.ensemble.rb.business.model.cm.transaction.TransactionControlModel;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcct;
import com.dcits.ensemble.util.BusiUtil;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * The type Deposit tran factory 2.
 */
@Component

public class DepositTranFactory2 implements ApplicationContextAware {

    private static Map<BalAgrTypeEnum, IDepositTranApplication2> tranBeanMap;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, IDepositTranApplication2> map = applicationContext.getBeansOfType(IDepositTranApplication2.class);
        tranBeanMap = new HashMap<>(5);
        for (IDepositTranApplication2 value : map.values()) {
            tranBeanMap.put(value.getBalAgrType(), value);
        }
    }

    private static BalAgrTypeEnum getEnum(RbAcct rbAcct, TransactionControlModel controlModel, String agreementType) {
        if (BusiUtil.isEquals(SourceModuleEnum.GL.toString(), rbAcct.getSourceModule())) {
            return BalAgrTypeEnum.GL;
        } else if (controlModel.isCorrect()) {
            return BalAgrTypeEnum.CORRECT;
        } else if (BusiUtil.isEquals(agreementType, BalAgrTypeEnum.FIN.toString())) {
            return BalAgrTypeEnum.FIN;
        } else if (BusiUtil.isEquals(agreementType, BalAgrTypeEnum.YHT.toString())) {
            return BalAgrTypeEnum.YHT;
        } else {
            return null;
        }

    }

    /**
     * Gets tran application.
     *
     * @param rbAcct        the mb acct
     * @param controlModel  the control model
     * @param agreementType the agreement type
     * @return the tran application
     */
    public static IDepositTranApplication2 getTranApplication(RbAcct rbAcct, TransactionControlModel controlModel, String agreementType) {
        return tranBeanMap.get(getEnum(rbAcct, controlModel, agreementType));
    }

}
