package com.dcits.ensemble.rb.business.api.component.dd;

import com.dcits.ensemble.rb.business.model.fee.MbServChargeModel;
import com.dcits.ensemble.rb.business.model.acct.AcctOpenBaseModel;
import com.dcits.ensemble.rb.business.model.acct.AcctOpenBusinessOutModel;

import java.util.List;

/**
 * The interface Current open and dep.
 */
public interface ICurrentOpenAndDep {

    /**
     * Current open and dep acct open business out model.
     *
     * @param mainModel    the main model
     * @param subMobel     the sub mobel
     * @param pwd          the pwd
     * @param withdrawType the withdraw type
     * @param settleModels the settle models
     * @param servCharge   the serv charge
     * @param mbTran       the mb tran
     * @param acctContact  the acct contact
     * @return the acct open business out model
     */
    AcctOpenBusinessOutModel currentOpenAndDep(AcctOpenBaseModel mainModel, AcctOpenBaseModel subMobel,
                                               List pwd, List withdrawType, List settleModels, MbServChargeModel servCharge, List mbTran,
                                               List acctContact);
}
