package com.dcits.ensemble.rb.business.api.unit.res;

import com.dcits.ensemble.rb.business.model.res.restraints.RestraintsTypeEnum;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by furongb on 2018/3/1.
 */
@Component

public class RestraintsOperateFactory implements ApplicationContextAware {

    private static Map<RestraintsTypeEnum, IRestraintsOperate> restraintsBeanMap;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, IRestraintsOperate> map = applicationContext.getBeansOfType(IRestraintsOperate.class);
        restraintsBeanMap = new HashMap<>(5);
        for (IRestraintsOperate value : map.values()) {
            restraintsBeanMap.put(value.getRestraintsClass(), value);
        }
    }

    public static IRestraintsOperate getRestraintsBusiness(RestraintsTypeEnum type) {
        return restraintsBeanMap.get(type);
    }

    public static Map<RestraintsTypeEnum, IRestraintsOperate> getAllRestraintsBusiness() {
        return restraintsBeanMap;
    }
}
