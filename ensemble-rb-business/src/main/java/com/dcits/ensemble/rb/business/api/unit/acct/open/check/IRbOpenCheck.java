package com.dcits.ensemble.rb.business.api.unit.acct.open.check;

import com.dcits.ensemble.rb.business.model.acct.MbAcctProtModel;
import com.dcits.ensemble.rb.business.model.cm.commission.MbCommissionModel;
import com.dcits.ensemble.rb.business.model.acct.settle.MbAcctSettleModel;
import com.dcits.ensemble.rb.business.model.cm.pwd.MbAcctWithdrawTypeModel;
import com.dcits.ensemble.rb.business.model.cm.pwd.MbPwdModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.MbTransactionModel;
import com.dcits.ensemble.rb.business.model.cm.common.RbAcctStandardModel;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2018/3/13
 * 存款开户检查接口(除客户信息之外)
 */
public interface IRbOpenCheck {

    /**
     * 检查核准件信息-增加账户用途
     */
    void checkApprLetter(String clientNo, String apprLetterNo, String ccy ,String reasonCode,String baseAcctNo);
    /**
     * 检查核准件信息-增加账户用途-账户维护
     */
    void checkUpdApprLetter(String clientNo, String apprLetterNo, String ccy ,String reasonCode,String baseAcctNo);

    /**
     * 检查核准件信息
     */
    void checkApprLetter(RbAcctStandardModel acctStandardModel, String clientNo, String apprLetterNo, String ccy, String acctProperty);

    /**
     * 检查证件类型下，帐号数量限制
     */
    void checkMbOpenCtl(String clientNo, String prodType, String acctNature, String docType, String acctClass);

    /**
     * 增加对公开户时对期限的限制检查 验资户检查 *
     */
    void checkAcctNatureClass(String acctNature, String acctDueDate);

    /**
     * 检查代办人办卡张数
     */
    void checkCommisionCardNum(MbCommissionModel mbCommissionModel);


    /**
     * 卡吉祥号号检查(活期开户)
     */
    void checkLuckyCard(String cardNo);


    /**
     * 非零余额开户条件检查
     */
    void checkDepCondition(List<MbAcctProtModel> mbSubAccts,
                                 List<MbTransactionModel> tranLists);

    /**
     * 开户时起存金额检查
     */
    void checkInitBal(String prodType, String ccy, List<MbTransactionModel> tranLists);

    /**
     * 开户过程中检查转出账户，结算账户信息
     */
    void checkAcctForOpen(String clientNo, String prodType, String ccy, List<MbAcctSettleModel> mbAcctSettles, List<MbTransactionModel> mbTransactionModels);

    /**
     * 开户凭密码支取检查密码是否为空
     */
    void checkPasswordIsNull(List<MbPwdModel> passwordArray, List<MbAcctWithdrawTypeModel> withdrawArray);

    /**
     * 临时户有效期检查
     * @param acctNature
     * @param acctOpenDate
     * @param acctDueDate
     */
    void checkTempAcctNature(String acctNature, String acctOpenDate, String acctDueDate);
}
