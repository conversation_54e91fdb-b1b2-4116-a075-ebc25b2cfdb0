package com.dcits.ensemble.rb.business.api.component.cd.pos;

import com.dcits.ensemble.rb.business.model.cm.common.RbAcctStandardModel;
import com.dcits.ensemble.rb.business.model.cm.restful.tae.TaeAcctMaintradesRow;
import com.dcits.ensemble.rb.business.model.cm.restful.tae.TaeAcctSubtradesRow;

import java.math.BigDecimal;
import java.util.List;


/**
 * The interface ES Transfer.
 */
public interface IEsTransferService {

    /**
     * 财务系统下拨、上收、付款
     * ES Transfer Allocation.
     *
     * @return String
     */
    String esTransferPaidAllocation(RbAcctStandardModel drAcct, RbAcctStandardModel crAcct, String transFlag, BigDecimal tranAmt, String paidBranch);

    /**
     * 财务系统下拨、上收、付款
     * ES Transfer Allocation.
     *
     * @return String
     */
    String esTransferAllocation(RbAcctStandardModel drAcct, RbAcctStandardModel crAcct, String transFlag, BigDecimal tranAmt);

    /**
     * 财务系统批量下拨、上收、付款
     * ES Transfer Allocation.
     *
     * @return String
     */
    void esTransferBatch(RbAcctStandardModel drAcct, RbAcctStandardModel crAcct, String transFlag, BigDecimal tranAmt, TaeAcctMaintradesRow maintradesRow,List<TaeAcctSubtradesRow> subtradesRowList,String
                         paidBranch);
    /**
     * 财务系统收款
     * ES Transfer Collect.
     *
     * @return String
     */
    String esTransferCollect(RbAcctStandardModel drAcct, String transFlag, BigDecimal tranAmt);

}
