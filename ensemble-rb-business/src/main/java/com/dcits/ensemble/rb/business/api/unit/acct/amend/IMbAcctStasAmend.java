package com.dcits.ensemble.rb.business.api.unit.acct.amend;

import com.dcits.ensemble.rb.business.model.cm.amend.StasAmendModel;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcct;


/**
 * Created by duanal on 2018/3/17.
 */
public interface IMbAcctStasAmend {


    void acctStasAmend(StasAmendModel stasAmendModel);

    void acctStasAmend2(String newStatus, RbAcct rbAcct, String amendDate,String batchNo);

    void acctStasAmend3(String newStatus, RbAcct rbAcct, String amendDate);
}
