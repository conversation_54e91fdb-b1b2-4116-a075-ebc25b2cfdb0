package com.dcits.ensemble.rb.business.api.component.cm.transaction;


import com.dcits.ensemble.rb.business.model.cm.transaction.MbTransactionModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.bal.AcctTransactionInModel;
import com.dcits.ensemble.rb.business.model.cm.common.RbAcctStandardModel;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/4/26 20:31
 * @description:内部账挂销账交易处理类
 */
public interface IGlHangWriteOffTranService {

    /**
     * 内部账销账处理
     * 1. 更新挂账交易流水挂账余额， 挂账状态
     * 2. 登记销账登记簿
     * @param inModel
     */
    void writeOffGlHangAccount(AcctTransactionInModel inModel);


    /**
     * 开户失败销账 冲正处理
     * 根据reference查询对应的销账登记流水表进行销账
     *  修改表HANG_ACCOUNT的状态和已处理金额
     *  更新表WRITE_OFF_ACCOUNT中的已冲正数据
     *  更新汇总表HANG_HEAD
     * @param reference
     */
    void writeOffGlHangAccountReversal(String  reference);

    /**
     * 销账余额检查
     * @param hangSeqNo
     * @param tranAmt
     * @param clientNo
     * @param baseAcctNo
     * @return
     */
    BigDecimal tranAmtCheck(String hangSeqNo, BigDecimal tranAmt, String clientNo,String baseAcctNo);

    /**
     * 销账余额更新
     * @param writeOffStatus
     * @param otherAcc
     * @param hangSeqNo
     * @param acctTransactionInModel
     */
    void updateHangBal(String writeOffStatus,String otherAcc,String hangSeqNo,AcctTransactionInModel acctTransactionInModel);


    String  addHangAccount(RbAcctStandardModel acctStandardModel, AcctTransactionInModel acctTransactionUnitModel);

    void writeOffAccount(AcctTransactionInModel inModel);

    String getNewSubHangSeqNo(AcctTransactionInModel model);

    String addGlHangAccount(AcctTransactionInModel acctTransactionInModel,RbAcctStandardModel standardModel);

    void insertRbGlHangHead(AcctTransactionInModel acctTransactionInModel,RbAcctStandardModel standardModel);

     Date getHangEndDate (AcctTransactionInModel acctTransactionInModel, RbAcctStandardModel standardModel);
    /**
     * 对手账户销账处理
     * @param model
     */
    void writeOffAccountOth(MbTransactionModel model);

    void hangWriteOffCheck (String hangOrWriteOff, MbTransactionModel mbTransactionModel, RbAcctStandardModel rbAcctStandardModel);
}
