package com.dcits.ensemble.rb.business.api.component.cd.pos;

import com.dcits.ensemble.rb.business.bc.component.channel.model.UcBaseModel;
import com.dcits.ensemble.rb.business.model.cm.common.RbAcctStandardModel;

/**
 * <AUTHOR>
 */
public interface IEventBusServer {
    /**
     * 外围公共检查
     * @param baseModel
     * @param rbAcctStandardModel
     */
    public void publicCheck(UcBaseModel baseModel, RbAcctStandardModel rbAcctStandardModel);

    /**
     * 外围公共检查
     * 在 publicCheck 方法的基础上增加 根据StatusFlag查询是否包含销户状态的账户信息
     * @param baseModel
     * @param rbAcctStandardModel
     */
    public void publicCheckByStatusFlag(UcBaseModel baseModel, RbAcctStandardModel rbAcctStandardModel, String statusFlag);

    /**
     * xxx
     * @param baseAcctNo
     * @param clientNo
     * @return
     */
    public boolean checkCorpPayCard(String baseAcctNo, String clientNo);

    /**
     * 三磁检查
     * @param baseModel
     * @param rbAcctStandardModel
     */
    public void thdPublicCheck(UcBaseModel baseModel,RbAcctStandardModel rbAcctStandardModel);

    /**
     * 三磁检查
     * @param baseModel
     * @param rbAcctStandardModel
     */
    public void thdPublicCheckByStatusFlag(UcBaseModel baseModel, RbAcctStandardModel rbAcctStandardModel, String StatusFlag);

    /**
     * 校验ICCDATA
     * @param rbAcctStandardModel
     * @param iccData
     * @param icCheckFlag
     */
    public void checkIccData(RbAcctStandardModel rbAcctStandardModel,String cardNo,String iccData,String icCheckFlag);

    /**
     * 校验ICCDATA
     * 在 checkIccData 方法的基础上增加 根据StatusFlag查询是否包含销户状态的账户信息
     * @param rbAcctStandardModel
     * @param iccData
     * @param icCheckFlag
     */
    public void checkIccDataByStatusFlag(RbAcctStandardModel rbAcctStandardModel,String cardNo,String iccData,String icCheckFlag,
                                         String statusFlag,String checkOption,String icCardSeq);
}
