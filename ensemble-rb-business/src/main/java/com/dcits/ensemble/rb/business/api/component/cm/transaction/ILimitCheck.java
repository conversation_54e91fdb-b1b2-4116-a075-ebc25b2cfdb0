package com.dcits.ensemble.rb.business.api.component.cm.transaction;

import com.dcits.ensemble.rb.business.bc.component.channel.ChannelControlModel;
import com.dcits.ensemble.rb.business.bc.unit.acct.transaction.model.AcctTransactionUnitModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.TransactionControlModel;

import java.util.Map;

/**
 * @version V1.0
 * @description 限额检查
 * <AUTHOR>
 * @update 20160225
 */
public interface ILimitCheck {

    /***
     * 限额检查
     * @param acctTransactionUnitModel 记账模型
     * @param conModel 控制模型
     */
    Map<String, String> limitCheck(AcctTransactionUnitModel acctTransactionUnitModel, TransactionControlModel conModel);

    /**
     * 获取限额校验标志
     *
     * @param unitModel    账务处理模型
     * @param controlModel 账务控制模型
     * @return boolean 限额校验标志
     */
    boolean getLimitCheckFlag(AcctTransactionUnitModel unitModel, TransactionControlModel controlModel);
}
