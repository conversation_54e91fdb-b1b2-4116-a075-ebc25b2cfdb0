package com.dcits.ensemble.rb.business.api.unit.interest;


import com.dcits.comet.util.json.JSONArray;
import com.dcits.ensemble.rb.business.model.cm.prod.ProductAmt;
import com.dcits.ensemble.rb.business.model.acct.MbAcctIntModel;
import com.dcits.ensemble.rb.business.model.interest.RbAcctIntDetailsModel;
import com.dcits.ensemble.rb.business.model.interest.MbInterestModel;
import com.dcits.ensemble.rb.business.model.interest.RbRevIntInput;
import com.dcits.ensemble.rb.business.model.cm.common.RbAcctStandardModel;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2017/7/10
 */
public interface IComGetInterests {

    MbInterestModel calcInterests(RbAcctStandardModel rbAcctStandardModel, MbAcctIntModel mbIntModel);

    List<RbAcctIntDetailsModel> calcRbAcctIntDetailsModel(RbAcctStandardModel rbAcctStandardModel, MbAcctIntModel mbIntModel);

    /**
     * 利息试算
     * 与利率市场化无关的
     *
     * @param startDate
     * @param endDate
     * @param amt
     * @param realRate
     * @param monthBasis
     * @param yearBasis
     * @return
     */
    BigDecimal getInterestsMB(String startDate, String endDate, BigDecimal amt, BigDecimal realRate, String monthBasis, String yearBasis);

    /**
     * 定期手工结息
     *
     * @param internalKey
     * @param preAmt
     * @return
     */
    MbInterestModel calcInterests(Long internalKey, BigDecimal preAmt, String endDate);

    /**
     * 存在司法冻结时定活两遍支取
     * chenhrb
     * 20160922
     *
     * @param internalKey
     * @param preAmt
     * @return
     */
    MbInterestModel calcDHLBInterests(Long internalKey, BigDecimal preAmt);


    /**
     * 利息冲正
     *
     * @param mbRevIntModels
     * @return
     */

    List<MbInterestModel> reversalInterests(JSONArray mbRevIntModels);

    /**
     * 利息冲正
     * @param rbRevIntInputList
     * @return
     */
    List<MbInterestModel> reversalInterests2(List<RbRevIntInput> rbRevIntInputList);


    /**
     * 获取等值金额
     */
//    MbTransactionModel getBaseEquiv(String buyCcy,
//                                    BigDecimal buyAmount,
//                                    String buyCaTt,
//                                    String sellCcy,
//                                    BigDecimal sellAmount,
//                                    String sellCaTt,
//                                    String clientNo,
//                                    String sourceType
//    );

    /**
     * 获取违约利息，定期提前部分支取，提前全部支取
     *
     * @param mbAcctIntModel mbAcctIntModel
     * @param productAmt     产品金额信息
     * @param eventType      事件类型
     * @return MbInterestModel
     */
    MbInterestModel calcPreInterests(MbAcctIntModel mbAcctIntModel, ProductAmt productAmt, String eventType);

    /**
     * @param mbAcctIntModel mbAcctIntModel
     * @return MbInterestModel
     */
    MbInterestModel noInterests(MbAcctIntModel mbAcctIntModel);

    /**
     * 累计计提查询
     *
     * @param mbAcctIntModel mbAcctIntModel
     * @return MbInterestModel
     */
    MbInterestModel calcNormalInterests(MbAcctIntModel mbAcctIntModel);

    /**
     * 通知存款有通知全部支取、无通知支取专用
     *
     * @param mbAcctIntModel mbAcctIntModel
     * @param eventType      事件类型
     * @param preAmt         交易金额
     * @param precontractAmt 通知金额
     * @param violateAdj     违约积数(通知存款专用)
     * @return MbInterestModel
     */
    MbInterestModel calcNoticePreInterests(MbAcctIntModel mbAcctIntModel, String eventType, BigDecimal preAmt, BigDecimal precontractAmt, BigDecimal violateAdj);

    /**
     * @param totalAmount    余额
     * @param tranAmt        交易金额
     * @param mbAcctIntModel mbAcctIntModel
     * @param precontAmt     通知金额
     * @param violateAdj     违约积数
     * @return MbInterestModel
     */
    MbInterestModel noticeInterestPublic(BigDecimal totalAmount, BigDecimal tranAmt, MbAcctIntModel mbAcctIntModel, BigDecimal precontAmt,
                                         BigDecimal violateAdj);

    /**
     * @param mbAcctIntModel mbAcctIntModel
     * @param productAmt     产品金额类型
     * @param pbFlag         携带证明标志
     * @return MbInterestModel
     */
    MbInterestModel calcJycxPsdInterests(MbAcctIntModel mbAcctIntModel, ProductAmt productAmt, String pbFlag);

}
