package com.dcits.ensemble.rb.business.api.component.cd.corpcd;

import com.dcits.ensemble.rb.business.bc.unit.voucher.base.business.BaseVoucherModel;
import com.dcits.ensemble.rb.business.model.cm.SettleCardModel;

/**
 * The interface Settle card open opt.
 */
public interface ISettleCardOpenOpt {
    /**
     * Sets card open.
     *
     * @param settleCardModel the settle card model
     */
    BaseVoucherModel settleCardOpen(SettleCardModel settleCardModel);
}
