package com.dcits.ensemble.rb.business.api.unit.interest.tax;

import com.dcits.ensemble.rb.business.model.acct.MbAcctIntModel;
import com.dcits.ensemble.rb.business.model.interest.MbIntCycleModel;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctIntDetail;

/**
 * Created by furongb on 2018/3/28.
 */
public interface ICaptTaxProcess {
    void process(MbAcctIntModel mbAcctIntModel, MbIntCycleModel mbIntCycleModel, RbAcctIntDetail rbAcctIntDetail);
}
