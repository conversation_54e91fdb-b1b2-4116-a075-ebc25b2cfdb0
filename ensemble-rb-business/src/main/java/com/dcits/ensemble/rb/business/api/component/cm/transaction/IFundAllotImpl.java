package com.dcits.ensemble.rb.business.api.component.cm.transaction;

import com.dcits.ensemble.rb.business.model.inneracct.MbGlTransferModel;

/**
 * The interface Fund allot.
 */
public interface IFundAllotImpl {

    /**
     * Execute bean result.
     *
     * @param mbGlTransferModel the mb gl transfer model
     * @return the bean result
     */
    public void execute(MbGlTransferModel mbGlTransferModel);

    /**
     * Check bean result.
     *
     * @param mbGlTransferModel the mb gl transfer model
     * @return the bean result
     */
    public void check(MbGlTransferModel mbGlTransferModel);

    /**
     * Reversal.
     *
     * @param reference the reference
     */
    public void reversal(String reference);
}
