package com.dcits.ensemble.rb.business.api.component.cd;

import com.dcits.comet.flow.component.annotation.Check;
import com.dcits.gravity.api.annotation.GravityComponent;

/**
 * The interface Card check service.
 */
public interface ICardCheckService {


    /**
     * Check card status bean result.
     *
     * @param cardNo the card no
     * @return the bean result
     */
//    @Check
//    @GravityComponent(navigationMenu = "no-group", name = "卡状态检查40")
    void checkCardStatus(String cardNo);

    void checkAppCardStatus(String cardNo,String clientNo);
}
