package com.dcits.ensemble.rb.business.api.component.td.certtd;

import java.math.BigDecimal;

/**
 * The interface Dc settle acct insert.
 */
public interface IDcSettleAcctInsert {

    /**
     * Press.
     *
     * @param mbAcctIntnalkey the mb acct intnalkey
     * @param settleIntnalkey the settle intnalkey
     * @param issueYear       the issue year
     * @param stageCode       the stage code
     * @param amount          the amount
     */
    void press( Long mbAcctIntnalkey,Long settleIntnalkey, String issueYear, String stageCode ,BigDecimal amount);

}