package com.dcits.ensemble.rb.business.api.component.dd.tran;

import com.dcits.ensemble.rb.business.model.cm.transaction.BalAgrTypeEnum;
import com.dcits.ensemble.rb.business.model.cm.transaction.TransactionControlModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.TransactionOutModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.bal.AcctTransactionInModel;

/**
 * The interface Acct tra application.
 */
public interface IAcctTraApplication {

    /**
     * Gets bal agr type.
     *
     * @return the bal agr type
     */
    BalAgrTypeEnum getBalAgrType();

    /**
     * Acct tra transaction transaction out model.
     *
     * @param inModel      the in model
     * @param controlModel the control model
     * @return the transaction out model
     */
    TransactionOutModel acctTraTransaction(AcctTransactionInModel inModel, TransactionControlModel controlModel);
}
