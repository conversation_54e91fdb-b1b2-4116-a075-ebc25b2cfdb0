package com.dcits.ensemble.rb.business.api.component.td.tran;

import com.dcits.ensemble.rb.business.model.cm.transaction.TermTranControlModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.TermBaseModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.bal.AcctTransactionInModel;

import java.util.List;

/**
 * The interface Term tran check.
 */
public interface ITermTranCheck {
    /**
     * Check debt.
     *
     * @param termBaseModel the term base model
     * @param list          the list
     * @param controlModel  the control model
     */
    void checkDebt(TermBaseModel termBaseModel, List<AcctTransactionInModel> list, TermTranControlModel controlModel);

    /**
     * Check cret.
     *
     * @param termBaseModel the term base model
     * @param list          the list
     * @param controlModel  the control model
     */
    void checkCret(TermBaseModel termBaseModel,List<AcctTransactionInModel> list, TermTranControlModel controlModel);

}
