package com.dcits.ensemble.rb.business.api.component.td.tran;

import com.dcits.ensemble.rb.business.model.acct.FixedCallEnum;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * The type Term calc factory.
 */
@Component

public class TermCalcFactory implements ApplicationContextAware {
    private static Map<FixedCallEnum, ITermGetInterests> interestsMap;
    private static Map<FixedCallEnum, ITermCalcService> calcMap;

    /**
     * Gets i term get interests.
     *
     * @param fixedCall the fixed call
     * @return the i term get interests
     */
    public static ITermGetInterests getITermGetInterests(String fixedCall) {
        return interestsMap.get(FixedCallEnum.valueOf(fixedCall));
    }

    /**
     * Gets i term calc.
     *
     * @param fixedCall the fixed call
     * @return the i term calc
     */
    public static ITermCalcService  getITermCalc(String fixedCall) {
        return calcMap.get(FixedCallEnum.valueOf(fixedCall));
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, ITermGetInterests> map = applicationContext.getBeansOfType(ITermGetInterests.class);
        interestsMap = new HashMap<>(5);
        for (ITermGetInterests value : map.values()) {
            interestsMap.put(value.getFixedCall(), value);
        }

        Map<String, ITermCalcService> newMap = applicationContext.getBeansOfType(ITermCalcService.class);
        calcMap = new HashMap<>(5);
        for (ITermCalcService value : newMap.values()) {
            calcMap.put(value.getFixedCall(), value);
        }

    }
}
