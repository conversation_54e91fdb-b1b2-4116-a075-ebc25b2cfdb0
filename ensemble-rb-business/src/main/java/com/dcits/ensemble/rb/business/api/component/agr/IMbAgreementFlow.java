package com.dcits.ensemble.rb.business.api.component.agr;

import com.dcits.ensemble.rb.business.model.agr.AgreementTypeEnum;
import com.dcits.ensemble.rb.business.model.agr.MbAgreementModel;

/**
 * The interface Mb agreement flow.
 */
public interface IMbAgreementFlow {

  /**
   * Gets agreement class.
   *
   * @return the agreement class
   */
  AgreementTypeEnum getAgreementClass();

  /**
   * Check bean result.
   *
   * @param mbAgreementModel the mb agreement model
   * @return the bean result
   */
  void check(MbAgreementModel mbAgreementModel);

  /**
   * Process bean result.
   *
   * @param mbAgreementModel the mb agreement model
   * @return the bean result
   */
  MbAgreementModel process(MbAgreementModel mbAgreementModel);
}
