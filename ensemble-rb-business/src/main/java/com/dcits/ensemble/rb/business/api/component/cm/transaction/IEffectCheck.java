package com.dcits.ensemble.rb.business.api.component.cm.transaction;

import com.dcits.ensemble.rb.business.model.cm.transaction.bal.AcctTransactionInModel;

/***
 * 倒起息公共检查类
 */
public interface IEffectCheck {

    /**
     *校验倒起息生效日期
     * @param effectDate
     * @param acctOpenDate
     * @param othAcctOpenDate
     * @return
     */
    Boolean checkEffectDate(String effectDate, String acctOpenDate, String othAcctOpenDate);

    /**
     * 校验支取金额
     * @param acctTransactionInModel
     * @return
     */
    Boolean checkEffectBal(AcctTransactionInModel acctTransactionInModel);

    /**
     * 倒起息日期余额校验
     * @param acctTransactionInModel
     */
    void effectCheck(AcctTransactionInModel acctTransactionInModel);

    /**
     * 倒起息冲正校验
     * @param acctTransactionInModel
     */
    void reversalEffectCheck(AcctTransactionInModel acctTransactionInModel);
}
