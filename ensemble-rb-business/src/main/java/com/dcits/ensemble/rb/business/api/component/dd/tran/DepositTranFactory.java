package com.dcits.ensemble.rb.business.api.component.dd.tran;

import com.dcits.ensemble.rb.business.model.agr.AgreementTypeEnum;
import com.dcits.ensemble.rb.business.model.cm.transaction.BalAgrTypeEnum;
import com.dcits.ensemble.rb.business.model.acct.SourceModuleEnum;
import com.dcits.ensemble.rb.business.model.cm.transaction.TransactionControlModel;
import com.dcits.ensemble.rb.business.model.cm.common.RbAcctStandardModel;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcct;
import com.dcits.ensemble.util.BusiUtil;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * The type Deposit tran factory.
 * <AUTHOR>
 */
@Component

public class DepositTranFactory implements ApplicationContextAware {

    private static Map<BalAgrTypeEnum, IDepositTranApplication> tranBeanMap;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, IDepositTranApplication> map = applicationContext.getBeansOfType(IDepositTranApplication.class);
        tranBeanMap = new HashMap<>(5);
        for (IDepositTranApplication value : map.values()) {
            tranBeanMap.put(value.getBalAgrType(), value);
        }
    }

    private static BalAgrTypeEnum getEnum(RbAcct mbAcct, TransactionControlModel controlModel) {
        if (BusiUtil.isEquals(SourceModuleEnum.GL.toString(), mbAcct.getSourceModule())) {
            return BalAgrTypeEnum.GL;
        } else if (controlModel.isCorrect()) {
            return BalAgrTypeEnum.CORRECT;
        } else if (BusiUtil.isEquals(controlModel.getIsAgreement(), BalAgrTypeEnum.FIN.name())) {
            return BalAgrTypeEnum.FIN;
        } else if (BusiUtil.isEquals(controlModel.getIsAgreement(), BalAgrTypeEnum.YHT.name())) {
            return BalAgrTypeEnum.YHT;
        } else if (BusiUtil.isEquals(controlModel.getIsAgreement(), AgreementTypeEnum.ZXY.toString())) {
            return BalAgrTypeEnum.ZXQY;
        }else {
            return BalAgrTypeEnum.DEFAULT;
        }
        //活期金融接口实现的路由方式待优化

    }

    private static BalAgrTypeEnum getEnum(RbAcctStandardModel acctStdModel, TransactionControlModel controlModel) {
        if (BusiUtil.isEquals(SourceModuleEnum.GL.toString(), acctStdModel.getSourceModule())) {
            return BalAgrTypeEnum.GL;
        } else if (controlModel.isCorrect()) {
            return BalAgrTypeEnum.CORRECT;
        } else if (BusiUtil.isEquals(controlModel.getIsAgreement(), BalAgrTypeEnum.FIN.name())) {
            return BalAgrTypeEnum.FIN;
        } else if (BusiUtil.isEquals(controlModel.getIsAgreement(), BalAgrTypeEnum.YHT.name())) {
            return BalAgrTypeEnum.YHT;
        } else if (BusiUtil.isEquals(controlModel.getIsAgreement(), BalAgrTypeEnum.ZXQY.name())) {
            return BalAgrTypeEnum.ZXQY;
        } else if (BusiUtil.isEquals(controlModel.getIsAgreement(), BalAgrTypeEnum.NTE.name())) {
            return BalAgrTypeEnum.NTE;
        } else {
            return BalAgrTypeEnum.DEFAULT;
        }
        //活期金融接口实现的路由方式待优化

    }

    /**
     * Gets tran application.
     *
     * @param mbAcct       the mb acct
     * @param controlModel the control model
     * @return the tran application
     */
    @Deprecated
    public static IDepositTranApplication getTranApplication(RbAcct mbAcct, TransactionControlModel controlModel) {
        return tranBeanMap.get(getEnum(mbAcct, controlModel));
    }

    /**
     * Gets tran application.
     *
     * @param acctStdModel the mb acct
     * @param controlModel the control model
     * @return the tran application
     */
    public static IDepositTranApplication getTranApplication(RbAcctStandardModel acctStdModel, TransactionControlModel controlModel) {
        return tranBeanMap.get(getEnum(acctStdModel, controlModel));
    }

}
