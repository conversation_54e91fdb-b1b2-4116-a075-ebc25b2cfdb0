package com.dcits.ensemble.rb.business.api.component.td.tran;

import com.dcits.ensemble.rb.business.model.cm.transaction.TermTranControlModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.TermTranOutModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.TermAcctWtdinModel;

/**
 * The interface Term tran service.
 */
public interface ITermTranService {

    /**
     * Debt transaction term tran out model.
     *
     * @param acctWtdInModel the acct wtd in model
     * @param controlModel   the control model
     * @return the term tran out model
     */
    TermTranOutModel debtTransaction(TermAcctWtdinModel acctWtdInModel, TermTranControlModel controlModel);


//    /**
//     * Cret transaction transaction out model.
//     *
//     * @param inModel      the in model
//     * @param controlModel the control model
//     * @return the transaction out model
//     */
//    TransactionOutModel cretTransaction(AcctTransactionInModel inModel, TermTranControlModel controlModel);
}
