package com.dcits.ensemble.rb.business.api.component.fee;


import com.dcits.ensemble.rb.business.model.fee.MbServChargeModel;
import com.dcits.ensemble.rb.business.model.fee.MbChargeTypeEnum;
import com.dcits.ensemble.rb.business.model.cm.common.BaseModel;

/**
 * The interface Mb fee charge.
 * <AUTHOR>
public interface IMbFeeChargeReturn {

    /**
     * Gets charge type.
     *
     * @return the charge type
     */
    MbChargeTypeEnum getChargeType();

    /**
     * Execute bean result.
     *
     * @param mbServChargeModel the mb serv charge model
     * @param baseModel         the base model
     * @param charge            the charge
     * @param dealType          the deal type
     * @return the bean result
     */
    void execute(MbServChargeModel mbServChargeModel, BaseModel baseModel, String charge, String dealType);

    /**
     * Execute lost bean result.
     *
     * @param mbServChargeModel the mb serv charge model
     * @param baseModel         the base model
     * @param charge            the charge
     * @param dealType          the deal type
     * @return the bean result
     */
    void executeLost(MbServChargeModel mbServChargeModel, BaseModel baseModel, String charge, String dealType);

    /**
     * Check bean result.
     *
     * @param mbServChargeModel the mb serv charge model
     * @param baseModel         the base model
     * @param charge            the charge
     * @param dealType          the deal type
     * @return the bean result
     */
    void check(MbServChargeModel mbServChargeModel, BaseModel baseModel, String charge, String dealType);

}
