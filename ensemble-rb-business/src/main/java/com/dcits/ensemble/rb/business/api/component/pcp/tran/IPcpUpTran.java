package com.dcits.ensemble.rb.business.api.component.pcp.tran;

import com.dcits.ensemble.rb.business.model.pcp.PcpTranControlModel;
import com.dcits.ensemble.rb.business.model.pcp.PcpTranOutBaseModel;
import com.dcits.ensemble.rb.business.model.pcp.PcpUpTranModel;

/**
 * The interface Pcp up tran.
 */
public interface IPcpUpTran {
    /**
     * Up tran pcp tran out base model.
     *
     * @param upTranModel      the up tran model
     * @param tranControlModel the tran control model
     * @return the pcp tran out base model
     */
    PcpTranOutBaseModel upTran(PcpUpTranModel upTranModel, PcpTranControlModel tranControlModel);
}
