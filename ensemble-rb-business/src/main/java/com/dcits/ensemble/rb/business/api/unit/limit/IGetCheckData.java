package com.dcits.ensemble.rb.business.api.unit.limit;

import com.dcits.comet.rpc.api.model.BaseRequest;
import com.dcits.comet.util.json.JSONObject;

/**
 * <p>Name: 检查数据有效性</p>
 * <p/>
 * <p>@descrption:</p>
 * <p>功能描述</p>
 * <p>检查数据有效性</p>
 * <p>功能范围</p>
 * <p>1.检查数据</p>
 * <p>2.</p>
 *
 * <AUTHOR>
 * @date 2018/4/4 16:13
 */
public interface IGetCheckData {

  /**
   * 获取检查数据
   *
   * @return 返回结果
   */
  JSONObject getCheckData(BaseRequest request);

}
