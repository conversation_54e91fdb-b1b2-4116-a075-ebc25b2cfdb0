package com.dcits.ensemble.rb.business.api.component.dd.tran;

import com.dcits.ensemble.rb.business.model.cm.transaction.TransactionControlModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.TransactionOutModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.bal.AcctTransactionInModel;

/**
 * The interface Current tran service.
 */
public interface ICurrentTranService {

    /**
     * Debt transaction transaction out model.
     *deleteRestraints
     * @param inModel      the in model
     * @param controlModel the control model
     * @return the transaction out model
     */
    TransactionOutModel debtTransaction(AcctTransactionInModel inModel, TransactionControlModel controlModel);


    /**
     * Cret transaction transaction out model.
     *
     * @param inModel      the in model
     * @param controlModel the control model
     * @return the transaction out model
     */
    TransactionOutModel cretTransaction(AcctTransactionInModel inModel, TransactionControlModel controlModel);
}
