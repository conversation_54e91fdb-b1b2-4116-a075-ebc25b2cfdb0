package com.dcits.ensemble.rb.business.api.unit.acct.close;


import com.dcits.ensemble.rb.business.model.acct.close.AcctCloseModel;

/**
 * @Description: 对私销户回退接口
 * @author: yangxyo
 * @date: 2018/04/04
 */
public interface ICloseReturn {
    /**
     * @Description: 对私销户回退接口
     * @author: yangxyo
     * @date: 2018/04/04
     * @param acctCloseModel
     */
    void executeFlow(AcctCloseModel acctCloseModel);
}