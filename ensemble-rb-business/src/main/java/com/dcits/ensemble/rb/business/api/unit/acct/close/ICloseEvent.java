package com.dcits.ensemble.rb.business.api.unit.acct.close;

import com.dcits.ensemble.rb.business.model.acct.close.AcctCloseModel;
import com.dcits.ensemble.rb.business.model.acct.close.AcctNewCloseModel;
import com.dcits.ensemble.rb.business.model.acct.close.CLoseControlModel;

/**
 * @Description 销户接口
 * <AUTHOR>
 * @Date 2018/3/5:20:22
 * @Address  基地
 */

public interface ICloseEvent {
    /**
     * @Description  销户功能
     * @Name  execute
     * @Param acctCloseModel
     * @return  AcctCloseModel
     * <AUTHOR>
     * @Date 2018/3/5:20:22
     * @Address 基地
     */

    AcctCloseModel execute(AcctNewCloseModel acctNewCloseModel, CLoseControlModel cLoseControlModel) ;
}
