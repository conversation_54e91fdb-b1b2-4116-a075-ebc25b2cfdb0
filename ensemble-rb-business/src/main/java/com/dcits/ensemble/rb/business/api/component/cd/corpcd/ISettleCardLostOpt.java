package com.dcits.ensemble.rb.business.api.component.cd.corpcd;

import com.dcits.ensemble.rb.business.model.cm.SettleCardModel;

/**
 * The interface Settle card lost opt.
 */
public interface ISettleCardLostOpt {
    /**
     * Bal lost.
     *
     * @param settleCardModel the settle card model
     */
    void balLost(SettleCardModel settleCardModel);

    /**
     * Farmal lost.
     *
     * @param settleCardModel the settle card model
     */
    void farmalLost(SettleCardModel settleCardModel);

    /**
     * Remove bal lost.
     *
     * @param settleCardModel the settle card model
     */
    void removeBalLost(SettleCardModel settleCardModel);

    /**
     * Remove farmal lost.
     *
     * @param settleCardModel the settle card model
     */
    void removeFarmalLost(SettleCardModel settleCardModel);
}
