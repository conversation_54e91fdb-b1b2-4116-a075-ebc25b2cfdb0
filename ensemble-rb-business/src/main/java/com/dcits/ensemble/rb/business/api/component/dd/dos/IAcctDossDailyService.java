package com.dcits.ensemble.rb.business.api.component.dd.dos;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcct;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctDossDaily;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctDossReg;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbPreDossReg;

public interface IAcctDossDailyService {

    /**
     * 记录转久悬成功信息
     * @param rbAcct
     * @return
     */
    RbAcctDossDaily createRbAcctDossDailySuccess(RbAcct rbAcct);

    /**
     * 记录对公转久悬失败信息
     * @param RbAcct
     * @param failMsg
     * @return
     */
    RbAcctDossDaily createRbAcctDossDailyFailed1(RbAcct RbAcct, String failMsg);

    /**
     * 记录个人转久悬失败信息
     * @param rbAcct
     * @param failMsg
     * @return
     */
    RbAcctDossDaily createRbAcctDossDailyFailed(RbAcct rbAcct, String failMsg);
}