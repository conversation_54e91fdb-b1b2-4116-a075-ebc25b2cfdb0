package com.dcits.ensemble.rb.business.api.unit.acct.amend;

import com.dcits.ensemble.rb.business.model.cm.amend.RemainTermAmendModel;

/**
 * <p>Name:  com.dcits.ensemble.rb.business.api.business.amend</p>
 * <p>Description: 账户剩余期限变更处理</p>
 *
 * @author: fugd
 * @date 2023/03/19
 */
public interface IMbAcctRemainTermAmend {
    /**
     * 账户剩余期限变更主逻辑处理
     * @param remainTermAmendModel
     */
    void remainTermAmend(RemainTermAmendModel remainTermAmendModel);

    /**
     * 账户剩余期限变更前检查  -- 可以不需要
     * @param remainTermAmendModel
     */
    void check(RemainTermAmendModel remainTermAmendModel);
}
