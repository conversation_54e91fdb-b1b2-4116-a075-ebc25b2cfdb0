package com.dcits.ensemble.rb.business.api.unit.acct.open;

import com.dcits.ensemble.rb.business.model.acct.OpenEnum;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by weiping on 2018/3/8.
 */
@Component

public class OpenEventFactory implements ApplicationContextAware {

    private static Map<OpenEnum, IOpenEvent> openEventMap  = new HashMap<>();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {

        Map<String, IOpenEvent> map = applicationContext.getBeansOfType(IOpenEvent.class);

        for(IOpenEvent value:map.values()){
            openEventMap.put(value.getOpenModel(), value);
        }
    }

    public static IOpenEvent getOpenEvent(OpenEnum openEnum){
        return openEventMap.get(openEnum);
    }
}
