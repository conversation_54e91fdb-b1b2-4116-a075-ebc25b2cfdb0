package com.dcits.ensemble.rb.business.api.component.cd.corpcd;

import com.dcits.ensemble.rb.business.model.cm.pwd.MbPwdModel;

import java.util.List;


/**
 * The interface Mb settle card password stor.
 */
public interface IMbSettleCardPasswordStor {

    /**
     * Create password.
     *
     * @param mbPwds   the mb pwds
     * @param prodType the prod type
     */
    void createPassword(List<MbPwdModel> mbPwds,String prodType);

    /**
     * Create pwd.
     *
     * @param mbPwdModel the mb pwd model
     * @param prodType   the prod type
     */
    void createPwd(MbPwdModel mbPwdModel,String prodType);

    /**
     * Update pwd.
     *
     * @param mbPwdModel the mb pwd model
     * @param prodType   the prod type
     */
    void updatePwd(MbPwdModel mbPwdModel,String prodType);

    /**
     * Validate pwd.
     *
     * @param mbPwdModel the mb pwd model
     * @param prodType   the prod type
     */
    void validatePwd(MbPwdModel mbPwdModel,String prodType);

    /**
     * Reset pwd.
     *
     * @param mbPwdModel the mb pwd model
     * @param prodType   the prod type
     */
    void resetPwd(MbPwdModel mbPwdModel,String prodType);


    /**
     * Unlock pwd.
     *
     * @param mbPwdModel the mb pwd model
     * @param prodType   the prod type
     */
    void unlockPwd(MbPwdModel mbPwdModel,String prodType);

    /**
     * Acct active.
     *
     * @param mbPwdModel the mb pwd model
     * @param prodType   the prod type
     */
    void acctActive(MbPwdModel mbPwdModel,String prodType);
}
