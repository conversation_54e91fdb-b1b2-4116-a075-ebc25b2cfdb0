package com.dcits.ensemble.rb.business.api.component.agr;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementNorthbound;
import com.dcits.ensemble.rb.business.model.cm.transaction.reversal.TransactionReversalInModel;
import com.dcits.ensemble.rb.business.model.cm.common.RbAcctStandardModel;

/**
 * 北向通业务组件
 */
public interface IBXTBusiComponent {
    //签约检查
    public void signCheck(RbAcctStandardModel rbAcctStandardModel);
    //解约检查
    public void unsignCheck(RbAgreementNorthbound rbAgreementNorthbound);
    //解约确认检查
    public void unsignConfirmCheck(RbAgreementNorthbound rbAgreementNorthbound,RbAcctStandardModel rbAcctStandardModel);
    //解约确认
    public void unsignConfirm(RbAgreementNorthbound rbAgreementNorthbound);
    //签约
    public String sign(RbAcctStandardModel rbAcctStandardModel);
    //解约
    public void unsign(RbAgreementNorthbound rbAgreementNorthbound,RbAcctStandardModel rbAcctStandardModel);
    //冲正更新
    public void reversalUpdCheck(RbAcctStandardModel rbAcctStandardModel, TransactionReversalInModel transactionReversalInModel);

}
