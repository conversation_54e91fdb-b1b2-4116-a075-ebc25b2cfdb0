package com.dcits.ensemble.rb.business.api.component.cm.transaction;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * <p>Name:  com.dcits.ensemble.rb.business.api.business.transaction.event</p>
 * <p>Description: </p>
 *
 * <AUTHOR>
 * @date 2018/3/14 21:37
 */
@Component

public class TranEventFactory implements ApplicationContextAware {

    private static Map<String, ICretEvent> cretBeanMap;
    private static Map<String, IDebtEvent> debtBeanMap;

    // 静态工厂单一实现 带扩展
    public static ICretEvent getCretEvent() {
        Iterator iterator = cretBeanMap.values().iterator();
        if (iterator.hasNext()) {
            return (ICretEvent) iterator.next();
        }
        return null;
    }

    // 静态工厂单一实现 带扩展
    public static IDebtEvent getDebtEvnt() {
        Iterator iterator = debtBeanMap.values().iterator();
        if (iterator.hasNext()) {
            return (IDebtEvent) iterator.next();
        }
        return null;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, ICretEvent> cretMap = applicationContext.getBeansOfType(ICretEvent.class);
        Map<String, IDebtEvent> debtMap = applicationContext.getBeansOfType(IDebtEvent.class);
        cretBeanMap = new HashMap<>();
        debtBeanMap = new HashMap<>();

        for (Map.Entry<String, ICretEvent> entry : cretMap.entrySet()) {
            cretBeanMap.put(entry.getKey(), entry.getValue());
        }

        for (Map.Entry<String, IDebtEvent> entry : debtMap.entrySet()) {
            debtBeanMap.put(entry.getKey(), entry.getValue());
        }
    }

}
