package com.dcits.ensemble.rb.business.api.component.agr;


import com.dcits.ensemble.rb.business.model.agr.AgreementTypeEnum;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * The type Agreement flow factory.
 */
@Component

public class AgreementFlowFactory implements ApplicationContextAware {

    private static Map<AgreementTypeEnum, IMbAgreementFlow> agreementOperateMap;

    /**
     * Gets agreement.
     *
     * @param type the type
     * @return the agreement
     */
    public static IMbAgreementFlow getAgreement(AgreementTypeEnum type) {
        return agreementOperateMap.get(type);
    }

    /**
     * Gets all agreement operate.
     *
     * @return the all agreement operate
     */
    public static Map<AgreementTypeEnum, IMbAgreementFlow> getAllAgreementOperate() {
        return agreementOperateMap;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, IMbAgreementFlow> map = applicationContext.getBeansOfType(IMbAgreementFlow.class);
        agreementOperateMap = new HashMap<>();
        for (IMbAgreementFlow value : map.values()) {
            agreementOperateMap.put(value.getAgreementClass(), value);
        }
    }

}
