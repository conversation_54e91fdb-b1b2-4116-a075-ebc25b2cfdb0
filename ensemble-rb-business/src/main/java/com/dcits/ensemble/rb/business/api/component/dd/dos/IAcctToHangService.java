package com.dcits.ensemble.rb.business.api.component.dd.dos;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcct;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctDossDaily;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctDossReg;

/**
 * The interface Acct hang service.
 */
public interface IAcctToHangService {

    /**
     * Acct hang bean result.

     * @param rbAcctDossReg the mb acct doss reg models
     * @return void
     */
    RbAcctDossDaily acctHang(RbAcctDossReg rbAcctDossReg);

    /**
     * Check list.
     *
     * @param rbAcctDossReg   the mb acct doss reg models
     * @param rbAcctDossDaily the mb acct doss reg Daily models
     * @return void
     */
    void check(RbAcctDossReg rbAcctDossReg, RbAcctDossDaily rbAcctDossDaily);

    /**
     * 转久悬处理
     * @param rbAcct
     * @param rbAcctDossReg
     */
    void acctHang(RbAcct rbAcct, RbAcctDossReg rbAcctDossReg);

}
