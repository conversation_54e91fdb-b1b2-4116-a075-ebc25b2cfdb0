package com.dcits.ensemble.rb.business.api.unit.acct.settle;


import com.dcits.ensemble.rb.business.model.interest.MbIntCycleModel;
import com.dcits.ensemble.rb.business.model.acct.settle.AcctSettleModel;

import java.math.BigDecimal;

/**
 * @Description 账户结算入账接口 提供存款结算入账 贷款结算入账功能
 * @Name ISettleEvent
 * <AUTHOR>
 * @Date 2018/3/2:16:53
 * @Address 基地
 */

public interface ICycleSettleEvent {

    /**
     * @return AcctSettleModel
     * @Description 利息结算入账接口
     * @Name process
     * @Param MbIntCycleModel mbIntCycleModel, String tranType, BigDecimal billeAmt
     * <AUTHOR>
     * @Date 2018/3/5:16:13
     * @Address 基地
     */
    AcctSettleModel process(MbIntCycleModel mbIntCycleModel, String tranType, BigDecimal billeAmt);
}
