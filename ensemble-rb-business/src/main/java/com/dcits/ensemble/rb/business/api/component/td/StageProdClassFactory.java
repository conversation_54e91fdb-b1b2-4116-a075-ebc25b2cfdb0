package com.dcits.ensemble.rb.business.api.component.td;

import com.dcits.ensemble.rb.business.model.td.certtd.StageProdClassEnum;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * The type Stage prod class factory.
 */
@Component

public class StageProdClassFactory implements ApplicationContextAware {

    private static Map< StageProdClassEnum, IStageProdClass> tranBeanMap;

    /**
     * Gets i stage prod class.
     *
     * @param stageProdClass the stage prod class
     * @return the i stage prod class
     */
    public static IStageProdClass getIStageProdClass (String stageProdClass) {
        return tranBeanMap.get(StageProdClassEnum.valueOf(stageProdClass));
    }

    /**
     * 结构性存款开立工厂类
     * @param applicationContext
     * @throws BeansException
     */
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {

        Map<String, IStageProdClass > map = applicationContext.getBeansOfType(IStageProdClass.class);

        tranBeanMap = new HashMap<>(5);
        for (IStageProdClass value : map.values()) {
            tranBeanMap.put(value.getStageProdClass(), value);
        }
    }
}

