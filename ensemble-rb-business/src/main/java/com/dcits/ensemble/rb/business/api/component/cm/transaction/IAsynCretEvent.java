package com.dcits.ensemble.rb.business.api.component.cm.transaction;

import com.dcits.ensemble.rb.business.model.cm.transaction.TransactionControlModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.TransactionOutModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.bal.AcctTransactionInModel;

/**
 * <p>Name:  com.dcits.ensemble.rb.business.api.business.transaction</p>
 * <p>Description: 贷记记账事件接口 基础实现</p>
 * 功能实现 账户贷记记账
 * 参数定义
 * -输入模型 AcctTransactionInModel
 * 包含账户交易主体账户信息模型mbAcct 交易类型对应模型mbTranDef 对手交易信息othInternalKey等
 * 交易关键信息 交易金额tranAmt 交易币种ccy 交易机构tranBranch
 * 以及登记流水相关信息 tranDate 操作柜员
 * -必输控制 推荐使用AcctTransactionInModel
 * (@NonNull RbAcct mbAcct, @NonNull MbTranDef mbTranDef, @NonNull BigDecimal tranAmt) 构造函数
 * 客户号
 * 卡号赋值cardNo 不再赋值 baseAcctNo 卡号账号字段区分 避免后续处理判断处理不清晰
 * <p>
 * <p>
 * * 功能范围：
 * 异步记账事件
 * 主要分为
 * 1.检查功能 为try接口提供对应的检查功能
 * 2.记账功能 为confirm接口提供下账和入账的功能
 *
 * <AUTHOR>
 * @date 2018/3/2
 * @see AcctTransactionInModel
 */
public interface IAsynCretEvent {


    /**
     * @param acctTransactionInModel  输入模型
     * @param transactionControlModel 控制模型
     * @return TransactionOutModel
     */
    TransactionOutModel execute(AcctTransactionInModel acctTransactionInModel, TransactionControlModel transactionControlModel);

    /**
     * @param acctTransactionInModel  输入模型
     * @param transactionControlModel 控制模型
     */
    void check(AcctTransactionInModel acctTransactionInModel, TransactionControlModel transactionControlModel);
}
