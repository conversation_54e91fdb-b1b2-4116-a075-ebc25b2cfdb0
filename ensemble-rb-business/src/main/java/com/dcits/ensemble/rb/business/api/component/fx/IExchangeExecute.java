package com.dcits.ensemble.rb.business.api.component.fx;

import com.dcits.ensemble.rb.business.model.fx.ExchangeExecuteEnum;
import com.dcits.ensemble.rb.business.model.fx.MbExchangeModel;
import com.dcits.ensemble.rb.business.model.fx.MbExchangeOutModel;

/**
 * The interface Exchange execute.
 */
public interface IExchangeExecute {

    /**
     * Execute mb exchange out model.
     *
     * @param mbExchangeModel the mb exchange model
     * @return the mb exchange out model
     */
    MbExchangeOutModel execute(MbExchangeModel mbExchangeModel);

    /**
     * Gets exchange.
     *
     * @return the exchange
     */
    ExchangeExecuteEnum getExchange();

}
