package com.dcits.ensemble.rb.business.api.component.cm.transaction;


import com.dcits.ensemble.rb.business.model.cm.AccountRbFlowModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.TransactionControlModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.TransactionOutModel;

/**
 * INormalAccountingApplication
 */
public interface INormalAccountingApplication {

    /**
     *
     * @param inModel
     * @param controlModel
     * @return
     */
    TransactionOutModel normalAccounting(AccountRbFlowModel inModel, TransactionControlModel controlModel);
}
