package com.dcits.ensemble.rb.business.api.component.dd.dos;

import com.dcits.ensemble.rb.business.model.cm.common.RbAcctStandardModel;

/**
 * 不动/久悬户激活
 */
public interface IAutoToActive {

    /**
     * 不动户自动激活
     * @param rbAcctStandardModel
     */
    void dormantToActive(RbAcctStandardModel rbAcctStandardModel);

    /**
     * 久悬户自动激活
     * @param rbAcctStandardModel
     */
    void dossToActive(RbAcctStandardModel rbAcctStandardModel);
}
