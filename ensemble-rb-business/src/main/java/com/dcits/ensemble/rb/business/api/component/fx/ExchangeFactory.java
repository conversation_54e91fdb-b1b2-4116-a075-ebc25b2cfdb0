package com.dcits.ensemble.rb.business.api.component.fx;

import com.dcits.ensemble.rb.business.common.constant.BaseEvent;
import com.dcits.ensemble.rb.business.model.fx.ExchangeExecuteEnum;
import com.dcits.ensemble.rb.business.model.interest.model.ExchangeTypeEnum;
import com.dcits.ensemble.util.BusiUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * The type Exchange factory.
 */
@Component
@Slf4j
public class ExchangeFactory implements ApplicationContextAware {



    private static Map<ExchangeExecuteEnum, IExchangeExecute> exchangeMap;

    /**
     * Get exchange execute exchange execute.
     *
     * @param opType    the op type
     * @param eventType the event type
     * @return the exchange execute
     */
    public static IExchangeExecute getExchangeExecute(String opType, String eventType) {
        ExchangeExecuteEnum exEnum = getEnum(opType, eventType);
        log.debug("ExchangeExecuteEnum:{}", exEnum);
        return exchangeMap.get(exEnum);
    }

    public static IExchangeExecute getExchangeExecute(String opType, String eventType, String isOverdraftCard) {
        ExchangeExecuteEnum exEnum = getEnum(opType, eventType, isOverdraftCard);
        log.debug("ExchangeExecuteEnum:{}", exEnum);
        return exchangeMap.get(exEnum);
    }


    private static ExchangeExecuteEnum getEnum(String opType, String eventType) {

        if (BusiUtil.isEquals(eventType, BaseEvent.DEBT.toString())) {

            if (BusiUtil.isEquals(ExchangeTypeEnum.CASH_TO_CASH, opType) || BusiUtil.isEquals(ExchangeTypeEnum.CASH_TO_ACCT, opType)) {
                return ExchangeExecuteEnum.DEBTCASH;
            }
            if (BusiUtil.isEquals(ExchangeTypeEnum.ACCT_TO_CASH, opType) || BusiUtil.isEquals(ExchangeTypeEnum.ACCT_TO_ACCT, opType)) {
                return ExchangeExecuteEnum.DEBTAC;
            }

        } else if (BusiUtil.isEquals(eventType, BaseEvent.CRET.toString())) {

            if (BusiUtil.isEquals(ExchangeTypeEnum.CASH_TO_CASH, opType) || BusiUtil.isEquals(ExchangeTypeEnum.ACCT_TO_CASH, opType)) {
                return ExchangeExecuteEnum.CRETCASH;
            }
            if (BusiUtil.isEquals(ExchangeTypeEnum.CASH_TO_ACCT, opType) || BusiUtil.isEquals(ExchangeTypeEnum.ACCT_TO_ACCT, opType)) {
                return ExchangeExecuteEnum.CRETAC;
            }
        }
        throw BusiUtil.createBusinessException("MB9001", eventType);
    }

    private static ExchangeExecuteEnum getEnum(String opType, String eventType, String isOverdraftCard) {

        if (BusiUtil.isEquals(eventType, BaseEvent.DEBT.toString())) {

            if (BusiUtil.isEquals(ExchangeTypeEnum.CASH_TO_CASH, opType) || BusiUtil.isEquals(ExchangeTypeEnum.CASH_TO_ACCT, opType)) {
                return ExchangeExecuteEnum.DEBTCASH;
            }

            if ((BusiUtil.isEquals(ExchangeTypeEnum.ACCT_TO_CASH, opType) || BusiUtil.isEquals(ExchangeTypeEnum.ACCT_TO_ACCT, opType)) && BusiUtil.isEqualY(isOverdraftCard)) {
                return ExchangeExecuteEnum.DEBTACCOC;
            }

            if (BusiUtil.isEquals(ExchangeTypeEnum.ACCT_TO_CASH, opType) || BusiUtil.isEquals(ExchangeTypeEnum.ACCT_TO_ACCT, opType)) {
                return ExchangeExecuteEnum.DEBTAC;
            }


        } else if (BusiUtil.isEquals(eventType, BaseEvent.CRET.toString())) {

            if (BusiUtil.isEquals(ExchangeTypeEnum.CASH_TO_CASH, opType) || BusiUtil.isEquals(ExchangeTypeEnum.ACCT_TO_CASH, opType)) {
                return ExchangeExecuteEnum.CRETCASH;
            }
            if (BusiUtil.isEquals(ExchangeTypeEnum.CASH_TO_ACCT, opType) || BusiUtil.isEquals(ExchangeTypeEnum.ACCT_TO_ACCT, opType)) {
                return ExchangeExecuteEnum.CRETAC;
            }
        }
        throw BusiUtil.createBusinessException("MB9001", eventType);
    }


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, IExchangeExecute> map = applicationContext.getBeansOfType(IExchangeExecute.class);
        exchangeMap = new HashMap<>(5);
        for (IExchangeExecute value : map.values()) {
            exchangeMap.put(value.getExchange(), value);
        }
    }
}
