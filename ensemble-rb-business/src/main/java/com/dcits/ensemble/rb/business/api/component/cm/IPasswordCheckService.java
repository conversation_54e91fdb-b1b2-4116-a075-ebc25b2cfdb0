package com.dcits.ensemble.rb.business.api.component.cm;

import com.dcits.ensemble.rb.business.model.cm.common.RbAcctStandardModel;
import org.springframework.stereotype.Component;

/**
 * The interface Password check service.
 * <AUTHOR>
 */
@Component
public interface IPasswordCheckService {


    /**
     * Check password bean result.
     *
     * @param baseAcctNo     the base acct no
     * @param prodType       the prod type
     * @param ccy            the acctCcy
     * @param seqNo          the seq no
     * @param withdrawalType the withdrawal type
     * @param password       the password
     * @return the bean result
     * 密码检查
     */
//    @Check
//    @GravityComponent(navigationMenu = "no-group", name = "密码检查")
    void checkPassword(String baseAcctNo, String prodType, String ccy, String seqNo, String withdrawalType, String password) ;

    /**
     * Check password bean result.
     *
     * @param baseAcctNo     the base acct no
     * @param prodType       the prod type
     * @param ccy            the acctCcy
     * @param seqNo          the seq no
     * @param withdrawalType the withdrawal type
     * @param password       the password
     * @return the bean result
     * 密码检查
     */
//    @Check
//    @GravityComponent(navigationMenu = "no-group", name = "密码检查")
    void checkPasswordByStatusFlag(String baseAcctNo, String prodType, String ccy, String seqNo, String withdrawalType, String password, String statusFlag) ;

    /**
     * Check password bean result.
     *
     * @param baseAcctNo     the base acct no
     * @param prodType       the prod type
     * @param ccy            the acctCcy
     * @param seqNo          the seq no
     * @param withdrawalType the withdrawal type
     * @param password       the password
     * @param skipCheckFlag      跳过密码检查标识
     * @return the bean result
     * 跳过密码检查密码检查
     */
//    @Check
//    @GravityComponent(navigationMenu = "no-grousp", name = "可选择性密码校验" , desc = "通过是否密码校验标识选择性进行密码校验")
    void checkPasswordWithPass(String baseAcctNo, String prodType, String ccy, String seqNo, String withdrawalType, String password,String skipCheckFlag) ;

    /**
     *  密码状态校验
     * @param standardModel standardModel
     */
     void checkCurrentPassWordStatus(RbAcctStandardModel standardModel);
}
