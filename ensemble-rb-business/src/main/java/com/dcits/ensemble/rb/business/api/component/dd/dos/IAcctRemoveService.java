package com.dcits.ensemble.rb.business.api.component.dd.dos;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctDossReg;
import com.dcits.ensemble.rb.business.model.acct.close.MbAcctDossModel;

import java.util.List;

/**
 * The interface Acct remove service.
 */
public interface IAcctRemoveService {

    /**
     * Check bean result.
     *
     * @param mbAcctDossModel the mb acct doss model
     * @return the bean result
     */
    public void check(List<MbAcctDossModel> mbAcctDossModel);

    /**
     * Acct remove bean result.
     *
     * @param rbAcctDossReg the mb acct doss model
     * @return the bean result
     */
    public void acctToOut(RbAcctDossReg rbAcctDossReg);

    /**
     * Acct remove bean result.
     *
     * @param mbAcctDossModel the mb acct doss model
     * @return the bean result
     */
    public void acctRemove(List<MbAcctDossModel> mbAcctDossModel);

}
