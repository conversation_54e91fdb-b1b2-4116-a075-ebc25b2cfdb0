package com.dcits.ensemble.rb.business.api.component.pcp.plan;

import com.dcits.ensemble.rb.business.model.pcp.PcpTranControlModel;
import com.dcits.ensemble.rb.business.model.pcp.PcpTranOutBaseModel;
import com.dcits.ensemble.rb.business.model.pcp.PcpUpTranModel;
import com.dcits.ensemble.rb.business.common.constant.PcpUpEnum;

/**
 * The interface Pcp up plan application.
 */
public interface IPcpUpPlanApplication {

    /**
     * Gets up plan type.
     *
     * @return the up plan type
     */
    PcpUpEnum.UpPlan getUpPlanType();

    /**
     * Up plan application pcp tran out base model.
     *
     * @param upTranModel      the up tran model
     * @param tranControlModel the tran control model
     * @return the pcp tran out base model
     */
    PcpTranOutBaseModel upPlanApplication(PcpUpTranModel upTranModel, PcpTranControlModel tranControlModel);
}
