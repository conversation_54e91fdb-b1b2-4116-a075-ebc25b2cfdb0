package com.dcits.ensemble.rb.business.api.unit.acct.settle;

import com.dcits.ensemble.rb.business.model.acct.settle.*;

import java.util.List;

/**
 *
 * * <p>Name:  com.dcits.ensemble.rb.business.api.business.settle</p>
 * <p>Description: 账户结算信息增删改操作接口 基础组件</p>
 * 功能实现 协议账户，转存账户，资金扣款账户，利息入账账户，资金来源账户结算信息的增删改功能
 * 参数定义：账户结算信息模型 增加账户结算信息时，账户主键internalKey和账户四要素必输其一，结算账户标识符settleAcctInternelKey必输
 *                            更新账户结算信息时，账户主键internalKey，结算编号必输
 *                            删除账户信息时，账户主键internalKey，该方法会删除账户结算信息表中同类型（SETTLE_ACCT_CLASS相同）的结算信息
 * Created by suwu on 2018/3/14
 */
public interface IMbSettleOperate {
    /**
     * 获取结算类型枚举分类类型
     * @return
     */

    SettleClassEnum getMbSettleClass();

    /**
     * 增加账户的结算信息（单条）
     * 增加类型包含账户的资金来源账户信息，协议账户信息，转存账户信息，资金扣划账户信息，利息入账账户信息
     * 路由规则由MbSettleOperateFactory提供
     * @param mbAcctSettleModel
     * @param creatSettleControlModel
     */
    void addSettle(MbAcctSettleModel mbAcctSettleModel, CreatSettleControlModel creatSettleControlModel);

    /**
     * 增加账户的结算信息（多条）
     * 增加类型包含账户的资金来源账户信息，协议账户信息，转存账户信息，资金扣划账户信息，利息入账账户信息
     * 路由规则由MbSettleOperateFactory提供
     * @param mbAcctSettleModels
     * @param creatSettleControlModel
     */
    void addSettle(List<MbAcctSettleModel> mbAcctSettleModels, CreatSettleControlModel creatSettleControlModel);

    /**
     * 删除账户的结算信息
     * 删除类型包含账户的资金来源账户信息，协议账户信息，转存账户信息，资金扣划账户信息，利息入账账户信息
     * 路由规则由MbSettleOperateFactory提供
     * @param internalKey
     * @param deleteSettleControlModel
     */
    void delSettle(Long internalKey, DeleteSettleControlModel deleteSettleControlModel,String clientNo);

    /**
     * 修改账户的结算信息
     * 删除类型包含账户的资金来源账户信息，协议账户信息，转存账户信息，资金扣划账户信息，利息入账账户信息
     * 路由规则由MbSettleOperateFactory提供
     * @param updateSettleControlModel
     * @param mbAcctSettleModels
     */
    void updSettle(List<MbAcctSettleModel> mbAcctSettleModels, UpdateSettleControlModel updateSettleControlModel);
}
