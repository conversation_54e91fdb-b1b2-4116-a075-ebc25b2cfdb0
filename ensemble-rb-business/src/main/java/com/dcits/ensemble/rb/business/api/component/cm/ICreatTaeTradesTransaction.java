package com.dcits.ensemble.rb.business.api.component.cm;

import com.dcits.ensemble.rb.business.common.constant.RbCurrentTaeTradesEnum;
import com.dcits.ensemble.rb.business.model.cm.transaction.TransactionControlModel;
import com.dcits.ensemble.rb.business.model.cm.restful.rb.AsynNormalCurrentCretIn;
import com.dcits.ensemble.rb.business.model.cm.restful.rb.AsynNormalCurrentDebtIn;
import com.dcits.ensemble.rb.business.model.cm.restful.tae.TaeAcctMaintradesRow;
import com.dcits.ensemble.rb.business.model.cm.restful.tae.TaeAcctSubtradesRow;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020年5月13日10:33:19
 * tae组织子流水
 */
public interface ICreatTaeTradesTransaction {
    /***
     *
     * 获取交易类型
     * @return
     */
    RbCurrentTaeTradesEnum getTaeTradesType();

    /***
     * 创建tae流水(贷)
     *
     * @param maintradesRow maintradesRow
     * @param asynNormalCurrentCretIn asynNormalCurrentCretIn
     * @param controlModel controlModel
     * @return taeAcctSubtradesRowList
     */
    List<TaeAcctSubtradesRow> creatCretTaeTrades(TaeAcctMaintradesRow maintradesRow, AsynNormalCurrentCretIn asynNormalCurrentCretIn,
                                                 TransactionControlModel controlModel);


    /***
     * 创建tae流水(借)
     * @Description
     * @param maintradesRow maintradesRow
     * @param asynNormalCurrentDebtIn asynNormalCurrentDebtIn
     * @param controlModel controlModel
     * @return taeAcctSubtradesRowList
     */
    List<TaeAcctSubtradesRow> creatDebtTaeTrades(TaeAcctMaintradesRow maintradesRow, AsynNormalCurrentDebtIn asynNormalCurrentDebtIn,
                                                 TransactionControlModel controlModel);

}