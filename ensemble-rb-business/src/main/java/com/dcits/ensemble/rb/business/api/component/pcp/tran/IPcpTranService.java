package com.dcits.ensemble.rb.business.api.component.pcp.tran;

import com.dcits.ensemble.rb.business.model.pcp.PcpDownTranModel;
import com.dcits.ensemble.rb.business.model.pcp.PcpTranControlModel;
import com.dcits.ensemble.rb.business.model.pcp.PcpUpTranModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.TransactionOutModel;

/**
 * The interface Pcp tran service.
 */
public interface IPcpTranService {

    /**
     * Up transaction transaction out model.
     *
     * @param inModel      the in model
     * @param controlModel the control model
     * @return the transaction out model
     */
    TransactionOutModel upTransaction(PcpUpTranModel inModel, PcpTranControlModel controlModel);

    /**
     * Down transaction transaction out model.
     *
     * @param inModel      the in model
     * @param controlModel the control model
     * @return the transaction out model
     */
    TransactionOutModel  downTransaction(PcpDownTranModel inModel, PcpTranControlModel controlModel);

}
