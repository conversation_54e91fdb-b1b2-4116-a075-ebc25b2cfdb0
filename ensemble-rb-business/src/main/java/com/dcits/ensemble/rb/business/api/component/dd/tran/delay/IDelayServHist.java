package com.dcits.ensemble.rb.business.api.component.dd.tran.delay;

import com.dcits.ensemble.rb.business.model.res.restraints.OperateRestraintsOutModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.TransactionControlModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.bal.AcctTransactionInModel;



/**
 * <AUTHOR>
 * 功能实现:24小时到账登记接口
 * 功能范围:
 * 1.如果到帐方式选择24小时到账，则活期转账会调用本接口进行延时转账信息登记
 * 2.登记当前时间点24小时以后的时间戳为到账时间，提供给定时任务扫描使用
 */
public interface IDelayServHist {

    /**
     * 延迟转账
     * @param acctTransactionInModel
     * @param transactionControlModel
     * @return
     */
    OperateRestraintsOutModel delayServ(AcctTransactionInModel acctTransactionInModel, TransactionControlModel transactionControlModel);
}
