package com.dcits.ensemble.rb.business.api.component.dd.dos;

import com.dcits.ensemble.rb.business.model.acct.close.MbAcctDossModel;

/**
 * The interface Acct active service.
 */
public interface IAcctActiveService {
    /**
     * Check bean result.
     *
     * @param mbAcctDossModel the mb acct doss model
     * @return the bean result
     */
    void check(MbAcctDossModel mbAcctDossModel);

    /**
     * Acct active bean result.
     *
     * @param mbAcctDossModel the mb acct doss model
     * @return the bean result
     */
    void acctActive(MbAcctDossModel mbAcctDossModel);

}
