package com.dcits.ensemble.rb.business.api.unit.interest.cycle;

import com.dcits.ensemble.rb.business.model.acct.SourceModuleEnum;
import com.dcits.ensemble.rb.business.model.interest.CycleTypeEnum;
import com.dcits.ensemble.rb.business.model.cm.common.RbAcctStandardModel;
import com.dcits.ensemble.util.BusiUtil;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by furongb on 2018/3/28.
 */
@Component

public class CycleEventFactory implements ApplicationContextAware {

    private static Map<CycleTypeEnum, ICycleEvent> cycleBeanMap;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, ICycleEvent> map = applicationContext.getBeansOfType(ICycleEvent.class);
        cycleBeanMap = new HashMap<>(5);
        for (ICycleEvent value : map.values()) {
            cycleBeanMap.put(value.getCycleEventBusiness(), value);
        }
    }

    public static ICycleEvent getCycleEventBusiness(CycleTypeEnum type) {
        return cycleBeanMap.get(type);
    }

    public static ICycleEvent getImplBySourceModule(RbAcctStandardModel standardModel) {
        return cycleBeanMap.get(getEnum(standardModel));
    }

    private static CycleTypeEnum getEnum(RbAcctStandardModel standardModel) {
        if (BusiUtil.isEquals(SourceModuleEnum.GL.toString(), standardModel.getSourceModule())) {
            return CycleTypeEnum.GL;
        } else {
            return CycleTypeEnum.DEFAULT;
        }
    }
}
