package com.dcits.ensemble.rb.business.api.component.cd.corpcd;

import com.dcits.ensemble.rb.business.model.cm.SettleCardModel;

/**
 * The interface Settle card lost chk.
 */
public interface ISettleCardLostChk {
    /**
     * <PERSON>l lost check.
     *
     * @param settleCardModel the settle card model
     */
    void balLostCheck(SettleCardModel settleCardModel);

    /**
     * Farmal lost check.
     *
     * @param settleCardModel the settle card model
     */
    void farmalLostCheck(SettleCardModel settleCardModel);

    /**
     * Remove bal lost check.
     *
     * @param settleCardModel the settle card model
     */
    void removeBalLostCheck(SettleCardModel settleCardModel);

    /**
     * Remove farmal lost check.
     *
     * @param settleCardModel the settle card model
     */
    void removeFarmalLostCheck(SettleCardModel settleCardModel);
}
