package com.dcits.ensemble.rb.business.api.component.cm;

import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcct;

/**
 * The interface Get default acct for channel.
 */
public interface IGetDefaultAcctForChannel {

    /**
     * Gets internal key.
     *
     * @param baseAcctNo the base acct no
     * @param ccy        the acctCcy
     * @param channel    the channel
     * @return the internal key
     */
    RbAcct getInternalKey(String baseAcctNo, String ccy, String channel);
}
