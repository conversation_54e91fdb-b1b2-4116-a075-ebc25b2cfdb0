package com.dcits.ensemble.rb.business.api.unit.interest.lim;

import com.dcits.ensemble.base.data.EnsSysHead;
import com.dcits.ensemble.rb.business.model.interest.MbInterestModel;


/**
 * Created by zhangxij on 2018/3/28.
 */
public interface ILimProcess {


//    /**
//     * Lim是否返回成功
//     *
//     * @param out
//     */
//    void getLimRet(IBaseResponse out);

    /**
     * 是否提前支取
     *
     * @param runDate      交易日期
     * @param maturityDate 到期日
     * @return
     */
    boolean isPreCalc(String runDate, String maturityDate);

    /**
     * 获取事件利息计算事件类型
     * PRE    提前支取/回收
     * MRT    到期支取/回收 活期结息
     * PSD    逾期支取/回收
     * FAD    违约支取/回收
     *
     * @param runDate      交易日期
     * @param maturityDate 到期日
     * @return
     */
    String getIntEventType(String runDate, String maturityDate);

    /**
     * 获取利率市场化接口SysHead
     *
     * @param messageCode
     * @param messageType
     * @return
     */
    EnsSysHead getLimSysHead(String messageCode, String messageType,String branch);

    /**
     * 利息为0的数据模型
     */
    MbInterestModel.InterestArray getZeroMbInterestModel();

//    /**
//     * 利率市场化接口内部调用方法，包含报错处理
//     *
//     * @param businessProcess
//     * @param request
//     * @return
//     */
//    void innerProcess(BusinessProcess businessProcess, EnsRequest request);

}
