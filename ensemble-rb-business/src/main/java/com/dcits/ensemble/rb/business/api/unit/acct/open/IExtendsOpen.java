package com.dcits.ensemble.rb.business.api.unit.acct.open;

import com.dcits.ensemble.model.base.DbTablesModel;


/**
 * 开户扩展
 *
 * @param <T>
 */
public interface IExtendsOpen<T extends Object> {
    /**
     * 开户扩展接口
     *
     * @param model
     * @param tables
     */
    void extendsAcct(T model, DbTablesModel tables);

    /**
     * 获取处理类型
     *
     * @param
     * @return
     */
    String getType();
}
