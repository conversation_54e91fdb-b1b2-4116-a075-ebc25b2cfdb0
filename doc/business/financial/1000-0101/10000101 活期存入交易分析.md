# 10000101 活期存入交易分析

## 概述

本报告详细分析了10000101活期存入交易的完整代码实现，包括每一个组件的具体逻辑，调用链路的完整梳理，以及详细的代码执行流程。

## 1. 交易入口分析

### 1.1 ICore10000101接口定义

**文件位置**: `ensemble-rb-online-contract/src/main/java/com/dcits/ensemble/rb/api/bs/ICore10000101.java`

**关键信息**:

- 服务URL: `/rb/fin/current/dep`
- 服务编码: `MbsdCore-1000-0101`
- 支持存款类型: 活期存折账户、卡下活期账户、对公活期账户
- 支持功能: 代办人存入、一户通资金联动

### 1.2 Core10000101服务实现

**文件位置**: `ensemble-rb-service/src/main/java/com/dcits/ensemble/rb/service/bs/dd/Core10000101.java`

## 2. 核心业务流程分析

### 2.1 Core10000101Flow业务流程

**文件位置**: `ensemble-rb-service/src/main/java/com/dcits/ensemble/rb/service/bf/dd/Core10000101Flow.java`

#### 2.1.1 identifyAcctStd账户模型确认

**方法签名**: `protected RbAcctStdContext identifyAcctStd(Core10000101In core10000101In)`

**关键信息**:

- 获取主账户信息， 获取产品信息
- 客户号一致性校验
- 黑白名单检查
- 多币种账户和外币子账户处理
- MCA多币种账户处理
- 构建账户标准上下文

```java
// 1、获取最终的账户信息
// 2、应用标识校验
// 3、获取客户信息和产品信息
// 4、构建业务上下文
// 5、追缴账户特殊处理
// 6、设置交易类型
```

#### 2.1.2 execute核心执行方法

**方法签名**: `protected Core10000101Out execute(Core10000101In in, Class<Core10000101Out> outClass)`

## 3. Gravity组件流程分析

Gravity流程包含以下组件（按执行顺序）：

```mermaid
graph TB
    %% 定义样式类
    classDef startStyle fill:#e8f5e8,stroke:#4caf50,stroke-width:3px,color:#1b5e20,font-weight:bold
    classDef processStyle fill:#e3f2fd,stroke:#2196f3,stroke-width:2px,color:#0d47a1,font-weight:500
    classDef decisionStyle fill:#fff3e0,stroke:#ff9800,stroke-width:2px,color:#e65100,font-weight:500
    classDef errorStyle fill:#ffebee,stroke:#f44336,stroke-width:2px,color:#b71c1c,font-weight:500
    classDef successStyle fill:#e8f5e8,stroke:#4caf50,stroke-width:3px,color:#1b5e20,font-weight:bold
    

A[开始]:::startStyle
B[结束]:::successStyle
ClientCheck[客户校验流程组-客户基本校验BusiClientCheckGroup.checkClientBasis组件]:::processStyle
AcctCheck[账户校验流程组-账户基本校验BusiAcctCheckGroup.checkAcctBasis组件]:::processStyle
cond{开立检查}:::decisionStyle
checkRes[交易校验流程组-分户限制检查BusiTranCheckGroup.checkAllRestraints组件]
Core10000101Stria[活期存入Core10000101Stria.execute组件]
Core10000101Stria0[活期存入自动开外币子账户Core10000101Stria.execute0组件]

A --> ClientCheck
ClientCheck --> AcctCheck
AcctCheck --> cond
cond -->|OpenFlag = N| checkRes
cond -->|OpenFlag = Y| Core10000101Stria0
checkRes --> Core10000101Stria
Core10000101Stria --> B
Core10000101Stria0 --> B
```



### 3.1 客户校验流程组 - BusiClientCheckGroup.checkClientBasis

**文件位置**: `ensemble-rb-service/src/main/java/com/dcits/ensemble/rb/service/bg/cm/BusiClientCheckGroup.java`

**执行逻辑**:
1. 记录客户校验开始日志
2. 调用`rbClientCheckComponent.checkClientCommon`方法执行通用客户校验
3. 校验内容包括客户状态、客户类型等基本信息验证
4. 记录客户校验结束日志

### 3.2 账户校验流程组 - BusiAcctCheckGroup.checkAcctBasis

**文件位置**: `ensemble-rb-service/src/main/java/com/dcits/ensemble/rb/service/bg/cm/acct/BusiAcctCheckGroup.java`

**执行逻辑**:
1. 记录账户校验开始日志
2. 检查业务参数上下文中的`openFlag`，如果是开户操作("Y")则跳过校验
3. 如果不是开户操作，执行账户通用检查
4. 根据`isIndividual`标识分别执行个人账户检查或对公账户检查
5. 记录账户校验结束日志

### 3.3 交易校验流程组 - BusiTranCheckGroup.checkAllRestraints

**文件位置**: `ensemble-rb-service/src/main/java/com/dcits/ensemble/rb/service/bg/cm/BusiTranCheckGroup.java`

**执行逻辑**:
1. 记录分户限制检查开始日志
2. 设置生效日期，默认为当前营业日期
3. 获取或构建交易定义对象
4. 构建限制检查模型，包括账户信息、生效日期、交易定义
5. 通过工厂模式获取限制检查业务实例
6. 执行ALL类型的限制检查
7. 如果有错误则抛出业务异常
8. 执行渠道控制检查
9. 特殊账户状态检查（暂收账户等）
10. 记录检查结束日志

### 3.4 活期存入Core10000101Stria.execute组件

**文件位置**: `ensemble-rb-service/src/main/java/com/dcits/ensemble/rb/service/bg/dd/Core10000101Stria.java`

#### 3.4.1 主要执行方法execute

**详细代码分析**:
```java
@Commit
@GravityComponent(
   navigationMenu = "no-group",
   name = "活期存入10"
)
public Core10000101Out execute(Core10000101In in) {
    log.info("[Core10000101] start");
    Body body = in.getBody();
    
    // 第108行：获取交易定义
    RbTranDef rbTranDef = TransactionUtil.getMbTranDef(body.getTranType());
    if (BusiUtil.isNull(rbTranDef)) {
        throw BusiUtil.createBusinessException("RB8227", new String[]{body.getTranType()});
    } else {
        // 第112行：获取账户标准模型
        RbAcctStandardModel acctStandardModel = this.mbAcctInfoService.getPriAcctStd(
            body.getBaseAcctNo(), body.getProdType(), body.getAcctCcy(), body.getAcctSeqNo());
        
        // 第113-114行：检查账户类型，定期账户不能存款
        if (BusiUtil.isEquals(acctStandardModel.getAcctType(), "T")) {
            throw BusiUtil.createBusinessException("RB4034", new String[]{body.getBaseAcctNo()});
        }
        
        // 第115-117行：检查账户状态，暂收账户不能存款
        else if (BusiUtil.isEquals(AcctStatusEnum.SUSPENSE.getCode(), acctStandardModel.getAcctStatus())) {
            throw BusiUtil.createBusinessException("RB7003", 
                new String[]{acctStandardModel.getBaseAcctNo(), acctStandardModel.getProdType(), 
                            acctStandardModel.getAcctCcy(), acctStandardModel.getAcctSeqNo()});
        }
        
        // 第117-119行：检查交易金额
        else if (body.getTranAmt().compareTo(BigDecimal.ZERO) <= 0) {
            throw BusiUtil.createBusinessException("MB3333");
        } else {
            // 第120-123行：检查交易机构币种
            String tranBranch = Context.getInstance().getBranchId();
            if (!FmUtil.checkBranchCccy(tranBranch, body.getTranCcy())) {
                throw BusiUtil.createBusinessException("RB6695", 
                    new String[]{tranBranch, body.getTranCcy()});
            } else {
                log.info("Account attributes are..." + acctStandardModel.getAcctNature());
                
                // 第125-127行：反洗钱资金流向登记
                if (BusiUtil.isEquals("0017", acctStandardModel.getAcctNature())) {
                    this.WrtieFundFromTo(acctStandardModel, body);
                }

                // 第129-169行：现金来源和用途检查
                String cashSourceRemark = body.getCashSourceRemark();
                String cashUseRemark = body.getCashUseRemark();
                String remark = body.getRemark();
                
                if (!BusiUtil.isNotNull(cashSourceRemark) || 
                    !BusiUtil.isEquals("09", cashSourceRemark) || 
                    !BusiUtil.isEquals("", remark) && !BusiUtil.isNull(remark)) {
                    
                    if (BusiUtil.isNotNull(cashUseRemark) && 
                        BusiUtil.isEquals("08", cashUseRemark) && 
                        (BusiUtil.isEquals("", remark) || BusiUtil.isNull(remark))) {
                        throw BusiUtil.createBusinessException("RB8226");
                    } else {
                        // 第136-164行：核心交易处理逻辑
                        boolean isCorrect = BusiUtil.isNotNull(body.getReference());
                        TransactionControlModel controlModel = TransactionControlModel.builder()
                            .isCorrect(isCorrect).build();
                        
                        // 更正交易检查
                        this.correctCheck(body, acctStandardModel, controlModel);
                        
                        // GL账户不支持更正
                        if (BusiUtil.isEquals("GL", acctStandardModel.getSourceModule()) && isCorrect) {
                            throw BusiUtil.createBusinessException("RB3217");
                        } else {
                            // 构建账户交易输入模型
                            AcctTransactionInModel tranModel = new AcctTransactionInModel(
                                acctStandardModel, rbTranDef, body.getTranAmt());
                            
                            // 组装交易模型
                            this.assembleAcctTransactionModle(body, tranModel, acctStandardModel);
                            
                            String sourceType = Context.getInstance().getSourceType();
                            log.debug("[Core10000101] tranModel {}", tranModel.toString());
                            
                            // 构建异步交易输入
                            AsynNormalCurrentCretIn asynNormalCurrentCretIn = new AsynNormalCurrentCretIn(tranModel);
                            asynNormalCurrentCretIn.getAcctTransactionInModel().setProdType(acctStandardModel.getProdType());
                            
                            // 处理服务费明细
                            List<MbServDetailModel> mbServDetailModels = new ArrayList();
                            List<ServDetailArray> servDetailArray = body.getServDetailArray();
                            if (BusiUtil.isNotNull(servDetailArray)) {
                                Iterator var17 = servDetailArray.iterator();
                                while(var17.hasNext()) {
                                    ServDetailArray detailArray = (ServDetailArray)var17.next();
                                    ChargeUtil.getFeeType(detailArray.getFeeType());
                                }
                                BeanUtil.listCopy(servDetailArray, mbServDetailModels, MbServDetailModel.class);
                            }

                            // 调用TAE记账引擎
                            this.processTae(BusiUtil.getMessageByKey("MG0328"), 
                                          asynNormalCurrentCretIn, controlModel, mbServDetailModels);
                            
                            // 返回结果
                            Core10000101Out out = new Core10000101Out();
                            return out;
                        }
                    }
                } else {
                    throw BusiUtil.createBusinessException("RB8225");
                }
            }
        }
    }
}
```

#### 3.4.2 TAE处理方法processTae

**详细代码分析**:
```java
public SettleEngineOut processTae(String tranDesc, AsynNormalCurrentCretIn asynNormalCurrentCretIn, 
                                 TransactionControlModel controlModel, List<MbServDetailModel> mbServDetailModels) {
    AcctTransactionInModel acctTransactionInModel = asynNormalCurrentCretIn.getAcctTransactionInModel();
    Assert.notNull(acctTransactionInModel, "Create tae acctTransactionInModel must not be null");
    
    RbAcctStandardModel rbAcctStandardModel = acctTransactionInModel.getRbAcctStandardModel();
    
    // 计算当前TAE交易类型枚举
    RbCurrentTaeTradesEnum currentEnum = CurrentTaeService.calcCurrentEnum(null, acctTransactionInModel);
    
    RbAcctTransactionOutModel acctTransactionOutModel;
    
    // 判断账户币种和交易币种是否一致
    if (BusiUtil.isNotEquals(rbAcctStandardModel.getAcctCcy(), acctTransactionInModel.getTranCcy())) {
        // 跨币种交易处理
        acctTransactionInModel.setBaseAcctNo(null);
        acctTransactionInModel.setAcctCcy(null);
        acctTransactionInModel.setAcctSeqNo(null);
        acctTransactionInModel.setProdType(null);
        acctTransactionInModel.setOthBaseAcctNo(rbAcctStandardModel.getBaseAcctNo());
        acctTransactionInModel.setOthAcctCcy(rbAcctStandardModel.getAcctCcy());
        acctTransactionInModel.setOthAcctSeqNo(rbAcctStandardModel.getAcctSeqNo());
        acctTransactionInModel.setOthProdType(rbAcctStandardModel.getProdType());
        
        // 调用外汇通用组件处理跨币种交易
        acctTransactionOutModel = this.iExchangeCommon.assembleTransactionTaeRowByDiffCcy(acctTransactionInModel);
    } else {
        // 同币种交易处理
        acctTransactionOutModel = this.assembleTransactionTaeRow(
            tranDesc, asynNormalCurrentCretIn, controlModel, currentEnum, mbServDetailModels);
    }

    // 构建TAE引擎输入
    SettleEngineIn settleEngineIn = new SettleEngineIn(
        acctTransactionOutModel.getTaeAcctMaintradesRow(), 
        acctTransactionOutModel.getTaeAcctSubtradesRow());
    
    // 发送给TAE记账引擎处理
    SettleEngineOut settleEngineOut = currentEnum.sendTae(settleEngineIn);
    return settleEngineOut;
}
```

#### 3.4.3 交易模型组装方法assembleAcctTransactionModle

**详细代码分析**:
```java
private void assembleAcctTransactionModle(Body body, AcctTransactionInModel tranModel, 
                                        RbAcctStandardModel acctStandardModel) {
    // 设置账户信息
    body.setAcctCcy(tranModel.getAcctCcy());
    body.setProdType(tranModel.getProdType());
    body.setAcctSeqNo(tranModel.getAcctSeqNo());
    
    // 使用MapStruct转换器转换模型
    Core10000101Convert.INSTANCE.convertTranModel(body, tranModel);
    
    // 设置交易备注和付款单位
    tranModel.setTranNote(body.getTranNote());
    tranModel.setPayUnit(body.getPayUnit());
    
    // 设置默认摘要
    if (BusiUtil.isNull(tranModel.getNarrative())) {
        tranModel.setNarrative(BusiUtil.getMessageByKey("MG0328"));
    }

    // 设置介质标识和类型
    if (BusiUtil.isNotNull(body)) {
        tranModel.setMediumFlag(BusiUtil.isNotNull(body.getMediumFlag()) ? body.getMediumFlag() : "");
        tranModel.setMediumType(BusiUtil.isNotNull(body.getMediumType()) ? body.getMediumType() : "");
    }

    // 获取客户信息
    CifBaseInfo cifBaseInfo = CifRpc.getCifBaseInfo(acctStandardModel.getClientNo());
    tranModel.setCifBaseInfo(cifBaseInfo);
    
    // 获取产品信息
    RbProduct rbProduct = ProductRpc.getRbProduct(
        acctStandardModel.getProdType(), RbEventEnum.CRET.toString(), acctStandardModel.getCompany());
    tranModel.setRbProduct(rbProduct);
    
    // 设置外汇相关字段
    tranModel.setTranCcy(body.getTranCcy());
    tranModel.setBuyCcy(body.getBuyCcy());
    tranModel.setBuyAmount(body.getBuyAmount());
    tranModel.setBuyRate(body.getBuyRate());
    tranModel.setBuyUncRate(body.getBuyUncRate());
    tranModel.setSellCcy(body.getSellCcy());
    tranModel.setSellAmount(body.getSellAmount());
    tranModel.setSellRate(body.getSellRate());
    tranModel.setSellUncRate(body.getSellUncRate());
    tranModel.setQuoteType(body.getQuoteType());
    tranModel.setRateType(body.getRateType());
    tranModel.setCrossRate(body.getCrossRate());
    tranModel.setFloatRate(body.getFloatRate());
    tranModel.setInnerRate(body.getInnerRate());
}
```

### 3.5 自动开外币子账户组件executeO

**详细代码分析**:
```java
@Commit
@GravityComponent(
   navigationMenu = "no-group",
   name = "活期存入自动开外币子账户"
)
public Core10000101Out executeO(Core10000101In in, List<MbTransactionModel> mbTran) {
    log.info("[Core10000101] start");
    Body body = in.getBody();
    
    // 调用账户维护应用业务组件开立外币子账户
    this.iMbAcctMaintApplicationBg.openForeSubAcct(
        null, null, null, null, 
        body.getBaseAcctNo(), 
        body.getTranCcy(), 
        mbTran, 
        null, 
        BusiUtil.getMessageByKey("MG0328"));
    
    log.info("[Core10000101] end");
    Core10000101Out out = new Core10000101Out();
    return out;
}
```

**执行逻辑**:
1. 记录开始日志
2. 调用`iMbAcctMaintApplicationBg.openForeSubAcct`方法自动开立外币子账户
3. 传入基础账号、交易币种、交易明细等参数
4. 记录结束日志
5. 返回空的输出对象

## 4. TAE异步回调分析

### 4.1 IAsynFinancial异步接口

**文件位置**: `ensemble-rb-service/src/main/java/com/dcits/ensemble/rb/service/util/IAsynFinancial.java`

### 4.2 AsynFinancialImpl实现类

**文件位置**: `ensemble-rb-service/src/main/java/com/dcits/ensemble/rb/service/bs/tae/AsynFinancialImpl.java`

### 4.3 AsynFinancialFlow异步流程

**文件位置**: `ensemble-rb-service/src/main/java/com/dcits/ensemble/rb/service/bf/tae/AsynFinancialFlow.java`

**详细代码分析**:
```java
@Service
public class AsynFinancialFlow extends AbstractFlow<AsynFinancialIn, AsynFinancialOut> {
   private static final Logger log = LoggerFactory.getLogger(AsynFinancialFlow.class);
   
   @Resource private AccountCheckConfig accountCheckConfig;
   @Resource private RbTaeAccountCheckRepository rbTaeAccountCheckRepository;
   @Value("${com.dcits.gl-system}") private String glSystem;

   public AsynFinancialOut execute(AsynFinancialIn asynFinancialIn) {
       log.info("[Flow-AsynFinancialFlow] start");
       
       AsynFinancialIn.Body body = asynFinancialIn.getBody();
       String jsonData = body.getJsonData();
       String tranCode = body.getTranCode();
       
       // 设置子序号
       GenerateTaeMessage.putSubSeqNo(body.getSubSeqNo());
       
       log.debug("[Flow- AsynFinancialFlow] body ----->[{}]", body);
       log.debug("[Flow- AsynFinancialFlow] tranCode ----->[{}]-[{}]", 
                 tranCode, TranCodeDict.getDesc(tranCode));
       
       // 通过工厂模式获取TAE流程处理器
       ITaeFlow taeFlow = TaeFlowFactory.getTaeFlowByTranCode(tranCode);
       if (BusiUtil.isNull(taeFlow)) {
           throw new BusinessException("999999", "can't execute taeFlow ,ITaeFlow not found impl");
       } else {
           // 转换为请求对象
           EnsRequest ensRequest = taeFlow.convertToRequest(jsonData, taeFlow.getTargetModel());
           log.debug("Flow ----> [{}] paramater ----->[{}]", taeFlow.getClass(), ensRequest);
           
           // 处理TAE流程
           AsynTaeFlowOut taeFlowOut = taeFlow.process(ensRequest);
           
           // 参数校验
           PreconditionUtils.checkNotNull(taeFlowOut.getReference(), RbDictEnum.REFERENCE);
           PreconditionUtils.checkNotNull(taeFlowOut.getClientNo(), RbDictEnum.CLIENTNO);
           
           // 插入TAE信息（如果开启账户检查）
           if (Boolean.TRUE.equals(this.accountCheckConfig.getCheckEnable())) {
               this.insertrbTaeInfo(asynFinancialIn);
           }

           // 发送总账历史消息
           MqUtil.sendRbGlHistMsgBySubSeqNo(taeFlowOut.getReference(), 
                                          body.getSubSeqNo(), 
                                          taeFlowOut.getClientNo(), 
                                          this.glSystem);
           
           // 构建返回结果
           AsynFinancialOut financialOut = new AsynFinancialOut();
           financialOut.setBaseAcctNo(taeFlowOut.getBaseAcctNo());
           financialOut.setResSeqNo(taeFlowOut.getRestraintNo());
           financialOut.setAcctSeqNo(taeFlowOut.getAcctSeqNo());
           financialOut.setCcy(taeFlowOut.getCcy());
           financialOut.setProdType(taeFlowOut.getProdType());
           financialOut.setSeqNo(taeFlowOut.getSeqNo());
           financialOut.setInternalKey(taeFlowOut.getInternalKey());
           financialOut.setIban(taeFlowOut.getIban());
           return financialOut;
       }
   }
}
```

## 5. 完整调用链路分析

### 5.1 详细调用链路图

### 5.2 文字描述调用链路

#### 5.2.1 联机交易主流程

1. **服务入口层** (bs/)
   - 客户端发送HTTP请求到 `/rb/fin/current/dep`
   - `ICore10000101`接口接收请求参数`Core10000101In`
   - `Core10000101.runService`方法被调用
   - 通过`ExecutorFlow.startFlow("core10000101Flow", in, Core10000101Out.class)`启动业务流程

2. **业务流程层** (bf/)
   - `Core10000101Flow.identifyAcctStd`方法执行账户标准化识别：
     - 调用`mbAcctInfoService.getRbAcctInfo`获取主账户信息
     - 调用`ProductRpc.getRbProduct`获取产品信息
     - 执行客户号一致性校验
     - 通过`rcAllListRepository.getRcAllList`执行黑白名单检查
     - 处理多币种账户逻辑，包括外币子账户检查和MCA账户处理
     - 调用`CifRpc.getCifBaseInfo`获取客户信息
     - 构建`RbAcctStdContext`账户标准上下文并返回

   - `Core10000101Flow.execute`方法调用父类`executeGravity`
   - `AbstractRbBusiFlow.executeGravity`通过`FlowDiagramExecutor.executorGravityDiagram`执行Gravity组件流程图

3. **Gravity组件执行链路**：

   **3.1 客户校验组件**
   - `BusiClientCheckGroup.checkClientBasis`被调用
   - 内部调用`rbClientCheckComponent.checkClientCommon(acctStdModel.getClientNo())`
   - 执行客户状态、客户类型等基本信息验证

   **3.2 账户校验组件**
   - `BusiAcctCheckGroup.checkAcctBasis`被调用
   - 检查业务参数上下文中的`openFlag`标志
   - 如果非开户操作，调用`rbAcctCheckComponent.checkAcctCommon(acctStdModel)`
   - 根据`isIndividual`标识分别执行个人或对公账户检查

   **3.3 分支判断**
   - 如果`openFlag = "Y"`，跳转到自动开户组件
   - 如果`openFlag = "N"`，继续执行交易校验组件

   **3.4 交易校验组件**
   - `BusiTranCheckGroup.checkAllRestraints`被调用
   - 通过`TransactionUtil.getMbTranDef(tranType)`获取交易定义
   - 构建`CheckRestraintsModel`和`ResCheckControlModel`
   - 通过`RestraintsCheckFactory.getRestraintsBusiness(RestraintsTypeEnum.ALL)`获取限制检查实例
   - 执行分户限制检查、渠道控制检查、特殊账户状态检查

   **3.5 活期存入处理组件**
   - `Core10000101Stria.execute`被调用，执行以下步骤：
     - 通过`TransactionUtil.getMbTranDef(body.getTranType())`获取交易定义
     - 通过`mbAcctInfoService.getPriAcctStd`获取账户标准模型
     - 执行账户类型检查（定期账户不允许）、账户状态检查、交易金额检查
     - 检查交易机构币种通过`FmUtil.checkBranchCccy`
     - 反洗钱资金流向登记（账户性质0017）
     - 现金来源用途检查
     - 更正交易检查通过`correctCheck`方法
     - 构建`AcctTransactionInModel`交易模型
     - 通过`assembleAcctTransactionModle`组装交易模型
     - 处理服务费明细
     - 调用`processTae`方法发送给TAE记账引擎

   **3.6 TAE处理流程**
   - `processTae`方法内部：
     - 通过`CurrentTaeService.calcCurrentEnum`计算TAE交易类型枚举
     - 判断是否跨币种交易
     - 如果跨币种，调用`iExchangeCommon.assembleTransactionTaeRowByDiffCcy`
     - 如果同币种，调用`assembleTransactionTaeRow`构建TAE交易行
     - 构建`SettleEngineIn`并通过`currentEnum.sendTae`发送给TAE引擎

4. **TAE异步回调流程**：
   - TAE记账引擎异步回调 `/rb/fin/asyn/financial`
   - `AsynFinancialImpl.runService`接收回调
   - 通过`ExecutorFlow.startFlow("asynFinancialFlow")`启动异步流程
   - `AsynFinancialFlow.execute`被调用：
     - 解析回调参数中的`tranCode`（值为`NORMAL_CURRENT_CRET`）
     - 通过`TaeFlowFactory.getTaeFlowByTranCode(tranCode)`获取对应的TAE流程处理器
     - 调用`taeFlow.convertToRequest`转换请求对象
     - 调用`taeFlow.process`处理TAE流程业务逻辑
     - 如果开启账户检查，通过`insertrbTaeInfo`插入TAE信息
     - 通过`MqUtil.sendRbGlHistMsgBySubSeqNo`发送总账历史消息
     - 构建并返回`AsynFinancialOut`结果

5. **自动开外币子账户流程**（仅当openFlag="Y"时）：
   - `Core10000101Stria.executeO`被调用
   - 通过`iMbAcctMaintApplicationBg.openForeSubAcct`自动开立外币子账户
   - 传入基础账号、交易币种、交易明细等参数

