package com.dcits.ensemble.rb.service.bs.channel.accountings;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.accounting.ICore13006037;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.accounting.Core13006037In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.accounting.Core13006037Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core13006037 implements ICore13006037 {
   private static final Logger log = LoggerFactory.getLogger(Core13006037.class);

   @CometMapping(
      value = "/rb/rev/pos/consume/cancel",
      serviceCode = "MbsdCore",
      messageType = "1300",
      messageCode = "6037",
      name = "银联POS消费撤销冲正"
   )
   public Core13006037Out runService(@RequestBody Core13006037In in) {
      return (Core13006037Out)ExecutorFlow.startFlow("core13006037Flow", in, Core13006037Out.class);
   }
}
