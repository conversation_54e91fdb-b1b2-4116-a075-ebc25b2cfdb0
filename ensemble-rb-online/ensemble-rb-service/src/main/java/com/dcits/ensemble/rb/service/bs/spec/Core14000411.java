package com.dcits.ensemble.rb.service.bs.spec;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000411;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000411In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000411Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000411 implements ICore14000411 {
   private static final Logger log = LoggerFactory.getLogger(Core14000411.class);

   @CometMapping(
      value = "/rb/inq/zxqy/earnings",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0411",
      name = "坐享其盈收益查询"
   )
   public Core14000411Out runService(@RequestBody Core14000411In in) {
      return (Core14000411Out)ExecutorFlow.startFlow("core14000411Flow", in, Core14000411Out.class);
   }
}
