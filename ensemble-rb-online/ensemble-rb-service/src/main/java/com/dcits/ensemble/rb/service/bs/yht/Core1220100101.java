package com.dcits.ensemble.rb.service.bs.yht;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1220100101;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1220100101In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1220100101Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1220100101 implements ICore1220100101 {
   private static final Logger log = LoggerFactory.getLogger(Core1220100101.class);

   @CometMapping(
      value = "/rb/file/current/sub/batch/open",
      serviceCode = "MbsdCore",
      messageType = "1220",
      messageCode = "100101",
      name = "母虚子实子账户批量续开"
   )
   public Core1220100101Out runService(@RequestBody Core1220100101In in) {
      return (Core1220100101Out)ExecutorFlow.startGravity(in);
   }
}
