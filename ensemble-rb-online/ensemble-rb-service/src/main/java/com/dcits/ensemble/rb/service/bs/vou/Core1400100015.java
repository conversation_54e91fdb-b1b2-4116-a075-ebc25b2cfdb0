package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1400100015;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100015In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100015Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400100015 implements ICore1400100015 {
   private static final Logger log = LoggerFactory.getLogger(Core1400100015.class);

   @CometMapping(
      value = "/rb/inq/card/voucher/query",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "100015",
      name = "卡产品/凭证互查"
   )
   public Core1400100015Out runService(@RequestBody Core1400100015In in) {
      return (Core1400100015Out)ExecutorFlow.startFlow("core1400100015Flow", in);
   }
}
