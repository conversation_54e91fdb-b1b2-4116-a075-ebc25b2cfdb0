package com.dcits.ensemble.rb.service.bs.agr.subagr.subcorpagr;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1200100126;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1200100126In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1200100126Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1200100126 implements ICore1200100126 {
   private static final Logger log = LoggerFactory.getLogger(Core1200100126.class);

   @CometMapping(
      value = "/rb/nfin/agreement/corperate",
      name = "对公理财签约",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "100126"
   )
   public Core1200100126Out runService(@RequestBody Core1200100126In in) {
      return (Core1200100126Out)ExecutorFlow.startGravity(in);
   }
}
