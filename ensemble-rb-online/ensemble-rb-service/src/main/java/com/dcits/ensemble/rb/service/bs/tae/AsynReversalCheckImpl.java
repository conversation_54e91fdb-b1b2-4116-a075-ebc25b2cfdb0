package com.dcits.ensemble.rb.service.bs.tae;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.service.util.IAsynReversalCheck;
import com.dcits.ensemble.rb.service.util.tae.RbReversalIn;
import com.dcits.ensemble.rb.service.util.tae.RbReversalOut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class AsynReversalCheckImpl implements IAsynReversalCheck {
   private static final Logger log = LoggerFactory.getLogger(AsynReversalCheckImpl.class);

   @CometMapping(
      value = "/rb/rev/tran/check",
      serviceCode = "MbsdCore",
      messageType = "1500",
      messageCode = "0105",
      name = "存款异步记账统一冲正检查"
   )
   public RbReversalOut runService(@RequestBody RbReversalIn in) {
      return (RbReversalOut)ExecutorFlow.startFlow("asynReversalCheckFlow", in, RbReversalOut.class);
   }
}
