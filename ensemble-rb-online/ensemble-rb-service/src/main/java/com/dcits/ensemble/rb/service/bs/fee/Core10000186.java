package com.dcits.ensemble.rb.service.bs.fee;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10000186In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10000186Out;
import com.dcits.ensemble.rb.api.model.mbsdcore.ICore10000186;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core10000186 implements ICore10000186 {
   private static final Logger log = LoggerFactory.getLogger(Core10000186.class);

   @CometMapping(
      value = "/rb/fin/fee/pay",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "0186",
      name = "费用支出"
   )
   public Core10000186Out runService(@RequestBody Core10000186In in) {
      return (Core10000186Out)ExecutorFlow.startGravity(in);
   }
}
