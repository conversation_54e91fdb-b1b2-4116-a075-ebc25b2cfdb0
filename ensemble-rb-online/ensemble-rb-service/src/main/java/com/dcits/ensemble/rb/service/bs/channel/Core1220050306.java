package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore1220050306;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1220050306In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1220050306Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core1220050306 implements ICore1220050306 {
   private static final Logger log = LoggerFactory.getLogger(Core1220050306.class);

   @CometMapping(
      value = "/rb/file/current/frozen/operate",
      serviceCode = "MbsdCore",
      messageType = "1220",
      messageCode = "050306",
      name = "批量账户冻结"
   )
   public Core1220050306Out runService(@RequestBody Core1220050306In in) {
      return (Core1220050306Out)ExecutorFlow.startFlow("core1220050306Flow", in, Core1220050306Out.class);
   }
}
