package com.dcits.ensemble.rb.service.bs.spec;

import com.dcits.ensemble.rb.business.bc.unit.acct.transaction.repository.RbAcctBalanceRepository;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcctBalance;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreement;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAgreementOverdraft;
import com.dcits.ensemble.rb.business.model.agr.LoanOdInfoModel;
import com.dcits.ensemble.rb.business.repository.agr.RbAgreementOverdraftRepository;
import com.dcits.ensemble.rb.business.repository.agr.RbAgreementRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.math.BigDecimal;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class AsynUpdateBranchInfo {
   private static final Logger log = LoggerFactory.getLogger(AsynUpdateBranchInfo.class);
   @Resource
   private RbAgreementOverdraftRepository rbAgreementOverdraftRepository;
   @Resource
   private RbAcctBalanceRepository rbAcctBalanceRepository;
   @Resource
   private RbAgreementRepository rbAgreementRepository;

   public RbAcctBalance updatebranchinfo(LoanOdInfoModel loanOdInfoModel) {
      log.debug("loanOdInfoModel-------->{}", loanOdInfoModel);
      String loanNo = loanOdInfoModel.getLoanNo();
      String reversalFlag = loanOdInfoModel.getReversalFlag();
      BigDecimal tranAmt = loanOdInfoModel.getTranAmt();
      if (!BusiUtil.isNull(loanNo) && !BusiUtil.isNull(reversalFlag) && !BusiUtil.isNull(tranAmt)) {
         RbAgreementOverdraft rbAgreementOverdraft = new RbAgreementOverdraft();
         rbAgreementOverdraft.setLoanBaseAcctNo(loanNo);
         RbAgreementOverdraft rbAgreementOverdraft1 = (RbAgreementOverdraft)this.rbAgreementOverdraftRepository.selectOne(rbAgreementOverdraft);
         if (BusiUtil.isNull(rbAgreementOverdraft1)) {
            throw BusiUtil.createBusinessException("RB4070");
         } else {
            RbAgreement rbAgreement = this.rbAgreementRepository.getAgreementByAgreementId(rbAgreementOverdraft1.getAgreementId(), rbAgreementOverdraft1.getClientNo());
            RbAcctBalance rbAcctBalance = new RbAcctBalance();
            rbAcctBalance.setClientNo(rbAgreementOverdraft1.getClientNo());
            rbAcctBalance.setInternalKey(Long.valueOf(rbAgreement.getAgreementKey()));
            RbAcctBalance rbAcctBalanceNew = (RbAcctBalance)this.rbAcctBalanceRepository.selectOne(rbAcctBalance);
            if (BusiUtil.isEquals("N", reversalFlag)) {
               rbAcctBalanceNew.setOdAmount(rbAcctBalanceNew.getOdAmount().add(tranAmt));
            }

            if (BusiUtil.isEquals("Y", reversalFlag)) {
               rbAcctBalanceNew.setOdAmount(rbAcctBalanceNew.getOdAmount().subtract(tranAmt));
            }

            this.rbAcctBalanceRepository.update(rbAcctBalanceNew);
            return rbAcctBalanceNew;
         }
      } else {
         throw BusiUtil.createBusinessException("RB9854");
      }
   }
}
