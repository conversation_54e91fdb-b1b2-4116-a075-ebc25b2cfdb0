package com.dcits.ensemble.rb.service.bs.channel.pos;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.pos.ICore1000056031;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core1000056031In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core1000056031Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core1000056031 implements ICore1000056031 {
   private static final Logger log = LoggerFactory.getLogger(Core1000056031.class);

   @CometMapping(
      value = "/rb/fin/channel/authdone/cancel",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "056031",
      name = "pos预授权完成撤销"
   )
   public Core1000056031Out runService(@RequestBody Core1000056031In in) {
      return (Core1000056031Out)ExecutorFlow.startGravity(in, Core1000056031Out.class);
   }
}
