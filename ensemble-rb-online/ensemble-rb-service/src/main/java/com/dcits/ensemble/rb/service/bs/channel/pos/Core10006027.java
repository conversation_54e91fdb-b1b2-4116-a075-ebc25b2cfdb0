package com.dcits.ensemble.rb.service.bs.channel.pos;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.pos.ICore10006027;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core10006027In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core10006027Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core10006027 implements ICore10006027 {
   private static final Logger log = LoggerFactory.getLogger(Core10006027.class);

   @CometMapping(
      value = "/rb/fin/agent/out",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "6027",
      name = "银联他代本转账转出"
   )
   public Core10006027Out runService(@RequestBody Core10006027In in) {
      return (Core10006027Out)ExecutorFlow.startGravity(in, Core10006027Out.class);
   }
}
