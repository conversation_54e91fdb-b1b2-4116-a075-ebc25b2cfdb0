package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore1200050159;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1200050159In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1200050159Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1200050159 implements ICore1200050159 {
   private static final Logger log = LoggerFactory.getLogger(Core1200050159.class);

   @CometMapping(
      value = "/rb/nfin/channel/judicature/assistance",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "050159",
      name = "有权机关协助信息录入1"
   )
   public Core1200050159Out runService(@RequestBody Core1200050159In in) {
      return (Core1200050159Out)ExecutorFlow.startGravity(in, Core1200050159Out.class);
   }
}
