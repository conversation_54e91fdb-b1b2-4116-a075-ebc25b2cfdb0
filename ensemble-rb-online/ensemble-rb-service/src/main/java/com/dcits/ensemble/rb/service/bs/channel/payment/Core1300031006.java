package com.dcits.ensemble.rb.service.bs.channel.payment;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.payment.ICore1300031006;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.payment.Core1300031006In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.payment.Core1300031006Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core1300031006 implements ICore1300031006 {
   private static final Logger log = LoggerFactory.getLogger(Core1300031006.class);

   @CometMapping(
      value = "/rb/rev/pay/dedicated",
      serviceCode = "MbsdCore",
      messageType = "1300",
      messageCode = "031006",
      name = "支付系统记账冲正"
   )
   public Core1300031006Out runService(@RequestBody Core1300031006In in) {
      return (Core1300031006Out)ExecutorFlow.startGravity(in, Core1300031006Out.class);
   }
}
