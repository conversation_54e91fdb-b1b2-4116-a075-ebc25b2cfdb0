package com.dcits.ensemble.rb.service.bs.agr.mainagr;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1200109181;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1200109181In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1200109181Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1200109181 implements ICore1200109181 {
   private static final Logger log = LoggerFactory.getLogger(Core1200109181.class);

   @CometMapping(
      value = "/rb/nfin/agreement/signcheck",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "109181",
      name = "协议互斥检查"
   )
   public Core1200109181Out runService(@RequestBody Core1200109181In in) {
      return (Core1200109181Out)ExecutorFlow.startFlow("core1200109181Flow", in);
   }
}
