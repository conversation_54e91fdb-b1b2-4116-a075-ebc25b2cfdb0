package com.dcits.ensemble.rb.service.bs.aio;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12000401;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000401In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000401Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000401 implements ICore12000401 {
   private static final Logger log = LoggerFactory.getLogger(Core12000401.class);

   @CometMapping(
      value = "/rb/nfin/aio/open",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0401",
      name = "AIO帐户开户"
   )
   public Core12000401Out runService(@RequestBody Core12000401In in) {
      return (Core12000401Out)ExecutorFlow.startFlow("core12000401Flow", in, Core12000401Out.class);
   }
}
