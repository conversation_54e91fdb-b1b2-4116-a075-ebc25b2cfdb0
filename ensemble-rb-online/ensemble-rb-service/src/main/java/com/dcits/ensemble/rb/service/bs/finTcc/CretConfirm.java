package com.dcits.ensemble.rb.service.bs.finTcc;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.service.util.tae.AsynFinancialIn;
import com.dcits.ensemble.rb.service.util.tae.AsynFinancialOut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@CometProvider
public class CretConfirm implements ICretConfirm {
   private static final Logger log = LoggerFactory.getLogger(CretConfirm.class);

   @CometMapping(
      value = "/rb/fin/cret/confirm",
      serviceCode = "MbsdCore",
      messageType = "1500",
      messageCode = "0113"
   )
   public AsynFinancialOut runService(AsynFinancialIn in) {
      return (AsynFinancialOut)ExecutorFlow.startFlow("cretConfirmTccFlow", in, AsynFinancialOut.class);
   }
}
