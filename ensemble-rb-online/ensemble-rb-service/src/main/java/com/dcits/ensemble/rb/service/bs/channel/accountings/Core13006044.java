package com.dcits.ensemble.rb.service.bs.channel.accountings;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.accounting.ICore13006044;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.accounting.Core13006044In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.accounting.Core13006044Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core13006044 implements ICore13006044 {
   private static final Logger log = LoggerFactory.getLogger(Core13006044.class);

   @CometMapping(
      value = "/rb/rev/collection/cancel",
      serviceCode = "MbsdCore",
      messageType = "1300",
      messageCode = "6044",
      name = "代收撤销冲正"
   )
   public Core13006044Out runService(@RequestBody Core13006044In in) {
      return (Core13006044Out)ExecutorFlow.startFlow("core13006044Flow", in, Core13006044Out.class);
   }
}
