package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore1200058004;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1200058004In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1200058004Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core1200058004 implements ICore1200058004 {
   private static final Logger log = LoggerFactory.getLogger(Core1200058004.class);

   @CometMapping(
      value = "/rb/nfin/channel/xdb/print/update",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "058004",
      name = "单位协定宝打印次数更新"
   )
   public Core1200058004Out runService(@RequestBody Core1200058004In in) {
      return (Core1200058004Out)ExecutorFlow.startGravity(in, Core1200058004Out.class);
   }
}
