package com.dcits.ensemble.rb.service.bs.spec;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1400100125;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100125In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100125Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400100125 implements ICore1400100125 {
   private static final Logger log = LoggerFactory.getLogger(Core1400100125.class);

   @CometMapping(
      value = "/rb/inq/agreement/signquery",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "100125",
      name = "转存类理财协议信息查询"
   )
   public Core1400100125Out runService(@RequestBody Core1400100125In in) {
      return (Core1400100125Out)ExecutorFlow.startFlow("core1400100125Flow", in);
   }
}
