package com.dcits.ensemble.rb.service.bs.agr.subagr.subcorpagr;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.accounting.ICore12000162;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000162In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000162Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000162 implements ICore12000162 {
   private static final Logger log = LoggerFactory.getLogger(Core12000162.class);

   @CometMapping(
      value = "/rb/nfin/agreement/xdb",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0162",
      name = "单位综合签约"
   )
   public Core12000162Out runService(@RequestBody Core12000162In in) {
      return (Core12000162Out)ExecutorFlow.startFlow("core12000162Flow", in, Core12000162Out.class);
   }
}
