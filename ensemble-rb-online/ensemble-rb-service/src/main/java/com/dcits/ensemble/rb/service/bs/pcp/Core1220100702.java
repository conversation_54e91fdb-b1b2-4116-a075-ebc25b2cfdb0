package com.dcits.ensemble.rb.service.bs.pcp;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1220100702;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1220100702In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1220100702Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1220100702 implements ICore1220100702 {
   private static final Logger log = LoggerFactory.getLogger(Core1220100702.class);

   @CometMapping(
      value = "/rb/fin/pcp/file/allocate",
      name = "资金池手工批量下拨",
      serviceCode = "MbsdCore",
      messageType = "1220",
      messageCode = "100702"
   )
   public Core1220100702Out runService(@RequestBody Core1220100702In in) {
      return (Core1220100702Out)ExecutorFlow.startGravity(in);
   }
}
