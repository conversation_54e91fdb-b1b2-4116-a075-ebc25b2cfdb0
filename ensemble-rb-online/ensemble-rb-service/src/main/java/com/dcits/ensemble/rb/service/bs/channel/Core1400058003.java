package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore1400058003;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1400058003In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1400058003Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core1400058003 implements ICore1400058003 {
   private static final Logger log = LoggerFactory.getLogger(Core1400058003.class);

   @CometMapping(
      value = "/rb/inq/withdraw/hist",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "058003",
      name = "支付方式修改历史查询"
   )
   public Core1400058003Out runService(@RequestBody Core1400058003In in) {
      return (Core1400058003Out)ExecutorFlow.startGravity(in, Core1400058003Out.class);
   }
}
