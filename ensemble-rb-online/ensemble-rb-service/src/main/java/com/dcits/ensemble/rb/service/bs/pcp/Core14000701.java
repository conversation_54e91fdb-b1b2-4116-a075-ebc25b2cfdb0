package com.dcits.ensemble.rb.service.bs.pcp;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.pcp.ICore14000701;
import com.dcits.ensemble.rb.api.model.mbsdcore.pcp.Core14000701In;
import com.dcits.ensemble.rb.api.model.mbsdcore.pcp.Core14000701Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000701 implements ICore14000701 {
   private static final Logger log = LoggerFactory.getLogger(Core14000701.class);

   @CometMapping(
      value = "/rb/inq/pcp/limit/used",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0701",
      name = "资金池限额使用情况查询"
   )
   public Core14000701Out runService(@RequestBody Core14000701In in) {
      return (Core14000701Out)ExecutorFlow.startFlow("core14000701Flow", in, Core14000701Out.class);
   }
}
