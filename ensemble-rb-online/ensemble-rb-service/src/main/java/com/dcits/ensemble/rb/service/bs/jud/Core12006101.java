package com.dcits.ensemble.rb.service.bs.jud;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12006101;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12006101In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12006101Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core12006101 implements ICore12006101 {
   private static final Logger log = LoggerFactory.getLogger(Core12006101.class);

   @CometMapping(
      value = "/rb/nfin/impound/hang/operate",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "6101",
      name = "强制扣划挂账处理"
   )
   public Core12006101Out runService(@RequestBody Core12006101In in) {
      return (Core12006101Out)ExecutorFlow.startGravity(in, Core12006101Out.class);
   }
}
