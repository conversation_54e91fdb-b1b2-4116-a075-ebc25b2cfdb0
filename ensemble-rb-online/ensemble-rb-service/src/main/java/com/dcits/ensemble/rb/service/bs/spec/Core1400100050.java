package com.dcits.ensemble.rb.service.bs.spec;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1400100050;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100050In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100050Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400100050 implements ICore1400100050 {
   private static final Logger log = LoggerFactory.getLogger(Core1400100050.class);

   @CometMapping(
      value = "/rb/inq/agreement/txy",
      name = "同兴赢协议查询",
      messageType = "1400",
      messageCode = "100050",
      serviceCode = "MbsdCore"
   )
   public Core1400100050Out runService(@RequestBody Core1400100050In in) {
      return (Core1400100050Out)ExecutorFlow.startFlow("core1400100050Flow", in);
   }
}
