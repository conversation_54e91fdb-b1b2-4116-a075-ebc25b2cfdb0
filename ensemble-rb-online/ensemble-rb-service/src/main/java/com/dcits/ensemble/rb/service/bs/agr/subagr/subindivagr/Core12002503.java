package com.dcits.ensemble.rb.service.bs.agr.subagr.subindivagr;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12002503;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12002503In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12002503Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core12002503 implements ICore12002503 {
   private static final Logger log = LoggerFactory.getLogger(Core12002503.class);

   @CometMapping(
      value = "/rb/nfin/agreement/supplement/sign",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "2503",
      name = "金额补足签约"
   )
   public Core12002503Out runService(@RequestBody Core12002503In in) {
      return (Core12002503Out)ExecutorFlow.startGravity(in, Core12002503Out.class);
   }
}
