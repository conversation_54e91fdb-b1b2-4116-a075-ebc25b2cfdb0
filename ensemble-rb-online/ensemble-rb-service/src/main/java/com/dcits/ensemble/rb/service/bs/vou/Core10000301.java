package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore10000301;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10000301In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10000301Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core10000301 implements ICore10000301 {
   private static final Logger log = LoggerFactory.getLogger(Core10000301.class);

   @CometMapping(
      value = "/rb/fin/voucher/sell",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "0301",
      name = "凭证出售"
   )
   public Core10000301Out runService(@RequestBody Core10000301In in) {
      return (Core10000301Out)ExecutorFlow.startFlow("core10000301Flow", in, Core10000301Out.class);
   }
}
