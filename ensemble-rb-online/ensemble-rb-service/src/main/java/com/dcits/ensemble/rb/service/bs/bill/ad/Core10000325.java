package com.dcits.ensemble.rb.service.bs.bill.ad;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore10000325;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10000325In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10000325Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core10000325 implements ICore10000325 {
   private static final Logger log = LoggerFactory.getLogger(Core10000325.class);

   @CometMapping(
      value = "/rb/fin/bab/compensate",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "0325",
      name = "保函保证金备款"
   )
   public Core10000325Out runService(@RequestBody Core10000325In in) {
      return (Core10000325Out)ExecutorFlow.startFlow("core10000325Flow", in, Core10000325Out.class);
   }
}
