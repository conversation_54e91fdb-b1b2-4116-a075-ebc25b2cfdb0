package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12000126;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000126In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000126Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000126 implements ICore12000126 {
   private static final Logger log = LoggerFactory.getLogger(Core12000126.class);

   @CometMapping(
      value = "/rb/nfin/voucher/deposit/operate",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0126",
      name = "存款证明操作"
   )
   public Core12000126Out runService(@RequestBody Core12000126In in) {
      return (Core12000126Out)ExecutorFlow.startGravity(in, Core12000126Out.class);
   }
}
