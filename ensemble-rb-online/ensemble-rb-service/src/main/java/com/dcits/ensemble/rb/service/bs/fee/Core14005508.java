package com.dcits.ensemble.rb.service.bs.fee;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore14005508;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14005508In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14005508Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14005508 implements ICore14005508 {
   private static final Logger log = LoggerFactory.getLogger(Core14005508.class);

   @CometMapping(
      value = "/rb/inq/find/eco",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "5508",
      name = "免费账户收费查询"
   )
   public Core14005508Out runService(@RequestBody Core14005508In in) {
      return (Core14005508Out)ExecutorFlow.startFlow("core14005508Flow", in);
   }
}
