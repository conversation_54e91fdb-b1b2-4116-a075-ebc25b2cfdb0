package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore1400050154;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1400050154In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1400050154Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400050154 implements ICore1400050154 {
   private static final Logger log = LoggerFactory.getLogger(Core1400050154.class);

   @CometMapping(
      value = "/rb/inq/card/type",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "050154",
      name = "查询卡类型"
   )
   public Core1400050154Out runService(@RequestBody Core1400050154In in) {
      return (Core1400050154Out)ExecutorFlow.startFlow("core1400050154Flow", in, Core1400050154Out.class);
   }
}
