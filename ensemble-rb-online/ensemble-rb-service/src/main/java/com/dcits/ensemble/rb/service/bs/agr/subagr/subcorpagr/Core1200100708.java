package com.dcits.ensemble.rb.service.bs.agr.subagr.subcorpagr;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1200100708;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1200100708In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1200100708Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1200100708 implements ICore1200100708 {
   private static final Logger log = LoggerFactory.getLogger(Core1200100708.class);

   @CometMapping(
      value = "/rb/nfin/pcp/cross/agreement/deal",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "100708",
      name = "跨境资金池综合维护"
   )
   public Core1200100708Out runService(@RequestBody Core1200100708In in) {
      return (Core1200100708Out)ExecutorFlow.startFlow("core1200100708Flow", in);
   }
}
