package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000026;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000026In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000026Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000026 implements ICore14000026 {
   private static final Logger log = LoggerFactory.getLogger(Core14000026.class);

   @CometMapping(
      value = "/rb/inq/pbk/noprint",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0026",
      name = "查询存折未补登明细"
   )
   public Core14000026Out runService(@RequestBody Core14000026In in) {
      return (Core14000026Out)ExecutorFlow.startFlow("core14000026Flow", in, Core14000026Out.class);
   }
}
