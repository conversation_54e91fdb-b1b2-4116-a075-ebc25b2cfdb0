package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1220100010;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1220100010In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1220100010Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1220100010 implements ICore1220100010 {
   private static final Logger log = LoggerFactory.getLogger(Core1220100010.class);

   @CometMapping(
      value = "/rb/file/channel/tranhist/export",
      serviceCode = "MbsdCore",
      messageType = "1220",
      messageCode = "100010",
      name = "生成回单文件"
   )
   public Core1220100010Out runService(@RequestBody Core1220100010In in) {
      return (Core1220100010Out)ExecutorFlow.startFlow("core1220100010Flow", in);
   }
}
