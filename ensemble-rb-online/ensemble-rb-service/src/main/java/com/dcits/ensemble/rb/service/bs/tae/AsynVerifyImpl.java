package com.dcits.ensemble.rb.service.bs.tae;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.service.util.IAsynVerify;
import com.dcits.ensemble.rb.service.util.tae.RbVerifyIn;
import com.dcits.ensemble.rb.service.util.tae.RbVerifyOut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
@Service
public class AsynVerifyImpl implements IAsynVerify {
   private static final Logger log = LoggerFactory.getLogger(AsynVerifyImpl.class);

   @CometMapping(
      value = "/rb/nfin/tae/tran/verify",
      serviceCode = "MbsdCore",
      messageType = "1500",
      messageCode = "0103",
      name = "存款异步记账统一冲正"
   )
   public RbVerifyOut runService(@RequestBody RbVerifyIn in) {
      return (RbVerifyOut)ExecutorFlow.startFlow("asynVerifyFlow", in, RbVerifyOut.class);
   }
}
