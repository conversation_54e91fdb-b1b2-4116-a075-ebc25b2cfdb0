package com.dcits.ensemble.rb.service.bs.channel.pos;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.pos.ICore12006056;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core12006056In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core12006056Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core12006056 implements ICore12006056 {
   private static final Logger log = LoggerFactory.getLogger(Core12006056.class);

   @CometMapping(
      value = "/rb/nfin/channel/pos/order",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "6056",
      name = "银联POS预授权"
   )
   public Core12006056Out runService(@RequestBody Core12006056In in) {
      return (Core12006056Out)ExecutorFlow.startGravity(in, Core12006056Out.class);
   }
}
