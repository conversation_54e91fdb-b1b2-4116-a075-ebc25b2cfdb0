package com.dcits.ensemble.rb.service.bs.td;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14008784;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14008784In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14008784Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14008784 implements ICore14008784 {
   private static final Logger log = LoggerFactory.getLogger(Core14008784.class);

   @CometMapping(
      value = "/rb/inq/acct/autosettle",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "8784",
      name = "到期自动结清登记簿查询"
   )
   public Core14008784Out runService(@RequestBody Core14008784In in) {
      return (Core14008784Out)ExecutorFlow.startFlow("core14008784Flow", in, Core14008784Out.class);
   }
}
