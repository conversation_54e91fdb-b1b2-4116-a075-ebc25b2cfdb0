package com.dcits.ensemble.rb.service.bs.pcp;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1400100705;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100705In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100705Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400100705 implements ICore1400100705 {
   private static final Logger log = LoggerFactory.getLogger(Core1400100705.class);

   @CometMapping(
      value = "/rb/inq/pcp/member/rate",
      name = "资金池成员账户利率查询",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "100705"
   )
   public Core1400100705Out runService(@RequestBody Core1400100705In in) {
      return (Core1400100705Out)ExecutorFlow.startFlow("core1400100705Flow", in);
   }
}
