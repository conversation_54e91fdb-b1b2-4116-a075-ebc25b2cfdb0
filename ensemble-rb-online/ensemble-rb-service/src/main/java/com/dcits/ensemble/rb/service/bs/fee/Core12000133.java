package com.dcits.ensemble.rb.service.bs.fee;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12000133;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000133In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000133Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000133 implements ICore12000133 {
   private static final Logger log = LoggerFactory.getLogger(Core12000133.class);

   @CometMapping(
      value = "/rb/nfin/fee/amortize/operate",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0133",
      name = "费用摊销合约操作"
   )
   public Core12000133Out runService(@RequestBody Core12000133In in) {
      return (Core12000133Out)ExecutorFlow.startGravity(in, Core12000133Out.class);
   }
}
