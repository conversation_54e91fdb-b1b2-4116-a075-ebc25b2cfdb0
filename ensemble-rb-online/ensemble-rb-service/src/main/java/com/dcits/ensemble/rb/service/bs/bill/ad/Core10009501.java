package com.dcits.ensemble.rb.service.bs.bill.ad;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore10009501;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10009501In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10009501Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core10009501 implements ICore10009501 {
   private static final Logger log = LoggerFactory.getLogger(Core10009501.class);

   @CometMapping(
      value = "/rb/fin/loan/standby",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "9501",
      name = "联机备款"
   )
   public Core10009501Out runService(@RequestBody Core10009501In in) {
      return (Core10009501Out)ExecutorFlow.startFlow("core10009501Flow", in);
   }
}
