package com.dcits.ensemble.rb.service.bs.jud;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.cm.ICore14000106;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000106In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000106Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000106 implements ICore14000106 {
   private static final Logger log = LoggerFactory.getLogger(Core14000106.class);

   @CometMapping(
      value = "/rb/inq/acct/impound/query",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0106",
      name = "司法强制扣划信息查询"
   )
   public Core14000106Out runService(@RequestBody Core14000106In in) {
      return (Core14000106Out)ExecutorFlow.startFlow("core14000106Flow", in, Core14000106Out.class);
   }
}
