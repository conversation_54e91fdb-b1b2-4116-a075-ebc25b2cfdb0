package com.dcits.ensemble.rb.service.bs.fee;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore1400049076;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400049076In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400049076Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400049076 implements ICore1400049076 {
   private static final Logger log = LoggerFactory.getLogger(Core1400049076.class);

   @CometMapping(
      value = "/rb/inq/fee/package",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "049076",
      name = "费用套餐查询"
   )
   public Core1400049076Out runService(@RequestBody Core1400049076In in) {
      return (Core1400049076Out)ExecutorFlow.startFlow("core1400049076Flow", in, Core1400049076Out.class);
   }
}
