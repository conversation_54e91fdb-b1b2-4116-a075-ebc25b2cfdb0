package com.dcits.ensemble.rb.service.bs.bill.pn;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1400023402;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400023402In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400023402Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400023402 implements ICore1400023402 {
   private static final Logger log = LoggerFactory.getLogger(Core1400023402.class);

   @CometMapping(
      value = "/rb/inq/bill/tranDetail",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "023402",
      name = "银行本票交易信息查询"
   )
   public Core1400023402Out runService(@RequestBody Core1400023402In in) {
      return (Core1400023402Out)ExecutorFlow.startFlow("core1400023402Flow", in, Core1400023402Out.class);
   }
}
