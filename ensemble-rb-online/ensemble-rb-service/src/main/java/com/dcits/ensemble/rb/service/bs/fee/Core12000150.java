package com.dcits.ensemble.rb.service.bs.fee;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12000150;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000150In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000150Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000150 implements ICore12000150 {
   private static final Logger log = LoggerFactory.getLogger(Core12000150.class);

   @CometMapping(
      value = "/rb/nfin/fee/amortize/change",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0150",
      name = "预收手续费修改"
   )
   public Core12000150Out runService(@RequestBody Core12000150In in) {
      return (Core12000150Out)ExecutorFlow.startGravity(in, Core12000150Out.class);
   }
}
