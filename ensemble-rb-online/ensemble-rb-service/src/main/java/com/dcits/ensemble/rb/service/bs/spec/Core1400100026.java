package com.dcits.ensemble.rb.service.bs.spec;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1400100026;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100026In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100026Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400100026 implements ICore1400100026 {
   private static final Logger log = LoggerFactory.getLogger(Core1400100026.class);

   @CometMapping(
      value = "/rb/inq/acct/allacctquery",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "100026",
      name = "华兴储蓄产品信息查询"
   )
   public Core1400100026Out runService(@RequestBody Core1400100026In in) {
      return (Core1400100026Out)ExecutorFlow.startFlow("core1400100026Flow", in);
   }
}
