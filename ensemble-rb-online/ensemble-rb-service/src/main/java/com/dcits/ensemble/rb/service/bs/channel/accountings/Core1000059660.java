package com.dcits.ensemble.rb.service.bs.channel.accountings;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.accounting.ICore1000059660;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.accounting.Core1000059660In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.accounting.Core1000059660Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core1000059660 implements ICore1000059660 {
   private static final Logger log = LoggerFactory.getLogger(Core1000059660.class);

   @CometMapping(
      value = "/rb/fin/channel/common/account",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "059660",
      name = "通用记账"
   )
   public Core1000059660Out runService(@RequestBody Core1000059660In in) {
      return (Core1000059660Out)ExecutorFlow.startFlow("core1000059660Flow", in, Core1000059660Out.class);
   }
}
