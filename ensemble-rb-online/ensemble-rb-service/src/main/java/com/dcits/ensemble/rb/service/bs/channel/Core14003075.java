package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore14003075;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core14003075In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core14003075Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core14003075 implements ICore14003075 {
   private static final Logger log = LoggerFactory.getLogger(Core14003075.class);

   @CometMapping(
      value = "/rb/inq/batch/acct/detail",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "3075",
      name = "存款账户批量明细查询"
   )
   public Core14003075Out runService(@RequestBody Core14003075In in) {
      return (Core14003075Out)ExecutorFlow.startFlow("core14003075Flow", in, Core14003075Out.class);
   }
}
