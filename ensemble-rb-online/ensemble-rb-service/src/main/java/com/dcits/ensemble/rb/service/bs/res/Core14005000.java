package com.dcits.ensemble.rb.service.bs.res;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14005000;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14005000In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14005000Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core14005000 implements ICore14005000 {
   private static final Logger log = LoggerFactory.getLogger(Core14005000.class);

   @CometMapping(
      value = "/rb/inq/rev/allow",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "5000",
      name = "据reference或者渠道流水号检查账户限制，是否重冲正"
   )
   public Core14005000Out runService(@RequestBody Core14005000In in) {
      return (Core14005000Out)ExecutorFlow.startFlow("core14005000Flow", in, Core14005000Out.class);
   }
}
