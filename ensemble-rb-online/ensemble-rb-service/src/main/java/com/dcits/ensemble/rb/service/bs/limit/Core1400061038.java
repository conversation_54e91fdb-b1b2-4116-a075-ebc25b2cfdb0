package com.dcits.ensemble.rb.service.bs.limit;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1400061038;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400061038In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400061038Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400061038 implements ICore1400061038 {
   private static final Logger log = LoggerFactory.getLogger(Core1400061038.class);

   @CometMapping(
      value = "/rb/nfin/tran/cumulative/query",
      name = "深圳地区限额查询"
   )
   public Core1400061038Out runService(@RequestBody Core1400061038In in) {
      return (Core1400061038Out)ExecutorFlow.startFlow("core1400061038Flow", in);
   }
}
