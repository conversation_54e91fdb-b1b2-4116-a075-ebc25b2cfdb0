package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore1220039003;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1220039003In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1220039003Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core1220039003 implements ICore1220039003 {
   private static final Logger log = LoggerFactory.getLogger(Core1220039003.class);

   @CometMapping(
      value = "/rb/file/babbill/syn",
      serviceCode = "MbsdCore",
      messageType = "1220",
      messageCode = "039003",
      name = "汇票信息签发同步"
   )
   public Core1220039003Out runService(@RequestBody Core1220039003In in) {
      return (Core1220039003Out)ExecutorFlow.startGravity(in, Core1220039003Out.class);
   }
}
