package com.dcits.ensemble.rb.service.bs.spec.id;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore14000805;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000805In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000805Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000805 implements ICore14000805 {
   private static final Logger log = LoggerFactory.getLogger(Core14000805.class);

   @CometMapping(
      value = "/rb/inq/ind/agreement",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0805",
      name = "智能通知存款信息查询"
   )
   public Core14000805Out runService(@RequestBody Core14000805In in) {
      return (Core14000805Out)ExecutorFlow.startFlow("core14000805Flow", in, Core14000805Out.class);
   }
}
