package com.dcits.ensemble.rb.service.bs.pcp;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.pcp.ICore12000702;
import com.dcits.ensemble.rb.api.model.mbsdcore.pcp.Core12000702In;
import com.dcits.ensemble.rb.api.model.mbsdcore.pcp.Core12000702Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000702 implements ICore12000702 {
   private static final Logger log = LoggerFactory.getLogger(Core12000702.class);

   @CometMapping(
      value = "/rb/nfin/pcp/group/maint",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0702",
      name = "账户组维护"
   )
   public Core12000702Out runService(@RequestBody Core12000702In in) {
      return (Core12000702Out)ExecutorFlow.startGravity(in, Core12000702Out.class);
   }
}
