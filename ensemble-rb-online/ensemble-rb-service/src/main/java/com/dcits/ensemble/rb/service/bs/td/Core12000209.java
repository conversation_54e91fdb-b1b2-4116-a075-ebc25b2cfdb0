package com.dcits.ensemble.rb.service.bs.td;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12000209;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000209In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000209Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000209 implements ICore12000209 {
   private static final Logger log = LoggerFactory.getLogger(Core12000209.class);

   @CometMapping(
      value = "/rb/nfin/term/agre/open",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0209",
      name = "协议存款开户"
   )
   public Core12000209Out runService(@RequestBody Core12000209In in) {
      return (Core12000209Out)ExecutorFlow.startGravity(in, Core12000209Out.class);
   }
}
