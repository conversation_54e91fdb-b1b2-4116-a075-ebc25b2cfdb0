package com.dcits.ensemble.rb.service.bs.tae;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.service.util.IAsynReversal;
import com.dcits.ensemble.rb.service.util.tae.RbReversalIn;
import com.dcits.ensemble.rb.service.util.tae.RbReversalOut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
@Service
public class AsynReversalImpl implements IAsynReversal {
   private static final Logger log = LoggerFactory.getLogger(AsynReversalImpl.class);

   @CometMapping(
      value = "/rb/rev/tran/rev",
      serviceCode = "MbsdCore",
      messageType = "1500",
      messageCode = "0103",
      name = "存款异步记账统一冲正"
   )
   public RbReversalOut runService(@RequestBody RbReversalIn in) {
      return (RbReversalOut)ExecutorFlow.startFlow("asynReversalFlow", in, RbReversalOut.class);
   }
}
