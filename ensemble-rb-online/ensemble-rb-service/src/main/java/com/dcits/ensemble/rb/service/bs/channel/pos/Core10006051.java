package com.dcits.ensemble.rb.service.bs.channel.pos;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.pos.ICore10006051;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core10006051In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core10006051Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core10006051 implements ICore10006051 {
   private static final Logger log = LoggerFactory.getLogger(Core10006051.class);

   @CometMapping(
      value = "/rb/fin/channel/meagent/withdraw",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "6051",
      name = "银联本代他取款"
   )
   public Core10006051Out runService(@RequestBody Core10006051In in) {
      return (Core10006051Out)ExecutorFlow.startFlow("core10006051Flow", in, Core10006051Out.class);
   }
}
