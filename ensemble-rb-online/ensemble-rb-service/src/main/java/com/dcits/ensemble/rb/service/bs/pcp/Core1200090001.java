package com.dcits.ensemble.rb.service.bs.pcp;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1200090001;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1200090001In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1200090001Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1200090001 implements ICore1200090001 {
   private static final Logger log = LoggerFactory.getLogger(Core1200090001.class);

   @CometMapping(
      value = "/rb/nfin/cashctl/sign",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "090001",
      name = "现金管理签约服务"
   )
   public Core1200090001Out runService(@RequestBody Core1200090001In in) {
      return (Core1200090001Out)ExecutorFlow.startGravity(in);
   }
}
