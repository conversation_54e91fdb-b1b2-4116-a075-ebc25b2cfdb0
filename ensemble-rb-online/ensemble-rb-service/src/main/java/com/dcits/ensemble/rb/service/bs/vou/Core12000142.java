package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12000142;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000142In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000142Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000142 implements ICore12000142 {
   private static final Logger log = LoggerFactory.getLogger(Core12000142.class);

   @CometMapping(
      value = "/rb/nfin/individual/voucher/lost/operate",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0142",
      name = "凭证挂失解挂"
   )
   public Core12000142Out runService(@RequestBody Core12000142In in) {
      return (Core12000142Out)ExecutorFlow.startFlow("core12000142Flow", in, Core12000142Out.class);
   }
}
