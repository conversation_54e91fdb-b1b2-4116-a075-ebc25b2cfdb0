package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore1200050158;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1200050158In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1200050158Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1200050158 implements ICore1200050158 {
   private static final Logger log = LoggerFactory.getLogger(Core1200050158.class);

   @CometMapping(
      value = "/rb/nfin/channel/cmcd/acct/syn",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "050158",
      name = "现管子账户信息同步"
   )
   public Core1200050158Out runService(@RequestBody Core1200050158In in) {
      return (Core1200050158Out)ExecutorFlow.startFlow("core1200050158Flow", in, Core1200050158Out.class);
   }
}
