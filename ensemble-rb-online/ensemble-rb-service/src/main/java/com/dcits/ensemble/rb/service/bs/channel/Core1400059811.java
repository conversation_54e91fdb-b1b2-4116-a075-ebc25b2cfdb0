package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore1400059811;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1400059811In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1400059811Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core1400059811 implements ICore1400059811 {
   private static final Logger log = LoggerFactory.getLogger(Core1400059811.class);

   @CometMapping(
      value = "/rb/inq/channel/phone/repeat",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "059811",
      name = "手机号码重复查询"
   )
   public Core1400059811Out runService(@RequestBody Core1400059811In in) {
      return (Core1400059811Out)ExecutorFlow.startFlow("core1400059811Flow", in, Core1400059811Out.class);
   }
}
