package com.dcits.ensemble.rb.service.bs.fee;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000186In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000186Out;
import com.dcits.ensemble.rb.api.model.mbsdcore.ICore14000186;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000186 implements ICore14000186 {
   private static final Logger log = LoggerFactory.getLogger(Core14000186.class);

   @CometMapping(
      value = "/rb/inq/fee/pay",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0186",
      name = "费用支出信息查询"
   )
   public Core14000186Out runService(@RequestBody Core14000186In in) {
      return (Core14000186Out)ExecutorFlow.startFlow("core14000186Flow", in);
   }
}
