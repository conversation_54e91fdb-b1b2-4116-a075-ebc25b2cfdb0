package com.dcits.ensemble.rb.service.bs.yht;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12000230;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000230In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000230Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000230 implements ICore12000230 {
   private static final Logger log = LoggerFactory.getLogger(Core12000230.class);

   @CometMapping(
      value = "/rb/nfin/yht/open",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0230",
      name = "一户通开户"
   )
   public Core12000230Out runService(@RequestBody Core12000230In in) {
      return (Core12000230Out)ExecutorFlow.startGravity(in, Core12000230Out.class);
   }
}
