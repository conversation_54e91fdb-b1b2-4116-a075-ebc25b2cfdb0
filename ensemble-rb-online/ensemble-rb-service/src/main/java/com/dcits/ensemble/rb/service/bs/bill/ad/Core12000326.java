package com.dcits.ensemble.rb.service.bs.bill.ad;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ad.ICore12000326;
import com.dcits.ensemble.rb.api.model.mbsdcore.ad.Core12000326In;
import com.dcits.ensemble.rb.api.model.mbsdcore.ad.Core12000326Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000326 implements ICore12000326 {
   private static final Logger log = LoggerFactory.getLogger(Core12000326.class);

   @CometMapping(
      value = "/rb/nfin/bad/return",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0326",
      name = "银行承兑汇票未备款退回"
   )
   public Core12000326Out runService(@RequestBody Core12000326In in) {
      return (Core12000326Out)ExecutorFlow.startGravity(in, Core12000326Out.class);
   }
}
