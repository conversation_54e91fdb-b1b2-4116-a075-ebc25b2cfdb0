package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore12000140;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000140In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000140Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000140 implements ICore12000140 {
   private static final Logger log = LoggerFactory.getLogger(Core12000140.class);

   @CometMapping(
      value = "/rb/nfin/pbk/change/card",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0140",
      name = "折换卡"
   )
   public Core12000140Out runService(@RequestBody Core12000140In in) {
      return (Core12000140Out)ExecutorFlow.startGravity(in);
   }
}
