package com.dcits.ensemble.rb.service.bs.channel.accountings;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.cd.ICore1000050206;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.accounting.Core1000050206In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.accounting.Core1000050206Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core1000050206 implements ICore1000050206 {
   private static final Logger log = LoggerFactory.getLogger(Core1000050206.class);

   @CometMapping(
      value = "/rb/fin/bon/wtd/post",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "050206",
      name = "国债兑付提交"
   )
   public Core1000050206Out runService(@RequestBody Core1000050206In in) {
      return (Core1000050206Out)ExecutorFlow.startFlow("core1000050206Flow", in, Core1000050206Out.class);
   }
}
