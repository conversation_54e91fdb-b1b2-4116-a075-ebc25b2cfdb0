package com.dcits.ensemble.rb.service.bs.agr.subagr.subindivagr;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore14003211;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14003211In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14003211Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14003211 implements ICore14003211 {
   private static final Logger log = LoggerFactory.getLogger(Core14003211.class);

   @CometMapping(
      value = "/rb/inq/avg/agreement/",
      name = "日均余额靠档协议信息查询"
   )
   public Core14003211Out runService(@RequestBody Core14003211In in) {
      return (Core14003211Out)ExecutorFlow.startFlow("core14003211Flow", in);
   }
}
