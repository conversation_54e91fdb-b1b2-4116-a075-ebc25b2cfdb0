package com.dcits.ensemble.rb.service.bs.yht;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore12000232;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000232In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000232Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000232 implements ICore12000232 {
   private static final Logger log = LoggerFactory.getLogger(Core12000232.class);

   @CometMapping(
      value = "/rb/nfin/yht/batch/close",
      serviceCode = "Core",
      messageType = "1200",
      messageCode = "0232",
      name = "单位虚拟子账户批量销户"
   )
   public Core12000232Out runService(@RequestBody Core12000232In in) {
      return (Core12000232Out)ExecutorFlow.startGravity(in, Core12000232Out.class);
   }
}
