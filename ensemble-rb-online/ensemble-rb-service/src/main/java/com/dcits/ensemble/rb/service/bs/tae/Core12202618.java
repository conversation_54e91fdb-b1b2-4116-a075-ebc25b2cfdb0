package com.dcits.ensemble.rb.service.bs.tae;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore12202618;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12202618In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12202618Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12202618 implements ICore12202618 {
   private static final Logger log = LoggerFactory.getLogger(Core12202618.class);

   @CometMapping(
      value = "/rb/file/account/check",
      name = "tae日间对账请求"
   )
   public Core12202618Out runService(@RequestBody Core12202618In in) {
      return (Core12202618Out)ExecutorFlow.startFlow("core12202618Flow", in);
   }
}
