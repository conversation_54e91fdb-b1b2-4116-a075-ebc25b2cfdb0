package com.dcits.ensemble.rb.service.bs.res;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000165In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000165Out;
import com.dcits.ensemble.rb.api.model.mbsdcore.ICore14000165;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000165 implements ICore14000165 {
   private static final Logger log = LoggerFactory.getLogger(Core14000165.class);

   @CometMapping(
      value = "/rb/inq/channel/control",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0165",
      name = "渠道控制查询"
   )
   public Core14000165Out runService(@RequestBody Core14000165In in) {
      return (Core14000165Out)ExecutorFlow.startFlow("core14000165Flow", in);
   }
}
