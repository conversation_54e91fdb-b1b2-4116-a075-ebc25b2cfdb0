package com.dcits.ensemble.rb.service.bs.agr.subagr.subcorpagr;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12002501;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12002501In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12002501Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12002501 implements ICore12002501 {
   private static final Logger log = LoggerFactory.getLogger(Core12002501.class);

   @CometMapping(
      value = "/rb/nfin/agreement/overdraft/sign",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "2501",
      name = "法人透支签约"
   )
   public Core12002501Out runService(@RequestBody Core12002501In in) {
      return (Core12002501Out)ExecutorFlow.startGravity(in, Core12002501Out.class);
   }
}
