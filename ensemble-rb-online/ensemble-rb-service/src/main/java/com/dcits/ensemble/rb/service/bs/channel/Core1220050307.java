package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore1220050307;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1220050307In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1220050307Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core1220050307 implements ICore1220050307 {
   private static final Logger log = LoggerFactory.getLogger(Core1220050307.class);

   @CometMapping(
      value = "/rb/file/current/batch/frozen/export",
      serviceCode = "MbsdCore",
      messageType = "1220",
      messageCode = "050307",
      name = "获取核心批量账户冻结文件"
   )
   public Core1220050307Out runService(@RequestBody Core1220050307In in) {
      return (Core1220050307Out)ExecutorFlow.startFlow("core1220050307Flow", in, Core1220050307Out.class);
   }
}
