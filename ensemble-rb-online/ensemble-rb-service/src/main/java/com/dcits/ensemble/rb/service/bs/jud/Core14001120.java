package com.dcits.ensemble.rb.service.bs.jud;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore14001120;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14001120In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14001120Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14001120 implements ICore14001120 {
   private static final Logger log = LoggerFactory.getLogger(Core14001120.class);

   @CometMapping(
      value = "/rb/inq/voucher/court",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "1120",
      name = "法院处理查询"
   )
   public Core14001120Out runService(@RequestBody Core14001120In in) {
      return (Core14001120Out)ExecutorFlow.startGravity(in);
   }
}
