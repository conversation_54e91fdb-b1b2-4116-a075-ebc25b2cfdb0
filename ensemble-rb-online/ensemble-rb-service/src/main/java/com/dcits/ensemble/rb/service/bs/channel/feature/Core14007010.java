package com.dcits.ensemble.rb.service.bs.channel.feature;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.feature.ICore14007010;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.feature.Core14007010In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.feature.Core14007010Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core14007010 implements ICore14007010 {
   private static final Logger log = LoggerFactory.getLogger(Core14007010.class);

   @CometMapping(
      value = "/rb/inq/agreement/jdl/product/query",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "7010",
      name = "加多利产品查询"
   )
   public Core14007010Out runService(@RequestBody Core14007010In in) {
      return (Core14007010Out)ExecutorFlow.startFlow("core14007010Flow", in, Core14007010Out.class);
   }
}
