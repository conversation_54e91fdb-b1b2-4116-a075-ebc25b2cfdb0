package com.dcits.ensemble.rb.service.bs.bill.pn;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1200023403;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1200023403In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1200023403Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1200023403 implements ICore1200023403 {
   private static final Logger log = LoggerFactory.getLogger(Core1200023403.class);

   @CometMapping(
      value = "/rb/nfin/bill/lostCancel",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "023403",
      name = "本票挂失解挂"
   )
   public Core1200023403Out runService(@RequestBody Core1200023403In in) {
      return (Core1200023403Out)ExecutorFlow.startFlow("core1200023403Flow", in, Core1200023403Out.class);
   }
}
