package com.dcits.ensemble.rb.service.bs.spec.id;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.id.ICore14000801;
import com.dcits.ensemble.rb.api.model.mbsdcore.id.Core14000801In;
import com.dcits.ensemble.rb.api.model.mbsdcore.id.Core14000801Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000801 implements ICore14000801 {
   private static final Logger log = LoggerFactory.getLogger(Core14000801.class);

   @CometMapping(
      value = "/rb/inq/id/agreement",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0801",
      name = "智能存款信息查询"
   )
   public Core14000801Out excute(@RequestBody Core14000801In in) {
      return (Core14000801Out)ExecutorFlow.startFlow("core14000801Flow", in, Core14000801Out.class);
   }
}
