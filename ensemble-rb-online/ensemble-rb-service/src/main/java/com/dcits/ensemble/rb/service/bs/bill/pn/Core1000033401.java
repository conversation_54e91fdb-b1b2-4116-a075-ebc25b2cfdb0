package com.dcits.ensemble.rb.service.bs.bill.pn;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.utils.BeanUtil;
import com.dcits.comet.commons.utils.DateUtil;
import com.dcits.comet.commons.utils.SeqUtils;
import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.flow.FlowDiagramExecutor;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.base.data.EnsSysHead;
import com.dcits.ensemble.fm.api.IFmBaseStor;
import com.dcits.ensemble.fm.model.FmBranch;
import com.dcits.ensemble.model.base.ChannelTypeEnum;
import com.dcits.ensemble.rb.api.bs.ICore1000033401;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1000033401In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1000033401Out;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core13000001In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core13000001Out;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core13000001In.Body;
import com.dcits.ensemble.rb.business.api.component.cm.transaction.ICretEvent;
import com.dcits.ensemble.rb.business.api.component.cm.transaction.IDebtEvent;
import com.dcits.ensemble.rb.business.api.component.cm.transaction.IMbAcctInfoService;
import com.dcits.ensemble.rb.business.api.component.cm.transaction.TranEventFactory;
import com.dcits.ensemble.rb.business.bc.component.cm.common.RbBaseAcctInfoImpl;
import com.dcits.ensemble.rb.business.bc.unit.acct.transaction.business.TransactionUtil;
import com.dcits.ensemble.rb.business.bc.unit.voucher.base.api.IMbUpdate;
import com.dcits.ensemble.rb.business.bc.unit.voucher.base.business.VoucherStatusEnum;
import com.dcits.ensemble.rb.business.common.rpc.TbRpc;
import com.dcits.ensemble.rb.business.common.util.FmUtil;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbPnRegister;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbPnTranDetail;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbTranDef;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbVoucherAcctRelation;
import com.dcits.ensemble.rb.business.model.cm.common.RbAcctStandardModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.TransactionControlModel;
import com.dcits.ensemble.rb.business.model.cm.transaction.bal.AcctTransactionInModel;
import com.dcits.ensemble.rb.business.model.entity.dbmodel.PtSettleAcct;
import com.dcits.ensemble.rb.business.model.fee.MbServChargeModel;
import com.dcits.ensemble.rb.business.model.fee.MbServModel;
import com.dcits.ensemble.rb.business.model.vou.cheque.MbPnRegModel;
import com.dcits.ensemble.rb.business.model.vou.cheque.MbPnTranDetailModel;
import com.dcits.ensemble.rb.business.repository.vou.PtSettleAcctRepository;
import com.dcits.ensemble.rb.business.repository.vou.RbPnRegisterRepository;
import com.dcits.ensemble.rb.business.repository.vou.RbPnTranDetailRepository;
import com.dcits.ensemble.rb.business.repository.vou.RbVoucherRepository;
import com.dcits.ensemble.rb.service.bg.bill.pn.BankOrderService;
import com.dcits.ensemble.util.BusiUtil;
import java.math.BigDecimal;
import java.util.Iterator;
import java.util.List;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1000033401 implements ICore1000033401 {
   private static final Logger log = LoggerFactory.getLogger(Core1000033401.class);
   @Resource
   private RbPnRegisterRepository mbPnRegisterRepository;
   @Resource
   private RbPnTranDetailRepository mbPnTranDetailRepository;
   @Resource
   private IMbUpdate mbUpdate;
   @Resource
   private RbVoucherRepository rbVoucherRepository;
   @Resource
   private IFmBaseStor fmBaseStor;
   @Resource
   private PtSettleAcctRepository ptSettleAcctRepository;
   @Resource
   private IMbAcctInfoService mbAcctInfoService;
   @Resource
   private RbBaseAcctInfoImpl rbBaseAcctInfo;
   @Resource
   private BankOrderService bankOrderService;

   @CometMapping(
      value = "/rb/fin/ad/note/operate",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "033401",
      name = "银行本票签发/修改/删除"
   )
   public Core1000033401Out runService(@RequestBody Core1000033401In in) {
      return (Core1000033401Out)ExecutorFlow.startGravity(in, Core1000033401Out.class);
   }

   public Core1000033401Out noteFinancial(MbPnRegModel mbPnRegModel, String operType) {
      Core1000033401Out out = this.bankOrderService.noteFinancial(mbPnRegModel, operType);
      return out;
   }

   private MbPnRegModel noteGrant(MbPnRegModel mbPnRegModel) {
      RbAcctStandardModel acctStandardModel = this.mbAcctInfoService.getRbAcctInfo(mbPnRegModel.getPayerAcctNo(), mbPnRegModel.getPayerProdeType(), mbPnRegModel.getPayerAcctCcy(), mbPnRegModel.getPayerAcctSeqNo());
      if (BusiUtil.isNotNull(acctStandardModel) && mbPnRegModel.getBillType().equals("2") && acctStandardModel.getClientType().equals("02")) {
         throw BusiUtil.createBusinessException("RB4109");
      } else {
         String acctNo = acctStandardModel.getActualAcctNo();
         List<RbVoucherAcctRelation> mbVoucherAcctRelationList = this.rbVoucherRepository.getMbVoucherAcctRelationByVoucher(acctNo, mbPnRegModel.getBillApplyType(), mbPnRegModel.getPrefix(), mbPnRegModel.getBillApplyNo(), "ACT", mbPnRegModel.getClientNo());
         if (BusiUtil.isNull(mbVoucherAcctRelationList)) {
            throw BusiUtil.createBusinessException("TB5428");
         } else {
            MbPnTranDetailModel mbPnTranDetailModel = mbPnRegModel.getMbPnTranDetailModel();
            RbAcctStandardModel innerAcctStandardModel = null;
            List<PtSettleAcct> ptSettleAccts = this.ptSettleAcctRepository.selectInfoMsg("BEPS", Context.getInstance().getBranchId(), (String)null, "22040100", mbPnRegModel.getPayerAcctCcy(), (String)null, (Long)null);
            if (BusiUtil.isNotNull(ptSettleAccts)) {
               innerAcctStandardModel = this.rbBaseAcctInfo.getLeadBaseAcctOrAcct(((PtSettleAcct)ptSettleAccts.get(0)).getAcctNo());
               this.pnTransfer(acctStandardModel, innerAcctStandardModel, "PN03", mbPnRegModel.getBillTranAmt());
               FmBranch var8 = FmUtil.getBranch(Context.getInstance().getBranchId());
               String branchName = var8.getBranchName();
               mbPnRegModel.setPayerBankCode(Context.getInstance().getBranchId());
               mbPnRegModel.setPayerBankName(branchName);
               mbPnTranDetailModel.setIssueBankNo(Context.getInstance().getBranchId());
               mbPnTranDetailModel.setIssueBankName(branchName);
               mbPnTranDetailModel.setDealResult("待签发");
               mbPnTranDetailModel.setApproUserId(mbPnRegModel.getBillSignApproveUser());
               mbPnTranDetailModel.setBillNo(mbPnRegModel.getBillNo());
               RbPnRegister mbPnRegister = this.noteGrantInsert(mbPnRegModel);
               mbPnRegModel.setSerialNo(mbPnRegister.getSerialNo());
               mbPnTranDetailModel.setSerialNo(mbPnRegister.getSerialNo());
               mbPnTranDetailModel.setOrigSerialNo(mbPnRegister.getSerialNo());
               this.mbPnTranDetailInsert(mbPnTranDetailModel);
               RbAcctStandardModel rbAcctStandardModel = this.rbBaseAcctInfo.getLeadBaseAcctOrAcct(mbPnRegModel.getBaseAcctNo());
               this.mbUpdate.updateVoucher(rbAcctStandardModel, mbPnRegModel.getBillApplyType(), mbPnRegModel.getBillApplyPrefix(), mbPnRegModel.getBillApplyNo(), mbPnRegModel.getBillApplyNo(), "USE", Context.getInstance().getBranchId(), acctStandardModel.getInternalKey().toString(), "本票申请书", (String)null, (String)null);
               return mbPnRegModel;
            } else {
               throw BusiUtil.createBusinessException("RB4219");
            }
         }
      }
   }

   private String noteReturn(MbPnRegModel mbPnRegModel) {
      MbPnTranDetailModel mbPnTranDetailModel = mbPnRegModel.getMbPnTranDetailModel();
      RbPnRegister mbPnRegister = this.mbPnRegisterRepository.getMbPnRegisterByBillNo(mbPnRegModel.getDocType(), mbPnRegModel.getBillNo());
      String dispareFlag = mbPnRegister.getBillStatus();
      if (!BusiUtil.isNotNull(dispareFlag) || !dispareFlag.equals("08") && !dispareFlag.equals("09")) {
         String returnAcctNo = mbPnTranDetailModel.getReturnAcctNo();
         return null;
      } else {
         throw BusiUtil.createBusinessException("RB4121");
      }
   }

   private String updateSignInfo(MbPnRegModel mbPnRegModel) {
      MbPnTranDetailModel mbPnTranDetailModel = mbPnRegModel.getMbPnTranDetailModel();
      RbPnRegister mbPnRegister = this.mbPnRegisterRepository.getMbPdRegisterBySerialNo(mbPnRegModel.getSerialNo());
      if (BusiUtil.isNotEquals(mbPnRegister.getBillStatus(), "00")) {
         throw BusiUtil.createBusinessException("RB4102");
      } else if (BusiUtil.isNotEquals(mbPnRegister.getBillSignUserId(), Context.getInstance().getUserId())) {
         throw BusiUtil.createBusinessException("RB4103");
      } else {
         mbPnRegister.setBillType(mbPnRegModel.getBillType());
         mbPnRegister.setPayeeBaseAcctNo(mbPnRegModel.getPayeeAcctNo());
         mbPnRegister.setPayeeAcctName(mbPnRegModel.getPayeeName());
         mbPnRegister.setRemark(mbPnRegModel.getRemark());
         mbPnRegister.setLastTranDate(mbPnRegModel.getLastTranDate());
         mbPnRegister.setDocType(mbPnRegModel.getDocType());
         this.mbPnRegisterRepository.updMbPnRegisterDb(mbPnRegister);
         this.setTranDetail(mbPnRegister, mbPnTranDetailModel);
         mbPnTranDetailModel.setSerialNo("");
         return this.mbPnTranDetailInsert(mbPnTranDetailModel);
      }
   }

   private String approveSignInfo(MbPnRegModel mbPnRegModel) {
      MbPnTranDetailModel mbPnTranDetailModel = mbPnRegModel.getMbPnTranDetailModel();
      RbPnRegister mbPnRegister = this.mbPnRegisterRepository.getMbPdRegisterBySerialNo(mbPnRegModel.getSerialNo());
      RbAcctStandardModel acctStandardModel = this.mbAcctInfoService.getRbAcctInfo(mbPnRegModel.getPayerAcctNo(), mbPnRegModel.getPayerProdeType(), mbPnRegModel.getPayerAcctCcy(), mbPnRegModel.getPayerAcctSeqNo());
      String option = mbPnRegModel.getOption();
      String encryptKey = mbPnRegModel.getEncryptKey();
      if ((BusiUtil.isEquals(option, "01") || BusiUtil.isEquals(option, "02") || BusiUtil.isEquals(option, "03")) && BusiUtil.isNotEquals(mbPnRegister.getBillStatus(), "00")) {
         throw BusiUtil.createBusinessException("RB4117");
      } else if (BusiUtil.isEquals(option, "04") && BusiUtil.isNotEquals(mbPnRegister.getBillStatus(), "01")) {
         throw BusiUtil.createBusinessException("RB4114");
      } else if (BusiUtil.isEquals(option, "05") && BusiUtil.isNull(mbPnRegModel.getBillNo())) {
         throw BusiUtil.createBusinessException("RB4115");
      } else {
         if (BusiUtil.isEquals(option, "01")) {
            mbPnRegister.setBillStatus("01");
            mbPnRegister.setAuthUserId(Context.getInstance().getAuthUserId());
            mbPnRegister.setLastTranDate(mbPnRegModel.getLastTranDate());
            this.mbPnRegisterRepository.updMbPnRegisterDb(mbPnRegister);
            mbPnTranDetailModel.setOrigSerialNo(mbPnRegModel.getSerialNo());
            mbPnTranDetailModel.setDealResult("复核通过");
            mbPnTranDetailModel.setBillNo(mbPnRegModel.getBillNo());
            mbPnTranDetailModel.setIssueBankNo(Context.getInstance().getBranchId());
            mbPnTranDetailModel.setBillStatus("01");
            mbPnTranDetailModel.setBillAmt(mbPnRegModel.getBillTranAmt());
            FmBranch branch = FmUtil.getBranch(acctStandardModel.getAcctBranch());
            mbPnTranDetailModel.setIssueBankName(branch.getBranchName());
            mbPnTranDetailModel.setApproUserId(Context.getInstance().getUserId());
            mbPnTranDetailModel.setSerialNo("");
            this.mbPnTranDetailInsert(mbPnTranDetailModel);
         } else if (BusiUtil.isEquals(option, "02")) {
            mbPnRegister.setBillStatus("00");
            mbPnRegister.setLastTranDate(mbPnRegModel.getLastTranDate());
            mbPnRegister.setRefuseReason(mbPnRegModel.getRejectReason());
            mbPnTranDetailModel.setBillAmt(mbPnRegModel.getBillTranAmt());
            mbPnTranDetailModel.setIssueBankNo(Context.getInstance().getBranchId());
            mbPnTranDetailModel.setIssueBankName(mbPnRegModel.getPayerBankName());
            this.mbPnRegisterRepository.updMbPnRegisterDb(mbPnRegister);
            mbPnTranDetailModel.setOperType("11");
            mbPnTranDetailModel.setOrigSerialNo(mbPnRegModel.getSerialNo());
            mbPnTranDetailModel.setDealResult("复核拒绝");
            mbPnTranDetailModel.setApproUserId(Context.getInstance().getUserId());
            mbPnTranDetailModel.setBillNo(mbPnRegModel.getBillNo());
            mbPnTranDetailModel.setIssueBankName(mbPnRegModel.getPayerBankName());
            mbPnTranDetailModel.setBillStatus("00");
            mbPnTranDetailModel.setSerialNo("");
            this.mbPnTranDetailInsert(mbPnTranDetailModel);
         } else if (BusiUtil.isEquals(option, "03")) {
            mbPnRegister.setBillStatus("00");
            mbPnRegister.setLastTranDate(mbPnRegModel.getLastTranDate());
            this.mbPnRegisterRepository.updMbPnRegisterDb(mbPnRegister);
            mbPnTranDetailModel.setOrigSerialNo(mbPnRegModel.getSerialNo());
            mbPnTranDetailModel.setDealResult("复核取消");
            mbPnTranDetailModel.setSerialNo("");
            mbPnTranDetailModel.setBillStatus("00");
         } else if (BusiUtil.isEquals(option, "04") || BusiUtil.isEquals(option, "05")) {
            mbPnTranDetailModel.setBillAmt(mbPnRegister.getBillTranAmt());
            RbAcctStandardModel acctStandardModel3 = this.mbAcctInfoService.getRbAcctInfo(mbPnRegister.getPayerBaseAcctNo(), mbPnRegister.getPayerProdType(), mbPnRegister.getPayerAcctCcy(), mbPnRegister.getPayerAcctSeqNo());
            RbAcctStandardModel rbAcctStandardModel = null;
            if (BusiUtil.isEquals(option, "04")) {
               rbAcctStandardModel = this.rbBaseAcctInfo.getLeadBaseAcctOrAcct(mbPnRegModel.getBaseAcctNo());
               if (BusiUtil.isNotNull(mbPnRegister.getDocType()) && BusiUtil.isNotNull(mbPnRegister.getBillNo())) {
                  this.mbUpdate.updateVoucher(rbAcctStandardModel, mbPnRegister.getDocType(), mbPnRegModel.getPrefix(), mbPnRegister.getBillNo(), mbPnRegister.getBillNo(), "CAN", Context.getInstance().getBranchId(), acctStandardModel3.getInternalKey().toString(), "银行本票", (String)null, (String)null);
               }
            }

            encryptKey = ((String)BusiUtil.nvl(mbPnTranDetailModel.getBillNo(), "")).concat(SeqUtils.getRandomNumber(12)).concat(mbPnTranDetailModel.getBillAmt().toString());
            encryptKey = encryptKey.length() > 16 ? encryptKey.substring(0, 16) : encryptKey;
            mbPnRegister.setBillPswd(encryptKey);
            mbPnRegister.setDocType(mbPnRegModel.getDocType());
            mbPnRegister.setBillNo(mbPnRegModel.getBillNo());
            mbPnRegister.setBillSignDate(DateUtil.parseDate(Context.getInstance().getRunDate()));
            mbPnRegister.setBillSignBranch(Context.getInstance().getBranchId());
            mbPnRegister.setApprUserId(Context.getInstance().getUserId());
            mbPnRegister.setLastTranDate(mbPnRegModel.getLastTranDate());
            this.mbPnRegisterRepository.updMbPnRegisterDb(mbPnRegister);
            RbPnTranDetail mbPnTranDetail = this.mbPnTranDetailRepository.getMbPnTranDetailOne(mbPnRegModel.getSerialNo(), "01");
            mbPnTranDetail.setDocType(mbPnRegModel.getDocType());
            mbPnTranDetail.setBillNo(mbPnRegModel.getBillNo());
            mbPnTranDetail.setBillPswd(encryptKey);
            mbPnTranDetail.setOrigSerialNo(mbPnRegModel.getSerialNo());
            mbPnTranDetail.setDealResult("已打印");
            this.mbPnTranDetailRepository.updMbPnTranDetailDb(mbPnTranDetail);
            if (BusiUtil.isEquals(option, "04")) {
               TbRpc.updateVoucher(mbPnRegModel.getDocType(), mbPnRegModel.getPrefix(), mbPnRegModel.getBillNo(), mbPnRegModel.getBillNo(), VoucherStatusEnum.ACT.getCode(), Context.getInstance().getBranchId(), acctStandardModel3.getInternalKey().toString(), "银行本票", (String)null);
               this.mbUpdate.updateVoucher(rbAcctStandardModel, mbPnRegModel.getDocType(), mbPnRegModel.getPrefix(), mbPnRegModel.getBillNo(), mbPnRegModel.getBillNo(), "ACT", Context.getInstance().getBranchId(), acctStandardModel3.getInternalKey().toString(), "银行本票", (String)null, (String)null);
            }
         }

         return encryptKey;
      }
   }

   private String deleteSignInfo(MbPnRegModel mbPnRegModel) {
      MbPnTranDetailModel mbPnTranDetailModel = mbPnRegModel.getMbPnTranDetailModel();
      RbPnRegister mbPnRegister = this.mbPnRegisterRepository.getMbPdRegisterBySerialNo(mbPnRegModel.getSerialNo());
      RbAcctStandardModel acctStandardModel = this.mbAcctInfoService.getRbAcctInfo(mbPnRegister.getPayerBaseAcctNo(), mbPnRegister.getPayerProdType(), mbPnRegister.getPayerAcctCcy(), mbPnRegister.getPayerAcctSeqNo());
      if (BusiUtil.isNotEquals(mbPnRegister.getBillStatus(), "00")) {
         throw BusiUtil.createBusinessException("RB4102");
      } else if (BusiUtil.isNotEquals(mbPnRegister.getBillSignUserId(), Context.getInstance().getUserId())) {
         throw BusiUtil.createBusinessException("RB4103");
      } else {
         mbPnRegister.setBillStatus("06");
         mbPnRegister.setLastTranDate(mbPnRegModel.getLastTranDate());
         this.mbPnRegisterRepository.updMbPnRegisterDb(mbPnRegister);
         RbPnTranDetail mbPnTranDetail = this.mbPnTranDetailRepository.getMbPnTranDetailOne(mbPnRegister.getSerialNo(), "00");
         RbAcctStandardModel innerAcctStandardModel = null;
         List<PtSettleAcct> ptSettleAccts = this.ptSettleAcctRepository.selectInfoMsg("BEPS", Context.getInstance().getBranchId(), (String)null, "22040100", mbPnRegModel.getPayerAcctCcy(), (String)null, (Long)null);
         if (BusiUtil.isNotNull(ptSettleAccts)) {
            innerAcctStandardModel = this.rbBaseAcctInfo.getLeadBaseAcctOrAcct(((PtSettleAcct)ptSettleAccts.get(0)).getAcctNo());
            this.pnTransfer(innerAcctStandardModel, acctStandardModel, "PN03", mbPnRegModel.getBillTranAmt());
            mbPnTranDetailModel.setOrigSerialNo(mbPnRegModel.getSerialNo());
            mbPnTranDetailModel.setBillStatus("07");
            mbPnTranDetailModel.setBillNo(mbPnRegModel.getBillNo());
            mbPnTranDetailModel.setIssueBankNo(mbPnRegModel.getBillSignBranch());
            mbPnTranDetailModel.setIssueBankName(mbPnRegModel.getPayerBankName());
            mbPnTranDetailModel.setApproUserId(Context.getInstance().getUserId());
            mbPnTranDetailModel.setDealResult("删除");
            mbPnTranDetailModel.setDocType(mbPnRegister.getBillApplyType());
            mbPnTranDetailModel.setSerialNo("");
            String serialNo = this.mbPnTranDetailInsert(mbPnTranDetailModel);
            EnsSysHead sysHead = new EnsSysHead();
            BeanUtil.copy(Context.getInstance().getSysHead(), sysHead);
            Core13000001In core13000001In = new Core13000001In();
            core13000001In.setSysHead(sysHead);
            core13000001In.getSysHead().setMessageType("1300");
            core13000001In.getSysHead().setMessageCode("0001");
            core13000001In.getSysHead().setServiceCode("MbsdCore");
            core13000001In.getSysHead().setTranMode("ONLINE");
            core13000001In.getSysHead().setApprFlag("");
            core13000001In.getSysHead().setAuthFlag("M");
            core13000001In.getSysHead().setSourceType(ChannelTypeEnum.MT.getCode());
            core13000001In.getSysHead().setSystemId("10");
            core13000001In.getSysHead().setTranDate(Context.getInstance().getTranDate());
            core13000001In.getSysHead().setUserId(Context.getInstance().getUserId());
            core13000001In.getSysHead().setUserLang("CHINESE");
            core13000001In.setSysHead(sysHead);
            core13000001In.setBody(new Body());
            core13000001In.getBody().setReversalReason("本票手续费退回");
            FlowDiagramExecutor.executorGravityDiagram(core13000001In, Core13000001Out.class);
            RbAcctStandardModel rbAcctStandardModel = this.rbBaseAcctInfo.getLeadBaseAcctOrAcct(mbPnRegModel.getBaseAcctNo());
            this.mbUpdate.updateVoucher(rbAcctStandardModel, mbPnRegister.getBillApplyType(), mbPnRegister.getBillApplyPrefix(), mbPnRegister.getBillApplyNo(), mbPnRegister.getBillApplyNo(), "ACT", Context.getInstance().getBranchId(), acctStandardModel.getInternalKey().toString(), "本票申请书", (String)null, (String)null);
            return serialNo;
         } else {
            throw BusiUtil.createBusinessException("RB4219");
         }
      }
   }

   private RbPnRegister noteGrantInsert(MbPnRegModel mbPnRegModel) {
      RbPnRegister mbPnRegister = this.mbPnRegisterRepository.createMbRegister(mbPnRegModel);
      this.mbPnRegisterRepository.mbPnRegisterInsert(mbPnRegister);
      return mbPnRegister;
   }

   private String mbPnTranDetailInsert(MbPnTranDetailModel mbPnTranDetailModel) {
      RbPnTranDetail mbPnTranDetail = this.mbPnTranDetailRepository.createMbPnTranDetail(mbPnTranDetailModel);
      RbPnRegister mbPnRegister = this.mbPnRegisterRepository.getMbPdRegisterBySerialNo(mbPnTranDetailModel.getOrigSerialNo());
      mbPnTranDetail.setSignTime(com.dcits.ensemble.util.DateUtil.formatDate(mbPnRegister.getBillSignDate(), "yyyy-MM-dd HH:mm:ss.SSSSSS"));
      this.mbPnTranDetailRepository.mbPnTranDetailInsert(mbPnTranDetail);
      return mbPnTranDetail.getSerialNo();
   }

   private void setTranDetail(RbPnRegister mbPnRegister, MbPnTranDetailModel mbPnTranDetailModel) {
      mbPnTranDetailModel.setDealResult("修改");
      mbPnTranDetailModel.setDocType(mbPnRegister.getBillApplyType());
      mbPnTranDetailModel.setOrigSerialNo(mbPnRegister.getSerialNo());
      mbPnTranDetailModel.setCcySign(mbPnRegister.getSignCcy());
      mbPnTranDetailModel.setBillAmt(mbPnRegister.getBillTranAmt());
      mbPnTranDetailModel.setTranferCashFlag(mbPnRegister.getTranferCashFlag());
      mbPnTranDetailModel.setBillStatus(mbPnRegister.getBillStatus());
      mbPnTranDetailModel.setIssueBankNo(mbPnRegister.getPayerBankCode());
      mbPnTranDetailModel.setIssueBankName(mbPnRegister.getPayerBankName());
      mbPnTranDetailModel.setPayerAcctNo(mbPnRegister.getPayerBaseAcctNo());
      mbPnTranDetailModel.setPayerName(mbPnRegister.getPayerAcctName());
      mbPnTranDetailModel.setPayeeAcctNo(mbPnRegister.getPayeeBaseAcctNo());
      mbPnTranDetailModel.setPayeeAcctName(mbPnRegister.getPayeeAcctName());
   }

   private void serviceFee(MbPnRegModel mbPnRegModel) {
      RbAcctStandardModel acctStandardModel = this.mbAcctInfoService.getRbAcctInfo(mbPnRegModel.getPayerAcctNo(), mbPnRegModel.getPayerProdeType(), mbPnRegModel.getPayerAcctCcy(), mbPnRegModel.getPayerAcctSeqNo());
      new MbServChargeModel();
      List<MbServModel> mbServModels = mbPnRegModel.getMbServModels();
      Iterator var5 = mbServModels.iterator();

      while(var5.hasNext()) {
         MbServModel mbServModel = (MbServModel)var5.next();
         mbServModel.setClientNo(acctStandardModel.getClientNo());
         mbServModel.setChargePeriodFreq("");
         mbServModel.setNextChargeDate(mbPnRegModel.getLastTranDate());
      }

   }

   public void pnTransfer(RbAcctStandardModel outAcctStandardModel, RbAcctStandardModel inAcctStandardModel, String tranType, BigDecimal tranAmt) {
      RbTranDef rbTranDef = TransactionUtil.getMbTranDef(tranType);
      AcctTransactionInModel tranModel = new AcctTransactionInModel(outAcctStandardModel, rbTranDef, tranAmt);
      tranModel.setBaseAcctNo(outAcctStandardModel.getBaseAcctNo());
      tranModel.setAcctCcy(outAcctStandardModel.getCcy());
      tranModel.setAcctSeqNo(outAcctStandardModel.getAcctSeqNo());
      tranModel.setProdType(outAcctStandardModel.getProdType());
      tranModel.setInternalKey(outAcctStandardModel.getInternalKey());
      tranModel.setOthBaseAcctNo(inAcctStandardModel.getBaseAcctNo());
      tranModel.setOthAcctCcy(inAcctStandardModel.getCcy());
      tranModel.setOthAcctSeqNo(inAcctStandardModel.getAcctSeqNo());
      tranModel.setOthProdType(inAcctStandardModel.getProdType());
      tranModel.setOthInternalKey(inAcctStandardModel.getInternalKey());
      tranModel.setPrimaryEventType("DEBT");
      TransactionControlModel transactionControlModel = TransactionControlModel.builder().build();
      IDebtEvent debtEvent = TranEventFactory.getDebtEvnt();
      debtEvent.execute(tranModel, transactionControlModel);
      String mbTranDef1 = rbTranDef.getOthTranType();
      RbTranDef rbTranDef11 = TransactionUtil.getMbTranDef(mbTranDef1);
      AcctTransactionInModel tranModelDebt = new AcctTransactionInModel(inAcctStandardModel, rbTranDef11, tranAmt);
      tranModelDebt.setBaseAcctNo(inAcctStandardModel.getBaseAcctNo());
      tranModelDebt.setAcctCcy(inAcctStandardModel.getCcy());
      tranModelDebt.setAcctSeqNo(inAcctStandardModel.getAcctSeqNo());
      tranModelDebt.setProdType(inAcctStandardModel.getProdType());
      tranModelDebt.setInternalKey(inAcctStandardModel.getInternalKey());
      tranModelDebt.setOthBaseAcctNo(outAcctStandardModel.getBaseAcctNo());
      tranModelDebt.setOthAcctCcy(outAcctStandardModel.getCcy());
      tranModelDebt.setOthAcctSeqNo(outAcctStandardModel.getAcctSeqNo());
      tranModelDebt.setOthProdType(outAcctStandardModel.getProdType());
      tranModelDebt.setOthInternalKey(outAcctStandardModel.getInternalKey());
      ICretEvent certEvent = TranEventFactory.getCretEvent();
      certEvent.execute(tranModelDebt, transactionControlModel);
   }
}
