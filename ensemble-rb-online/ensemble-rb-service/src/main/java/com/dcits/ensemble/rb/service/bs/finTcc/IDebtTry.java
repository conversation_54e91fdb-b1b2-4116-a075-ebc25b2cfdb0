package com.dcits.ensemble.rb.service.bs.finTcc;

import com.dcits.comet.flow.annotation.CometDesc;
import com.dcits.ensemble.rb.service.util.tae.AsynFinancialIn;
import com.dcits.ensemble.rb.service.util.tae.AsynFinancialOut;
import com.dcits.gravity.api.annotation.ServiceApi;
import org.springframework.web.bind.annotation.RequestBody;

@ServiceApi
public interface IDebtTry {
   String URL = "/rb/fin/debt/try";

   @CometDesc(
      serviceCode = "MbsdCore",
      messageType = "1500",
      messageCode = "0110",
      businCategory = "1500-内部调用",
      name = "借方Try接口",
      desc = "TAE回调统一入口具体实现由交易场景独立实现",
      remark = "标准优化"
   )
   AsynFinancialOut runService(@RequestBody AsynFinancialIn var1);
}
