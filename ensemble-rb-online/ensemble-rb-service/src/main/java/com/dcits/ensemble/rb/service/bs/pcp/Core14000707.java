package com.dcits.ensemble.rb.service.bs.pcp;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.pcp.ICore14000707;
import com.dcits.ensemble.rb.api.model.mbsdcore.pcp.Core14000707In;
import com.dcits.ensemble.rb.api.model.mbsdcore.pcp.Core14000707Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000707 implements ICore14000707 {
   private static final Logger log = LoggerFactory.getLogger(Core14000707.class);

   @CometMapping(
      value = "/rb/inq/pcp/acct/sign/query",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0707",
      name = "资金池签约分户信息查询"
   )
   public Core14000707Out runService(@RequestBody Core14000707In in) {
      return (Core14000707Out)ExecutorFlow.startFlow("core14000707Flow", in, Core14000707Out.class);
   }
}
