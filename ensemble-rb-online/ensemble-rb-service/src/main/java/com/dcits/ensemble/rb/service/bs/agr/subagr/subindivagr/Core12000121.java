package com.dcits.ensemble.rb.service.bs.agr.subagr.subindivagr;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12000121;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000121In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000121Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000121 implements ICore12000121 {
   private static final Logger log = LoggerFactory.getLogger(Core12000121.class);

   @CometMapping(
      value = "/rb/nfin/sweep/agreement/operate",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0121",
      name = "约定转存协议操作"
   )
   public Core12000121Out runService(@RequestBody Core12000121In in) {
      return (Core12000121Out)ExecutorFlow.startGravity(in, Core12000121Out.class);
   }
}
