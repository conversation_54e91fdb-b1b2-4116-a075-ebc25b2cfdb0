package com.dcits.ensemble.rb.service.bs.agr.subagr.subcorpagr;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1400100706;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100706In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100706Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400100706 implements ICore1400100706 {
   private static final Logger log = LoggerFactory.getLogger(Core1400100706.class);

   @CometMapping(
      value = "/rb/inq/pcp/cross/agreement",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "100706",
      name = "跨境资金池查询"
   )
   public Core1400100706Out runService(@RequestBody Core1400100706In in) {
      return (Core1400100706Out)ExecutorFlow.startFlow("core1400100706Flow", in);
   }
}
