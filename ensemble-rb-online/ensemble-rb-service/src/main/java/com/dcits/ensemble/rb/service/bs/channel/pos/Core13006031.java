package com.dcits.ensemble.rb.service.bs.channel.pos;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.pos.ICore13006031;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core13006031In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core13006031Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core13006031 implements ICore13006031 {
   private static final Logger log = LoggerFactory.getLogger(Core13006031.class);

   @CometMapping(
      value = "/rb/rev/posauthdone/cancel",
      serviceCode = "MbsdCore",
      messageType = "1300",
      messageCode = "6031",
      name = "银联POS预授权完成撤销冲正"
   )
   public Core13006031Out runService(@RequestBody Core13006031In in) {
      return (Core13006031Out)ExecutorFlow.startFlow("core13006031Flow", in, Core13006031Out.class);
   }
}
