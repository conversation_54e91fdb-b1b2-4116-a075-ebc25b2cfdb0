package com.dcits.ensemble.rb.service.bs.channel.pos;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.pos.ICore1400011002;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core1400011002In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core1400011002Out;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400011002 implements ICore1400011002 {
   @CometMapping(
      value = "/rb/inq/corp/client/holiday",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "011002",
      name = "对公客户节假日查询"
   )
   public Core1400011002Out runService(@RequestBody Core1400011002In in) {
      return (Core1400011002Out)ExecutorFlow.startFlow("core1400011002Flow", in, Core1400011002Out.class);
   }
}
