package com.dcits.ensemble.rb.service.bs.pcp;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.pcp.ICore12000701;
import com.dcits.ensemble.rb.api.model.mbsdcore.pcp.Core12000701In;
import com.dcits.ensemble.rb.api.model.mbsdcore.pcp.Core12000701Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000701 implements ICore12000701 {
   private static final Logger log = LoggerFactory.getLogger(Core12000701.class);

   @CometMapping(
      value = "/rb/nfin/pcp/group/create",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0701",
      name = "账户组建立"
   )
   public Core12000701Out runService(@RequestBody Core12000701In in) {
      return (Core12000701Out)ExecutorFlow.startGravity(in, Core12000701Out.class);
   }
}
