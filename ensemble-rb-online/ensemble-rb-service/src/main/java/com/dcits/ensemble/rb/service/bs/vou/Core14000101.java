package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000101;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000101In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000101Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000101 implements ICore14000101 {
   private static final Logger log = LoggerFactory.getLogger(Core14000101.class);

   @CometMapping(
      value = "/rb/inq/deposit/cert",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0101",
      name = "存款证明凭证查询"
   )
   public Core14000101Out runService(@RequestBody Core14000101In in) {
      return (Core14000101Out)ExecutorFlow.startFlow("core14000101Flow", in, Core14000101Out.class);
   }
}
