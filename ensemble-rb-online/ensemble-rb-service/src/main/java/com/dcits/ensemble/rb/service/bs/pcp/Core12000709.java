package com.dcits.ensemble.rb.service.bs.pcp;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore12000709;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000709In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000709Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000709 implements ICore12000709 {
   private static final Logger log = LoggerFactory.getLogger(Core12000709.class);

   @CometMapping(
      value = "/rb/nfin/pcp/agreement/cancel",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0709",
      name = "资金池解约综合处理"
   )
   public Core12000709Out runService(@RequestBody Core12000709In in) {
      return (Core12000709Out)ExecutorFlow.startGravity(in);
   }
}
