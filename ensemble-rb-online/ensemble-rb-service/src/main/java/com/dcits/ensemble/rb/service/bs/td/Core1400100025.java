package com.dcits.ensemble.rb.service.bs.td;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1400100025;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100025In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100025Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400100025 implements ICore1400100025 {
   private static final Logger log = LoggerFactory.getLogger(Core1400100025.class);

   @CometMapping(
      value = "/rb/inq/acct/termquery",
      name = "客户定期业务汇总查询",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "100025"
   )
   public Core1400100025Out runService(@RequestBody Core1400100025In in) {
      return (Core1400100025Out)ExecutorFlow.startFlow("core1400100025Flow", in);
   }
}
