package com.dcits.ensemble.rb.service.bs.bill.pn;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore1400033402;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400033402In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400033402Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400033402 implements ICore1400033402 {
   private static final Logger log = LoggerFactory.getLogger(Core1400033402.class);

   @CometMapping(
      value = "/rb/inq/pn/tran/hist",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "033402",
      name = "银行本票交易信息查询"
   )
   public Core1400033402Out runService(@RequestBody Core1400033402In in) {
      return (Core1400033402Out)ExecutorFlow.startFlow("core1400033402Flow", in, Core1400033402Out.class);
   }
}
