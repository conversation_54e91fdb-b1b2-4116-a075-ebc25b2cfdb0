package com.dcits.ensemble.rb.service.bs.agr.subagr.subindivagr;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14002503;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14002503In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14002503Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14002503 implements ICore14002503 {
   private static final Logger log = LoggerFactory.getLogger(Core14002503.class);

   @CometMapping(
      value = "/rb/inq/supplement/agreement",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "2503",
      name = "金额补足签约查询"
   )
   public Core14002503Out runService(@RequestBody Core14002503In in) {
      return (Core14002503Out)ExecutorFlow.startFlow("core14002503Flow", in, Core14002503Out.class);
   }
}
