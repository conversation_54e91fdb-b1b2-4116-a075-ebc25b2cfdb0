package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000156;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000156In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000156Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000156 implements ICore14000156 {
   private static final Logger log = LoggerFactory.getLogger(Core14000156.class);

   @CometMapping(
      value = "/rb/inq/voucher/lost",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0156",
      name = "凭证挂失解挂补发查询"
   )
   public Core14000156Out runService(@RequestBody Core14000156In in) {
      return (Core14000156Out)ExecutorFlow.startFlow("core14000156Flow", in, Core14000156Out.class);
   }
}
