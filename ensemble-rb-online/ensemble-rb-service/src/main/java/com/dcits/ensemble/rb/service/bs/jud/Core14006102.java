package com.dcits.ensemble.rb.service.bs.jud;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14006102;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14006102In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14006102Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14006102 implements ICore14006102 {
   private static final Logger log = LoggerFactory.getLogger(Core14006102.class);

   @CometMapping(
      value = "/rb/inq/impound/hang/detail/query",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "6102",
      name = "强制扣划挂账详细信息查询"
   )
   public Core14006102Out runService(@RequestBody Core14006102In in) {
      return (Core14006102Out)ExecutorFlow.startFlow("core14006102Flow", in, Core14006102Out.class);
   }
}
