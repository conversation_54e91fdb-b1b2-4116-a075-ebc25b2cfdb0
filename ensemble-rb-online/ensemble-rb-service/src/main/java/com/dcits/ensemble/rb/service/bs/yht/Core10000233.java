package com.dcits.ensemble.rb.service.bs.yht;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore10000233;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10000233In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10000233Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core10000233 implements ICore10000233 {
   private static final Logger log = LoggerFactory.getLogger(Core10000233.class);

   @CometMapping(
      value = "/rb/fin/yht/deal",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "0233",
      name = "一户通调账"
   )
   public Core10000233Out runService(@RequestBody Core10000233In in) {
      return (Core10000233Out)ExecutorFlow.startGravity(in, Core10000233Out.class);
   }
}
