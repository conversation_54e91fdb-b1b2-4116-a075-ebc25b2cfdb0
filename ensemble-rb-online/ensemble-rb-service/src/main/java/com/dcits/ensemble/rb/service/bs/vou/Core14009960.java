package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14009960;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14009960In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14009960Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14009960 implements ICore14009960 {
   private static final Logger log = LoggerFactory.getLogger(Core14009960.class);

   @CometMapping(
      value = "/rb/inq/card/by/voucher",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "9960",
      name = "根据凭证号查询卡号"
   )
   public Core14009960Out runService(@RequestBody Core14009960In in) {
      return (Core14009960Out)ExecutorFlow.startFlow("core14009960Flow", in, Core14009960Out.class);
   }
}
