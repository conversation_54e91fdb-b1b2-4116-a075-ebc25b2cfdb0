package com.dcits.ensemble.rb.service.bs.agr.subagr.subindivagr;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000136;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000136In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000136Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000136 implements ICore14000136 {
   private static final Logger log = LoggerFactory.getLogger(Core14000136.class);

   @CometMapping(
      value = "/rb/inq/acc/agreement",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0136",
      name = "协定存款签约/解约查询"
   )
   public Core14000136Out runService(@RequestBody Core14000136In in) {
      return (Core14000136Out)ExecutorFlow.startFlow("core14000136Flow", in, Core14000136Out.class);
   }
}
