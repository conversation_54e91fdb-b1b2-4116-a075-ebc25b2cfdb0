package com.dcits.ensemble.rb.service.bs.agr.subagr.subindivagr;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1200100125;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1200100125In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1200100125Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1200100125 implements ICore1200100125 {
   private static final Logger log = LoggerFactory.getLogger(Core1200100125.class);

   @CometMapping(
      value = "/rb/nfin/agreement/individual",
      name = "对私卡理财签约",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "100125"
   )
   public Core1200100125Out runService(@RequestBody Core1200100125In in) {
      return (Core1200100125Out)ExecutorFlow.startGravity(in);
   }
}
