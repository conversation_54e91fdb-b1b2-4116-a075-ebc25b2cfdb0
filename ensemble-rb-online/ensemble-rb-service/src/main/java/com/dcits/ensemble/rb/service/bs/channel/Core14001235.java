package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore14001235;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core14001235In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core14001235Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core14001235 implements ICore14001235 {
   private static final Logger log = LoggerFactory.getLogger(Core14001235.class);

   @CometMapping(
      value = "/rb/inq/batch/acct",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "1235",
      name = "批量账户信息查询"
   )
   public Core14001235Out runService(@RequestBody Core14001235In in) {
      return (Core14001235Out)ExecutorFlow.startFlow("core14001235Flow", in, Core14001235Out.class);
   }
}
