package com.dcits.ensemble.rb.service.bs.bill.pn;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1400023401;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400023401In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400023401Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400023401 implements ICore1400023401 {
   private static final Logger log = LoggerFactory.getLogger(Core1400023401.class);

   @CometMapping(
      value = "/rb/inq/bill/register",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "023401",
      name = "银行本票签发查询"
   )
   public Core1400023401Out runService(@RequestBody Core1400023401In in) {
      return (Core1400023401Out)ExecutorFlow.startFlow("core1400023401Flow", in, Core1400023401Out.class);
   }
}
