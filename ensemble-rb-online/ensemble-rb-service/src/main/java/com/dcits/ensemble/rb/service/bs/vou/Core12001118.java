package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12001118;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12001118In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12001118Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12001118 implements ICore12001118 {
   private static final Logger log = LoggerFactory.getLogger(Core12001118.class);

   @CometMapping(
      value = "/rb/nfin/voucher/status/maint",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "1118",
      name = "凭证状态修改"
   )
   public Core12001118Out runService(@RequestBody Core12001118In in) {
      return (Core12001118Out)ExecutorFlow.startGravity(in, Core12001118Out.class);
   }
}
