package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000325;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000325In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000325Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000325 implements ICore14000325 {
   private static final Logger log = LoggerFactory.getLogger(Core14000325.class);

   @CometMapping(
      value = "/rb/inq/voucher/group",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0325",
      name = "凭证信息查询(分组)"
   )
   public Core14000325Out runService(@RequestBody Core14000325In in) {
      return (Core14000325Out)ExecutorFlow.startFlow("core14000325Flow", in, Core14000325Out.class);
   }
}
