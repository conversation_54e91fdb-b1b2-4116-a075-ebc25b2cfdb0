package com.dcits.ensemble.rb.service.bs.channel.pos;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.feature.ICore12006013;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core12006013In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core12006013Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core12006013 implements ICore12006013 {
   private static final Logger log = LoggerFactory.getLogger(Core12006013.class);

   @CometMapping(
      value = "/rb/nfin/pos/preauth/undo",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "6013",
      name = "银联POS预授权撤销"
   )
   public Core12006013Out runService(@RequestBody Core12006013In in) {
      return (Core12006013Out)ExecutorFlow.startGravity(in, Core12006013Out.class);
   }
}
