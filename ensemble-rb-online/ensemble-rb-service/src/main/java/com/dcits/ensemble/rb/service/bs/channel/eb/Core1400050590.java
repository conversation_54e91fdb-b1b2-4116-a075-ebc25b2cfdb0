package com.dcits.ensemble.rb.service.bs.channel.eb;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.eb.ICore1400050590;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.eb.Core1400050590In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.eb.Core1400050590Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core1400050590 implements ICore1400050590 {
   private static final Logger log = LoggerFactory.getLogger(Core1400050590.class);

   @CometMapping(
      value = "/rb/inq/channel/safe/lock",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "050590",
      name = "安全锁信息查询"
   )
   public Core1400050590Out runService(@RequestBody Core1400050590In in) {
      return (Core1400050590Out)ExecutorFlow.startFlow("core1400050590Flow", in, Core1400050590Out.class);
   }
}
