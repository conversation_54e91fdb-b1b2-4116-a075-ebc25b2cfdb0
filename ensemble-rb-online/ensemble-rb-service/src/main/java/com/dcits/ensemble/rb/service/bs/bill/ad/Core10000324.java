package com.dcits.ensemble.rb.service.bs.bill.ad;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ad.ICore10000324;
import com.dcits.ensemble.rb.api.model.mbsdcore.ad.Core10000324In;
import com.dcits.ensemble.rb.api.model.mbsdcore.ad.Core10000324Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core10000324 implements ICore10000324 {
   private static final Logger log = LoggerFactory.getLogger(Core10000324.class);

   @CometMapping(
      value = "/rb/fin/bab/standby",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "0324",
      name = "银行承兑汇票备款"
   )
   public Core10000324Out runService(@RequestBody Core10000324In in) {
      return (Core10000324Out)ExecutorFlow.startGravity(in, Core10000324Out.class);
   }
}
