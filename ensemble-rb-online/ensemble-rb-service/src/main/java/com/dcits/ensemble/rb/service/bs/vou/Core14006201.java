package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14006201;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14006201In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14006201Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14006201 implements ICore14006201 {
   private static final Logger log = LoggerFactory.getLogger(Core14006201.class);

   @CometMapping(
      value = "/rb/inq/deposit/prove/query",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "6201",
      name = "资信证明补打查询"
   )
   public Core14006201Out runService(@RequestBody Core14006201In in) {
      return (Core14006201Out)ExecutorFlow.startFlow("core14006201Flow", in, Core14006201Out.class);
   }
}
