package com.dcits.ensemble.rb.service.bs.channel.pos;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.pos.ICore13006035;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core13006035In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core13006035Out;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core13006035 implements ICore13006035 {
   @CometMapping(
      value = "/rb/rev/channel/pos/auth/cancel",
      serviceCode = "MbsdCore",
      messageType = "1300",
      messageCode = "6035",
      name = "POS预授权撤销冲正"
   )
   public Core13006035Out runService(@RequestBody Core13006035In in) {
      return (Core13006035Out)ExecutorFlow.startFlow("core13006035Flow", in, Core13006035Out.class);
   }
}
