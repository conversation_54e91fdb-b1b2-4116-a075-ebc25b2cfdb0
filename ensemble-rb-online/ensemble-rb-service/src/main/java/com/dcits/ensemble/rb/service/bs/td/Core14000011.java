package com.dcits.ensemble.rb.service.bs.td;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000011;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000011In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000011Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000011 implements ICore14000011 {
   private static final Logger log = LoggerFactory.getLogger(Core14000011.class);

   @CometMapping(
      value = "/rb/inq/term/period",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0011",
      name = "零存整取期次信息查询"
   )
   public Core14000011Out runService(@RequestBody Core14000011In in) {
      return (Core14000011Out)ExecutorFlow.startFlow("core14000011Flow", in, Core14000011Out.class);
   }
}
