package com.dcits.ensemble.rb.service.bs.channel.pos;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.pos.ICore10006036;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core10006036In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core10006036Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core10006036 implements ICore10006036 {
   private static final Logger log = LoggerFactory.getLogger(Core10006036.class);

   @CometMapping(
      value = "/rb/fin/cret/execute",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "6036",
      name = "银联他代本贷记记账"
   )
   public Core10006036Out runService(@RequestBody Core10006036In in) {
      return (Core10006036Out)ExecutorFlow.startFlow("core10006036Flow", in, Core10006036Out.class);
   }
}
