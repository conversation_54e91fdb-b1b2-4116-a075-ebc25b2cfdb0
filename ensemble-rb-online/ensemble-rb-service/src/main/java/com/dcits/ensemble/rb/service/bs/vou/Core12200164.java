package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12200164;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12200164In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12200164Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12200164 implements ICore12200164 {
   private static final Logger log = LoggerFactory.getLogger(Core12200164.class);

   @CometMapping(
      value = "/rb/file/emptycheque/import",
      serviceCode = "MbsdCore",
      messageType = "1220",
      messageCode = "0164",
      name = "空头支票名单导入"
   )
   public Core12200164Out runService(@RequestBody Core12200164In in) {
      return (Core12200164Out)ExecutorFlow.startGravity(in, Core12200164Out.class);
   }
}
