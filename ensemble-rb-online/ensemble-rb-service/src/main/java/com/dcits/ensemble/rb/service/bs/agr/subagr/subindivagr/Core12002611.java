package com.dcits.ensemble.rb.service.bs.agr.subagr.subindivagr;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore12002611;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12002611In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12002611Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core12002611 implements ICore12002611 {
   private static final Logger log = LoggerFactory.getLogger(Core12002611.class);

   @CometMapping(
      value = "/rb/nfin/northbound/unsign",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "2611",
      name = "北向通解约"
   )
   public Core12002611Out runService(@RequestBody Core12002611In in) {
      return (Core12002611Out)ExecutorFlow.startGravity(in, Core12002611Out.class);
   }
}
