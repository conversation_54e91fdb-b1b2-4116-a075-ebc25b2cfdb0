package com.dcits.ensemble.rb.service.bs.td;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000212;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000212In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000212Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000212 implements ICore14000212 {
   private static final Logger log = LoggerFactory.getLogger(Core14000212.class);

   @CometMapping(
      value = "/rb/inq/term/period/calc",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0212",
      name = "定期存期计算"
   )
   public Core14000212Out runService(@RequestBody Core14000212In in) {
      return (Core14000212Out)ExecutorFlow.startFlow("core14000212Flow", in, Core14000212Out.class);
   }
}
