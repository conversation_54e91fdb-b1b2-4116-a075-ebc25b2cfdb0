package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore14006604;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core14006604In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core14006604Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core14006604 implements ICore14006604 {
   private static final Logger log = LoggerFactory.getLogger(Core14006604.class);

   @CometMapping(
      value = "/rb/inq/pay/status",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "6604",
      name = "支付记账状态查询（晋商特色接口）"
   )
   public Core14006604Out runService(@RequestBody Core14006604In in) {
      return (Core14006604Out)ExecutorFlow.startFlow("core14006604Flow", in, Core14006604Out.class);
   }
}
