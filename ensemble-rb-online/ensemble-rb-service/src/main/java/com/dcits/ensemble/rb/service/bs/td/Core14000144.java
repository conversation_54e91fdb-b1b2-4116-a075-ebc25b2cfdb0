package com.dcits.ensemble.rb.service.bs.td;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore14000144;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000144In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000144Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000144 implements ICore14000144 {
   private static final Logger log = LoggerFactory.getLogger(Core14000144.class);

   @CometMapping(
      value = "/rb/inq/client/assetDetail",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0144",
      name = "客户资产收支余额明细查询"
   )
   public Core14000144Out runService(@RequestBody Core14000144In in) {
      return (Core14000144Out)ExecutorFlow.startFlow("core14000144Flow", in);
   }
}
