package com.dcits.ensemble.rb.service.bs.bill.pn;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.utils.DateUtil;
import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore1000033402;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1000033402In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1000033402Out;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1000033402In.Body;
import com.dcits.ensemble.rb.business.api.component.cm.transaction.IMbAcctInfoService;
import com.dcits.ensemble.rb.business.bc.component.cm.common.RbBaseAcctInfoImpl;
import com.dcits.ensemble.rb.business.bc.unit.voucher.base.api.IMbUpdate;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbPnPayment;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbPnRegister;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbPnTranDetail;
import com.dcits.ensemble.rb.business.model.cm.common.RbAcctStandardModel;
import com.dcits.ensemble.rb.business.model.entity.dbmodel.PtSettleAcct;
import com.dcits.ensemble.rb.business.repository.vou.PtSettleAcctRepository;
import com.dcits.ensemble.rb.business.repository.vou.RbPnPaymentRepository;
import com.dcits.ensemble.rb.business.repository.vou.RbPnRegisterRepository;
import com.dcits.ensemble.rb.business.repository.vou.RbPnTranDetailRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.List;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1000033402 implements ICore1000033402 {
   private static final Logger log = LoggerFactory.getLogger(Core1000033402.class);
   @Resource
   private RbPnRegisterRepository mbPnRegisterRepository;
   @Resource
   private RbPnTranDetailRepository mbPnTranDetailRepository;
   @Resource
   private RbPnPaymentRepository mbPnPaymentRepository;
   @Resource
   private IMbUpdate mbUpdate;
   @Resource
   private RbBaseAcctInfoImpl rbBaseAcctInfo;
   @Resource
   private Core1000033401 core1000033401;
   @Resource
   private PtSettleAcctRepository ptSettleAcctRepository;
   @Resource
   private IMbAcctInfoService mbAcctInfoService;

   @CometMapping(
      value = "/rb/fin/ad/note/pay",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "033402",
      name = "银行本票兑付"
   )
   public Core1000033402Out runService(@RequestBody Core1000033402In in) {
      return (Core1000033402Out)ExecutorFlow.startGravity(in, Core1000033402Out.class);
   }

   public Core1000033402Out noteFinancial(Core1000033402In in) {
      if (log.isInfoEnabled()) {
         log.info("start Core1000033402.noteFinancial");
      }

      Body body = in.getBody();
      Core1000033402Out out = new Core1000033402Out();
      RbPnRegister mbPnRegister = this.mbPnRegisterRepository.getMbPdRegisterBySerialNo(body.getOrigSerialNo());
      if (BusiUtil.isNotNull(mbPnRegister) && BusiUtil.isNotEquals(body.getBillPswd(), mbPnRegister.getBillPswd())) {
         throw BusiUtil.createBusinessException("RB4165");
      } else {
         RbAcctStandardModel innerAcctStandardModel = null;
         List<PtSettleAcct> ptSettleAccts = this.ptSettleAcctRepository.selectInfoMsg("BEPS", Context.getInstance().getBranchId(), (String)null, "22040100", body.getPayerAcctCcy(), (String)null, (Long)null);
         if (BusiUtil.isNotNull(ptSettleAccts)) {
            innerAcctStandardModel = this.rbBaseAcctInfo.getLeadBaseAcctOrAcct(((PtSettleAcct)ptSettleAccts.get(0)).getAcctNo());
            RbAcctStandardModel acctStandardModel1 = this.mbAcctInfoService.getRbAcctInfo(body.getPayeeBaseAcctNo(), body.getPayeeProdType(), body.getPayeeAcctCcy(), body.getPayeeAcctSeqNo());
            this.core1000033401.pnTransfer(innerAcctStandardModel, acctStandardModel1, "PN03", body.getBillAmt());
            RbAcctStandardModel acctStandardModel2 = this.mbAcctInfoService.getRbAcctInfo(body.getPayerBaseAcctNo(), body.getPayerProdType(), body.getPayerAcctCcy(), body.getPayerAcctSeqNo());
            this.mbUpdate.updateVoucher(acctStandardModel2, body.getDocType(), body.getPrefix(), body.getBillNo(), body.getBillNo(), "POB", Context.getInstance().getBranchId(), acctStandardModel2.getInternalKey().toString(), "银行本票", (String)null, (String)null);
            RbPnRegister mbPnRegister1 = new RbPnRegister();
            mbPnRegister1.setPaymentDate(DateUtil.parseDate(body.getPaymentDate()));
            mbPnRegister1.setPaymentBankNo(body.getPaymentBankNo());
            mbPnRegister1.setPaymentType(body.getTranferCashFlag());
            mbPnRegister1.setBillApplyNo(body.getOrigSerialNo());
            mbPnRegister1.setPayeeBaseAcctNo(body.getPayeeBaseAcctNo());
            mbPnRegister1.setPayeeAcctCcy(body.getPayeeAcctCcy());
            mbPnRegister1.setPayeeProdType(body.getPayeeProdType());
            mbPnRegister1.setPayeeAcctName(body.getPayeeAcctName());
            mbPnRegister1.setPayeeAcctSeqNo(body.getPayeeAcctSeqNo());
            mbPnRegister1.setBillStatus("02");
            this.mbPnRegisterRepository.updMbPnRegisterDb(mbPnRegister1);
            RbPnPayment mbPnPayment = new RbPnPayment();
            mbPnPayment.setSerialNo(body.getOrigSerialNo());
            mbPnPayment.setOrigSerialNo(body.getOrigSerialNo());
            mbPnPayment.setReference(Context.getInstance().getReference());
            mbPnPayment.setBillCategory(body.getBillCategory());
            mbPnPayment.setBillType(body.getBillType());
            mbPnPayment.setBillNo(body.getBillNo());
            mbPnPayment.setBillPswd(body.getBillPswd());
            mbPnPayment.setBillDate(DateUtil.parseDate(body.getBillDate()));
            mbPnPayment.setBillAmt(body.getBillAmt());
            mbPnPayment.setPayerAcctName(body.getPayerAcctName());
            mbPnPayment.setPayerAcctSeqNo(body.getPayerAcctSeqNo());
            mbPnPayment.setPayerProdType(body.getPayerProdType());
            mbPnPayment.setPayerAcctCcy(body.getPayerAcctCcy());
            mbPnPayment.setPayerBaseAcctNo(body.getPayerBaseAcctNo());
            mbPnPayment.setPayeeBaseAcctNo(body.getPayeeBaseAcctNo());
            mbPnPayment.setBillBankNo(body.getBillBankNo());
            mbPnPayment.setBillBankName(body.getBillBankName());
            mbPnPayment.setOrigPayeeAcctName(body.getOrigPayeeAcctName());
            mbPnPayment.setOrigPayeeAcctCcy(body.getOrigPayeeAcctCcy());
            mbPnPayment.setOrigPayeeAcctNo(body.getOrigPayeeAcctNo());
            mbPnPayment.setOrigPayeeAcctSeqNo(body.getOrigPayeeAcctSeqNo());
            mbPnPayment.setPayeeBaseAcctNo(body.getPayeeBaseAcctNo());
            mbPnPayment.setPayeeAcctCcy(body.getPayeeAcctCcy());
            mbPnPayment.setPayeeAcctSeqNo(body.getPayeeAcctSeqNo());
            mbPnPayment.setPayerProdType(body.getPayeeProdType());
            mbPnPayment.setPayeeAcctName(body.getPayeeAcctName());
            mbPnPayment.setIsEndorse(body.getIsEndorse());
            mbPnPayment.setEndorserNum(body.getEndorserNum());
            mbPnPayment.setRemark(body.getRemark());
            mbPnPayment.setPaymentBankNo(body.getPaymentBankNo());
            mbPnPayment.setPaymentDate(DateUtil.parseDate(body.getPaymentDate()));
            mbPnPayment.setPaymentUser(Context.getInstance().getUserId());
            mbPnPayment.setApprUserId(body.getApprUserId());
            mbPnPayment.setRefSerialNo(body.getRefSerialNo());
            mbPnPayment.setDocType(body.getDocType());
            mbPnPayment.setTranferCashFlag(body.getTranferCashFlag());
            this.mbPnPaymentRepository.mbPnPaymentInsert(mbPnPayment);
            String serialNo = mbPnPayment.getSerialNo();
            RbPnTranDetail mbPnTranDetail = new RbPnTranDetail();
            mbPnTranDetail.setDocType(body.getDocType());
            mbPnTranDetail.setDealResult("兑付");
            mbPnTranDetail.setBillStatus("02");
            mbPnTranDetail.setSerialNo(serialNo);
            mbPnTranDetail.setOrigSerialNo(body.getOrigSerialNo());
            mbPnTranDetail.setTranType("PN03");
            mbPnTranDetail.setOperType(body.getOperType());
            mbPnTranDetail.setBillType(body.getBillType());
            mbPnTranDetail.setBillNo(body.getBillNo());
            mbPnTranDetail.setBillPswd(body.getBillPswd());
            mbPnTranDetail.setBillAmt(body.getBillAmt());
            mbPnTranDetail.setTranferCashFlag(body.getTranferCashFlag());
            mbPnTranDetail.setPayeeBaseAcctNo(body.getPayeeBaseAcctNo());
            mbPnTranDetail.setPayerAcctName(body.getPayerAcctName());
            mbPnTranDetail.setPayeeAcctNo(body.getPayeeBaseAcctNo());
            mbPnTranDetail.setPayeeAcctName(body.getPayeeAcctName());
            mbPnTranDetail.setIssueBankNo(body.getBillBankNo());
            mbPnTranDetail.setIssueBankName(body.getBillBankName());
            mbPnTranDetail.setTranDate(DateUtil.parseDate(Context.getInstance().getTranDate()));
            mbPnTranDetail.setTranBranch(Context.getInstance().getBranchId());
            mbPnTranDetail.setUserId(Context.getInstance().getUserId());
            mbPnTranDetail.setAuthUserId(Context.getInstance().getAuthUserId());
            mbPnTranDetail.setReference(Context.getInstance().getReference());
            mbPnTranDetail.setSignTime(DateUtil.formatDate(mbPnRegister.getBillSignDate()));
            mbPnTranDetail.setSignCcy(mbPnRegister.getSignCcy());
            this.mbPnTranDetailRepository.insert(mbPnTranDetail);
            out.setTranHistSeqNo(serialNo);
            return out;
         } else {
            throw BusiUtil.createBusinessException("RB4219");
         }
      }
   }
}
