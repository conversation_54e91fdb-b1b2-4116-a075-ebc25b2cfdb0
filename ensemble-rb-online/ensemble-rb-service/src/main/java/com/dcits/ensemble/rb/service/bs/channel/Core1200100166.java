package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1200100166;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1200100166In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1200100166Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core1200100166 implements ICore1200100166 {
   private static final Logger log = LoggerFactory.getLogger(Core1200100166.class);

   @CometMapping(
      value = "/rb/nfin/foreign/debt/capital/limit/amend",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "100166",
      name = "外债及资本专户额度维护"
   )
   public Core1200100166Out runService(@RequestBody Core1200100166In core1200100166In) {
      return (Core1200100166Out)ExecutorFlow.startFlow("core1200100166Flow", core1200100166In, Core1200100166Out.class);
   }
}
