package com.dcits.ensemble.rb.service.bs.fee;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000234;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000234In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000234Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000234 implements ICore14000234 {
   private static final Logger log = LoggerFactory.getLogger(Core14000234.class);

   @CometMapping(
      value = "/rb/inq/fee/origtran",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0234",
      name = "根据流水号查询原交易费用列表"
   )
   public Core14000234Out runService(@RequestBody Core14000234In in) {
      return (Core14000234Out)ExecutorFlow.startFlow("core14000234Flow", in, Core14000234Out.class);
   }
}
