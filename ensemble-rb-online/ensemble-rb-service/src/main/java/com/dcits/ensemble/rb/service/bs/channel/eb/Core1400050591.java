package com.dcits.ensemble.rb.service.bs.channel.eb;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.eb.ICore1400050591;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.eb.Core1400050591In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.eb.Core1400050591Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core1400050591 implements ICore1400050591 {
   private static final Logger log = LoggerFactory.getLogger(Core1400050591.class);

   @CometMapping(
      value = "/rb/inq/safelock/refuse",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "050591",
      name = "安全锁信拒绝交易查询"
   )
   public Core1400050591Out runService(@RequestBody Core1400050591In in) {
      return (Core1400050591Out)ExecutorFlow.startFlow("core1400050591Flow", in, Core1400050591Out.class);
   }
}
