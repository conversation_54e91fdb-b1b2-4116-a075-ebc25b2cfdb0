package com.dcits.ensemble.rb.service.bs.pcp;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore12000205;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000205In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000205Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000205 implements ICore12000205 {
   private static final Logger log = LoggerFactory.getLogger(Core12000205.class);

   @CometMapping(
      value = "/rb/nfin/agreement/group",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0205",
      name = "存款组签约解约"
   )
   public Core12000205Out runService(@RequestBody Core12000205In in) {
      return (Core12000205Out)ExecutorFlow.startFlow("core12000205Flow", in);
   }
}
