package com.dcits.ensemble.rb.service.bs.fee;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1400100002;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100002In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100002Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400100002 implements ICore1400100002 {
   private static final Logger log = LoggerFactory.getLogger(Core1400100002.class);

   @CometMapping(
      value = "/rb/inq/fee/accr",
      name = "费用计提查询",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "100002"
   )
   public Core1400100002Out runService(@RequestBody Core1400100002In in) {
      return (Core1400100002Out)ExecutorFlow.startFlow("core1400100002Flow", in);
   }
}
