package com.dcits.ensemble.rb.service.bs.spec;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1400100008;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100008In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100008Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400100008 implements ICore1400100008 {
   private static final Logger log = LoggerFactory.getLogger(Core1400100008.class);

   @CometMapping(
      value = "/rb/inq/agreement/corpacct",
      name = "智能通知存款账户信息查询",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "100008"
   )
   public Core1400100008Out runService(@RequestBody Core1400100008In in) {
      return (Core1400100008Out)ExecutorFlow.startFlow("core1400100008Flow", in);
   }
}
