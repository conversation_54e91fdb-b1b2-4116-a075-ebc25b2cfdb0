package com.dcits.ensemble.rb.service.bs.channel.eb;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.eb.ICore1000053253;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.eb.Core1000053253In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.eb.Core1000053253Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core1000053253 implements ICore1000053253 {
   private static final Logger log = LoggerFactory.getLogger(Core1000053253.class);

   @CometMapping(
      value = "/rb/fin/eb/account",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "053253",
      name = "票据统一记账"
   )
   public Core1000053253Out runService(@RequestBody Core1000053253In in) {
      return (Core1000053253Out)ExecutorFlow.startGravity(in, Core1000053253Out.class);
   }
}
