package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore1000053102;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1000053102In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1000053102Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1000053102 implements ICore1000053102 {
   private static final Logger log = LoggerFactory.getLogger(Core1000053102.class);

   @CometMapping(
      value = "/rb/fin/channel/finsys/collect",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "053102",
      name = "财务系统收款"
   )
   public Core1000053102Out runService(@RequestBody Core1000053102In in) {
      return (Core1000053102Out)ExecutorFlow.startFlow("core1000053102Flow", in, Core1000053102Out.class);
   }
}
