package com.dcits.ensemble.rb.service.bs.res;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000165In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000165Out;
import com.dcits.ensemble.rb.api.model.mbsdcore.ICore12000165;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000165 implements ICore12000165 {
   private static final Logger log = LoggerFactory.getLogger(Core12000165.class);

   @CometMapping(
      value = "/rb/nfin/channel/control",
      name = "渠道级限制维护",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0165"
   )
   public Core12000165Out runService(@RequestBody Core12000165In in) {
      return (Core12000165Out)ExecutorFlow.startFlow("core12000165Flow", in, Core12000165Out.class);
   }
}
