package com.dcits.ensemble.rb.service.bs.agr.mainagr;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore1200029181;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1200029181In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1200029181Out;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
@Service
public class Core1200029181 implements ICore1200029181 {
   @CometMapping(
      value = "/rb/nfin/channel/agreement/common/signtwo",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "029181",
      name = "统一签约"
   )
   public Core1200029181Out runService(@RequestBody Core1200029181In in) {
      return (Core1200029181Out)ExecutorFlow.startGravity(in, Core1200029181Out.class);
   }
}
