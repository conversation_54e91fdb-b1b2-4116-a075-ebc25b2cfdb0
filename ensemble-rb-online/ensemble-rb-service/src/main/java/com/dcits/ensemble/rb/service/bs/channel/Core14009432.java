package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore14009432;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core14009432In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core14009432Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core14009432 implements ICore14009432 {
   private static final Logger log = LoggerFactory.getLogger(Core14009432.class);

   @CometMapping(
      value = "/rb/inq/dc/interest/rate",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "9432",
      name = "大额存单利率信息查询"
   )
   public Core14009432Out runService(@RequestBody Core14009432In in) {
      return (Core14009432Out)ExecutorFlow.startFlow("core14009432Flow", in, Core14009432Out.class);
   }
}
