package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore1200058002;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1200058002In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1200058002Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core1200058002 implements ICore1200058002 {
   private static final Logger log = LoggerFactory.getLogger(Core1200058002.class);

   @CometMapping(
      value = "/rb/nfin/channel/password/rec",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "058002",
      name = "回单密码维护"
   )
   public Core1200058002Out runService(@RequestBody Core1200058002In in) {
      return (Core1200058002Out)ExecutorFlow.startGravity(in, Core1200058002Out.class);
   }
}
