package com.dcits.ensemble.rb.service.bs.spec;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore10000118;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10000118In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10000118Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core10000118 implements ICore10000118 {
   private static final Logger log = LoggerFactory.getLogger(Core10000118.class);

   @CometMapping(
      value = "/rb/fin/zxqy/turnback",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "0118",
      name = "坐享其盈手工转回"
   )
   public Core10000118Out runService(@RequestBody Core10000118In in) {
      return (Core10000118Out)ExecutorFlow.startFlow("core10000118Flow", in, Core10000118Out.class);
   }
}
