package com.dcits.ensemble.rb.service.bs.tae;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.service.util.IAsynCretCheck;
import com.dcits.ensemble.rb.service.util.tae.AsynFinancialIn;
import com.dcits.ensemble.rb.service.util.tae.AsynFinancialOut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
@Service
public class AsynCretCheckImpl implements IAsynCretCheck {
   private static final Logger log = LoggerFactory.getLogger(AsynCretCheckImpl.class);

   @CometMapping(
      value = "/rb/fin/asyn/cret/check",
      serviceCode = "MbsdCore",
      messageType = "1500",
      messageCode = "0102",
      name = "存款异步记账贷方检查"
   )
   public AsynFinancialOut runService(@RequestBody AsynFinancialIn in) {
      return (AsynFinancialOut)ExecutorFlow.startFlow("asynCretCheckFlow", in, AsynFinancialOut.class);
   }
}
