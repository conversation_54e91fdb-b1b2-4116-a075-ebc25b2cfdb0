package com.dcits.ensemble.rb.service.bs.channel.feature;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.feature.ICore14000413;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.feature.Core14000413In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.feature.Core14000413Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000413 implements ICore14000413 {
   private static final Logger log = LoggerFactory.getLogger(Core14000413.class);

   @CometMapping(
      value = "/rb/inq/agreement/wdl/query",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0413",
      name = "稳得利系列签约查询"
   )
   public Core14000413Out runService(@RequestBody Core14000413In in) {
      return (Core14000413Out)ExecutorFlow.startFlow("core14000413Flow", in, Core14000413Out.class);
   }
}
