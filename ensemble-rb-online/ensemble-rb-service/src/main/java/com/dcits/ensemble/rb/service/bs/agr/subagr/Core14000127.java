package com.dcits.ensemble.rb.service.bs.agr.subagr;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore14000127;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000127In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000127Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000127 implements ICore14000127 {
   private static final Logger log = LoggerFactory.getLogger(Core14000127.class);

   @CometMapping(
      value = "/rb/nfin/msa/agreement/query",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0127",
      name = "MSA账户定期转账协议信息查询"
   )
   public Core14000127Out runService(@RequestBody Core14000127In in) {
      return (Core14000127Out)ExecutorFlow.startFlow("core14000127Flow", in);
   }
}
