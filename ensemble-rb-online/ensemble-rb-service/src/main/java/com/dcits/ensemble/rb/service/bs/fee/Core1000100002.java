package com.dcits.ensemble.rb.service.bs.fee;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1000100002;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1000100002In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1000100002Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1000100002 implements ICore1000100002 {
   private static final Logger log = LoggerFactory.getLogger(Core1000100002.class);

   @CometMapping(
      value = "/rb/fin/fee/accr/write",
      name = "费用计提核算",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "100002"
   )
   public Core1000100002Out runService(@RequestBody Core1000100002In in) {
      return (Core1000100002Out)ExecutorFlow.startFlow("core1000100002Flow", in);
   }
}
