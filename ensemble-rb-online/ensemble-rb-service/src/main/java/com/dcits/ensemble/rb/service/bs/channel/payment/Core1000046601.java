package com.dcits.ensemble.rb.service.bs.channel.payment;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.payment.ICore1000046601;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.payment.Core1000046601In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.payment.Core1000046601Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core1000046601 implements ICore1000046601 {
   private static final Logger log = LoggerFactory.getLogger(Core1000046601.class);

   @CometMapping(
      value = "/rb/fin/channel/payment/account",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "046601",
      name = "支付系统记账专用"
   )
   public Core1000046601Out runService(@RequestBody Core1000046601In in) {
      return (Core1000046601Out)ExecutorFlow.startGravity(in, Core1000046601Out.class);
   }
}
