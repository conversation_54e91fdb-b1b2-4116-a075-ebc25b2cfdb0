package com.dcits.ensemble.rb.service.bs.pcp;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.pcp.ICore12000705;
import com.dcits.ensemble.rb.api.model.mbsdcore.pcp.Core12000705In;
import com.dcits.ensemble.rb.api.model.mbsdcore.pcp.Core12000705Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000705 implements ICore12000705 {
   private static final Logger log = LoggerFactory.getLogger(Core12000705.class);

   @CometMapping(
      value = "/rb/nfin/pcp/cancel",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0705",
      name = "资金池解约（提交）"
   )
   public Core12000705Out runService(@RequestBody Core12000705In in) {
      return (Core12000705Out)ExecutorFlow.startGravity(in, Core12000705Out.class);
   }
}
