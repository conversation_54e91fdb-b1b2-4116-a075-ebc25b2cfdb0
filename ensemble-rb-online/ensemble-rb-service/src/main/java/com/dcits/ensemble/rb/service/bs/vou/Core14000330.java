package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000330;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000330In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000330Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000330 implements ICore14000330 {
   private static final Logger log = LoggerFactory.getLogger(Core14000330.class);

   @CometMapping(
      value = "/rb/inq/voucher/status/no",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0330",
      name = "凭证账户状态查询"
   )
   public Core14000330Out runService(@RequestBody Core14000330In in) {
      return (Core14000330Out)ExecutorFlow.startFlow("core14000330Flow", in, Core14000330Out.class);
   }
}
