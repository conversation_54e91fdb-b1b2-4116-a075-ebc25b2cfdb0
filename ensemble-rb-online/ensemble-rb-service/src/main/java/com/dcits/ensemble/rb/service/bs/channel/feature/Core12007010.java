package com.dcits.ensemble.rb.service.bs.channel.feature;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.payment.ICore12007010;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.feature.Core12007010In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.feature.Core12007010Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core12007010 implements ICore12007010 {
   private static final Logger log = LoggerFactory.getLogger(Core12007010.class);

   @CometMapping(
      value = "/rb/nfin/agreement/jdl",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "7010",
      name = "加多利签约/维护"
   )
   public Core12007010Out runService(@RequestBody Core12007010In in) {
      return (Core12007010Out)ExecutorFlow.startFlow("core12007010Flow", in, Core12007010Out.class);
   }
}
