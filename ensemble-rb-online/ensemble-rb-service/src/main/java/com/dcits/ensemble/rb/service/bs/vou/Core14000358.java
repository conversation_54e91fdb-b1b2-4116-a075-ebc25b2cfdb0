package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000358;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000358In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000358Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000358 implements ICore14000358 {
   private static final Logger log = LoggerFactory.getLogger(Core14000358.class);

   @CometMapping(
      value = "/rb/inq/cheque/refund",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0358",
      name = "支票退票查询"
   )
   public Core14000358Out runService(@RequestBody Core14000358In in) {
      return (Core14000358Out)ExecutorFlow.startFlow("core14000358Flow", in, Core14000358Out.class);
   }
}
