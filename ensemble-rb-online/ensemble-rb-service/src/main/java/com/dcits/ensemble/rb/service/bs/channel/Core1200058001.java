package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore1200058001;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1200058001In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1200058001Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core1200058001 implements ICore1200058001 {
   private static final Logger log = LoggerFactory.getLogger(Core1200058001.class);

   @CometMapping(
      value = "/rb/nfin/channel/agreement/rec",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "058001",
      name = "回单签约"
   )
   public Core1200058001Out runService(@RequestBody Core1200058001In in) {
      return (Core1200058001Out)ExecutorFlow.startGravity(in, Core1200058001Out.class);
   }
}
