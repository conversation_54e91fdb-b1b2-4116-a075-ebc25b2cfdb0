package com.dcits.ensemble.rb.service.bs.pcp;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.pcp.ICore12000707;
import com.dcits.ensemble.rb.api.model.mbsdcore.pcp.Core12000707In;
import com.dcits.ensemble.rb.api.model.mbsdcore.pcp.Core12000707Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000707 implements ICore12000707 {
   private static final Logger log = LoggerFactory.getLogger(Core12000707.class);

   @CometMapping(
      value = "/rb/nfin/pcp/deal",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0707",
      name = "资金池综合处理"
   )
   public Core12000707Out runService(@RequestBody Core12000707In in) {
      return (Core12000707Out)ExecutorFlow.startGravity(in, Core12000707Out.class);
   }
}
