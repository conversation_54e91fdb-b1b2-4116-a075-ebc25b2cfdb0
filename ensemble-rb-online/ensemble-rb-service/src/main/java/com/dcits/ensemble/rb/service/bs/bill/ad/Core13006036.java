package com.dcits.ensemble.rb.service.bs.bill.ad;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ad.ICore13006036;
import com.dcits.ensemble.rb.api.model.mbsdcore.ad.Core13006036In;
import com.dcits.ensemble.rb.api.model.mbsdcore.ad.Core13006036Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core13006036 implements ICore13006036 {
   private static final Logger log = LoggerFactory.getLogger(Core13006036.class);

   @CometMapping(
      value = "/rb/rev/advances/cancel",
      serviceCode = "MbsdCore",
      messageType = "1300",
      messageCode = "6036",
      name = "垫款冲销"
   )
   public Core13006036Out runService(@RequestBody Core13006036In in) {
      return (Core13006036Out)ExecutorFlow.startGravity(in, Core13006036Out.class);
   }
}
