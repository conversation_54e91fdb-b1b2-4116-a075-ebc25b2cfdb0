package com.dcits.ensemble.rb.service.bs.bill.pn;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1400023403;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400023403In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400023403Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400023403 implements ICore1400023403 {
   private static final Logger log = LoggerFactory.getLogger(Core1400023403.class);

   @CometMapping(
      value = "/rb/inq/bill/unappr",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "023403",
      name = "未复核本汇票签发查询"
   )
   public Core1400023403Out runService(@RequestBody Core1400023403In in) {
      return (Core1400023403Out)ExecutorFlow.startFlow("core1400023403Flow", in, Core1400023403Out.class);
   }
}
