package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore14003001;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core14003001In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core14003001Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core14003001 implements ICore14003001 {
   private static final Logger log = LoggerFactory.getLogger(Core14003001.class);

   @CometMapping(
      value = "/rb/inq/tran/status",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "3001",
      name = "交易状态查询"
   )
   public Core14003001Out runService(@RequestBody Core14003001In in) {
      return (Core14003001Out)ExecutorFlow.startFlow("core14003001Flow", in, Core14003001Out.class);
   }
}
