package com.dcits.ensemble.rb.service.bs.bill.pn;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1200023401;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1200023401In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1200023401Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1200023401 implements ICore1200023401 {
   private static final Logger log = LoggerFactory.getLogger(Core1200023401.class);

   @CometMapping(
      value = "/rb/nfin/bill/issueAppr",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "023401",
      name = "本汇票签发复核"
   )
   public Core1200023401Out runService(@RequestBody Core1200023401In in) {
      return (Core1200023401Out)ExecutorFlow.startFlow("core1200023401Flow", in, Core1200023401Out.class);
   }
}
