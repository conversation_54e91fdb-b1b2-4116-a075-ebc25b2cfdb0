package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore14000359;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000359In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000359Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000359 implements ICore14000359 {
   private static final Logger log = LoggerFactory.getLogger(Core14000359.class);

   @CometMapping(
      value = "/rb/inq/cheque/defense",
      name = "支票协防查询"
   )
   public Core14000359Out runService(@RequestBody Core14000359In in) {
      return (Core14000359Out)ExecutorFlow.startFlow("core14000359Flow", in);
   }
}
