package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore1400050115;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1400050115In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1400050115Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core1400050115 implements ICore1400050115 {
   private static final Logger log = LoggerFactory.getLogger(Core1400050115.class);

   @CometMapping(
      value = "/rb/inq/channel/tran/amount",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "050115",
      name = "非柜面交易金额查询"
   )
   public Core1400050115Out runService(@RequestBody Core1400050115In in) {
      return (Core1400050115Out)ExecutorFlow.startFlow("core1400050115Flow", in, Core1400050115Out.class);
   }
}
