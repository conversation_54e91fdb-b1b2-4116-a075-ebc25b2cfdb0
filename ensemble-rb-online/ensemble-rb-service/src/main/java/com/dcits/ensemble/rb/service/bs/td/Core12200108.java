package com.dcits.ensemble.rb.service.bs.td;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12200108;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12200108In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12200108Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12200108 implements ICore12200108 {
   private static final Logger log = LoggerFactory.getLogger(Core12200108.class);

   @CometMapping(
      value = "/rb/file/term/batch/open",
      serviceCode = "MbsdCore",
      messageType = "1220",
      messageCode = "0108",
      name = "批量开立存单"
   )
   public Core12200108Out runService(@RequestBody Core12200108In in) {
      return (Core12200108Out)ExecutorFlow.startGravity(in, Core12200108Out.class);
   }
}
