package com.dcits.ensemble.rb.service.bs.spec;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1400100031;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100031In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100031Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400100031 implements ICore1400100031 {
   private static final Logger log = LoggerFactory.getLogger(Core1400100031.class);

   @CometMapping(
      value = "/rb/inq/agreement/xald",
      name = "兴安利得查询",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "100031"
   )
   public Core1400100031Out runService(@RequestBody Core1400100031In in) {
      return (Core1400100031Out)ExecutorFlow.startFlow("core1400100031Flow", in);
   }
}
