package com.dcits.ensemble.rb.service.bs.fee;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1200100002;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1200100002In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1200100002Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1200100002 implements ICore1200100002 {
   private static final Logger log = LoggerFactory.getLogger(Core1200100002.class);

   @CometMapping(
      value = "/rb/nfin/fee/accr",
      name = "费用计提处理",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "100002"
   )
   public Core1200100002Out runService(@RequestBody Core1200100002In in) {
      return (Core1200100002Out)ExecutorFlow.startFlow("core1200100002Flow", in);
   }
}
