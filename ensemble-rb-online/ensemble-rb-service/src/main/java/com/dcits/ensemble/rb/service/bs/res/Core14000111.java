package com.dcits.ensemble.rb.service.bs.res;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000111;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000111In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000111Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000111 implements ICore14000111 {
   private static final Logger log = LoggerFactory.getLogger(Core14000111.class);

   @CometMapping(
      value = "/rb/inq/acct/restraint",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0111",
      name = "账户限制查询"
   )
   public Core14000111Out runService(@RequestBody Core14000111In in) {
      return (Core14000111Out)ExecutorFlow.startFlow("core14000111Flow", in, Core14000111Out.class);
   }
}
