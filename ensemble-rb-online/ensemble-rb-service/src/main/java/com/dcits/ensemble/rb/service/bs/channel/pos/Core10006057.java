package com.dcits.ensemble.rb.service.bs.channel.pos;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.pos.ICore10006057;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core10006057In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core10006057Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core10006057 implements ICore10006057 {
   private static final Logger log = LoggerFactory.getLogger(Core10006057.class);

   @CometMapping(
      value = "/rb/fin/channel/pos/consume",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "6057",
      name = "pos消费"
   )
   public Core10006057Out runService(@RequestBody Core10006057In in) {
      return (Core10006057Out)ExecutorFlow.startFlow("core10006057Flow", in, Core10006057Out.class);
   }
}
