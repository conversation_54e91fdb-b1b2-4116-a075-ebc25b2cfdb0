package com.dcits.ensemble.rb.service.bs.channel.ic;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ic.ICore1000056201;
import com.dcits.ensemble.rb.api.model.mbsdcore.ic.Core1000056201In;
import com.dcits.ensemble.rb.api.model.mbsdcore.ic.Core1000056201Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1000056201 implements ICore1000056201 {
   private static final Logger log = LoggerFactory.getLogger(Core1000056201.class);

   @CometMapping(
      value = "/rb/fin/channel/iccard/transf",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "056201",
      name = "电子现金圈存/圈提"
   )
   public Core1000056201Out runService(@RequestBody Core1000056201In in) {
      return (Core1000056201Out)ExecutorFlow.startFlow("core1000056201Flow", in, Core1000056201Out.class);
   }
}
