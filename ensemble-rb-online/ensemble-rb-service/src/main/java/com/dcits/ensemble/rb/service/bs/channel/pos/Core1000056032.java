package com.dcits.ensemble.rb.service.bs.channel.pos;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.pos.ICore1000056032;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core1000056032In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core1000056032Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core1000056032 implements ICore1000056032 {
   private static final Logger log = LoggerFactory.getLogger(Core1000056032.class);

   @CometMapping(
      value = "/rb/fin/otheragent/deposit/revert",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "056032",
      name = "本代他现金充值冲正"
   )
   public Core1000056032Out runService(@RequestBody Core1000056032In in) {
      return (Core1000056032Out)ExecutorFlow.startFlow("core1000056032Flow", in);
   }
}
