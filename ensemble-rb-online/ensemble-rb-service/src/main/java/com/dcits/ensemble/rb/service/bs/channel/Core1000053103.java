package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore1000053103;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1000053103In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1000053103Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1000053103 implements ICore1000053103 {
   private static final Logger log = LoggerFactory.getLogger(Core1000053103.class);

   @CometMapping(
      value = "/rb/fin/channel/finsys/batch/operate",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "053103",
      name = "财务系统批量下拨上收交易"
   )
   public Core1000053103Out runService(@RequestBody Core1000053103In in) {
      return (Core1000053103Out)ExecutorFlow.startGravity(in, Core1000053103Out.class);
   }
}
