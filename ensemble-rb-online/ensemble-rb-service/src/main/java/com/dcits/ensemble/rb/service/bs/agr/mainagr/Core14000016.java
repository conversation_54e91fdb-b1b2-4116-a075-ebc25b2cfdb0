package com.dcits.ensemble.rb.service.bs.agr.mainagr;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000016;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000016In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000016Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000016 implements ICore14000016 {
   private static final Logger log = LoggerFactory.getLogger(Core14000016.class);

   @CometMapping(
      value = "/rb/inq/acct/close/agreement",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0016",
      name = "销户账户协议查询"
   )
   public Core14000016Out runService(@RequestBody Core14000016In in) {
      return (Core14000016Out)ExecutorFlow.startFlow("core14000016Flow", in, Core14000016Out.class);
   }
}
