package com.dcits.ensemble.rb.service.bs.bill.pn;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1400023404;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400023404In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400023404Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400023404 implements ICore1400023404 {
   private static final Logger log = LoggerFactory.getLogger(Core1400023404.class);

   @CometMapping(
      value = "/rb/inq/bill/lost",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "023404",
      name = "本汇票挂失解挂查询"
   )
   public Core1400023404Out runService(@RequestBody Core1400023404In in) {
      return (Core1400023404Out)ExecutorFlow.startFlow("core1400023404Flow", in, Core1400023404Out.class);
   }
}
