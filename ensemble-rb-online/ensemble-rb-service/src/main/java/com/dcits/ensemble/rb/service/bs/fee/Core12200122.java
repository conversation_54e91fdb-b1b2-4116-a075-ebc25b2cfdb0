package com.dcits.ensemble.rb.service.bs.fee;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12200122;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12200122In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12200122Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core12200122 implements ICore12200122 {
   private static final Logger log = LoggerFactory.getLogger(Core12200122.class);

   @CometMapping(
      value = "/rb/file/fee/batch/collect",
      serviceCode = "MbsdCore",
      messageType = "1220",
      messageCode = "0122",
      name = "批量手续费收取"
   )
   public Core12200122Out runService(@RequestBody Core12200122In in) {
      return (Core12200122Out)ExecutorFlow.startFlow("core12200122Flow", in, Core12200122Out.class);
   }
}
