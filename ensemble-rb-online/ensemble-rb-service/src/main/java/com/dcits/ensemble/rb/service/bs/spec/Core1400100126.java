package com.dcits.ensemble.rb.service.bs.spec;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1400100126;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100126In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100126Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400100126 implements ICore1400100126 {
   private static final Logger log = LoggerFactory.getLogger(Core1400100126.class);

   @CometMapping(
      value = "/rb/inq/agreement/dept",
      name = "理财签约提前支取查询",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "100126"
   )
   public Core1400100126Out runService(@RequestBody Core1400100126In in) {
      return (Core1400100126Out)ExecutorFlow.startFlow("core1400100126Flow", in);
   }
}
