package com.dcits.ensemble.rb.service.bs.bill.pn;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1200023402;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1200023402In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1200023402Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1200023402 implements ICore1200023402 {
   private static final Logger log = LoggerFactory.getLogger(Core1200023402.class);

   @CometMapping(
      value = "/rb/nfin/bill/reprint",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "023402",
      name = "银行本票签发补打"
   )
   public Core1200023402Out runService(@RequestBody Core1200023402In in) {
      return (Core1200023402Out)ExecutorFlow.startFlow("core1200023402Flow", in, Core1200023402Out.class);
   }
}
