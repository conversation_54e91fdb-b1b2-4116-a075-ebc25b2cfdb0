package com.dcits.ensemble.rb.service.bs.finTcc;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.service.util.tae.AsynFinancialIn;
import com.dcits.ensemble.rb.service.util.tae.AsynFinancialOut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@CometProvider
public class DebtCancle implements IDebtCancel {
   private static final Logger log = LoggerFactory.getLogger(DebtCancle.class);

   @CometMapping(
      value = "/rb/fin/debt/cancel",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "0805"
   )
   public AsynFinancialOut runService(AsynFinancialIn in) {
      return (AsynFinancialOut)ExecutorFlow.startFlow("debtCancelTccFlow", in, AsynFinancialOut.class);
   }
}
