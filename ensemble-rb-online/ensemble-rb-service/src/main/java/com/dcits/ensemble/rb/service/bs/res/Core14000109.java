package com.dcits.ensemble.rb.service.bs.res;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.dc.ICore14000109;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000109In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000109Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000109 implements ICore14000109 {
   private static final Logger log = LoggerFactory.getLogger(Core14000109.class);

   @CometMapping(
      value = "/rb/inq/acct/allfrozen/query",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0109",
      name = "司法冻结/解冻信息查询"
   )
   public Core14000109Out runService(@RequestBody Core14000109In in) {
      return (Core14000109Out)ExecutorFlow.startFlow("core14000109Flow", in, Core14000109Out.class);
   }
}
