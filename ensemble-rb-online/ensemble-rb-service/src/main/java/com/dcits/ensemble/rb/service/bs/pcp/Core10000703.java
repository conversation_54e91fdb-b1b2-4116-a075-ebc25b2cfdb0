package com.dcits.ensemble.rb.service.bs.pcp;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.pcp.ICore10000703;
import com.dcits.ensemble.rb.api.model.mbsdcore.pcp.Core10000703In;
import com.dcits.ensemble.rb.api.model.mbsdcore.pcp.Core10000703Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core10000703 implements ICore10000703 {
   private static final Logger log = LoggerFactory.getLogger(Core10000703.class);

   @CometMapping(
      value = "/rb/fin/pcp/manual/interest",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "0703",
      name = "资金池手工计价"
   )
   public Core10000703Out runService(@RequestBody Core10000703In in) {
      return (Core10000703Out)ExecutorFlow.startGravity(in, Core10000703Out.class);
   }
}
