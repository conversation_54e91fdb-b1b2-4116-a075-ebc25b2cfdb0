package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000113;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000113In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000113Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000113 implements ICore14000113 {
   private static final Logger log = LoggerFactory.getLogger(Core14000113.class);

   @CometMapping(
      value = "/rb/inq/voucher/single",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0113",
      name = "凭证信息查询"
   )
   public Core14000113Out runService(@RequestBody Core14000113In in) {
      return (Core14000113Out)ExecutorFlow.startFlow("core14000113Flow", in, Core14000113Out.class);
   }
}
