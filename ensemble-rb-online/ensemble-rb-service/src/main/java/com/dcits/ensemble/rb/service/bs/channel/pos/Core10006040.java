package com.dcits.ensemble.rb.service.bs.channel.pos;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.pos.ICore10006040;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core10006040In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core10006040Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core10006040 implements ICore10006040 {
   private static final Logger log = LoggerFactory.getLogger(Core10006040.class);

   @CometMapping(
      value = "/rb/fin/channel/onbehalf/collect/cancel",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "6040",
      name = "银联代收撤销"
   )
   public Core10006040Out runService(@RequestBody Core10006040In in) {
      return (Core10006040Out)ExecutorFlow.startFlow("core10006040Flow", in, Core10006040Out.class);
   }
}
