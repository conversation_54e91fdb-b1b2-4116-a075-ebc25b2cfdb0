package com.dcits.ensemble.rb.service.bs.spec;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore14000139;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000139In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000139Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000139 implements ICore14000139 {
   private static final Logger log = LoggerFactory.getLogger(Core14000139.class);

   @CometMapping(
      value = "/rb/inq/interest/cycled",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0139",
      name = "坐享其赢理财转回查询"
   )
   public Core14000139Out runService(@RequestBody Core14000139In in) {
      return (Core14000139Out)ExecutorFlow.startFlow("core14000139Flow", in, Core14000139Out.class);
   }
}
