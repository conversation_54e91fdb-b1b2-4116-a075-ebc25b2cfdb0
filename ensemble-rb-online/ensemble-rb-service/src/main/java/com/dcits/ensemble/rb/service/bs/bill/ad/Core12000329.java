package com.dcits.ensemble.rb.service.bs.bill.ad;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ad.ICore12000329;
import com.dcits.ensemble.rb.api.model.mbsdcore.ad.Core12000329In;
import com.dcits.ensemble.rb.api.model.mbsdcore.ad.Core12000329Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000329 implements ICore12000329 {
   private static final Logger log = LoggerFactory.getLogger(Core12000329.class);

   @CometMapping(
      value = "/rb/fin/bab/standby/tray",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0329",
      name = "银行承兑汇票备款试算"
   )
   public Core12000329Out runService(@RequestBody Core12000329In in) {
      return (Core12000329Out)ExecutorFlow.startGravity(in, Core12000329Out.class);
   }
}
