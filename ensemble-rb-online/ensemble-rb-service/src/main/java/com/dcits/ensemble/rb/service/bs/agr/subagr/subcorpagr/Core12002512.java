package com.dcits.ensemble.rb.service.bs.agr.subagr.subcorpagr;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore12002512;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12002512In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12002512Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12002512 implements ICore12002512 {
   private static final Logger log = LoggerFactory.getLogger(Core12002512.class);

   @CometMapping(
      value = "/rb/nfin/agreement/lye/qyjy",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "2512",
      name = "财政零余额签约/解约"
   )
   public Core12002512Out runService(@RequestBody Core12002512In in) {
      return (Core12002512Out)ExecutorFlow.startGravity(in);
   }
}
