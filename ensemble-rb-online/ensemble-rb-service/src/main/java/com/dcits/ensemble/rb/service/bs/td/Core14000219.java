package com.dcits.ensemble.rb.service.bs.td;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore14000219;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000219In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000219Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000219 implements ICore14000219 {
   private static final Logger log = LoggerFactory.getLogger(Core14000219.class);

   @CometMapping(
      value = "/rb/inq/term/hang/detail",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0219",
      name = "定期开户资金来源挂销账明细查询"
   )
   public Core14000219Out runService(@RequestBody Core14000219In in) {
      return (Core14000219Out)ExecutorFlow.startFlow("core14000219Flow", in);
   }
}
