package com.dcits.ensemble.rb.service.bs.channel.pos;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.pos.ICore10006032;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core10006032In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core10006032Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core10006032 implements ICore10006032 {
   private static final Logger log = LoggerFactory.getLogger(Core10006032.class);

   @CometMapping(
      value = "/rb/fin/channel/otheragent/deposit/revoke",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "6032",
      name = "他代本存款撤销"
   )
   public Core10006032Out runService(@RequestBody Core10006032In in) {
      return (Core10006032Out)ExecutorFlow.startFlow("core10006032Flow", in, Core10006032Out.class);
   }
}
