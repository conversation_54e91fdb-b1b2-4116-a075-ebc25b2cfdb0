package com.dcits.ensemble.rb.service.bs.td;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12000202;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000202In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000202Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000202 implements ICore12000202 {
   private static final Logger log = LoggerFactory.getLogger(Core12000202.class);

   @CometMapping(
      value = "/rb/nfin/term/sub/open",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0202",
      name = "定期存单开户"
   )
   public Core12000202Out runService(@RequestBody Core12000202In in) {
      return (Core12000202Out)ExecutorFlow.startFlow("core12000202Flow", in, Core12000202Out.class);
   }
}
