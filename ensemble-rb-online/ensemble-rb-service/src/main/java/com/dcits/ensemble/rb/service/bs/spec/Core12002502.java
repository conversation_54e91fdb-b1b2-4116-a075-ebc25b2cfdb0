package com.dcits.ensemble.rb.service.bs.spec;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore12002502;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12002502In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12002502Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12002502 implements ICore12002502 {
   private static final Logger log = LoggerFactory.getLogger(Core12002502.class);

   @CometMapping(
      value = "/rb/nfin/asyn/updateloaninfo",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "2502",
      name = "更新贷款合同可透支额度"
   )
   public Core12002502Out runService(@RequestBody Core12002502In core12002502In) {
      return (Core12002502Out)ExecutorFlow.startFlow("core12002502Flow", core12002502In, Core12002502Out.class);
   }
}
