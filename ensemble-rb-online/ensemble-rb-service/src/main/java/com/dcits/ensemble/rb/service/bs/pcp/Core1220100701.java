package com.dcits.ensemble.rb.service.bs.pcp;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1220100701;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1220100701In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1220100701Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1220100701 implements ICore1220100701 {
   private static final Logger log = LoggerFactory.getLogger(Core1220100701.class);

   @CometMapping(
      value = "/rb/fin/pcp/file/impute",
      name = "资金池手工批量归集",
      serviceCode = "MbsdCore",
      messageType = "1220",
      messageCode = "100701"
   )
   public Core1220100701Out runService(@RequestBody Core1220100701In in) {
      return (Core1220100701Out)ExecutorFlow.startGravity(in);
   }
}
