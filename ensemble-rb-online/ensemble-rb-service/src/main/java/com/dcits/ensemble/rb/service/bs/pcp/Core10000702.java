package com.dcits.ensemble.rb.service.bs.pcp;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.pcp.ICore10000702;
import com.dcits.ensemble.rb.api.model.mbsdcore.pcp.Core10000702In;
import com.dcits.ensemble.rb.api.model.mbsdcore.pcp.Core10000702Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core10000702 implements ICore10000702 {
   private static final Logger log = LoggerFactory.getLogger(Core10000702.class);

   @CometMapping(
      value = "/rb/fin/pcp/manual/allocate",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "0702",
      name = "手动下拨"
   )
   public Core10000702Out runService(@RequestBody Core10000702In in) {
      return (Core10000702Out)ExecutorFlow.startGravity(in, Core10000702Out.class);
   }
}
