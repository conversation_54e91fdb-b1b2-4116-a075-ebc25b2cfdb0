package com.dcits.ensemble.rb.service.bs.fee;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14006002;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14006002In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14006002Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14006002 implements ICore14006002 {
   private static final Logger log = LoggerFactory.getLogger(Core14006002.class);

   @CometMapping(
      value = "/rb/inq/amortize/agreement",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "6002",
      name = "预收手续费合约查询"
   )
   public Core14006002Out runService(@RequestBody Core14006002In in) {
      return (Core14006002Out)ExecutorFlow.startFlow("core14006002Flow", in, Core14006002Out.class);
   }
}
