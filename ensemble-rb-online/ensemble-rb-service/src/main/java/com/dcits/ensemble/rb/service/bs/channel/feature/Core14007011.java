package com.dcits.ensemble.rb.service.bs.channel.feature;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.feature.ICore14007011;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.feature.Core14007011In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.feature.Core14007011Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14007011 implements ICore14007011 {
   private static final Logger log = LoggerFactory.getLogger(Core14007011.class);

   @CometMapping(
      value = "/rb/inq/agreement/wdl/tranhist/query",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "7011",
      name = "稳得利系列交易历史明细查询"
   )
   public Core14007011Out runService(@RequestBody Core14007011In in) {
      return (Core14007011Out)ExecutorFlow.startFlow("core14007011Flow", in, Core14007011Out.class);
   }
}
