package com.dcits.ensemble.rb.service.bs.channel.pos;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.pos.ICore10006011;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core10006011In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core10006011Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core10006011 implements ICore10006011 {
   private static final Logger log = LoggerFactory.getLogger(Core10006011.class);

   @CometMapping(
      value = "/rb/fin/channel/otheragent/wtd",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "6011",
      name = "银联他代本取款"
   )
   public Core10006011Out runService(@RequestBody Core10006011In in) {
      return (Core10006011Out)ExecutorFlow.startFlow("core10006011Flow", in, Core10006011Out.class);
   }
}
