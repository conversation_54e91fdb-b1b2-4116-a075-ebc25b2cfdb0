package com.dcits.ensemble.rb.service.bs.res;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12000103;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000103In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000103Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core12000103 implements ICore12000103 {
   private static final Logger log = LoggerFactory.getLogger(Core12000103.class);

   @CometMapping(
      value = "/rb/nfin/acct/restraint",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0103",
      name = "账户限制"
   )
   public Core12000103Out runService(@RequestBody Core12000103In in) {
      return (Core12000103Out)ExecutorFlow.startFlow("core12000103Flow", in, Core12000103Out.class);
   }
}
