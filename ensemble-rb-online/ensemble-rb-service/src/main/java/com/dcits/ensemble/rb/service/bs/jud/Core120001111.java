package com.dcits.ensemble.rb.service.bs.jud;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore120001111;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core120001111In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core120001111Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core120001111 implements ICore120001111 {
   private static final Logger log = LoggerFactory.getLogger(Core120001111.class);

   @CometMapping(
      value = "/rb/nfin/impound/hanginfo/insert",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "01111",
      name = "行外扣划挂账信息录入"
   )
   public Core120001111Out runService(@RequestBody Core120001111In in) {
      return (Core120001111Out)ExecutorFlow.startGravity(in, Core120001111Out.class);
   }
}
