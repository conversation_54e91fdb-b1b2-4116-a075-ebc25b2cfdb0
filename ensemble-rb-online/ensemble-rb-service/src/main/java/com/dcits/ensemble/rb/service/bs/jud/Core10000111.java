package com.dcits.ensemble.rb.service.bs.jud;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore10000111;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10000111In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10000111Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core10000111 implements ICore10000111 {
   private static final Logger log = LoggerFactory.getLogger(Core10000111.class);

   @CometMapping(
      value = "/rb/fin/acct/impound",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "0111",
      name = "强制扣划"
   )
   public Core10000111Out runService(@RequestBody Core10000111In in) {
      return (Core10000111Out)ExecutorFlow.startGravity(in, Core10000111Out.class);
   }
}
