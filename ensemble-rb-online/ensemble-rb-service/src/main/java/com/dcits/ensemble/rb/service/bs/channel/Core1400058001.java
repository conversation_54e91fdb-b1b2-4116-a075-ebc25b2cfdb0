package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore1400058001;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1400058001In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1400058001Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core1400058001 implements ICore1400058001 {
   private static final Logger log = LoggerFactory.getLogger(Core1400058001.class);

   @CometMapping(
      value = "/rb/inq/agent/info",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "058001",
      name = "根据客户号查询经办人信息"
   )
   public Core1400058001Out runService(@RequestBody Core1400058001In in) {
      return (Core1400058001Out)ExecutorFlow.startGravity(in, Core1400058001Out.class);
   }
}
