package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12000118;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000118In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000118Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000118 implements ICore12000118 {
   private static final Logger log = LoggerFactory.getLogger(Core12000118.class);

   @CometMapping(
      value = "/rb/nfin/voucher/seal/relation",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0118",
      name = "账户印鉴卡关联"
   )
   public Core12000118Out runService(@RequestBody Core12000118In in) {
      return (Core12000118Out)ExecutorFlow.startGravity(in, Core12000118Out.class);
   }
}
