package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12000109;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000109In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000109Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000109 implements ICore12000109 {
   private static final Logger log = LoggerFactory.getLogger(Core12000109.class);

   @CometMapping(
      value = "/rb/nfin/pbk/print",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0109",
      name = "存折补登"
   )
   public Core12000109Out runService(@RequestBody Core12000109In in) {
      return (Core12000109Out)ExecutorFlow.startGravity(in, Core12000109Out.class);
   }
}
