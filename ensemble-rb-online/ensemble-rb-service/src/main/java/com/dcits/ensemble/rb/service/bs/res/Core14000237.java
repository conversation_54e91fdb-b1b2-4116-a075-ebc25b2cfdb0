package com.dcits.ensemble.rb.service.bs.res;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000237;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000237In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000237Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000237 implements ICore14000237 {
   private static final Logger log = LoggerFactory.getLogger(Core14000237.class);

   @CometMapping(
      value = "/rb/inq/acct/restraint/all",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0237",
      name = "限制解限信息查询"
   )
   public Core14000237Out runService(@RequestBody Core14000237In in) {
      return (Core14000237Out)ExecutorFlow.startFlow("core14000237Flow", in, Core14000237Out.class);
   }
}
