package com.dcits.ensemble.rb.service.bs.bill.pn;

import com.dcits.comet.commons.Context;
import com.dcits.comet.commons.utils.BeanUtil;
import com.dcits.comet.commons.utils.DateUtil;
import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.fm.api.IFmBaseStor;
import com.dcits.ensemble.fm.model.FmUser;
import com.dcits.ensemble.rb.api.bs.ICore1200033403;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1200033403In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1200033403Out;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1200033403In.Body.ServArray;
import com.dcits.ensemble.rb.business.api.component.cm.transaction.IMbAcctInfoService;
import com.dcits.ensemble.rb.business.api.component.fee.IMbFeeCharge;
import com.dcits.ensemble.rb.business.api.component.fee.MbFeeChargeFactory;
import com.dcits.ensemble.rb.business.bc.component.cm.EventCommonCheck;
import com.dcits.ensemble.rb.business.bc.component.cm.common.RbBaseAcctInfoImpl;
import com.dcits.ensemble.rb.business.bc.unit.voucher.base.api.IMbUpdate;
import com.dcits.ensemble.rb.business.common.util.FmUtil;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbAcct;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbPnLost;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbPnRegister;
import com.dcits.ensemble.rb.business.entity.dbmodel.RbPnTranDetail;
import com.dcits.ensemble.rb.business.model.cm.common.BaseModel;
import com.dcits.ensemble.rb.business.model.cm.common.RbAcctStandardModel;
import com.dcits.ensemble.rb.business.model.fee.MbChargeTypeEnum;
import com.dcits.ensemble.rb.business.model.fee.MbServChargeModel;
import com.dcits.ensemble.rb.business.model.fee.MbServDetailModel;
import com.dcits.ensemble.rb.business.model.vou.cheque.MbPnTranDetailModel;
import com.dcits.ensemble.rb.business.repository.acct.RbAcctRepository;
import com.dcits.ensemble.rb.business.repository.vou.RbPnLostRepository;
import com.dcits.ensemble.rb.business.repository.vou.RbPnRegisterRepository;
import com.dcits.ensemble.rb.business.repository.vou.RbPnTranDetailRepository;
import com.dcits.ensemble.util.BusiUtil;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1200033403 implements ICore1200033403 {
   private static final Logger log = LoggerFactory.getLogger(Core1200033403.class);
   @Resource
   private RbPnRegisterRepository mbPnRegisterInquiry;
   @Resource
   private RbAcctRepository mbAcctRepository;
   @Resource
   private RbPnLostRepository mbPnLostRepository;
   @Resource
   private IMbUpdate mbUpdate;
   @Resource
   private RbPnTranDetailRepository mbPnTranDetailHandle;
   @Resource
   private RbPnLostRepository mbPnLostInquiry;
   @Resource
   private RbPnRegisterRepository mbPnRegisterHandle;
   @Resource
   private IFmBaseStor fmBaseStor;
   @Resource
   private EventCommonCheck eventCommonCheck;
   @Resource
   private IMbAcctInfoService mbAcctInfoService;
   @Resource
   private RbBaseAcctInfoImpl rbBaseAcctInfo;

   @CometMapping(
      value = "/rb/nfin/ad/noteslost/operate",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "033403",
      name = "本票挂失解挂"
   )
   public Core1200033403Out runService(@RequestBody Core1200033403In in) {
      return (Core1200033403Out)ExecutorFlow.startGravity(in, Core1200033403Out.class);
   }

   public Core1200033403Out processIn(Core1200033403In in) {
      if (log.isInfoEnabled()) {
         log.info("start Core1200033403 process...");
      }

      Core1200033403Out out = new Core1200033403Out();
      String option = in.getBody().getBillLostUnlostOperateType();
      String lostStatus = in.getBody().getLostStatus();
      RbPnLost lost = new RbPnLost();
      String agentFlag = in.getBody().getAgentFlag();
      String agentName = in.getBody().getAgentName();
      String agentDocumentType = in.getBody().getAgentDocumentType();
      String agentDocumentNo = in.getBody().getAgentDocumentId();
      BeanUtil.copy(in.getBody(), lost);
      RbPnRegister register = this.mbPnRegisterInquiry.getRegisterByBillNo((String)null, in.getBody().getBillNo());
      register.setPayerBankCode(in.getBody().getIssueBankNo());
      RbAcctStandardModel acctStdModel = this.mbAcctInfoService.getRbAcctInfo(register.getPayerBaseAcctNo(), register.getPayerProdType(), register.getPayerAcctCcy(), register.getPayerAcctSeqNo());
      RbPnLost pnLost;
      if ("01".equals(option)) {
         if (BusiUtil.isNotNull(lost.getApplyerBaseAcctNo())) {
            RbAcctStandardModel acctStdModel2 = this.rbBaseAcctInfo.getLeadBaseAcctOrAcct(lost.getApplyerBaseAcctNo());
            if (BusiUtil.isNotNull(acctStdModel2)) {
               this.eventCommonCheck.checkClientExpiryDate(acctStdModel2.getClientNo(), acctStdModel2.getBaseAcctNo(), acctStdModel2.getProdType(), acctStdModel2.getCcy(), acctStdModel2.getAcctSeqNo());
            }
         }

         if (BusiUtil.isNotNull(lost.getPayeeAcctNo())) {
            RbAcct rbAcct1 = this.mbAcctRepository.getMbLeadAcct(lost.getPayeeAcctNo(), false);
            if (BusiUtil.isNotNull(rbAcct1)) {
               this.eventCommonCheck.checkClientExpiryDate(rbAcct1.getClientNo(), rbAcct1.getBaseAcctNo(), rbAcct1.getProdType(), rbAcct1.getAcctCcy(), rbAcct1.getAcctSeqNo());
            }
         }

         if (BusiUtil.isNotNull(lost.getLostDocumentType()) && BusiUtil.isNotNull(lost.getLostDocumentId())) {
            FmUser fmUser = FmUtil.getFmUser(Context.getInstance().getUserId());
            if (BusiUtil.isEquals(lost.getAgentDocumentType(), fmUser.getDocumentType()) && BusiUtil.isEquals(lost.getLostDocumentId(), fmUser.getDocumentId())) {
               throw BusiUtil.createBusinessException("RB5040");
            }
         }

         pnLost = this.mbPnLostInquiry.getMbPnLostByBillNo(in.getBody().getBillNo(), "A");
         if (BusiUtil.isNotNull(pnLost) && "1".equals(pnLost.getLossNo())) {
            throw BusiUtil.createBusinessException("RB4117");
         }

         if (BusiUtil.isNotEquals(register.getBillStatus(), "01") && BusiUtil.isNotEquals(register.getBillStatus(), "05") && BusiUtil.isNotEquals(register.getBillStatus(), "08")) {
            throw BusiUtil.createBusinessException("RB4156");
         }

         if (BusiUtil.isEquals(lostStatus, "0")) {
            register.setBillStatus("08");
         } else if (BusiUtil.isEquals(lostStatus, "1")) {
            register.setBillStatus("09");
         }

         register.setLastTranDate(DateUtil.parseDate(Context.getInstance().getRunDate()));
         this.mbPnRegisterHandle.updMbPnRegisterDb(register);
         String lostNo = in.getBody().getLossNo();
         lost.setOrigSerialNo(register.getBillApplyNo());
         lost.setTranValidFlag("A");
         lost.setLostTime(Context.getInstance().getTranDate());
         lost.setLostUserId(Context.getInstance().getUserId());
         lost.setBranch(register.getBillSignBranch());
         RbAcctStandardModel rbAcctStandardModel;
         if (BusiUtil.isEquals("1", lostStatus)) {
            rbAcctStandardModel = this.rbBaseAcctInfo.getLeadBaseAcctOrAcct(register.getPayerBaseAcctNo());
            this.mbUpdate.updateVoucher(rbAcctStandardModel, register.getDocType(), (String)null, register.getBillNo(), register.getBillNo(), "PSE", Context.getInstance().getBranchId(), acctStdModel.getInternalKey().toString(), "银行本票凭证公示催告", (String)null, (String)null);
         } else {
            rbAcctStandardModel = this.rbBaseAcctInfo.getLeadBaseAcctOrAcct(register.getPayerBaseAcctNo());
            this.mbUpdate.updateVoucher(rbAcctStandardModel, register.getDocType(), (String)null, register.getBillNo(), register.getBillNo(), "LSP", Context.getInstance().getBranchId(), acctStdModel.getInternalKey().toString(), "银行本票凭证挂失止付", (String)null, (String)null);
         }

         if (BusiUtil.isEquals(lostStatus, "0") || BusiUtil.isEquals(lostStatus, "1") && BusiUtil.isNull(lostNo)) {
            lost.setLossNo(acctStdModel.getInternalKey().toString());
            lost.setDocType(register.getDocType());
            if (BusiUtil.isNotNull(agentFlag) && agentFlag.equals("Y")) {
               lost.setAgentDocumentId(agentDocumentNo);
               lost.setAgentDocumentType(agentDocumentType);
               lost.setAgentName(agentName);
            }

            this.mbPnLostRepository.mbPnLostInsert(lost);
         }

         if (BusiUtil.isEquals(lostStatus, "1") && BusiUtil.isNotNull(lostNo)) {
            lost.setLossNo(lostNo);
            lost.setTranValidFlag("P");
            lost.setDocType(register.getDocType());
            this.mbPnLostRepository.updateMbPnLostStatus(lost);
         }

         String serialNo = this.mbPnTranDetailInsert(register, lost.getLossNo(), option);
         this.serviceFee(in, register);
         out.setLossNo(lost.getLossNo());
         out.setSerialNo(serialNo);
      } else if ("02".equals(option)) {
         if (BusiUtil.isNotNull(lost.getUnlostDocumentType()) && BusiUtil.isNotNull(lost.getUnlostDocumentId())) {
            List<FmUser> userList = this.fmBaseStor.selectUserByDocument(lost.getUnlostDocumentType(), lost.getUnlostDocumentId());
            if (userList != null && userList.size() > 0) {
               throw BusiUtil.createBusinessException("RB5041");
            }
         }

         pnLost = this.mbPnLostInquiry.getMbPnLostBylossNo(in.getBody().getLossNo(), "A");
         if (BusiUtil.isNull(pnLost)) {
            pnLost = this.mbPnLostInquiry.getMbPnLostBylossNo(in.getBody().getLossNo(), "P");
         }

         if (BusiUtil.isNull(pnLost)) {
            throw BusiUtil.createBusinessException("RB4116");
         }

         RbAcctStandardModel acctStdModel2;
         if (BusiUtil.isNotNull(pnLost.getApplyerBaseAcctNo())) {
            acctStdModel2 = this.rbBaseAcctInfo.getLeadBaseAcctOrAcct(lost.getApplyerBaseAcctNo());
            if (BusiUtil.isNotNull(acctStdModel2)) {
               this.eventCommonCheck.checkClientExpiryDate(acctStdModel2.getClientNo(), acctStdModel2.getBaseAcctNo(), acctStdModel2.getProdType(), acctStdModel2.getCcy(), acctStdModel2.getAcctSeqNo());
            }
         }

         register.setBillStatus("05");
         register.setLastTranDate(DateUtil.parseDate(Context.getInstance().getRunDate()));
         this.mbPnRegisterHandle.updMbPnRegisterDb(register);
         lost.setLossNo(pnLost.getLossNo());
         lost.setLostNo(pnLost.getLostNo());
         lost.setTranValidFlag("E");
         lost.setUnlostTime(Context.getInstance().getTranDate());
         lost.setUnlostUserId(Context.getInstance().getUserId());
         this.mbPnLostRepository.updateMbPnLostStatus(lost);
         this.mbPnTranDetailInsert(register, pnLost.getLossNo(), option);
         acctStdModel2 = this.rbBaseAcctInfo.getLeadBaseAcctOrAcct(register.getPayerBaseAcctNo());
         this.mbUpdate.updateVoucher(acctStdModel2, register.getDocType(), (String)null, register.getBillNo(), register.getBillNo(), "ACT", Context.getInstance().getBranchId(), acctStdModel.getInternalKey().toString(), "银行本票解挂", (String)null, (String)null);
      }

      if (log.isInfoEnabled()) {
         log.info("end Core1200033403 process...");
      }

      return out;
   }

   private String mbPnTranDetailInsert(RbPnRegister register, String lostNo, String option) {
      MbPnTranDetailModel mbPnTranDetailModel = new MbPnTranDetailModel();
      mbPnTranDetailModel.setOrigSerialNo(register.getBillApplyNo());
      if ("01".equals(option)) {
         if ("08".equals(register.getBillStatus())) {
            mbPnTranDetailModel.setOperType("08");
            mbPnTranDetailModel.setDealResult("挂失止付");
            mbPnTranDetailModel.setBillStatus("08");
            mbPnTranDetailModel.setAuthUserId(register.getAuthUserId());
            mbPnTranDetailModel.setIssueBankName(register.getPayerBankName());
            mbPnTranDetailModel.setSignDate(register.getBillSignDate());
            mbPnTranDetailModel.setLossNo(lostNo);
         }

         if ("09".equals(register.getBillStatus())) {
            mbPnTranDetailModel.setOperType("09");
            mbPnTranDetailModel.setDealResult("公示催告");
            mbPnTranDetailModel.setBillStatus("09");
            mbPnTranDetailModel.setAuthUserId(register.getAuthUserId());
            mbPnTranDetailModel.setIssueBankName(register.getPayerBankName());
            mbPnTranDetailModel.setLossNo(lostNo);
            mbPnTranDetailModel.setSignDate(register.getBillSignDate());
         }
      } else {
         mbPnTranDetailModel.setOperType("05");
         mbPnTranDetailModel.setDealResult("解挂");
         mbPnTranDetailModel.setBillStatus("05");
         mbPnTranDetailModel.setIssueBankName(register.getPayerBankName());
         mbPnTranDetailModel.setSignDate(register.getBillSignDate());
      }

      mbPnTranDetailModel.setBillType(register.getBillType());
      mbPnTranDetailModel.setBillNo(register.getBillNo());
      mbPnTranDetailModel.setEncryptKey(register.getBillPswd());
      mbPnTranDetailModel.setCcySign(register.getSignCcy());
      mbPnTranDetailModel.setBillAmt(register.getBillTranAmt());
      mbPnTranDetailModel.setTranferCashFlag(register.getTranferCashFlag());
      mbPnTranDetailModel.setPayerAcctNo(register.getPayerBaseAcctNo());
      mbPnTranDetailModel.setPayerName(register.getPayerAcctName());
      mbPnTranDetailModel.setPayeeAcctNo(register.getPayeeBaseAcctNo());
      mbPnTranDetailModel.setPayeeAcctName(register.getPayeeAcctName());
      mbPnTranDetailModel.setIssueBankNo(register.getPayerBankCode());
      mbPnTranDetailModel.setLossNo(lostNo);
      mbPnTranDetailModel.setTranDate(DateUtil.parseDate(Context.getInstance().getRunDate()));
      mbPnTranDetailModel.setTranBranch(Context.getInstance().getBranchId());
      mbPnTranDetailModel.setUserId(Context.getInstance().getUserId());
      mbPnTranDetailModel.setApproUserId(Context.getInstance().getApprUserId());
      mbPnTranDetailModel.setAuthUserId(Context.getInstance().getAuthUserId());
      mbPnTranDetailModel.setReference(Context.getInstance().getReference());
      mbPnTranDetailModel.setDocType(register.getDocType());
      mbPnTranDetailModel.setSerialNo(register.getSerialNo());
      RbPnTranDetail mbPnTranDetail = this.mbPnTranDetailHandle.createMbPnTranDetail(mbPnTranDetailModel);
      this.mbPnTranDetailHandle.mbPnTranDetailInsert(mbPnTranDetail);
      return mbPnTranDetail.getSerialNo();
   }

   private void serviceFee(Core1200033403In in, RbPnRegister register) {
      List<ServArray> servArray = in.getBody().getServArray();
      RbAcctStandardModel accStdModel = this.mbAcctInfoService.getRbAcctInfo(register.getPayerBaseAcctNo(), register.getPayerProdType(), register.getPayeeAcctCcy(), register.getPayerAcctSeqNo());
      IMbFeeCharge mbTranServChargeImpl = MbFeeChargeFactory.getCharge(MbChargeTypeEnum.CHARGETRAN);
      MbServChargeModel mbServChargeModel = new MbServChargeModel();
      List<MbServDetailModel> mbServDetailModels = new ArrayList();
      BeanUtil.listCopy(servArray, mbServDetailModels, MbServDetailModel.class);
      if (BusiUtil.isNotNull(mbServDetailModels)) {
         Iterator var8 = mbServDetailModels.iterator();

         while(var8.hasNext()) {
            MbServDetailModel mbServDetailModel = (MbServDetailModel)var8.next();
            mbServDetailModel.setBoClass("O");
            mbServDetailModel.setChargeToBaseAcctNo(register.getPayeeBaseAcctNo());
            mbServDetailModel.setChargeToCcy(register.getPayeeAcctCcy());
            mbServDetailModel.setChargeToProdType(register.getPayerProdType());
            mbServDetailModel.setChargeToAcctSeqNo(register.getPayerAcctSeqNo());
            mbServDetailModel.setInternalKey(accStdModel.getInternalKey());
            mbServDetailModel.setClientNo(accStdModel.getClientNo());
         }
      }

      mbServChargeModel.setMbServDetailModels(mbServDetailModels);
      mbServChargeModel.setClientNo(accStdModel.getClientNo());
      mbTranServChargeImpl.execute(mbServChargeModel, (BaseModel)null, "CHARGETRAN", (String)null);
   }
}
