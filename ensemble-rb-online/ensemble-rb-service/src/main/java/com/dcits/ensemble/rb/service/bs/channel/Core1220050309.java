package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore1220050309;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1220050309In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1220050309Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core1220050309 implements ICore1220050309 {
   private static final Logger log = LoggerFactory.getLogger(Core1220050309.class);

   @CometMapping(
      value = "/rb/file/current/online/frozen/export",
      serviceCode = "MbsdCore",
      messageType = "1220",
      messageCode = "050309",
      name = "获取核心联机账户冻结文件"
   )
   public Core1220050309Out runService(@RequestBody Core1220050309In in) {
      return (Core1220050309Out)ExecutorFlow.startFlow("core1220050309Flow", in, Core1220050309Out.class);
   }
}
