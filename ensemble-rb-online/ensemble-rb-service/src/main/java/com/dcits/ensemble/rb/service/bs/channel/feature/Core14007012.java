package com.dcits.ensemble.rb.service.bs.channel.feature;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.feature.ICore14007012;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.feature.Core14007012In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.feature.Core14007012Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14007012 implements ICore14007012 {
   private static final Logger log = LoggerFactory.getLogger(Core14007012.class);

   @CometMapping(
      value = "/rb/inq/agreement/wdl/acct/query",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "7012",
      name = "稳得利系列存款信息查询"
   )
   public Core14007012Out runService(@RequestBody Core14007012In in) {
      return (Core14007012Out)ExecutorFlow.startFlow("core14007012Flow", in, Core14007012Out.class);
   }
}
