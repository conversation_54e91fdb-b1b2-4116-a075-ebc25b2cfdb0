package com.dcits.ensemble.rb.service.bs.bill.pn;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1000023403;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1000023403In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1000023403Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1000023403 implements ICore1000023403 {
   private static final Logger log = LoggerFactory.getLogger(Core1000023403.class);

   @CometMapping(
      value = "/rb/fin/bill/return",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "023403",
      name = "本汇票退回"
   )
   public Core1000023403Out runService(@RequestBody Core1000023403In in) {
      return (Core1000023403Out)ExecutorFlow.startFlow("core1000023403Flow", in, Core1000023403Out.class);
   }
}
