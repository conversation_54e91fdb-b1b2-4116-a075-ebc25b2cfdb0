package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore1400050155;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1400050155In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1400050155Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400050155 implements ICore1400050155 {
   private static final Logger log = LoggerFactory.getLogger(Core1400050155.class);

   @CometMapping(
      value = "/rb/inq/judicature/apply",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "050155",
      name = "有权机关查询返回结果"
   )
   public Core1400050155Out runService(@RequestBody Core1400050155In in) {
      return (Core1400050155Out)ExecutorFlow.startFlow("core1400050155Flow", in, Core1400050155Out.class);
   }
}
