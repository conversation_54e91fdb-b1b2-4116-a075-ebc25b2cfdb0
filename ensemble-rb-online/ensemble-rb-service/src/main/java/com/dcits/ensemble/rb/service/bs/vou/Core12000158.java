package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore12000158;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000158In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000158Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000158 implements ICore12000158 {
   private static final Logger log = LoggerFactory.getLogger(Core12000158.class);

   @CometMapping(
      value = "/rb/nfin/voucher/card/pb/reissue",
      name = "卡折一体户凭证补发"
   )
   public Core12000158Out runService(@RequestBody Core12000158In in) {
      return (Core12000158Out)ExecutorFlow.startFlow("core12000158Flow", in);
   }
}
