package com.dcits.ensemble.rb.service.bs.spec.id;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.id.ICore14000802;
import com.dcits.ensemble.rb.api.model.mbsdcore.id.Core14000802In;
import com.dcits.ensemble.rb.api.model.mbsdcore.id.Core14000802Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000802 implements ICore14000802 {
   private static final Logger log = LoggerFactory.getLogger(Core14000802.class);

   @CometMapping(
      value = "/rb/inq/id/acct/reg",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0802",
      name = "智能存款账户登记簿查询"
   )
   public Core14000802Out runService(@RequestBody Core14000802In in) {
      return (Core14000802Out)ExecutorFlow.startFlow("core14000802Flow", in, Core14000802Out.class);
   }
}
