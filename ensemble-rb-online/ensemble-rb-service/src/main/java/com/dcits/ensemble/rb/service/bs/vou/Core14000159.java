package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000159;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000159In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000159Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000159 implements ICore14000159 {
   private static final Logger log = LoggerFactory.getLogger(Core14000159.class);

   @CometMapping(
      value = "/rb/inq/acct/seal/relation",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0159",
      name = "账户印鉴关联查询"
   )
   public Core14000159Out runService(@RequestBody Core14000159In in) {
      return (Core14000159Out)ExecutorFlow.startFlow("core14000159Flow", in, Core14000159Out.class);
   }
}
