package com.dcits.ensemble.rb.service.bs.pcp;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.pcp.ICore14000702;
import com.dcits.ensemble.rb.api.model.mbsdcore.pcp.Core14000702In;
import com.dcits.ensemble.rb.api.model.mbsdcore.pcp.Core14000702Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000702 implements ICore14000702 {
   private static final Logger log = LoggerFactory.getLogger(Core14000702.class);

   @CometMapping(
      value = "/rb/inq/pcp/limit/available",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0702",
      name = "资金池额度信息查询"
   )
   public Core14000702Out runService(@RequestBody Core14000702In in) {
      return (Core14000702Out)ExecutorFlow.startFlow("core14000702Flow", in, Core14000702Out.class);
   }
}
