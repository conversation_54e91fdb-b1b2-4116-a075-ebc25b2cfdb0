package com.dcits.ensemble.rb.service.bs.channel.payment;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore12006603;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.payment.Core12006603In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.payment.Core12006603Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core12006603 implements ICore12006603 {
   private static final Logger log = LoggerFactory.getLogger(Core12006603.class);

   @CometMapping(
      value = "/rb/nfin/payment/acctcheck/apply",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "6603",
      name = "支付系统对账申请"
   )
   public Core12006603Out runService(@RequestBody Core12006603In in) {
      return (Core12006603Out)ExecutorFlow.startGravity(in, Core12006603Out.class);
   }
}
