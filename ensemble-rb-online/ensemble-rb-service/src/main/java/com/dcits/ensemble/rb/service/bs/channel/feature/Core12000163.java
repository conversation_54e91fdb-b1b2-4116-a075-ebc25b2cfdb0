package com.dcits.ensemble.rb.service.bs.channel.feature;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.accounting.ICore12000163;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.feature.Core12000163In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.feature.Core12000163Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000163 implements ICore12000163 {
   private static final Logger log = LoggerFactory.getLogger(Core12000163.class);

   @CometMapping(
      value = "/rb/nfin/agreement/wdlseries",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0163",
      name = " 稳得利/智尊宝签约"
   )
   public Core12000163Out runService(@RequestBody Core12000163In in) {
      return (Core12000163Out)ExecutorFlow.startFlow("core12000163Flow", in, Core12000163Out.class);
   }
}
