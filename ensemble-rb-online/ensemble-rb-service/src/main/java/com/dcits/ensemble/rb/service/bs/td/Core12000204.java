package com.dcits.ensemble.rb.service.bs.td;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore12000204;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000204In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000204Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000204 implements ICore12000204 {
   private static final Logger log = LoggerFactory.getLogger(Core12000204.class);

   @CometMapping(
      value = "/rb/nfin/term/close/open",
      serviceCode = "Core",
      messageType = "1200",
      messageCode = "0204",
      name = "定期销转开"
   )
   public Core12000204Out runService(@RequestBody Core12000204In in) {
      return (Core12000204Out)ExecutorFlow.startGravity(in);
   }
}
