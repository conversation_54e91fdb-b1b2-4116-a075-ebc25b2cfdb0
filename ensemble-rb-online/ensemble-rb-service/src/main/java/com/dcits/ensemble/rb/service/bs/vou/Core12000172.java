package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore12000172;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000172In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000172Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000172 implements ICore12000172 {
   private static final Logger log = LoggerFactory.getLogger(Core12000172.class);

   @CometMapping(
      value = "/rb/nfin/book/print",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0172",
      name = "卡配对账簿补登"
   )
   public Core12000172Out runService(@RequestBody Core12000172In in) {
      return (Core12000172Out)ExecutorFlow.startFlow("core12000172Flow", in, Core12000172Out.class);
   }
}
