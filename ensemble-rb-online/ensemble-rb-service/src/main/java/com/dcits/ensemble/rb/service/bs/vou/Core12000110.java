package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12000110;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000110In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000110Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000110 implements ICore12000110 {
   private static final Logger log = LoggerFactory.getLogger(Core12000110.class);

   @CometMapping(
      value = "/rb/nfin/pbk/print/maint",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0110",
      name = "调整存折打印行"
   )
   public Core12000110Out runService(@RequestBody Core12000110In in) {
      return (Core12000110Out)ExecutorFlow.startGravity(in, Core12000110Out.class);
   }
}
