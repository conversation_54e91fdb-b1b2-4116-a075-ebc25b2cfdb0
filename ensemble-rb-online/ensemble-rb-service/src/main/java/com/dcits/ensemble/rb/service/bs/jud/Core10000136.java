package com.dcits.ensemble.rb.service.bs.jud;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore10000136;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10000136In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10000136Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core10000136 implements ICore10000136 {
   private static final Logger log = LoggerFactory.getLogger(Core10000136.class);

   @CometMapping(
      value = "/rb/fin/agreement/pcd/operate",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "0136",
      name = "周期性强制扣划协议操作"
   )
   public Core10000136Out runService(@RequestBody Core10000136In in) {
      return (Core10000136Out)ExecutorFlow.startGravity(in, Core10000136Out.class);
   }
}
