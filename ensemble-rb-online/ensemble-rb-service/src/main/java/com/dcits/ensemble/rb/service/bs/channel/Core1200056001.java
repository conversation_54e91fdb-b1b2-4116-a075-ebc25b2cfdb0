package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore1200056001;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1200056001In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1200056001Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core1200056001 implements ICore1200056001 {
   private static final Logger log = LoggerFactory.getLogger(Core1200056001.class);

   @CometMapping(
      value = "/rb/nfin/channel/iccard/second/note",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "056001",
      name = "IC卡系统制卡文件二次加工通知"
   )
   public Core1200056001Out runService(@RequestBody Core1200056001In in) {
      return (Core1200056001Out)ExecutorFlow.startFlow("core1200056001Flow", in, Core1200056001Out.class);
   }
}
