package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore1220050131;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1220050131In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1220050131Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core1220050131 implements ICore1220050131 {
   private static final Logger log = LoggerFactory.getLogger(Core1220050131.class);

   @CometMapping(
      value = "/rb/fin/iccard/account",
      serviceCode = "MbsdCore",
      messageType = "1220",
      messageCode = "050131",
      name = "IC卡商户批量入账（文件）"
   )
   public Core1220050131Out runService(@RequestBody Core1220050131In in) {
      return (Core1220050131Out)ExecutorFlow.startGravity(in, Core1220050131Out.class);
   }
}
