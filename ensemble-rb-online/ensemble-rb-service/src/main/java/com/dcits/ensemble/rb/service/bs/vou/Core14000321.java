package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore14000321;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000321In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000321Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000321 implements ICore14000321 {
   private static final Logger log = LoggerFactory.getLogger(Core14000321.class);

   @CometMapping(
      value = "/rb/tran/print/query",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0321",
      name = "存折回单打印信息查询 "
   )
   public Core14000321Out runService(@RequestBody Core14000321In in) {
      return (Core14000321Out)ExecutorFlow.startFlow("core14000321Flow", in, Core14000321Out.class);
   }
}
