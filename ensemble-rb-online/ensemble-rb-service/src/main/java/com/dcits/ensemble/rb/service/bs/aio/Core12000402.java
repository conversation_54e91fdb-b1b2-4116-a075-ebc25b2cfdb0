package com.dcits.ensemble.rb.service.bs.aio;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore12000402;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000402In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000402Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000402 implements ICore12000402 {
   private static final Logger log = LoggerFactory.getLogger(Core12000402.class);

   @CometMapping(
      value = "/rb/nfin/aio/uopen",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0402",
      name = "普通账户升级AIO账户"
   )
   public Core12000402Out runService(@RequestBody Core12000402In in) {
      return (Core12000402Out)ExecutorFlow.startFlow("core12000402Flow", in, Core12000402Out.class);
   }
}
