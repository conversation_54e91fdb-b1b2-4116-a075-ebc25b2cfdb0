package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore1220012101;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1220012101In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1220012101Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1220012101 implements ICore1220012101 {
   private static final Logger log = LoggerFactory.getLogger(Core1220012101.class);

   @CometMapping(
      value = "/rb/file/current/batchtran",
      serviceCode = "MbsdCore",
      messageType = "1220",
      messageCode = "012101",
      name = "批量转账（文件）"
   )
   public Core1220012101Out runService(@RequestBody Core1220012101In in) {
      return (Core1220012101Out)ExecutorFlow.startGravity(in, Core1220012101Out.class);
   }
}
