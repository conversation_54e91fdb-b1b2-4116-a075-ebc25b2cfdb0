package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000311;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000311In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000311Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000311 implements ICore14000311 {
   private static final Logger log = LoggerFactory.getLogger(Core14000311.class);

   @CometMapping(
      value = "/rb/inq/voucher/remove",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0311",
      name = "预约登记去介质信息查询（主账户，子账户、附属卡）"
   )
   public Core14000311Out runService(@RequestBody Core14000311In in) {
      return (Core14000311Out)ExecutorFlow.startFlow("core14000311Flow", in, Core14000311Out.class);
   }
}
