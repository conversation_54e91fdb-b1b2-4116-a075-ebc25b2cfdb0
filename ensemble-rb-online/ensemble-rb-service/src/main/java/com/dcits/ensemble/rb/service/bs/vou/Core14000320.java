package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000320;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000320In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000320Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000320 implements ICore14000320 {
   private static final Logger log = LoggerFactory.getLogger(Core14000320.class);

   @CometMapping(
      value = "/rb/inq/acct/lost",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0320",
      name = "挂失解挂查询有凭证账户信息（主账户，子账户、附属卡）"
   )
   public Core14000320Out runService(@RequestBody Core14000320In in) {
      return (Core14000320Out)ExecutorFlow.startFlow("core14000320Flow", in, Core14000320Out.class);
   }
}
