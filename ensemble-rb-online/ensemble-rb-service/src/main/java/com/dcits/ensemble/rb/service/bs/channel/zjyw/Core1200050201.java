package com.dcits.ensemble.rb.service.bs.channel.zjyw;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.feature.ICore1200050201;
import com.dcits.ensemble.rb.api.model.mbsdcore.zjyw.Core1200050201In;
import com.dcits.ensemble.rb.api.model.mbsdcore.zjyw.Core1200050201Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1200050201 implements ICore1200050201 {
   private static final Logger log = LoggerFactory.getLogger(Core1200050201.class);

   @CometMapping(
      value = "/rb/nfin/bon/issue/maint",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "050201",
      name = "国债发行维护"
   )
   public Core1200050201Out runService(@RequestBody Core1200050201In in) {
      return (Core1200050201Out)ExecutorFlow.startFlow("core1200050201Flow", in, Core1200050201Out.class);
   }
}
