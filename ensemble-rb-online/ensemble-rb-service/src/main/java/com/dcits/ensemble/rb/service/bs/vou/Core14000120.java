package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore14000120;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000120In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000120Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000120 implements ICore14000120 {
   private static final Logger log = LoggerFactory.getLogger(Core14000120.class);

   @CometMapping(
      value = "/rb/inq/voucher/deposit/cert",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0120",
      name = "存款证明凭证查询"
   )
   public Core14000120Out runService(@RequestBody Core14000120In in) {
      return (Core14000120Out)ExecutorFlow.startFlow("core14000120Flow", in, Core14000120Out.class);
   }
}
