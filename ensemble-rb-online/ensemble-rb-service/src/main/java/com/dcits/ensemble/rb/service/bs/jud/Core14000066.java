package com.dcits.ensemble.rb.service.bs.jud;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000066;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000066In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000066Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000066 implements ICore14000066 {
   private static final Logger log = LoggerFactory.getLogger(Core14000066.class);

   @CometMapping(
      value = "/rb/inq/endday/close/door",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0066",
      name = "日终关门检查"
   )
   public Core14000066Out runService(@RequestBody Core14000066In in) {
      return (Core14000066Out)ExecutorFlow.startFlow("core14000066Flow", in, Core14000066Out.class);
   }
}
