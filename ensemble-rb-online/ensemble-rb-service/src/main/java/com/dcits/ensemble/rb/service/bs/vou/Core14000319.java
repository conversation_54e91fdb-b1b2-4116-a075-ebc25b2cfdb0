package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000319;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000319In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000319Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000319 implements ICore14000319 {
   private static final Logger log = LoggerFactory.getLogger(Core14000319.class);

   @CometMapping(
      value = "/rb/inq/voucher/info/query",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0319",
      name = "凭证账户状态查询"
   )
   public Core14000319Out runService(@RequestBody Core14000319In in) {
      return (Core14000319Out)ExecutorFlow.startFlow("core14000319Flow", in, Core14000319Out.class);
   }
}
