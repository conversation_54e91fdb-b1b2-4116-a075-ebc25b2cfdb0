package com.dcits.ensemble.rb.service.bs.spec;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1400100110;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100110In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100110Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400100110 implements ICore1400100110 {
   private static final Logger log = LoggerFactory.getLogger(Core1400100110.class);

   @CometMapping(
      value = "/rb/inq/interest/conductdetail",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "100110",
      name = "理财产品利息清单查询"
   )
   public Core1400100110Out runService(@RequestBody Core1400100110In in) {
      return (Core1400100110Out)ExecutorFlow.startFlow("core1400100110Flow", in);
   }
}
