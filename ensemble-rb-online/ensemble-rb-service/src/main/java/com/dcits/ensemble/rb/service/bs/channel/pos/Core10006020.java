package com.dcits.ensemble.rb.service.bs.channel.pos;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.pos.ICore10006020;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core10006020In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core10006020Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core10006020 implements ICore10006020 {
   private static final Logger log = LoggerFactory.getLogger(Core10006020.class);

   @CometMapping(
      value = "/rb/fin/channel/meagent/dep",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "6020",
      name = "银联他代本存款"
   )
   public Core10006020Out runService(@RequestBody Core10006020In in) {
      return (Core10006020Out)ExecutorFlow.startFlow("core10006020Flow", in, Core10006020Out.class);
   }
}
