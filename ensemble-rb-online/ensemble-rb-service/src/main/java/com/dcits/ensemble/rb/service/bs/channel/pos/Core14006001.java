package com.dcits.ensemble.rb.service.bs.channel.pos;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.pos.ICore14006001;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core14006001In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core14006001Out;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14006001 implements ICore14006001 {
   @CometMapping(
      value = "/rb/inq/channel/acct/base",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "6001",
      name = "账户基本信息查询(外围专用)"
   )
   public Core14006001Out runService(@RequestBody Core14006001In in) {
      return (Core14006001Out)ExecutorFlow.startFlow("core14006001Flow", in, Core14006001Out.class);
   }
}
