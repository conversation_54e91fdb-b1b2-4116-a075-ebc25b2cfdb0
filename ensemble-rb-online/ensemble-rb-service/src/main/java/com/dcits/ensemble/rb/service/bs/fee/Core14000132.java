package com.dcits.ensemble.rb.service.bs.fee;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000132;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000132In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000132Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000132 implements ICore14000132 {
   private static final Logger log = LoggerFactory.getLogger(Core14000132.class);

   @CometMapping(
      value = "/rb/inq/fee/amortize/agreement",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0132",
      name = "费用摊销合约信息查询"
   )
   public Core14000132Out runService(@RequestBody Core14000132In in) {
      return (Core14000132Out)ExecutorFlow.startFlow("core14000132Flow", in, Core14000132Out.class);
   }
}
