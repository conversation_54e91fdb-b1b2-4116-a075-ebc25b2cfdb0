package com.dcits.ensemble.rb.service.bs.jud;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14006101;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14006101In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14006101Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14006101 implements ICore14006101 {
   private static final Logger log = LoggerFactory.getLogger(Core14006101.class);

   @CometMapping(
      value = "/rb/inq/impound/hang/query",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "6101",
      name = "强制扣划挂账查询"
   )
   public Core14006101Out runService(@RequestBody Core14006101In in) {
      return (Core14006101Out)ExecutorFlow.startFlow("core14006101Flow", in, Core14006101Out.class);
   }
}
