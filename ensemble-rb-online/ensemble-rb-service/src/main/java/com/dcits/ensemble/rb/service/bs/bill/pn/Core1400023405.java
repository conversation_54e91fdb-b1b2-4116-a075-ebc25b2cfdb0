package com.dcits.ensemble.rb.service.bs.bill.pn;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1400023405;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400023405In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400023405Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400023405 implements ICore1400023405 {
   private static final Logger log = LoggerFactory.getLogger(Core1400023405.class);

   @CometMapping(
      value = "/rb/inq/bill/orgLimit",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "023405",
      name = "机构票据交易限额查询"
   )
   public Core1400023405Out runService(@RequestBody Core1400023405In in) {
      return (Core1400023405Out)ExecutorFlow.startFlow("core1400023405Flow", in, Core1400023405Out.class);
   }
}
