package com.dcits.ensemble.rb.service.bs.channel.pos;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.pos.ICore1000056030;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core1000056030In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core1000056030Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core1000056030 implements ICore1000056030 {
   private static final Logger log = LoggerFactory.getLogger(Core1000056030.class);

   @CometMapping(
      value = "/rb/fin/channel/otheragent/delay/wtd",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "056030",
      name = "他代本延迟转账转出到期扣款"
   )
   public Core1000056030Out runService(@RequestBody Core1000056030In in) {
      return (Core1000056030Out)ExecutorFlow.startFlow("core1000056030Flow", in, Core1000056030Out.class);
   }
}
