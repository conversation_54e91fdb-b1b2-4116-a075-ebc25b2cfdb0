package com.dcits.ensemble.rb.service.bs.pcp;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore14000206;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000206In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000206Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000206 implements ICore14000206 {
   private static final Logger log = LoggerFactory.getLogger(Core14000206.class);

   @CometMapping(
      value = "/rb/inq/agreement/group",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0206",
      name = "存款组签约解约查询"
   )
   public Core14000206Out runService(@RequestBody Core14000206In in) {
      return (Core14000206Out)ExecutorFlow.startFlow("core14000206Flow", in);
   }
}
