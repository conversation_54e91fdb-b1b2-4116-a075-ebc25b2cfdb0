package com.dcits.ensemble.rb.service.bs.fee;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000134;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000134In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000134Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000134 implements ICore14000134 {
   private static final Logger log = LoggerFactory.getLogger(Core14000134.class);

   @CometMapping(
      value = "/rb/inq/fee/all",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0134",
      name = "费用查询"
   )
   public Core14000134Out runService(@RequestBody Core14000134In in) {
      return (Core14000134Out)ExecutorFlow.startFlow("core14000134Flow", in, Core14000134Out.class);
   }
}
