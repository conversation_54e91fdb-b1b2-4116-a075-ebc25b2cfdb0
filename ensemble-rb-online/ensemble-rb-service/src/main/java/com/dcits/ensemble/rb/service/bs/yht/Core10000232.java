package com.dcits.ensemble.rb.service.bs.yht;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore10000232;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10000232In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10000232Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core10000232 implements ICore10000232 {
   private static final Logger log = LoggerFactory.getLogger(Core10000232.class);

   @CometMapping(
      value = "/rb/fin/yht/adjbalance",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "0232",
      name = "一户通手工调整余额"
   )
   public Core10000232Out runService(@RequestBody Core10000232In in) {
      return (Core10000232Out)ExecutorFlow.startGravity(in, Core10000232Out.class);
   }
}
