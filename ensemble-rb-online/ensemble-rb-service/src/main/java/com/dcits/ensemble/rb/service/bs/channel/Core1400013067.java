package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore1400013067;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1400013067In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1400013067Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400013067 implements ICore1400013067 {
   private static final Logger log = LoggerFactory.getLogger(Core1400013067.class);

   @CometMapping(
      value = "/rb/inq/ib/account/batchdetail",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "013067",
      name = "网银批量记账批次明细查询"
   )
   public Core1400013067Out runService(@RequestBody Core1400013067In in) {
      return (Core1400013067Out)ExecutorFlow.startFlow("core1400013067Flow", in, Core1400013067Out.class);
   }
}
