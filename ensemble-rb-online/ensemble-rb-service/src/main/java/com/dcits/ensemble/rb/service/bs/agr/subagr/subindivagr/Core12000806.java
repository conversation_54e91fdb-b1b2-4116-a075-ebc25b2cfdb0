package com.dcits.ensemble.rb.service.bs.agr.subagr.subindivagr;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore12000806;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000806In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000806Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000806 implements ICore12000806 {
   private static final Logger log = LoggerFactory.getLogger(Core12000806.class);

   @CometMapping(
      value = "/rb/nfin/tx/ageement/sub",
      name = "同兴赢子协议签约管理",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0806"
   )
   public Core12000806Out runService(@RequestBody Core12000806In in) {
      return (Core12000806Out)ExecutorFlow.startFlow("core12000806Flow", in);
   }
}
