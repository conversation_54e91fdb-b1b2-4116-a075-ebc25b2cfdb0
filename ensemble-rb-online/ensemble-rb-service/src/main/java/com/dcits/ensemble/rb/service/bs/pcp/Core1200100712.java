package com.dcits.ensemble.rb.service.bs.pcp;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1200100712;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1200100712In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1200100712Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1200100712 implements ICore1200100712 {
   private static final Logger log = LoggerFactory.getLogger(Core1200100712.class);

   @CometMapping(
      value = "/rb/pcp/agreement/rate/register",
      name = "资金池利率登记",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "100712"
   )
   public Core1200100712Out runService(@RequestBody Core1200100712In in) {
      return (Core1200100712Out)ExecutorFlow.startGravity(in);
   }
}
