package com.dcits.ensemble.rb.service.bs.pcp;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.pcp.ICore14000706;
import com.dcits.ensemble.rb.api.model.mbsdcore.pcp.Core14000706In;
import com.dcits.ensemble.rb.api.model.mbsdcore.pcp.Core14000706Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000706 implements ICore14000706 {
   private static final Logger log = LoggerFactory.getLogger(Core14000706.class);

   @CometMapping(
      value = "/rb/inq/pcp/agreement",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0706",
      name = "资金池签约信息查询"
   )
   public Core14000706Out runService(@RequestBody Core14000706In in) {
      return (Core14000706Out)ExecutorFlow.startFlow("core14000706Flow", in, Core14000706Out.class);
   }
}
