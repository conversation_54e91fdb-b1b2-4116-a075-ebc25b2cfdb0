package com.dcits.ensemble.rb.service.bs.td;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1400100011;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100011In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100011Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400100011 implements ICore1400100011 {
   private static final Logger log = LoggerFactory.getLogger(Core1400100011.class);

   @CometMapping(
      value = "/rb/inq/tran/eoshistperiphery",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "100011",
      name = "活期账户查询定期交易历史"
   )
   public Core1400100011Out runService(@RequestBody Core1400100011In core1400100011In) {
      return (Core1400100011Out)ExecutorFlow.startFlow("core1400100011Flow", core1400100011In);
   }
}
