package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000324;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000324In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000324Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000324 implements ICore14000324 {
   private static final Logger log = LoggerFactory.getLogger(Core14000324.class);

   @CometMapping(
      value = "/rb/inq/voucher/salecancle",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0324",
      name = "凭证取消出售查询"
   )
   public Core14000324Out runService(@RequestBody Core14000324In in) {
      return (Core14000324Out)ExecutorFlow.startFlow("core14000324Flow", in, Core14000324Out.class);
   }
}
