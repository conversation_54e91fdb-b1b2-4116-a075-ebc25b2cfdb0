package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore12000173;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000173In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000173Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000173 implements ICore12000173 {
   private static final Logger log = LoggerFactory.getLogger(Core12000173.class);

   @CometMapping(
      value = "/rb/nfin/book/print/maint",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0173",
      name = "卡配对账簿调整打印行"
   )
   public Core12000173Out runService(@RequestBody Core12000173In in) {
      return (Core12000173Out)ExecutorFlow.startFlow("core12000173Flow", in, Core12000173Out.class);
   }
}
