package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore10009405;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10009405In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10009405Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core10009405 implements ICore10009405 {
   private static final Logger log = LoggerFactory.getLogger(Core10009405.class);

   @CometMapping(
      value = "/rb/fin/inner/dep",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "9405",
      name = "内部户现金存入"
   )
   public Core10009405Out runService(@RequestBody Core10009405In in) {
      return (Core10009405Out)ExecutorFlow.startGravity(in);
   }
}
