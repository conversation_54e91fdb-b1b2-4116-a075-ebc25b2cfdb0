package com.dcits.ensemble.rb.service.bs.yht;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12000139;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000139In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000139Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000139 implements ICore12000139 {
   private static final Logger log = LoggerFactory.getLogger(Core12000139.class);

   @CometMapping(
      value = "/rb/nfin/yht/close",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0139",
      name = "一户通销户"
   )
   public Core12000139Out runService(@RequestBody Core12000139In in) {
      return (Core12000139Out)ExecutorFlow.startGravity(in, Core12000139Out.class);
   }
}
