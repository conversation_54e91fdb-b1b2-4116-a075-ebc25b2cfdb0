package com.dcits.ensemble.rb.service.bs.fee;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore10006002;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10006002In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10006002Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core10006002 implements ICore10006002 {
   private static final Logger log = LoggerFactory.getLogger(Core10006002.class);

   @CometMapping(
      value = "/rb/fin/fee/amortize/return",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "6002",
      name = "预收手续费费用退回接口"
   )
   public Core10006002Out runService(@RequestBody Core10006002In in) {
      return (Core10006002Out)ExecutorFlow.startFlow("core10006002Flow", in, Core10006002Out.class);
   }
}
