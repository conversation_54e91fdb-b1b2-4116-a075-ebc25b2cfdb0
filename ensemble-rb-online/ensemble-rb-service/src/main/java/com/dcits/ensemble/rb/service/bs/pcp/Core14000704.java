package com.dcits.ensemble.rb.service.bs.pcp;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.pcp.ICore14000704;
import com.dcits.ensemble.rb.api.model.mbsdcore.pcp.Core14000704In;
import com.dcits.ensemble.rb.api.model.mbsdcore.pcp.Core14000704Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000704 implements ICore14000704 {
   private static final Logger log = LoggerFactory.getLogger(Core14000704.class);

   @CometMapping(
      value = "/rb/inq/pcp/sub/amount",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0704",
      name = "子账户可归集下拨金额查询"
   )
   public Core14000704Out runService(@RequestBody Core14000704In in) {
      return (Core14000704Out)ExecutorFlow.startFlow("core14000704Flow", in, Core14000704Out.class);
   }
}
