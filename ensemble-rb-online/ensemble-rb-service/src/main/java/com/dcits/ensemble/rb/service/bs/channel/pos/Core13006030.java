package com.dcits.ensemble.rb.service.bs.channel.pos;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.pos.ICore13006030;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core13006030In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core13006030Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core13006030 implements ICore13006030 {
   private static final Logger log = LoggerFactory.getLogger(Core13006030.class);

   @CometMapping(
      value = "/rb/rev/pos/auth/done",
      serviceCode = "MbsdCore",
      messageType = "1300",
      messageCode = "6030",
      name = "银联POS预授权完成冲正"
   )
   public Core13006030Out runService(@RequestBody Core13006030In in) {
      return (Core13006030Out)ExecutorFlow.startFlow("core13006030Flow", in, Core13006030Out.class);
   }
}
