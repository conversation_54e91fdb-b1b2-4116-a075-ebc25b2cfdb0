package com.dcits.ensemble.rb.service.bs.bill.ad;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ad.ICore12000325;
import com.dcits.ensemble.rb.api.model.mbsdcore.ad.Core12000325In;
import com.dcits.ensemble.rb.api.model.mbsdcore.ad.Core12000325Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000325 implements ICore12000325 {
   private static final Logger log = LoggerFactory.getLogger(Core12000325.class);

   @CometMapping(
      value = "/rb/nfin/bab/cash/operate",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0325",
      name = "银行承兑汇票兑付"
   )
   public Core12000325Out runService(@RequestBody Core12000325In in) {
      return (Core12000325Out)ExecutorFlow.startGravity(in, Core12000325Out.class);
   }
}
