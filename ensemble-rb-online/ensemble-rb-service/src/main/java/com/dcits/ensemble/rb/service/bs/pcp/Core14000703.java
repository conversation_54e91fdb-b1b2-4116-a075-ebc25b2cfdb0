package com.dcits.ensemble.rb.service.bs.pcp;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.pcp.ICore14000703;
import com.dcits.ensemble.rb.api.model.mbsdcore.pcp.Core14000703In;
import com.dcits.ensemble.rb.api.model.mbsdcore.pcp.Core14000703Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000703 implements ICore14000703 {
   private static final Logger log = LoggerFactory.getLogger(Core14000703.class);

   @CometMapping(
      value = "/rb/inq/pcp/price",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0703",
      name = "内部计价利息查询"
   )
   public Core14000703Out runService(@RequestBody Core14000703In in) {
      return (Core14000703Out)ExecutorFlow.startFlow("core14000703Flow", in, Core14000703Out.class);
   }
}
