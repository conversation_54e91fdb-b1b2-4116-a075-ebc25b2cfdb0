package com.dcits.ensemble.rb.service.bs.td;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12000124;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000124In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000124Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000124 implements ICore12000124 {
   private static final Logger log = LoggerFactory.getLogger(Core12000124.class);

   @CometMapping(
      value = "/rb/nfin/term/term/change",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0124",
      name = "定期存期变更"
   )
   public Core12000124Out runService(@RequestBody Core12000124In in) {
      return (Core12000124Out)ExecutorFlow.startGravity(in, Core12000124Out.class);
   }
}
