package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12006201;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12006201In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12006201Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core12006201 implements ICore12006201 {
   private static final Logger log = LoggerFactory.getLogger(Core12006201.class);

   @CometMapping(
      value = "/rb/nfin/deposit/prove/reprint",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "6201",
      name = "资信证明补打处理"
   )
   public Core12006201Out runService(@RequestBody Core12006201In in) {
      return (Core12006201Out)ExecutorFlow.startFlow("core12006201Flow", in, Core12006201Out.class);
   }
}
