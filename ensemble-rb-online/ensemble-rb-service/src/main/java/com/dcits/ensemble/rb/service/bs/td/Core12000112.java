package com.dcits.ensemble.rb.service.bs.td;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12000112;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000112In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000112Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000112 implements ICore12000112 {
   private static final Logger log = LoggerFactory.getLogger(Core12000112.class);

   @CometMapping(
      value = "/rb/nfin/term/client/change",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0112",
      name = "定期持有人变更"
   )
   public Core12000112Out runService(@RequestBody Core12000112In in) {
      return (Core12000112Out)ExecutorFlow.startGravity(in, Core12000112Out.class);
   }
}
