package com.dcits.ensemble.rb.service.bs.bill.pn;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore1400032401;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400032401In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400032401Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400032401 implements ICore1400032401 {
   private static final Logger log = LoggerFactory.getLogger(Core1400032401.class);

   @CometMapping(
      value = "/rb/inq/bab/standby",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "032401",
      name = "备款信息查询"
   )
   public Core1400032401Out runService(@RequestBody Core1400032401In in) {
      return (Core1400032401Out)ExecutorFlow.startFlow("core1400032401Flow", in, Core1400032401Out.class);
   }
}
