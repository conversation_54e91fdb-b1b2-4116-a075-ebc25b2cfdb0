package com.dcits.ensemble.rb.service.bs.bill.pn;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1000023402;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1000023402In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1000023402Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1000023402 implements ICore1000023402 {
   private static final Logger log = LoggerFactory.getLogger(Core1000023402.class);

   @CometMapping(
      value = "/rb/fin/bill/cashing",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "023402",
      name = "银行本票兑付"
   )
   public Core1000023402Out runService(@RequestBody Core1000023402In in) {
      return (Core1000023402Out)ExecutorFlow.startFlow("core1000023402Flow", in, Core1000023402Out.class);
   }
}
