package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.zjyw.ICore1400010103;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1400010103In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1400010103Out;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400010103 implements ICore1400010103 {
   @CometMapping(
      value = "/rb/inq/client/asset/query",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "010103",
      name = "个人客户资产查询"
   )
   public Core1400010103Out runService(@RequestBody Core1400010103In in) {
      return (Core1400010103Out)ExecutorFlow.startFlow("core1400010103Flow", in, Core1400010103Out.class);
   }
}
