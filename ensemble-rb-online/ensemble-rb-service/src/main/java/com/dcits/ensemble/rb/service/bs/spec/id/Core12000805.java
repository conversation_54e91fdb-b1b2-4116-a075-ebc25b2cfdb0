package com.dcits.ensemble.rb.service.bs.spec.id;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore12000805;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000805In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000805Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000805 implements ICore12000805 {
   private static final Logger log = LoggerFactory.getLogger(Core12000805.class);

   @CometMapping(
      value = "/rb/nfin/ind/ageement/maint",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0805",
      name = "智能通知存款签约/维护/解约"
   )
   public Core12000805Out runService(@RequestBody Core12000805In in) {
      return (Core12000805Out)ExecutorFlow.startGravity(in, Core12000805Out.class);
   }
}
