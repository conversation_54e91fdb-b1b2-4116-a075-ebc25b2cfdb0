package com.dcits.ensemble.rb.service.bs.td;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore12000210;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000210In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000210Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000210 implements ICore12000210 {
   private static final Logger log = LoggerFactory.getLogger(Core12000210.class);

   @CometMapping(
      value = "/rb/nfin/term/agre/modify",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0210",
      name = "协议存款维护"
   )
   public Core12000210Out runService(@RequestBody Core12000210In in) {
      return (Core12000210Out)ExecutorFlow.startGravity(in);
   }
}
