package com.dcits.ensemble.rb.service.bs.spec;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore1400057406;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400057406In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400057406Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400057406 implements ICore1400057406 {
   private static final Logger log = LoggerFactory.getLogger(Core1400057406.class);

   @CometMapping(
      value = "/rb/inq/ybwl/earnings",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "057406",
      name = "一本万利收益查询"
   )
   public Core1400057406Out runService(@RequestBody Core1400057406In in) {
      return (Core1400057406Out)ExecutorFlow.startFlow("core1400057406Flow", in, Core1400057406Out.class);
   }
}
