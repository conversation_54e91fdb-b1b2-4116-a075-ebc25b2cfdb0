package com.dcits.ensemble.rb.service.bs.bill.ad;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ad.ICore12000331;
import com.dcits.ensemble.rb.api.model.mbsdcore.ad.Core12000331In;
import com.dcits.ensemble.rb.api.model.mbsdcore.ad.Core12000331Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000331 implements ICore12000331 {
   private static final Logger log = LoggerFactory.getLogger(Core12000331.class);

   @CometMapping(
      value = "/rb/nfin/bab/margin/operate",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0331",
      name = "银行承兑汇票保证金账户追加/取消"
   )
   public Core12000331Out runService(@RequestBody Core12000331In in) {
      return (Core12000331Out)ExecutorFlow.startGravity(in, Core12000331Out.class);
   }
}
