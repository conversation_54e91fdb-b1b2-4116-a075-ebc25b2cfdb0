package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000123;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000123In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000123Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000123 implements ICore14000123 {
   private static final Logger log = LoggerFactory.getLogger(Core14000123.class);

   @CometMapping(
      value = "/rb/inq/pbk/printline",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0123",
      name = "存折打印行查询"
   )
   public Core14000123Out runService(@RequestBody Core14000123In in) {
      return (Core14000123Out)ExecutorFlow.startFlow("core14000123Flow", in, Core14000123Out.class);
   }
}
