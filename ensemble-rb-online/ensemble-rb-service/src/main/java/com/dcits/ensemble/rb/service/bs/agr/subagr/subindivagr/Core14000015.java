package com.dcits.ensemble.rb.service.bs.agr.subagr.subindivagr;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000015;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000015In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000015Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000015 implements ICore14000015 {
   private static final Logger log = LoggerFactory.getLogger(Core14000015.class);

   @CometMapping(
      value = "/rb/inq/fin/agreement",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0015",
      name = "卡理财签约/解约查询"
   )
   public Core14000015Out runService(@RequestBody Core14000015In in) {
      return (Core14000015Out)ExecutorFlow.startFlow("core14000015Flow", in, Core14000015Out.class);
   }
}
