package com.dcits.ensemble.rb.service.bs.agr.subagr.subindivagr;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore14000147;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000147In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000147Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000147 implements ICore14000147 {
   private static final Logger log = LoggerFactory.getLogger(Core14000147.class);

   @CometMapping(
      value = "/rb/nfin/agreement/csl/query",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0136",
      name = "联动扣款协议信息查询"
   )
   public Core14000147Out runService(@RequestBody Core14000147In in) {
      return (Core14000147Out)ExecutorFlow.startFlow("core14000147Flow", in, Core14000147Out.class);
   }
}
