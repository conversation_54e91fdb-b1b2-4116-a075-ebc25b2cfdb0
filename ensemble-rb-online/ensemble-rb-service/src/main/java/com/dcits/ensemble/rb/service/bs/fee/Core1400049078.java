package com.dcits.ensemble.rb.service.bs.fee;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore1400049078;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400049078In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400049078Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400049078 implements ICore1400049078 {
   private static final Logger log = LoggerFactory.getLogger(Core1400049078.class);

   @CometMapping(
      value = "/rb/inq/feepackage/tran",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "049078",
      name = "费用套餐交易明细查询"
   )
   public Core1400049078Out runService(@RequestBody Core1400049078In in) {
      return (Core1400049078Out)ExecutorFlow.startFlow("core1400049078Flow", in, Core1400049078Out.class);
   }
}
