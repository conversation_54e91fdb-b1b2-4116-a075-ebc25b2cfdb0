package com.dcits.ensemble.rb.service.bs.spec;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1400100020;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100020In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100020Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400100020 implements ICore1400100020 {
   private static final Logger log = LoggerFactory.getLogger(Core1400100020.class);

   @CometMapping(
      value = "/rb/inq/agreement/indvacct",
      name = "灵活盈储蓄定投账户信息查询",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "100020"
   )
   public Core1400100020Out runService(@RequestBody Core1400100020In in) {
      return (Core1400100020Out)ExecutorFlow.startFlow("core1400100020Flow", in);
   }
}
