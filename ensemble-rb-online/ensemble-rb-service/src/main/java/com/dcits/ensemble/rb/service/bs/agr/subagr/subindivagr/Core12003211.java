package com.dcits.ensemble.rb.service.bs.agr.subagr.subindivagr;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore12003211;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12003211In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12003211Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12003211 implements ICore12003211 {
   private static final Logger log = LoggerFactory.getLogger(Core12003211.class);

   @CometMapping(
      value = "/rb/nfin/fin/agreement/avg/operate",
      name = "日均余额靠档协议处理"
   )
   public Core12003211Out runService(@RequestBody Core12003211In in) {
      return (Core12003211Out)ExecutorFlow.startGravity(in);
   }
}
