package com.dcits.ensemble.rb.service.bs.channel.pos;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.pos.ICore13006034;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core13006034In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core13006034Out;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core13006034 implements ICore13006034 {
   @CometMapping(
      value = "/rb/rev/channel/pos/auth",
      serviceCode = "MbsdCore",
      messageType = "1300",
      messageCode = "6034",
      name = "银联POS预授权冲正"
   )
   public Core13006034Out runService(@RequestBody Core13006034In in) {
      return (Core13006034Out)ExecutorFlow.startFlow("core13006034Flow", in, Core13006034Out.class);
   }
}
