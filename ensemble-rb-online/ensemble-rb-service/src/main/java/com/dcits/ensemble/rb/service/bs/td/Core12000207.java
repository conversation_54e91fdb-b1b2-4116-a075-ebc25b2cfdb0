package com.dcits.ensemble.rb.service.bs.td;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12000207;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000207In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000207Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000207 implements ICore12000207 {
   private static final Logger log = LoggerFactory.getLogger(Core12000207.class);

   @CometMapping(
      value = "/rb/nfin/term/inout",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0207",
      name = "子账户移入移出"
   )
   public Core12000207Out runService(@RequestBody Core12000207In in) {
      return (Core12000207Out)ExecutorFlow.startFlow("core12000207Flow", in, Core12000207Out.class);
   }
}
