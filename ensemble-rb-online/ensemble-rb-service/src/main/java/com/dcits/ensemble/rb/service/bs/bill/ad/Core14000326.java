package com.dcits.ensemble.rb.service.bs.bill.ad;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ad.ICore14000326;
import com.dcits.ensemble.rb.api.model.mbsdcore.ad.Core14000326In;
import com.dcits.ensemble.rb.api.model.mbsdcore.ad.Core14000326Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000326 implements ICore14000326 {
   private static final Logger log = LoggerFactory.getLogger(Core14000326.class);

   @CometMapping(
      value = "/rb/inq/bab/info",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0326",
      name = "银行承兑汇票信息查询"
   )
   public Core14000326Out runService(@RequestBody Core14000326In in) {
      return (Core14000326Out)ExecutorFlow.startFlow("core14000326Flow", in, Core14000326Out.class);
   }
}
