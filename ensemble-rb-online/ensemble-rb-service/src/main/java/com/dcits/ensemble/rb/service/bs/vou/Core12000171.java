package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore12000171;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000171In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000171Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000171 implements ICore12000171 {
   private static final Logger log = LoggerFactory.getLogger(Core12000171.class);

   @CometMapping(
      value = "/rb/nfin/printbook/change",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0171",
      name = "卡配对账簿更换"
   )
   public Core12000171Out runService(@RequestBody Core12000171In in) {
      return (Core12000171Out)ExecutorFlow.startFlow("core12000171Flow", in, Core12000171Out.class);
   }
}
