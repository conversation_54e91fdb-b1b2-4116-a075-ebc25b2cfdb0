package com.dcits.ensemble.rb.service.bs.spec;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1400100221;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100221In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100221Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400100221 implements ICore1400100221 {
   private static final Logger log = LoggerFactory.getLogger(Core1400100221.class);

   @CometMapping(
      value = "/rb/inq/xxc/create/stagecode",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "100221",
      name = "新兴存零售定期期次代码生成"
   )
   public Core1400100221Out runService(@RequestBody Core1400100221In in) {
      return (Core1400100221Out)ExecutorFlow.startFlow("core1400100221Flow", in);
   }
}
