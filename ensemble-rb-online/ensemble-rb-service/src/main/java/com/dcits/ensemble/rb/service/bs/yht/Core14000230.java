package com.dcits.ensemble.rb.service.bs.yht;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000230;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000230In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000230Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000230 implements ICore14000230 {
   private static final Logger log = LoggerFactory.getLogger(Core14000230.class);

   @CometMapping(
      value = "/rb/inq/yht/acct/main",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0230",
      name = "一户通主账户信息查询"
   )
   public Core14000230Out runService(@RequestBody Core14000230In in) {
      return (Core14000230Out)ExecutorFlow.startFlow("core14000230Flow", in, Core14000230Out.class);
   }
}
