package com.dcits.ensemble.rb.service.bs.channel.pos;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.pos.ICore10006014;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core10006014In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core10006014Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core10006014 implements ICore10006014 {
   private static final Logger log = LoggerFactory.getLogger(Core10006014.class);

   @CometMapping(
      value = "/rb/fin/channel/pos/consume/cancel",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "6014",
      name = "POS消费撤销"
   )
   public Core10006014Out runService(@RequestBody Core10006014In in) {
      return (Core10006014Out)ExecutorFlow.startFlow("core10006014Flow", in, Core10006014Out.class);
   }
}
