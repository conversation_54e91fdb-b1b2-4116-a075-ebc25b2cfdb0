package com.dcits.ensemble.rb.service.bs.channel.pos;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.pos.ICore10006038;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core10006038In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core10006038Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core10006038 implements ICore10006038 {
   private static final Logger log = LoggerFactory.getLogger(Core10006038.class);

   @CometMapping(
      value = "/rb/fin/channel/onbehalf/collect",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "6038",
      name = "银联他代本代收"
   )
   public Core10006038Out runService(@RequestBody Core10006038In in) {
      return (Core10006038Out)ExecutorFlow.startFlow("core10006038Flow", in, Core10006038Out.class);
   }
}
