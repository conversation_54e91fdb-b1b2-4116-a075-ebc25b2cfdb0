package com.dcits.ensemble.rb.service.bs.pcp;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore12209415;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12209415In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12209415Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12209415 implements ICore12209415 {
   private static final Logger log = LoggerFactory.getLogger(Core12209415.class);

   @CometMapping(
      value = "/rb/inq/highRisk/excel",
      serviceCode = "MbsdCore",
      messageType = "1220",
      messageCode = "9415",
      name = "高风险交易记录生成文件"
   )
   public Core12209415Out runService(@RequestBody Core12209415In in) {
      return (Core12209415Out)ExecutorFlow.startFlow("core12209415Flow", in);
   }
}
