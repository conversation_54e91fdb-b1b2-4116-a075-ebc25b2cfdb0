package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore1400050411;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1400050411In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1400050411Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core1400050411 implements ICore1400050411 {
   private static final Logger log = LoggerFactory.getLogger(Core1400050411.class);

   @CometMapping(
      value = "/rb/inq/zxqy/hist",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "050411",
      name = "坐享其盈明细查询"
   )
   public Core1400050411Out runService(@RequestBody Core1400050411In in) {
      return (Core1400050411Out)ExecutorFlow.startFlow("core1400050411Flow", in, Core1400050411Out.class);
   }
}
