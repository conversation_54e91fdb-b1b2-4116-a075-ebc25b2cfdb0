package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12000308;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000308In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000308Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000308 implements ICore12000308 {
   private static final Logger log = LoggerFactory.getLogger(Core12000308.class);

   @CometMapping(
      value = "/rb/nfin/cheque/refund",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0308",
      name = "支票退票"
   )
   public Core12000308Out runService(@RequestBody Core12000308In in) {
      return (Core12000308Out)ExecutorFlow.startGravity(in, Core12000308Out.class);
   }
}
