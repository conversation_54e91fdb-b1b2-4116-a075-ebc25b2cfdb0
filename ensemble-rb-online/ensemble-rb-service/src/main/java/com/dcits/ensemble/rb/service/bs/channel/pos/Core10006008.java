package com.dcits.ensemble.rb.service.bs.channel.pos;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.pos.ICore10006008;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core10006008In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core10006008Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core10006008 implements ICore10006008 {
   private static final Logger log = LoggerFactory.getLogger(Core10006008.class);

   @CometMapping(
      value = "/rb/fin/channel/meagent/dep/confirm",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "6008"
   )
   public Core10006008Out runService(@RequestBody Core10006008In in) {
      return (Core10006008Out)ExecutorFlow.startFlow("core10006008Flow", in, Core10006008Out.class);
   }
}
