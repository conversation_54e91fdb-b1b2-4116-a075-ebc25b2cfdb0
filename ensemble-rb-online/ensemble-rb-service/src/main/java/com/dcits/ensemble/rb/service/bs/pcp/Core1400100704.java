package com.dcits.ensemble.rb.service.bs.pcp;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1400100704;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100704In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100704Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400100704 implements ICore1400100704 {
   private static final Logger log = LoggerFactory.getLogger(Core1400100704.class);

   @CometMapping(
      value = "/rb/inq/pcp/group/acct",
      name = "资金池账户查询",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "100704"
   )
   public Core1400100704Out runService(@RequestBody Core1400100704In in) {
      return (Core1400100704Out)ExecutorFlow.startFlow("core1400100704Flow", in);
   }
}
