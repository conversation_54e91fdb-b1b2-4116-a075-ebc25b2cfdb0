package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000403;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000403In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000403Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000403 implements ICore14000403 {
   private static final Logger log = LoggerFactory.getLogger(Core14000403.class);

   @CometMapping(
      value = "/rb/inq/voucher/change",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0403",
      name = "凭证更换查询"
   )
   public Core14000403Out runService(@RequestBody Core14000403In in) {
      return (Core14000403Out)ExecutorFlow.startFlow("core14000403Flow", in, Core14000403Out.class);
   }
}
