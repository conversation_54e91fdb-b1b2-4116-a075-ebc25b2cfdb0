package com.dcits.ensemble.rb.service.bs.channel.accountings;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.accounting.ICore1000056028;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.accounting.Core1000056028In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.accounting.Core1000056028Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core1000056028 implements ICore1000056028 {
   private static final Logger log = LoggerFactory.getLogger(Core1000056028.class);

   @CometMapping(
      value = "/rb/fin/channel/otheragent/consume/revert",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "056028",
      name = "银联本代他取款和他带本消费和他带本取款冲正"
   )
   public Core1000056028Out runService(@RequestBody Core1000056028In in) {
      return (Core1000056028Out)ExecutorFlow.startGravity(in, Core1000056028Out.class);
   }
}
