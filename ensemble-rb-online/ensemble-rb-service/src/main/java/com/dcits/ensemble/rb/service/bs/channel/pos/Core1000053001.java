package com.dcits.ensemble.rb.service.bs.channel.pos;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.pos.ICore1000053001;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core1000053001In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core1000053001Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core1000053001 implements ICore1000053001 {
   private static final Logger log = LoggerFactory.getLogger(Core1000053001.class);

   @CometMapping(
      value = "/rb/fin/channel/sigle/account",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "053001",
      name = "单笔统一记账"
   )
   public Core1000053001Out runService(@RequestBody Core1000053001In in) {
      return (Core1000053001Out)ExecutorFlow.startFlow("core1000053001Flow", in, Core1000053001Out.class);
   }
}
