package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore12000170;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000170In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000170Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000170 implements ICore12000170 {
   private static final Logger log = LoggerFactory.getLogger(Core12000170.class);

   @CometMapping(
      value = "/rb/nfin/printbook/open",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0170",
      name = "卡配对账簿开立"
   )
   public Core12000170Out runService(@RequestBody Core12000170In in) {
      return (Core12000170Out)ExecutorFlow.startFlow("core12000170Flow", in, Core12000170Out.class);
   }
}
