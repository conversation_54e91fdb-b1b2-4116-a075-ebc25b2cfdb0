package com.dcits.ensemble.rb.service.bs.td;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000402;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000402In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000402Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000402 implements ICore14000402 {
   private static final Logger log = LoggerFactory.getLogger(Core14000402.class);

   @CometMapping(
      value = "/rb/inq/acct/precontract",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0402",
      name = "通知存款通知查询"
   )
   public Core14000402Out runService(@RequestBody Core14000402In in) {
      return (Core14000402Out)ExecutorFlow.startFlow("core14000402Flow", in, Core14000402Out.class);
   }
}
