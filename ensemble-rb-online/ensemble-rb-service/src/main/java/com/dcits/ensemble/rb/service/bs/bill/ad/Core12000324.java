package com.dcits.ensemble.rb.service.bs.bill.ad;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ad.ICore12000324;
import com.dcits.ensemble.rb.api.model.mbsdcore.ad.Core12000324In;
import com.dcits.ensemble.rb.api.model.mbsdcore.ad.Core12000324Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000324 implements ICore12000324 {
   private static final Logger log = LoggerFactory.getLogger(Core12000324.class);

   @CometMapping(
      value = "/rb/nfin/bab/accptance/cancel",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0324",
      name = "银行承兑汇票承兑/取消"
   )
   public Core12000324Out runService(@RequestBody Core12000324In in) {
      return (Core12000324Out)ExecutorFlow.startGravity(in, Core12000324Out.class);
   }
}
