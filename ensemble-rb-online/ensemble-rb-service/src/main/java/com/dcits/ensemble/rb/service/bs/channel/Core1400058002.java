package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore1400058002;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1400058002In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1400058002Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core1400058002 implements ICore1400058002 {
   private static final Logger log = LoggerFactory.getLogger(Core1400058002.class);

   @CometMapping(
      value = "/rb/inq/sign/acct",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "058002",
      name = "根据客户号经办人证件号查询签约账户"
   )
   public Core1400058002Out runService(@RequestBody Core1400058002In in) {
      return (Core1400058002Out)ExecutorFlow.startGravity(in, Core1400058002Out.class);
   }
}
