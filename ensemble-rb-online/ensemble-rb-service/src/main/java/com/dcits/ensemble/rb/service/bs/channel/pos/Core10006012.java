package com.dcits.ensemble.rb.service.bs.channel.pos;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.pos.ICore10006012;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core10006012In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core10006012Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core10006012 implements ICore10006012 {
   private static final Logger log = LoggerFactory.getLogger(Core10006012.class);

   @CometMapping(
      value = "/rb/fin/channel/pos/auth/done",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "6012",
      name = "银联POS预授权完成"
   )
   public Core10006012Out runService(@RequestBody Core10006012In in) {
      return (Core10006012Out)ExecutorFlow.startFlow("core10006012Flow", in, Core10006012Out.class);
   }
}
