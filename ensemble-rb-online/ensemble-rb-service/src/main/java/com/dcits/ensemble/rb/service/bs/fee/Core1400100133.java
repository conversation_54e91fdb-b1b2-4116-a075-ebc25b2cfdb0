package com.dcits.ensemble.rb.service.bs.fee;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1400100133;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100133In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100133Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400100133 implements ICore1400100133 {
   private static final Logger log = LoggerFactory.getLogger(Core1400100133.class);

   @CometMapping(
      value = "/rb/inq/amort/change/hist",
      name = "费用摊销修改历史查询",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "100133"
   )
   public Core1400100133Out runService(@RequestBody Core1400100133In in) {
      return (Core1400100133Out)ExecutorFlow.startFlow("core1400100133Flow", in);
   }
}
