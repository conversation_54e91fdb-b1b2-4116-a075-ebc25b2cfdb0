package com.dcits.ensemble.rb.service.bs.res;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000207;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000207In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000207Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000207 implements ICore14000207 {
   private static final Logger log = LoggerFactory.getLogger(Core14000207.class);

   @CometMapping(
      value = "/rb/inq/all/restraint",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0207",
      name = "限制信息查询"
   )
   public Core14000207Out runService(@RequestBody Core14000207In in) {
      return (Core14000207Out)ExecutorFlow.startFlow("core14000207Flow", in, Core14000207Out.class);
   }
}
