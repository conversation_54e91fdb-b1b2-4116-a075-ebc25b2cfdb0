package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.pos.ICore1220039001;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core1220039001In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core1220039001Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core1220039001 implements ICore1220039001 {
   private static final Logger log = LoggerFactory.getLogger(Core1220039001.class);

   @CometMapping(
      value = "/rb/file/statement/export",
      serviceCode = "MbsdCore",
      messageType = "1220",
      messageCode = "039001",
      name = "生成对账文件"
   )
   public Core1220039001Out runService(@RequestBody Core1220039001In in) {
      return (Core1220039001Out)ExecutorFlow.startGravity(in, Core1220039001Out.class);
   }
}
