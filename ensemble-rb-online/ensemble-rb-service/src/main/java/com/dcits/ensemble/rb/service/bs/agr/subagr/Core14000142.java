package com.dcits.ensemble.rb.service.bs.agr.subagr;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore14000142;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000142In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000142Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000142 implements ICore14000142 {
   private static final Logger log = LoggerFactory.getLogger(Core14000142.class);

   @CometMapping(
      value = "/rb/nfin/msa/transfer/query",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0142",
      name = "MSA账户转账明细查询"
   )
   public Core14000142Out runService(@RequestBody Core14000142In in) {
      return (Core14000142Out)ExecutorFlow.startFlow("core14000142Flow", in);
   }
}
