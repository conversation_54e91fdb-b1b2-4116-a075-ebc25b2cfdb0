package com.dcits.ensemble.rb.service.bs.bill.ad;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ad.ICore12000327;
import com.dcits.ensemble.rb.api.model.mbsdcore.ad.Core12000327In;
import com.dcits.ensemble.rb.api.model.mbsdcore.ad.Core12000327Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000327 implements ICore12000327 {
   private static final Logger log = LoggerFactory.getLogger(Core12000327.class);

   @CometMapping(
      value = "/rb/nfin/bab/ontes/changea",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0327",
      name = "银行承兑汇票票据更换"
   )
   public Core12000327Out runService(@RequestBody Core12000327In in) {
      return (Core12000327Out)ExecutorFlow.startGravity(in, Core12000327Out.class);
   }
}
