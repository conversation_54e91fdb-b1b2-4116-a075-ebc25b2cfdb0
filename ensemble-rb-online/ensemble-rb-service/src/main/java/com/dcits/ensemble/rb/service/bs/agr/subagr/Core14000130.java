package com.dcits.ensemble.rb.service.bs.agr.subagr;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000130;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000130In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000130Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000130 implements ICore14000130 {
   private static final Logger log = LoggerFactory.getLogger(Core14000130.class);

   @CometMapping(
      value = "/rb/inq/nocharge/fee/agreement",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0130",
      name = "暂不收费签约查询"
   )
   public Core14000130Out runService(@RequestBody Core14000130In in) {
      return (Core14000130Out)ExecutorFlow.startFlow("core14000130Flow", in, Core14000130Out.class);
   }
}
