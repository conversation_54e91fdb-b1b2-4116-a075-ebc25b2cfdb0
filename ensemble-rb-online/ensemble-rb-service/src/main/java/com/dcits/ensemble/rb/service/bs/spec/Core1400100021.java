package com.dcits.ensemble.rb.service.bs.spec;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1400100021;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100021In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100021Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400100021 implements ICore1400100021 {
   private static final Logger log = LoggerFactory.getLogger(Core1400100021.class);

   @CometMapping(
      value = "/rb/inq/agreement/tran/hist",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "100021",
      name = "灵活盈/储蓄定投交易历史查询"
   )
   public Core1400100021Out runService(@RequestBody Core1400100021In in) {
      return (Core1400100021Out)ExecutorFlow.startFlow("core1400100021Flow", in);
   }
}
