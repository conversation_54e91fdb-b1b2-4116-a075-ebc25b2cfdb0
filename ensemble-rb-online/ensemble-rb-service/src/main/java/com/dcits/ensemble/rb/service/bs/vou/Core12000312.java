package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12000312;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000312In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000312Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000312 implements ICore12000312 {
   private static final Logger log = LoggerFactory.getLogger(Core12000312.class);

   @CometMapping(
      value = "/rb/nfin/voucher/operate",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0312",
      name = "去/补介质"
   )
   public Core12000312Out runService(@RequestBody Core12000312In in) {
      return (Core12000312Out)ExecutorFlow.startGravity(in, Core12000312Out.class);
   }
}
