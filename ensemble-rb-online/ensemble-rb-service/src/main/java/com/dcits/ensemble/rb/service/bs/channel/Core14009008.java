package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore14009008;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core14009008In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core14009008Out;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14009008 implements ICore14009008 {
   @CometMapping(
      value = "/rb/inq/channel/acct/all",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "9008",
      name = "账户信息查询（外围专用，单账户查询多账户）"
   )
   public Core14009008Out runService(@RequestBody Core14009008In in) {
      return (Core14009008Out)ExecutorFlow.startFlow("core14009008Flow", in, Core14009008Out.class);
   }
}
