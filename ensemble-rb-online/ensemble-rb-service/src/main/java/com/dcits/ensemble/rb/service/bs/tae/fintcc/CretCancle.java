package com.dcits.ensemble.rb.service.bs.tae.fintcc;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.service.bs.finTcc.ICretCancel;
import com.dcits.ensemble.rb.service.util.tae.AsynFinancialIn;
import com.dcits.ensemble.rb.service.util.tae.AsynFinancialOut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@CometProvider
public class CretCancle implements ICretCancel {
   private static final Logger log = LoggerFactory.getLogger(CretCancle.class);

   @CometMapping(
      value = "/rb/fin/cret/cancel",
      serviceCode = "MbsdCore",
      messageType = "1500",
      messageCode = "0111"
   )
   public AsynFinancialOut runService(AsynFinancialIn in) {
      return (AsynFinancialOut)ExecutorFlow.startFlow("cretCancelTccFlow", in, AsynFinancialOut.class);
   }
}
