package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore12001120;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12001120In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12001120Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12001120 implements ICore12001120 {
   private static final Logger log = LoggerFactory.getLogger(Core12001120.class);

   @CometMapping(
      value = "/rb/nfin/voucher/court",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "1120",
      name = "已挂失凭证法院处理"
   )
   public Core12001120Out runService(@RequestBody Core12001120In in) {
      return (Core12001120Out)ExecutorFlow.startGravity(in);
   }
}
