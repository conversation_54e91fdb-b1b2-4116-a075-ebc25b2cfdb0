package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore14000115;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000115In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000115Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000115 implements ICore14000115 {
   private static final Logger log = LoggerFactory.getLogger(Core14000115.class);

   @CometMapping(
      value = "/rb/inq/acct/voucher",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0115",
      name = "通过凭证号查询对应账户的信息"
   )
   public Core14000115Out runService(@RequestBody Core14000115In in) {
      return (Core14000115Out)ExecutorFlow.startFlow("core14000115Flow", in, Core14000115Out.class);
   }
}
