package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore1200050155;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1200050155In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1200050155Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1200050155 implements ICore1200050155 {
   private static final Logger log = LoggerFactory.getLogger(Core1200050155.class);

   @CometMapping(
      value = "/rb/nfin/judicature/file/apply",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "050155",
      name = "大数据文件生成通知核心"
   )
   public Core1200050155Out runService(@RequestBody Core1200050155In in) {
      return (Core1200050155Out)ExecutorFlow.startGravity(in, Core1200050155Out.class);
   }
}
