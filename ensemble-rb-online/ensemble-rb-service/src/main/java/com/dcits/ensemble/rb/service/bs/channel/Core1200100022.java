package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1200100022;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1200100022In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1200100022Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1200100022 implements ICore1200100022 {
   private static final Logger log = LoggerFactory.getLogger(Core1200100022.class);

   @CometMapping(
      value = "/rb/nfin/channel/msg",
      name = "短信开关",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "100022"
   )
   public Core1200100022Out runService(@RequestBody Core1200100022In in) {
      return (Core1200100022Out)ExecutorFlow.startFlow("core1200100022Flow", in);
   }
}
