package com.dcits.ensemble.rb.service.bs.fee;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12000128;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000128In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000128Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000128 implements ICore12000128 {
   private static final Logger log = LoggerFactory.getLogger(Core12000128.class);

   @CometMapping(
      value = "/rb/nfin/osd/agreement/operate",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0128",
      name = "暂不收费协议操作"
   )
   public Core12000128Out runService(@RequestBody Core12000128In in) {
      return (Core12000128Out)ExecutorFlow.startGravity(in, Core12000128Out.class);
   }
}
