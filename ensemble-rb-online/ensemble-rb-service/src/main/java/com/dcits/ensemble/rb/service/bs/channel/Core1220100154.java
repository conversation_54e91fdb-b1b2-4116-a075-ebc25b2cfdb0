package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1220100154;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1220100154In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1220100154Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1220100154 implements ICore1220100154 {
   private static final Logger log = LoggerFactory.getLogger(Core1220100154.class);

   @CometMapping(
      value = "/rb/file/born/syn",
      serviceCode = "MbsdCore",
      messageType = "1220",
      messageCode = "100154",
      name = "保证金关联关系同步"
   )
   public Core1220100154Out runService(@RequestBody Core1220100154In in) {
      return (Core1220100154Out)ExecutorFlow.startGravity(in);
   }
}
