package com.dcits.ensemble.rb.service.bs.channel.accountings;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore10009406;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10009406In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10009406Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core10009406 implements ICore10009406 {
   private static final Logger log = LoggerFactory.getLogger(Core10009406.class);

   @CometMapping(
      value = "/rb/fin/inner/wtd",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "9406",
      name = "内部户现金支取"
   )
   public Core10009406Out runService(@RequestBody Core10009406In in) {
      return (Core10009406Out)ExecutorFlow.startGravity(in);
   }
}
