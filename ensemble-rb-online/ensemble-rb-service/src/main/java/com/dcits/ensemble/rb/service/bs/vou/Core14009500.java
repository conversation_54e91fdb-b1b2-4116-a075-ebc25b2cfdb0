package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14009500;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14009500In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14009500Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14009500 implements ICore14009500 {
   private static final Logger log = LoggerFactory.getLogger(Core14009500.class);

   @CometMapping(
      value = "/rb/inq/voucher/busi/list",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "9500",
      name = "凭证关联业务清单"
   )
   public Core14009500Out runService(@RequestBody Core14009500In in) {
      return (Core14009500Out)ExecutorFlow.startFlow("core14009500Flow", in, Core14009500Out.class);
   }
}
