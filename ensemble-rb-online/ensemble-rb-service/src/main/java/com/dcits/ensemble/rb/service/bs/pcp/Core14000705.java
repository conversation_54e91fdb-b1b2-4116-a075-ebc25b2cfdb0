package com.dcits.ensemble.rb.service.bs.pcp;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.pcp.ICore14000705;
import com.dcits.ensemble.rb.api.model.mbsdcore.pcp.Core14000705In;
import com.dcits.ensemble.rb.api.model.mbsdcore.pcp.Core14000705Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000705 implements ICore14000705 {
   private static final Logger log = LoggerFactory.getLogger(Core14000705.class);

   @CometMapping(
      value = "/rb/inq/pcp/group/detail",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0705",
      name = "账户组查询"
   )
   public Core14000705Out runService(@RequestBody Core14000705In in) {
      return (Core14000705Out)ExecutorFlow.startFlow("core14000705Flow", in, Core14000705Out.class);
   }
}
