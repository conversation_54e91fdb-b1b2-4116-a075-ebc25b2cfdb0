package com.dcits.ensemble.rb.service.bs.yht;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000231;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000231In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000231Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000231 implements ICore14000231 {
   private static final Logger log = LoggerFactory.getLogger(Core14000231.class);

   @CometMapping(
      value = "/rb/inq/yht/acct/all",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0231",
      name = "一户通账户信息查询"
   )
   public Core14000231Out runService(@RequestBody Core14000231In in) {
      return (Core14000231Out)ExecutorFlow.startFlow("core14000231Flow", in, Core14000231Out.class);
   }
}
