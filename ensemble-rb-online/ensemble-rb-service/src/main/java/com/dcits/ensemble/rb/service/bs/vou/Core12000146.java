package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore12000146;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000146In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000146Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000146 implements ICore12000146 {
   private static final Logger log = LoggerFactory.getLogger(Core12000146.class);

   @CometMapping(
      value = "/rb/nfin/voucher/status/cardunionmaint",
      name = "卡折一体户凭证作废"
   )
   public Core12000146Out runService(@RequestBody Core12000146In in) {
      return (Core12000146Out)ExecutorFlow.startFlow("core12000146Flow", in);
   }
}
