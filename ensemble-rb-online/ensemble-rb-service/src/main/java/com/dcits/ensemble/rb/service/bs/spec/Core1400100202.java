package com.dcits.ensemble.rb.service.bs.spec;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1400100202;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100202In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100202Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400100202 implements ICore1400100202 {
   private static final Logger log = LoggerFactory.getLogger(Core1400100202.class);

   @CometMapping(
      value = "/rb/inq/cxdt/dept/calc",
      name = "储蓄定投支取计算查询",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "100202"
   )
   public Core1400100202Out runService(@RequestBody Core1400100202In in) {
      return (Core1400100202Out)ExecutorFlow.startFlow("core1400100202Flow", in);
   }
}
