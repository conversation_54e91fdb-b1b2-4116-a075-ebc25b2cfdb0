package com.dcits.ensemble.rb.service.bs.res;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore1400055114;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400055114In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400055114Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400055114 implements ICore1400055114 {
   private static final Logger log = LoggerFactory.getLogger(Core1400055114.class);

   @CometMapping(
      value = "/rb/inq/teller/stop",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "055114",
      name = "暂停非柜面信息查询"
   )
   public Core1400055114Out runService(@RequestBody Core1400055114In in) {
      return (Core1400055114Out)ExecutorFlow.startFlow("core1400055114Flow", in, Core1400055114Out.class);
   }
}
