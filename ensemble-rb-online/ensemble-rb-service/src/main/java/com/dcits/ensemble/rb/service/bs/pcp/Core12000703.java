package com.dcits.ensemble.rb.service.bs.pcp;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.pcp.ICore12000703;
import com.dcits.ensemble.rb.api.model.mbsdcore.pcp.Core12000703In;
import com.dcits.ensemble.rb.api.model.mbsdcore.pcp.Core12000703Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000703 implements ICore12000703 {
   private static final Logger log = LoggerFactory.getLogger(Core12000703.class);

   @CometMapping(
      value = "/rb/nfin/pcp/sign",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0703",
      name = "资金池签约(提交)"
   )
   public Core12000703Out runService(@RequestBody Core12000703In in) {
      return (Core12000703Out)ExecutorFlow.startGravity(in, Core12000703Out.class);
   }
}
