package com.dcits.ensemble.rb.service.bs.fee;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000131;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000131In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000131Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000131 implements ICore14000131 {
   private static final Logger log = LoggerFactory.getLogger(Core14000131.class);

   @CometMapping(
      value = "/rb/inq/fee/arrearage",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0131",
      name = "手续费欠费查询"
   )
   public Core14000131Out runService(@RequestBody Core14000131In in) {
      return (Core14000131Out)ExecutorFlow.startFlow("core14000131Flow", in, Core14000131Out.class);
   }
}
