package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12000127;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000127In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000127Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000127 implements ICore12000127 {
   private static final Logger log = LoggerFactory.getLogger(Core12000127.class);

   @CometMapping(
      value = "/rb/nfin/voucher/lost/operate",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0127",
      name = "凭证挂失解挂"
   )
   public Core12000127Out runService(@RequestBody Core12000127In in) {
      return (Core12000127Out)ExecutorFlow.startFlow("core12000127Flow", in, Core12000127Out.class);
   }
}
