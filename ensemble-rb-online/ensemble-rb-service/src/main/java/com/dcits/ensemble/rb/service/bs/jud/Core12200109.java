package com.dcits.ensemble.rb.service.bs.jud;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12200109;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12200109In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12200109Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12200109 implements ICore12200109 {
   private static final Logger log = LoggerFactory.getLogger(Core12200109.class);

   @CometMapping(
      value = "/rb/file/reg/batch/judiciary",
      serviceCode = "MbsdCore",
      messageType = "1220",
      messageCode = "0109",
      name = "批量司法查询登记"
   )
   public Core12200109Out runService(@RequestBody Core12200109In in) {
      return (Core12200109Out)ExecutorFlow.startGravity(in, Core12200109Out.class);
   }
}
