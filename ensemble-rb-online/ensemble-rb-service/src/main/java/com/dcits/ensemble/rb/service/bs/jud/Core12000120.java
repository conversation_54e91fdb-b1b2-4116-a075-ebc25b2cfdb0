package com.dcits.ensemble.rb.service.bs.jud;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12000120;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000120In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000120Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000120 implements ICore12000120 {
   private static final Logger log = LoggerFactory.getLogger(Core12000120.class);

   @CometMapping(
      value = "/rb/nfin/judiciary/reg",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0120",
      name = "司法查询登记"
   )
   public Core12000120Out runService(@RequestBody Core12000120In in) {
      return (Core12000120Out)ExecutorFlow.startGravity(in, Core12000120Out.class);
   }
}
