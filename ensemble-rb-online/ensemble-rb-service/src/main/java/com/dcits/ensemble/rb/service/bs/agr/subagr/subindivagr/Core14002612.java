package com.dcits.ensemble.rb.service.bs.agr.subagr.subindivagr;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore14002612;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14002612In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14002612Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14002612 implements ICore14002612 {
   private static final Logger log = LoggerFactory.getLogger(Core14002612.class);

   @CometMapping(
      value = "/rb/nfin/northbound/inqury",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "2612",
      name = "北向通签约查询"
   )
   public Core14002612Out runService(@RequestBody Core14002612In in) {
      return (Core14002612Out)ExecutorFlow.startFlow("core14002612Flow", in, Core14002612Out.class);
   }
}
