package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12000408;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000408In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000408Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000408 implements ICore12000408 {
   private static final Logger log = LoggerFactory.getLogger(Core12000408.class);

   @CometMapping(
      value = "/rb/nfin/voucher/batch/relevancy",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0408",
      name = "批量开户凭证关联"
   )
   public Core12000408Out runService(@RequestBody Core12000408In in) {
      return (Core12000408Out)ExecutorFlow.startGravity(in, Core12000408Out.class);
   }
}
