package com.dcits.ensemble.rb.service.bs.fee;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000145;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000145In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000145Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000145 implements ICore14000145 {
   private static final Logger log = LoggerFactory.getLogger(Core14000145.class);

   @CometMapping(
      value = "/rb/inq/service/charge",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0145",
      name = "手续费查询"
   )
   public Core14000145Out runService(@RequestBody Core14000145In in) {
      return (Core14000145Out)ExecutorFlow.startFlow("core14000145Flow", in, Core14000145Out.class);
   }
}
