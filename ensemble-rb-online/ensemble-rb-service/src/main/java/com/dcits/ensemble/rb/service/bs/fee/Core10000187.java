package com.dcits.ensemble.rb.service.bs.fee;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10000187In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10000187Out;
import com.dcits.ensemble.rb.api.model.mbsdcore.ICore10000187;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core10000187 implements ICore10000187 {
   private static final Logger log = LoggerFactory.getLogger(Core10000187.class);

   @CometMapping(
      value = "/rb/fin/fee/pay/confirm",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "0187",
      name = "费用预提支出确认"
   )
   public Core10000187Out runService(@RequestBody Core10000187In in) {
      return (Core10000187Out)ExecutorFlow.startGravity(in);
   }
}
