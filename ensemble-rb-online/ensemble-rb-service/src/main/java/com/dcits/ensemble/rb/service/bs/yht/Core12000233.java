package com.dcits.ensemble.rb.service.bs.yht;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12000233;
import com.dcits.ensemble.rb.api.model.mbsdcore.cm.Core12000233In;
import com.dcits.ensemble.rb.api.model.mbsdcore.cm.Core12000233Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000233 implements ICore12000233 {
   private static final Logger log = LoggerFactory.getLogger(Core12000233.class);

   @CometMapping(
      value = "/rb/nfin/yht/batch/maint",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0233",
      name = "单位虚拟子账户批量维护"
   )
   public Core12000233Out runService(@RequestBody Core12000233In in) {
      return (Core12000233Out)ExecutorFlow.startGravity(in, Core12000233Out.class);
   }
}
