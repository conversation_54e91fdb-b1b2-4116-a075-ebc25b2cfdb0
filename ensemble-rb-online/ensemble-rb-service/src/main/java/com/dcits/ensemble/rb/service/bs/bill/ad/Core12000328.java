package com.dcits.ensemble.rb.service.bs.bill.ad;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ad.ICore12000328;
import com.dcits.ensemble.rb.api.model.mbsdcore.ad.Core12000328In;
import com.dcits.ensemble.rb.api.model.mbsdcore.ad.Core12000328Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000328 implements ICore12000328 {
   private static final Logger log = LoggerFactory.getLogger(Core12000328.class);

   @CometMapping(
      value = "/rb/nfin/bab/lost/operate",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0328",
      name = "银行承兑汇票挂失解挂"
   )
   public Core12000328Out runService(@RequestBody Core12000328In in) {
      return (Core12000328Out)ExecutorFlow.startGravity(in, Core12000328Out.class);
   }
}
