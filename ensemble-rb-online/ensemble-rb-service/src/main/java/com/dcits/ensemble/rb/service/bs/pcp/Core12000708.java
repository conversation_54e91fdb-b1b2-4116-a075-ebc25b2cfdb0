package com.dcits.ensemble.rb.service.bs.pcp;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore12000708;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000708In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000708Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000708 implements ICore12000708 {
   private static final Logger log = LoggerFactory.getLogger(Core12000708.class);

   @CometMapping(
      value = "/rb/nfin/pcp/agreement/deal",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0708",
      name = "资金池签约综合处理"
   )
   public Core12000708Out runService(@RequestBody Core12000708In in) {
      return (Core12000708Out)ExecutorFlow.startGravity(in);
   }
}
