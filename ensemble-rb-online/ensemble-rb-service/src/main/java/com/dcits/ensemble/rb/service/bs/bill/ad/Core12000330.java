package com.dcits.ensemble.rb.service.bs.bill.ad;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ad.ICore12000330;
import com.dcits.ensemble.rb.api.model.mbsdcore.ad.Core12000330In;
import com.dcits.ensemble.rb.api.model.mbsdcore.ad.Core12000330Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000330 implements ICore12000330 {
   private static final Logger log = LoggerFactory.getLogger(Core12000330.class);

   @CometMapping(
      value = "/rb/fin/bab/standby/prioritize",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0330",
      name = "银行承兑汇票备款账户优先顺序调整"
   )
   public Core12000330Out runService(@RequestBody Core12000330In in) {
      return (Core12000330Out)ExecutorFlow.startGravity(in, Core12000330Out.class);
   }
}
