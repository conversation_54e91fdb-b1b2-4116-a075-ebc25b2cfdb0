package com.dcits.ensemble.rb.service.bs.pcp;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.pcp.ICore12000704;
import com.dcits.ensemble.rb.api.model.mbsdcore.pcp.Core12000704In;
import com.dcits.ensemble.rb.api.model.mbsdcore.pcp.Core12000704Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000704 implements ICore12000704 {
   private static final Logger log = LoggerFactory.getLogger(Core12000704.class);

   @CometMapping(
      value = "/rb/nfin/pcp/acct/maint",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0704",
      name = "资金池签约账户信息维护（提交）"
   )
   public Core12000704Out runService(@RequestBody Core12000704In in) {
      return (Core12000704Out)ExecutorFlow.startGravity(in, Core12000704Out.class);
   }
}
