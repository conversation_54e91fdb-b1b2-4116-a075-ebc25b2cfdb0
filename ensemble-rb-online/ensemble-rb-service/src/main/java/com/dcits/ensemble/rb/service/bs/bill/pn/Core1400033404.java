package com.dcits.ensemble.rb.service.bs.bill.pn;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore1400033404;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400033404In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400033404Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400033404 implements ICore1400033404 {
   private static final Logger log = LoggerFactory.getLogger(Core1400033404.class);

   @CometMapping(
      value = "/rb/inq/pn/lost",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "033404",
      name = "银行本票挂失信息查询"
   )
   public Core1400033404Out runService(@RequestBody Core1400033404In in) {
      return (Core1400033404Out)ExecutorFlow.startFlow("core1400033404Flow", in, Core1400033404Out.class);
   }
}
