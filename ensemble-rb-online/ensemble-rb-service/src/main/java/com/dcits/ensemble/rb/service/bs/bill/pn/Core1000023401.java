package com.dcits.ensemble.rb.service.bs.bill.pn;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1000023401;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1000023401In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1000023401Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1000023401 implements ICore1000023401 {
   private static final Logger log = LoggerFactory.getLogger(Core1000023401.class);

   @CometMapping(
      value = "/rb/fin/bill/issueCud",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "023401",
      name = "本汇票签发/修改/删除"
   )
   public Core1000023401Out runService(@RequestBody Core1000023401In in) {
      return (Core1000023401Out)ExecutorFlow.startFlow("core1000023401Flow", in, Core1000023401Out.class);
   }
}
