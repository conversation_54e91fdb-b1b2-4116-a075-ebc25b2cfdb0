package com.dcits.ensemble.rb.service.bs.spec;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1000100202;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1000100202In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1000100202Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1000100202 implements ICore1000100202 {
   private static final Logger log = LoggerFactory.getLogger(Core1000100202.class);

   @CometMapping(
      value = "/rb/fin/agreement/term/withdrawal",
      name = "理财支取",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "100202"
   )
   public Core1000100202Out runService(@RequestBody Core1000100202In in) {
      return (Core1000100202Out)ExecutorFlow.startGravity(in);
   }
}
