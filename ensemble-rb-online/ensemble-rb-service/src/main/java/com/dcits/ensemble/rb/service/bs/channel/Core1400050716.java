package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore1400050716;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1400050716In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1400050716Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400050716 implements ICore1400050716 {
   private static final Logger log = LoggerFactory.getLogger(Core1400050716.class);

   @CometMapping(
      value = "/rb/inq/internal/acct",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "050716",
      name = "机构内部账户查询"
   )
   public Core1400050716Out runService(@RequestBody Core1400050716In in) {
      return (Core1400050716Out)ExecutorFlow.startFlow("core1400050716Flow", in, Core1400050716Out.class);
   }
}
