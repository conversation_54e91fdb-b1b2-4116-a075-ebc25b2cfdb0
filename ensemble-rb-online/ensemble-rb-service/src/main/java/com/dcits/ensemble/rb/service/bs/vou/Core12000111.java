package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12000111;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000111In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000111Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000111 implements ICore12000111 {
   private static final Logger log = LoggerFactory.getLogger(Core12000111.class);

   @CometMapping(
      value = "/rb/nfin/voucher/change",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0111",
      name = "凭证更换"
   )
   public Core12000111Out runService(@RequestBody Core12000111In in) {
      return (Core12000111Out)ExecutorFlow.startFlow("core12000111Flow", in, Core12000111Out.class);
   }
}
