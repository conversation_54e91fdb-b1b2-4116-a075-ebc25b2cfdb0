package com.dcits.ensemble.rb.service.bs.fee;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12000129;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000129In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000129Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000129 implements ICore12000129 {
   private static final Logger log = LoggerFactory.getLogger(Core12000129.class);

   @CometMapping(
      value = "/rb/nfin/pkg/agreement/operate",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0129",
      name = "费用套餐协议操作"
   )
   public Core12000129Out runService(@RequestBody Core12000129In in) {
      return (Core12000129Out)ExecutorFlow.startGravity(in, Core12000129Out.class);
   }
}
