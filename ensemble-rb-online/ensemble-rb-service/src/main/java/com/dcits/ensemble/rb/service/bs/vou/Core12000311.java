package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12000311;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000311In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000311Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000311 implements ICore12000311 {
   private static final Logger log = LoggerFactory.getLogger(Core12000311.class);

   @CometMapping(
      value = "/rb/nfin/voucher/remove",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0311",
      name = "去介质预约登记"
   )
   public Core12000311Out runService(@RequestBody Core12000311In in) {
      return (Core12000311Out)ExecutorFlow.startGravity(in, Core12000311Out.class);
   }
}
