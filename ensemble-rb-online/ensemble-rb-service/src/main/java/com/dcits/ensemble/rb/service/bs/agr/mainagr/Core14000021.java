package com.dcits.ensemble.rb.service.bs.agr.mainagr;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000021;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000021In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000021Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core14000021 implements ICore14000021 {
   private static final Logger log = LoggerFactory.getLogger(Core14000021.class);

   @CometMapping(
      value = "/rb/inq/agreement",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0021",
      name = "签约查询"
   )
   public Core14000021Out runService(@RequestBody Core14000021In in) {
      return (Core14000021Out)ExecutorFlow.startFlow("core14000021Flow", in, Core14000021Out.class);
   }
}
