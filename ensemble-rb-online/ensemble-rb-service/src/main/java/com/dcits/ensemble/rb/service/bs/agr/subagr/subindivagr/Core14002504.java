package com.dcits.ensemble.rb.service.bs.agr.subagr.subindivagr;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14002504;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14002504In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14002504Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14002504 implements ICore14002504 {
   private static final Logger log = LoggerFactory.getLogger(Core14002504.class);

   @CometMapping(
      value = "/rb/inq/kyd/agreement",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "2504",
      name = "活期支取活期转账交易，校验是否有卡易贷签约"
   )
   public Core14002504Out runService(@RequestBody Core14002504In in) {
      return (Core14002504Out)ExecutorFlow.startFlow("core14002504Flow", in, Core14002504Out.class);
   }
}
