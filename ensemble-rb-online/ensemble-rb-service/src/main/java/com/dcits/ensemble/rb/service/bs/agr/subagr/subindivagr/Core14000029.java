package com.dcits.ensemble.rb.service.bs.agr.subagr.subindivagr;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore14000029;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000029In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000029Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000029 implements ICore14000029 {
   private static final Logger log = LoggerFactory.getLogger(Core14000029.class);

   @CometMapping(
      value = "/rb/inq/all/agreement",
      name = "理财签约查询 "
   )
   public Core14000029Out runService(@RequestBody Core14000029In in) {
      return (Core14000029Out)ExecutorFlow.startFlow("core14000029Flow", in);
   }
}
