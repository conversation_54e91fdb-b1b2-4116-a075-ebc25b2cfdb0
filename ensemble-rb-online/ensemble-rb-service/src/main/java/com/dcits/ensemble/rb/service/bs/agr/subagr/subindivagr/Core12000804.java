package com.dcits.ensemble.rb.service.bs.agr.subagr.subindivagr;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore12000804;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000804In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000804Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000804 implements ICore12000804 {
   private static final Logger log = LoggerFactory.getLogger(Core12000804.class);

   @CometMapping(
      value = "/rb/nfin/tx/agreement/main",
      name = "同兴赢主协议签约管理",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0804"
   )
   public Core12000804Out runService(@RequestBody Core12000804In in) {
      return (Core12000804Out)ExecutorFlow.startFlow("core12000804Flow", in);
   }
}
