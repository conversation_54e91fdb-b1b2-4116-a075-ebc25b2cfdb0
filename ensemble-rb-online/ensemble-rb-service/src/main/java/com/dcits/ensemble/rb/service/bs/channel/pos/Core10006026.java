package com.dcits.ensemble.rb.service.bs.channel.pos;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.pos.ICore10006026;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core10006026In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core10006026Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core10006026 implements ICore10006026 {
   private static final Logger log = LoggerFactory.getLogger(Core10006026.class);

   @CometMapping(
      value = "/rb/fin/agent/in",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "6026",
      name = "银联转账他代本转入"
   )
   public Core10006026Out runService(@RequestBody Core10006026In in) {
      return (Core10006026Out)ExecutorFlow.startFlow("core10006026Flow", in, Core10006026Out.class);
   }
}
