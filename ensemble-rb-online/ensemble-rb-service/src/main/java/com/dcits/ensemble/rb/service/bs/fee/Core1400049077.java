package com.dcits.ensemble.rb.service.bs.fee;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore1400049077;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400049077In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400049077Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400049077 implements ICore1400049077 {
   private static final Logger log = LoggerFactory.getLogger(Core1400049077.class);

   @CometMapping(
      value = "/rb/inq/package/acct",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "049077",
      name = "费用套餐受益账户查询"
   )
   public Core1400049077Out runService(@RequestBody Core1400049077In in) {
      return (Core1400049077Out)ExecutorFlow.startFlow("core1400049077Flow", in, Core1400049077Out.class);
   }
}
