package com.dcits.ensemble.rb.service.bs.channel.pos;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.pos.ICore10006015;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core10006015In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.pos.Core10006015Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core10006015 implements ICore10006015 {
   private static final Logger log = LoggerFactory.getLogger(Core10006015.class);

   @CometMapping(
      value = "/rb/fin/channel/pos/consume/refund",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "6015",
      name = "银联POS消费退货"
   )
   public Core10006015Out runService(@RequestBody Core10006015In in) {
      return (Core10006015Out)ExecutorFlow.startFlow("core10006015Flow", in, Core10006015Out.class);
   }
}
