package com.dcits.ensemble.rb.service.bs.res;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000161;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000161In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000161Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000161 implements ICore14000161 {
   private static final Logger log = LoggerFactory.getLogger(Core14000161.class);

   @CometMapping(
      value = "/rb/inq/batch/import/result",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0161",
      name = "手工批量导入登记表结果查询"
   )
   public Core14000161Out runService(@RequestBody Core14000161In in) {
      return (Core14000161Out)ExecutorFlow.startFlow("core14000161Flow", in, Core14000161Out.class);
   }
}
