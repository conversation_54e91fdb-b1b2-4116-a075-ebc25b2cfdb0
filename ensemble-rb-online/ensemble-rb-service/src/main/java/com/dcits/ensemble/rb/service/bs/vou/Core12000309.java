package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore12000309;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000309In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000309Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000309 implements ICore12000309 {
   private static final Logger log = LoggerFactory.getLogger(Core12000309.class);

   @CometMapping(
      value = "/rb/nfin/cheque/defense",
      name = "支票协防登记/撤销"
   )
   public Core12000309Out runService(@RequestBody Core12000309In in) {
      return (Core12000309Out)ExecutorFlow.startGravity(in);
   }
}
