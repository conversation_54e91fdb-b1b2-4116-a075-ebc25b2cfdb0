package com.dcits.ensemble.rb.service.bs.bill.pn;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1000023404;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1000023404In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1000023404Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1000023404 implements ICore1000023404 {
   private static final Logger log = LoggerFactory.getLogger(Core1000023404.class);

   @CometMapping(
      value = "/rb/fin/bill/settlement",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "023404",
      name = "过期本票解付"
   )
   public Core1000023404Out runService(@RequestBody Core1000023404In in) {
      return (Core1000023404Out)ExecutorFlow.startFlow("core1000023404Flow", in, Core1000023404Out.class);
   }
}
