package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore14009009;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14009009In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14009009Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14009009 implements ICore14009009 {
   private static final Logger log = LoggerFactory.getLogger(Core14009009.class);

   @CometMapping(
      value = "/rb/inq/channel/acct/info",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "9009",
      name = "账户信息查询"
   )
   public Core14009009Out runService(@RequestBody Core14009009In in) {
      return (Core14009009Out)ExecutorFlow.startFlow("core14009009Flow", in);
   }
}
