package com.dcits.ensemble.rb.service.bs.td;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000204;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000204In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000204Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000204 implements ICore14000204 {
   private static final Logger log = LoggerFactory.getLogger(Core14000204.class);

   @CometMapping(
      value = "/rb/inq/term/hist",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0204",
      name = "定期流水查询"
   )
   public Core14000204Out runService(@RequestBody Core14000204In in) {
      return (Core14000204Out)ExecutorFlow.startFlow("core14000204Flow", in, Core14000204Out.class);
   }
}
