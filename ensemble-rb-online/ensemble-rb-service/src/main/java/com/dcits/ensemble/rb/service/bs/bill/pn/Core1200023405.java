package com.dcits.ensemble.rb.service.bs.bill.pn;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1200023405;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1200023405In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1200023405Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1200023405 implements ICore1200023405 {
   private static final Logger log = LoggerFactory.getLogger(Core1200023405.class);

   @CometMapping(
      value = "/rb/nfin/bill/orgLimit",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "023405",
      name = "机构票据交易限额维护"
   )
   public Core1200023405Out runService(@RequestBody Core1200023405In in) {
      return (Core1200023405Out)ExecutorFlow.startFlow("core1200023405Flow", in, Core1200023405Out.class);
   }
}
