package com.dcits.ensemble.rb.service.bs.yht;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000232;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000232In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000232Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000232 implements ICore14000232 {
   private static final Logger log = LoggerFactory.getLogger(Core14000232.class);

   @CometMapping(
      value = "/rb/inq/yht/hist",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0232",
      name = "一户通账户交易历史查询"
   )
   public Core14000232Out runService(@RequestBody Core14000232In in) {
      return (Core14000232Out)ExecutorFlow.startFlow("core14000232Flow", in, Core14000232Out.class);
   }
}
