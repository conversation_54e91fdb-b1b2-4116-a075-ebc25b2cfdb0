package com.dcits.ensemble.rb.service.bs.jud;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000105;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000105In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000105Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000105 implements ICore14000105 {
   private static final Logger log = LoggerFactory.getLogger(Core14000105.class);

   @CometMapping(
      value = "/rb/inq/agreement/impound",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0105",
      name = "周期性扣划信息查询"
   )
   public Core14000105Out runService(@RequestBody Core14000105In in) {
      return (Core14000105Out)ExecutorFlow.startFlow("core14000105Flow", in, Core14000105Out.class);
   }
}
