package com.dcits.ensemble.rb.service.bs.pcp;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.pcp.ICore10000701;
import com.dcits.ensemble.rb.api.model.mbsdcore.pcp.Core10000701In;
import com.dcits.ensemble.rb.api.model.mbsdcore.pcp.Core10000701Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core10000701 implements ICore10000701 {
   private static final Logger log = LoggerFactory.getLogger(Core10000701.class);

   @CometMapping(
      value = "/rb/fin/pcp/manual/impute",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "0701",
      name = "手动归集"
   )
   public Core10000701Out runService(@RequestBody Core10000701In in) {
      return (Core10000701Out)ExecutorFlow.startGravity(in, Core10000701Out.class);
   }
}
