package com.dcits.ensemble.rb.service.bs.spec.id;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.id.ICore12000801;
import com.dcits.ensemble.rb.api.model.mbsdcore.id.Core12000801In;
import com.dcits.ensemble.rb.api.model.mbsdcore.id.Core12000801Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000801 implements ICore12000801 {
   private static final Logger log = LoggerFactory.getLogger(Core12000801.class);

   @CometMapping(
      value = "/rb/nfin/id/ageement/maint",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0801",
      name = "智能存款签约/维护/解约"
   )
   public Core12000801Out runService(@RequestBody Core12000801In in) {
      return (Core12000801Out)ExecutorFlow.startGravity(in, Core12000801Out.class);
   }
}
