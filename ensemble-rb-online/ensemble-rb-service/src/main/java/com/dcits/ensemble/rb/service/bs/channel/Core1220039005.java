package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore1220039005;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1220039005In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1220039005Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core1220039005 implements ICore1220039005 {
   private static final Logger log = LoggerFactory.getLogger(Core1220039005.class);

   @CometMapping(
      value = "/rb/file/cmcd/statement/export",
      serviceCode = "MbsdCore",
      messageType = "1220",
      messageCode = "039005",
      name = "现管系统文件对账"
   )
   public Core1220039005Out runService(@RequestBody Core1220039005In in) {
      return (Core1220039005Out)ExecutorFlow.startGravity(in, Core1220039005Out.class);
   }
}
