package com.dcits.ensemble.rb.service.bs.channel.accountings;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.accounting.ICore13006039;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.accounting.Core13006039In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.accounting.Core13006039Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core13006039 implements ICore13006039 {
   private static final Logger log = LoggerFactory.getLogger(Core13006039.class);

   @CometMapping(
      value = "/rb/rev/collection",
      serviceCode = "MbsdCore",
      messageType = "1300",
      messageCode = "6039",
      name = "代收冲正"
   )
   public Core13006039Out runService(@RequestBody Core13006039In in) {
      return (Core13006039Out)ExecutorFlow.startGravity(in, Core13006039Out.class);
   }
}
