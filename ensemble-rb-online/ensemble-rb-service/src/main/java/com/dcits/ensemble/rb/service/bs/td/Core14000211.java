package com.dcits.ensemble.rb.service.bs.td;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore14000211;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000211In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000211Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000211 implements ICore14000211 {
   private static final Logger log = LoggerFactory.getLogger(Core14000211.class);

   @CometMapping(
      value = "/rb/inq/term/agre",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0211",
      name = "协议存款查询"
   )
   public Core14000211Out runService(@RequestBody Core14000211In in) {
      return (Core14000211Out)ExecutorFlow.startFlow("core14000211Flow", in);
   }
}
