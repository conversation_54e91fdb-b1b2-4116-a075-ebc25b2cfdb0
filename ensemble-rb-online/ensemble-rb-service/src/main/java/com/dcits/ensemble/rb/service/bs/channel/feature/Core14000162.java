package com.dcits.ensemble.rb.service.bs.channel.feature;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.feature.ICore14000162;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.feature.Core14000162In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.feature.Core14000162Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core14000162 implements ICore14000162 {
   private static final Logger log = LoggerFactory.getLogger(Core14000162.class);

   @CometMapping(
      value = "/rb/inq/agreement/xdbwdl/query",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0162",
      name = "协定宝/稳得利产品查询"
   )
   public Core14000162Out runService(@RequestBody Core14000162In in) {
      return (Core14000162Out)ExecutorFlow.startFlow("core14000162Flow", in, Core14000162Out.class);
   }
}
