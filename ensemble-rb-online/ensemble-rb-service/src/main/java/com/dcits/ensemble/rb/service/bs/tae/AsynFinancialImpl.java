package com.dcits.ensemble.rb.service.bs.tae;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.service.util.IAsynFinancial;
import com.dcits.ensemble.rb.service.util.tae.AsynFinancialIn;
import com.dcits.ensemble.rb.service.util.tae.AsynFinancialOut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@CometProvider
@Service
public class AsynFinancialImpl implements IAsynFinancial {
   private static final Logger log = LoggerFactory.getLogger(AsynFinancialImpl.class);

   @CometMapping(
      value = "/rb/fin/asyn/financial",
      serviceCode = "MbsdCore",
      messageType = "1500",
      messageCode = "0101",
      name = "存款异步记账统一"
   )
   public AsynFinancialOut runService(AsynFinancialIn in) {
      return (AsynFinancialOut)ExecutorFlow.startFlow("asynFinancialFlow", in, AsynFinancialOut.class);
   }
}
