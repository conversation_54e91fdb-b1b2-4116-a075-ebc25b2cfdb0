package com.dcits.ensemble.rb.service.bs.pcp;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1400100711;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100711In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100711Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400100711 implements ICore1400100711 {
   private static final Logger log = LoggerFactory.getLogger(Core1400100711.class);

   @CometMapping(
      value = "/rb/inq/pcp/file/result",
      name = "批量划拨结果查询",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "100711"
   )
   public Core1400100711Out runService(@RequestBody Core1400100711In in) {
      return (Core1400100711Out)ExecutorFlow.startFlow("core1400100711Flow", in);
   }
}
