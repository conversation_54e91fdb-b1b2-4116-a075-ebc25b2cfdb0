package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore1000053101;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1000053101In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1000053101Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1000053101 implements ICore1000053101 {
   private static final Logger log = LoggerFactory.getLogger(Core1000053101.class);

   @CometMapping(
      value = "/rb/fin/channel/finsys/operate",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "053101",
      name = "财务系统下拨、上收、付款"
   )
   public Core1000053101Out runService(@RequestBody Core1000053101In in) {
      return (Core1000053101Out)ExecutorFlow.startFlow("core1000053101Flow", in, Core1000053101Out.class);
   }
}
