package com.dcits.ensemble.rb.service.bs.channel.eb;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.eb.ICore1200050590;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.eb.Core1200050590In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.eb.Core1200050590Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core1200050590 implements ICore1200050590 {
   private static final Logger log = LoggerFactory.getLogger(Core1200050590.class);

   @CometMapping(
      value = "/rb/nfin/channel/safelock",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "050590",
      name = "安全锁设置"
   )
   public Core1200050590Out runService(@RequestBody Core1200050590In in) {
      return (Core1200050590Out)ExecutorFlow.startGravity(in, Core1200050590Out.class);
   }
}
