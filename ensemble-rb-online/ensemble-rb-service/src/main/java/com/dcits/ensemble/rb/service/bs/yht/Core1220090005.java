package com.dcits.ensemble.rb.service.bs.yht;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1220090005;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1220090005In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1220090005Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1220090005 implements ICore1220090005 {
   private static final Logger log = LoggerFactory.getLogger(Core1220090005.class);

   @CometMapping(
      value = "/rb/file/batch/open/batchopenyht",
      serviceCode = "MbsdCore",
      messageType = "1220",
      messageCode = "090005",
      name = "批量开立一户通虚子账户"
   )
   public Core1220090005Out runService(@RequestBody Core1220090005In in) {
      return (Core1220090005Out)ExecutorFlow.startFlow("core1220090005Flow", in, Core1220090005Out.class);
   }
}
