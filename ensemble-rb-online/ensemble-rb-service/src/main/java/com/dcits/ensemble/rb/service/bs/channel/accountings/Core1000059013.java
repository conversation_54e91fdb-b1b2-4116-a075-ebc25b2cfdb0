package com.dcits.ensemble.rb.service.bs.channel.accountings;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.cd.ICore1000059013;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.accounting.Core1000059013In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.accounting.Core1000059013Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core1000059013 implements ICore1000059013 {
   private static final Logger log = LoggerFactory.getLogger(Core1000059013.class);

   @CometMapping(
      value = "/rb/fin/current/unfreeze/deduct",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "059013",
      name = "解冻扣款或入账冻结"
   )
   public Core1000059013Out runService(@RequestBody Core1000059013In in) {
      return (Core1000059013Out)ExecutorFlow.startFlow("core1000059013Flow", in, Core1000059013Out.class);
   }
}
