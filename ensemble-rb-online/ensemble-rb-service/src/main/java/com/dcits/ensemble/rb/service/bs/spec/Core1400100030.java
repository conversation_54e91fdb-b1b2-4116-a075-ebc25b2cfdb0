package com.dcits.ensemble.rb.service.bs.spec;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1400100030;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100030In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1400100030Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core1400100030 implements ICore1400100030 {
   private static final Logger log = LoggerFactory.getLogger(Core1400100030.class);

   @CometMapping(
      value = "/rb/inq/invest/agreement/query",
      name = "储蓄定投协议明细查询",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "100030"
   )
   public Core1400100030Out runService(@RequestBody Core1400100030In in) {
      return (Core1400100030Out)ExecutorFlow.startFlow("core1400100030Flow", in);
   }
}
