package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore1000056029;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1000056029In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1000056029Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core1000056029 implements ICore1000056029 {
   private static final Logger log = LoggerFactory.getLogger(Core1000056029.class);

   @CometMapping(
      value = "/rb/fin/channel/otheragent/deposit/confirm",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "056029",
      name = "银联他代本存款确认"
   )
   public Core1000056029Out runService(@RequestBody Core1000056029In in) {
      return (Core1000056029Out)ExecutorFlow.startGravity(in, Core1000056029Out.class);
   }
}
