package com.dcits.ensemble.rb.service.bs.td;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12000132;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000132In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000132Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000132 implements ICore12000132 {
   private static final Logger log = LoggerFactory.getLogger(Core12000132.class);

   @CometMapping(
      value = "/rb/nfin/current/sub/open",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0132",
      name = "子账户续开"
   )
   public Core12000132Out runService(@RequestBody Core12000132In in) {
      return (Core12000132Out)ExecutorFlow.startFlow("core12000132Flow", in, Core12000132Out.class);
   }
}
