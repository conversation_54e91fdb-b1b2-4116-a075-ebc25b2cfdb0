package com.dcits.ensemble.rb.service.bs.agr.subagr.subcorpagr;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12000136;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000136In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000136Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000136 implements ICore12000136 {
   private static final Logger log = LoggerFactory.getLogger(Core12000136.class);

   @CometMapping(
      value = "/rb/nfin/acc/agreement/operate",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0136",
      name = "协定协议操作"
   )
   public Core12000136Out runService(@RequestBody Core12000136In in) {
      return (Core12000136Out)ExecutorFlow.startFlow("core12000136Flow", in, Core12000136Out.class);
   }
}
