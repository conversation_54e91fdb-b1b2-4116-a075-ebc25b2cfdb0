package com.dcits.ensemble.rb.service.bs.agr.subagr.subindivagr;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore12000144;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000144In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000144Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12000144 implements ICore12000144 {
   private static final Logger log = LoggerFactory.getLogger(Core12000144.class);

   @CometMapping(
      value = "/rb/nfin/msa/agreement/operate",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0144",
      name = "MSA账户定期转账协议操作"
   )
   public Core12000144Out runService(@RequestBody Core12000144In in) {
      return (Core12000144Out)ExecutorFlow.startGravity(in);
   }
}
