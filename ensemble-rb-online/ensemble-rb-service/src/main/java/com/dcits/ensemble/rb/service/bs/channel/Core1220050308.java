package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore1220050308;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1220050308In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1220050308Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core1220050308 implements ICore1220050308 {
   private static final Logger log = LoggerFactory.getLogger(Core1220050308.class);

   @CometMapping(
      value = "/rb/file/current/unfreeze/operate",
      serviceCode = "MbsdCore",
      messageType = "1220",
      messageCode = "050308",
      name = "批量解冻扣款"
   )
   public Core1220050308Out runService(@RequestBody Core1220050308In in) {
      return (Core1220050308Out)ExecutorFlow.startFlow("core1220050308Flow", in, Core1220050308Out.class);
   }
}
