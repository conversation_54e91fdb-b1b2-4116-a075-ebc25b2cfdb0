package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore1200058003;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1200058003In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1200058003Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core1200058003 implements ICore1200058003 {
   private static final Logger log = LoggerFactory.getLogger(Core1200058003.class);

   @CometMapping(
      value = "/rb/nfin/channel/voucher/sell",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "058003",
      name = "凭证出售（外围专用）"
   )
   public Core1200058003Out runService(@RequestBody Core1200058003In in) {
      return (Core1200058003Out)ExecutorFlow.startGravity(in, Core1200058003Out.class);
   }
}
