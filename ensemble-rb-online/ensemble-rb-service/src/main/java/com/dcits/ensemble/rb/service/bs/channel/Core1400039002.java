package com.dcits.ensemble.rb.service.bs.channel;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.ICore1400039002;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1400039002In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.Core1400039002Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core1400039002 implements ICore1400039002 {
   private static final Logger log = LoggerFactory.getLogger(Core1400039002.class);

   @CometMapping(
      value = "/rb/inq/agreement/unified",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "039002",
      name = "统一签约详细信息查询"
   )
   public Core1400039002Out runService(@RequestBody Core1400039002In in) {
      return (Core1400039002Out)ExecutorFlow.startFlow("core1400039002Flow", in, Core1400039002Out.class);
   }
}
