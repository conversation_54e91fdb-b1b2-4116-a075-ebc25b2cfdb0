package com.dcits.ensemble.rb.service.bs.channel.feature;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.channel.feature.ICore14000163;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.feature.Core14000163In;
import com.dcits.ensemble.rb.api.model.mbsdcore.channel.feature.Core14000163Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core14000163 implements ICore14000163 {
   private static final Logger log = LoggerFactory.getLogger(Core14000163.class);

   @CometMapping(
      value = "/rb/inq/agreement/product/query",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0163",
      name = "特色签约产品查询"
   )
   public Core14000163Out runService(@RequestBody Core14000163In in) {
      return (Core14000163Out)ExecutorFlow.startFlow("core14000163Flow", in, Core14000163Out.class);
   }
}
