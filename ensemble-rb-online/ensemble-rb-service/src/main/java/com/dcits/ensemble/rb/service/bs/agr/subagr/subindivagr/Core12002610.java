package com.dcits.ensemble.rb.service.bs.agr.subagr.subindivagr;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore12002610;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12002610In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12002610Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core12002610 implements ICore12002610 {
   private static final Logger log = LoggerFactory.getLogger(Core12002610.class);

   @CometMapping(
      value = "/rb/nfin/northbound/sign",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "2610",
      name = "北向通签约"
   )
   public Core12002610Out runService(@RequestBody Core12002610In in) {
      return (Core12002610Out)ExecutorFlow.startGravity(in, Core12002610Out.class);
   }
}
