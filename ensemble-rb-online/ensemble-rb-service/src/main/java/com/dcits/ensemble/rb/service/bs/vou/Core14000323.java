package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore14000323;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000323In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000323Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core14000323 implements ICore14000323 {
   private static final Logger log = LoggerFactory.getLogger(Core14000323.class);

   @CometMapping(
      value = "/rb/inq/voucher/precise",
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0323",
      name = "凭证查询（详细条件）"
   )
   public Core14000323Out runService(@RequestBody Core14000323In in) {
      return (Core14000323Out)ExecutorFlow.startFlow("core14000323Flow", in, Core14000323Out.class);
   }
}
