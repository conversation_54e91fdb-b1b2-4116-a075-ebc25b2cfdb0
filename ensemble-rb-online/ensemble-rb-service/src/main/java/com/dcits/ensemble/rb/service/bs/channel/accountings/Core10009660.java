package com.dcits.ensemble.rb.service.bs.channel.accountings;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore10009660;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10009660In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10009660Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core10009660 implements ICore10009660 {
   private static final Logger log = LoggerFactory.getLogger(Core10009660.class);

   @CometMapping(
      value = "/rb/fin/channel/common/gjaccount",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "9660",
      name = "国结通用记账"
   )
   public Core10009660Out runService(@RequestBody Core10009660In in) {
      return (Core10009660Out)ExecutorFlow.startGravity(in);
   }
}
