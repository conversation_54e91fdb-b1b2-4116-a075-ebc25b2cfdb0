package com.dcits.ensemble.rb.service.bs.vou;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore12001119;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12001119In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12001119Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;

@CometProvider
public class Core12001119 implements ICore12001119 {
   private static final Logger log = LoggerFactory.getLogger(Core12001119.class);

   @CometMapping(
      value = "/rb/nfin/voucher/sale/cancle",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "1119",
      name = "凭证取消出售"
   )
   public Core12001119Out runService(@RequestBody Core12001119In in) {
      return (Core12001119Out)ExecutorFlow.startFlow("core12001119Flow", in, Core12001119Out.class);
   }
}
