package com.dcits.ensemble.rb.service.bs.res;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.model.ICore1200100103;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1200100103In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core1200100103Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core1200100103 implements ICore1200100103 {
   private static final Logger log = LoggerFactory.getLogger(Core1200100103.class);

   @CometMapping(
      value = "/rb/nfin/acct/bail/restraint",
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "100103",
      name = "存单账户限制"
   )
   public Core1200100103Out runService(@RequestBody Core1200100103In in) {
      return (Core1200100103Out)ExecutorFlow.startFlow("core1200100103Flow", in, Core1200100103Out.class);
   }
}
