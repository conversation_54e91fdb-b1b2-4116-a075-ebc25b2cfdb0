package com.dcits.ensemble.rb.service.bs.fee;

import com.dcits.comet.flow.ExecutorFlow;
import com.dcits.comet.rpc.api.annotation.CometMapping;
import com.dcits.comet.rpc.provider.api.annotation.CometProvider;
import com.dcits.ensemble.rb.api.bs.ICore10000104;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10000104In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10000104Out;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@CometProvider
public class Core10000104 implements ICore10000104 {
   private static final Logger log = LoggerFactory.getLogger(Core10000104.class);

   @CometMapping(
      value = "/rb/fin/fee/charge",
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "0104",
      name = "手续费收取"
   )
   public Core10000104Out runService(@RequestBody Core10000104In in) {
      return (Core10000104Out)ExecutorFlow.startGravity(in, Core10000104Out.class);
   }
}
