package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400100014Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100014Out.AcctArray> acctArray;

   public List<Core1400100014Out.AcctArray> getAcctArray() {
      return this.acctArray;
   }

   public void setAcctArray(List<Core1400100014Out.AcctArray> acctArray) {
      this.acctArray = acctArray;
   }

   public String toString() {
      return "Core1400100014Out(acctArray=" + this.getAcctArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100014Out)) {
         return false;
      } else {
         Core1400100014Out other = (Core1400100014Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$acctArray = this.getAcctArray();
            Object other$acctArray = other.getAcctArray();
            if (this$acctArray == null) {
               if (other$acctArray != null) {
                  return false;
               }
            } else if (!this$acctArray.equals(other$acctArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100014Out;
   }
   public static class AcctArray {
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "序号",
         notNull = false,
         length = "50",
         remark = "序号",
         maxSize = 50
      )
      private String seqNo;
      @V(
         desc = "账户名称",
         notNull = false,
         length = "200",
         remark = "账户名称，一般指中文账户名称",
         maxSize = 200
      )
      private String acctName;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "余额",
         notNull = false,
         length = "17",
         remark = "余额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal balance;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "存期期限",
         notNull = false,
         length = "5",
         remark = "期限",
         maxSize = 5
      )
      private String term;
      @V(
         desc = "期限类型",
         notNull = false,
         length = "1",
         remark = "期限类型",
         maxSize = 1
      )
      private String termType;
      @V(
         desc = "凭证号码",
         notNull = false,
         length = "50",
         remark = "凭证号码",
         maxSize = 50
      )
      private String voucherNo;
      @V(
         desc = "账户开户日期",
         notNull = false,
         remark = "账户开户日期"
      )
      private String acctOpenDate;
      @V(
         desc = "销户日期",
         notNull = false,
         remark = "账户销户日期"
      )
      private String acctCloseDate;
      @V(
         desc = "账户状态",
         notNull = false,
         length = "1",
         remark = "描述账户生命周期不同阶段的划分",
         maxSize = 1
      )
      private String acctStatus;
      @V(
         desc = "余额类型",
         notNull = false,
         length = "2",
         remark = "余额类型",
         maxSize = 2
      )
      private String balType;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getSeqNo() {
         return this.seqNo;
      }

      public String getAcctName() {
         return this.acctName;
      }

      public String getCcy() {
         return this.ccy;
      }

      public BigDecimal getBalance() {
         return this.balance;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getTerm() {
         return this.term;
      }

      public String getTermType() {
         return this.termType;
      }

      public String getVoucherNo() {
         return this.voucherNo;
      }

      public String getAcctOpenDate() {
         return this.acctOpenDate;
      }

      public String getAcctCloseDate() {
         return this.acctCloseDate;
      }

      public String getAcctStatus() {
         return this.acctStatus;
      }

      public String getBalType() {
         return this.balType;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setSeqNo(String seqNo) {
         this.seqNo = seqNo;
      }

      public void setAcctName(String acctName) {
         this.acctName = acctName;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setBalance(BigDecimal balance) {
         this.balance = balance;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setTerm(String term) {
         this.term = term;
      }

      public void setTermType(String termType) {
         this.termType = termType;
      }

      public void setVoucherNo(String voucherNo) {
         this.voucherNo = voucherNo;
      }

      public void setAcctOpenDate(String acctOpenDate) {
         this.acctOpenDate = acctOpenDate;
      }

      public void setAcctCloseDate(String acctCloseDate) {
         this.acctCloseDate = acctCloseDate;
      }

      public void setAcctStatus(String acctStatus) {
         this.acctStatus = acctStatus;
      }

      public void setBalType(String balType) {
         this.balType = balType;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100014Out.AcctArray)) {
            return false;
         } else {
            Core1400100014Out.AcctArray other = (Core1400100014Out.AcctArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label167: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label167;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label167;
                  }

                  return false;
               }

               Object this$seqNo = this.getSeqNo();
               Object other$seqNo = other.getSeqNo();
               if (this$seqNo == null) {
                  if (other$seqNo != null) {
                     return false;
                  }
               } else if (!this$seqNo.equals(other$seqNo)) {
                  return false;
               }

               label153: {
                  Object this$acctName = this.getAcctName();
                  Object other$acctName = other.getAcctName();
                  if (this$acctName == null) {
                     if (other$acctName == null) {
                        break label153;
                     }
                  } else if (this$acctName.equals(other$acctName)) {
                     break label153;
                  }

                  return false;
               }

               Object this$ccy = this.getCcy();
               Object other$ccy = other.getCcy();
               if (this$ccy == null) {
                  if (other$ccy != null) {
                     return false;
                  }
               } else if (!this$ccy.equals(other$ccy)) {
                  return false;
               }

               label139: {
                  Object this$balance = this.getBalance();
                  Object other$balance = other.getBalance();
                  if (this$balance == null) {
                     if (other$balance == null) {
                        break label139;
                     }
                  } else if (this$balance.equals(other$balance)) {
                     break label139;
                  }

                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               label125: {
                  Object this$term = this.getTerm();
                  Object other$term = other.getTerm();
                  if (this$term == null) {
                     if (other$term == null) {
                        break label125;
                     }
                  } else if (this$term.equals(other$term)) {
                     break label125;
                  }

                  return false;
               }

               label118: {
                  Object this$termType = this.getTermType();
                  Object other$termType = other.getTermType();
                  if (this$termType == null) {
                     if (other$termType == null) {
                        break label118;
                     }
                  } else if (this$termType.equals(other$termType)) {
                     break label118;
                  }

                  return false;
               }

               Object this$voucherNo = this.getVoucherNo();
               Object other$voucherNo = other.getVoucherNo();
               if (this$voucherNo == null) {
                  if (other$voucherNo != null) {
                     return false;
                  }
               } else if (!this$voucherNo.equals(other$voucherNo)) {
                  return false;
               }

               label104: {
                  Object this$acctOpenDate = this.getAcctOpenDate();
                  Object other$acctOpenDate = other.getAcctOpenDate();
                  if (this$acctOpenDate == null) {
                     if (other$acctOpenDate == null) {
                        break label104;
                     }
                  } else if (this$acctOpenDate.equals(other$acctOpenDate)) {
                     break label104;
                  }

                  return false;
               }

               label97: {
                  Object this$acctCloseDate = this.getAcctCloseDate();
                  Object other$acctCloseDate = other.getAcctCloseDate();
                  if (this$acctCloseDate == null) {
                     if (other$acctCloseDate == null) {
                        break label97;
                     }
                  } else if (this$acctCloseDate.equals(other$acctCloseDate)) {
                     break label97;
                  }

                  return false;
               }

               Object this$acctStatus = this.getAcctStatus();
               Object other$acctStatus = other.getAcctStatus();
               if (this$acctStatus == null) {
                  if (other$acctStatus != null) {
                     return false;
                  }
               } else if (!this$acctStatus.equals(other$acctStatus)) {
                  return false;
               }

               Object this$balType = this.getBalType();
               Object other$balType = other.getBalType();
               if (this$balType == null) {
                  if (other$balType != null) {
                     return false;
                  }
               } else if (!this$balType.equals(other$balType)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100014Out.AcctArray;
      }
      public String toString() {
         return "Core1400100014Out.AcctArray(baseAcctNo=" + this.getBaseAcctNo() + ", seqNo=" + this.getSeqNo() + ", acctName=" + this.getAcctName() + ", ccy=" + this.getCcy() + ", balance=" + this.getBalance() + ", prodType=" + this.getProdType() + ", term=" + this.getTerm() + ", termType=" + this.getTermType() + ", voucherNo=" + this.getVoucherNo() + ", acctOpenDate=" + this.getAcctOpenDate() + ", acctCloseDate=" + this.getAcctCloseDate() + ", acctStatus=" + this.getAcctStatus() + ", balType=" + this.getBalType() + ")";
      }
   }
}
