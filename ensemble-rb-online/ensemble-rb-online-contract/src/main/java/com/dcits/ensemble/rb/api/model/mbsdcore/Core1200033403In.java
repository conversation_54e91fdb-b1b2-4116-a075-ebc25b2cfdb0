package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@MessageIn
public class Core1200033403In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1200033403In.Body body;

   public Core1200033403In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1200033403In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1200033403In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200033403In)) {
         return false;
      } else {
         Core1200033403In other = (Core1200033403In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200033403In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "办理人姓名",
         notNull = false,
         length = "200",
         remark = "办理人姓名",
         maxSize = 200
      )
      private String agentName;
      @V(
         desc = "发行日期",
         notNull = false,
         remark = "发行日期"
      )
      private String issueDate;
      @V(
         desc = "签发行行名",
         notNull = false,
         length = "50",
         remark = "签发行行名",
         maxSize = 50
      )
      private String issueBankName;
      @V(
         desc = "所属机构号",
         notNull = false,
         length = "50",
         remark = "机构代码",
         maxSize = 50
      )
      private String branch;
      @V(
         desc = "签发行行号",
         notNull = false,
         length = "20",
         remark = "签发行行号",
         maxSize = 20
      )
      private String issueBankNo;
      @V(
         desc = "挂失编号",
         notNull = false,
         length = "50",
         remark = "挂失编号",
         maxSize = 50
      )
      private String lostNo;
      @V(
         desc = "挂失申请书编号",
         notNull = false,
         length = "50",
         remark = "挂失申请书编号",
         maxSize = 50
      )
      private String lossNo;
      @V(
         desc = "原业务流水号",
         notNull = false,
         length = "50",
         remark = "原签发流水号",
         maxSize = 50
      )
      private String origSerialNo;
      @V(
         desc = "票面金额",
         notNull = false,
         length = "17",
         remark = "票面金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal billAmt;
      @V(
         desc = "挂失人证件类型",
         notNull = false,
         length = "3",
         remark = "挂失人证件类型",
         maxSize = 3
      )
      private String lostDocumentType;
      @V(
         desc = "解挂人证件类型",
         notNull = false,
         length = "3",
         remark = "解挂人证件类型",
         maxSize = 3
      )
      private String unlostDocumentType;
      @V(
         desc = "票据挂失解挂操作类型",
         notNull = true,
         length = "1",
         in = "0,1",
         inDesc = "0-挂失,1-解挂",
         remark = "票据挂失解挂操作类型",
         maxSize = 1
      )
      private String billLostUnlostOperateType;
      @V(
         desc = "申请人产品类型",
         notNull = false,
         length = "20",
         remark = "申请人产品类型",
         maxSize = 20
      )
      private String applyerProdeType;
      @V(
         desc = "代理标志",
         notNull = false,
         length = "1",
         in = "Y,N",
         inDesc = "Y-是,N-否",
         remark = "代理标志",
         maxSize = 1
      )
      private String agentFlag;
      @V(
         desc = "生效标识",
         notNull = false,
         length = "1",
         in = "Y,N",
         inDesc = "Y-是,N-否",
         remark = "生效标识",
         maxSize = 1
      )
      private String tranValidFlag;
      @V(
         desc = "票据类型",
         notNull = false,
         length = "5",
         in = "P,E",
         inDesc = "P-纸质,E-电子,CT00-可转让汇票,CT01-不可转让汇票,CT02-现金汇票",
         remark = "票据类型",
         maxSize = 5
      )
      private String billType;
      @V(
         desc = "签发标识",
         notNull = false,
         length = "1",
         in = "0,1",
         inDesc = "0-本行签发,1-他行签发",
         remark = "签发标识",
         maxSize = 1
      )
      private String issueFlag;
      @V(
         desc = "收款人账户产品类型",
         notNull = false,
         length = "20",
         remark = "收款人账户产品类型",
         maxSize = 20
      )
      private String payeeProdType;
      @V(
         desc = "申请人账号",
         notNull = false,
         length = "50",
         remark = "申请人账号",
         maxSize = 50
      )
      private String applyerBaseAcctNo;
      @V(
         desc = "收款人账户",
         notNull = false,
         length = "50",
         remark = "收款人账户",
         maxSize = 50
      )
      private String payeeAcctNo;
      @V(
         desc = "申请人账户币种",
         notNull = false,
         length = "3",
         remark = "申请人账户币种",
         maxSize = 3
      )
      private String applyerAcctCcy;
      @V(
         desc = "收款人账户币种",
         notNull = false,
         length = "3",
         remark = "收款人账户币种",
         maxSize = 3
      )
      private String payeeAcctCcy;
      @V(
         desc = "申请人账户序列号",
         notNull = false,
         length = "5",
         remark = "申请人账户序列号",
         maxSize = 5
      )
      private String applyerAcctSeqNo;
      @V(
         desc = "收款人账户序列号",
         notNull = false,
         length = "5",
         remark = "收款人账户序列号",
         maxSize = 5
      )
      private String payeeAcctSeqNo;
      @V(
         desc = "申请人名称",
         notNull = false,
         length = "200",
         remark = "申请人名称",
         maxSize = 200
      )
      private String applyerAcctName;
      @V(
         desc = "收款人名称",
         notNull = false,
         length = "200",
         remark = "收款人名称",
         maxSize = 200
      )
      private String payeeAcctName;
      @V(
         desc = "挂失人证件号码",
         notNull = false,
         length = "50",
         remark = "挂失人证件号码",
         maxSize = 50
      )
      private String lostDocumentId;
      @V(
         desc = "解挂人姓名",
         notNull = false,
         length = "200",
         remark = "解挂人姓名",
         maxSize = 200
      )
      private String unlostName;
      @V(
         desc = "解挂人证件号码",
         notNull = false,
         length = "50",
         remark = "解挂人证件号码",
         maxSize = 50
      )
      private String unlostDocumentId;
      @V(
         desc = "挂失人名称",
         notNull = false,
         length = "200",
         remark = "挂失人名称",
         maxSize = 200
      )
      private String lostName;
      @V(
         desc = "挂失人联系电话",
         notNull = false,
         length = "20",
         remark = "挂失人联系电话",
         maxSize = 20
      )
      private String lostTelNo;
      @V(
         desc = "本票丧失地点",
         notNull = false,
         length = "500",
         remark = "本票丧失地点",
         maxSize = 500
      )
      private String billLostAddr;
      @V(
         desc = "营业场所住所",
         notNull = false,
         length = "500",
         remark = "营业场所住所",
         maxSize = 500
      )
      private String busiPlace;
      @V(
         desc = "挂失原因",
         notNull = false,
         length = "200",
         remark = "挂失原因",
         maxSize = 200
      )
      private String lostReason;
      @V(
         desc = "挂失状态",
         notNull = false,
         length = "1",
         in = "0,1,2",
         inDesc = "0-正常,1-书面挂失,2-口头挂失 ,3-已挂失,4-已解挂",
         remark = "挂失状态",
         maxSize = 1
      )
      private String lostStatus;
      @V(
         desc = "解挂人联系电话",
         notNull = false,
         length = "20",
         remark = "解挂人联系电话",
         maxSize = 20
      )
      private String unlostTelNo;
      @V(
         desc = "解挂原因",
         notNull = false,
         length = "200",
         remark = "解挂原因",
         maxSize = 200
      )
      private String unlostReason;
      @V(
         desc = "票据号码",
         notNull = false,
         length = "50",
         remark = "票据号码",
         maxSize = 50
      )
      private String billNo;
      @V(
         desc = "挂失柜员",
         notNull = false,
         length = "30",
         remark = "挂失柜员",
         maxSize = 30
      )
      private String lostUserId;
      @V(
         desc = "解挂柜员",
         notNull = false,
         length = "30",
         remark = "解挂柜员",
         maxSize = 30
      )
      private String unlostUserId;
      @V(
         desc = "本票丧失时间",
         notNull = false,
         length = "26",
         remark = "本票丧失时间",
         maxSize = 26
      )
      private String billLostTime;
      @V(
         desc = "挂失时间",
         notNull = false,
         length = "26",
         remark = "挂失时间",
         maxSize = 26
      )
      private String lostTime;
      @V(
         desc = "解挂时间",
         notNull = false,
         length = "26",
         remark = "解挂时间",
         maxSize = 26
      )
      private String unlostTime;
      @V(
         desc = "是否代办人",
         notNull = false,
         length = "1",
         in = "Y,N",
         inDesc = "Y-是,N-否",
         remark = "是否代办人",
         maxSize = 1
      )
      private String isCommission;
      @V(
         desc = "办理人证件类型",
         notNull = false,
         length = "3",
         remark = "办理人证件类型",
         maxSize = 3
      )
      private String agentDocumentType;
      @V(
         desc = "办理人证件号码",
         notNull = false,
         length = "50",
         remark = "办理人证件号码",
         maxSize = 50
      )
      private String agentDocumentId;
      @V(
         desc = "数组",
         notNull = false,
         remark = "数组"
      )
      private List<Core1200033403In.Body.ServArray> servArray;

      public String getAgentName() {
         return this.agentName;
      }

      public String getIssueDate() {
         return this.issueDate;
      }

      public String getIssueBankName() {
         return this.issueBankName;
      }

      public String getBranch() {
         return this.branch;
      }

      public String getIssueBankNo() {
         return this.issueBankNo;
      }

      public String getLostNo() {
         return this.lostNo;
      }

      public String getLossNo() {
         return this.lossNo;
      }

      public String getOrigSerialNo() {
         return this.origSerialNo;
      }

      public BigDecimal getBillAmt() {
         return this.billAmt;
      }

      public String getLostDocumentType() {
         return this.lostDocumentType;
      }

      public String getUnlostDocumentType() {
         return this.unlostDocumentType;
      }

      public String getBillLostUnlostOperateType() {
         return this.billLostUnlostOperateType;
      }

      public String getApplyerProdeType() {
         return this.applyerProdeType;
      }

      public String getAgentFlag() {
         return this.agentFlag;
      }

      public String getTranValidFlag() {
         return this.tranValidFlag;
      }

      public String getBillType() {
         return this.billType;
      }

      public String getIssueFlag() {
         return this.issueFlag;
      }

      public String getPayeeProdType() {
         return this.payeeProdType;
      }

      public String getApplyerBaseAcctNo() {
         return this.applyerBaseAcctNo;
      }

      public String getPayeeAcctNo() {
         return this.payeeAcctNo;
      }

      public String getApplyerAcctCcy() {
         return this.applyerAcctCcy;
      }

      public String getPayeeAcctCcy() {
         return this.payeeAcctCcy;
      }

      public String getApplyerAcctSeqNo() {
         return this.applyerAcctSeqNo;
      }

      public String getPayeeAcctSeqNo() {
         return this.payeeAcctSeqNo;
      }

      public String getApplyerAcctName() {
         return this.applyerAcctName;
      }

      public String getPayeeAcctName() {
         return this.payeeAcctName;
      }

      public String getLostDocumentId() {
         return this.lostDocumentId;
      }

      public String getUnlostName() {
         return this.unlostName;
      }

      public String getUnlostDocumentId() {
         return this.unlostDocumentId;
      }

      public String getLostName() {
         return this.lostName;
      }

      public String getLostTelNo() {
         return this.lostTelNo;
      }

      public String getBillLostAddr() {
         return this.billLostAddr;
      }

      public String getBusiPlace() {
         return this.busiPlace;
      }

      public String getLostReason() {
         return this.lostReason;
      }

      public String getLostStatus() {
         return this.lostStatus;
      }

      public String getUnlostTelNo() {
         return this.unlostTelNo;
      }

      public String getUnlostReason() {
         return this.unlostReason;
      }

      public String getBillNo() {
         return this.billNo;
      }

      public String getLostUserId() {
         return this.lostUserId;
      }

      public String getUnlostUserId() {
         return this.unlostUserId;
      }

      public String getBillLostTime() {
         return this.billLostTime;
      }

      public String getLostTime() {
         return this.lostTime;
      }

      public String getUnlostTime() {
         return this.unlostTime;
      }

      public String getIsCommission() {
         return this.isCommission;
      }

      public String getAgentDocumentType() {
         return this.agentDocumentType;
      }

      public String getAgentDocumentId() {
         return this.agentDocumentId;
      }

      public List<Core1200033403In.Body.ServArray> getServArray() {
         return this.servArray;
      }

      public void setAgentName(String agentName) {
         this.agentName = agentName;
      }

      public void setIssueDate(String issueDate) {
         this.issueDate = issueDate;
      }

      public void setIssueBankName(String issueBankName) {
         this.issueBankName = issueBankName;
      }

      public void setBranch(String branch) {
         this.branch = branch;
      }

      public void setIssueBankNo(String issueBankNo) {
         this.issueBankNo = issueBankNo;
      }

      public void setLostNo(String lostNo) {
         this.lostNo = lostNo;
      }

      public void setLossNo(String lossNo) {
         this.lossNo = lossNo;
      }

      public void setOrigSerialNo(String origSerialNo) {
         this.origSerialNo = origSerialNo;
      }

      public void setBillAmt(BigDecimal billAmt) {
         this.billAmt = billAmt;
      }

      public void setLostDocumentType(String lostDocumentType) {
         this.lostDocumentType = lostDocumentType;
      }

      public void setUnlostDocumentType(String unlostDocumentType) {
         this.unlostDocumentType = unlostDocumentType;
      }

      public void setBillLostUnlostOperateType(String billLostUnlostOperateType) {
         this.billLostUnlostOperateType = billLostUnlostOperateType;
      }

      public void setApplyerProdeType(String applyerProdeType) {
         this.applyerProdeType = applyerProdeType;
      }

      public void setAgentFlag(String agentFlag) {
         this.agentFlag = agentFlag;
      }

      public void setTranValidFlag(String tranValidFlag) {
         this.tranValidFlag = tranValidFlag;
      }

      public void setBillType(String billType) {
         this.billType = billType;
      }

      public void setIssueFlag(String issueFlag) {
         this.issueFlag = issueFlag;
      }

      public void setPayeeProdType(String payeeProdType) {
         this.payeeProdType = payeeProdType;
      }

      public void setApplyerBaseAcctNo(String applyerBaseAcctNo) {
         this.applyerBaseAcctNo = applyerBaseAcctNo;
      }

      public void setPayeeAcctNo(String payeeAcctNo) {
         this.payeeAcctNo = payeeAcctNo;
      }

      public void setApplyerAcctCcy(String applyerAcctCcy) {
         this.applyerAcctCcy = applyerAcctCcy;
      }

      public void setPayeeAcctCcy(String payeeAcctCcy) {
         this.payeeAcctCcy = payeeAcctCcy;
      }

      public void setApplyerAcctSeqNo(String applyerAcctSeqNo) {
         this.applyerAcctSeqNo = applyerAcctSeqNo;
      }

      public void setPayeeAcctSeqNo(String payeeAcctSeqNo) {
         this.payeeAcctSeqNo = payeeAcctSeqNo;
      }

      public void setApplyerAcctName(String applyerAcctName) {
         this.applyerAcctName = applyerAcctName;
      }

      public void setPayeeAcctName(String payeeAcctName) {
         this.payeeAcctName = payeeAcctName;
      }

      public void setLostDocumentId(String lostDocumentId) {
         this.lostDocumentId = lostDocumentId;
      }

      public void setUnlostName(String unlostName) {
         this.unlostName = unlostName;
      }

      public void setUnlostDocumentId(String unlostDocumentId) {
         this.unlostDocumentId = unlostDocumentId;
      }

      public void setLostName(String lostName) {
         this.lostName = lostName;
      }

      public void setLostTelNo(String lostTelNo) {
         this.lostTelNo = lostTelNo;
      }

      public void setBillLostAddr(String billLostAddr) {
         this.billLostAddr = billLostAddr;
      }

      public void setBusiPlace(String busiPlace) {
         this.busiPlace = busiPlace;
      }

      public void setLostReason(String lostReason) {
         this.lostReason = lostReason;
      }

      public void setLostStatus(String lostStatus) {
         this.lostStatus = lostStatus;
      }

      public void setUnlostTelNo(String unlostTelNo) {
         this.unlostTelNo = unlostTelNo;
      }

      public void setUnlostReason(String unlostReason) {
         this.unlostReason = unlostReason;
      }

      public void setBillNo(String billNo) {
         this.billNo = billNo;
      }

      public void setLostUserId(String lostUserId) {
         this.lostUserId = lostUserId;
      }

      public void setUnlostUserId(String unlostUserId) {
         this.unlostUserId = unlostUserId;
      }

      public void setBillLostTime(String billLostTime) {
         this.billLostTime = billLostTime;
      }

      public void setLostTime(String lostTime) {
         this.lostTime = lostTime;
      }

      public void setUnlostTime(String unlostTime) {
         this.unlostTime = unlostTime;
      }

      public void setIsCommission(String isCommission) {
         this.isCommission = isCommission;
      }

      public void setAgentDocumentType(String agentDocumentType) {
         this.agentDocumentType = agentDocumentType;
      }

      public void setAgentDocumentId(String agentDocumentId) {
         this.agentDocumentId = agentDocumentId;
      }

      public void setServArray(List<Core1200033403In.Body.ServArray> servArray) {
         this.servArray = servArray;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1200033403In.Body)) {
            return false;
         } else {
            Core1200033403In.Body other = (Core1200033403In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label575: {
                  Object this$agentName = this.getAgentName();
                  Object other$agentName = other.getAgentName();
                  if (this$agentName == null) {
                     if (other$agentName == null) {
                        break label575;
                     }
                  } else if (this$agentName.equals(other$agentName)) {
                     break label575;
                  }

                  return false;
               }

               Object this$issueDate = this.getIssueDate();
               Object other$issueDate = other.getIssueDate();
               if (this$issueDate == null) {
                  if (other$issueDate != null) {
                     return false;
                  }
               } else if (!this$issueDate.equals(other$issueDate)) {
                  return false;
               }

               Object this$issueBankName = this.getIssueBankName();
               Object other$issueBankName = other.getIssueBankName();
               if (this$issueBankName == null) {
                  if (other$issueBankName != null) {
                     return false;
                  }
               } else if (!this$issueBankName.equals(other$issueBankName)) {
                  return false;
               }

               label554: {
                  Object this$branch = this.getBranch();
                  Object other$branch = other.getBranch();
                  if (this$branch == null) {
                     if (other$branch == null) {
                        break label554;
                     }
                  } else if (this$branch.equals(other$branch)) {
                     break label554;
                  }

                  return false;
               }

               label547: {
                  Object this$issueBankNo = this.getIssueBankNo();
                  Object other$issueBankNo = other.getIssueBankNo();
                  if (this$issueBankNo == null) {
                     if (other$issueBankNo == null) {
                        break label547;
                     }
                  } else if (this$issueBankNo.equals(other$issueBankNo)) {
                     break label547;
                  }

                  return false;
               }

               Object this$lostNo = this.getLostNo();
               Object other$lostNo = other.getLostNo();
               if (this$lostNo == null) {
                  if (other$lostNo != null) {
                     return false;
                  }
               } else if (!this$lostNo.equals(other$lostNo)) {
                  return false;
               }

               Object this$lossNo = this.getLossNo();
               Object other$lossNo = other.getLossNo();
               if (this$lossNo == null) {
                  if (other$lossNo != null) {
                     return false;
                  }
               } else if (!this$lossNo.equals(other$lossNo)) {
                  return false;
               }

               label526: {
                  Object this$origSerialNo = this.getOrigSerialNo();
                  Object other$origSerialNo = other.getOrigSerialNo();
                  if (this$origSerialNo == null) {
                     if (other$origSerialNo == null) {
                        break label526;
                     }
                  } else if (this$origSerialNo.equals(other$origSerialNo)) {
                     break label526;
                  }

                  return false;
               }

               label519: {
                  Object this$billAmt = this.getBillAmt();
                  Object other$billAmt = other.getBillAmt();
                  if (this$billAmt == null) {
                     if (other$billAmt == null) {
                        break label519;
                     }
                  } else if (this$billAmt.equals(other$billAmt)) {
                     break label519;
                  }

                  return false;
               }

               Object this$lostDocumentType = this.getLostDocumentType();
               Object other$lostDocumentType = other.getLostDocumentType();
               if (this$lostDocumentType == null) {
                  if (other$lostDocumentType != null) {
                     return false;
                  }
               } else if (!this$lostDocumentType.equals(other$lostDocumentType)) {
                  return false;
               }

               label505: {
                  Object this$unlostDocumentType = this.getUnlostDocumentType();
                  Object other$unlostDocumentType = other.getUnlostDocumentType();
                  if (this$unlostDocumentType == null) {
                     if (other$unlostDocumentType == null) {
                        break label505;
                     }
                  } else if (this$unlostDocumentType.equals(other$unlostDocumentType)) {
                     break label505;
                  }

                  return false;
               }

               Object this$billLostUnlostOperateType = this.getBillLostUnlostOperateType();
               Object other$billLostUnlostOperateType = other.getBillLostUnlostOperateType();
               if (this$billLostUnlostOperateType == null) {
                  if (other$billLostUnlostOperateType != null) {
                     return false;
                  }
               } else if (!this$billLostUnlostOperateType.equals(other$billLostUnlostOperateType)) {
                  return false;
               }

               label491: {
                  Object this$applyerProdeType = this.getApplyerProdeType();
                  Object other$applyerProdeType = other.getApplyerProdeType();
                  if (this$applyerProdeType == null) {
                     if (other$applyerProdeType == null) {
                        break label491;
                     }
                  } else if (this$applyerProdeType.equals(other$applyerProdeType)) {
                     break label491;
                  }

                  return false;
               }

               Object this$agentFlag = this.getAgentFlag();
               Object other$agentFlag = other.getAgentFlag();
               if (this$agentFlag == null) {
                  if (other$agentFlag != null) {
                     return false;
                  }
               } else if (!this$agentFlag.equals(other$agentFlag)) {
                  return false;
               }

               Object this$tranValidFlag = this.getTranValidFlag();
               Object other$tranValidFlag = other.getTranValidFlag();
               if (this$tranValidFlag == null) {
                  if (other$tranValidFlag != null) {
                     return false;
                  }
               } else if (!this$tranValidFlag.equals(other$tranValidFlag)) {
                  return false;
               }

               label470: {
                  Object this$billType = this.getBillType();
                  Object other$billType = other.getBillType();
                  if (this$billType == null) {
                     if (other$billType == null) {
                        break label470;
                     }
                  } else if (this$billType.equals(other$billType)) {
                     break label470;
                  }

                  return false;
               }

               label463: {
                  Object this$issueFlag = this.getIssueFlag();
                  Object other$issueFlag = other.getIssueFlag();
                  if (this$issueFlag == null) {
                     if (other$issueFlag == null) {
                        break label463;
                     }
                  } else if (this$issueFlag.equals(other$issueFlag)) {
                     break label463;
                  }

                  return false;
               }

               Object this$payeeProdType = this.getPayeeProdType();
               Object other$payeeProdType = other.getPayeeProdType();
               if (this$payeeProdType == null) {
                  if (other$payeeProdType != null) {
                     return false;
                  }
               } else if (!this$payeeProdType.equals(other$payeeProdType)) {
                  return false;
               }

               Object this$applyerBaseAcctNo = this.getApplyerBaseAcctNo();
               Object other$applyerBaseAcctNo = other.getApplyerBaseAcctNo();
               if (this$applyerBaseAcctNo == null) {
                  if (other$applyerBaseAcctNo != null) {
                     return false;
                  }
               } else if (!this$applyerBaseAcctNo.equals(other$applyerBaseAcctNo)) {
                  return false;
               }

               label442: {
                  Object this$payeeAcctNo = this.getPayeeAcctNo();
                  Object other$payeeAcctNo = other.getPayeeAcctNo();
                  if (this$payeeAcctNo == null) {
                     if (other$payeeAcctNo == null) {
                        break label442;
                     }
                  } else if (this$payeeAcctNo.equals(other$payeeAcctNo)) {
                     break label442;
                  }

                  return false;
               }

               label435: {
                  Object this$applyerAcctCcy = this.getApplyerAcctCcy();
                  Object other$applyerAcctCcy = other.getApplyerAcctCcy();
                  if (this$applyerAcctCcy == null) {
                     if (other$applyerAcctCcy == null) {
                        break label435;
                     }
                  } else if (this$applyerAcctCcy.equals(other$applyerAcctCcy)) {
                     break label435;
                  }

                  return false;
               }

               Object this$payeeAcctCcy = this.getPayeeAcctCcy();
               Object other$payeeAcctCcy = other.getPayeeAcctCcy();
               if (this$payeeAcctCcy == null) {
                  if (other$payeeAcctCcy != null) {
                     return false;
                  }
               } else if (!this$payeeAcctCcy.equals(other$payeeAcctCcy)) {
                  return false;
               }

               Object this$applyerAcctSeqNo = this.getApplyerAcctSeqNo();
               Object other$applyerAcctSeqNo = other.getApplyerAcctSeqNo();
               if (this$applyerAcctSeqNo == null) {
                  if (other$applyerAcctSeqNo != null) {
                     return false;
                  }
               } else if (!this$applyerAcctSeqNo.equals(other$applyerAcctSeqNo)) {
                  return false;
               }

               label414: {
                  Object this$payeeAcctSeqNo = this.getPayeeAcctSeqNo();
                  Object other$payeeAcctSeqNo = other.getPayeeAcctSeqNo();
                  if (this$payeeAcctSeqNo == null) {
                     if (other$payeeAcctSeqNo == null) {
                        break label414;
                     }
                  } else if (this$payeeAcctSeqNo.equals(other$payeeAcctSeqNo)) {
                     break label414;
                  }

                  return false;
               }

               label407: {
                  Object this$applyerAcctName = this.getApplyerAcctName();
                  Object other$applyerAcctName = other.getApplyerAcctName();
                  if (this$applyerAcctName == null) {
                     if (other$applyerAcctName == null) {
                        break label407;
                     }
                  } else if (this$applyerAcctName.equals(other$applyerAcctName)) {
                     break label407;
                  }

                  return false;
               }

               Object this$payeeAcctName = this.getPayeeAcctName();
               Object other$payeeAcctName = other.getPayeeAcctName();
               if (this$payeeAcctName == null) {
                  if (other$payeeAcctName != null) {
                     return false;
                  }
               } else if (!this$payeeAcctName.equals(other$payeeAcctName)) {
                  return false;
               }

               label393: {
                  Object this$lostDocumentId = this.getLostDocumentId();
                  Object other$lostDocumentId = other.getLostDocumentId();
                  if (this$lostDocumentId == null) {
                     if (other$lostDocumentId == null) {
                        break label393;
                     }
                  } else if (this$lostDocumentId.equals(other$lostDocumentId)) {
                     break label393;
                  }

                  return false;
               }

               Object this$unlostName = this.getUnlostName();
               Object other$unlostName = other.getUnlostName();
               if (this$unlostName == null) {
                  if (other$unlostName != null) {
                     return false;
                  }
               } else if (!this$unlostName.equals(other$unlostName)) {
                  return false;
               }

               label379: {
                  Object this$unlostDocumentId = this.getUnlostDocumentId();
                  Object other$unlostDocumentId = other.getUnlostDocumentId();
                  if (this$unlostDocumentId == null) {
                     if (other$unlostDocumentId == null) {
                        break label379;
                     }
                  } else if (this$unlostDocumentId.equals(other$unlostDocumentId)) {
                     break label379;
                  }

                  return false;
               }

               Object this$lostName = this.getLostName();
               Object other$lostName = other.getLostName();
               if (this$lostName == null) {
                  if (other$lostName != null) {
                     return false;
                  }
               } else if (!this$lostName.equals(other$lostName)) {
                  return false;
               }

               Object this$lostTelNo = this.getLostTelNo();
               Object other$lostTelNo = other.getLostTelNo();
               if (this$lostTelNo == null) {
                  if (other$lostTelNo != null) {
                     return false;
                  }
               } else if (!this$lostTelNo.equals(other$lostTelNo)) {
                  return false;
               }

               label358: {
                  Object this$billLostAddr = this.getBillLostAddr();
                  Object other$billLostAddr = other.getBillLostAddr();
                  if (this$billLostAddr == null) {
                     if (other$billLostAddr == null) {
                        break label358;
                     }
                  } else if (this$billLostAddr.equals(other$billLostAddr)) {
                     break label358;
                  }

                  return false;
               }

               label351: {
                  Object this$busiPlace = this.getBusiPlace();
                  Object other$busiPlace = other.getBusiPlace();
                  if (this$busiPlace == null) {
                     if (other$busiPlace == null) {
                        break label351;
                     }
                  } else if (this$busiPlace.equals(other$busiPlace)) {
                     break label351;
                  }

                  return false;
               }

               Object this$lostReason = this.getLostReason();
               Object other$lostReason = other.getLostReason();
               if (this$lostReason == null) {
                  if (other$lostReason != null) {
                     return false;
                  }
               } else if (!this$lostReason.equals(other$lostReason)) {
                  return false;
               }

               Object this$lostStatus = this.getLostStatus();
               Object other$lostStatus = other.getLostStatus();
               if (this$lostStatus == null) {
                  if (other$lostStatus != null) {
                     return false;
                  }
               } else if (!this$lostStatus.equals(other$lostStatus)) {
                  return false;
               }

               label330: {
                  Object this$unlostTelNo = this.getUnlostTelNo();
                  Object other$unlostTelNo = other.getUnlostTelNo();
                  if (this$unlostTelNo == null) {
                     if (other$unlostTelNo == null) {
                        break label330;
                     }
                  } else if (this$unlostTelNo.equals(other$unlostTelNo)) {
                     break label330;
                  }

                  return false;
               }

               label323: {
                  Object this$unlostReason = this.getUnlostReason();
                  Object other$unlostReason = other.getUnlostReason();
                  if (this$unlostReason == null) {
                     if (other$unlostReason == null) {
                        break label323;
                     }
                  } else if (this$unlostReason.equals(other$unlostReason)) {
                     break label323;
                  }

                  return false;
               }

               Object this$billNo = this.getBillNo();
               Object other$billNo = other.getBillNo();
               if (this$billNo == null) {
                  if (other$billNo != null) {
                     return false;
                  }
               } else if (!this$billNo.equals(other$billNo)) {
                  return false;
               }

               Object this$lostUserId = this.getLostUserId();
               Object other$lostUserId = other.getLostUserId();
               if (this$lostUserId == null) {
                  if (other$lostUserId != null) {
                     return false;
                  }
               } else if (!this$lostUserId.equals(other$lostUserId)) {
                  return false;
               }

               label302: {
                  Object this$unlostUserId = this.getUnlostUserId();
                  Object other$unlostUserId = other.getUnlostUserId();
                  if (this$unlostUserId == null) {
                     if (other$unlostUserId == null) {
                        break label302;
                     }
                  } else if (this$unlostUserId.equals(other$unlostUserId)) {
                     break label302;
                  }

                  return false;
               }

               label295: {
                  Object this$billLostTime = this.getBillLostTime();
                  Object other$billLostTime = other.getBillLostTime();
                  if (this$billLostTime == null) {
                     if (other$billLostTime == null) {
                        break label295;
                     }
                  } else if (this$billLostTime.equals(other$billLostTime)) {
                     break label295;
                  }

                  return false;
               }

               Object this$lostTime = this.getLostTime();
               Object other$lostTime = other.getLostTime();
               if (this$lostTime == null) {
                  if (other$lostTime != null) {
                     return false;
                  }
               } else if (!this$lostTime.equals(other$lostTime)) {
                  return false;
               }

               label281: {
                  Object this$unlostTime = this.getUnlostTime();
                  Object other$unlostTime = other.getUnlostTime();
                  if (this$unlostTime == null) {
                     if (other$unlostTime == null) {
                        break label281;
                     }
                  } else if (this$unlostTime.equals(other$unlostTime)) {
                     break label281;
                  }

                  return false;
               }

               Object this$isCommission = this.getIsCommission();
               Object other$isCommission = other.getIsCommission();
               if (this$isCommission == null) {
                  if (other$isCommission != null) {
                     return false;
                  }
               } else if (!this$isCommission.equals(other$isCommission)) {
                  return false;
               }

               label267: {
                  Object this$agentDocumentType = this.getAgentDocumentType();
                  Object other$agentDocumentType = other.getAgentDocumentType();
                  if (this$agentDocumentType == null) {
                     if (other$agentDocumentType == null) {
                        break label267;
                     }
                  } else if (this$agentDocumentType.equals(other$agentDocumentType)) {
                     break label267;
                  }

                  return false;
               }

               Object this$agentDocumentId = this.getAgentDocumentId();
               Object other$agentDocumentId = other.getAgentDocumentId();
               if (this$agentDocumentId == null) {
                  if (other$agentDocumentId != null) {
                     return false;
                  }
               } else if (!this$agentDocumentId.equals(other$agentDocumentId)) {
                  return false;
               }

               Object this$servArray = this.getServArray();
               Object other$servArray = other.getServArray();
               if (this$servArray == null) {
                  if (other$servArray != null) {
                     return false;
                  }
               } else if (!this$servArray.equals(other$servArray)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1200033403In.Body;
      }
      public String toString() {
         return "Core1200033403In.Body(agentName=" + this.getAgentName() + ", issueDate=" + this.getIssueDate() + ", issueBankName=" + this.getIssueBankName() + ", branch=" + this.getBranch() + ", issueBankNo=" + this.getIssueBankNo() + ", lostNo=" + this.getLostNo() + ", lossNo=" + this.getLossNo() + ", origSerialNo=" + this.getOrigSerialNo() + ", billAmt=" + this.getBillAmt() + ", lostDocumentType=" + this.getLostDocumentType() + ", unlostDocumentType=" + this.getUnlostDocumentType() + ", billLostUnlostOperateType=" + this.getBillLostUnlostOperateType() + ", applyerProdeType=" + this.getApplyerProdeType() + ", agentFlag=" + this.getAgentFlag() + ", tranValidFlag=" + this.getTranValidFlag() + ", billType=" + this.getBillType() + ", issueFlag=" + this.getIssueFlag() + ", payeeProdType=" + this.getPayeeProdType() + ", applyerBaseAcctNo=" + this.getApplyerBaseAcctNo() + ", payeeAcctNo=" + this.getPayeeAcctNo() + ", applyerAcctCcy=" + this.getApplyerAcctCcy() + ", payeeAcctCcy=" + this.getPayeeAcctCcy() + ", applyerAcctSeqNo=" + this.getApplyerAcctSeqNo() + ", payeeAcctSeqNo=" + this.getPayeeAcctSeqNo() + ", applyerAcctName=" + this.getApplyerAcctName() + ", payeeAcctName=" + this.getPayeeAcctName() + ", lostDocumentId=" + this.getLostDocumentId() + ", unlostName=" + this.getUnlostName() + ", unlostDocumentId=" + this.getUnlostDocumentId() + ", lostName=" + this.getLostName() + ", lostTelNo=" + this.getLostTelNo() + ", billLostAddr=" + this.getBillLostAddr() + ", busiPlace=" + this.getBusiPlace() + ", lostReason=" + this.getLostReason() + ", lostStatus=" + this.getLostStatus() + ", unlostTelNo=" + this.getUnlostTelNo() + ", unlostReason=" + this.getUnlostReason() + ", billNo=" + this.getBillNo() + ", lostUserId=" + this.getLostUserId() + ", unlostUserId=" + this.getUnlostUserId() + ", billLostTime=" + this.getBillLostTime() + ", lostTime=" + this.getLostTime() + ", unlostTime=" + this.getUnlostTime() + ", isCommission=" + this.getIsCommission() + ", agentDocumentType=" + this.getAgentDocumentType() + ", agentDocumentId=" + this.getAgentDocumentId() + ", servArray=" + this.getServArray() + ")";
      }

      public static class ServArray {
         @V(
            desc = "密码",
            notNull = false,
            length = "200",
            remark = "密码",
            maxSize = 200
         )
         private String password;
         @V(
            desc = "凭证类型",
            notNull = false,
            length = "10",
            remark = "凭证类型",
            maxSize = 10
         )
         private String docType;
         @V(
            desc = "凭证起始号码",
            notNull = false,
            length = "50",
            remark = "凭证起始号码",
            maxSize = 50
         )
         private String voucherStartNo;
         @V(
            desc = "凭证终止号码",
            notNull = false,
            length = "50",
            remark = "凭证终止号码",
            maxSize = 50
         )
         private String voucherEndNo;
         @V(
            desc = "前缀",
            notNull = false,
            length = "10",
            remark = "前缀",
            maxSize = 10
         )
         private String prefix;
         @V(
            desc = "凭证数量",
            notNull = false,
            length = "6",
            remark = "凭证数量"
         )
         private Integer voucherNum;
         @V(
            desc = "税金",
            notNull = false,
            length = "17",
            remark = "税金",
            decimalLength = 2,
            precision = 2
         )
         private BigDecimal taxAmt;
         @V(
            desc = "费用金额",
            notNull = true,
            length = "17",
            remark = "费用金额",
            decimalLength = 2,
            precision = 2
         )
         private BigDecimal feeAmt;
         @V(
            desc = "原始费用金额,即折扣前的费用金额",
            notNull = false,
            length = "17",
            remark = "原始费用金额,即折扣前的费用金额",
            decimalLength = 2,
            precision = 2
         )
         private BigDecimal origFeeAmt;
         @V(
            desc = "折扣金额",
            notNull = false,
            length = "17",
            remark = "费用折扣金额，即优惠的金额，少收取的金额",
            decimalLength = 2,
            precision = 2
         )
         private BigDecimal discFeeAmt;
         @V(
            desc = "利率折扣",
            notNull = false,
            length = "15",
            remark = "利率折扣",
            decimalLength = 8,
            precision = 8
         )
         private BigDecimal discRate;
         @V(
            desc = "税种",
            notNull = false,
            length = "5",
            inDesc = "VAT-增值税,ADT-附加税",
            remark = "税种",
            maxSize = 5
         )
         private String taxType;
         @V(
            desc = "税率",
            notNull = false,
            length = "15",
            remark = "税率",
            decimalLength = 8,
            precision = 8
         )
         private BigDecimal taxRate;
         @V(
            desc = "支取方式",
            notNull = false,
            length = "1",
            in = "S,P,W,B,O",
            inDesc = "S-凭印鉴支取,P-凭密码支取,W-无密码无印鉴支取,B-凭印鉴和密码支取,O-凭证件支取,R-支付密码器和印鉴",
            remark = "支取方式",
            maxSize = 1
         )
         private String withdrawalType;
         @V(
            desc = "费用类型",
            notNull = true,
            length = "20",
            remark = "费率类型",
            maxSize = 20
         )
         private String feeType;
         @V(
            desc = "折扣类型",
            notNull = false,
            length = "2",
            in = "01,02,03,04,06",
            inDesc = "01-折上折,02-取最大,03-取最小,04-取平均值,06-取权重",
            remark = "费用的折扣方式，打折或者根据定价区不同的费用值",
            maxSize = 2
         )
         private String discType;
         @V(
            desc = "收费账户产品类型",
            notNull = false,
            length = "20",
            remark = "收费账户产品类型",
            maxSize = 20
         )
         private String chargeToProdType;
         @V(
            desc = "收费账号",
            notNull = false,
            length = "50",
            remark = "收费账号",
            maxSize = 50
         )
         private String chargeToBaseAcctNo;
         @V(
            desc = "收费币种",
            notNull = true,
            length = "3",
            remark = "收费币种",
            maxSize = 3
         )
         private String feeCcy;
         @V(
            desc = "收费账户币种符",
            notNull = false,
            length = "3",
            remark = "收费账户币种符",
            maxSize = 3
         )
         private String chargeToCcy;
         @V(
            desc = "收费账户序号",
            notNull = false,
            length = "5",
            remark = "收费账户序号",
            maxSize = 5
         )
         private String chargeToAcctSeqNo;
         @V(
            desc = "日终/联机标志",
            notNull = false,
            length = "1",
            in = "O,B",
            inDesc = "O-联机,B-日终批量",
            remark = "日终/联机标志",
            maxSize = 1
         )
         private String boInd;
         @V(
            desc = "收取标志",
            notNull = true,
            length = "1",
            in = "C,T,N,P",
            inDesc = "C-现金收取,T-转账收取,N-暂不收取,P-套餐内抵用",
            remark = "收取标志",
            maxSize = 1
         )
         private String chargeMode;

         public String getPassword() {
            return this.password;
         }

         public String getDocType() {
            return this.docType;
         }

         public String getVoucherStartNo() {
            return this.voucherStartNo;
         }

         public String getVoucherEndNo() {
            return this.voucherEndNo;
         }

         public String getPrefix() {
            return this.prefix;
         }

         public Integer getVoucherNum() {
            return this.voucherNum;
         }

         public BigDecimal getTaxAmt() {
            return this.taxAmt;
         }

         public BigDecimal getFeeAmt() {
            return this.feeAmt;
         }

         public BigDecimal getOrigFeeAmt() {
            return this.origFeeAmt;
         }

         public BigDecimal getDiscFeeAmt() {
            return this.discFeeAmt;
         }

         public BigDecimal getDiscRate() {
            return this.discRate;
         }

         public String getTaxType() {
            return this.taxType;
         }

         public BigDecimal getTaxRate() {
            return this.taxRate;
         }

         public String getWithdrawalType() {
            return this.withdrawalType;
         }

         public String getFeeType() {
            return this.feeType;
         }

         public String getDiscType() {
            return this.discType;
         }

         public String getChargeToProdType() {
            return this.chargeToProdType;
         }

         public String getChargeToBaseAcctNo() {
            return this.chargeToBaseAcctNo;
         }

         public String getFeeCcy() {
            return this.feeCcy;
         }

         public String getChargeToCcy() {
            return this.chargeToCcy;
         }

         public String getChargeToAcctSeqNo() {
            return this.chargeToAcctSeqNo;
         }

         public String getBoInd() {
            return this.boInd;
         }

         public String getChargeMode() {
            return this.chargeMode;
         }

         public void setPassword(String password) {
            this.password = password;
         }

         public void setDocType(String docType) {
            this.docType = docType;
         }

         public void setVoucherStartNo(String voucherStartNo) {
            this.voucherStartNo = voucherStartNo;
         }

         public void setVoucherEndNo(String voucherEndNo) {
            this.voucherEndNo = voucherEndNo;
         }

         public void setPrefix(String prefix) {
            this.prefix = prefix;
         }

         public void setVoucherNum(Integer voucherNum) {
            this.voucherNum = voucherNum;
         }

         public void setTaxAmt(BigDecimal taxAmt) {
            this.taxAmt = taxAmt;
         }

         public void setFeeAmt(BigDecimal feeAmt) {
            this.feeAmt = feeAmt;
         }

         public void setOrigFeeAmt(BigDecimal origFeeAmt) {
            this.origFeeAmt = origFeeAmt;
         }

         public void setDiscFeeAmt(BigDecimal discFeeAmt) {
            this.discFeeAmt = discFeeAmt;
         }

         public void setDiscRate(BigDecimal discRate) {
            this.discRate = discRate;
         }

         public void setTaxType(String taxType) {
            this.taxType = taxType;
         }

         public void setTaxRate(BigDecimal taxRate) {
            this.taxRate = taxRate;
         }

         public void setWithdrawalType(String withdrawalType) {
            this.withdrawalType = withdrawalType;
         }

         public void setFeeType(String feeType) {
            this.feeType = feeType;
         }

         public void setDiscType(String discType) {
            this.discType = discType;
         }

         public void setChargeToProdType(String chargeToProdType) {
            this.chargeToProdType = chargeToProdType;
         }

         public void setChargeToBaseAcctNo(String chargeToBaseAcctNo) {
            this.chargeToBaseAcctNo = chargeToBaseAcctNo;
         }

         public void setFeeCcy(String feeCcy) {
            this.feeCcy = feeCcy;
         }

         public void setChargeToCcy(String chargeToCcy) {
            this.chargeToCcy = chargeToCcy;
         }

         public void setChargeToAcctSeqNo(String chargeToAcctSeqNo) {
            this.chargeToAcctSeqNo = chargeToAcctSeqNo;
         }

         public void setBoInd(String boInd) {
            this.boInd = boInd;
         }

         public void setChargeMode(String chargeMode) {
            this.chargeMode = chargeMode;
         }

         public boolean equals(Object o) {
            if (o == this) {
               return true;
            } else if (!(o instanceof Core1200033403In.Body.ServArray)) {
               return false;
            } else {
               Core1200033403In.Body.ServArray other = (Core1200033403In.Body.ServArray)o;
               if (!other.canEqual(this)) {
                  return false;
               } else {
                  label287: {
                     Object this$password = this.getPassword();
                     Object other$password = other.getPassword();
                     if (this$password == null) {
                        if (other$password == null) {
                           break label287;
                        }
                     } else if (this$password.equals(other$password)) {
                        break label287;
                     }

                     return false;
                  }

                  Object this$docType = this.getDocType();
                  Object other$docType = other.getDocType();
                  if (this$docType == null) {
                     if (other$docType != null) {
                        return false;
                     }
                  } else if (!this$docType.equals(other$docType)) {
                     return false;
                  }

                  Object this$voucherStartNo = this.getVoucherStartNo();
                  Object other$voucherStartNo = other.getVoucherStartNo();
                  if (this$voucherStartNo == null) {
                     if (other$voucherStartNo != null) {
                        return false;
                     }
                  } else if (!this$voucherStartNo.equals(other$voucherStartNo)) {
                     return false;
                  }

                  label266: {
                     Object this$voucherEndNo = this.getVoucherEndNo();
                     Object other$voucherEndNo = other.getVoucherEndNo();
                     if (this$voucherEndNo == null) {
                        if (other$voucherEndNo == null) {
                           break label266;
                        }
                     } else if (this$voucherEndNo.equals(other$voucherEndNo)) {
                        break label266;
                     }

                     return false;
                  }

                  label259: {
                     Object this$prefix = this.getPrefix();
                     Object other$prefix = other.getPrefix();
                     if (this$prefix == null) {
                        if (other$prefix == null) {
                           break label259;
                        }
                     } else if (this$prefix.equals(other$prefix)) {
                        break label259;
                     }

                     return false;
                  }

                  Object this$voucherNum = this.getVoucherNum();
                  Object other$voucherNum = other.getVoucherNum();
                  if (this$voucherNum == null) {
                     if (other$voucherNum != null) {
                        return false;
                     }
                  } else if (!this$voucherNum.equals(other$voucherNum)) {
                     return false;
                  }

                  Object this$taxAmt = this.getTaxAmt();
                  Object other$taxAmt = other.getTaxAmt();
                  if (this$taxAmt == null) {
                     if (other$taxAmt != null) {
                        return false;
                     }
                  } else if (!this$taxAmt.equals(other$taxAmt)) {
                     return false;
                  }

                  label238: {
                     Object this$feeAmt = this.getFeeAmt();
                     Object other$feeAmt = other.getFeeAmt();
                     if (this$feeAmt == null) {
                        if (other$feeAmt == null) {
                           break label238;
                        }
                     } else if (this$feeAmt.equals(other$feeAmt)) {
                        break label238;
                     }

                     return false;
                  }

                  label231: {
                     Object this$origFeeAmt = this.getOrigFeeAmt();
                     Object other$origFeeAmt = other.getOrigFeeAmt();
                     if (this$origFeeAmt == null) {
                        if (other$origFeeAmt == null) {
                           break label231;
                        }
                     } else if (this$origFeeAmt.equals(other$origFeeAmt)) {
                        break label231;
                     }

                     return false;
                  }

                  Object this$discFeeAmt = this.getDiscFeeAmt();
                  Object other$discFeeAmt = other.getDiscFeeAmt();
                  if (this$discFeeAmt == null) {
                     if (other$discFeeAmt != null) {
                        return false;
                     }
                  } else if (!this$discFeeAmt.equals(other$discFeeAmt)) {
                     return false;
                  }

                  label217: {
                     Object this$discRate = this.getDiscRate();
                     Object other$discRate = other.getDiscRate();
                     if (this$discRate == null) {
                        if (other$discRate == null) {
                           break label217;
                        }
                     } else if (this$discRate.equals(other$discRate)) {
                        break label217;
                     }

                     return false;
                  }

                  Object this$taxType = this.getTaxType();
                  Object other$taxType = other.getTaxType();
                  if (this$taxType == null) {
                     if (other$taxType != null) {
                        return false;
                     }
                  } else if (!this$taxType.equals(other$taxType)) {
                     return false;
                  }

                  label203: {
                     Object this$taxRate = this.getTaxRate();
                     Object other$taxRate = other.getTaxRate();
                     if (this$taxRate == null) {
                        if (other$taxRate == null) {
                           break label203;
                        }
                     } else if (this$taxRate.equals(other$taxRate)) {
                        break label203;
                     }

                     return false;
                  }

                  Object this$withdrawalType = this.getWithdrawalType();
                  Object other$withdrawalType = other.getWithdrawalType();
                  if (this$withdrawalType == null) {
                     if (other$withdrawalType != null) {
                        return false;
                     }
                  } else if (!this$withdrawalType.equals(other$withdrawalType)) {
                     return false;
                  }

                  Object this$feeType = this.getFeeType();
                  Object other$feeType = other.getFeeType();
                  if (this$feeType == null) {
                     if (other$feeType != null) {
                        return false;
                     }
                  } else if (!this$feeType.equals(other$feeType)) {
                     return false;
                  }

                  label182: {
                     Object this$discType = this.getDiscType();
                     Object other$discType = other.getDiscType();
                     if (this$discType == null) {
                        if (other$discType == null) {
                           break label182;
                        }
                     } else if (this$discType.equals(other$discType)) {
                        break label182;
                     }

                     return false;
                  }

                  label175: {
                     Object this$chargeToProdType = this.getChargeToProdType();
                     Object other$chargeToProdType = other.getChargeToProdType();
                     if (this$chargeToProdType == null) {
                        if (other$chargeToProdType == null) {
                           break label175;
                        }
                     } else if (this$chargeToProdType.equals(other$chargeToProdType)) {
                        break label175;
                     }

                     return false;
                  }

                  Object this$chargeToBaseAcctNo = this.getChargeToBaseAcctNo();
                  Object other$chargeToBaseAcctNo = other.getChargeToBaseAcctNo();
                  if (this$chargeToBaseAcctNo == null) {
                     if (other$chargeToBaseAcctNo != null) {
                        return false;
                     }
                  } else if (!this$chargeToBaseAcctNo.equals(other$chargeToBaseAcctNo)) {
                     return false;
                  }

                  Object this$feeCcy = this.getFeeCcy();
                  Object other$feeCcy = other.getFeeCcy();
                  if (this$feeCcy == null) {
                     if (other$feeCcy != null) {
                        return false;
                     }
                  } else if (!this$feeCcy.equals(other$feeCcy)) {
                     return false;
                  }

                  label154: {
                     Object this$chargeToCcy = this.getChargeToCcy();
                     Object other$chargeToCcy = other.getChargeToCcy();
                     if (this$chargeToCcy == null) {
                        if (other$chargeToCcy == null) {
                           break label154;
                        }
                     } else if (this$chargeToCcy.equals(other$chargeToCcy)) {
                        break label154;
                     }

                     return false;
                  }

                  label147: {
                     Object this$chargeToAcctSeqNo = this.getChargeToAcctSeqNo();
                     Object other$chargeToAcctSeqNo = other.getChargeToAcctSeqNo();
                     if (this$chargeToAcctSeqNo == null) {
                        if (other$chargeToAcctSeqNo == null) {
                           break label147;
                        }
                     } else if (this$chargeToAcctSeqNo.equals(other$chargeToAcctSeqNo)) {
                        break label147;
                     }

                     return false;
                  }

                  Object this$boInd = this.getBoInd();
                  Object other$boInd = other.getBoInd();
                  if (this$boInd == null) {
                     if (other$boInd != null) {
                        return false;
                     }
                  } else if (!this$boInd.equals(other$boInd)) {
                     return false;
                  }

                  Object this$chargeMode = this.getChargeMode();
                  Object other$chargeMode = other.getChargeMode();
                  if (this$chargeMode == null) {
                     if (other$chargeMode != null) {
                        return false;
                     }
                  } else if (!this$chargeMode.equals(other$chargeMode)) {
                     return false;
                  }

                  return true;
               }
            }
         }

         protected boolean canEqual(Object other) {
            return other instanceof Core1200033403In.Body.ServArray;
         }
         public String toString() {
            return "Core1200033403In.Body.ServArray(password=" + this.getPassword() + ", docType=" + this.getDocType() + ", voucherStartNo=" + this.getVoucherStartNo() + ", voucherEndNo=" + this.getVoucherEndNo() + ", prefix=" + this.getPrefix() + ", voucherNum=" + this.getVoucherNum() + ", taxAmt=" + this.getTaxAmt() + ", feeAmt=" + this.getFeeAmt() + ", origFeeAmt=" + this.getOrigFeeAmt() + ", discFeeAmt=" + this.getDiscFeeAmt() + ", discRate=" + this.getDiscRate() + ", taxType=" + this.getTaxType() + ", taxRate=" + this.getTaxRate() + ", withdrawalType=" + this.getWithdrawalType() + ", feeType=" + this.getFeeType() + ", discType=" + this.getDiscType() + ", chargeToProdType=" + this.getChargeToProdType() + ", chargeToBaseAcctNo=" + this.getChargeToBaseAcctNo() + ", feeCcy=" + this.getFeeCcy() + ", chargeToCcy=" + this.getChargeToCcy() + ", chargeToAcctSeqNo=" + this.getChargeToAcctSeqNo() + ", boInd=" + this.getBoInd() + ", chargeMode=" + this.getChargeMode() + ")";
         }
      }
   }
}
