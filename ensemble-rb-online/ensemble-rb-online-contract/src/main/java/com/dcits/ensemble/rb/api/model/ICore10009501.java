package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10009501In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10009501Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore10009501 {
   String URL = "/rb/fin/loan/standby";


   @ApiRemark("信贷系统发起的联机备款接口")
   @ApiDesc("信贷系统发起的联机备款接口")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "9501"
   )
   @BusinessCategory("1000-金融")
   Core10009501Out runService(Core10009501In var1);
}
