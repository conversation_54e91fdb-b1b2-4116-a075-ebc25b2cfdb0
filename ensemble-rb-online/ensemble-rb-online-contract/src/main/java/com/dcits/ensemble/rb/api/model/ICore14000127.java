package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000127In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000127Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000127 {
   String URL = "/rb/nfin/msa/agreement/query";


   @ApiRemark("MSA账户定期转账协议信息查询")
   @ApiDesc("MSA账户定期转账协议信息查询")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0127"
   )
   Core14000127Out runService(Core14000127In var1);
}
