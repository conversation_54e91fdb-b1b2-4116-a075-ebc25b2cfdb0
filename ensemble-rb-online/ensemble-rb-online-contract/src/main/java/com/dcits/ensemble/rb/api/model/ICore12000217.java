package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000217In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000217Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12000217 {
   String URL = "/rb/nfin/dc/redeem/apply/single";


   @ApiRemark("【过时】单位大额存单赎回、撤销（功能同12000216重复）")
   @ApiDesc("用于单位大额存单赎回申请，限制单期次仅允许有一个大额存单账户。")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0217"
   )
   @BusinessCategory("1200-非金融")
   @FunctionCategory("RB10-大额存单")
   @ConsumeSys("TLE/PR/EOS")
   @ApiUseStatus("PRODUCT-产品")
   Core12000217Out runService(Core12000217In var1);
}
