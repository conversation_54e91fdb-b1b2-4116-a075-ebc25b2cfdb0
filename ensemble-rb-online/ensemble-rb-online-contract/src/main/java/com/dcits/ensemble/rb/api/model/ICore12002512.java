package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12002512In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12002512Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12002512 {
   String URL = "/rb/nfin/agreement/lye/qyjy";


   @ApiRemark("财政零余额签约解约")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "2512"
   )
   @BusinessCategory("存款")
   @FunctionCategory("RB05-协议管理")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core12002512Out runService(Core12002512In var1);
}
