package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;

@MessageOut
public class Core1200100126Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "协议编号",
      notNull = false,
      length = "50",
      remark = "协议编号",
      maxSize = 50
   )
   private String agreementId;

   public String getAgreementId() {
      return this.agreementId;
   }

   public void setAgreementId(String agreementId) {
      this.agreementId = agreementId;
   }

   public String toString() {
      return "Core1200100126Out(agreementId=" + this.getAgreementId() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200100126Out)) {
         return false;
      } else {
         Core1200100126Out other = (Core1200100126Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$agreementId = this.getAgreementId();
            Object other$agreementId = other.getAgreementId();
            if (this$agreementId == null) {
               if (other$agreementId != null) {
                  return false;
               }
            } else if (!this$agreementId.equals(other$agreementId)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200100126Out;
   }
}
