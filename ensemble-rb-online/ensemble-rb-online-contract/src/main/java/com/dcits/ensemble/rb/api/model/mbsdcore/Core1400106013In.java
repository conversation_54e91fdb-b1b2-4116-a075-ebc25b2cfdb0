package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.util.List;

@MessageIn
public class Core1400106013In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400106013In.Body body;

   public Core1400106013In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400106013In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400106013In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400106013In)) {
         return false;
      } else {
         Core1400106013In other = (Core1400106013In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400106013In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "凭证类型",
         notNull = true,
         length = "10",
         remark = "凭证类型",
         maxSize = 10
      )
      private String docType;
      @V(
         desc = "凭证明细数组",
         notNull = true,
         remark = "凭证明细数组"
      )
      private List<Core1400106013In.Body.VoucherArray> voucherArray;

      public String getDocType() {
         return this.docType;
      }

      public List<Core1400106013In.Body.VoucherArray> getVoucherArray() {
         return this.voucherArray;
      }

      public void setDocType(String docType) {
         this.docType = docType;
      }

      public void setVoucherArray(List<Core1400106013In.Body.VoucherArray> voucherArray) {
         this.voucherArray = voucherArray;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400106013In.Body)) {
            return false;
         } else {
            Core1400106013In.Body other = (Core1400106013In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$docType = this.getDocType();
               Object other$docType = other.getDocType();
               if (this$docType == null) {
                  if (other$docType != null) {
                     return false;
                  }
               } else if (!this$docType.equals(other$docType)) {
                  return false;
               }

               Object this$voucherArray = this.getVoucherArray();
               Object other$voucherArray = other.getVoucherArray();
               if (this$voucherArray == null) {
                  if (other$voucherArray != null) {
                     return false;
                  }
               } else if (!this$voucherArray.equals(other$voucherArray)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400106013In.Body;
      }
      public String toString() {
         return "Core1400106013In.Body(docType=" + this.getDocType() + ", voucherArray=" + this.getVoucherArray() + ")";
      }

      public static class VoucherArray {
         @V(
            desc = "凭证起始号码",
            notNull = true,
            length = "50",
            remark = "凭证起始号码",
            maxSize = 50
         )
         private String voucherStartNo;
         @V(
            desc = "凭证终止号码",
            notNull = true,
            length = "50",
            remark = "凭证终止号码",
            maxSize = 50
         )
         private String voucherEndNo;

         public String getVoucherStartNo() {
            return this.voucherStartNo;
         }

         public String getVoucherEndNo() {
            return this.voucherEndNo;
         }

         public void setVoucherStartNo(String voucherStartNo) {
            this.voucherStartNo = voucherStartNo;
         }

         public void setVoucherEndNo(String voucherEndNo) {
            this.voucherEndNo = voucherEndNo;
         }

         public boolean equals(Object o) {
            if (o == this) {
               return true;
            } else if (!(o instanceof Core1400106013In.Body.VoucherArray)) {
               return false;
            } else {
               Core1400106013In.Body.VoucherArray other = (Core1400106013In.Body.VoucherArray)o;
               if (!other.canEqual(this)) {
                  return false;
               } else {
                  Object this$voucherStartNo = this.getVoucherStartNo();
                  Object other$voucherStartNo = other.getVoucherStartNo();
                  if (this$voucherStartNo == null) {
                     if (other$voucherStartNo != null) {
                        return false;
                     }
                  } else if (!this$voucherStartNo.equals(other$voucherStartNo)) {
                     return false;
                  }

                  Object this$voucherEndNo = this.getVoucherEndNo();
                  Object other$voucherEndNo = other.getVoucherEndNo();
                  if (this$voucherEndNo == null) {
                     if (other$voucherEndNo != null) {
                        return false;
                     }
                  } else if (!this$voucherEndNo.equals(other$voucherEndNo)) {
                     return false;
                  }

                  return true;
               }
            }
         }

         protected boolean canEqual(Object other) {
            return other instanceof Core1400106013In.Body.VoucherArray;
         }
         public String toString() {
            return "Core1400106013In.Body.VoucherArray(voucherStartNo=" + this.getVoucherStartNo() + ", voucherEndNo=" + this.getVoucherEndNo() + ")";
         }
      }
   }
}
