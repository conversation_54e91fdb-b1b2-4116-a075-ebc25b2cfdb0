package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000233In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000233Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000233 {
   String URL = "/rb/inq/dc/priceRange";


   @ApiRemark("根据转让本金、账号、账户序号、币种、产品类型查询大额存单转让价款区间")
   @ApiDesc("大额存单转让价款区间值查询")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0233"
   )
   @BusinessCategory("1400-查询")
   @FunctionCategory("RB10-大额存单")
   @ConsumeSys("EOS")
   @ApiUseStatus("PRODUCT-产品")
   Core14000233Out runService(Core14000233In var1);
}
