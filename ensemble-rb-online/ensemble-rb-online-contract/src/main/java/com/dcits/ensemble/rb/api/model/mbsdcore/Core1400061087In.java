package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400061087In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400061087In.Body body;

   public Core1400061087In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400061087In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400061087In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400061087In)) {
         return false;
      } else {
         Core1400061087In other = (Core1400061087In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400061087In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "外币币种",
         notNull = false,
         length = "3",
         remark = "外币币种",
         maxSize = 3
      )
      private String foreCcy;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "客户中文名称",
         notNull = false,
         length = "200",
         remark = "客户中文名称",
         maxSize = 200
      )
      private String chClientName;
      @V(
         desc = "业务编号",
         notNull = false,
         length = "50",
         remark = "业务编号",
         maxSize = 50
      )
      private String busiNo;
      @V(
         desc = "登记日期",
         notNull = false,
         remark = "登记日期"
      )
      private String regTranDate;
      @V(
         desc = "客户英文名称",
         notNull = false,
         length = "200",
         remark = "客户英文名称",
         maxSize = 200
      )
      private String enClientName;
      @V(
         desc = "是否不删除",
         notNull = false,
         length = "1",
         remark = "是否不删除",
         maxSize = 1
      )
      private String isNotDelete;
      @V(
         desc = "兑换类型",
         notNull = false,
         length = "1",
         inDesc = "B-结汇,S-售汇,E-外币兑换",
         remark = "兑换类型",
         maxSize = 1
      )
      private String exType;
      @V(
         desc = "是否完全交割",
         notNull = false,
         length = "1",
         remark = "是否完全交割",
         maxSize = 1
      )
      private String isAllExchange;

      public String getForeCcy() {
         return this.foreCcy;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public String getChClientName() {
         return this.chClientName;
      }

      public String getBusiNo() {
         return this.busiNo;
      }

      public String getRegTranDate() {
         return this.regTranDate;
      }

      public String getEnClientName() {
         return this.enClientName;
      }

      public String getIsNotDelete() {
         return this.isNotDelete;
      }

      public String getExType() {
         return this.exType;
      }

      public String getIsAllExchange() {
         return this.isAllExchange;
      }

      public void setForeCcy(String foreCcy) {
         this.foreCcy = foreCcy;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setChClientName(String chClientName) {
         this.chClientName = chClientName;
      }

      public void setBusiNo(String busiNo) {
         this.busiNo = busiNo;
      }

      public void setRegTranDate(String regTranDate) {
         this.regTranDate = regTranDate;
      }

      public void setEnClientName(String enClientName) {
         this.enClientName = enClientName;
      }

      public void setIsNotDelete(String isNotDelete) {
         this.isNotDelete = isNotDelete;
      }

      public void setExType(String exType) {
         this.exType = exType;
      }

      public void setIsAllExchange(String isAllExchange) {
         this.isAllExchange = isAllExchange;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400061087In.Body)) {
            return false;
         } else {
            Core1400061087In.Body other = (Core1400061087In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label119: {
                  Object this$foreCcy = this.getForeCcy();
                  Object other$foreCcy = other.getForeCcy();
                  if (this$foreCcy == null) {
                     if (other$foreCcy == null) {
                        break label119;
                     }
                  } else if (this$foreCcy.equals(other$foreCcy)) {
                     break label119;
                  }

                  return false;
               }

               Object this$clientNo = this.getClientNo();
               Object other$clientNo = other.getClientNo();
               if (this$clientNo == null) {
                  if (other$clientNo != null) {
                     return false;
                  }
               } else if (!this$clientNo.equals(other$clientNo)) {
                  return false;
               }

               label105: {
                  Object this$chClientName = this.getChClientName();
                  Object other$chClientName = other.getChClientName();
                  if (this$chClientName == null) {
                     if (other$chClientName == null) {
                        break label105;
                     }
                  } else if (this$chClientName.equals(other$chClientName)) {
                     break label105;
                  }

                  return false;
               }

               Object this$busiNo = this.getBusiNo();
               Object other$busiNo = other.getBusiNo();
               if (this$busiNo == null) {
                  if (other$busiNo != null) {
                     return false;
                  }
               } else if (!this$busiNo.equals(other$busiNo)) {
                  return false;
               }

               label91: {
                  Object this$regTranDate = this.getRegTranDate();
                  Object other$regTranDate = other.getRegTranDate();
                  if (this$regTranDate == null) {
                     if (other$regTranDate == null) {
                        break label91;
                     }
                  } else if (this$regTranDate.equals(other$regTranDate)) {
                     break label91;
                  }

                  return false;
               }

               Object this$enClientName = this.getEnClientName();
               Object other$enClientName = other.getEnClientName();
               if (this$enClientName == null) {
                  if (other$enClientName != null) {
                     return false;
                  }
               } else if (!this$enClientName.equals(other$enClientName)) {
                  return false;
               }

               label77: {
                  Object this$isNotDelete = this.getIsNotDelete();
                  Object other$isNotDelete = other.getIsNotDelete();
                  if (this$isNotDelete == null) {
                     if (other$isNotDelete == null) {
                        break label77;
                     }
                  } else if (this$isNotDelete.equals(other$isNotDelete)) {
                     break label77;
                  }

                  return false;
               }

               label70: {
                  Object this$exType = this.getExType();
                  Object other$exType = other.getExType();
                  if (this$exType == null) {
                     if (other$exType == null) {
                        break label70;
                     }
                  } else if (this$exType.equals(other$exType)) {
                     break label70;
                  }

                  return false;
               }

               Object this$isAllExchange = this.getIsAllExchange();
               Object other$isAllExchange = other.getIsAllExchange();
               if (this$isAllExchange == null) {
                  if (other$isAllExchange != null) {
                     return false;
                  }
               } else if (!this$isAllExchange.equals(other$isAllExchange)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400061087In.Body;
      }
      public String toString() {
         return "Core1400061087In.Body(foreCcy=" + this.getForeCcy() + ", clientNo=" + this.getClientNo() + ", chClientName=" + this.getChClientName() + ", busiNo=" + this.getBusiNo() + ", regTranDate=" + this.getRegTranDate() + ", enClientName=" + this.getEnClientName() + ", isNotDelete=" + this.getIsNotDelete() + ", exType=" + this.getExType() + ", isAllExchange=" + this.getIsAllExchange() + ")";
      }
   }
}
