package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;

@MessageIn
public class Core1200100217In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1200100217In.Body body;

   public Core1200100217In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1200100217In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1200100217In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200100217In)) {
         return false;
      } else {
         Core1200100217In other = (Core1200100217In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200100217In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "使用类型",
         notNull = false,
         length = "1",
         inDesc = "1-使用use_remark配置的描述,2-展示此指定列的内容,3-报表特殊处理,4-使用指定列的内容回填列描述",
         remark = "使用类型",
         maxSize = 1
      )
      private String useType;
      @V(
         desc = "预约总金额",
         notNull = false,
         length = "17",
         remark = "预约总金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal applyAmt;
      @V(
         desc = "发行年度",
         notNull = false,
         length = "5",
         remark = "发行年度",
         maxSize = 5
      )
      private String issueYear;
      @V(
         desc = "审批单号",
         notNull = false,
         length = "50",
         remark = "审批单号",
         maxSize = 50
      )
      private String approvalNo;
      @V(
         desc = "申请机构",
         notNull = false,
         length = "50",
         remark = "申请机构",
         maxSize = 50
      )
      private String applyBranch;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;

      public String getUseType() {
         return this.useType;
      }

      public BigDecimal getApplyAmt() {
         return this.applyAmt;
      }

      public String getIssueYear() {
         return this.issueYear;
      }

      public String getApprovalNo() {
         return this.approvalNo;
      }

      public String getApplyBranch() {
         return this.applyBranch;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public void setUseType(String useType) {
         this.useType = useType;
      }

      public void setApplyAmt(BigDecimal applyAmt) {
         this.applyAmt = applyAmt;
      }

      public void setIssueYear(String issueYear) {
         this.issueYear = issueYear;
      }

      public void setApprovalNo(String approvalNo) {
         this.approvalNo = approvalNo;
      }

      public void setApplyBranch(String applyBranch) {
         this.applyBranch = applyBranch;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1200100217In.Body)) {
            return false;
         } else {
            Core1200100217In.Body other = (Core1200100217In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$useType = this.getUseType();
               Object other$useType = other.getUseType();
               if (this$useType == null) {
                  if (other$useType != null) {
                     return false;
                  }
               } else if (!this$useType.equals(other$useType)) {
                  return false;
               }

               Object this$applyAmt = this.getApplyAmt();
               Object other$applyAmt = other.getApplyAmt();
               if (this$applyAmt == null) {
                  if (other$applyAmt != null) {
                     return false;
                  }
               } else if (!this$applyAmt.equals(other$applyAmt)) {
                  return false;
               }

               Object this$issueYear = this.getIssueYear();
               Object other$issueYear = other.getIssueYear();
               if (this$issueYear == null) {
                  if (other$issueYear != null) {
                     return false;
                  }
               } else if (!this$issueYear.equals(other$issueYear)) {
                  return false;
               }

               label62: {
                  Object this$approvalNo = this.getApprovalNo();
                  Object other$approvalNo = other.getApprovalNo();
                  if (this$approvalNo == null) {
                     if (other$approvalNo == null) {
                        break label62;
                     }
                  } else if (this$approvalNo.equals(other$approvalNo)) {
                     break label62;
                  }

                  return false;
               }

               label55: {
                  Object this$applyBranch = this.getApplyBranch();
                  Object other$applyBranch = other.getApplyBranch();
                  if (this$applyBranch == null) {
                     if (other$applyBranch == null) {
                        break label55;
                     }
                  } else if (this$applyBranch.equals(other$applyBranch)) {
                     break label55;
                  }

                  return false;
               }

               Object this$clientNo = this.getClientNo();
               Object other$clientNo = other.getClientNo();
               if (this$clientNo == null) {
                  if (other$clientNo != null) {
                     return false;
                  }
               } else if (!this$clientNo.equals(other$clientNo)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1200100217In.Body;
      }
      public String toString() {
         return "Core1200100217In.Body(useType=" + this.getUseType() + ", applyAmt=" + this.getApplyAmt() + ", issueYear=" + this.getIssueYear() + ", approvalNo=" + this.getApprovalNo() + ", applyBranch=" + this.getApplyBranch() + ", clientNo=" + this.getClientNo() + ")";
      }
   }
}
