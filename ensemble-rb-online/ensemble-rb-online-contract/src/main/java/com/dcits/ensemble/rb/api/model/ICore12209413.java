package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12209413In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12209413Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12209413 {
   String URL = "/rb/inq/inner/message/execl";


   @ApiRemark("内部户账户信息查询(文件)")
   @ApiDesc("用于内部户查询账户信息生成结果文件使用")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1220",
      messageCode = "9413"
   )
   @BusinessCategory("1220-文件")
   @FunctionCategory("RB15-内部账")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core12209413Out runService(Core12209413In var1);
}
