package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;

@MessageIn
public class Core1200061079In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1200061079In.Body body;

   public Core1200061079In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1200061079In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1200061079In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200061079In)) {
         return false;
      } else {
         Core1200061079In other = (Core1200061079In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200061079In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "操作类型",
         notNull = false,
         length = "2",
         inDesc = "D-删除,U-修改,A-新增",
         remark = "操作类型",
         maxSize = 2
      )
      private String options;
      @V(
         desc = "所属机构号",
         notNull = false,
         length = "50",
         remark = "所属机构号",
         maxSize = 50
      )
      private String branch;
      @V(
         desc = "证件类型",
         notNull = false,
         length = "3",
         remark = "证件类型",
         maxSize = 3
      )
      private String documentType;
      @V(
         desc = "证件号码",
         notNull = false,
         length = "50",
         remark = "证件号码",
         maxSize = 50
      )
      private String documentId;
      @V(
         desc = "客户名称",
         notNull = false,
         length = "200",
         remark = "客户名称",
         maxSize = 200
      )
      private String clientName;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "生效日期",
         notNull = false,
         remark = "生效日期"
      )
      private String effectDate;
      @V(
         desc = "凭证号码",
         notNull = false,
         length = "50",
         remark = "凭证号码",
         maxSize = 50
      )
      private String voucherNo;
      @V(
         desc = "凭证类型",
         notNull = false,
         length = "10",
         remark = "凭证类型",
         maxSize = 10
      )
      private String docType;
      @V(
         desc = "前缀",
         notNull = false,
         length = "10",
         remark = "前缀",
         maxSize = 10
      )
      private String prefix;
      @V(
         desc = "国籍",
         notNull = false,
         length = "10",
         remark = "国籍",
         maxSize = 10
      )
      private String national;
      @V(
         desc = "新凭证号码",
         notNull = false,
         length = "50",
         remark = "新凭证号码",
         maxSize = 50
      )
      private String newVoucherNo;
      @V(
         desc = "目的地",
         notNull = false,
         length = "100",
         remark = "目的地",
         maxSize = 100
      )
      private String bourn;
      @V(
         desc = "新凭证类型",
         notNull = false,
         length = "10",
         remark = "新凭证类型",
         maxSize = 10
      )
      private String newDocType;
      @V(
         desc = "金额",
         notNull = false,
         length = "17",
         remark = "金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal amt;
      @V(
         desc = "新凭证前缀",
         notNull = false,
         length = "10",
         remark = "新凭证前缀",
         maxSize = 10
      )
      private String newPrefix;
      @V(
         desc = "携带证编号",
         notNull = false,
         length = "10",
         remark = "携带证编号",
         maxSize = 10
      )
      private String takeDocId;
      @V(
         desc = "新有效日期",
         notNull = false,
         length = "10",
         remark = "新有效日期",
         maxSize = 10
      )
      private String newEffectDate;

      public String getOptions() {
         return this.options;
      }

      public String getBranch() {
         return this.branch;
      }

      public String getDocumentType() {
         return this.documentType;
      }

      public String getDocumentId() {
         return this.documentId;
      }

      public String getClientName() {
         return this.clientName;
      }

      public String getCcy() {
         return this.ccy;
      }

      public String getEffectDate() {
         return this.effectDate;
      }

      public String getVoucherNo() {
         return this.voucherNo;
      }

      public String getDocType() {
         return this.docType;
      }

      public String getPrefix() {
         return this.prefix;
      }

      public String getNational() {
         return this.national;
      }

      public String getNewVoucherNo() {
         return this.newVoucherNo;
      }

      public String getBourn() {
         return this.bourn;
      }

      public String getNewDocType() {
         return this.newDocType;
      }

      public BigDecimal getAmt() {
         return this.amt;
      }

      public String getNewPrefix() {
         return this.newPrefix;
      }

      public String getTakeDocId() {
         return this.takeDocId;
      }

      public String getNewEffectDate() {
         return this.newEffectDate;
      }

      public void setOptions(String options) {
         this.options = options;
      }

      public void setBranch(String branch) {
         this.branch = branch;
      }

      public void setDocumentType(String documentType) {
         this.documentType = documentType;
      }

      public void setDocumentId(String documentId) {
         this.documentId = documentId;
      }

      public void setClientName(String clientName) {
         this.clientName = clientName;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setEffectDate(String effectDate) {
         this.effectDate = effectDate;
      }

      public void setVoucherNo(String voucherNo) {
         this.voucherNo = voucherNo;
      }

      public void setDocType(String docType) {
         this.docType = docType;
      }

      public void setPrefix(String prefix) {
         this.prefix = prefix;
      }

      public void setNational(String national) {
         this.national = national;
      }

      public void setNewVoucherNo(String newVoucherNo) {
         this.newVoucherNo = newVoucherNo;
      }

      public void setBourn(String bourn) {
         this.bourn = bourn;
      }

      public void setNewDocType(String newDocType) {
         this.newDocType = newDocType;
      }

      public void setAmt(BigDecimal amt) {
         this.amt = amt;
      }

      public void setNewPrefix(String newPrefix) {
         this.newPrefix = newPrefix;
      }

      public void setTakeDocId(String takeDocId) {
         this.takeDocId = takeDocId;
      }

      public void setNewEffectDate(String newEffectDate) {
         this.newEffectDate = newEffectDate;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1200061079In.Body)) {
            return false;
         } else {
            Core1200061079In.Body other = (Core1200061079In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$options = this.getOptions();
               Object other$options = other.getOptions();
               if (this$options == null) {
                  if (other$options != null) {
                     return false;
                  }
               } else if (!this$options.equals(other$options)) {
                  return false;
               }

               Object this$branch = this.getBranch();
               Object other$branch = other.getBranch();
               if (this$branch == null) {
                  if (other$branch != null) {
                     return false;
                  }
               } else if (!this$branch.equals(other$branch)) {
                  return false;
               }

               Object this$documentType = this.getDocumentType();
               Object other$documentType = other.getDocumentType();
               if (this$documentType == null) {
                  if (other$documentType != null) {
                     return false;
                  }
               } else if (!this$documentType.equals(other$documentType)) {
                  return false;
               }

               label206: {
                  Object this$documentId = this.getDocumentId();
                  Object other$documentId = other.getDocumentId();
                  if (this$documentId == null) {
                     if (other$documentId == null) {
                        break label206;
                     }
                  } else if (this$documentId.equals(other$documentId)) {
                     break label206;
                  }

                  return false;
               }

               label199: {
                  Object this$clientName = this.getClientName();
                  Object other$clientName = other.getClientName();
                  if (this$clientName == null) {
                     if (other$clientName == null) {
                        break label199;
                     }
                  } else if (this$clientName.equals(other$clientName)) {
                     break label199;
                  }

                  return false;
               }

               Object this$ccy = this.getCcy();
               Object other$ccy = other.getCcy();
               if (this$ccy == null) {
                  if (other$ccy != null) {
                     return false;
                  }
               } else if (!this$ccy.equals(other$ccy)) {
                  return false;
               }

               label185: {
                  Object this$effectDate = this.getEffectDate();
                  Object other$effectDate = other.getEffectDate();
                  if (this$effectDate == null) {
                     if (other$effectDate == null) {
                        break label185;
                     }
                  } else if (this$effectDate.equals(other$effectDate)) {
                     break label185;
                  }

                  return false;
               }

               label178: {
                  Object this$voucherNo = this.getVoucherNo();
                  Object other$voucherNo = other.getVoucherNo();
                  if (this$voucherNo == null) {
                     if (other$voucherNo == null) {
                        break label178;
                     }
                  } else if (this$voucherNo.equals(other$voucherNo)) {
                     break label178;
                  }

                  return false;
               }

               Object this$docType = this.getDocType();
               Object other$docType = other.getDocType();
               if (this$docType == null) {
                  if (other$docType != null) {
                     return false;
                  }
               } else if (!this$docType.equals(other$docType)) {
                  return false;
               }

               Object this$prefix = this.getPrefix();
               Object other$prefix = other.getPrefix();
               if (this$prefix == null) {
                  if (other$prefix != null) {
                     return false;
                  }
               } else if (!this$prefix.equals(other$prefix)) {
                  return false;
               }

               label157: {
                  Object this$national = this.getNational();
                  Object other$national = other.getNational();
                  if (this$national == null) {
                     if (other$national == null) {
                        break label157;
                     }
                  } else if (this$national.equals(other$national)) {
                     break label157;
                  }

                  return false;
               }

               label150: {
                  Object this$newVoucherNo = this.getNewVoucherNo();
                  Object other$newVoucherNo = other.getNewVoucherNo();
                  if (this$newVoucherNo == null) {
                     if (other$newVoucherNo == null) {
                        break label150;
                     }
                  } else if (this$newVoucherNo.equals(other$newVoucherNo)) {
                     break label150;
                  }

                  return false;
               }

               Object this$bourn = this.getBourn();
               Object other$bourn = other.getBourn();
               if (this$bourn == null) {
                  if (other$bourn != null) {
                     return false;
                  }
               } else if (!this$bourn.equals(other$bourn)) {
                  return false;
               }

               label136: {
                  Object this$newDocType = this.getNewDocType();
                  Object other$newDocType = other.getNewDocType();
                  if (this$newDocType == null) {
                     if (other$newDocType == null) {
                        break label136;
                     }
                  } else if (this$newDocType.equals(other$newDocType)) {
                     break label136;
                  }

                  return false;
               }

               Object this$amt = this.getAmt();
               Object other$amt = other.getAmt();
               if (this$amt == null) {
                  if (other$amt != null) {
                     return false;
                  }
               } else if (!this$amt.equals(other$amt)) {
                  return false;
               }

               label122: {
                  Object this$newPrefix = this.getNewPrefix();
                  Object other$newPrefix = other.getNewPrefix();
                  if (this$newPrefix == null) {
                     if (other$newPrefix == null) {
                        break label122;
                     }
                  } else if (this$newPrefix.equals(other$newPrefix)) {
                     break label122;
                  }

                  return false;
               }

               Object this$takeDocId = this.getTakeDocId();
               Object other$takeDocId = other.getTakeDocId();
               if (this$takeDocId == null) {
                  if (other$takeDocId != null) {
                     return false;
                  }
               } else if (!this$takeDocId.equals(other$takeDocId)) {
                  return false;
               }

               Object this$newEffectDate = this.getNewEffectDate();
               Object other$newEffectDate = other.getNewEffectDate();
               if (this$newEffectDate == null) {
                  if (other$newEffectDate != null) {
                     return false;
                  }
               } else if (!this$newEffectDate.equals(other$newEffectDate)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1200061079In.Body;
      }
      public String toString() {
         return "Core1200061079In.Body(options=" + this.getOptions() + ", branch=" + this.getBranch() + ", documentType=" + this.getDocumentType() + ", documentId=" + this.getDocumentId() + ", clientName=" + this.getClientName() + ", ccy=" + this.getCcy() + ", effectDate=" + this.getEffectDate() + ", voucherNo=" + this.getVoucherNo() + ", docType=" + this.getDocType() + ", prefix=" + this.getPrefix() + ", national=" + this.getNational() + ", newVoucherNo=" + this.getNewVoucherNo() + ", bourn=" + this.getBourn() + ", newDocType=" + this.getNewDocType() + ", amt=" + this.getAmt() + ", newPrefix=" + this.getNewPrefix() + ", takeDocId=" + this.getTakeDocId() + ", newEffectDate=" + this.getNewEffectDate() + ")";
      }
   }
}
