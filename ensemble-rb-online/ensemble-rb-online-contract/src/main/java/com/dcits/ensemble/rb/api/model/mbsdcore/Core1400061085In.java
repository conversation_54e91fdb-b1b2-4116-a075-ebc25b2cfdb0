package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400061085In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400061085In.Body body;

   public Core1400061085In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400061085In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400061085In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400061085In)) {
         return false;
      } else {
         Core1400061085In other = (Core1400061085In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400061085In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "所属机构号",
         notNull = false,
         length = "50",
         remark = "所属机构号",
         maxSize = 50
      )
      private String branch;
      @V(
         desc = "兑换类型",
         notNull = false,
         length = "1",
         inDesc = "B-结汇,S-售汇,E-外币兑换",
         remark = "兑换类型",
         maxSize = 1
      )
      private String exType;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "客户名称",
         notNull = false,
         length = "200",
         remark = "客户名称",
         maxSize = 200
      )
      private String clientName;
      @V(
         desc = "业务编号",
         notNull = false,
         length = "50",
         remark = "业务编号",
         maxSize = 50
      )
      private String busiNo;
      @V(
         desc = "开始日期",
         notNull = false,
         remark = "开始日期"
      )
      private String startDate;
      @V(
         desc = "结束日期",
         notNull = false,
         remark = "结束日期"
      )
      private String endDate;
      @V(
         desc = "操作类型",
         notNull = false,
         length = "2",
         inDesc = "D-删除,U-修改,A-新增",
         remark = "操作类型",
         maxSize = 2
      )
      private String options;
      @V(
         desc = "交割方式",
         notNull = false,
         length = "1",
         inDesc = "1-择期交割日,0-固定交割日",
         remark = "远期结售汇交割方式",
         maxSize = 1
      )
      private String deliveryMethod;
      @V(
         desc = "交割或特殊处理方式",
         notNull = false,
         length = "2",
         remark = "交割或特殊处理方式",
         maxSize = 2
      )
      private String exDealType;
      @V(
         desc = "交易对手",
         notNull = false,
         length = "1",
         inDesc = "1-对客,2-合作行",
         remark = "交易对手",
         maxSize = 1
      )
      private String dealRecAcct;
      @V(
         desc = "是否不删除",
         notNull = false,
         length = "1",
         remark = "是否不删除",
         maxSize = 1
      )
      private String isNotDelete;
      @V(
         desc = "是否完全交割",
         notNull = false,
         length = "1",
         remark = "是否完全交割",
         maxSize = 1
      )
      private String isAllExchange;

      public String getBranch() {
         return this.branch;
      }

      public String getExType() {
         return this.exType;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public String getClientName() {
         return this.clientName;
      }

      public String getBusiNo() {
         return this.busiNo;
      }

      public String getStartDate() {
         return this.startDate;
      }

      public String getEndDate() {
         return this.endDate;
      }

      public String getOptions() {
         return this.options;
      }

      public String getDeliveryMethod() {
         return this.deliveryMethod;
      }

      public String getExDealType() {
         return this.exDealType;
      }

      public String getDealRecAcct() {
         return this.dealRecAcct;
      }

      public String getIsNotDelete() {
         return this.isNotDelete;
      }

      public String getIsAllExchange() {
         return this.isAllExchange;
      }

      public void setBranch(String branch) {
         this.branch = branch;
      }

      public void setExType(String exType) {
         this.exType = exType;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setClientName(String clientName) {
         this.clientName = clientName;
      }

      public void setBusiNo(String busiNo) {
         this.busiNo = busiNo;
      }

      public void setStartDate(String startDate) {
         this.startDate = startDate;
      }

      public void setEndDate(String endDate) {
         this.endDate = endDate;
      }

      public void setOptions(String options) {
         this.options = options;
      }

      public void setDeliveryMethod(String deliveryMethod) {
         this.deliveryMethod = deliveryMethod;
      }

      public void setExDealType(String exDealType) {
         this.exDealType = exDealType;
      }

      public void setDealRecAcct(String dealRecAcct) {
         this.dealRecAcct = dealRecAcct;
      }

      public void setIsNotDelete(String isNotDelete) {
         this.isNotDelete = isNotDelete;
      }

      public void setIsAllExchange(String isAllExchange) {
         this.isAllExchange = isAllExchange;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400061085In.Body)) {
            return false;
         } else {
            Core1400061085In.Body other = (Core1400061085In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label167: {
                  Object this$branch = this.getBranch();
                  Object other$branch = other.getBranch();
                  if (this$branch == null) {
                     if (other$branch == null) {
                        break label167;
                     }
                  } else if (this$branch.equals(other$branch)) {
                     break label167;
                  }

                  return false;
               }

               Object this$exType = this.getExType();
               Object other$exType = other.getExType();
               if (this$exType == null) {
                  if (other$exType != null) {
                     return false;
                  }
               } else if (!this$exType.equals(other$exType)) {
                  return false;
               }

               label153: {
                  Object this$clientNo = this.getClientNo();
                  Object other$clientNo = other.getClientNo();
                  if (this$clientNo == null) {
                     if (other$clientNo == null) {
                        break label153;
                     }
                  } else if (this$clientNo.equals(other$clientNo)) {
                     break label153;
                  }

                  return false;
               }

               Object this$clientName = this.getClientName();
               Object other$clientName = other.getClientName();
               if (this$clientName == null) {
                  if (other$clientName != null) {
                     return false;
                  }
               } else if (!this$clientName.equals(other$clientName)) {
                  return false;
               }

               label139: {
                  Object this$busiNo = this.getBusiNo();
                  Object other$busiNo = other.getBusiNo();
                  if (this$busiNo == null) {
                     if (other$busiNo == null) {
                        break label139;
                     }
                  } else if (this$busiNo.equals(other$busiNo)) {
                     break label139;
                  }

                  return false;
               }

               Object this$startDate = this.getStartDate();
               Object other$startDate = other.getStartDate();
               if (this$startDate == null) {
                  if (other$startDate != null) {
                     return false;
                  }
               } else if (!this$startDate.equals(other$startDate)) {
                  return false;
               }

               label125: {
                  Object this$endDate = this.getEndDate();
                  Object other$endDate = other.getEndDate();
                  if (this$endDate == null) {
                     if (other$endDate == null) {
                        break label125;
                     }
                  } else if (this$endDate.equals(other$endDate)) {
                     break label125;
                  }

                  return false;
               }

               label118: {
                  Object this$options = this.getOptions();
                  Object other$options = other.getOptions();
                  if (this$options == null) {
                     if (other$options == null) {
                        break label118;
                     }
                  } else if (this$options.equals(other$options)) {
                     break label118;
                  }

                  return false;
               }

               Object this$deliveryMethod = this.getDeliveryMethod();
               Object other$deliveryMethod = other.getDeliveryMethod();
               if (this$deliveryMethod == null) {
                  if (other$deliveryMethod != null) {
                     return false;
                  }
               } else if (!this$deliveryMethod.equals(other$deliveryMethod)) {
                  return false;
               }

               label104: {
                  Object this$exDealType = this.getExDealType();
                  Object other$exDealType = other.getExDealType();
                  if (this$exDealType == null) {
                     if (other$exDealType == null) {
                        break label104;
                     }
                  } else if (this$exDealType.equals(other$exDealType)) {
                     break label104;
                  }

                  return false;
               }

               label97: {
                  Object this$dealRecAcct = this.getDealRecAcct();
                  Object other$dealRecAcct = other.getDealRecAcct();
                  if (this$dealRecAcct == null) {
                     if (other$dealRecAcct == null) {
                        break label97;
                     }
                  } else if (this$dealRecAcct.equals(other$dealRecAcct)) {
                     break label97;
                  }

                  return false;
               }

               Object this$isNotDelete = this.getIsNotDelete();
               Object other$isNotDelete = other.getIsNotDelete();
               if (this$isNotDelete == null) {
                  if (other$isNotDelete != null) {
                     return false;
                  }
               } else if (!this$isNotDelete.equals(other$isNotDelete)) {
                  return false;
               }

               Object this$isAllExchange = this.getIsAllExchange();
               Object other$isAllExchange = other.getIsAllExchange();
               if (this$isAllExchange == null) {
                  if (other$isAllExchange != null) {
                     return false;
                  }
               } else if (!this$isAllExchange.equals(other$isAllExchange)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400061085In.Body;
      }
      public String toString() {
         return "Core1400061085In.Body(branch=" + this.getBranch() + ", exType=" + this.getExType() + ", clientNo=" + this.getClientNo() + ", clientName=" + this.getClientName() + ", busiNo=" + this.getBusiNo() + ", startDate=" + this.getStartDate() + ", endDate=" + this.getEndDate() + ", options=" + this.getOptions() + ", deliveryMethod=" + this.getDeliveryMethod() + ", exDealType=" + this.getExDealType() + ", dealRecAcct=" + this.getDealRecAcct() + ", isNotDelete=" + this.getIsNotDelete() + ", isAllExchange=" + this.getIsAllExchange() + ")";
      }
   }
}
