package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class MbsdCore14008002In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private MbsdCore14008002In.Body body;

   public MbsdCore14008002In.Body getBody() {
      return this.body;
   }

   public void setBody(MbsdCore14008002In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "MbsdCore14008002In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof MbsdCore14008002In)) {
         return false;
      } else {
         MbsdCore14008002In other = (MbsdCore14008002In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof MbsdCore14008002In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "账户内部键值",
         notNull = true,
         length = "15",
         remark = "账户内部键值"
      )
      private Long internalKey;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;

      public Long getInternalKey() {
         return this.internalKey;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public void setInternalKey(Long internalKey) {
         this.internalKey = internalKey;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof MbsdCore14008002In.Body)) {
            return false;
         } else {
            MbsdCore14008002In.Body other = (MbsdCore14008002In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$internalKey = this.getInternalKey();
               Object other$internalKey = other.getInternalKey();
               if (this$internalKey == null) {
                  if (other$internalKey != null) {
                     return false;
                  }
               } else if (!this$internalKey.equals(other$internalKey)) {
                  return false;
               }

               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo != null) {
                     return false;
                  }
               } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof MbsdCore14008002In.Body;
      }
      public String toString() {
         return "MbsdCore14008002In.Body(internalKey=" + this.getInternalKey() + ", baseAcctNo=" + this.getBaseAcctNo() + ")";
      }
   }
}
