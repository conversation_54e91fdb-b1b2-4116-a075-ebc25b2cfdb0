package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400100021Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "贷方总笔数",
      notNull = false,
      length = "10",
      remark = "贷方总笔数"
   )
   private Long crTotalNum;
   @V(
      desc = "贷方总金额",
      notNull = false,
      length = "38",
      remark = "贷方总金额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal crTotalAmt;
   @V(
      desc = "借方总笔数",
      notNull = false,
      length = "10",
      remark = "借方总笔数"
   )
   private Long drTotalNum;
   @V(
      desc = "借方金额",
      notNull = false,
      length = "38",
      remark = "借方金额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal drTotalAmt;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100021Out.TranHistArray> tranHistArray;

   public Long getCrTotalNum() {
      return this.crTotalNum;
   }

   public BigDecimal getCrTotalAmt() {
      return this.crTotalAmt;
   }

   public Long getDrTotalNum() {
      return this.drTotalNum;
   }

   public BigDecimal getDrTotalAmt() {
      return this.drTotalAmt;
   }

   public List<Core1400100021Out.TranHistArray> getTranHistArray() {
      return this.tranHistArray;
   }

   public void setCrTotalNum(Long crTotalNum) {
      this.crTotalNum = crTotalNum;
   }

   public void setCrTotalAmt(BigDecimal crTotalAmt) {
      this.crTotalAmt = crTotalAmt;
   }

   public void setDrTotalNum(Long drTotalNum) {
      this.drTotalNum = drTotalNum;
   }

   public void setDrTotalAmt(BigDecimal drTotalAmt) {
      this.drTotalAmt = drTotalAmt;
   }

   public void setTranHistArray(List<Core1400100021Out.TranHistArray> tranHistArray) {
      this.tranHistArray = tranHistArray;
   }

   public String toString() {
      return "Core1400100021Out(crTotalNum=" + this.getCrTotalNum() + ", crTotalAmt=" + this.getCrTotalAmt() + ", drTotalNum=" + this.getDrTotalNum() + ", drTotalAmt=" + this.getDrTotalAmt() + ", tranHistArray=" + this.getTranHistArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100021Out)) {
         return false;
      } else {
         Core1400100021Out other = (Core1400100021Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            label73: {
               Object this$crTotalNum = this.getCrTotalNum();
               Object other$crTotalNum = other.getCrTotalNum();
               if (this$crTotalNum == null) {
                  if (other$crTotalNum == null) {
                     break label73;
                  }
               } else if (this$crTotalNum.equals(other$crTotalNum)) {
                  break label73;
               }

               return false;
            }

            Object this$crTotalAmt = this.getCrTotalAmt();
            Object other$crTotalAmt = other.getCrTotalAmt();
            if (this$crTotalAmt == null) {
               if (other$crTotalAmt != null) {
                  return false;
               }
            } else if (!this$crTotalAmt.equals(other$crTotalAmt)) {
               return false;
            }

            label59: {
               Object this$drTotalNum = this.getDrTotalNum();
               Object other$drTotalNum = other.getDrTotalNum();
               if (this$drTotalNum == null) {
                  if (other$drTotalNum == null) {
                     break label59;
                  }
               } else if (this$drTotalNum.equals(other$drTotalNum)) {
                  break label59;
               }

               return false;
            }

            Object this$drTotalAmt = this.getDrTotalAmt();
            Object other$drTotalAmt = other.getDrTotalAmt();
            if (this$drTotalAmt == null) {
               if (other$drTotalAmt != null) {
                  return false;
               }
            } else if (!this$drTotalAmt.equals(other$drTotalAmt)) {
               return false;
            }

            Object this$tranHistArray = this.getTranHistArray();
            Object other$tranHistArray = other.getTranHistArray();
            if (this$tranHistArray == null) {
               if (other$tranHistArray != null) {
                  return false;
               }
            } else if (!this$tranHistArray.equals(other$tranHistArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100021Out;
   }
   public static class TranHistArray {
      @V(
         desc = "交易日期",
         notNull = false,
         remark = "交易日期"
      )
      private String tranDate;
      @V(
         desc = "交易时间",
         notNull = false,
         length = "26",
         remark = "交易时间",
         maxSize = 26
      )
      private String tranTime;
      @V(
         desc = "交易参考号",
         notNull = false,
         length = "50",
         remark = "交易参考号",
         maxSize = 50
      )
      private String reference;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "交易类型",
         notNull = false,
         length = "10",
         remark = "交易类型",
         maxSize = 10
      )
      private String tranType;
      @V(
         desc = "交易金额",
         notNull = false,
         length = "17",
         remark = "交易金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal tranAmt;
      @V(
         desc = "余额",
         notNull = false,
         length = "17",
         remark = "余额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal balance;
      @V(
         desc = "入账柜员",
         notNull = false,
         length = "30",
         remark = "入账柜员",
         maxSize = 30
      )
      private String accountUserId;
      @V(
         desc = "交易机构",
         notNull = false,
         length = "50",
         remark = "交易机构",
         maxSize = 50
      )
      private String tranBranch;
      @V(
         desc = "凭证类型",
         notNull = false,
         length = "10",
         remark = "凭证类型",
         maxSize = 10
      )
      private String docType;
      @V(
         desc = "凭证号码",
         notNull = false,
         length = "50",
         remark = "凭证号码",
         maxSize = 50
      )
      private String voucherNo;
      @V(
         desc = "对方账号/卡号",
         notNull = false,
         length = "50",
         remark = "对方账号/卡号",
         maxSize = 50
      )
      private String othBaseAcctNo;
      @V(
         desc = "对方账户序列号",
         notNull = false,
         length = "5",
         remark = "对方账户序列号",
         maxSize = 5
      )
      private String othAcctSeqNo;
      @V(
         desc = "对方账户名称",
         notNull = false,
         length = "200",
         remark = "对方账户名称",
         maxSize = 200
      )
      private String othAcctName;
      @V(
         desc = "冲正和抹账标识",
         notNull = false,
         length = "1",
         remark = "冲正和抹账标识",
         maxSize = 1
      )
      private String wipeAccount;
      @V(
         desc = "摘要",
         notNull = false,
         length = "500",
         remark = "开户时的账号用途，销户时的销户原因",
         maxSize = 500
      )
      private String narrative;
      @V(
         desc = "借贷标志",
         notNull = false,
         length = "1",
         remark = "借贷标志",
         maxSize = 1
      )
      private String crDrInd;

      public String getTranDate() {
         return this.tranDate;
      }

      public String getTranTime() {
         return this.tranTime;
      }

      public String getReference() {
         return this.reference;
      }

      public String getCcy() {
         return this.ccy;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getTranType() {
         return this.tranType;
      }

      public BigDecimal getTranAmt() {
         return this.tranAmt;
      }

      public BigDecimal getBalance() {
         return this.balance;
      }

      public String getAccountUserId() {
         return this.accountUserId;
      }

      public String getTranBranch() {
         return this.tranBranch;
      }

      public String getDocType() {
         return this.docType;
      }

      public String getVoucherNo() {
         return this.voucherNo;
      }

      public String getOthBaseAcctNo() {
         return this.othBaseAcctNo;
      }

      public String getOthAcctSeqNo() {
         return this.othAcctSeqNo;
      }

      public String getOthAcctName() {
         return this.othAcctName;
      }

      public String getWipeAccount() {
         return this.wipeAccount;
      }

      public String getNarrative() {
         return this.narrative;
      }

      public String getCrDrInd() {
         return this.crDrInd;
      }

      public void setTranDate(String tranDate) {
         this.tranDate = tranDate;
      }

      public void setTranTime(String tranTime) {
         this.tranTime = tranTime;
      }

      public void setReference(String reference) {
         this.reference = reference;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setTranType(String tranType) {
         this.tranType = tranType;
      }

      public void setTranAmt(BigDecimal tranAmt) {
         this.tranAmt = tranAmt;
      }

      public void setBalance(BigDecimal balance) {
         this.balance = balance;
      }

      public void setAccountUserId(String accountUserId) {
         this.accountUserId = accountUserId;
      }

      public void setTranBranch(String tranBranch) {
         this.tranBranch = tranBranch;
      }

      public void setDocType(String docType) {
         this.docType = docType;
      }

      public void setVoucherNo(String voucherNo) {
         this.voucherNo = voucherNo;
      }

      public void setOthBaseAcctNo(String othBaseAcctNo) {
         this.othBaseAcctNo = othBaseAcctNo;
      }

      public void setOthAcctSeqNo(String othAcctSeqNo) {
         this.othAcctSeqNo = othAcctSeqNo;
      }

      public void setOthAcctName(String othAcctName) {
         this.othAcctName = othAcctName;
      }

      public void setWipeAccount(String wipeAccount) {
         this.wipeAccount = wipeAccount;
      }

      public void setNarrative(String narrative) {
         this.narrative = narrative;
      }

      public void setCrDrInd(String crDrInd) {
         this.crDrInd = crDrInd;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100021Out.TranHistArray)) {
            return false;
         } else {
            Core1400100021Out.TranHistArray other = (Core1400100021Out.TranHistArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$tranDate = this.getTranDate();
               Object other$tranDate = other.getTranDate();
               if (this$tranDate == null) {
                  if (other$tranDate != null) {
                     return false;
                  }
               } else if (!this$tranDate.equals(other$tranDate)) {
                  return false;
               }

               Object this$tranTime = this.getTranTime();
               Object other$tranTime = other.getTranTime();
               if (this$tranTime == null) {
                  if (other$tranTime != null) {
                     return false;
                  }
               } else if (!this$tranTime.equals(other$tranTime)) {
                  return false;
               }

               Object this$reference = this.getReference();
               Object other$reference = other.getReference();
               if (this$reference == null) {
                  if (other$reference != null) {
                     return false;
                  }
               } else if (!this$reference.equals(other$reference)) {
                  return false;
               }

               label206: {
                  Object this$ccy = this.getCcy();
                  Object other$ccy = other.getCcy();
                  if (this$ccy == null) {
                     if (other$ccy == null) {
                        break label206;
                     }
                  } else if (this$ccy.equals(other$ccy)) {
                     break label206;
                  }

                  return false;
               }

               label199: {
                  Object this$acctSeqNo = this.getAcctSeqNo();
                  Object other$acctSeqNo = other.getAcctSeqNo();
                  if (this$acctSeqNo == null) {
                     if (other$acctSeqNo == null) {
                        break label199;
                     }
                  } else if (this$acctSeqNo.equals(other$acctSeqNo)) {
                     break label199;
                  }

                  return false;
               }

               Object this$tranType = this.getTranType();
               Object other$tranType = other.getTranType();
               if (this$tranType == null) {
                  if (other$tranType != null) {
                     return false;
                  }
               } else if (!this$tranType.equals(other$tranType)) {
                  return false;
               }

               label185: {
                  Object this$tranAmt = this.getTranAmt();
                  Object other$tranAmt = other.getTranAmt();
                  if (this$tranAmt == null) {
                     if (other$tranAmt == null) {
                        break label185;
                     }
                  } else if (this$tranAmt.equals(other$tranAmt)) {
                     break label185;
                  }

                  return false;
               }

               label178: {
                  Object this$balance = this.getBalance();
                  Object other$balance = other.getBalance();
                  if (this$balance == null) {
                     if (other$balance == null) {
                        break label178;
                     }
                  } else if (this$balance.equals(other$balance)) {
                     break label178;
                  }

                  return false;
               }

               Object this$accountUserId = this.getAccountUserId();
               Object other$accountUserId = other.getAccountUserId();
               if (this$accountUserId == null) {
                  if (other$accountUserId != null) {
                     return false;
                  }
               } else if (!this$accountUserId.equals(other$accountUserId)) {
                  return false;
               }

               Object this$tranBranch = this.getTranBranch();
               Object other$tranBranch = other.getTranBranch();
               if (this$tranBranch == null) {
                  if (other$tranBranch != null) {
                     return false;
                  }
               } else if (!this$tranBranch.equals(other$tranBranch)) {
                  return false;
               }

               label157: {
                  Object this$docType = this.getDocType();
                  Object other$docType = other.getDocType();
                  if (this$docType == null) {
                     if (other$docType == null) {
                        break label157;
                     }
                  } else if (this$docType.equals(other$docType)) {
                     break label157;
                  }

                  return false;
               }

               label150: {
                  Object this$voucherNo = this.getVoucherNo();
                  Object other$voucherNo = other.getVoucherNo();
                  if (this$voucherNo == null) {
                     if (other$voucherNo == null) {
                        break label150;
                     }
                  } else if (this$voucherNo.equals(other$voucherNo)) {
                     break label150;
                  }

                  return false;
               }

               Object this$othBaseAcctNo = this.getOthBaseAcctNo();
               Object other$othBaseAcctNo = other.getOthBaseAcctNo();
               if (this$othBaseAcctNo == null) {
                  if (other$othBaseAcctNo != null) {
                     return false;
                  }
               } else if (!this$othBaseAcctNo.equals(other$othBaseAcctNo)) {
                  return false;
               }

               label136: {
                  Object this$othAcctSeqNo = this.getOthAcctSeqNo();
                  Object other$othAcctSeqNo = other.getOthAcctSeqNo();
                  if (this$othAcctSeqNo == null) {
                     if (other$othAcctSeqNo == null) {
                        break label136;
                     }
                  } else if (this$othAcctSeqNo.equals(other$othAcctSeqNo)) {
                     break label136;
                  }

                  return false;
               }

               Object this$othAcctName = this.getOthAcctName();
               Object other$othAcctName = other.getOthAcctName();
               if (this$othAcctName == null) {
                  if (other$othAcctName != null) {
                     return false;
                  }
               } else if (!this$othAcctName.equals(other$othAcctName)) {
                  return false;
               }

               label122: {
                  Object this$wipeAccount = this.getWipeAccount();
                  Object other$wipeAccount = other.getWipeAccount();
                  if (this$wipeAccount == null) {
                     if (other$wipeAccount == null) {
                        break label122;
                     }
                  } else if (this$wipeAccount.equals(other$wipeAccount)) {
                     break label122;
                  }

                  return false;
               }

               Object this$narrative = this.getNarrative();
               Object other$narrative = other.getNarrative();
               if (this$narrative == null) {
                  if (other$narrative != null) {
                     return false;
                  }
               } else if (!this$narrative.equals(other$narrative)) {
                  return false;
               }

               Object this$crDrInd = this.getCrDrInd();
               Object other$crDrInd = other.getCrDrInd();
               if (this$crDrInd == null) {
                  if (other$crDrInd != null) {
                     return false;
                  }
               } else if (!this$crDrInd.equals(other$crDrInd)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100021Out.TranHistArray;
      }
      public String toString() {
         return "Core1400100021Out.TranHistArray(tranDate=" + this.getTranDate() + ", tranTime=" + this.getTranTime() + ", reference=" + this.getReference() + ", ccy=" + this.getCcy() + ", acctSeqNo=" + this.getAcctSeqNo() + ", tranType=" + this.getTranType() + ", tranAmt=" + this.getTranAmt() + ", balance=" + this.getBalance() + ", accountUserId=" + this.getAccountUserId() + ", tranBranch=" + this.getTranBranch() + ", docType=" + this.getDocType() + ", voucherNo=" + this.getVoucherNo() + ", othBaseAcctNo=" + this.getOthBaseAcctNo() + ", othAcctSeqNo=" + this.getOthAcctSeqNo() + ", othAcctName=" + this.getOthAcctName() + ", wipeAccount=" + this.getWipeAccount() + ", narrative=" + this.getNarrative() + ", crDrInd=" + this.getCrDrInd() + ")";
      }
   }
}
