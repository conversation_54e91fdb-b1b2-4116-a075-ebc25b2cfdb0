package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;

@MessageOut
public class Core1400100220Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "期次代码",
      notNull = false,
      length = "50",
      remark = "期次代码",
      maxSize = 50
   )
   private String stageCode;
   @V(
      desc = "期次描述",
      notNull = false,
      length = "200",
      remark = "期次描述",
      maxSize = 200
   )
   private String stageCodeDesc;
   @V(
      desc = "下一期次代码",
      notNull = false,
      length = "50",
      remark = "下一期次代码",
      maxSize = 50
   )
   private String nextStageCode;

   public String getStageCode() {
      return this.stageCode;
   }

   public String getStageCodeDesc() {
      return this.stageCodeDesc;
   }

   public String getNextStageCode() {
      return this.nextStageCode;
   }

   public void setStageCode(String stageCode) {
      this.stageCode = stageCode;
   }

   public void setStageCodeDesc(String stageCodeDesc) {
      this.stageCodeDesc = stageCodeDesc;
   }

   public void setNextStageCode(String nextStageCode) {
      this.nextStageCode = nextStageCode;
   }

   public String toString() {
      return "Core1400100220Out(stageCode=" + this.getStageCode() + ", stageCodeDesc=" + this.getStageCodeDesc() + ", nextStageCode=" + this.getNextStageCode() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100220Out)) {
         return false;
      } else {
         Core1400100220Out other = (Core1400100220Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            label49: {
               Object this$stageCode = this.getStageCode();
               Object other$stageCode = other.getStageCode();
               if (this$stageCode == null) {
                  if (other$stageCode == null) {
                     break label49;
                  }
               } else if (this$stageCode.equals(other$stageCode)) {
                  break label49;
               }

               return false;
            }

            Object this$stageCodeDesc = this.getStageCodeDesc();
            Object other$stageCodeDesc = other.getStageCodeDesc();
            if (this$stageCodeDesc == null) {
               if (other$stageCodeDesc != null) {
                  return false;
               }
            } else if (!this$stageCodeDesc.equals(other$stageCodeDesc)) {
               return false;
            }

            Object this$nextStageCode = this.getNextStageCode();
            Object other$nextStageCode = other.getNextStageCode();
            if (this$nextStageCode == null) {
               if (other$nextStageCode != null) {
                  return false;
               }
            } else if (!this$nextStageCode.equals(other$nextStageCode)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100220Out;
   }
}
