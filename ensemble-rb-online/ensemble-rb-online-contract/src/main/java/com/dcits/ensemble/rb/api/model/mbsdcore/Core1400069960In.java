package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400069960In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400069960In.Body body;

   public Core1400069960In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400069960In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400069960In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400069960In)) {
         return false;
      } else {
         Core1400069960In other = (Core1400069960In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400069960In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "所属机构号",
         notNull = false,
         length = "50",
         remark = "机构代码",
         maxSize = 50
      )
      private String branch;
      @V(
         desc = "机构名称",
         notNull = false,
         length = "200",
         remark = "机构名称",
         maxSize = 200
      )
      private String branchName;

      public String getBranch() {
         return this.branch;
      }

      public String getBranchName() {
         return this.branchName;
      }

      public void setBranch(String branch) {
         this.branch = branch;
      }

      public void setBranchName(String branchName) {
         this.branchName = branchName;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400069960In.Body)) {
            return false;
         } else {
            Core1400069960In.Body other = (Core1400069960In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$branch = this.getBranch();
               Object other$branch = other.getBranch();
               if (this$branch == null) {
                  if (other$branch != null) {
                     return false;
                  }
               } else if (!this$branch.equals(other$branch)) {
                  return false;
               }

               Object this$branchName = this.getBranchName();
               Object other$branchName = other.getBranchName();
               if (this$branchName == null) {
                  if (other$branchName != null) {
                     return false;
                  }
               } else if (!this$branchName.equals(other$branchName)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400069960In.Body;
      }
      public String toString() {
         return "Core1400069960In.Body(branch=" + this.getBranch() + ", branchName=" + this.getBranchName() + ")";
      }
   }
}
