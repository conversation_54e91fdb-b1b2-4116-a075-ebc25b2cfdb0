package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000709In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000709Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12000709 {
   String URL = "/rb/nfin/pcp/agreement/cancel";


   @ApiRemark("标准优化")
   @ApiDesc("资金池解约处理，01-正常解约 02-暂停解约 03-暂停恢复")
   @EnsembleElements(
      serviceCode = "Core",
      messageType = "1200",
      messageCode = "0709"
   )
   @FunctionCategory("RB13-现金池")
   @ConsumeSys("CMG/TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core12000709Out runService(Core12000709In var1);
}
