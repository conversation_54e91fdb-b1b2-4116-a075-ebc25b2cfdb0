package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12206010In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12206010Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12206010 {
   String URL = "/rb/file/branch/change";


   @ApiRemark("深度优化")
   @ApiDesc("按文件进行存款批量账户机构变更")
   @EnsembleElements(
      serviceCode = "Core",
      messageType = "1220",
      messageCode = "6010"
   )
   @FunctionCategory("RB49-机构撤并")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core12206010Out runService(Core12206010In var1);
}
