package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@MessageIn
public class Core1200100219In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1200100219In.Body body;

   public Core1200100219In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1200100219In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1200100219In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200100219In)) {
         return false;
      } else {
         Core1200100219In other = (Core1200100219In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200100219In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "发行年度",
         notNull = true,
         length = "5",
         remark = "发行年度",
         maxSize = 5
      )
      private String issueYear;
      @V(
         desc = "客户类型",
         notNull = true,
         length = "3",
         remark = "客户大类，目前一般分为个人，公司，金融机构和内部客户。取之于CIF_CLIENT_TYPE.CLIENT_TYPE",
         maxSize = 3
      )
      private String clientType;
      @V(
         desc = "所属机构号",
         notNull = true,
         length = "50",
         remark = "机构代码",
         maxSize = 50
      )
      private String branch;
      @V(
         desc = "期次发行金额",
         notNull = true,
         length = "17",
         remark = "期次发行金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal issueAmt;
      @V(
         desc = "开始日期",
         notNull = true,
         remark = "开始日期"
      )
      private String startDate;
      @V(
         desc = "结束日期",
         notNull = true,
         remark = "结束日期"
      )
      private String endDate;
      @V(
         desc = "起售时间",
         notNull = false,
         length = "26",
         remark = "起售时间",
         maxSize = 26
      )
      private String saleStartTime;
      @V(
         desc = "止售时间",
         notNull = false,
         length = "26",
         remark = "止售时间",
         maxSize = 26
      )
      private String saleEndTime;
      @V(
         desc = "期次起存金额",
         notNull = true,
         length = "17",
         remark = "期次起存金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal stageMinAmt;
      @V(
         desc = "单笔认购最大金额",
         notNull = false,
         length = "17",
         remark = "单笔认购最大金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal sgMaxAmt;
      @V(
         desc = "最小留存金额",
         notNull = true,
         length = "17",
         remark = "最小留存金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal keepMinBal;
      @V(
         desc = "存期期限",
         notNull = true,
         length = "5",
         remark = "期限",
         maxSize = 5
      )
      private String term;
      @V(
         desc = "利率类型",
         notNull = false,
         length = "5",
         remark = "利率类型",
         maxSize = 5
      )
      private String intType;
      @V(
         desc = "付息方式",
         notNull = true,
         length = "3",
         inDesc = "1-一次还本付息,2-定期按频率付息到期还本付息",
         remark = "付息方式",
         maxSize = 3
      )
      private String payIntType;
      @V(
         desc = "是否允许提前支取",
         notNull = true,
         length = "1",
         inDesc = "Y-是,N-否",
         remark = "是否允许提前支取",
         maxSize = 1
      )
      private String preWithdrawFlag;
      @V(
         desc = "提前支取次数",
         notNull = false,
         length = "5",
         remark = "提前支取次数",
         restraint = "preWithdrawFlag=Y"
      )
      private Integer preWithdrawNum;
      @V(
         desc = "是否全额转让",
         notNull = true,
         length = "1",
         remark = "定期转让是否全额转让 Y -是 N -否",
         maxSize = 1
      )
      private String isFullTransfer;
      @V(
         desc = "是否可赎回",
         notNull = true,
         length = "1",
         inDesc = "Y-是,N-否",
         remark = "是否可赎回",
         maxSize = 1
      )
      private String redemptionFlag;
      @V(
         desc = "赎回日期",
         notNull = false,
         remark = "赎回日期"
      )
      private String tohonorDate;
      @V(
         desc = "赎回利率",
         notNull = false,
         length = "15",
         remark = "赎回利率",
         restraint = "redemptionIntType=FIX",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal tohonorRate;
      @V(
         desc = "是否扣划利息标志",
         notNull = false,
         length = "1",
         inDesc = "Y-是,N-否",
         remark = "是否扣划利息标志",
         maxSize = 1
      )
      private String intFlag;
      @V(
         desc = "浮动利率",
         notNull = false,
         length = "15",
         remark = "浮动利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal floatRate;
      @V(
         desc = "转出费用",
         notNull = false,
         length = "17",
         remark = "转出费用",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal trfOutFeeAmt;
      @V(
         desc = "审批单号",
         notNull = false,
         length = "50",
         remark = "审批单号",
         maxSize = 50
      )
      private String approvalNo;
      @V(
         desc = "电子邮件",
         notNull = false,
         length = "200",
         remark = "电子邮件",
         maxSize = 200
      )
      private String email;
      @V(
         desc = "账户币种",
         notNull = true,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "产品类型",
         notNull = true,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "申请柜员",
         notNull = false,
         length = "30",
         remark = "申请柜员",
         maxSize = 30
      )
      private String applyUserId;
      @V(
         desc = "数组",
         notNull = false,
         remark = "数组"
      )
      private List<Core1200100219In.Body.ClientListArray> clientListArray;
      @V(
         desc = "执行利率",
         notNull = false,
         length = "15",
         remark = "执行利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal realRate;
      @V(
         desc = "数组",
         notNull = false,
         remark = "数组"
      )
      private List<Core1200100219In.Body.ChannelArray> channelArray;
      @V(
         desc = "赎回利率类型",
         notNull = true,
         length = "5",
         inDesc = "MRT-按产品利率,PRE-按活期利率,FIX-固定利率",
         remark = "赎回利率类型",
         maxSize = 5
      )
      private String redemptionIntType;
      @V(
         desc = "转入费用类型",
         notNull = false,
         length = "20",
         remark = "转入费用类型",
         maxSize = 20
      )
      private String trfInFeeType;
      @V(
         desc = "转出费用类型",
         notNull = false,
         length = "20",
         remark = "转出费用类型",
         maxSize = 20
      )
      private String trfOutFeeType;
      @V(
         desc = "发行方式",
         notNull = false,
         length = "20",
         remark = "发行方式",
         maxSize = 20
      )
      private String issueMethod;
      @V(
         desc = "转入手续费",
         notNull = false,
         length = "17",
         remark = "转入手续费",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal inFee;
      @V(
         desc = "资金来源是否基于内部户",
         notNull = false,
         length = "2",
         remark = "资金来源是否基于内部户",
         maxSize = 2
      )
      private String isCashFromInnerAcct;
      @V(
         desc = "最小变动额",
         notNull = true,
         length = "17",
         remark = "最小变动额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal minChangeBalance;
      @V(
         desc = "起息日",
         notNull = false,
         remark = "起息日",
         restraint = "intStartFlag=1"
      )
      private String intStartDate;
      @V(
         desc = "到期日",
         notNull = false,
         remark = "到期日",
         restraint = "intStartFlag=1"
      )
      private String matureDate;
      @V(
         desc = "境内境外标志",
         notNull = true,
         length = "1",
         inDesc = "I-境内,O-境外",
         remark = "境内境外标志",
         maxSize = 1
      )
      private String inlandOffshore;
      @V(
         desc = "账户类型",
         notNull = true,
         length = "1",
         inDesc = "A-AIO账户,C-结算账户,D-垫款,E-委托贷款,L-转让贷款,M-普通贷款,S-储蓄账户,T-定期账户,U-贴现贷款,Y-银团贷款,Z-资产证券化",
         remark = "账户类型",
         maxSize = 1
      )
      private String acctType;
      @V(
         desc = "期限类型",
         notNull = true,
         length = "1",
         inDesc = "Y-年,Q-季,M-月,W-周,D-日",
         remark = "期限类型",
         maxSize = 1
      )
      private String termType;
      @V(
         desc = "期次代码",
         notNull = true,
         length = "50",
         remark = "期次代码",
         maxSize = 50
      )
      private String stageCode;
      @V(
         desc = "冷静期",
         notNull = false,
         length = "5",
         remark = "大额存单兑付冷静期"
      )
      private Integer calmDays;
      @V(
         desc = "出售分行或者出售机构",
         notNull = false,
         length = "500",
         remark = "出售分行或者出售机构，多可以是一个或者多个，定义多个时，需要用分隔符 | 进行分开存储",
         maxSize = 500
      )
      private String sellBranch;
      @V(
         desc = "期次描述",
         notNull = true,
         length = "200",
         remark = "期次描述",
         maxSize = 200
      )
      private String stageCodeDesc;
      @V(
         desc = "自动续存资金来源账户付息频率",
         notNull = false,
         length = "5",
         remark = "自动续存资金来源账户付息频率",
         maxSize = 5
      )
      private String aupCycle;
      @V(
         desc = "结息频率",
         notNull = false,
         length = "5",
         remark = "结息频率",
         restraint = "payIntType=2",
         maxSize = 5
      )
      private String cycleFreq;
      @V(
         desc = "是否指定收息",
         notNull = false,
         length = "1",
         inDesc = "Y-是,N-否",
         remark = "是否指定收息",
         maxSize = 1
      )
      private String directionChargeIntFlag;
      @V(
         desc = "赎回起息日期",
         notNull = false,
         remark = "赎回起息日期"
      )
      private String redeemIntDate;
      @V(
         desc = "起息标识",
         notNull = true,
         length = "1",
         inDesc = "0-立即起息,1-募集结束日起息",
         remark = "起息标识",
         maxSize = 1
      )
      private String intStartFlag;
      @V(
         desc = "结息日期",
         notNull = false,
         remark = "结息日期"
      )
      private String captDate;
      @V(
         desc = "到期本金在宽限期是否收息",
         notNull = false,
         length = "1",
         inDesc = "Y-是,N-否",
         remark = "到期本金在宽限期是否收息",
         maxSize = 1
      )
      private String graceChargeIntFlag;
      @V(
         desc = "是否组合产品",
         notNull = false,
         length = "1",
         inDesc = "0-存单买,1-纯组合,2-单买&组合买",
         remark = "是否组合产品",
         maxSize = 1
      )
      private String combProdFlag;
      @V(
         desc = "交易密码操作类型",
         notNull = false,
         length = "1",
         remark = "交易密码操作类型",
         maxSize = 1
      )
      private String tranPasswordOperateType;
      @V(
         desc = "认购账户类型",
         notNull = false,
         length = "1",
         remark = "认购账户类型（活期、定期一本通）",
         maxSize = 1
      )
      private String subsAcctType;
      @V(
         desc = "期次类型",
         notNull = true,
         length = "1",
         inDesc = "A-个人C,B-对公A,C-机构B",
         remark = "个人C，对公A，机构B",
         maxSize = 1
      )
      private String stageType;
      @V(
         desc = "预约起始日期",
         notNull = true,
         remark = "预约起始日期"
      )
      private String precontractStartDate;
      @V(
         desc = "预约结束时间",
         notNull = false,
         length = "26",
         remark = "预约结束时间",
         maxSize = 26
      )
      private String precontractEndTime;

      public String getIssueYear() {
         return this.issueYear;
      }

      public String getClientType() {
         return this.clientType;
      }

      public String getBranch() {
         return this.branch;
      }

      public BigDecimal getIssueAmt() {
         return this.issueAmt;
      }

      public String getStartDate() {
         return this.startDate;
      }

      public String getEndDate() {
         return this.endDate;
      }

      public String getSaleStartTime() {
         return this.saleStartTime;
      }

      public String getSaleEndTime() {
         return this.saleEndTime;
      }

      public BigDecimal getStageMinAmt() {
         return this.stageMinAmt;
      }

      public BigDecimal getSgMaxAmt() {
         return this.sgMaxAmt;
      }

      public BigDecimal getKeepMinBal() {
         return this.keepMinBal;
      }

      public String getTerm() {
         return this.term;
      }

      public String getIntType() {
         return this.intType;
      }

      public String getPayIntType() {
         return this.payIntType;
      }

      public String getPreWithdrawFlag() {
         return this.preWithdrawFlag;
      }

      public Integer getPreWithdrawNum() {
         return this.preWithdrawNum;
      }

      public String getIsFullTransfer() {
         return this.isFullTransfer;
      }

      public String getRedemptionFlag() {
         return this.redemptionFlag;
      }

      public String getTohonorDate() {
         return this.tohonorDate;
      }

      public BigDecimal getTohonorRate() {
         return this.tohonorRate;
      }

      public String getIntFlag() {
         return this.intFlag;
      }

      public BigDecimal getFloatRate() {
         return this.floatRate;
      }

      public BigDecimal getTrfOutFeeAmt() {
         return this.trfOutFeeAmt;
      }

      public String getApprovalNo() {
         return this.approvalNo;
      }

      public String getEmail() {
         return this.email;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getApplyUserId() {
         return this.applyUserId;
      }

      public List<Core1200100219In.Body.ClientListArray> getClientListArray() {
         return this.clientListArray;
      }

      public BigDecimal getRealRate() {
         return this.realRate;
      }

      public List<Core1200100219In.Body.ChannelArray> getChannelArray() {
         return this.channelArray;
      }

      public String getRedemptionIntType() {
         return this.redemptionIntType;
      }

      public String getTrfInFeeType() {
         return this.trfInFeeType;
      }

      public String getTrfOutFeeType() {
         return this.trfOutFeeType;
      }

      public String getIssueMethod() {
         return this.issueMethod;
      }

      public BigDecimal getInFee() {
         return this.inFee;
      }

      public String getIsCashFromInnerAcct() {
         return this.isCashFromInnerAcct;
      }

      public BigDecimal getMinChangeBalance() {
         return this.minChangeBalance;
      }

      public String getIntStartDate() {
         return this.intStartDate;
      }

      public String getMatureDate() {
         return this.matureDate;
      }

      public String getInlandOffshore() {
         return this.inlandOffshore;
      }

      public String getAcctType() {
         return this.acctType;
      }

      public String getTermType() {
         return this.termType;
      }

      public String getStageCode() {
         return this.stageCode;
      }

      public Integer getCalmDays() {
         return this.calmDays;
      }

      public String getSellBranch() {
         return this.sellBranch;
      }

      public String getStageCodeDesc() {
         return this.stageCodeDesc;
      }

      public String getAupCycle() {
         return this.aupCycle;
      }

      public String getCycleFreq() {
         return this.cycleFreq;
      }

      public String getDirectionChargeIntFlag() {
         return this.directionChargeIntFlag;
      }

      public String getRedeemIntDate() {
         return this.redeemIntDate;
      }

      public String getIntStartFlag() {
         return this.intStartFlag;
      }

      public String getCaptDate() {
         return this.captDate;
      }

      public String getGraceChargeIntFlag() {
         return this.graceChargeIntFlag;
      }

      public String getCombProdFlag() {
         return this.combProdFlag;
      }

      public String getTranPasswordOperateType() {
         return this.tranPasswordOperateType;
      }

      public String getSubsAcctType() {
         return this.subsAcctType;
      }

      public String getStageType() {
         return this.stageType;
      }

      public String getPrecontractStartDate() {
         return this.precontractStartDate;
      }

      public String getPrecontractEndTime() {
         return this.precontractEndTime;
      }

      public void setIssueYear(String issueYear) {
         this.issueYear = issueYear;
      }

      public void setClientType(String clientType) {
         this.clientType = clientType;
      }

      public void setBranch(String branch) {
         this.branch = branch;
      }

      public void setIssueAmt(BigDecimal issueAmt) {
         this.issueAmt = issueAmt;
      }

      public void setStartDate(String startDate) {
         this.startDate = startDate;
      }

      public void setEndDate(String endDate) {
         this.endDate = endDate;
      }

      public void setSaleStartTime(String saleStartTime) {
         this.saleStartTime = saleStartTime;
      }

      public void setSaleEndTime(String saleEndTime) {
         this.saleEndTime = saleEndTime;
      }

      public void setStageMinAmt(BigDecimal stageMinAmt) {
         this.stageMinAmt = stageMinAmt;
      }

      public void setSgMaxAmt(BigDecimal sgMaxAmt) {
         this.sgMaxAmt = sgMaxAmt;
      }

      public void setKeepMinBal(BigDecimal keepMinBal) {
         this.keepMinBal = keepMinBal;
      }

      public void setTerm(String term) {
         this.term = term;
      }

      public void setIntType(String intType) {
         this.intType = intType;
      }

      public void setPayIntType(String payIntType) {
         this.payIntType = payIntType;
      }

      public void setPreWithdrawFlag(String preWithdrawFlag) {
         this.preWithdrawFlag = preWithdrawFlag;
      }

      public void setPreWithdrawNum(Integer preWithdrawNum) {
         this.preWithdrawNum = preWithdrawNum;
      }

      public void setIsFullTransfer(String isFullTransfer) {
         this.isFullTransfer = isFullTransfer;
      }

      public void setRedemptionFlag(String redemptionFlag) {
         this.redemptionFlag = redemptionFlag;
      }

      public void setTohonorDate(String tohonorDate) {
         this.tohonorDate = tohonorDate;
      }

      public void setTohonorRate(BigDecimal tohonorRate) {
         this.tohonorRate = tohonorRate;
      }

      public void setIntFlag(String intFlag) {
         this.intFlag = intFlag;
      }

      public void setFloatRate(BigDecimal floatRate) {
         this.floatRate = floatRate;
      }

      public void setTrfOutFeeAmt(BigDecimal trfOutFeeAmt) {
         this.trfOutFeeAmt = trfOutFeeAmt;
      }

      public void setApprovalNo(String approvalNo) {
         this.approvalNo = approvalNo;
      }

      public void setEmail(String email) {
         this.email = email;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setApplyUserId(String applyUserId) {
         this.applyUserId = applyUserId;
      }

      public void setClientListArray(List<Core1200100219In.Body.ClientListArray> clientListArray) {
         this.clientListArray = clientListArray;
      }

      public void setRealRate(BigDecimal realRate) {
         this.realRate = realRate;
      }

      public void setChannelArray(List<Core1200100219In.Body.ChannelArray> channelArray) {
         this.channelArray = channelArray;
      }

      public void setRedemptionIntType(String redemptionIntType) {
         this.redemptionIntType = redemptionIntType;
      }

      public void setTrfInFeeType(String trfInFeeType) {
         this.trfInFeeType = trfInFeeType;
      }

      public void setTrfOutFeeType(String trfOutFeeType) {
         this.trfOutFeeType = trfOutFeeType;
      }

      public void setIssueMethod(String issueMethod) {
         this.issueMethod = issueMethod;
      }

      public void setInFee(BigDecimal inFee) {
         this.inFee = inFee;
      }

      public void setIsCashFromInnerAcct(String isCashFromInnerAcct) {
         this.isCashFromInnerAcct = isCashFromInnerAcct;
      }

      public void setMinChangeBalance(BigDecimal minChangeBalance) {
         this.minChangeBalance = minChangeBalance;
      }

      public void setIntStartDate(String intStartDate) {
         this.intStartDate = intStartDate;
      }

      public void setMatureDate(String matureDate) {
         this.matureDate = matureDate;
      }

      public void setInlandOffshore(String inlandOffshore) {
         this.inlandOffshore = inlandOffshore;
      }

      public void setAcctType(String acctType) {
         this.acctType = acctType;
      }

      public void setTermType(String termType) {
         this.termType = termType;
      }

      public void setStageCode(String stageCode) {
         this.stageCode = stageCode;
      }

      public void setCalmDays(Integer calmDays) {
         this.calmDays = calmDays;
      }

      public void setSellBranch(String sellBranch) {
         this.sellBranch = sellBranch;
      }

      public void setStageCodeDesc(String stageCodeDesc) {
         this.stageCodeDesc = stageCodeDesc;
      }

      public void setAupCycle(String aupCycle) {
         this.aupCycle = aupCycle;
      }

      public void setCycleFreq(String cycleFreq) {
         this.cycleFreq = cycleFreq;
      }

      public void setDirectionChargeIntFlag(String directionChargeIntFlag) {
         this.directionChargeIntFlag = directionChargeIntFlag;
      }

      public void setRedeemIntDate(String redeemIntDate) {
         this.redeemIntDate = redeemIntDate;
      }

      public void setIntStartFlag(String intStartFlag) {
         this.intStartFlag = intStartFlag;
      }

      public void setCaptDate(String captDate) {
         this.captDate = captDate;
      }

      public void setGraceChargeIntFlag(String graceChargeIntFlag) {
         this.graceChargeIntFlag = graceChargeIntFlag;
      }

      public void setCombProdFlag(String combProdFlag) {
         this.combProdFlag = combProdFlag;
      }

      public void setTranPasswordOperateType(String tranPasswordOperateType) {
         this.tranPasswordOperateType = tranPasswordOperateType;
      }

      public void setSubsAcctType(String subsAcctType) {
         this.subsAcctType = subsAcctType;
      }

      public void setStageType(String stageType) {
         this.stageType = stageType;
      }

      public void setPrecontractStartDate(String precontractStartDate) {
         this.precontractStartDate = precontractStartDate;
      }

      public void setPrecontractEndTime(String precontractEndTime) {
         this.precontractEndTime = precontractEndTime;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1200100219In.Body)) {
            return false;
         } else {
            Core1200100219In.Body other = (Core1200100219In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label731: {
                  Object this$issueYear = this.getIssueYear();
                  Object other$issueYear = other.getIssueYear();
                  if (this$issueYear == null) {
                     if (other$issueYear == null) {
                        break label731;
                     }
                  } else if (this$issueYear.equals(other$issueYear)) {
                     break label731;
                  }

                  return false;
               }

               Object this$clientType = this.getClientType();
               Object other$clientType = other.getClientType();
               if (this$clientType == null) {
                  if (other$clientType != null) {
                     return false;
                  }
               } else if (!this$clientType.equals(other$clientType)) {
                  return false;
               }

               Object this$branch = this.getBranch();
               Object other$branch = other.getBranch();
               if (this$branch == null) {
                  if (other$branch != null) {
                     return false;
                  }
               } else if (!this$branch.equals(other$branch)) {
                  return false;
               }

               label710: {
                  Object this$issueAmt = this.getIssueAmt();
                  Object other$issueAmt = other.getIssueAmt();
                  if (this$issueAmt == null) {
                     if (other$issueAmt == null) {
                        break label710;
                     }
                  } else if (this$issueAmt.equals(other$issueAmt)) {
                     break label710;
                  }

                  return false;
               }

               label703: {
                  Object this$startDate = this.getStartDate();
                  Object other$startDate = other.getStartDate();
                  if (this$startDate == null) {
                     if (other$startDate == null) {
                        break label703;
                     }
                  } else if (this$startDate.equals(other$startDate)) {
                     break label703;
                  }

                  return false;
               }

               label696: {
                  Object this$endDate = this.getEndDate();
                  Object other$endDate = other.getEndDate();
                  if (this$endDate == null) {
                     if (other$endDate == null) {
                        break label696;
                     }
                  } else if (this$endDate.equals(other$endDate)) {
                     break label696;
                  }

                  return false;
               }

               Object this$saleStartTime = this.getSaleStartTime();
               Object other$saleStartTime = other.getSaleStartTime();
               if (this$saleStartTime == null) {
                  if (other$saleStartTime != null) {
                     return false;
                  }
               } else if (!this$saleStartTime.equals(other$saleStartTime)) {
                  return false;
               }

               label682: {
                  Object this$saleEndTime = this.getSaleEndTime();
                  Object other$saleEndTime = other.getSaleEndTime();
                  if (this$saleEndTime == null) {
                     if (other$saleEndTime == null) {
                        break label682;
                     }
                  } else if (this$saleEndTime.equals(other$saleEndTime)) {
                     break label682;
                  }

                  return false;
               }

               Object this$stageMinAmt = this.getStageMinAmt();
               Object other$stageMinAmt = other.getStageMinAmt();
               if (this$stageMinAmt == null) {
                  if (other$stageMinAmt != null) {
                     return false;
                  }
               } else if (!this$stageMinAmt.equals(other$stageMinAmt)) {
                  return false;
               }

               label668: {
                  Object this$sgMaxAmt = this.getSgMaxAmt();
                  Object other$sgMaxAmt = other.getSgMaxAmt();
                  if (this$sgMaxAmt == null) {
                     if (other$sgMaxAmt == null) {
                        break label668;
                     }
                  } else if (this$sgMaxAmt.equals(other$sgMaxAmt)) {
                     break label668;
                  }

                  return false;
               }

               Object this$keepMinBal = this.getKeepMinBal();
               Object other$keepMinBal = other.getKeepMinBal();
               if (this$keepMinBal == null) {
                  if (other$keepMinBal != null) {
                     return false;
                  }
               } else if (!this$keepMinBal.equals(other$keepMinBal)) {
                  return false;
               }

               Object this$term = this.getTerm();
               Object other$term = other.getTerm();
               if (this$term == null) {
                  if (other$term != null) {
                     return false;
                  }
               } else if (!this$term.equals(other$term)) {
                  return false;
               }

               label647: {
                  Object this$intType = this.getIntType();
                  Object other$intType = other.getIntType();
                  if (this$intType == null) {
                     if (other$intType == null) {
                        break label647;
                     }
                  } else if (this$intType.equals(other$intType)) {
                     break label647;
                  }

                  return false;
               }

               label640: {
                  Object this$payIntType = this.getPayIntType();
                  Object other$payIntType = other.getPayIntType();
                  if (this$payIntType == null) {
                     if (other$payIntType == null) {
                        break label640;
                     }
                  } else if (this$payIntType.equals(other$payIntType)) {
                     break label640;
                  }

                  return false;
               }

               Object this$preWithdrawFlag = this.getPreWithdrawFlag();
               Object other$preWithdrawFlag = other.getPreWithdrawFlag();
               if (this$preWithdrawFlag == null) {
                  if (other$preWithdrawFlag != null) {
                     return false;
                  }
               } else if (!this$preWithdrawFlag.equals(other$preWithdrawFlag)) {
                  return false;
               }

               Object this$preWithdrawNum = this.getPreWithdrawNum();
               Object other$preWithdrawNum = other.getPreWithdrawNum();
               if (this$preWithdrawNum == null) {
                  if (other$preWithdrawNum != null) {
                     return false;
                  }
               } else if (!this$preWithdrawNum.equals(other$preWithdrawNum)) {
                  return false;
               }

               label619: {
                  Object this$isFullTransfer = this.getIsFullTransfer();
                  Object other$isFullTransfer = other.getIsFullTransfer();
                  if (this$isFullTransfer == null) {
                     if (other$isFullTransfer == null) {
                        break label619;
                     }
                  } else if (this$isFullTransfer.equals(other$isFullTransfer)) {
                     break label619;
                  }

                  return false;
               }

               Object this$redemptionFlag = this.getRedemptionFlag();
               Object other$redemptionFlag = other.getRedemptionFlag();
               if (this$redemptionFlag == null) {
                  if (other$redemptionFlag != null) {
                     return false;
                  }
               } else if (!this$redemptionFlag.equals(other$redemptionFlag)) {
                  return false;
               }

               Object this$tohonorDate = this.getTohonorDate();
               Object other$tohonorDate = other.getTohonorDate();
               if (this$tohonorDate == null) {
                  if (other$tohonorDate != null) {
                     return false;
                  }
               } else if (!this$tohonorDate.equals(other$tohonorDate)) {
                  return false;
               }

               label598: {
                  Object this$tohonorRate = this.getTohonorRate();
                  Object other$tohonorRate = other.getTohonorRate();
                  if (this$tohonorRate == null) {
                     if (other$tohonorRate == null) {
                        break label598;
                     }
                  } else if (this$tohonorRate.equals(other$tohonorRate)) {
                     break label598;
                  }

                  return false;
               }

               label591: {
                  Object this$intFlag = this.getIntFlag();
                  Object other$intFlag = other.getIntFlag();
                  if (this$intFlag == null) {
                     if (other$intFlag == null) {
                        break label591;
                     }
                  } else if (this$intFlag.equals(other$intFlag)) {
                     break label591;
                  }

                  return false;
               }

               label584: {
                  Object this$floatRate = this.getFloatRate();
                  Object other$floatRate = other.getFloatRate();
                  if (this$floatRate == null) {
                     if (other$floatRate == null) {
                        break label584;
                     }
                  } else if (this$floatRate.equals(other$floatRate)) {
                     break label584;
                  }

                  return false;
               }

               Object this$trfOutFeeAmt = this.getTrfOutFeeAmt();
               Object other$trfOutFeeAmt = other.getTrfOutFeeAmt();
               if (this$trfOutFeeAmt == null) {
                  if (other$trfOutFeeAmt != null) {
                     return false;
                  }
               } else if (!this$trfOutFeeAmt.equals(other$trfOutFeeAmt)) {
                  return false;
               }

               label570: {
                  Object this$approvalNo = this.getApprovalNo();
                  Object other$approvalNo = other.getApprovalNo();
                  if (this$approvalNo == null) {
                     if (other$approvalNo == null) {
                        break label570;
                     }
                  } else if (this$approvalNo.equals(other$approvalNo)) {
                     break label570;
                  }

                  return false;
               }

               Object this$email = this.getEmail();
               Object other$email = other.getEmail();
               if (this$email == null) {
                  if (other$email != null) {
                     return false;
                  }
               } else if (!this$email.equals(other$email)) {
                  return false;
               }

               label556: {
                  Object this$acctCcy = this.getAcctCcy();
                  Object other$acctCcy = other.getAcctCcy();
                  if (this$acctCcy == null) {
                     if (other$acctCcy == null) {
                        break label556;
                     }
                  } else if (this$acctCcy.equals(other$acctCcy)) {
                     break label556;
                  }

                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               Object this$applyUserId = this.getApplyUserId();
               Object other$applyUserId = other.getApplyUserId();
               if (this$applyUserId == null) {
                  if (other$applyUserId != null) {
                     return false;
                  }
               } else if (!this$applyUserId.equals(other$applyUserId)) {
                  return false;
               }

               label535: {
                  Object this$clientListArray = this.getClientListArray();
                  Object other$clientListArray = other.getClientListArray();
                  if (this$clientListArray == null) {
                     if (other$clientListArray == null) {
                        break label535;
                     }
                  } else if (this$clientListArray.equals(other$clientListArray)) {
                     break label535;
                  }

                  return false;
               }

               label528: {
                  Object this$realRate = this.getRealRate();
                  Object other$realRate = other.getRealRate();
                  if (this$realRate == null) {
                     if (other$realRate == null) {
                        break label528;
                     }
                  } else if (this$realRate.equals(other$realRate)) {
                     break label528;
                  }

                  return false;
               }

               Object this$channelArray = this.getChannelArray();
               Object other$channelArray = other.getChannelArray();
               if (this$channelArray == null) {
                  if (other$channelArray != null) {
                     return false;
                  }
               } else if (!this$channelArray.equals(other$channelArray)) {
                  return false;
               }

               Object this$redemptionIntType = this.getRedemptionIntType();
               Object other$redemptionIntType = other.getRedemptionIntType();
               if (this$redemptionIntType == null) {
                  if (other$redemptionIntType != null) {
                     return false;
                  }
               } else if (!this$redemptionIntType.equals(other$redemptionIntType)) {
                  return false;
               }

               label507: {
                  Object this$trfInFeeType = this.getTrfInFeeType();
                  Object other$trfInFeeType = other.getTrfInFeeType();
                  if (this$trfInFeeType == null) {
                     if (other$trfInFeeType == null) {
                        break label507;
                     }
                  } else if (this$trfInFeeType.equals(other$trfInFeeType)) {
                     break label507;
                  }

                  return false;
               }

               Object this$trfOutFeeType = this.getTrfOutFeeType();
               Object other$trfOutFeeType = other.getTrfOutFeeType();
               if (this$trfOutFeeType == null) {
                  if (other$trfOutFeeType != null) {
                     return false;
                  }
               } else if (!this$trfOutFeeType.equals(other$trfOutFeeType)) {
                  return false;
               }

               Object this$issueMethod = this.getIssueMethod();
               Object other$issueMethod = other.getIssueMethod();
               if (this$issueMethod == null) {
                  if (other$issueMethod != null) {
                     return false;
                  }
               } else if (!this$issueMethod.equals(other$issueMethod)) {
                  return false;
               }

               label486: {
                  Object this$inFee = this.getInFee();
                  Object other$inFee = other.getInFee();
                  if (this$inFee == null) {
                     if (other$inFee == null) {
                        break label486;
                     }
                  } else if (this$inFee.equals(other$inFee)) {
                     break label486;
                  }

                  return false;
               }

               label479: {
                  Object this$isCashFromInnerAcct = this.getIsCashFromInnerAcct();
                  Object other$isCashFromInnerAcct = other.getIsCashFromInnerAcct();
                  if (this$isCashFromInnerAcct == null) {
                     if (other$isCashFromInnerAcct == null) {
                        break label479;
                     }
                  } else if (this$isCashFromInnerAcct.equals(other$isCashFromInnerAcct)) {
                     break label479;
                  }

                  return false;
               }

               label472: {
                  Object this$minChangeBalance = this.getMinChangeBalance();
                  Object other$minChangeBalance = other.getMinChangeBalance();
                  if (this$minChangeBalance == null) {
                     if (other$minChangeBalance == null) {
                        break label472;
                     }
                  } else if (this$minChangeBalance.equals(other$minChangeBalance)) {
                     break label472;
                  }

                  return false;
               }

               Object this$intStartDate = this.getIntStartDate();
               Object other$intStartDate = other.getIntStartDate();
               if (this$intStartDate == null) {
                  if (other$intStartDate != null) {
                     return false;
                  }
               } else if (!this$intStartDate.equals(other$intStartDate)) {
                  return false;
               }

               label458: {
                  Object this$matureDate = this.getMatureDate();
                  Object other$matureDate = other.getMatureDate();
                  if (this$matureDate == null) {
                     if (other$matureDate == null) {
                        break label458;
                     }
                  } else if (this$matureDate.equals(other$matureDate)) {
                     break label458;
                  }

                  return false;
               }

               Object this$inlandOffshore = this.getInlandOffshore();
               Object other$inlandOffshore = other.getInlandOffshore();
               if (this$inlandOffshore == null) {
                  if (other$inlandOffshore != null) {
                     return false;
                  }
               } else if (!this$inlandOffshore.equals(other$inlandOffshore)) {
                  return false;
               }

               label444: {
                  Object this$acctType = this.getAcctType();
                  Object other$acctType = other.getAcctType();
                  if (this$acctType == null) {
                     if (other$acctType == null) {
                        break label444;
                     }
                  } else if (this$acctType.equals(other$acctType)) {
                     break label444;
                  }

                  return false;
               }

               Object this$termType = this.getTermType();
               Object other$termType = other.getTermType();
               if (this$termType == null) {
                  if (other$termType != null) {
                     return false;
                  }
               } else if (!this$termType.equals(other$termType)) {
                  return false;
               }

               Object this$stageCode = this.getStageCode();
               Object other$stageCode = other.getStageCode();
               if (this$stageCode == null) {
                  if (other$stageCode != null) {
                     return false;
                  }
               } else if (!this$stageCode.equals(other$stageCode)) {
                  return false;
               }

               label423: {
                  Object this$calmDays = this.getCalmDays();
                  Object other$calmDays = other.getCalmDays();
                  if (this$calmDays == null) {
                     if (other$calmDays == null) {
                        break label423;
                     }
                  } else if (this$calmDays.equals(other$calmDays)) {
                     break label423;
                  }

                  return false;
               }

               label416: {
                  Object this$sellBranch = this.getSellBranch();
                  Object other$sellBranch = other.getSellBranch();
                  if (this$sellBranch == null) {
                     if (other$sellBranch == null) {
                        break label416;
                     }
                  } else if (this$sellBranch.equals(other$sellBranch)) {
                     break label416;
                  }

                  return false;
               }

               Object this$stageCodeDesc = this.getStageCodeDesc();
               Object other$stageCodeDesc = other.getStageCodeDesc();
               if (this$stageCodeDesc == null) {
                  if (other$stageCodeDesc != null) {
                     return false;
                  }
               } else if (!this$stageCodeDesc.equals(other$stageCodeDesc)) {
                  return false;
               }

               Object this$aupCycle = this.getAupCycle();
               Object other$aupCycle = other.getAupCycle();
               if (this$aupCycle == null) {
                  if (other$aupCycle != null) {
                     return false;
                  }
               } else if (!this$aupCycle.equals(other$aupCycle)) {
                  return false;
               }

               label395: {
                  Object this$cycleFreq = this.getCycleFreq();
                  Object other$cycleFreq = other.getCycleFreq();
                  if (this$cycleFreq == null) {
                     if (other$cycleFreq == null) {
                        break label395;
                     }
                  } else if (this$cycleFreq.equals(other$cycleFreq)) {
                     break label395;
                  }

                  return false;
               }

               Object this$directionChargeIntFlag = this.getDirectionChargeIntFlag();
               Object other$directionChargeIntFlag = other.getDirectionChargeIntFlag();
               if (this$directionChargeIntFlag == null) {
                  if (other$directionChargeIntFlag != null) {
                     return false;
                  }
               } else if (!this$directionChargeIntFlag.equals(other$directionChargeIntFlag)) {
                  return false;
               }

               Object this$redeemIntDate = this.getRedeemIntDate();
               Object other$redeemIntDate = other.getRedeemIntDate();
               if (this$redeemIntDate == null) {
                  if (other$redeemIntDate != null) {
                     return false;
                  }
               } else if (!this$redeemIntDate.equals(other$redeemIntDate)) {
                  return false;
               }

               label374: {
                  Object this$intStartFlag = this.getIntStartFlag();
                  Object other$intStartFlag = other.getIntStartFlag();
                  if (this$intStartFlag == null) {
                     if (other$intStartFlag == null) {
                        break label374;
                     }
                  } else if (this$intStartFlag.equals(other$intStartFlag)) {
                     break label374;
                  }

                  return false;
               }

               label367: {
                  Object this$captDate = this.getCaptDate();
                  Object other$captDate = other.getCaptDate();
                  if (this$captDate == null) {
                     if (other$captDate == null) {
                        break label367;
                     }
                  } else if (this$captDate.equals(other$captDate)) {
                     break label367;
                  }

                  return false;
               }

               label360: {
                  Object this$graceChargeIntFlag = this.getGraceChargeIntFlag();
                  Object other$graceChargeIntFlag = other.getGraceChargeIntFlag();
                  if (this$graceChargeIntFlag == null) {
                     if (other$graceChargeIntFlag == null) {
                        break label360;
                     }
                  } else if (this$graceChargeIntFlag.equals(other$graceChargeIntFlag)) {
                     break label360;
                  }

                  return false;
               }

               Object this$combProdFlag = this.getCombProdFlag();
               Object other$combProdFlag = other.getCombProdFlag();
               if (this$combProdFlag == null) {
                  if (other$combProdFlag != null) {
                     return false;
                  }
               } else if (!this$combProdFlag.equals(other$combProdFlag)) {
                  return false;
               }

               label346: {
                  Object this$tranPasswordOperateType = this.getTranPasswordOperateType();
                  Object other$tranPasswordOperateType = other.getTranPasswordOperateType();
                  if (this$tranPasswordOperateType == null) {
                     if (other$tranPasswordOperateType == null) {
                        break label346;
                     }
                  } else if (this$tranPasswordOperateType.equals(other$tranPasswordOperateType)) {
                     break label346;
                  }

                  return false;
               }

               Object this$subsAcctType = this.getSubsAcctType();
               Object other$subsAcctType = other.getSubsAcctType();
               if (this$subsAcctType == null) {
                  if (other$subsAcctType != null) {
                     return false;
                  }
               } else if (!this$subsAcctType.equals(other$subsAcctType)) {
                  return false;
               }

               label332: {
                  Object this$stageType = this.getStageType();
                  Object other$stageType = other.getStageType();
                  if (this$stageType == null) {
                     if (other$stageType == null) {
                        break label332;
                     }
                  } else if (this$stageType.equals(other$stageType)) {
                     break label332;
                  }

                  return false;
               }

               Object this$precontractStartDate = this.getPrecontractStartDate();
               Object other$precontractStartDate = other.getPrecontractStartDate();
               if (this$precontractStartDate == null) {
                  if (other$precontractStartDate != null) {
                     return false;
                  }
               } else if (!this$precontractStartDate.equals(other$precontractStartDate)) {
                  return false;
               }

               Object this$precontractEndTime = this.getPrecontractEndTime();
               Object other$precontractEndTime = other.getPrecontractEndTime();
               if (this$precontractEndTime == null) {
                  if (other$precontractEndTime != null) {
                     return false;
                  }
               } else if (!this$precontractEndTime.equals(other$precontractEndTime)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1200100219In.Body;
      }
      public String toString() {
         return "Core1200100219In.Body(issueYear=" + this.getIssueYear() + ", clientType=" + this.getClientType() + ", branch=" + this.getBranch() + ", issueAmt=" + this.getIssueAmt() + ", startDate=" + this.getStartDate() + ", endDate=" + this.getEndDate() + ", saleStartTime=" + this.getSaleStartTime() + ", saleEndTime=" + this.getSaleEndTime() + ", stageMinAmt=" + this.getStageMinAmt() + ", sgMaxAmt=" + this.getSgMaxAmt() + ", keepMinBal=" + this.getKeepMinBal() + ", term=" + this.getTerm() + ", intType=" + this.getIntType() + ", payIntType=" + this.getPayIntType() + ", preWithdrawFlag=" + this.getPreWithdrawFlag() + ", preWithdrawNum=" + this.getPreWithdrawNum() + ", isFullTransfer=" + this.getIsFullTransfer() + ", redemptionFlag=" + this.getRedemptionFlag() + ", tohonorDate=" + this.getTohonorDate() + ", tohonorRate=" + this.getTohonorRate() + ", intFlag=" + this.getIntFlag() + ", floatRate=" + this.getFloatRate() + ", trfOutFeeAmt=" + this.getTrfOutFeeAmt() + ", approvalNo=" + this.getApprovalNo() + ", email=" + this.getEmail() + ", acctCcy=" + this.getAcctCcy() + ", prodType=" + this.getProdType() + ", applyUserId=" + this.getApplyUserId() + ", clientListArray=" + this.getClientListArray() + ", realRate=" + this.getRealRate() + ", channelArray=" + this.getChannelArray() + ", redemptionIntType=" + this.getRedemptionIntType() + ", trfInFeeType=" + this.getTrfInFeeType() + ", trfOutFeeType=" + this.getTrfOutFeeType() + ", issueMethod=" + this.getIssueMethod() + ", inFee=" + this.getInFee() + ", isCashFromInnerAcct=" + this.getIsCashFromInnerAcct() + ", minChangeBalance=" + this.getMinChangeBalance() + ", intStartDate=" + this.getIntStartDate() + ", matureDate=" + this.getMatureDate() + ", inlandOffshore=" + this.getInlandOffshore() + ", acctType=" + this.getAcctType() + ", termType=" + this.getTermType() + ", stageCode=" + this.getStageCode() + ", calmDays=" + this.getCalmDays() + ", sellBranch=" + this.getSellBranch() + ", stageCodeDesc=" + this.getStageCodeDesc() + ", aupCycle=" + this.getAupCycle() + ", cycleFreq=" + this.getCycleFreq() + ", directionChargeIntFlag=" + this.getDirectionChargeIntFlag() + ", redeemIntDate=" + this.getRedeemIntDate() + ", intStartFlag=" + this.getIntStartFlag() + ", captDate=" + this.getCaptDate() + ", graceChargeIntFlag=" + this.getGraceChargeIntFlag() + ", combProdFlag=" + this.getCombProdFlag() + ", tranPasswordOperateType=" + this.getTranPasswordOperateType() + ", subsAcctType=" + this.getSubsAcctType() + ", stageType=" + this.getStageType() + ", precontractStartDate=" + this.getPrecontractStartDate() + ", precontractEndTime=" + this.getPrecontractEndTime() + ")";
      }

      public static class ChannelArray {
         @V(
            desc = "渠道",
            notNull = false,
            length = "10",
            inDesc = "JZ-集中作业,MT-柜面,ZD-智能网点自助交易平台,VT-远程智能自助银行,QT-XBankT10,QE-快窗智能高柜,CP-手机银行,EB-个人网上银行,CB-企业网上银行,CM-现金管理,WB-微信银行A ,PT-支付",
            remark = "渠道细类",
            maxSize = 10
         )
         private String channel;

         public String getChannel() {
            return this.channel;
         }

         public void setChannel(String channel) {
            this.channel = channel;
         }

         public boolean equals(Object o) {
            if (o == this) {
               return true;
            } else if (!(o instanceof Core1200100219In.Body.ChannelArray)) {
               return false;
            } else {
               Core1200100219In.Body.ChannelArray other = (Core1200100219In.Body.ChannelArray)o;
               if (!other.canEqual(this)) {
                  return false;
               } else {
                  Object this$channel = this.getChannel();
                  Object other$channel = other.getChannel();
                  if (this$channel == null) {
                     if (other$channel != null) {
                        return false;
                     }
                  } else if (!this$channel.equals(other$channel)) {
                     return false;
                  }

                  return true;
               }
            }
         }

         protected boolean canEqual(Object other) {
            return other instanceof Core1200100219In.Body.ChannelArray;
         }
         public String toString() {
            return "Core1200100219In.Body.ChannelArray(channel=" + this.getChannel() + ")";
         }
      }

      public static class ClientListArray {
         @V(
            desc = "客户号",
            notNull = false,
            length = "20",
            remark = "客户号",
            maxSize = 20
         )
         private String clientNo;
         @V(
            desc = "客户名称",
            notNull = false,
            length = "200",
            remark = "客户名称",
            maxSize = 200
         )
         private String clientName;
         @V(
            desc = "证件号码",
            notNull = false,
            length = "50",
            remark = "证件号码",
            maxSize = 50
         )
         private String documentId;
         @V(
            desc = "证件类型",
            notNull = false,
            length = "3",
            remark = "证件类型",
            maxSize = 3
         )
         private String documentType;
         @V(
            desc = "对客户信息操作类型",
            notNull = false,
            length = "2",
            inDesc = "A-增,U-改,D-删",
            remark = "对客户信息操作类型",
            maxSize = 2
         )
         private String optionCli;

         public String getClientNo() {
            return this.clientNo;
         }

         public String getClientName() {
            return this.clientName;
         }

         public String getDocumentId() {
            return this.documentId;
         }

         public String getDocumentType() {
            return this.documentType;
         }

         public String getOptionCli() {
            return this.optionCli;
         }

         public void setClientNo(String clientNo) {
            this.clientNo = clientNo;
         }

         public void setClientName(String clientName) {
            this.clientName = clientName;
         }

         public void setDocumentId(String documentId) {
            this.documentId = documentId;
         }

         public void setDocumentType(String documentType) {
            this.documentType = documentType;
         }

         public void setOptionCli(String optionCli) {
            this.optionCli = optionCli;
         }

         public boolean equals(Object o) {
            if (o == this) {
               return true;
            } else if (!(o instanceof Core1200100219In.Body.ClientListArray)) {
               return false;
            } else {
               Core1200100219In.Body.ClientListArray other = (Core1200100219In.Body.ClientListArray)o;
               if (!other.canEqual(this)) {
                  return false;
               } else {
                  label71: {
                     Object this$clientNo = this.getClientNo();
                     Object other$clientNo = other.getClientNo();
                     if (this$clientNo == null) {
                        if (other$clientNo == null) {
                           break label71;
                        }
                     } else if (this$clientNo.equals(other$clientNo)) {
                        break label71;
                     }

                     return false;
                  }

                  Object this$clientName = this.getClientName();
                  Object other$clientName = other.getClientName();
                  if (this$clientName == null) {
                     if (other$clientName != null) {
                        return false;
                     }
                  } else if (!this$clientName.equals(other$clientName)) {
                     return false;
                  }

                  label57: {
                     Object this$documentId = this.getDocumentId();
                     Object other$documentId = other.getDocumentId();
                     if (this$documentId == null) {
                        if (other$documentId == null) {
                           break label57;
                        }
                     } else if (this$documentId.equals(other$documentId)) {
                        break label57;
                     }

                     return false;
                  }

                  Object this$documentType = this.getDocumentType();
                  Object other$documentType = other.getDocumentType();
                  if (this$documentType == null) {
                     if (other$documentType != null) {
                        return false;
                     }
                  } else if (!this$documentType.equals(other$documentType)) {
                     return false;
                  }

                  Object this$optionCli = this.getOptionCli();
                  Object other$optionCli = other.getOptionCli();
                  if (this$optionCli == null) {
                     if (other$optionCli == null) {
                        return true;
                     }
                  } else if (this$optionCli.equals(other$optionCli)) {
                     return true;
                  }

                  return false;
               }
            }
         }

         protected boolean canEqual(Object other) {
            return other instanceof Core1200100219In.Body.ClientListArray;
         }
         public String toString() {
            return "Core1200100219In.Body.ClientListArray(clientNo=" + this.getClientNo() + ", clientName=" + this.getClientName() + ", documentId=" + this.getDocumentId() + ", documentType=" + this.getDocumentType() + ", optionCli=" + this.getOptionCli() + ")";
         }
      }
   }
}
