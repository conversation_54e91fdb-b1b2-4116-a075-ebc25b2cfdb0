package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@MessageIn
public class Core1200100222In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1200100222In.Body body;

   public Core1200100222In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1200100222In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1200100222In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200100222In)) {
         return false;
      } else {
         Core1200100222In other = (Core1200100222In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200100222In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "配额类型",
         notNull = true,
         length = "2",
         inDesc = "NO-不分配额度,CH-可按渠道分配,BR-可按分行分配,WL-白名单额度分配",
         remark = "配额类型",
         maxSize = 2
      )
      private String operateMethod;
      @V(
         desc = "期次代码",
         notNull = true,
         length = "50",
         remark = "期次代码",
         maxSize = 50
      )
      private String stageCode;
      @V(
         desc = "数组",
         notNull = false,
         remark = "数组"
      )
      private List<Core1200100222In.Body.BranchArray> branchArray;
      @V(
         desc = "数组",
         notNull = false,
         remark = "数组"
      )
      private List<Core1200100222In.Body.ClientListArray> clientListArray;
      @V(
         desc = "数组",
         notNull = false,
         remark = "数组"
      )
      private List<Core1200100222In.Body.ChannelArray> channelArray;
      @V(
         desc = "发行年度",
         notNull = true,
         length = "5",
         remark = "发行年度",
         maxSize = 5
      )
      private String issueYear;
      @V(
         desc = "产品类型",
         notNull = true,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;

      public String getOperateMethod() {
         return this.operateMethod;
      }

      public String getStageCode() {
         return this.stageCode;
      }

      public List<Core1200100222In.Body.BranchArray> getBranchArray() {
         return this.branchArray;
      }

      public List<Core1200100222In.Body.ClientListArray> getClientListArray() {
         return this.clientListArray;
      }

      public List<Core1200100222In.Body.ChannelArray> getChannelArray() {
         return this.channelArray;
      }

      public String getIssueYear() {
         return this.issueYear;
      }

      public String getProdType() {
         return this.prodType;
      }

      public void setOperateMethod(String operateMethod) {
         this.operateMethod = operateMethod;
      }

      public void setStageCode(String stageCode) {
         this.stageCode = stageCode;
      }

      public void setBranchArray(List<Core1200100222In.Body.BranchArray> branchArray) {
         this.branchArray = branchArray;
      }

      public void setClientListArray(List<Core1200100222In.Body.ClientListArray> clientListArray) {
         this.clientListArray = clientListArray;
      }

      public void setChannelArray(List<Core1200100222In.Body.ChannelArray> channelArray) {
         this.channelArray = channelArray;
      }

      public void setIssueYear(String issueYear) {
         this.issueYear = issueYear;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1200100222In.Body)) {
            return false;
         } else {
            Core1200100222In.Body other = (Core1200100222In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label95: {
                  Object this$operateMethod = this.getOperateMethod();
                  Object other$operateMethod = other.getOperateMethod();
                  if (this$operateMethod == null) {
                     if (other$operateMethod == null) {
                        break label95;
                     }
                  } else if (this$operateMethod.equals(other$operateMethod)) {
                     break label95;
                  }

                  return false;
               }

               Object this$stageCode = this.getStageCode();
               Object other$stageCode = other.getStageCode();
               if (this$stageCode == null) {
                  if (other$stageCode != null) {
                     return false;
                  }
               } else if (!this$stageCode.equals(other$stageCode)) {
                  return false;
               }

               Object this$branchArray = this.getBranchArray();
               Object other$branchArray = other.getBranchArray();
               if (this$branchArray == null) {
                  if (other$branchArray != null) {
                     return false;
                  }
               } else if (!this$branchArray.equals(other$branchArray)) {
                  return false;
               }

               label74: {
                  Object this$clientListArray = this.getClientListArray();
                  Object other$clientListArray = other.getClientListArray();
                  if (this$clientListArray == null) {
                     if (other$clientListArray == null) {
                        break label74;
                     }
                  } else if (this$clientListArray.equals(other$clientListArray)) {
                     break label74;
                  }

                  return false;
               }

               label67: {
                  Object this$channelArray = this.getChannelArray();
                  Object other$channelArray = other.getChannelArray();
                  if (this$channelArray == null) {
                     if (other$channelArray == null) {
                        break label67;
                     }
                  } else if (this$channelArray.equals(other$channelArray)) {
                     break label67;
                  }

                  return false;
               }

               Object this$issueYear = this.getIssueYear();
               Object other$issueYear = other.getIssueYear();
               if (this$issueYear == null) {
                  if (other$issueYear != null) {
                     return false;
                  }
               } else if (!this$issueYear.equals(other$issueYear)) {
                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1200100222In.Body;
      }
      public String toString() {
         return "Core1200100222In.Body(operateMethod=" + this.getOperateMethod() + ", stageCode=" + this.getStageCode() + ", branchArray=" + this.getBranchArray() + ", clientListArray=" + this.getClientListArray() + ", channelArray=" + this.getChannelArray() + ", issueYear=" + this.getIssueYear() + ", prodType=" + this.getProdType() + ")";
      }

      public static class ChannelArray {
         @V(
            desc = "渠道额度",
            notNull = false,
            length = "17",
            remark = "渠道额度",
            decimalLength = 2,
            precision = 2
         )
         private BigDecimal channelLimit;
         @V(
            desc = "渠道",
            notNull = false,
            length = "10",
            inDesc = "JZ-集中作业,MT-柜面,ZD-智能网点自助交易平台,VT-远程智能自助银行,QT-XBankT10,QE-快窗智能高柜,CP-手机银行,EB-个人网上银行,CB-企业网上银行,CM-现金管理,WB-微信银行A ,PT-支付",
            remark = "渠道细类",
            maxSize = 10
         )
         private String channel;

         public BigDecimal getChannelLimit() {
            return this.channelLimit;
         }

         public String getChannel() {
            return this.channel;
         }

         public void setChannelLimit(BigDecimal channelLimit) {
            this.channelLimit = channelLimit;
         }

         public void setChannel(String channel) {
            this.channel = channel;
         }

         public boolean equals(Object o) {
            if (o == this) {
               return true;
            } else if (!(o instanceof Core1200100222In.Body.ChannelArray)) {
               return false;
            } else {
               Core1200100222In.Body.ChannelArray other = (Core1200100222In.Body.ChannelArray)o;
               if (!other.canEqual(this)) {
                  return false;
               } else {
                  Object this$channelLimit = this.getChannelLimit();
                  Object other$channelLimit = other.getChannelLimit();
                  if (this$channelLimit == null) {
                     if (other$channelLimit != null) {
                        return false;
                     }
                  } else if (!this$channelLimit.equals(other$channelLimit)) {
                     return false;
                  }

                  Object this$channel = this.getChannel();
                  Object other$channel = other.getChannel();
                  if (this$channel == null) {
                     if (other$channel != null) {
                        return false;
                     }
                  } else if (!this$channel.equals(other$channel)) {
                     return false;
                  }

                  return true;
               }
            }
         }

         protected boolean canEqual(Object other) {
            return other instanceof Core1200100222In.Body.ChannelArray;
         }
         public String toString() {
            return "Core1200100222In.Body.ChannelArray(channelLimit=" + this.getChannelLimit() + ", channel=" + this.getChannel() + ")";
         }
      }

      public static class ClientListArray {
         @V(
            desc = "白名单额度",
            notNull = true,
            length = "17",
            remark = "白名单额度",
            decimalLength = 2,
            precision = 2
         )
         private BigDecimal whiteLimit;
         @V(
            desc = "客户号",
            notNull = true,
            length = "20",
            remark = "客户号",
            maxSize = 20
         )
         private String clientNo;
         @V(
            desc = "证件类型",
            notNull = true,
            length = "3",
            remark = "证件类型",
            maxSize = 3
         )
         private String documentType;
         @V(
            desc = "证件号码",
            notNull = true,
            length = "50",
            remark = "证件号码",
            maxSize = 50
         )
         private String documentId;

         public BigDecimal getWhiteLimit() {
            return this.whiteLimit;
         }

         public String getClientNo() {
            return this.clientNo;
         }

         public String getDocumentType() {
            return this.documentType;
         }

         public String getDocumentId() {
            return this.documentId;
         }

         public void setWhiteLimit(BigDecimal whiteLimit) {
            this.whiteLimit = whiteLimit;
         }

         public void setClientNo(String clientNo) {
            this.clientNo = clientNo;
         }

         public void setDocumentType(String documentType) {
            this.documentType = documentType;
         }

         public void setDocumentId(String documentId) {
            this.documentId = documentId;
         }

         public boolean equals(Object o) {
            if (o == this) {
               return true;
            } else if (!(o instanceof Core1200100222In.Body.ClientListArray)) {
               return false;
            } else {
               Core1200100222In.Body.ClientListArray other = (Core1200100222In.Body.ClientListArray)o;
               if (!other.canEqual(this)) {
                  return false;
               } else {
                  label59: {
                     Object this$whiteLimit = this.getWhiteLimit();
                     Object other$whiteLimit = other.getWhiteLimit();
                     if (this$whiteLimit == null) {
                        if (other$whiteLimit == null) {
                           break label59;
                        }
                     } else if (this$whiteLimit.equals(other$whiteLimit)) {
                        break label59;
                     }

                     return false;
                  }

                  Object this$clientNo = this.getClientNo();
                  Object other$clientNo = other.getClientNo();
                  if (this$clientNo == null) {
                     if (other$clientNo != null) {
                        return false;
                     }
                  } else if (!this$clientNo.equals(other$clientNo)) {
                     return false;
                  }

                  Object this$documentType = this.getDocumentType();
                  Object other$documentType = other.getDocumentType();
                  if (this$documentType == null) {
                     if (other$documentType != null) {
                        return false;
                     }
                  } else if (!this$documentType.equals(other$documentType)) {
                     return false;
                  }

                  Object this$documentId = this.getDocumentId();
                  Object other$documentId = other.getDocumentId();
                  if (this$documentId == null) {
                     if (other$documentId != null) {
                        return false;
                     }
                  } else if (!this$documentId.equals(other$documentId)) {
                     return false;
                  }

                  return true;
               }
            }
         }

         protected boolean canEqual(Object other) {
            return other instanceof Core1200100222In.Body.ClientListArray;
         }
         public String toString() {
            return "Core1200100222In.Body.ClientListArray(whiteLimit=" + this.getWhiteLimit() + ", clientNo=" + this.getClientNo() + ", documentType=" + this.getDocumentType() + ", documentId=" + this.getDocumentId() + ")";
         }
      }

      public static class BranchArray {
         @V(
            desc = "所属机构号",
            notNull = false,
            length = "50",
            remark = "所属机构号",
            maxSize = 50
         )
         private String branch;
         @V(
            desc = "支行额度",
            notNull = false,
            length = "17",
            remark = "支行额度",
            decimalLength = 2,
            precision = 2
         )
         private BigDecimal branchLimit;

         public String getBranch() {
            return this.branch;
         }

         public BigDecimal getBranchLimit() {
            return this.branchLimit;
         }

         public void setBranch(String branch) {
            this.branch = branch;
         }

         public void setBranchLimit(BigDecimal branchLimit) {
            this.branchLimit = branchLimit;
         }

         public boolean equals(Object o) {
            if (o == this) {
               return true;
            } else if (!(o instanceof Core1200100222In.Body.BranchArray)) {
               return false;
            } else {
               Core1200100222In.Body.BranchArray other = (Core1200100222In.Body.BranchArray)o;
               if (!other.canEqual(this)) {
                  return false;
               } else {
                  Object this$branch = this.getBranch();
                  Object other$branch = other.getBranch();
                  if (this$branch == null) {
                     if (other$branch != null) {
                        return false;
                     }
                  } else if (!this$branch.equals(other$branch)) {
                     return false;
                  }

                  Object this$branchLimit = this.getBranchLimit();
                  Object other$branchLimit = other.getBranchLimit();
                  if (this$branchLimit == null) {
                     if (other$branchLimit != null) {
                        return false;
                     }
                  } else if (!this$branchLimit.equals(other$branchLimit)) {
                     return false;
                  }

                  return true;
               }
            }
         }

         protected boolean canEqual(Object other) {
            return other instanceof Core1200100222In.Body.BranchArray;
         }
         public String toString() {
            return "Core1200100222In.Body.BranchArray(branch=" + this.getBranch() + ", branchLimit=" + this.getBranchLimit() + ")";
         }
      }
   }
}
