package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000232In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000232Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12000232 {
   String URL = "/rb/nfin/yht/batch/close";


   @EnsembleElements(
      serviceCode = "Core",
      messageType = "1200",
      messageCode = "0232"
   )
   @FunctionCategory("RB14-一户通")
   Core12000232Out runService(Core12000232In var1);
}
