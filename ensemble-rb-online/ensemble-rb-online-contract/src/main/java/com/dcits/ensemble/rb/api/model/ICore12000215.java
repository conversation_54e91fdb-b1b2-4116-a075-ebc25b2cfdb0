package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000215In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000215Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12000215 {
   String URL = "/rb/nfin/dc/transfer/apply";

   
   @ApiRemark("转让申请提交，日终计算转让价格")
   @ApiDesc("该功能用于大额存单转让申请，转让申请分为全部转让和部分转让。")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0215"
   )
   @BusinessCategory("1200-非金融")
   @FunctionCategory("RB10-大额存单")
   @ConsumeSys("EOS")
   @ApiUseStatus("PRODUCT-产品")
   Core12000215Out runService(Core12000215In var1);
}
