package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400100239Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100239Out.StageCodeInfoArray> stageCodeInfoArray;

   public List<Core1400100239Out.StageCodeInfoArray> getStageCodeInfoArray() {
      return this.stageCodeInfoArray;
   }

   public void setStageCodeInfoArray(List<Core1400100239Out.StageCodeInfoArray> stageCodeInfoArray) {
      this.stageCodeInfoArray = stageCodeInfoArray;
   }

   public String toString() {
      return "Core1400100239Out(stageCodeInfoArray=" + this.getStageCodeInfoArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100239Out)) {
         return false;
      } else {
         Core1400100239Out other = (Core1400100239Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$stageCodeInfoArray = this.getStageCodeInfoArray();
            Object other$stageCodeInfoArray = other.getStageCodeInfoArray();
            if (this$stageCodeInfoArray == null) {
               if (other$stageCodeInfoArray != null) {
                  return false;
               }
            } else if (!this$stageCodeInfoArray.equals(other$stageCodeInfoArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100239Out;
   }
   public static class StageCodeInfoArray {
      @V(
         desc = "期次代码",
         notNull = false,
         length = "50",
         remark = "期次代码",
         maxSize = 50
      )
      private String stageCode;
      @V(
         desc = "期次描述",
         notNull = false,
         length = "200",
         remark = "期次描述",
         maxSize = 200
      )
      private String stageCodeDesc;
      @V(
         desc = "发行年度",
         notNull = false,
         length = "5",
         remark = "发行年度",
         maxSize = 5
      )
      private String issueYear;
      @V(
         desc = "第二语言期次描述",
         notNull = false,
         length = "50",
         remark = "第二语言期次描述",
         maxSize = 50
      )
      private String stageCodeDescSecond;
      @V(
         desc = "客户类型",
         notNull = false,
         length = "3",
         remark = "客户大类，目前一般分为个人，公司，金融机构和内部客户。取之于CIF_CLIENT_TYPE.CLIENT_TYPE",
         maxSize = 3
      )
      private String clientType;
      @V(
         desc = "所属机构号",
         notNull = false,
         length = "50",
         remark = "机构代码",
         maxSize = 50
      )
      private String branch;
      @V(
         desc = "期次发行金额",
         notNull = false,
         length = "17",
         remark = "期次发行金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal issueAmt;
      @V(
         desc = "开始日期",
         notNull = false,
         remark = "开始日期"
      )
      private String startDate;
      @V(
         desc = "结束日期",
         notNull = false,
         remark = "结束日期"
      )
      private String endDate;
      @V(
         desc = "起售时间",
         notNull = false,
         length = "26",
         remark = "起售时间",
         maxSize = 26
      )
      private String saleStartTime;
      @V(
         desc = "止售时间",
         notNull = false,
         length = "26",
         remark = "止售时间",
         maxSize = 26
      )
      private String saleEndTime;
      @V(
         desc = "期次起存金额",
         notNull = false,
         length = "17",
         remark = "期次起存金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal stageMinAmt;
      @V(
         desc = "最小留存金额",
         notNull = false,
         length = "17",
         remark = "最小留存金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal keepMinBal;
      @V(
         desc = "存期期限",
         notNull = false,
         length = "5",
         remark = "期限",
         maxSize = 5
      )
      private String term;
      @V(
         desc = "利率类型",
         notNull = false,
         length = "5",
         remark = "利率类型",
         maxSize = 5
      )
      private String intType;
      @V(
         desc = "付息方式",
         notNull = false,
         length = "3",
         remark = "付息方式",
         maxSize = 3
      )
      private String payIntType;
      @V(
         desc = "是否允许提前支取",
         notNull = false,
         length = "1",
         remark = "是否允许提前支取",
         maxSize = 1
      )
      private String preWithdrawFlag;
      @V(
         desc = "提前支取次数",
         notNull = false,
         length = "5",
         remark = "提前支取次数"
      )
      private Integer preWithdrawNum;
      @V(
         desc = "是否全额转让",
         notNull = false,
         length = "1",
         remark = "定期转让是否全额转让 Y -是 N -否",
         maxSize = 1
      )
      private String isFullTransfer;
      @V(
         desc = "是否可赎回",
         notNull = false,
         length = "1",
         remark = "是否可赎回",
         maxSize = 1
      )
      private String redemptionFlag;
      @V(
         desc = "赎回利率",
         notNull = false,
         length = "15",
         remark = "赎回利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal tohonorRate;
      @V(
         desc = "赎回日期",
         notNull = false,
         remark = "赎回日期"
      )
      private String tohonorDate;
      @V(
         desc = "浮动利率",
         notNull = false,
         length = "15",
         remark = "浮动利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal floatRate;
      @V(
         desc = "电子邮件",
         notNull = false,
         length = "200",
         remark = "电子邮件",
         maxSize = 200
      )
      private String email;
      @V(
         desc = "转出费用",
         notNull = false,
         length = "17",
         remark = "转出费用",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal trfOutFeeAmt;
      @V(
         desc = "转入费用类型",
         notNull = false,
         length = "20",
         remark = "转入费用类型",
         maxSize = 20
      )
      private String trfInFeeType;
      @V(
         desc = "转出费用类型",
         notNull = false,
         length = "20",
         remark = "转出费用类型",
         maxSize = 20
      )
      private String trfOutFeeType;
      @V(
         desc = "执行利率",
         notNull = false,
         length = "15",
         remark = "执行利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal realRate;
      @V(
         desc = "赎回利率类型",
         notNull = false,
         length = "5",
         remark = "赎回利率类型",
         maxSize = 5
      )
      private String redemptionIntType;
      @V(
         desc = "转入手续费",
         notNull = false,
         length = "17",
         remark = "转入手续费",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal inFee;
      @V(
         desc = "发行方式",
         notNull = false,
         length = "20",
         remark = "发行方式",
         maxSize = 20
      )
      private String issueMethod;
      @V(
         desc = "资金来源是否基于内部户",
         notNull = false,
         length = "2",
         remark = "资金来源是否基于内部户",
         maxSize = 2
      )
      private String isCashFromInnerAcct;
      @V(
         desc = "最小变动额",
         notNull = false,
         length = "17",
         remark = "最小变动额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal minChangeBalance;
      @V(
         desc = "起息日",
         notNull = false,
         remark = "起息日"
      )
      private String intStartDate;
      @V(
         desc = "到期日",
         notNull = false,
         remark = "到期日"
      )
      private String matureDate;
      @V(
         desc = "境内境外标志",
         notNull = false,
         length = "1",
         remark = "境内境外标志",
         maxSize = 1
      )
      private String inlandOffshore;
      @V(
         desc = "账户类型",
         notNull = false,
         length = "1",
         remark = "账户类型",
         maxSize = 1
      )
      private String acctType;
      @V(
         desc = "期限类型",
         notNull = false,
         length = "1",
         remark = "期限类型",
         maxSize = 1
      )
      private String termType;
      @V(
         desc = "数组",
         notNull = false,
         remark = "数组"
      )
      private List<Core1400100239Out.StageCodeInfoArray.ChannelArray> channelArray;
      @V(
         desc = "起息标识",
         notNull = false,
         length = "1",
         remark = "起息标识",
         maxSize = 1
      )
      private String intStartFlag;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "剩余额度",
         notNull = false,
         length = "17",
         remark = "剩余额度",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal leaveQuota;
      @V(
         desc = "单笔认购最大金额",
         notNull = false,
         length = "17",
         remark = "单笔认购最大金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal sgMaxAmt;
      @V(
         desc = "结息频率",
         notNull = false,
         length = "5",
         remark = "结息频率",
         maxSize = 5
      )
      private String cycleFreq;
      @V(
         desc = "是否指定收息",
         notNull = false,
         length = "1",
         remark = "是否指定收息",
         maxSize = 1
      )
      private String directionChargeIntFlag;
      @V(
         desc = "新旧资金标识  | Y-新资金 N-不区分",
         notNull = false,
         length = "1",
         remark = "新旧资金标识  | Y-不区分 N-新资金",
         maxSize = 1
      )
      private String newAndOldFundFlag;
      @V(
         desc = "客户群体",
         notNull = false,
         length = "20",
         remark = "客户群体",
         maxSize = 20
      )
      private String customerBase;
      @V(
         desc = "最高客户额度",
         notNull = false,
         length = "17",
         remark = "最高客户额度",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal maxCustomerLimit;
      @V(
         desc = "是否可转让标志",
         notNull = false,
         length = "1",
         remark = "是否可转让标志",
         maxSize = 1
      )
      private String isTrfEnabled;
      @V(
         desc = "冷静期",
         notNull = false,
         length = "5",
         remark = "大额存单兑付冷静期"
      )
      private Integer calmDays;
      @V(
         desc = "机构信息列表",
         notNull = false,
         remark = "用来保存批量机构信息"
      )
      private List<Core1400100239Out.StageCodeInfoArray.BranchList> branchList;
      @V(
         desc = "个人剩余额度",
         notNull = false,
         length = "17",
         remark = "个人剩余额度",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal personalLeaveLimit;
      @V(
         desc = "自动结清标志",
         notNull = false,
         length = "1",
         remark = "自动结清标志",
         maxSize = 1
      )
      private String autoSettleFlag;
      @V(
         desc = "单次最小支取金额",
         notNull = false,
         length = "17",
         remark = "单次最小支取金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal sgMinAmt;
      @V(
         desc = "数组",
         notNull = false,
         remark = "数组"
      )
      private List<Core1400100239Out.StageCodeInfoArray.IntMatrixArray> intMatrixArray;
      @V(
         desc = "取息频率",
         notNull = false,
         length = "5",
         remark = "取息频率",
         maxSize = 5
      )
      private String getIntFreq;
      @V(
         desc = "支持组合购买方式",
         notNull = false,
         length = "1",
         remark = "1-单独购买2-组合购买3-单买与组合买",
         maxSize = 1
      )
      private String allowBuyWayCd;

      public String getStageCode() {
         return this.stageCode;
      }

      public String getStageCodeDesc() {
         return this.stageCodeDesc;
      }

      public String getIssueYear() {
         return this.issueYear;
      }

      public String getStageCodeDescSecond() {
         return this.stageCodeDescSecond;
      }

      public String getClientType() {
         return this.clientType;
      }

      public String getBranch() {
         return this.branch;
      }

      public BigDecimal getIssueAmt() {
         return this.issueAmt;
      }

      public String getStartDate() {
         return this.startDate;
      }

      public String getEndDate() {
         return this.endDate;
      }

      public String getSaleStartTime() {
         return this.saleStartTime;
      }

      public String getSaleEndTime() {
         return this.saleEndTime;
      }

      public BigDecimal getStageMinAmt() {
         return this.stageMinAmt;
      }

      public BigDecimal getKeepMinBal() {
         return this.keepMinBal;
      }

      public String getTerm() {
         return this.term;
      }

      public String getIntType() {
         return this.intType;
      }

      public String getPayIntType() {
         return this.payIntType;
      }

      public String getPreWithdrawFlag() {
         return this.preWithdrawFlag;
      }

      public Integer getPreWithdrawNum() {
         return this.preWithdrawNum;
      }

      public String getIsFullTransfer() {
         return this.isFullTransfer;
      }

      public String getRedemptionFlag() {
         return this.redemptionFlag;
      }

      public BigDecimal getTohonorRate() {
         return this.tohonorRate;
      }

      public String getTohonorDate() {
         return this.tohonorDate;
      }

      public BigDecimal getFloatRate() {
         return this.floatRate;
      }

      public String getEmail() {
         return this.email;
      }

      public BigDecimal getTrfOutFeeAmt() {
         return this.trfOutFeeAmt;
      }

      public String getTrfInFeeType() {
         return this.trfInFeeType;
      }

      public String getTrfOutFeeType() {
         return this.trfOutFeeType;
      }

      public BigDecimal getRealRate() {
         return this.realRate;
      }

      public String getRedemptionIntType() {
         return this.redemptionIntType;
      }

      public BigDecimal getInFee() {
         return this.inFee;
      }

      public String getIssueMethod() {
         return this.issueMethod;
      }

      public String getIsCashFromInnerAcct() {
         return this.isCashFromInnerAcct;
      }

      public BigDecimal getMinChangeBalance() {
         return this.minChangeBalance;
      }

      public String getIntStartDate() {
         return this.intStartDate;
      }

      public String getMatureDate() {
         return this.matureDate;
      }

      public String getInlandOffshore() {
         return this.inlandOffshore;
      }

      public String getAcctType() {
         return this.acctType;
      }

      public String getTermType() {
         return this.termType;
      }

      public List<Core1400100239Out.StageCodeInfoArray.ChannelArray> getChannelArray() {
         return this.channelArray;
      }

      public String getIntStartFlag() {
         return this.intStartFlag;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getCcy() {
         return this.ccy;
      }

      public BigDecimal getLeaveQuota() {
         return this.leaveQuota;
      }

      public BigDecimal getSgMaxAmt() {
         return this.sgMaxAmt;
      }

      public String getCycleFreq() {
         return this.cycleFreq;
      }

      public String getDirectionChargeIntFlag() {
         return this.directionChargeIntFlag;
      }

      public String getNewAndOldFundFlag() {
         return this.newAndOldFundFlag;
      }

      public String getCustomerBase() {
         return this.customerBase;
      }

      public BigDecimal getMaxCustomerLimit() {
         return this.maxCustomerLimit;
      }

      public String getIsTrfEnabled() {
         return this.isTrfEnabled;
      }

      public Integer getCalmDays() {
         return this.calmDays;
      }

      public List<Core1400100239Out.StageCodeInfoArray.BranchList> getBranchList() {
         return this.branchList;
      }

      public BigDecimal getPersonalLeaveLimit() {
         return this.personalLeaveLimit;
      }

      public String getAutoSettleFlag() {
         return this.autoSettleFlag;
      }

      public BigDecimal getSgMinAmt() {
         return this.sgMinAmt;
      }

      public List<Core1400100239Out.StageCodeInfoArray.IntMatrixArray> getIntMatrixArray() {
         return this.intMatrixArray;
      }

      public String getGetIntFreq() {
         return this.getIntFreq;
      }

      public String getAllowBuyWayCd() {
         return this.allowBuyWayCd;
      }

      public void setStageCode(String stageCode) {
         this.stageCode = stageCode;
      }

      public void setStageCodeDesc(String stageCodeDesc) {
         this.stageCodeDesc = stageCodeDesc;
      }

      public void setIssueYear(String issueYear) {
         this.issueYear = issueYear;
      }

      public void setStageCodeDescSecond(String stageCodeDescSecond) {
         this.stageCodeDescSecond = stageCodeDescSecond;
      }

      public void setClientType(String clientType) {
         this.clientType = clientType;
      }

      public void setBranch(String branch) {
         this.branch = branch;
      }

      public void setIssueAmt(BigDecimal issueAmt) {
         this.issueAmt = issueAmt;
      }

      public void setStartDate(String startDate) {
         this.startDate = startDate;
      }

      public void setEndDate(String endDate) {
         this.endDate = endDate;
      }

      public void setSaleStartTime(String saleStartTime) {
         this.saleStartTime = saleStartTime;
      }

      public void setSaleEndTime(String saleEndTime) {
         this.saleEndTime = saleEndTime;
      }

      public void setStageMinAmt(BigDecimal stageMinAmt) {
         this.stageMinAmt = stageMinAmt;
      }

      public void setKeepMinBal(BigDecimal keepMinBal) {
         this.keepMinBal = keepMinBal;
      }

      public void setTerm(String term) {
         this.term = term;
      }

      public void setIntType(String intType) {
         this.intType = intType;
      }

      public void setPayIntType(String payIntType) {
         this.payIntType = payIntType;
      }

      public void setPreWithdrawFlag(String preWithdrawFlag) {
         this.preWithdrawFlag = preWithdrawFlag;
      }

      public void setPreWithdrawNum(Integer preWithdrawNum) {
         this.preWithdrawNum = preWithdrawNum;
      }

      public void setIsFullTransfer(String isFullTransfer) {
         this.isFullTransfer = isFullTransfer;
      }

      public void setRedemptionFlag(String redemptionFlag) {
         this.redemptionFlag = redemptionFlag;
      }

      public void setTohonorRate(BigDecimal tohonorRate) {
         this.tohonorRate = tohonorRate;
      }

      public void setTohonorDate(String tohonorDate) {
         this.tohonorDate = tohonorDate;
      }

      public void setFloatRate(BigDecimal floatRate) {
         this.floatRate = floatRate;
      }

      public void setEmail(String email) {
         this.email = email;
      }

      public void setTrfOutFeeAmt(BigDecimal trfOutFeeAmt) {
         this.trfOutFeeAmt = trfOutFeeAmt;
      }

      public void setTrfInFeeType(String trfInFeeType) {
         this.trfInFeeType = trfInFeeType;
      }

      public void setTrfOutFeeType(String trfOutFeeType) {
         this.trfOutFeeType = trfOutFeeType;
      }

      public void setRealRate(BigDecimal realRate) {
         this.realRate = realRate;
      }

      public void setRedemptionIntType(String redemptionIntType) {
         this.redemptionIntType = redemptionIntType;
      }

      public void setInFee(BigDecimal inFee) {
         this.inFee = inFee;
      }

      public void setIssueMethod(String issueMethod) {
         this.issueMethod = issueMethod;
      }

      public void setIsCashFromInnerAcct(String isCashFromInnerAcct) {
         this.isCashFromInnerAcct = isCashFromInnerAcct;
      }

      public void setMinChangeBalance(BigDecimal minChangeBalance) {
         this.minChangeBalance = minChangeBalance;
      }

      public void setIntStartDate(String intStartDate) {
         this.intStartDate = intStartDate;
      }

      public void setMatureDate(String matureDate) {
         this.matureDate = matureDate;
      }

      public void setInlandOffshore(String inlandOffshore) {
         this.inlandOffshore = inlandOffshore;
      }

      public void setAcctType(String acctType) {
         this.acctType = acctType;
      }

      public void setTermType(String termType) {
         this.termType = termType;
      }

      public void setChannelArray(List<Core1400100239Out.StageCodeInfoArray.ChannelArray> channelArray) {
         this.channelArray = channelArray;
      }

      public void setIntStartFlag(String intStartFlag) {
         this.intStartFlag = intStartFlag;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setLeaveQuota(BigDecimal leaveQuota) {
         this.leaveQuota = leaveQuota;
      }

      public void setSgMaxAmt(BigDecimal sgMaxAmt) {
         this.sgMaxAmt = sgMaxAmt;
      }

      public void setCycleFreq(String cycleFreq) {
         this.cycleFreq = cycleFreq;
      }

      public void setDirectionChargeIntFlag(String directionChargeIntFlag) {
         this.directionChargeIntFlag = directionChargeIntFlag;
      }

      public void setNewAndOldFundFlag(String newAndOldFundFlag) {
         this.newAndOldFundFlag = newAndOldFundFlag;
      }

      public void setCustomerBase(String customerBase) {
         this.customerBase = customerBase;
      }

      public void setMaxCustomerLimit(BigDecimal maxCustomerLimit) {
         this.maxCustomerLimit = maxCustomerLimit;
      }

      public void setIsTrfEnabled(String isTrfEnabled) {
         this.isTrfEnabled = isTrfEnabled;
      }

      public void setCalmDays(Integer calmDays) {
         this.calmDays = calmDays;
      }

      public void setBranchList(List<Core1400100239Out.StageCodeInfoArray.BranchList> branchList) {
         this.branchList = branchList;
      }

      public void setPersonalLeaveLimit(BigDecimal personalLeaveLimit) {
         this.personalLeaveLimit = personalLeaveLimit;
      }

      public void setAutoSettleFlag(String autoSettleFlag) {
         this.autoSettleFlag = autoSettleFlag;
      }

      public void setSgMinAmt(BigDecimal sgMinAmt) {
         this.sgMinAmt = sgMinAmt;
      }

      public void setIntMatrixArray(List<Core1400100239Out.StageCodeInfoArray.IntMatrixArray> intMatrixArray) {
         this.intMatrixArray = intMatrixArray;
      }

      public void setGetIntFreq(String getIntFreq) {
         this.getIntFreq = getIntFreq;
      }

      public void setAllowBuyWayCd(String allowBuyWayCd) {
         this.allowBuyWayCd = allowBuyWayCd;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100239Out.StageCodeInfoArray)) {
            return false;
         } else {
            Core1400100239Out.StageCodeInfoArray other = (Core1400100239Out.StageCodeInfoArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$stageCode = this.getStageCode();
               Object other$stageCode = other.getStageCode();
               if (this$stageCode == null) {
                  if (other$stageCode != null) {
                     return false;
                  }
               } else if (!this$stageCode.equals(other$stageCode)) {
                  return false;
               }

               Object this$stageCodeDesc = this.getStageCodeDesc();
               Object other$stageCodeDesc = other.getStageCodeDesc();
               if (this$stageCodeDesc == null) {
                  if (other$stageCodeDesc != null) {
                     return false;
                  }
               } else if (!this$stageCodeDesc.equals(other$stageCodeDesc)) {
                  return false;
               }

               Object this$issueYear = this.getIssueYear();
               Object other$issueYear = other.getIssueYear();
               if (this$issueYear == null) {
                  if (other$issueYear != null) {
                     return false;
                  }
               } else if (!this$issueYear.equals(other$issueYear)) {
                  return false;
               }

               label686: {
                  Object this$stageCodeDescSecond = this.getStageCodeDescSecond();
                  Object other$stageCodeDescSecond = other.getStageCodeDescSecond();
                  if (this$stageCodeDescSecond == null) {
                     if (other$stageCodeDescSecond == null) {
                        break label686;
                     }
                  } else if (this$stageCodeDescSecond.equals(other$stageCodeDescSecond)) {
                     break label686;
                  }

                  return false;
               }

               label679: {
                  Object this$clientType = this.getClientType();
                  Object other$clientType = other.getClientType();
                  if (this$clientType == null) {
                     if (other$clientType == null) {
                        break label679;
                     }
                  } else if (this$clientType.equals(other$clientType)) {
                     break label679;
                  }

                  return false;
               }

               Object this$branch = this.getBranch();
               Object other$branch = other.getBranch();
               if (this$branch == null) {
                  if (other$branch != null) {
                     return false;
                  }
               } else if (!this$branch.equals(other$branch)) {
                  return false;
               }

               label665: {
                  Object this$issueAmt = this.getIssueAmt();
                  Object other$issueAmt = other.getIssueAmt();
                  if (this$issueAmt == null) {
                     if (other$issueAmt == null) {
                        break label665;
                     }
                  } else if (this$issueAmt.equals(other$issueAmt)) {
                     break label665;
                  }

                  return false;
               }

               label658: {
                  Object this$startDate = this.getStartDate();
                  Object other$startDate = other.getStartDate();
                  if (this$startDate == null) {
                     if (other$startDate == null) {
                        break label658;
                     }
                  } else if (this$startDate.equals(other$startDate)) {
                     break label658;
                  }

                  return false;
               }

               Object this$endDate = this.getEndDate();
               Object other$endDate = other.getEndDate();
               if (this$endDate == null) {
                  if (other$endDate != null) {
                     return false;
                  }
               } else if (!this$endDate.equals(other$endDate)) {
                  return false;
               }

               Object this$saleStartTime = this.getSaleStartTime();
               Object other$saleStartTime = other.getSaleStartTime();
               if (this$saleStartTime == null) {
                  if (other$saleStartTime != null) {
                     return false;
                  }
               } else if (!this$saleStartTime.equals(other$saleStartTime)) {
                  return false;
               }

               label637: {
                  Object this$saleEndTime = this.getSaleEndTime();
                  Object other$saleEndTime = other.getSaleEndTime();
                  if (this$saleEndTime == null) {
                     if (other$saleEndTime == null) {
                        break label637;
                     }
                  } else if (this$saleEndTime.equals(other$saleEndTime)) {
                     break label637;
                  }

                  return false;
               }

               label630: {
                  Object this$stageMinAmt = this.getStageMinAmt();
                  Object other$stageMinAmt = other.getStageMinAmt();
                  if (this$stageMinAmt == null) {
                     if (other$stageMinAmt == null) {
                        break label630;
                     }
                  } else if (this$stageMinAmt.equals(other$stageMinAmt)) {
                     break label630;
                  }

                  return false;
               }

               Object this$keepMinBal = this.getKeepMinBal();
               Object other$keepMinBal = other.getKeepMinBal();
               if (this$keepMinBal == null) {
                  if (other$keepMinBal != null) {
                     return false;
                  }
               } else if (!this$keepMinBal.equals(other$keepMinBal)) {
                  return false;
               }

               label616: {
                  Object this$term = this.getTerm();
                  Object other$term = other.getTerm();
                  if (this$term == null) {
                     if (other$term == null) {
                        break label616;
                     }
                  } else if (this$term.equals(other$term)) {
                     break label616;
                  }

                  return false;
               }

               Object this$intType = this.getIntType();
               Object other$intType = other.getIntType();
               if (this$intType == null) {
                  if (other$intType != null) {
                     return false;
                  }
               } else if (!this$intType.equals(other$intType)) {
                  return false;
               }

               label602: {
                  Object this$payIntType = this.getPayIntType();
                  Object other$payIntType = other.getPayIntType();
                  if (this$payIntType == null) {
                     if (other$payIntType == null) {
                        break label602;
                     }
                  } else if (this$payIntType.equals(other$payIntType)) {
                     break label602;
                  }

                  return false;
               }

               Object this$preWithdrawFlag = this.getPreWithdrawFlag();
               Object other$preWithdrawFlag = other.getPreWithdrawFlag();
               if (this$preWithdrawFlag == null) {
                  if (other$preWithdrawFlag != null) {
                     return false;
                  }
               } else if (!this$preWithdrawFlag.equals(other$preWithdrawFlag)) {
                  return false;
               }

               Object this$preWithdrawNum = this.getPreWithdrawNum();
               Object other$preWithdrawNum = other.getPreWithdrawNum();
               if (this$preWithdrawNum == null) {
                  if (other$preWithdrawNum != null) {
                     return false;
                  }
               } else if (!this$preWithdrawNum.equals(other$preWithdrawNum)) {
                  return false;
               }

               Object this$isFullTransfer = this.getIsFullTransfer();
               Object other$isFullTransfer = other.getIsFullTransfer();
               if (this$isFullTransfer == null) {
                  if (other$isFullTransfer != null) {
                     return false;
                  }
               } else if (!this$isFullTransfer.equals(other$isFullTransfer)) {
                  return false;
               }

               label574: {
                  Object this$redemptionFlag = this.getRedemptionFlag();
                  Object other$redemptionFlag = other.getRedemptionFlag();
                  if (this$redemptionFlag == null) {
                     if (other$redemptionFlag == null) {
                        break label574;
                     }
                  } else if (this$redemptionFlag.equals(other$redemptionFlag)) {
                     break label574;
                  }

                  return false;
               }

               label567: {
                  Object this$tohonorRate = this.getTohonorRate();
                  Object other$tohonorRate = other.getTohonorRate();
                  if (this$tohonorRate == null) {
                     if (other$tohonorRate == null) {
                        break label567;
                     }
                  } else if (this$tohonorRate.equals(other$tohonorRate)) {
                     break label567;
                  }

                  return false;
               }

               Object this$tohonorDate = this.getTohonorDate();
               Object other$tohonorDate = other.getTohonorDate();
               if (this$tohonorDate == null) {
                  if (other$tohonorDate != null) {
                     return false;
                  }
               } else if (!this$tohonorDate.equals(other$tohonorDate)) {
                  return false;
               }

               label553: {
                  Object this$floatRate = this.getFloatRate();
                  Object other$floatRate = other.getFloatRate();
                  if (this$floatRate == null) {
                     if (other$floatRate == null) {
                        break label553;
                     }
                  } else if (this$floatRate.equals(other$floatRate)) {
                     break label553;
                  }

                  return false;
               }

               label546: {
                  Object this$email = this.getEmail();
                  Object other$email = other.getEmail();
                  if (this$email == null) {
                     if (other$email == null) {
                        break label546;
                     }
                  } else if (this$email.equals(other$email)) {
                     break label546;
                  }

                  return false;
               }

               Object this$trfOutFeeAmt = this.getTrfOutFeeAmt();
               Object other$trfOutFeeAmt = other.getTrfOutFeeAmt();
               if (this$trfOutFeeAmt == null) {
                  if (other$trfOutFeeAmt != null) {
                     return false;
                  }
               } else if (!this$trfOutFeeAmt.equals(other$trfOutFeeAmt)) {
                  return false;
               }

               Object this$trfInFeeType = this.getTrfInFeeType();
               Object other$trfInFeeType = other.getTrfInFeeType();
               if (this$trfInFeeType == null) {
                  if (other$trfInFeeType != null) {
                     return false;
                  }
               } else if (!this$trfInFeeType.equals(other$trfInFeeType)) {
                  return false;
               }

               label525: {
                  Object this$trfOutFeeType = this.getTrfOutFeeType();
                  Object other$trfOutFeeType = other.getTrfOutFeeType();
                  if (this$trfOutFeeType == null) {
                     if (other$trfOutFeeType == null) {
                        break label525;
                     }
                  } else if (this$trfOutFeeType.equals(other$trfOutFeeType)) {
                     break label525;
                  }

                  return false;
               }

               label518: {
                  Object this$realRate = this.getRealRate();
                  Object other$realRate = other.getRealRate();
                  if (this$realRate == null) {
                     if (other$realRate == null) {
                        break label518;
                     }
                  } else if (this$realRate.equals(other$realRate)) {
                     break label518;
                  }

                  return false;
               }

               Object this$redemptionIntType = this.getRedemptionIntType();
               Object other$redemptionIntType = other.getRedemptionIntType();
               if (this$redemptionIntType == null) {
                  if (other$redemptionIntType != null) {
                     return false;
                  }
               } else if (!this$redemptionIntType.equals(other$redemptionIntType)) {
                  return false;
               }

               label504: {
                  Object this$inFee = this.getInFee();
                  Object other$inFee = other.getInFee();
                  if (this$inFee == null) {
                     if (other$inFee == null) {
                        break label504;
                     }
                  } else if (this$inFee.equals(other$inFee)) {
                     break label504;
                  }

                  return false;
               }

               Object this$issueMethod = this.getIssueMethod();
               Object other$issueMethod = other.getIssueMethod();
               if (this$issueMethod == null) {
                  if (other$issueMethod != null) {
                     return false;
                  }
               } else if (!this$issueMethod.equals(other$issueMethod)) {
                  return false;
               }

               label490: {
                  Object this$isCashFromInnerAcct = this.getIsCashFromInnerAcct();
                  Object other$isCashFromInnerAcct = other.getIsCashFromInnerAcct();
                  if (this$isCashFromInnerAcct == null) {
                     if (other$isCashFromInnerAcct == null) {
                        break label490;
                     }
                  } else if (this$isCashFromInnerAcct.equals(other$isCashFromInnerAcct)) {
                     break label490;
                  }

                  return false;
               }

               Object this$minChangeBalance = this.getMinChangeBalance();
               Object other$minChangeBalance = other.getMinChangeBalance();
               if (this$minChangeBalance == null) {
                  if (other$minChangeBalance != null) {
                     return false;
                  }
               } else if (!this$minChangeBalance.equals(other$minChangeBalance)) {
                  return false;
               }

               Object this$intStartDate = this.getIntStartDate();
               Object other$intStartDate = other.getIntStartDate();
               if (this$intStartDate == null) {
                  if (other$intStartDate != null) {
                     return false;
                  }
               } else if (!this$intStartDate.equals(other$intStartDate)) {
                  return false;
               }

               Object this$matureDate = this.getMatureDate();
               Object other$matureDate = other.getMatureDate();
               if (this$matureDate == null) {
                  if (other$matureDate != null) {
                     return false;
                  }
               } else if (!this$matureDate.equals(other$matureDate)) {
                  return false;
               }

               label462: {
                  Object this$inlandOffshore = this.getInlandOffshore();
                  Object other$inlandOffshore = other.getInlandOffshore();
                  if (this$inlandOffshore == null) {
                     if (other$inlandOffshore == null) {
                        break label462;
                     }
                  } else if (this$inlandOffshore.equals(other$inlandOffshore)) {
                     break label462;
                  }

                  return false;
               }

               label455: {
                  Object this$acctType = this.getAcctType();
                  Object other$acctType = other.getAcctType();
                  if (this$acctType == null) {
                     if (other$acctType == null) {
                        break label455;
                     }
                  } else if (this$acctType.equals(other$acctType)) {
                     break label455;
                  }

                  return false;
               }

               Object this$termType = this.getTermType();
               Object other$termType = other.getTermType();
               if (this$termType == null) {
                  if (other$termType != null) {
                     return false;
                  }
               } else if (!this$termType.equals(other$termType)) {
                  return false;
               }

               label441: {
                  Object this$channelArray = this.getChannelArray();
                  Object other$channelArray = other.getChannelArray();
                  if (this$channelArray == null) {
                     if (other$channelArray == null) {
                        break label441;
                     }
                  } else if (this$channelArray.equals(other$channelArray)) {
                     break label441;
                  }

                  return false;
               }

               label434: {
                  Object this$intStartFlag = this.getIntStartFlag();
                  Object other$intStartFlag = other.getIntStartFlag();
                  if (this$intStartFlag == null) {
                     if (other$intStartFlag == null) {
                        break label434;
                     }
                  } else if (this$intStartFlag.equals(other$intStartFlag)) {
                     break label434;
                  }

                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               Object this$ccy = this.getCcy();
               Object other$ccy = other.getCcy();
               if (this$ccy == null) {
                  if (other$ccy != null) {
                     return false;
                  }
               } else if (!this$ccy.equals(other$ccy)) {
                  return false;
               }

               label413: {
                  Object this$leaveQuota = this.getLeaveQuota();
                  Object other$leaveQuota = other.getLeaveQuota();
                  if (this$leaveQuota == null) {
                     if (other$leaveQuota == null) {
                        break label413;
                     }
                  } else if (this$leaveQuota.equals(other$leaveQuota)) {
                     break label413;
                  }

                  return false;
               }

               label406: {
                  Object this$sgMaxAmt = this.getSgMaxAmt();
                  Object other$sgMaxAmt = other.getSgMaxAmt();
                  if (this$sgMaxAmt == null) {
                     if (other$sgMaxAmt == null) {
                        break label406;
                     }
                  } else if (this$sgMaxAmt.equals(other$sgMaxAmt)) {
                     break label406;
                  }

                  return false;
               }

               Object this$cycleFreq = this.getCycleFreq();
               Object other$cycleFreq = other.getCycleFreq();
               if (this$cycleFreq == null) {
                  if (other$cycleFreq != null) {
                     return false;
                  }
               } else if (!this$cycleFreq.equals(other$cycleFreq)) {
                  return false;
               }

               label392: {
                  Object this$directionChargeIntFlag = this.getDirectionChargeIntFlag();
                  Object other$directionChargeIntFlag = other.getDirectionChargeIntFlag();
                  if (this$directionChargeIntFlag == null) {
                     if (other$directionChargeIntFlag == null) {
                        break label392;
                     }
                  } else if (this$directionChargeIntFlag.equals(other$directionChargeIntFlag)) {
                     break label392;
                  }

                  return false;
               }

               Object this$newAndOldFundFlag = this.getNewAndOldFundFlag();
               Object other$newAndOldFundFlag = other.getNewAndOldFundFlag();
               if (this$newAndOldFundFlag == null) {
                  if (other$newAndOldFundFlag != null) {
                     return false;
                  }
               } else if (!this$newAndOldFundFlag.equals(other$newAndOldFundFlag)) {
                  return false;
               }

               label378: {
                  Object this$customerBase = this.getCustomerBase();
                  Object other$customerBase = other.getCustomerBase();
                  if (this$customerBase == null) {
                     if (other$customerBase == null) {
                        break label378;
                     }
                  } else if (this$customerBase.equals(other$customerBase)) {
                     break label378;
                  }

                  return false;
               }

               Object this$maxCustomerLimit = this.getMaxCustomerLimit();
               Object other$maxCustomerLimit = other.getMaxCustomerLimit();
               if (this$maxCustomerLimit == null) {
                  if (other$maxCustomerLimit != null) {
                     return false;
                  }
               } else if (!this$maxCustomerLimit.equals(other$maxCustomerLimit)) {
                  return false;
               }

               Object this$isTrfEnabled = this.getIsTrfEnabled();
               Object other$isTrfEnabled = other.getIsTrfEnabled();
               if (this$isTrfEnabled == null) {
                  if (other$isTrfEnabled != null) {
                     return false;
                  }
               } else if (!this$isTrfEnabled.equals(other$isTrfEnabled)) {
                  return false;
               }

               Object this$calmDays = this.getCalmDays();
               Object other$calmDays = other.getCalmDays();
               if (this$calmDays == null) {
                  if (other$calmDays != null) {
                     return false;
                  }
               } else if (!this$calmDays.equals(other$calmDays)) {
                  return false;
               }

               label350: {
                  Object this$branchList = this.getBranchList();
                  Object other$branchList = other.getBranchList();
                  if (this$branchList == null) {
                     if (other$branchList == null) {
                        break label350;
                     }
                  } else if (this$branchList.equals(other$branchList)) {
                     break label350;
                  }

                  return false;
               }

               label343: {
                  Object this$personalLeaveLimit = this.getPersonalLeaveLimit();
                  Object other$personalLeaveLimit = other.getPersonalLeaveLimit();
                  if (this$personalLeaveLimit == null) {
                     if (other$personalLeaveLimit == null) {
                        break label343;
                     }
                  } else if (this$personalLeaveLimit.equals(other$personalLeaveLimit)) {
                     break label343;
                  }

                  return false;
               }

               Object this$autoSettleFlag = this.getAutoSettleFlag();
               Object other$autoSettleFlag = other.getAutoSettleFlag();
               if (this$autoSettleFlag == null) {
                  if (other$autoSettleFlag != null) {
                     return false;
                  }
               } else if (!this$autoSettleFlag.equals(other$autoSettleFlag)) {
                  return false;
               }

               label329: {
                  Object this$sgMinAmt = this.getSgMinAmt();
                  Object other$sgMinAmt = other.getSgMinAmt();
                  if (this$sgMinAmt == null) {
                     if (other$sgMinAmt == null) {
                        break label329;
                     }
                  } else if (this$sgMinAmt.equals(other$sgMinAmt)) {
                     break label329;
                  }

                  return false;
               }

               label322: {
                  Object this$intMatrixArray = this.getIntMatrixArray();
                  Object other$intMatrixArray = other.getIntMatrixArray();
                  if (this$intMatrixArray == null) {
                     if (other$intMatrixArray == null) {
                        break label322;
                     }
                  } else if (this$intMatrixArray.equals(other$intMatrixArray)) {
                     break label322;
                  }

                  return false;
               }

               Object this$getIntFreq = this.getGetIntFreq();
               Object other$getIntFreq = other.getGetIntFreq();
               if (this$getIntFreq == null) {
                  if (other$getIntFreq != null) {
                     return false;
                  }
               } else if (!this$getIntFreq.equals(other$getIntFreq)) {
                  return false;
               }

               Object this$allowBuyWayCd = this.getAllowBuyWayCd();
               Object other$allowBuyWayCd = other.getAllowBuyWayCd();
               if (this$allowBuyWayCd == null) {
                  if (other$allowBuyWayCd != null) {
                     return false;
                  }
               } else if (!this$allowBuyWayCd.equals(other$allowBuyWayCd)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100239Out.StageCodeInfoArray;
      }
      public String toString() {
         return "Core1400100239Out.StageCodeInfoArray(stageCode=" + this.getStageCode() + ", stageCodeDesc=" + this.getStageCodeDesc() + ", issueYear=" + this.getIssueYear() + ", stageCodeDescSecond=" + this.getStageCodeDescSecond() + ", clientType=" + this.getClientType() + ", branch=" + this.getBranch() + ", issueAmt=" + this.getIssueAmt() + ", startDate=" + this.getStartDate() + ", endDate=" + this.getEndDate() + ", saleStartTime=" + this.getSaleStartTime() + ", saleEndTime=" + this.getSaleEndTime() + ", stageMinAmt=" + this.getStageMinAmt() + ", keepMinBal=" + this.getKeepMinBal() + ", term=" + this.getTerm() + ", intType=" + this.getIntType() + ", payIntType=" + this.getPayIntType() + ", preWithdrawFlag=" + this.getPreWithdrawFlag() + ", preWithdrawNum=" + this.getPreWithdrawNum() + ", isFullTransfer=" + this.getIsFullTransfer() + ", redemptionFlag=" + this.getRedemptionFlag() + ", tohonorRate=" + this.getTohonorRate() + ", tohonorDate=" + this.getTohonorDate() + ", floatRate=" + this.getFloatRate() + ", email=" + this.getEmail() + ", trfOutFeeAmt=" + this.getTrfOutFeeAmt() + ", trfInFeeType=" + this.getTrfInFeeType() + ", trfOutFeeType=" + this.getTrfOutFeeType() + ", realRate=" + this.getRealRate() + ", redemptionIntType=" + this.getRedemptionIntType() + ", inFee=" + this.getInFee() + ", issueMethod=" + this.getIssueMethod() + ", isCashFromInnerAcct=" + this.getIsCashFromInnerAcct() + ", minChangeBalance=" + this.getMinChangeBalance() + ", intStartDate=" + this.getIntStartDate() + ", matureDate=" + this.getMatureDate() + ", inlandOffshore=" + this.getInlandOffshore() + ", acctType=" + this.getAcctType() + ", termType=" + this.getTermType() + ", channelArray=" + this.getChannelArray() + ", intStartFlag=" + this.getIntStartFlag() + ", prodType=" + this.getProdType() + ", ccy=" + this.getCcy() + ", leaveQuota=" + this.getLeaveQuota() + ", sgMaxAmt=" + this.getSgMaxAmt() + ", cycleFreq=" + this.getCycleFreq() + ", directionChargeIntFlag=" + this.getDirectionChargeIntFlag() + ", newAndOldFundFlag=" + this.getNewAndOldFundFlag() + ", customerBase=" + this.getCustomerBase() + ", maxCustomerLimit=" + this.getMaxCustomerLimit() + ", isTrfEnabled=" + this.getIsTrfEnabled() + ", calmDays=" + this.getCalmDays() + ", branchList=" + this.getBranchList() + ", personalLeaveLimit=" + this.getPersonalLeaveLimit() + ", autoSettleFlag=" + this.getAutoSettleFlag() + ", sgMinAmt=" + this.getSgMinAmt() + ", intMatrixArray=" + this.getIntMatrixArray() + ", getIntFreq=" + this.getGetIntFreq() + ", allowBuyWayCd=" + this.getAllowBuyWayCd() + ")";
      }

      public static class IntMatrixArray {
         @V(
            desc = "阶梯序号",
            notNull = false,
            length = "50",
            remark = "阶梯序号",
            maxSize = 50
         )
         private String matrixNo;
         @V(
            desc = "利率类型",
            notNull = false,
            length = "5",
            remark = "利率类型",
            maxSize = 5
         )
         private String intType;
         @V(
            desc = "年基准天数",
            notNull = false,
            length = "3",
            remark = "年基准天数",
            maxSize = 3
         )
         private String yearBasis;
         @V(
            desc = "频率id",
            notNull = false,
            length = "5",
            remark = "频率id",
            maxSize = 5
         )
         private String periodFreq;
         @V(
            desc = "每期天数",
            notNull = false,
            length = "5",
            remark = "DAY_MTH为D时，取ADD_NO值；为M时，值为7* ADD_NO；为Y时，值为360* ADD_NO"
         )
         private Integer dayNum;
         @V(
            desc = "执行利率",
            notNull = false,
            length = "15",
            remark = "执行利率",
            decimalLength = 8,
            precision = 8
         )
         private BigDecimal realRate;

         public String getMatrixNo() {
            return this.matrixNo;
         }

         public String getIntType() {
            return this.intType;
         }

         public String getYearBasis() {
            return this.yearBasis;
         }

         public String getPeriodFreq() {
            return this.periodFreq;
         }

         public Integer getDayNum() {
            return this.dayNum;
         }

         public BigDecimal getRealRate() {
            return this.realRate;
         }

         public void setMatrixNo(String matrixNo) {
            this.matrixNo = matrixNo;
         }

         public void setIntType(String intType) {
            this.intType = intType;
         }

         public void setYearBasis(String yearBasis) {
            this.yearBasis = yearBasis;
         }

         public void setPeriodFreq(String periodFreq) {
            this.periodFreq = periodFreq;
         }

         public void setDayNum(Integer dayNum) {
            this.dayNum = dayNum;
         }

         public void setRealRate(BigDecimal realRate) {
            this.realRate = realRate;
         }

         public boolean equals(Object o) {
            if (o == this) {
               return true;
            } else if (!(o instanceof Core1400100239Out.StageCodeInfoArray.IntMatrixArray)) {
               return false;
            } else {
               Core1400100239Out.StageCodeInfoArray.IntMatrixArray other = (Core1400100239Out.StageCodeInfoArray.IntMatrixArray)o;
               if (!other.canEqual(this)) {
                  return false;
               } else {
                  Object this$matrixNo = this.getMatrixNo();
                  Object other$matrixNo = other.getMatrixNo();
                  if (this$matrixNo == null) {
                     if (other$matrixNo != null) {
                        return false;
                     }
                  } else if (!this$matrixNo.equals(other$matrixNo)) {
                     return false;
                  }

                  Object this$intType = this.getIntType();
                  Object other$intType = other.getIntType();
                  if (this$intType == null) {
                     if (other$intType != null) {
                        return false;
                     }
                  } else if (!this$intType.equals(other$intType)) {
                     return false;
                  }

                  Object this$yearBasis = this.getYearBasis();
                  Object other$yearBasis = other.getYearBasis();
                  if (this$yearBasis == null) {
                     if (other$yearBasis != null) {
                        return false;
                     }
                  } else if (!this$yearBasis.equals(other$yearBasis)) {
                     return false;
                  }

                  label62: {
                     Object this$periodFreq = this.getPeriodFreq();
                     Object other$periodFreq = other.getPeriodFreq();
                     if (this$periodFreq == null) {
                        if (other$periodFreq == null) {
                           break label62;
                        }
                     } else if (this$periodFreq.equals(other$periodFreq)) {
                        break label62;
                     }

                     return false;
                  }

                  label55: {
                     Object this$dayNum = this.getDayNum();
                     Object other$dayNum = other.getDayNum();
                     if (this$dayNum == null) {
                        if (other$dayNum == null) {
                           break label55;
                        }
                     } else if (this$dayNum.equals(other$dayNum)) {
                        break label55;
                     }

                     return false;
                  }

                  Object this$realRate = this.getRealRate();
                  Object other$realRate = other.getRealRate();
                  if (this$realRate == null) {
                     if (other$realRate != null) {
                        return false;
                     }
                  } else if (!this$realRate.equals(other$realRate)) {
                     return false;
                  }

                  return true;
               }
            }
         }

         protected boolean canEqual(Object other) {
            return other instanceof Core1400100239Out.StageCodeInfoArray.IntMatrixArray;
         }
         public String toString() {
            return "Core1400100239Out.StageCodeInfoArray.IntMatrixArray(matrixNo=" + this.getMatrixNo() + ", intType=" + this.getIntType() + ", yearBasis=" + this.getYearBasis() + ", periodFreq=" + this.getPeriodFreq() + ", dayNum=" + this.getDayNum() + ", realRate=" + this.getRealRate() + ")";
         }
      }

      public static class BranchList {
         @V(
            desc = "所属机构号",
            notNull = false,
            length = "50",
            remark = "所属机构号",
            maxSize = 50
         )
         private String branch;

         public String getBranch() {
            return this.branch;
         }

         public void setBranch(String branch) {
            this.branch = branch;
         }

         public boolean equals(Object o) {
            if (o == this) {
               return true;
            } else if (!(o instanceof Core1400100239Out.StageCodeInfoArray.BranchList)) {
               return false;
            } else {
               Core1400100239Out.StageCodeInfoArray.BranchList other = (Core1400100239Out.StageCodeInfoArray.BranchList)o;
               if (!other.canEqual(this)) {
                  return false;
               } else {
                  Object this$branch = this.getBranch();
                  Object other$branch = other.getBranch();
                  if (this$branch == null) {
                     if (other$branch != null) {
                        return false;
                     }
                  } else if (!this$branch.equals(other$branch)) {
                     return false;
                  }

                  return true;
               }
            }
         }

         protected boolean canEqual(Object other) {
            return other instanceof Core1400100239Out.StageCodeInfoArray.BranchList;
         }
         public String toString() {
            return "Core1400100239Out.StageCodeInfoArray.BranchList(branch=" + this.getBranch() + ")";
         }
      }

      public static class ChannelArray {
         @V(
            desc = "渠道",
            notNull = false,
            length = "10",
            remark = "渠道细类",
            maxSize = 10
         )
         private String channel;

         public String getChannel() {
            return this.channel;
         }

         public void setChannel(String channel) {
            this.channel = channel;
         }

         public boolean equals(Object o) {
            if (o == this) {
               return true;
            } else if (!(o instanceof Core1400100239Out.StageCodeInfoArray.ChannelArray)) {
               return false;
            } else {
               Core1400100239Out.StageCodeInfoArray.ChannelArray other = (Core1400100239Out.StageCodeInfoArray.ChannelArray)o;
               if (!other.canEqual(this)) {
                  return false;
               } else {
                  Object this$channel = this.getChannel();
                  Object other$channel = other.getChannel();
                  if (this$channel == null) {
                     if (other$channel != null) {
                        return false;
                     }
                  } else if (!this$channel.equals(other$channel)) {
                     return false;
                  }

                  return true;
               }
            }
         }

         protected boolean canEqual(Object other) {
            return other instanceof Core1400100239Out.StageCodeInfoArray.ChannelArray;
         }
         public String toString() {
            return "Core1400100239Out.StageCodeInfoArray.ChannelArray(channel=" + this.getChannel() + ")";
         }
      }
   }
}
