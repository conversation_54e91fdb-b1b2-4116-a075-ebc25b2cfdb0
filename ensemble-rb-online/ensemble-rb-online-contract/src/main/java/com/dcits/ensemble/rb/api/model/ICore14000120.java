package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000120In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000120Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000120 {
   String URL = "/rb/inq/voucher/deposit/cert";


   @ApiRemark("标准优化,teller:3510-存款证明查询")
   @ApiDesc("通过账号，存款证明编号，柜员，证件类型查询存款证明信息")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0120"
   )
   @BusinessCategory("1400-查询")
   @FunctionCategory("RB06-凭证处理")
   @ConsumeSys("TLE/PR/EOS/CIS")
   @ApiUseStatus("PRODUCT-产品")
   Core14000120Out runService(Core14000120In var1);
}
