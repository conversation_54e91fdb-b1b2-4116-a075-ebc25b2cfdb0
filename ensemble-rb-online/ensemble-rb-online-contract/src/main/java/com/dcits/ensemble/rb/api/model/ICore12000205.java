package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000205In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000205Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12000205 {
   String URL = "/rb/nfin/agreement/group";


   @ApiRemark("存款组签约解约")
   @ApiDesc("存款组签约解约")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0205"
   )
   @BusinessCategory("1200-非金融交易")
   @FunctionCategory("RB05-协议管理")
   @ConsumeSys("CBS")
   @ApiUseStatus("PRODUCT-产品")
   Core12000205Out runService(Core12000205In var1);
}
