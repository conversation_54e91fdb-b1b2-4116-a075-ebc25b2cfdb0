package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000947In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000947Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12000947 {
   String URL = "/rb/nfin/prod/change";

   
   @ApiDesc("存款产品变更，根据产品变更映射表参数对产品进行变更")
   @EnsembleElements(
      serviceCode = "Core",
      messageType = "1200",
      messageCode = "0947"
   )
   @FunctionCategory("RB01-公共服务")
   @ConsumeSys("TLE")
   Core12000947Out runService(Core12000947In var1);
}
