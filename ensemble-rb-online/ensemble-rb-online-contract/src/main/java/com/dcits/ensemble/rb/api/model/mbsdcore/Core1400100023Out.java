package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.util.List;

@MessageOut
public class Core1400100023Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100023Out.AcctVerifyArray> acctVerifyArray;

   public List<Core1400100023Out.AcctVerifyArray> getAcctVerifyArray() {
      return this.acctVerifyArray;
   }

   public void setAcctVerifyArray(List<Core1400100023Out.AcctVerifyArray> acctVerifyArray) {
      this.acctVerifyArray = acctVerifyArray;
   }

   public String toString() {
      return "Core1400100023Out(acctVerifyArray=" + this.getAcctVerifyArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100023Out)) {
         return false;
      } else {
         Core1400100023Out other = (Core1400100023Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$acctVerifyArray = this.getAcctVerifyArray();
            Object other$acctVerifyArray = other.getAcctVerifyArray();
            if (this$acctVerifyArray == null) {
               if (other$acctVerifyArray != null) {
                  return false;
               }
            } else if (!this$acctVerifyArray.equals(other$acctVerifyArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100023Out;
   }
   public static class AcctVerifyArray {
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "账户核实状态",
         notNull = false,
         length = "1",
         remark = "核实状态",
         maxSize = 1
      )
      private String checkStatus;
      @V(
         desc = "无法核实原因",
         notNull = false,
         length = "200",
         remark = "无法核实原因",
         maxSize = 200
      )
      private String unverificationReason;
      @V(
         desc = "处置方式",
         notNull = false,
         length = "2",
         remark = "处置方式",
         maxSize = 2
      )
      private String treatment;
      @V(
         desc = "备注",
         notNull = false,
         length = "500",
         remark = "备注",
         maxSize = 500
      )
      private String narrative1;
      @V(
         desc = "操作柜员/接收柜员",
         notNull = false,
         length = "50",
         remark = "操作柜员/接收柜员",
         maxSize = 50
      )
      private String tellername;
      @V(
         desc = "上一操作时间",
         notNull = false,
         length = "26",
         remark = "上一操作时间",
         maxSize = 26
      )
      private String lastTimeStamp;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getCheckStatus() {
         return this.checkStatus;
      }

      public String getUnverificationReason() {
         return this.unverificationReason;
      }

      public String getTreatment() {
         return this.treatment;
      }

      public String getNarrative1() {
         return this.narrative1;
      }

      public String getTellername() {
         return this.tellername;
      }

      public String getLastTimeStamp() {
         return this.lastTimeStamp;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setCheckStatus(String checkStatus) {
         this.checkStatus = checkStatus;
      }

      public void setUnverificationReason(String unverificationReason) {
         this.unverificationReason = unverificationReason;
      }

      public void setTreatment(String treatment) {
         this.treatment = treatment;
      }

      public void setNarrative1(String narrative1) {
         this.narrative1 = narrative1;
      }

      public void setTellername(String tellername) {
         this.tellername = tellername;
      }

      public void setLastTimeStamp(String lastTimeStamp) {
         this.lastTimeStamp = lastTimeStamp;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100023Out.AcctVerifyArray)) {
            return false;
         } else {
            Core1400100023Out.AcctVerifyArray other = (Core1400100023Out.AcctVerifyArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo != null) {
                     return false;
                  }
               } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo != null) {
                     return false;
                  }
               } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                  return false;
               }

               label110: {
                  Object this$acctCcy = this.getAcctCcy();
                  Object other$acctCcy = other.getAcctCcy();
                  if (this$acctCcy == null) {
                     if (other$acctCcy == null) {
                        break label110;
                     }
                  } else if (this$acctCcy.equals(other$acctCcy)) {
                     break label110;
                  }

                  return false;
               }

               label103: {
                  Object this$checkStatus = this.getCheckStatus();
                  Object other$checkStatus = other.getCheckStatus();
                  if (this$checkStatus == null) {
                     if (other$checkStatus == null) {
                        break label103;
                     }
                  } else if (this$checkStatus.equals(other$checkStatus)) {
                     break label103;
                  }

                  return false;
               }

               Object this$unverificationReason = this.getUnverificationReason();
               Object other$unverificationReason = other.getUnverificationReason();
               if (this$unverificationReason == null) {
                  if (other$unverificationReason != null) {
                     return false;
                  }
               } else if (!this$unverificationReason.equals(other$unverificationReason)) {
                  return false;
               }

               label89: {
                  Object this$treatment = this.getTreatment();
                  Object other$treatment = other.getTreatment();
                  if (this$treatment == null) {
                     if (other$treatment == null) {
                        break label89;
                     }
                  } else if (this$treatment.equals(other$treatment)) {
                     break label89;
                  }

                  return false;
               }

               label82: {
                  Object this$narrative1 = this.getNarrative1();
                  Object other$narrative1 = other.getNarrative1();
                  if (this$narrative1 == null) {
                     if (other$narrative1 == null) {
                        break label82;
                     }
                  } else if (this$narrative1.equals(other$narrative1)) {
                     break label82;
                  }

                  return false;
               }

               Object this$tellername = this.getTellername();
               Object other$tellername = other.getTellername();
               if (this$tellername == null) {
                  if (other$tellername != null) {
                     return false;
                  }
               } else if (!this$tellername.equals(other$tellername)) {
                  return false;
               }

               Object this$lastTimeStamp = this.getLastTimeStamp();
               Object other$lastTimeStamp = other.getLastTimeStamp();
               if (this$lastTimeStamp == null) {
                  if (other$lastTimeStamp != null) {
                     return false;
                  }
               } else if (!this$lastTimeStamp.equals(other$lastTimeStamp)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100023Out.AcctVerifyArray;
      }
      public String toString() {
         return "Core1400100023Out.AcctVerifyArray(baseAcctNo=" + this.getBaseAcctNo() + ", prodType=" + this.getProdType() + ", acctSeqNo=" + this.getAcctSeqNo() + ", acctCcy=" + this.getAcctCcy() + ", checkStatus=" + this.getCheckStatus() + ", unverificationReason=" + this.getUnverificationReason() + ", treatment=" + this.getTreatment() + ", narrative1=" + this.getNarrative1() + ", tellername=" + this.getTellername() + ", lastTimeStamp=" + this.getLastTimeStamp() + ")";
      }
   }
}
