package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;

@MessageOut
public class MbsdCore12009987Out extends EnsResponse {
   private static final long serialVersionUID = 1L;

   public String toString() {
      return "MbsdCore12009987Out()";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof MbsdCore12009987Out)) {
         return false;
      } else {
         MbsdCore12009987Out other = (MbsdCore12009987Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else {
            return super.equals(o);
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof MbsdCore12009987Out;
   }
}
