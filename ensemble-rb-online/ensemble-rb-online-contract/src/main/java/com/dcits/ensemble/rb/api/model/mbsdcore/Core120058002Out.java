package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;

@MessageOut
public class Core120058002Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "协议编号",
      notNull = false,
      length = "50",
      remark = "协议编号",
      maxSize = 50
   )
   private String agreementId;
   @V(
      desc = "交易参考号",
      notNull = false,
      length = "50",
      remark = "交易参考号",
      maxSize = 50
   )
   private String reference;
   @V(
      desc = "签约日期",
      notNull = false,
      remark = "签约日期"
   )
   private String signDate;
   @V(
      desc = "签约机构",
      notNull = false,
      length = "50",
      remark = "签约机构",
      maxSize = 50
   )
   private String signBranch;
   @V(
      desc = "解约日期",
      notNull = false,
      remark = "解约日期"
   )
   private String outSignDate;
   @V(
      desc = "更新日期",
      notNull = false,
      remark = "更新日期"
   )
   private String updateDate;

   public String getAgreementId() {
      return this.agreementId;
   }

   public String getReference() {
      return this.reference;
   }

   public String getSignDate() {
      return this.signDate;
   }

   public String getSignBranch() {
      return this.signBranch;
   }

   public String getOutSignDate() {
      return this.outSignDate;
   }

   public String getUpdateDate() {
      return this.updateDate;
   }

   public void setAgreementId(String agreementId) {
      this.agreementId = agreementId;
   }

   public void setReference(String reference) {
      this.reference = reference;
   }

   public void setSignDate(String signDate) {
      this.signDate = signDate;
   }

   public void setSignBranch(String signBranch) {
      this.signBranch = signBranch;
   }

   public void setOutSignDate(String outSignDate) {
      this.outSignDate = outSignDate;
   }

   public void setUpdateDate(String updateDate) {
      this.updateDate = updateDate;
   }

   public String toString() {
      return "Core120058002Out(agreementId=" + this.getAgreementId() + ", reference=" + this.getReference() + ", signDate=" + this.getSignDate() + ", signBranch=" + this.getSignBranch() + ", outSignDate=" + this.getOutSignDate() + ", updateDate=" + this.getUpdateDate() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core120058002Out)) {
         return false;
      } else {
         Core120058002Out other = (Core120058002Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$agreementId = this.getAgreementId();
            Object other$agreementId = other.getAgreementId();
            if (this$agreementId == null) {
               if (other$agreementId != null) {
                  return false;
               }
            } else if (!this$agreementId.equals(other$agreementId)) {
               return false;
            }

            Object this$reference = this.getReference();
            Object other$reference = other.getReference();
            if (this$reference == null) {
               if (other$reference != null) {
                  return false;
               }
            } else if (!this$reference.equals(other$reference)) {
               return false;
            }

            label71: {
               Object this$signDate = this.getSignDate();
               Object other$signDate = other.getSignDate();
               if (this$signDate == null) {
                  if (other$signDate == null) {
                     break label71;
                  }
               } else if (this$signDate.equals(other$signDate)) {
                  break label71;
               }

               return false;
            }

            label64: {
               Object this$signBranch = this.getSignBranch();
               Object other$signBranch = other.getSignBranch();
               if (this$signBranch == null) {
                  if (other$signBranch == null) {
                     break label64;
                  }
               } else if (this$signBranch.equals(other$signBranch)) {
                  break label64;
               }

               return false;
            }

            Object this$outSignDate = this.getOutSignDate();
            Object other$outSignDate = other.getOutSignDate();
            if (this$outSignDate == null) {
               if (other$outSignDate != null) {
                  return false;
               }
            } else if (!this$outSignDate.equals(other$outSignDate)) {
               return false;
            }

            Object this$updateDate = this.getUpdateDate();
            Object other$updateDate = other.getUpdateDate();
            if (this$updateDate == null) {
               if (other$updateDate != null) {
                  return false;
               }
            } else if (!this$updateDate.equals(other$updateDate)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core120058002Out;
   }
}
