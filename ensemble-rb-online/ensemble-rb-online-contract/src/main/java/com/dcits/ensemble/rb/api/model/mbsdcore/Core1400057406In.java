package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400057406In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400057406In.Body body;

   public Core1400057406In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400057406In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400057406In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400057406In)) {
         return false;
      } else {
         Core1400057406In other = (Core1400057406In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400057406In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "是否记账标识",
         notNull = false,
         length = "1",
         in = "0,1",
         inDesc = "0-已入账,1-未入账",
         remark = "一本万利收益查询时的查询条件",
         maxSize = 1
      )
      private String inFlag;

      public String getProdType() {
         return this.prodType;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getInFlag() {
         return this.inFlag;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setInFlag(String inFlag) {
         this.inFlag = inFlag;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400057406In.Body)) {
            return false;
         } else {
            Core1400057406In.Body other = (Core1400057406In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label71: {
                  Object this$prodType = this.getProdType();
                  Object other$prodType = other.getProdType();
                  if (this$prodType == null) {
                     if (other$prodType == null) {
                        break label71;
                     }
                  } else if (this$prodType.equals(other$prodType)) {
                     break label71;
                  }

                  return false;
               }

               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo != null) {
                     return false;
                  }
               } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
                  return false;
               }

               label57: {
                  Object this$acctCcy = this.getAcctCcy();
                  Object other$acctCcy = other.getAcctCcy();
                  if (this$acctCcy == null) {
                     if (other$acctCcy == null) {
                        break label57;
                     }
                  } else if (this$acctCcy.equals(other$acctCcy)) {
                     break label57;
                  }

                  return false;
               }

               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo != null) {
                     return false;
                  }
               } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                  return false;
               }

               Object this$inFlag = this.getInFlag();
               Object other$inFlag = other.getInFlag();
               if (this$inFlag == null) {
                  if (other$inFlag == null) {
                     return true;
                  }
               } else if (this$inFlag.equals(other$inFlag)) {
                  return true;
               }

               return false;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400057406In.Body;
      }
      public String toString() {
         return "Core1400057406In.Body(prodType=" + this.getProdType() + ", baseAcctNo=" + this.getBaseAcctNo() + ", acctCcy=" + this.getAcctCcy() + ", acctSeqNo=" + this.getAcctSeqNo() + ", inFlag=" + this.getInFlag() + ")";
      }
   }
}
