package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000251In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000251Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12000251 {
   String URL = "/rb/nfin/exchange/discRateMaint";


   @ApiRemark("客户单户优惠汇率维护")
   @ApiDesc("登记或维护客户的汇率优惠信息")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0251"
   )
   @BusinessCategory("1200-非金融")
   @FunctionCategory("RB02-账户管理")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core12000251Out runService(Core12000251In var1);
}
