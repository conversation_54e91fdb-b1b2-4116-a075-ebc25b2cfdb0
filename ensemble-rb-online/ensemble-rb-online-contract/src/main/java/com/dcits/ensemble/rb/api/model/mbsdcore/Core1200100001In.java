package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1200100001In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1200100001In.Body body;

   public Core1200100001In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1200100001In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1200100001In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200100001In)) {
         return false;
      } else {
         Core1200100001In other = (Core1200100001In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200100001In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "期次代码",
         notNull = false,
         length = "50",
         remark = "期次代码",
         maxSize = 50
      )
      private String stageCode;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "序号",
         notNull = false,
         length = "50",
         remark = "序号",
         maxSize = 50
      )
      private String seqNo;
      @V(
         desc = "预检查分类",
         notNull = false,
         length = "2",
         inDesc = "S-认购检查,T-转让检查",
         remark = "预检查分类",
         maxSize = 2
      )
      private String preCheckClass;

      public String getStageCode() {
         return this.stageCode;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getCcy() {
         return this.ccy;
      }

      public String getSeqNo() {
         return this.seqNo;
      }

      public String getPreCheckClass() {
         return this.preCheckClass;
      }

      public void setStageCode(String stageCode) {
         this.stageCode = stageCode;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setSeqNo(String seqNo) {
         this.seqNo = seqNo;
      }

      public void setPreCheckClass(String preCheckClass) {
         this.preCheckClass = preCheckClass;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1200100001In.Body)) {
            return false;
         } else {
            Core1200100001In.Body other = (Core1200100001In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$stageCode = this.getStageCode();
               Object other$stageCode = other.getStageCode();
               if (this$stageCode == null) {
                  if (other$stageCode != null) {
                     return false;
                  }
               } else if (!this$stageCode.equals(other$stageCode)) {
                  return false;
               }

               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo != null) {
                     return false;
                  }
               } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               label62: {
                  Object this$ccy = this.getCcy();
                  Object other$ccy = other.getCcy();
                  if (this$ccy == null) {
                     if (other$ccy == null) {
                        break label62;
                     }
                  } else if (this$ccy.equals(other$ccy)) {
                     break label62;
                  }

                  return false;
               }

               label55: {
                  Object this$seqNo = this.getSeqNo();
                  Object other$seqNo = other.getSeqNo();
                  if (this$seqNo == null) {
                     if (other$seqNo == null) {
                        break label55;
                     }
                  } else if (this$seqNo.equals(other$seqNo)) {
                     break label55;
                  }

                  return false;
               }

               Object this$preCheckClass = this.getPreCheckClass();
               Object other$preCheckClass = other.getPreCheckClass();
               if (this$preCheckClass == null) {
                  if (other$preCheckClass != null) {
                     return false;
                  }
               } else if (!this$preCheckClass.equals(other$preCheckClass)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1200100001In.Body;
      }
      public String toString() {
         return "Core1200100001In.Body(stageCode=" + this.getStageCode() + ", baseAcctNo=" + this.getBaseAcctNo() + ", prodType=" + this.getProdType() + ", ccy=" + this.getCcy() + ", seqNo=" + this.getSeqNo() + ", preCheckClass=" + this.getPreCheckClass() + ")";
      }
   }
}
