package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400100241In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400100241In.Body body;

   public Core1400100241In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400100241In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400100241In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100241In)) {
         return false;
      } else {
         Core1400100241In other = (Core1400100241In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100241In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "期次代码",
         notNull = true,
         length = "50",
         remark = "期次代码",
         maxSize = 50
      )
      private String stageCode;

      public String getStageCode() {
         return this.stageCode;
      }

      public void setStageCode(String stageCode) {
         this.stageCode = stageCode;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100241In.Body)) {
            return false;
         } else {
            Core1400100241In.Body other = (Core1400100241In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$stageCode = this.getStageCode();
               Object other$stageCode = other.getStageCode();
               if (this$stageCode == null) {
                  if (other$stageCode != null) {
                     return false;
                  }
               } else if (!this$stageCode.equals(other$stageCode)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100241In.Body;
      }
      public String toString() {
         return "Core1400100241In.Body(stageCode=" + this.getStageCode() + ")";
      }
   }
}
