package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000192In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000192Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000192 {
   String URL = "/rb/nfin/card/lucky/receive/query";


   @ApiRemark("查询待签收的吉祥卡批次")
   @ApiDesc("根据申请日期、批次号、卡产品查询未签收的吉祥卡批次")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0192"
   )
   @BusinessCategory("1400-查询")
   @FunctionCategory("RB20-借记卡")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core14000192Out runService(Core14000192In var1);
}
