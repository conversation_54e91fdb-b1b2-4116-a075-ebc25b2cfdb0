package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000174In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000174Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12000174 {
   String URL = "/rb/nfin/register/delaypayint";

   
   @ApiDesc("延期付息信息登记")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0174"
   )
   @FunctionCategory("RB04-计结息")
   Core12000174Out runService(Core12000174In var1);
}
