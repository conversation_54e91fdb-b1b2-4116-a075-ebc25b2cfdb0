package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400100110Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "计息账户数组",
      notNull = false,
      remark = "计息账户数组"
   )
   private List<Core1400100110Out.IntAcctArray> intAcctArray;
   @V(
      desc = "累计历史收益",
      notNull = false,
      length = "17",
      remark = "累计历史收益",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal accruedTranAmt;
   @V(
      desc = "汇总金额",
      notNull = false,
      length = "17",
      remark = "汇总金额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal totalAmount;

   public List<Core1400100110Out.IntAcctArray> getIntAcctArray() {
      return this.intAcctArray;
   }

   public BigDecimal getAccruedTranAmt() {
      return this.accruedTranAmt;
   }

   public BigDecimal getTotalAmount() {
      return this.totalAmount;
   }

   public void setIntAcctArray(List<Core1400100110Out.IntAcctArray> intAcctArray) {
      this.intAcctArray = intAcctArray;
   }

   public void setAccruedTranAmt(BigDecimal accruedTranAmt) {
      this.accruedTranAmt = accruedTranAmt;
   }

   public void setTotalAmount(BigDecimal totalAmount) {
      this.totalAmount = totalAmount;
   }

   public String toString() {
      return "Core1400100110Out(intAcctArray=" + this.getIntAcctArray() + ", accruedTranAmt=" + this.getAccruedTranAmt() + ", totalAmount=" + this.getTotalAmount() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100110Out)) {
         return false;
      } else {
         Core1400100110Out other = (Core1400100110Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            label49: {
               Object this$intAcctArray = this.getIntAcctArray();
               Object other$intAcctArray = other.getIntAcctArray();
               if (this$intAcctArray == null) {
                  if (other$intAcctArray == null) {
                     break label49;
                  }
               } else if (this$intAcctArray.equals(other$intAcctArray)) {
                  break label49;
               }

               return false;
            }

            Object this$accruedTranAmt = this.getAccruedTranAmt();
            Object other$accruedTranAmt = other.getAccruedTranAmt();
            if (this$accruedTranAmt == null) {
               if (other$accruedTranAmt != null) {
                  return false;
               }
            } else if (!this$accruedTranAmt.equals(other$accruedTranAmt)) {
               return false;
            }

            Object this$totalAmount = this.getTotalAmount();
            Object other$totalAmount = other.getTotalAmount();
            if (this$totalAmount == null) {
               if (other$totalAmount != null) {
                  return false;
               }
            } else if (!this$totalAmount.equals(other$totalAmount)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100110Out;
   }
   public static class IntAcctArray {
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "交易日期",
         notNull = false,
         remark = "交易日期"
      )
      private String tranDate;
      @V(
         desc = "交易金额",
         notNull = false,
         length = "17",
         remark = "交易金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal tranAmt;
      @V(
         desc = "开始计提日期",
         notNull = false,
         remark = "开始计提日期"
      )
      private String startAccrualDate;
      @V(
         desc = "结束计提日期",
         notNull = false,
         remark = "结束计提日期"
      )
      private String endAccrualDate;
      @V(
         desc = "利息金额",
         notNull = false,
         length = "17",
         remark = "利息金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal intAmt;
      @V(
         desc = "本息合计",
         notNull = false,
         length = "17",
         remark = "本息合计",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal porIntTot;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "定期账号",
         notNull = false,
         length = "50",
         remark = "定期账号",
         maxSize = 50
      )
      private String tdaBaseAcctNo;
      @V(
         desc = "交易参考号",
         notNull = false,
         length = "50",
         remark = "交易参考号",
         maxSize = 50
      )
      private String reference;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "摘要",
         notNull = false,
         length = "500",
         remark = "开户时的账号用途，销户时的销户原因",
         maxSize = 500
      )
      private String narrative;
      @V(
         desc = "年利率",
         notNull = false,
         length = "15",
         remark = "年利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal yearRate;
      @V(
         desc = "交易描述",
         notNull = false,
         length = "200",
         remark = "交易描述",
         maxSize = 200
      )
      private String tranDesc;
      @V(
         desc = "渠道类型",
         notNull = false,
         length = "10",
         remark = "渠道类型",
         maxSize = 10
      )
      private String sourceType;

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getTranDate() {
         return this.tranDate;
      }

      public BigDecimal getTranAmt() {
         return this.tranAmt;
      }

      public String getStartAccrualDate() {
         return this.startAccrualDate;
      }

      public String getEndAccrualDate() {
         return this.endAccrualDate;
      }

      public BigDecimal getIntAmt() {
         return this.intAmt;
      }

      public BigDecimal getPorIntTot() {
         return this.porIntTot;
      }

      public String getCcy() {
         return this.ccy;
      }

      public String getTdaBaseAcctNo() {
         return this.tdaBaseAcctNo;
      }

      public String getReference() {
         return this.reference;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getNarrative() {
         return this.narrative;
      }

      public BigDecimal getYearRate() {
         return this.yearRate;
      }

      public String getTranDesc() {
         return this.tranDesc;
      }

      public String getSourceType() {
         return this.sourceType;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setTranDate(String tranDate) {
         this.tranDate = tranDate;
      }

      public void setTranAmt(BigDecimal tranAmt) {
         this.tranAmt = tranAmt;
      }

      public void setStartAccrualDate(String startAccrualDate) {
         this.startAccrualDate = startAccrualDate;
      }

      public void setEndAccrualDate(String endAccrualDate) {
         this.endAccrualDate = endAccrualDate;
      }

      public void setIntAmt(BigDecimal intAmt) {
         this.intAmt = intAmt;
      }

      public void setPorIntTot(BigDecimal porIntTot) {
         this.porIntTot = porIntTot;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setTdaBaseAcctNo(String tdaBaseAcctNo) {
         this.tdaBaseAcctNo = tdaBaseAcctNo;
      }

      public void setReference(String reference) {
         this.reference = reference;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setNarrative(String narrative) {
         this.narrative = narrative;
      }

      public void setYearRate(BigDecimal yearRate) {
         this.yearRate = yearRate;
      }

      public void setTranDesc(String tranDesc) {
         this.tranDesc = tranDesc;
      }

      public void setSourceType(String sourceType) {
         this.sourceType = sourceType;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100110Out.IntAcctArray)) {
            return false;
         } else {
            Core1400100110Out.IntAcctArray other = (Core1400100110Out.IntAcctArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label191: {
                  Object this$acctSeqNo = this.getAcctSeqNo();
                  Object other$acctSeqNo = other.getAcctSeqNo();
                  if (this$acctSeqNo == null) {
                     if (other$acctSeqNo == null) {
                        break label191;
                     }
                  } else if (this$acctSeqNo.equals(other$acctSeqNo)) {
                     break label191;
                  }

                  return false;
               }

               Object this$tranDate = this.getTranDate();
               Object other$tranDate = other.getTranDate();
               if (this$tranDate == null) {
                  if (other$tranDate != null) {
                     return false;
                  }
               } else if (!this$tranDate.equals(other$tranDate)) {
                  return false;
               }

               Object this$tranAmt = this.getTranAmt();
               Object other$tranAmt = other.getTranAmt();
               if (this$tranAmt == null) {
                  if (other$tranAmt != null) {
                     return false;
                  }
               } else if (!this$tranAmt.equals(other$tranAmt)) {
                  return false;
               }

               label170: {
                  Object this$startAccrualDate = this.getStartAccrualDate();
                  Object other$startAccrualDate = other.getStartAccrualDate();
                  if (this$startAccrualDate == null) {
                     if (other$startAccrualDate == null) {
                        break label170;
                     }
                  } else if (this$startAccrualDate.equals(other$startAccrualDate)) {
                     break label170;
                  }

                  return false;
               }

               label163: {
                  Object this$endAccrualDate = this.getEndAccrualDate();
                  Object other$endAccrualDate = other.getEndAccrualDate();
                  if (this$endAccrualDate == null) {
                     if (other$endAccrualDate == null) {
                        break label163;
                     }
                  } else if (this$endAccrualDate.equals(other$endAccrualDate)) {
                     break label163;
                  }

                  return false;
               }

               Object this$intAmt = this.getIntAmt();
               Object other$intAmt = other.getIntAmt();
               if (this$intAmt == null) {
                  if (other$intAmt != null) {
                     return false;
                  }
               } else if (!this$intAmt.equals(other$intAmt)) {
                  return false;
               }

               Object this$porIntTot = this.getPorIntTot();
               Object other$porIntTot = other.getPorIntTot();
               if (this$porIntTot == null) {
                  if (other$porIntTot != null) {
                     return false;
                  }
               } else if (!this$porIntTot.equals(other$porIntTot)) {
                  return false;
               }

               label142: {
                  Object this$ccy = this.getCcy();
                  Object other$ccy = other.getCcy();
                  if (this$ccy == null) {
                     if (other$ccy == null) {
                        break label142;
                     }
                  } else if (this$ccy.equals(other$ccy)) {
                     break label142;
                  }

                  return false;
               }

               label135: {
                  Object this$tdaBaseAcctNo = this.getTdaBaseAcctNo();
                  Object other$tdaBaseAcctNo = other.getTdaBaseAcctNo();
                  if (this$tdaBaseAcctNo == null) {
                     if (other$tdaBaseAcctNo == null) {
                        break label135;
                     }
                  } else if (this$tdaBaseAcctNo.equals(other$tdaBaseAcctNo)) {
                     break label135;
                  }

                  return false;
               }

               Object this$reference = this.getReference();
               Object other$reference = other.getReference();
               if (this$reference == null) {
                  if (other$reference != null) {
                     return false;
                  }
               } else if (!this$reference.equals(other$reference)) {
                  return false;
               }

               label121: {
                  Object this$prodType = this.getProdType();
                  Object other$prodType = other.getProdType();
                  if (this$prodType == null) {
                     if (other$prodType == null) {
                        break label121;
                     }
                  } else if (this$prodType.equals(other$prodType)) {
                     break label121;
                  }

                  return false;
               }

               Object this$narrative = this.getNarrative();
               Object other$narrative = other.getNarrative();
               if (this$narrative == null) {
                  if (other$narrative != null) {
                     return false;
                  }
               } else if (!this$narrative.equals(other$narrative)) {
                  return false;
               }

               label107: {
                  Object this$yearRate = this.getYearRate();
                  Object other$yearRate = other.getYearRate();
                  if (this$yearRate == null) {
                     if (other$yearRate == null) {
                        break label107;
                     }
                  } else if (this$yearRate.equals(other$yearRate)) {
                     break label107;
                  }

                  return false;
               }

               Object this$tranDesc = this.getTranDesc();
               Object other$tranDesc = other.getTranDesc();
               if (this$tranDesc == null) {
                  if (other$tranDesc != null) {
                     return false;
                  }
               } else if (!this$tranDesc.equals(other$tranDesc)) {
                  return false;
               }

               Object this$sourceType = this.getSourceType();
               Object other$sourceType = other.getSourceType();
               if (this$sourceType == null) {
                  if (other$sourceType != null) {
                     return false;
                  }
               } else if (!this$sourceType.equals(other$sourceType)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100110Out.IntAcctArray;
      }
      public String toString() {
         return "Core1400100110Out.IntAcctArray(acctSeqNo=" + this.getAcctSeqNo() + ", tranDate=" + this.getTranDate() + ", tranAmt=" + this.getTranAmt() + ", startAccrualDate=" + this.getStartAccrualDate() + ", endAccrualDate=" + this.getEndAccrualDate() + ", intAmt=" + this.getIntAmt() + ", porIntTot=" + this.getPorIntTot() + ", ccy=" + this.getCcy() + ", tdaBaseAcctNo=" + this.getTdaBaseAcctNo() + ", reference=" + this.getReference() + ", prodType=" + this.getProdType() + ", narrative=" + this.getNarrative() + ", yearRate=" + this.getYearRate() + ", tranDesc=" + this.getTranDesc() + ", sourceType=" + this.getSourceType() + ")";
      }
   }
}
