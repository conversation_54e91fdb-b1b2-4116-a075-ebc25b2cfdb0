package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12007092In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12007092Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12007092 {
   String URL = "/rb/nfin/recovery/stMaint";


   @ApiRemark("深度优化")
   @ApiDesc("该接口用于更新账户实施追缴标志")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "7092"
   )
   @BusinessCategory("1200-非金融")
   @FunctionCategory("RB02-账户管理")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core12007092Out runService(Core12007092In var1);
}
