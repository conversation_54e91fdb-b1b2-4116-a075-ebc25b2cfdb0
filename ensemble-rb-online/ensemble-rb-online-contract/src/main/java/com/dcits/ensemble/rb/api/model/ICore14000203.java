package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000203In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000203Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000203 {
   String URL = "/rb/inq/coc/agreement/query";


   @ApiRemark("查询消费透支卡协议信息")
   @ApiDesc("查询消费透支卡协议信息")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0203"
   )
   Core14000203Out runService(Core14000203In var1);
}
