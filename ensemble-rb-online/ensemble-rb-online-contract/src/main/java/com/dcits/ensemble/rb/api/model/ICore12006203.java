package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12006203In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12006203Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12006203 {
   String URL = "/rb/nfin/add/ticket";


   @ApiRemark("加息券登记")
   @ApiDesc("加息券登记")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "6203"
   )
   @BusinessCategory("1200-非金融")
   @FunctionCategory("RB04-计结息")
   @ConsumeSys("CBS")
   @ApiUseStatus("PRODUCT-产品")
   Core12006203Out runService(Core12006203In var1);
}
