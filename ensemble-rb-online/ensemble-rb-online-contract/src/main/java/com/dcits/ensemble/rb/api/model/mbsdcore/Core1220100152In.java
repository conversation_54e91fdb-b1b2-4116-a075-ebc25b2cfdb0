package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1220100152In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1220100152In.Body body;

   public Core1220100152In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1220100152In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1220100152In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1220100152In)) {
         return false;
      } else {
         Core1220100152In other = (Core1220100152In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1220100152In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "审批单号",
         notNull = true,
         length = "50",
         remark = "审批单号",
         maxSize = 50
      )
      private String approvalNo;
      @V(
         desc = "文件路径",
         notNull = false,
         length = "200",
         remark = "文件路径",
         maxSize = 200
      )
      private String filePath;

      public String getApprovalNo() {
         return this.approvalNo;
      }

      public String getFilePath() {
         return this.filePath;
      }

      public void setApprovalNo(String approvalNo) {
         this.approvalNo = approvalNo;
      }

      public void setFilePath(String filePath) {
         this.filePath = filePath;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1220100152In.Body)) {
            return false;
         } else {
            Core1220100152In.Body other = (Core1220100152In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$approvalNo = this.getApprovalNo();
               Object other$approvalNo = other.getApprovalNo();
               if (this$approvalNo == null) {
                  if (other$approvalNo != null) {
                     return false;
                  }
               } else if (!this$approvalNo.equals(other$approvalNo)) {
                  return false;
               }

               Object this$filePath = this.getFilePath();
               Object other$filePath = other.getFilePath();
               if (this$filePath == null) {
                  if (other$filePath != null) {
                     return false;
                  }
               } else if (!this$filePath.equals(other$filePath)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1220100152In.Body;
      }
      public String toString() {
         return "Core1220100152In.Body(approvalNo=" + this.getApprovalNo() + ", filePath=" + this.getFilePath() + ")";
      }
   }
}
