package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@MessageIn
public class Core1200109527In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1200109527In.Body body;

   public Core1200109527In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1200109527In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1200109527In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200109527In)) {
         return false;
      } else {
         Core1200109527In other = (Core1200109527In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200109527In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "交易参考号",
         notNull = false,
         length = "50",
         remark = "交易参考号",
         maxSize = 50
      )
      private String reference;
      @V(
         desc = "渠道流水号",
         notNull = false,
         length = "50",
         remark = "渠道流水号",
         maxSize = 50
      )
      private String channelSeqNo;
      @V(
         desc = "子流水号",
         notNull = false,
         length = "100",
         remark = "子流水号",
         maxSize = 100
      )
      private String subSeqNo;
      @V(
         desc = "真实交易对手数组",
         notNull = false,
         remark = "真实交易对手数组"
      )
      private List<Core1200109527In.Body.OthRealArray> othRealArray;

      public String getReference() {
         return this.reference;
      }

      public String getChannelSeqNo() {
         return this.channelSeqNo;
      }

      public String getSubSeqNo() {
         return this.subSeqNo;
      }

      public List<Core1200109527In.Body.OthRealArray> getOthRealArray() {
         return this.othRealArray;
      }

      public void setReference(String reference) {
         this.reference = reference;
      }

      public void setChannelSeqNo(String channelSeqNo) {
         this.channelSeqNo = channelSeqNo;
      }

      public void setSubSeqNo(String subSeqNo) {
         this.subSeqNo = subSeqNo;
      }

      public void setOthRealArray(List<Core1200109527In.Body.OthRealArray> othRealArray) {
         this.othRealArray = othRealArray;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1200109527In.Body)) {
            return false;
         } else {
            Core1200109527In.Body other = (Core1200109527In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label59: {
                  Object this$reference = this.getReference();
                  Object other$reference = other.getReference();
                  if (this$reference == null) {
                     if (other$reference == null) {
                        break label59;
                     }
                  } else if (this$reference.equals(other$reference)) {
                     break label59;
                  }

                  return false;
               }

               Object this$channelSeqNo = this.getChannelSeqNo();
               Object other$channelSeqNo = other.getChannelSeqNo();
               if (this$channelSeqNo == null) {
                  if (other$channelSeqNo != null) {
                     return false;
                  }
               } else if (!this$channelSeqNo.equals(other$channelSeqNo)) {
                  return false;
               }

               Object this$subSeqNo = this.getSubSeqNo();
               Object other$subSeqNo = other.getSubSeqNo();
               if (this$subSeqNo == null) {
                  if (other$subSeqNo != null) {
                     return false;
                  }
               } else if (!this$subSeqNo.equals(other$subSeqNo)) {
                  return false;
               }

               Object this$othRealArray = this.getOthRealArray();
               Object other$othRealArray = other.getOthRealArray();
               if (this$othRealArray == null) {
                  if (other$othRealArray != null) {
                     return false;
                  }
               } else if (!this$othRealArray.equals(other$othRealArray)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1200109527In.Body;
      }
      public String toString() {
         return "Core1200109527In.Body(reference=" + this.getReference() + ", channelSeqNo=" + this.getChannelSeqNo() + ", subSeqNo=" + this.getSubSeqNo() + ", othRealArray=" + this.getOthRealArray() + ")";
      }

      public static class OthRealArray {
         @V(
            desc = "序号",
            notNull = true,
            length = "50",
            remark = "序号",
            maxSize = 50
         )
         private String seqNo;
         @V(
            desc = "真实交易对手账号",
            notNull = true,
            length = "50",
            remark = "真实交易对手账号",
            maxSize = 50
         )
         private String othRealBaseAcctNo;
         @V(
            desc = "真实对方账户序列号",
            notNull = false,
            length = "5",
            remark = "真实对方账户序列号",
            maxSize = 5
         )
         private String othRealAcctSeqNo;
         @V(
            desc = "真实交易对手名称",
            notNull = true,
            length = "200",
            remark = "真实交易对手名称",
            maxSize = 200
         )
         private String othRealTranName;
         @V(
            desc = "他行行号",
            notNull = true,
            length = "20",
            remark = "他行行号",
            maxSize = 20
         )
         private String contraBankCode;
         @V(
            desc = "交易金额",
            notNull = true,
            length = "17",
            remark = "交易金额",
            decimalLength = 2,
            precision = 2
         )
         private BigDecimal tranAmt;
         @V(
            desc = "源模块",
            notNull = false,
            length = "3",
            inDesc = "RB-存款,CL-贷款,GL-总账,IA-内部户,ALL-所有",
            remark = "源模块",
            maxSize = 3
         )
         private String sourceModule;

         public String getSeqNo() {
            return this.seqNo;
         }

         public String getOthRealBaseAcctNo() {
            return this.othRealBaseAcctNo;
         }

         public String getOthRealAcctSeqNo() {
            return this.othRealAcctSeqNo;
         }

         public String getOthRealTranName() {
            return this.othRealTranName;
         }

         public String getContraBankCode() {
            return this.contraBankCode;
         }

         public BigDecimal getTranAmt() {
            return this.tranAmt;
         }

         public String getSourceModule() {
            return this.sourceModule;
         }

         public void setSeqNo(String seqNo) {
            this.seqNo = seqNo;
         }

         public void setOthRealBaseAcctNo(String othRealBaseAcctNo) {
            this.othRealBaseAcctNo = othRealBaseAcctNo;
         }

         public void setOthRealAcctSeqNo(String othRealAcctSeqNo) {
            this.othRealAcctSeqNo = othRealAcctSeqNo;
         }

         public void setOthRealTranName(String othRealTranName) {
            this.othRealTranName = othRealTranName;
         }

         public void setContraBankCode(String contraBankCode) {
            this.contraBankCode = contraBankCode;
         }

         public void setTranAmt(BigDecimal tranAmt) {
            this.tranAmt = tranAmt;
         }

         public void setSourceModule(String sourceModule) {
            this.sourceModule = sourceModule;
         }

         public boolean equals(Object o) {
            if (o == this) {
               return true;
            } else if (!(o instanceof Core1200109527In.Body.OthRealArray)) {
               return false;
            } else {
               Core1200109527In.Body.OthRealArray other = (Core1200109527In.Body.OthRealArray)o;
               if (!other.canEqual(this)) {
                  return false;
               } else {
                  label95: {
                     Object this$seqNo = this.getSeqNo();
                     Object other$seqNo = other.getSeqNo();
                     if (this$seqNo == null) {
                        if (other$seqNo == null) {
                           break label95;
                        }
                     } else if (this$seqNo.equals(other$seqNo)) {
                        break label95;
                     }

                     return false;
                  }

                  Object this$othRealBaseAcctNo = this.getOthRealBaseAcctNo();
                  Object other$othRealBaseAcctNo = other.getOthRealBaseAcctNo();
                  if (this$othRealBaseAcctNo == null) {
                     if (other$othRealBaseAcctNo != null) {
                        return false;
                     }
                  } else if (!this$othRealBaseAcctNo.equals(other$othRealBaseAcctNo)) {
                     return false;
                  }

                  Object this$othRealAcctSeqNo = this.getOthRealAcctSeqNo();
                  Object other$othRealAcctSeqNo = other.getOthRealAcctSeqNo();
                  if (this$othRealAcctSeqNo == null) {
                     if (other$othRealAcctSeqNo != null) {
                        return false;
                     }
                  } else if (!this$othRealAcctSeqNo.equals(other$othRealAcctSeqNo)) {
                     return false;
                  }

                  label74: {
                     Object this$othRealTranName = this.getOthRealTranName();
                     Object other$othRealTranName = other.getOthRealTranName();
                     if (this$othRealTranName == null) {
                        if (other$othRealTranName == null) {
                           break label74;
                        }
                     } else if (this$othRealTranName.equals(other$othRealTranName)) {
                        break label74;
                     }

                     return false;
                  }

                  label67: {
                     Object this$contraBankCode = this.getContraBankCode();
                     Object other$contraBankCode = other.getContraBankCode();
                     if (this$contraBankCode == null) {
                        if (other$contraBankCode == null) {
                           break label67;
                        }
                     } else if (this$contraBankCode.equals(other$contraBankCode)) {
                        break label67;
                     }

                     return false;
                  }

                  Object this$tranAmt = this.getTranAmt();
                  Object other$tranAmt = other.getTranAmt();
                  if (this$tranAmt == null) {
                     if (other$tranAmt != null) {
                        return false;
                     }
                  } else if (!this$tranAmt.equals(other$tranAmt)) {
                     return false;
                  }

                  Object this$sourceModule = this.getSourceModule();
                  Object other$sourceModule = other.getSourceModule();
                  if (this$sourceModule == null) {
                     if (other$sourceModule != null) {
                        return false;
                     }
                  } else if (!this$sourceModule.equals(other$sourceModule)) {
                     return false;
                  }

                  return true;
               }
            }
         }

         protected boolean canEqual(Object other) {
            return other instanceof Core1200109527In.Body.OthRealArray;
         }
         public String toString() {
            return "Core1200109527In.Body.OthRealArray(seqNo=" + this.getSeqNo() + ", othRealBaseAcctNo=" + this.getOthRealBaseAcctNo() + ", othRealAcctSeqNo=" + this.getOthRealAcctSeqNo() + ", othRealTranName=" + this.getOthRealTranName() + ", contraBankCode=" + this.getContraBankCode() + ", tranAmt=" + this.getTranAmt() + ", sourceModule=" + this.getSourceModule() + ")";
         }
      }
   }
}
