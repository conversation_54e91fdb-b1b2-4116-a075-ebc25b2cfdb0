package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12009405In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12009405Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12009405 {
   String URL = "/rb/nfin/inner/term/baseSub/open";


   @ApiRemark("华兴需求")
   @ApiDesc("存放同业定期一本通主子账户开立")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "9405"
   )
   @BusinessCategory("1200-非金融")
   @FunctionCategory("RB15-内部账")
   @ConsumeSys("TLE/137")
   @ApiUseStatus("PRODUCT-产品")
   Core12009405Out runService(Core12009405In var1);
}
