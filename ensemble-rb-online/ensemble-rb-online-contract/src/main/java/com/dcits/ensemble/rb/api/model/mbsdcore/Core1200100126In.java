package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;

@MessageIn
public class Core1200100126In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1200100126In.Body body;

   public Core1200100126In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1200100126In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1200100126In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200100126In)) {
         return false;
      } else {
         Core1200100126In other = (Core1200100126In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200100126In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "协议操作类型",
         notNull = true,
         length = "2",
         inDesc = "01-创建,02-修改,03-删除",
         remark = "协议操作类型",
         maxSize = 2
      )
      private String agreementOperateType;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "协议类型",
         notNull = false,
         length = "10",
         inDesc = "CLD-存立得,DC-大额存单,DLS-贷利省,HQB-活期宝,JDL-加多利,KDT-卡贷通,KYD-卡易贷,PCP-资金池,WDL-稳得利,XDB-协定宝,XDCK-协定存款产品,XDL-先得利,YBWL-一本万利,YCD-英才贷,YDT-易贷通,YHT-一户通,ZHY-周享赢,ZXY-坐享其盈,ZZB-至尊宝,LOA-贷款,ODF-法人透支协议,FIN-卡理财协议,SMS-短信,PKG-费用套餐,FEE-暂不收费,PCD-周期性强制扣划,ACC-协定存款协议,SWP-账户清扫协议,ID-智能存款协议,SL-金额补足协议,REC-回单签约,ES-电票签约,YD-约定,NTE-活期智能存款,PAS-隐私账户签约,BXD-协定利率（无留存）",
         remark = "协议类型",
         maxSize = 10
      )
      private String agreementType;
      @V(
         desc = "签约产品类型",
         notNull = false,
         length = "20",
         remark = "签约产品类型",
         maxSize = 20
      )
      private String signProdType;
      @V(
         desc = "是否自动转存",
         notNull = false,
         length = "10",
         inDesc = "N-不自动转存,W-本金自动转存,O-本息自动转存",
         remark = "定期是否自动转存",
         maxSize = 10
      )
      private String autoRenewRollover;
      @V(
         desc = "存期期限",
         notNull = false,
         length = "5",
         remark = "期限",
         maxSize = 5
      )
      private String term;
      @V(
         desc = "期限类型",
         notNull = false,
         length = "1",
         inDesc = "Y-年,Q-季,M-月,W-周,D-日",
         remark = "期限类型",
         maxSize = 1
      )
      private String termType;
      @V(
         desc = "定期账号",
         notNull = false,
         length = "50",
         remark = "定期账号",
         maxSize = 50
      )
      private String tdaBaseAcctNo;
      @V(
         desc = "定期账户产品类型",
         notNull = false,
         length = "20",
         remark = "定期账户产品类型",
         maxSize = 20
      )
      private String tdaAcctProdType;
      @V(
         desc = "定期账户币种",
         notNull = false,
         length = "3",
         remark = "定期账户币种",
         maxSize = 3
      )
      private String tdaAcctCcy;
      @V(
         desc = "定期账户序列号",
         notNull = false,
         length = "5",
         remark = "定期账户序列号",
         maxSize = 5
      )
      private String tdaAcctSeqNo;
      @V(
         desc = "协议编号",
         notNull = false,
         length = "50",
         remark = "协议编号",
         maxSize = 50
      )
      private String agreementId;
      @V(
         desc = "转存起始日期",
         notNull = false,
         remark = "理财签约后的转存开始日期"
      )
      private String transferStartDate;
      @V(
         desc = "转存结束日期或终止日期",
         notNull = false,
         remark = "理财协议转存的结束日期"
      )
      private String transferEndDate;
      @V(
         desc = "划转频率",
         notNull = false,
         length = "5",
         remark = "划转频率",
         maxSize = 5
      )
      private String transferFreq;
      @V(
         desc = "划转频率类型",
         notNull = false,
         length = "5",
         remark = "理财划转频率了行，Y年/M-月/D-日/Q-季/W-周",
         maxSize = 5
      )
      private String transferFreqType;
      @V(
         desc = "划转日",
         notNull = false,
         length = "2",
         remark = "划转日",
         maxSize = 2
      )
      private String transferDay;
      @V(
         desc = "协议留存金额",
         notNull = false,
         length = "17",
         remark = "协议留存金额(签约卡转存后  最小留存金额)",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal remainAmt;
      @V(
         desc = "理财固定金额",
         notNull = false,
         length = "17",
         remark = "理财固定金额（活期卡约定固定转存金额）",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal finFixedAmt;
      @V(
         desc = "客户经理",
         notNull = false,
         length = "30",
         remark = "客户经理",
         maxSize = 30
      )
      private String acctExec;
      @V(
         desc = "最小起存金额",
         notNull = false,
         length = "17",
         remark = "最小起存金额（活期卡最小转存借记金额 小于此金额不转存）",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal intMinAmt;
      @V(
         desc = "客户经理姓名",
         notNull = false,
         length = "200",
         remark = "客户经理姓名",
         maxSize = 200
      )
      private String acctExecName;
      @V(
         desc = "客户号",
         notNull = true,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "开始日期",
         notNull = false,
         remark = "开始日期"
      )
      private String startDate;
      @V(
         desc = "结束日期",
         notNull = false,
         remark = "结束日期"
      )
      private String endDate;
      @V(
         desc = "本金部分转存金额",
         notNull = false,
         length = "17",
         remark = "本金部分转存金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal partialRrAmt;
      @V(
         desc = "存款性质",
         notNull = false,
         length = "10",
         inDesc = "JJSB-基金社保,CZCK-财政性存款,JIES-结算类,TRZL-投融资类,QT-其他",
         remark = "存款性质",
         maxSize = 10
      )
      private String depositNature;
      @V(
         desc = "自动结清标志",
         notNull = false,
         length = "1",
         inDesc = "Y-是,N-否",
         remark = "自动结清标志",
         maxSize = 1
      )
      private String autoSettleFlag;

      public String getAgreementOperateType() {
         return this.agreementOperateType;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getAgreementType() {
         return this.agreementType;
      }

      public String getSignProdType() {
         return this.signProdType;
      }

      public String getAutoRenewRollover() {
         return this.autoRenewRollover;
      }

      public String getTerm() {
         return this.term;
      }

      public String getTermType() {
         return this.termType;
      }

      public String getTdaBaseAcctNo() {
         return this.tdaBaseAcctNo;
      }

      public String getTdaAcctProdType() {
         return this.tdaAcctProdType;
      }

      public String getTdaAcctCcy() {
         return this.tdaAcctCcy;
      }

      public String getTdaAcctSeqNo() {
         return this.tdaAcctSeqNo;
      }

      public String getAgreementId() {
         return this.agreementId;
      }

      public String getTransferStartDate() {
         return this.transferStartDate;
      }

      public String getTransferEndDate() {
         return this.transferEndDate;
      }

      public String getTransferFreq() {
         return this.transferFreq;
      }

      public String getTransferFreqType() {
         return this.transferFreqType;
      }

      public String getTransferDay() {
         return this.transferDay;
      }

      public BigDecimal getRemainAmt() {
         return this.remainAmt;
      }

      public BigDecimal getFinFixedAmt() {
         return this.finFixedAmt;
      }

      public String getAcctExec() {
         return this.acctExec;
      }

      public BigDecimal getIntMinAmt() {
         return this.intMinAmt;
      }

      public String getAcctExecName() {
         return this.acctExecName;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public String getStartDate() {
         return this.startDate;
      }

      public String getEndDate() {
         return this.endDate;
      }

      public BigDecimal getPartialRrAmt() {
         return this.partialRrAmt;
      }

      public String getDepositNature() {
         return this.depositNature;
      }

      public String getAutoSettleFlag() {
         return this.autoSettleFlag;
      }

      public void setAgreementOperateType(String agreementOperateType) {
         this.agreementOperateType = agreementOperateType;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setAgreementType(String agreementType) {
         this.agreementType = agreementType;
      }

      public void setSignProdType(String signProdType) {
         this.signProdType = signProdType;
      }

      public void setAutoRenewRollover(String autoRenewRollover) {
         this.autoRenewRollover = autoRenewRollover;
      }

      public void setTerm(String term) {
         this.term = term;
      }

      public void setTermType(String termType) {
         this.termType = termType;
      }

      public void setTdaBaseAcctNo(String tdaBaseAcctNo) {
         this.tdaBaseAcctNo = tdaBaseAcctNo;
      }

      public void setTdaAcctProdType(String tdaAcctProdType) {
         this.tdaAcctProdType = tdaAcctProdType;
      }

      public void setTdaAcctCcy(String tdaAcctCcy) {
         this.tdaAcctCcy = tdaAcctCcy;
      }

      public void setTdaAcctSeqNo(String tdaAcctSeqNo) {
         this.tdaAcctSeqNo = tdaAcctSeqNo;
      }

      public void setAgreementId(String agreementId) {
         this.agreementId = agreementId;
      }

      public void setTransferStartDate(String transferStartDate) {
         this.transferStartDate = transferStartDate;
      }

      public void setTransferEndDate(String transferEndDate) {
         this.transferEndDate = transferEndDate;
      }

      public void setTransferFreq(String transferFreq) {
         this.transferFreq = transferFreq;
      }

      public void setTransferFreqType(String transferFreqType) {
         this.transferFreqType = transferFreqType;
      }

      public void setTransferDay(String transferDay) {
         this.transferDay = transferDay;
      }

      public void setRemainAmt(BigDecimal remainAmt) {
         this.remainAmt = remainAmt;
      }

      public void setFinFixedAmt(BigDecimal finFixedAmt) {
         this.finFixedAmt = finFixedAmt;
      }

      public void setAcctExec(String acctExec) {
         this.acctExec = acctExec;
      }

      public void setIntMinAmt(BigDecimal intMinAmt) {
         this.intMinAmt = intMinAmt;
      }

      public void setAcctExecName(String acctExecName) {
         this.acctExecName = acctExecName;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setStartDate(String startDate) {
         this.startDate = startDate;
      }

      public void setEndDate(String endDate) {
         this.endDate = endDate;
      }

      public void setPartialRrAmt(BigDecimal partialRrAmt) {
         this.partialRrAmt = partialRrAmt;
      }

      public void setDepositNature(String depositNature) {
         this.depositNature = depositNature;
      }

      public void setAutoSettleFlag(String autoSettleFlag) {
         this.autoSettleFlag = autoSettleFlag;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1200100126In.Body)) {
            return false;
         } else {
            Core1200100126In.Body other = (Core1200100126In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label383: {
                  Object this$agreementOperateType = this.getAgreementOperateType();
                  Object other$agreementOperateType = other.getAgreementOperateType();
                  if (this$agreementOperateType == null) {
                     if (other$agreementOperateType == null) {
                        break label383;
                     }
                  } else if (this$agreementOperateType.equals(other$agreementOperateType)) {
                     break label383;
                  }

                  return false;
               }

               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo != null) {
                     return false;
                  }
               } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               label362: {
                  Object this$acctCcy = this.getAcctCcy();
                  Object other$acctCcy = other.getAcctCcy();
                  if (this$acctCcy == null) {
                     if (other$acctCcy == null) {
                        break label362;
                     }
                  } else if (this$acctCcy.equals(other$acctCcy)) {
                     break label362;
                  }

                  return false;
               }

               label355: {
                  Object this$acctSeqNo = this.getAcctSeqNo();
                  Object other$acctSeqNo = other.getAcctSeqNo();
                  if (this$acctSeqNo == null) {
                     if (other$acctSeqNo == null) {
                        break label355;
                     }
                  } else if (this$acctSeqNo.equals(other$acctSeqNo)) {
                     break label355;
                  }

                  return false;
               }

               Object this$agreementType = this.getAgreementType();
               Object other$agreementType = other.getAgreementType();
               if (this$agreementType == null) {
                  if (other$agreementType != null) {
                     return false;
                  }
               } else if (!this$agreementType.equals(other$agreementType)) {
                  return false;
               }

               Object this$signProdType = this.getSignProdType();
               Object other$signProdType = other.getSignProdType();
               if (this$signProdType == null) {
                  if (other$signProdType != null) {
                     return false;
                  }
               } else if (!this$signProdType.equals(other$signProdType)) {
                  return false;
               }

               label334: {
                  Object this$autoRenewRollover = this.getAutoRenewRollover();
                  Object other$autoRenewRollover = other.getAutoRenewRollover();
                  if (this$autoRenewRollover == null) {
                     if (other$autoRenewRollover == null) {
                        break label334;
                     }
                  } else if (this$autoRenewRollover.equals(other$autoRenewRollover)) {
                     break label334;
                  }

                  return false;
               }

               label327: {
                  Object this$term = this.getTerm();
                  Object other$term = other.getTerm();
                  if (this$term == null) {
                     if (other$term == null) {
                        break label327;
                     }
                  } else if (this$term.equals(other$term)) {
                     break label327;
                  }

                  return false;
               }

               Object this$termType = this.getTermType();
               Object other$termType = other.getTermType();
               if (this$termType == null) {
                  if (other$termType != null) {
                     return false;
                  }
               } else if (!this$termType.equals(other$termType)) {
                  return false;
               }

               label313: {
                  Object this$tdaBaseAcctNo = this.getTdaBaseAcctNo();
                  Object other$tdaBaseAcctNo = other.getTdaBaseAcctNo();
                  if (this$tdaBaseAcctNo == null) {
                     if (other$tdaBaseAcctNo == null) {
                        break label313;
                     }
                  } else if (this$tdaBaseAcctNo.equals(other$tdaBaseAcctNo)) {
                     break label313;
                  }

                  return false;
               }

               Object this$tdaAcctProdType = this.getTdaAcctProdType();
               Object other$tdaAcctProdType = other.getTdaAcctProdType();
               if (this$tdaAcctProdType == null) {
                  if (other$tdaAcctProdType != null) {
                     return false;
                  }
               } else if (!this$tdaAcctProdType.equals(other$tdaAcctProdType)) {
                  return false;
               }

               label299: {
                  Object this$tdaAcctCcy = this.getTdaAcctCcy();
                  Object other$tdaAcctCcy = other.getTdaAcctCcy();
                  if (this$tdaAcctCcy == null) {
                     if (other$tdaAcctCcy == null) {
                        break label299;
                     }
                  } else if (this$tdaAcctCcy.equals(other$tdaAcctCcy)) {
                     break label299;
                  }

                  return false;
               }

               Object this$tdaAcctSeqNo = this.getTdaAcctSeqNo();
               Object other$tdaAcctSeqNo = other.getTdaAcctSeqNo();
               if (this$tdaAcctSeqNo == null) {
                  if (other$tdaAcctSeqNo != null) {
                     return false;
                  }
               } else if (!this$tdaAcctSeqNo.equals(other$tdaAcctSeqNo)) {
                  return false;
               }

               Object this$agreementId = this.getAgreementId();
               Object other$agreementId = other.getAgreementId();
               if (this$agreementId == null) {
                  if (other$agreementId != null) {
                     return false;
                  }
               } else if (!this$agreementId.equals(other$agreementId)) {
                  return false;
               }

               label278: {
                  Object this$transferStartDate = this.getTransferStartDate();
                  Object other$transferStartDate = other.getTransferStartDate();
                  if (this$transferStartDate == null) {
                     if (other$transferStartDate == null) {
                        break label278;
                     }
                  } else if (this$transferStartDate.equals(other$transferStartDate)) {
                     break label278;
                  }

                  return false;
               }

               label271: {
                  Object this$transferEndDate = this.getTransferEndDate();
                  Object other$transferEndDate = other.getTransferEndDate();
                  if (this$transferEndDate == null) {
                     if (other$transferEndDate == null) {
                        break label271;
                     }
                  } else if (this$transferEndDate.equals(other$transferEndDate)) {
                     break label271;
                  }

                  return false;
               }

               Object this$transferFreq = this.getTransferFreq();
               Object other$transferFreq = other.getTransferFreq();
               if (this$transferFreq == null) {
                  if (other$transferFreq != null) {
                     return false;
                  }
               } else if (!this$transferFreq.equals(other$transferFreq)) {
                  return false;
               }

               Object this$transferFreqType = this.getTransferFreqType();
               Object other$transferFreqType = other.getTransferFreqType();
               if (this$transferFreqType == null) {
                  if (other$transferFreqType != null) {
                     return false;
                  }
               } else if (!this$transferFreqType.equals(other$transferFreqType)) {
                  return false;
               }

               label250: {
                  Object this$transferDay = this.getTransferDay();
                  Object other$transferDay = other.getTransferDay();
                  if (this$transferDay == null) {
                     if (other$transferDay == null) {
                        break label250;
                     }
                  } else if (this$transferDay.equals(other$transferDay)) {
                     break label250;
                  }

                  return false;
               }

               label243: {
                  Object this$remainAmt = this.getRemainAmt();
                  Object other$remainAmt = other.getRemainAmt();
                  if (this$remainAmt == null) {
                     if (other$remainAmt == null) {
                        break label243;
                     }
                  } else if (this$remainAmt.equals(other$remainAmt)) {
                     break label243;
                  }

                  return false;
               }

               Object this$finFixedAmt = this.getFinFixedAmt();
               Object other$finFixedAmt = other.getFinFixedAmt();
               if (this$finFixedAmt == null) {
                  if (other$finFixedAmt != null) {
                     return false;
                  }
               } else if (!this$finFixedAmt.equals(other$finFixedAmt)) {
                  return false;
               }

               Object this$acctExec = this.getAcctExec();
               Object other$acctExec = other.getAcctExec();
               if (this$acctExec == null) {
                  if (other$acctExec != null) {
                     return false;
                  }
               } else if (!this$acctExec.equals(other$acctExec)) {
                  return false;
               }

               label222: {
                  Object this$intMinAmt = this.getIntMinAmt();
                  Object other$intMinAmt = other.getIntMinAmt();
                  if (this$intMinAmt == null) {
                     if (other$intMinAmt == null) {
                        break label222;
                     }
                  } else if (this$intMinAmt.equals(other$intMinAmt)) {
                     break label222;
                  }

                  return false;
               }

               label215: {
                  Object this$acctExecName = this.getAcctExecName();
                  Object other$acctExecName = other.getAcctExecName();
                  if (this$acctExecName == null) {
                     if (other$acctExecName == null) {
                        break label215;
                     }
                  } else if (this$acctExecName.equals(other$acctExecName)) {
                     break label215;
                  }

                  return false;
               }

               Object this$clientNo = this.getClientNo();
               Object other$clientNo = other.getClientNo();
               if (this$clientNo == null) {
                  if (other$clientNo != null) {
                     return false;
                  }
               } else if (!this$clientNo.equals(other$clientNo)) {
                  return false;
               }

               label201: {
                  Object this$startDate = this.getStartDate();
                  Object other$startDate = other.getStartDate();
                  if (this$startDate == null) {
                     if (other$startDate == null) {
                        break label201;
                     }
                  } else if (this$startDate.equals(other$startDate)) {
                     break label201;
                  }

                  return false;
               }

               Object this$endDate = this.getEndDate();
               Object other$endDate = other.getEndDate();
               if (this$endDate == null) {
                  if (other$endDate != null) {
                     return false;
                  }
               } else if (!this$endDate.equals(other$endDate)) {
                  return false;
               }

               label187: {
                  Object this$partialRrAmt = this.getPartialRrAmt();
                  Object other$partialRrAmt = other.getPartialRrAmt();
                  if (this$partialRrAmt == null) {
                     if (other$partialRrAmt == null) {
                        break label187;
                     }
                  } else if (this$partialRrAmt.equals(other$partialRrAmt)) {
                     break label187;
                  }

                  return false;
               }

               Object this$depositNature = this.getDepositNature();
               Object other$depositNature = other.getDepositNature();
               if (this$depositNature == null) {
                  if (other$depositNature != null) {
                     return false;
                  }
               } else if (!this$depositNature.equals(other$depositNature)) {
                  return false;
               }

               Object this$autoSettleFlag = this.getAutoSettleFlag();
               Object other$autoSettleFlag = other.getAutoSettleFlag();
               if (this$autoSettleFlag == null) {
                  if (other$autoSettleFlag != null) {
                     return false;
                  }
               } else if (!this$autoSettleFlag.equals(other$autoSettleFlag)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1200100126In.Body;
      }
      public String toString() {
         return "Core1200100126In.Body(agreementOperateType=" + this.getAgreementOperateType() + ", baseAcctNo=" + this.getBaseAcctNo() + ", prodType=" + this.getProdType() + ", acctCcy=" + this.getAcctCcy() + ", acctSeqNo=" + this.getAcctSeqNo() + ", agreementType=" + this.getAgreementType() + ", signProdType=" + this.getSignProdType() + ", autoRenewRollover=" + this.getAutoRenewRollover() + ", term=" + this.getTerm() + ", termType=" + this.getTermType() + ", tdaBaseAcctNo=" + this.getTdaBaseAcctNo() + ", tdaAcctProdType=" + this.getTdaAcctProdType() + ", tdaAcctCcy=" + this.getTdaAcctCcy() + ", tdaAcctSeqNo=" + this.getTdaAcctSeqNo() + ", agreementId=" + this.getAgreementId() + ", transferStartDate=" + this.getTransferStartDate() + ", transferEndDate=" + this.getTransferEndDate() + ", transferFreq=" + this.getTransferFreq() + ", transferFreqType=" + this.getTransferFreqType() + ", transferDay=" + this.getTransferDay() + ", remainAmt=" + this.getRemainAmt() + ", finFixedAmt=" + this.getFinFixedAmt() + ", acctExec=" + this.getAcctExec() + ", intMinAmt=" + this.getIntMinAmt() + ", acctExecName=" + this.getAcctExecName() + ", clientNo=" + this.getClientNo() + ", startDate=" + this.getStartDate() + ", endDate=" + this.getEndDate() + ", partialRrAmt=" + this.getPartialRrAmt() + ", depositNature=" + this.getDepositNature() + ", autoSettleFlag=" + this.getAutoSettleFlag() + ")";
      }
   }
}
