package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12001120In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12001120Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12001120 {
   String URL = "/rb/nfin/voucher/court";

   
   @ApiRemark("标准优化")
   @ApiDesc("凭证法院处理，包括公示催告和法院作废")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "1120"
   )
   @BusinessCategory("1200-非金融")
   @FunctionCategory("RB06-凭证处理")
   @ConsumeSys("TLE/CIT")
   @ApiUseStatus("PRODUCT-产品")
   Core12001120Out runService(Core12001120In var1);
}
