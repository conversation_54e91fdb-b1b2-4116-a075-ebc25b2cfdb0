package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400100015In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400100015In.Body body;

   public Core1400100015In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400100015In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400100015In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100015In)) {
         return false;
      } else {
         Core1400100015In other = (Core1400100015In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100015In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "交易柜员",
         notNull = true,
         length = "30",
         remark = "交易柜员",
         maxSize = 30
      )
      private String userId;
      @V(
         desc = "凭证类型",
         notNull = true,
         length = "10",
         remark = "凭证类型",
         maxSize = 10
      )
      private String docType;
      @V(
         desc = "凭证号码",
         notNull = false,
         length = "50",
         remark = "凭证号码",
         maxSize = 50
      )
      private String voucherNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;

      public String getUserId() {
         return this.userId;
      }

      public String getDocType() {
         return this.docType;
      }

      public String getVoucherNo() {
         return this.voucherNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public void setUserId(String userId) {
         this.userId = userId;
      }

      public void setDocType(String docType) {
         this.docType = docType;
      }

      public void setVoucherNo(String voucherNo) {
         this.voucherNo = voucherNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100015In.Body)) {
            return false;
         } else {
            Core1400100015In.Body other = (Core1400100015In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label59: {
                  Object this$userId = this.getUserId();
                  Object other$userId = other.getUserId();
                  if (this$userId == null) {
                     if (other$userId == null) {
                        break label59;
                     }
                  } else if (this$userId.equals(other$userId)) {
                     break label59;
                  }

                  return false;
               }

               Object this$docType = this.getDocType();
               Object other$docType = other.getDocType();
               if (this$docType == null) {
                  if (other$docType != null) {
                     return false;
                  }
               } else if (!this$docType.equals(other$docType)) {
                  return false;
               }

               Object this$voucherNo = this.getVoucherNo();
               Object other$voucherNo = other.getVoucherNo();
               if (this$voucherNo == null) {
                  if (other$voucherNo != null) {
                     return false;
                  }
               } else if (!this$voucherNo.equals(other$voucherNo)) {
                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100015In.Body;
      }
      public String toString() {
         return "Core1400100015In.Body(userId=" + this.getUserId() + ", docType=" + this.getDocType() + ", voucherNo=" + this.getVoucherNo() + ", prodType=" + this.getProdType() + ")";
      }
   }
}
