package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400100002Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100002Out.IntArray> intArray;

   public List<Core1400100002Out.IntArray> getIntArray() {
      return this.intArray;
   }

   public void setIntArray(List<Core1400100002Out.IntArray> intArray) {
      this.intArray = intArray;
   }

   public String toString() {
      return "Core1400100002Out(intArray=" + this.getIntArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100002Out)) {
         return false;
      } else {
         Core1400100002Out other = (Core1400100002Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$intArray = this.getIntArray();
            Object other$intArray = other.getIntArray();
            if (this$intArray == null) {
               if (other$intArray != null) {
                  return false;
               }
            } else if (!this$intArray.equals(other$intArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100002Out;
   }
   public static class IntArray {
      @V(
         desc = "费用计提编号",
         notNull = false,
         length = "50",
         remark = "费用计提编号",
         maxSize = 50
      )
      private String feeIntNo;
      @V(
         desc = "费用类型",
         notNull = false,
         length = "20",
         remark = "费用类型",
         maxSize = 20
      )
      private String feeType;
      @V(
         desc = "利息金额",
         notNull = false,
         length = "17",
         remark = "利息金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal intAmt;
      @V(
         desc = "原业务编号",
         notNull = false,
         length = "50",
         remark = "原业务编号,如承兑汇票号码,信用证编号等",
         maxSize = 50
      )
      private String extTradeNo;
      @V(
         desc = "开始计提日期",
         notNull = false,
         remark = "开始计提日期"
      )
      private String startAccrualDate;
      @V(
         desc = "结束计提日期",
         notNull = false,
         remark = "结束计提日期"
      )
      private String endAccrualDate;
      @V(
         desc = "累计计提",
         notNull = false,
         length = "17",
         remark = "累计计提",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal intAccrued;
      @V(
         desc = "核销利息金额",
         notNull = false,
         length = "17",
         remark = "核销利息金额，包含已核销的所有应计应收利息、罚息、复利金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal writeOffIntAmt;
      @V(
         desc = "交易机构",
         notNull = false,
         length = "50",
         remark = "交易机构",
         maxSize = 50
      )
      private String tranBranch;
      @V(
         desc = "交易柜员",
         notNull = false,
         length = "30",
         remark = "交易柜员",
         maxSize = 30
      )
      private String userId;
      @V(
         desc = "授权柜员",
         notNull = false,
         length = "30",
         remark = "授权柜员",
         maxSize = 30
      )
      private String authUserId;
      @V(
         desc = "法人",
         notNull = false,
         length = "20",
         remark = "法人",
         maxSize = 20
      )
      private String company;
      @V(
         desc = "交易日期",
         notNull = false,
         remark = "交易日期"
      )
      private String tranDate;
      @V(
         desc = "摘要",
         notNull = false,
         length = "500",
         remark = "开户时的账号用途，销户时的销户原因",
         maxSize = 500
      )
      private String narrative;
      @V(
         desc = "摘要码",
         notNull = false,
         length = "30",
         remark = "摘要码",
         maxSize = 30
      )
      private String narrativeCode;

      public String getFeeIntNo() {
         return this.feeIntNo;
      }

      public String getFeeType() {
         return this.feeType;
      }

      public BigDecimal getIntAmt() {
         return this.intAmt;
      }

      public String getExtTradeNo() {
         return this.extTradeNo;
      }

      public String getStartAccrualDate() {
         return this.startAccrualDate;
      }

      public String getEndAccrualDate() {
         return this.endAccrualDate;
      }

      public BigDecimal getIntAccrued() {
         return this.intAccrued;
      }

      public BigDecimal getWriteOffIntAmt() {
         return this.writeOffIntAmt;
      }

      public String getTranBranch() {
         return this.tranBranch;
      }

      public String getUserId() {
         return this.userId;
      }

      public String getAuthUserId() {
         return this.authUserId;
      }

      public String getCompany() {
         return this.company;
      }

      public String getTranDate() {
         return this.tranDate;
      }

      public String getNarrative() {
         return this.narrative;
      }

      public String getNarrativeCode() {
         return this.narrativeCode;
      }

      public void setFeeIntNo(String feeIntNo) {
         this.feeIntNo = feeIntNo;
      }

      public void setFeeType(String feeType) {
         this.feeType = feeType;
      }

      public void setIntAmt(BigDecimal intAmt) {
         this.intAmt = intAmt;
      }

      public void setExtTradeNo(String extTradeNo) {
         this.extTradeNo = extTradeNo;
      }

      public void setStartAccrualDate(String startAccrualDate) {
         this.startAccrualDate = startAccrualDate;
      }

      public void setEndAccrualDate(String endAccrualDate) {
         this.endAccrualDate = endAccrualDate;
      }

      public void setIntAccrued(BigDecimal intAccrued) {
         this.intAccrued = intAccrued;
      }

      public void setWriteOffIntAmt(BigDecimal writeOffIntAmt) {
         this.writeOffIntAmt = writeOffIntAmt;
      }

      public void setTranBranch(String tranBranch) {
         this.tranBranch = tranBranch;
      }

      public void setUserId(String userId) {
         this.userId = userId;
      }

      public void setAuthUserId(String authUserId) {
         this.authUserId = authUserId;
      }

      public void setCompany(String company) {
         this.company = company;
      }

      public void setTranDate(String tranDate) {
         this.tranDate = tranDate;
      }

      public void setNarrative(String narrative) {
         this.narrative = narrative;
      }

      public void setNarrativeCode(String narrativeCode) {
         this.narrativeCode = narrativeCode;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100002Out.IntArray)) {
            return false;
         } else {
            Core1400100002Out.IntArray other = (Core1400100002Out.IntArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label191: {
                  Object this$feeIntNo = this.getFeeIntNo();
                  Object other$feeIntNo = other.getFeeIntNo();
                  if (this$feeIntNo == null) {
                     if (other$feeIntNo == null) {
                        break label191;
                     }
                  } else if (this$feeIntNo.equals(other$feeIntNo)) {
                     break label191;
                  }

                  return false;
               }

               Object this$feeType = this.getFeeType();
               Object other$feeType = other.getFeeType();
               if (this$feeType == null) {
                  if (other$feeType != null) {
                     return false;
                  }
               } else if (!this$feeType.equals(other$feeType)) {
                  return false;
               }

               Object this$intAmt = this.getIntAmt();
               Object other$intAmt = other.getIntAmt();
               if (this$intAmt == null) {
                  if (other$intAmt != null) {
                     return false;
                  }
               } else if (!this$intAmt.equals(other$intAmt)) {
                  return false;
               }

               label170: {
                  Object this$extTradeNo = this.getExtTradeNo();
                  Object other$extTradeNo = other.getExtTradeNo();
                  if (this$extTradeNo == null) {
                     if (other$extTradeNo == null) {
                        break label170;
                     }
                  } else if (this$extTradeNo.equals(other$extTradeNo)) {
                     break label170;
                  }

                  return false;
               }

               label163: {
                  Object this$startAccrualDate = this.getStartAccrualDate();
                  Object other$startAccrualDate = other.getStartAccrualDate();
                  if (this$startAccrualDate == null) {
                     if (other$startAccrualDate == null) {
                        break label163;
                     }
                  } else if (this$startAccrualDate.equals(other$startAccrualDate)) {
                     break label163;
                  }

                  return false;
               }

               Object this$endAccrualDate = this.getEndAccrualDate();
               Object other$endAccrualDate = other.getEndAccrualDate();
               if (this$endAccrualDate == null) {
                  if (other$endAccrualDate != null) {
                     return false;
                  }
               } else if (!this$endAccrualDate.equals(other$endAccrualDate)) {
                  return false;
               }

               Object this$intAccrued = this.getIntAccrued();
               Object other$intAccrued = other.getIntAccrued();
               if (this$intAccrued == null) {
                  if (other$intAccrued != null) {
                     return false;
                  }
               } else if (!this$intAccrued.equals(other$intAccrued)) {
                  return false;
               }

               label142: {
                  Object this$writeOffIntAmt = this.getWriteOffIntAmt();
                  Object other$writeOffIntAmt = other.getWriteOffIntAmt();
                  if (this$writeOffIntAmt == null) {
                     if (other$writeOffIntAmt == null) {
                        break label142;
                     }
                  } else if (this$writeOffIntAmt.equals(other$writeOffIntAmt)) {
                     break label142;
                  }

                  return false;
               }

               label135: {
                  Object this$tranBranch = this.getTranBranch();
                  Object other$tranBranch = other.getTranBranch();
                  if (this$tranBranch == null) {
                     if (other$tranBranch == null) {
                        break label135;
                     }
                  } else if (this$tranBranch.equals(other$tranBranch)) {
                     break label135;
                  }

                  return false;
               }

               Object this$userId = this.getUserId();
               Object other$userId = other.getUserId();
               if (this$userId == null) {
                  if (other$userId != null) {
                     return false;
                  }
               } else if (!this$userId.equals(other$userId)) {
                  return false;
               }

               label121: {
                  Object this$authUserId = this.getAuthUserId();
                  Object other$authUserId = other.getAuthUserId();
                  if (this$authUserId == null) {
                     if (other$authUserId == null) {
                        break label121;
                     }
                  } else if (this$authUserId.equals(other$authUserId)) {
                     break label121;
                  }

                  return false;
               }

               Object this$company = this.getCompany();
               Object other$company = other.getCompany();
               if (this$company == null) {
                  if (other$company != null) {
                     return false;
                  }
               } else if (!this$company.equals(other$company)) {
                  return false;
               }

               label107: {
                  Object this$tranDate = this.getTranDate();
                  Object other$tranDate = other.getTranDate();
                  if (this$tranDate == null) {
                     if (other$tranDate == null) {
                        break label107;
                     }
                  } else if (this$tranDate.equals(other$tranDate)) {
                     break label107;
                  }

                  return false;
               }

               Object this$narrative = this.getNarrative();
               Object other$narrative = other.getNarrative();
               if (this$narrative == null) {
                  if (other$narrative != null) {
                     return false;
                  }
               } else if (!this$narrative.equals(other$narrative)) {
                  return false;
               }

               Object this$narrativeCode = this.getNarrativeCode();
               Object other$narrativeCode = other.getNarrativeCode();
               if (this$narrativeCode == null) {
                  if (other$narrativeCode != null) {
                     return false;
                  }
               } else if (!this$narrativeCode.equals(other$narrativeCode)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100002Out.IntArray;
      }
      public String toString() {
         return "Core1400100002Out.IntArray(feeIntNo=" + this.getFeeIntNo() + ", feeType=" + this.getFeeType() + ", intAmt=" + this.getIntAmt() + ", extTradeNo=" + this.getExtTradeNo() + ", startAccrualDate=" + this.getStartAccrualDate() + ", endAccrualDate=" + this.getEndAccrualDate() + ", intAccrued=" + this.getIntAccrued() + ", writeOffIntAmt=" + this.getWriteOffIntAmt() + ", tranBranch=" + this.getTranBranch() + ", userId=" + this.getUserId() + ", authUserId=" + this.getAuthUserId() + ", company=" + this.getCompany() + ", tranDate=" + this.getTranDate() + ", narrative=" + this.getNarrative() + ", narrativeCode=" + this.getNarrativeCode() + ")";
      }
   }
}
