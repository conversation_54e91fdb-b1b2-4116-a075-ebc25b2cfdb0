package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400100125Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100125Out.SignArray> signArray;
   @V(
      desc = "可用余额",
      notNull = false,
      length = "17",
      remark = "可用余额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal availableAmt;

   public List<Core1400100125Out.SignArray> getSignArray() {
      return this.signArray;
   }

   public BigDecimal getAvailableAmt() {
      return this.availableAmt;
   }

   public void setSignArray(List<Core1400100125Out.SignArray> signArray) {
      this.signArray = signArray;
   }

   public void setAvailableAmt(BigDecimal availableAmt) {
      this.availableAmt = availableAmt;
   }

   public String toString() {
      return "Core1400100125Out(signArray=" + this.getSignArray() + ", availableAmt=" + this.getAvailableAmt() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100125Out)) {
         return false;
      } else {
         Core1400100125Out other = (Core1400100125Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$signArray = this.getSignArray();
            Object other$signArray = other.getSignArray();
            if (this$signArray == null) {
               if (other$signArray != null) {
                  return false;
               }
            } else if (!this$signArray.equals(other$signArray)) {
               return false;
            }

            Object this$availableAmt = this.getAvailableAmt();
            Object other$availableAmt = other.getAvailableAmt();
            if (this$availableAmt == null) {
               if (other$availableAmt != null) {
                  return false;
               }
            } else if (!this$availableAmt.equals(other$availableAmt)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100125Out;
   }
   public static class SignArray {
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "账户名称",
         notNull = false,
         length = "200",
         remark = "账户名称，一般指中文账户名称",
         maxSize = 200
      )
      private String acctName;
      @V(
         desc = "协议类型",
         notNull = false,
         length = "10",
         remark = "协议类型",
         maxSize = 10
      )
      private String agreementType;
      @V(
         desc = "协议状态",
         notNull = false,
         length = "2",
         remark = "普通协议使用，可应用于大部分场景",
         maxSize = 2
      )
      private String agreementStatus;
      @V(
         desc = "转存起始日期",
         notNull = false,
         remark = "理财签约后的转存开始日期"
      )
      private String transferStartDate;
      @V(
         desc = "转存结束日期或终止日期",
         notNull = false,
         remark = "理财协议转存的结束日期"
      )
      private String transferEndDate;
      @V(
         desc = "签约产品类型",
         notNull = false,
         length = "20",
         remark = "签约产品类型",
         maxSize = 20
      )
      private String signProdType;
      @V(
         desc = "划转日",
         notNull = false,
         length = "2",
         remark = "划转日",
         maxSize = 2
      )
      private String transferDay;
      @V(
         desc = "协议留存金额",
         notNull = false,
         length = "17",
         remark = "协议留存金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal remainAmt;
      @V(
         desc = "理财固定金额",
         notNull = false,
         length = "17",
         remark = "理财固定金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal finFixedAmt;
      @V(
         desc = "最小起存金额",
         notNull = false,
         length = "17",
         remark = "最小起存金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal intMinAmt;
      @V(
         desc = "划转频率",
         notNull = false,
         length = "5",
         remark = "划转频率",
         maxSize = 5
      )
      private String transferFreq;
      @V(
         desc = "划转频率类型",
         notNull = false,
         length = "5",
         remark = "理财划转频率了行，Y年/M-月/D-日/Q-季/W-周",
         maxSize = 5
      )
      private String transferFreqType;
      @V(
         desc = "存期期限",
         notNull = false,
         length = "5",
         remark = "期限",
         maxSize = 5
      )
      private String term;
      @V(
         desc = "期限类型",
         notNull = false,
         length = "1",
         remark = "期限类型",
         maxSize = 1
      )
      private String termType;
      @V(
         desc = "定期账户币种",
         notNull = false,
         length = "3",
         remark = "定期账户币种",
         maxSize = 3
      )
      private String tdaAcctCcy;
      @V(
         desc = "定期账户名称",
         notNull = false,
         length = "200",
         remark = "定期账户名称",
         maxSize = 200
      )
      private String tdaAcctName;
      @V(
         desc = "定期账户产品类型",
         notNull = false,
         length = "20",
         remark = "定期账户产品类型",
         maxSize = 20
      )
      private String tdaAcctProdType;
      @V(
         desc = "定期账户序列号",
         notNull = false,
         length = "5",
         remark = "定期账户序列号",
         maxSize = 5
      )
      private String tdaAcctSeqNo;
      @V(
         desc = "定期账号",
         notNull = false,
         length = "50",
         remark = "定期账号",
         maxSize = 50
      )
      private String tdaBaseAcctNo;
      @V(
         desc = "是否自动转存",
         notNull = false,
         length = "10",
         remark = "定期是否自动转存",
         maxSize = 10
      )
      private String autoRenewRollover;
      @V(
         desc = "特色产品签约日期",
         notNull = false,
         remark = "特色产品签约日期"
      )
      private String specialSignDate;
      @V(
         desc = "签约机构",
         notNull = false,
         length = "50",
         remark = "签约机构",
         maxSize = 50
      )
      private String signBranch;
      @V(
         desc = "签约日期",
         notNull = false,
         remark = "签约日期"
      )
      private String signDate;
      @V(
         desc = "签约柜员",
         notNull = false,
         length = "30",
         remark = "签约柜员",
         maxSize = 30
      )
      private String signUserId;
      @V(
         desc = "解约机构",
         notNull = false,
         length = "50",
         remark = "解约机构",
         maxSize = 50
      )
      private String outSignBranch;
      @V(
         desc = "理财金额",
         notNull = false,
         length = "17",
         remark = "理财金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal financialAmount;
      @V(
         desc = "客户经理",
         notNull = false,
         length = "30",
         remark = "客户经理",
         maxSize = 30
      )
      private String acctExec;
      @V(
         desc = "客户经理姓名",
         notNull = false,
         length = "200",
         remark = "客户经理姓名",
         maxSize = 200
      )
      private String acctExecName;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "累计转存金额",
         notNull = false,
         length = "17",
         remark = "累计转存金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal transferTotalAmt;
      @V(
         desc = "下次划转日期",
         notNull = false,
         remark = "下次划转日期"
      )
      private String nextTransferDate;
      @V(
         desc = "上次划转日期",
         notNull = false,
         remark = "上次划转日期"
      )
      private String lastTransferDate;
      @V(
         desc = "支取金额",
         notNull = false,
         length = "17",
         remark = "支取金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal debtAmt;
      @V(
         desc = "解约操作日期",
         notNull = false,
         remark = "解约操作日期"
      )
      private String unsignOperateDate;
      @V(
         desc = "失效阈值",
         notNull = false,
         length = "30",
         remark = "失效阈值",
         maxSize = 30
      )
      private String invalidValue;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getAcctName() {
         return this.acctName;
      }

      public String getAgreementType() {
         return this.agreementType;
      }

      public String getAgreementStatus() {
         return this.agreementStatus;
      }

      public String getTransferStartDate() {
         return this.transferStartDate;
      }

      public String getTransferEndDate() {
         return this.transferEndDate;
      }

      public String getSignProdType() {
         return this.signProdType;
      }

      public String getTransferDay() {
         return this.transferDay;
      }

      public BigDecimal getRemainAmt() {
         return this.remainAmt;
      }

      public BigDecimal getFinFixedAmt() {
         return this.finFixedAmt;
      }

      public BigDecimal getIntMinAmt() {
         return this.intMinAmt;
      }

      public String getTransferFreq() {
         return this.transferFreq;
      }

      public String getTransferFreqType() {
         return this.transferFreqType;
      }

      public String getTerm() {
         return this.term;
      }

      public String getTermType() {
         return this.termType;
      }

      public String getTdaAcctCcy() {
         return this.tdaAcctCcy;
      }

      public String getTdaAcctName() {
         return this.tdaAcctName;
      }

      public String getTdaAcctProdType() {
         return this.tdaAcctProdType;
      }

      public String getTdaAcctSeqNo() {
         return this.tdaAcctSeqNo;
      }

      public String getTdaBaseAcctNo() {
         return this.tdaBaseAcctNo;
      }

      public String getAutoRenewRollover() {
         return this.autoRenewRollover;
      }

      public String getSpecialSignDate() {
         return this.specialSignDate;
      }

      public String getSignBranch() {
         return this.signBranch;
      }

      public String getSignDate() {
         return this.signDate;
      }

      public String getSignUserId() {
         return this.signUserId;
      }

      public String getOutSignBranch() {
         return this.outSignBranch;
      }

      public BigDecimal getFinancialAmount() {
         return this.financialAmount;
      }

      public String getAcctExec() {
         return this.acctExec;
      }

      public String getAcctExecName() {
         return this.acctExecName;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public BigDecimal getTransferTotalAmt() {
         return this.transferTotalAmt;
      }

      public String getNextTransferDate() {
         return this.nextTransferDate;
      }

      public String getLastTransferDate() {
         return this.lastTransferDate;
      }

      public BigDecimal getDebtAmt() {
         return this.debtAmt;
      }

      public String getUnsignOperateDate() {
         return this.unsignOperateDate;
      }

      public String getInvalidValue() {
         return this.invalidValue;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setAcctName(String acctName) {
         this.acctName = acctName;
      }

      public void setAgreementType(String agreementType) {
         this.agreementType = agreementType;
      }

      public void setAgreementStatus(String agreementStatus) {
         this.agreementStatus = agreementStatus;
      }

      public void setTransferStartDate(String transferStartDate) {
         this.transferStartDate = transferStartDate;
      }

      public void setTransferEndDate(String transferEndDate) {
         this.transferEndDate = transferEndDate;
      }

      public void setSignProdType(String signProdType) {
         this.signProdType = signProdType;
      }

      public void setTransferDay(String transferDay) {
         this.transferDay = transferDay;
      }

      public void setRemainAmt(BigDecimal remainAmt) {
         this.remainAmt = remainAmt;
      }

      public void setFinFixedAmt(BigDecimal finFixedAmt) {
         this.finFixedAmt = finFixedAmt;
      }

      public void setIntMinAmt(BigDecimal intMinAmt) {
         this.intMinAmt = intMinAmt;
      }

      public void setTransferFreq(String transferFreq) {
         this.transferFreq = transferFreq;
      }

      public void setTransferFreqType(String transferFreqType) {
         this.transferFreqType = transferFreqType;
      }

      public void setTerm(String term) {
         this.term = term;
      }

      public void setTermType(String termType) {
         this.termType = termType;
      }

      public void setTdaAcctCcy(String tdaAcctCcy) {
         this.tdaAcctCcy = tdaAcctCcy;
      }

      public void setTdaAcctName(String tdaAcctName) {
         this.tdaAcctName = tdaAcctName;
      }

      public void setTdaAcctProdType(String tdaAcctProdType) {
         this.tdaAcctProdType = tdaAcctProdType;
      }

      public void setTdaAcctSeqNo(String tdaAcctSeqNo) {
         this.tdaAcctSeqNo = tdaAcctSeqNo;
      }

      public void setTdaBaseAcctNo(String tdaBaseAcctNo) {
         this.tdaBaseAcctNo = tdaBaseAcctNo;
      }

      public void setAutoRenewRollover(String autoRenewRollover) {
         this.autoRenewRollover = autoRenewRollover;
      }

      public void setSpecialSignDate(String specialSignDate) {
         this.specialSignDate = specialSignDate;
      }

      public void setSignBranch(String signBranch) {
         this.signBranch = signBranch;
      }

      public void setSignDate(String signDate) {
         this.signDate = signDate;
      }

      public void setSignUserId(String signUserId) {
         this.signUserId = signUserId;
      }

      public void setOutSignBranch(String outSignBranch) {
         this.outSignBranch = outSignBranch;
      }

      public void setFinancialAmount(BigDecimal financialAmount) {
         this.financialAmount = financialAmount;
      }

      public void setAcctExec(String acctExec) {
         this.acctExec = acctExec;
      }

      public void setAcctExecName(String acctExecName) {
         this.acctExecName = acctExecName;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setTransferTotalAmt(BigDecimal transferTotalAmt) {
         this.transferTotalAmt = transferTotalAmt;
      }

      public void setNextTransferDate(String nextTransferDate) {
         this.nextTransferDate = nextTransferDate;
      }

      public void setLastTransferDate(String lastTransferDate) {
         this.lastTransferDate = lastTransferDate;
      }

      public void setDebtAmt(BigDecimal debtAmt) {
         this.debtAmt = debtAmt;
      }

      public void setUnsignOperateDate(String unsignOperateDate) {
         this.unsignOperateDate = unsignOperateDate;
      }

      public void setInvalidValue(String invalidValue) {
         this.invalidValue = invalidValue;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100125Out.SignArray)) {
            return false;
         } else {
            Core1400100125Out.SignArray other = (Core1400100125Out.SignArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label479: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label479;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label479;
                  }

                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               Object this$acctCcy = this.getAcctCcy();
               Object other$acctCcy = other.getAcctCcy();
               if (this$acctCcy == null) {
                  if (other$acctCcy != null) {
                     return false;
                  }
               } else if (!this$acctCcy.equals(other$acctCcy)) {
                  return false;
               }

               label458: {
                  Object this$acctSeqNo = this.getAcctSeqNo();
                  Object other$acctSeqNo = other.getAcctSeqNo();
                  if (this$acctSeqNo == null) {
                     if (other$acctSeqNo == null) {
                        break label458;
                     }
                  } else if (this$acctSeqNo.equals(other$acctSeqNo)) {
                     break label458;
                  }

                  return false;
               }

               label451: {
                  Object this$acctName = this.getAcctName();
                  Object other$acctName = other.getAcctName();
                  if (this$acctName == null) {
                     if (other$acctName == null) {
                        break label451;
                     }
                  } else if (this$acctName.equals(other$acctName)) {
                     break label451;
                  }

                  return false;
               }

               Object this$agreementType = this.getAgreementType();
               Object other$agreementType = other.getAgreementType();
               if (this$agreementType == null) {
                  if (other$agreementType != null) {
                     return false;
                  }
               } else if (!this$agreementType.equals(other$agreementType)) {
                  return false;
               }

               Object this$agreementStatus = this.getAgreementStatus();
               Object other$agreementStatus = other.getAgreementStatus();
               if (this$agreementStatus == null) {
                  if (other$agreementStatus != null) {
                     return false;
                  }
               } else if (!this$agreementStatus.equals(other$agreementStatus)) {
                  return false;
               }

               label430: {
                  Object this$transferStartDate = this.getTransferStartDate();
                  Object other$transferStartDate = other.getTransferStartDate();
                  if (this$transferStartDate == null) {
                     if (other$transferStartDate == null) {
                        break label430;
                     }
                  } else if (this$transferStartDate.equals(other$transferStartDate)) {
                     break label430;
                  }

                  return false;
               }

               label423: {
                  Object this$transferEndDate = this.getTransferEndDate();
                  Object other$transferEndDate = other.getTransferEndDate();
                  if (this$transferEndDate == null) {
                     if (other$transferEndDate == null) {
                        break label423;
                     }
                  } else if (this$transferEndDate.equals(other$transferEndDate)) {
                     break label423;
                  }

                  return false;
               }

               Object this$signProdType = this.getSignProdType();
               Object other$signProdType = other.getSignProdType();
               if (this$signProdType == null) {
                  if (other$signProdType != null) {
                     return false;
                  }
               } else if (!this$signProdType.equals(other$signProdType)) {
                  return false;
               }

               label409: {
                  Object this$transferDay = this.getTransferDay();
                  Object other$transferDay = other.getTransferDay();
                  if (this$transferDay == null) {
                     if (other$transferDay == null) {
                        break label409;
                     }
                  } else if (this$transferDay.equals(other$transferDay)) {
                     break label409;
                  }

                  return false;
               }

               Object this$remainAmt = this.getRemainAmt();
               Object other$remainAmt = other.getRemainAmt();
               if (this$remainAmt == null) {
                  if (other$remainAmt != null) {
                     return false;
                  }
               } else if (!this$remainAmt.equals(other$remainAmt)) {
                  return false;
               }

               label395: {
                  Object this$finFixedAmt = this.getFinFixedAmt();
                  Object other$finFixedAmt = other.getFinFixedAmt();
                  if (this$finFixedAmt == null) {
                     if (other$finFixedAmt == null) {
                        break label395;
                     }
                  } else if (this$finFixedAmt.equals(other$finFixedAmt)) {
                     break label395;
                  }

                  return false;
               }

               Object this$intMinAmt = this.getIntMinAmt();
               Object other$intMinAmt = other.getIntMinAmt();
               if (this$intMinAmt == null) {
                  if (other$intMinAmt != null) {
                     return false;
                  }
               } else if (!this$intMinAmt.equals(other$intMinAmt)) {
                  return false;
               }

               Object this$transferFreq = this.getTransferFreq();
               Object other$transferFreq = other.getTransferFreq();
               if (this$transferFreq == null) {
                  if (other$transferFreq != null) {
                     return false;
                  }
               } else if (!this$transferFreq.equals(other$transferFreq)) {
                  return false;
               }

               label374: {
                  Object this$transferFreqType = this.getTransferFreqType();
                  Object other$transferFreqType = other.getTransferFreqType();
                  if (this$transferFreqType == null) {
                     if (other$transferFreqType == null) {
                        break label374;
                     }
                  } else if (this$transferFreqType.equals(other$transferFreqType)) {
                     break label374;
                  }

                  return false;
               }

               label367: {
                  Object this$term = this.getTerm();
                  Object other$term = other.getTerm();
                  if (this$term == null) {
                     if (other$term == null) {
                        break label367;
                     }
                  } else if (this$term.equals(other$term)) {
                     break label367;
                  }

                  return false;
               }

               Object this$termType = this.getTermType();
               Object other$termType = other.getTermType();
               if (this$termType == null) {
                  if (other$termType != null) {
                     return false;
                  }
               } else if (!this$termType.equals(other$termType)) {
                  return false;
               }

               Object this$tdaAcctCcy = this.getTdaAcctCcy();
               Object other$tdaAcctCcy = other.getTdaAcctCcy();
               if (this$tdaAcctCcy == null) {
                  if (other$tdaAcctCcy != null) {
                     return false;
                  }
               } else if (!this$tdaAcctCcy.equals(other$tdaAcctCcy)) {
                  return false;
               }

               label346: {
                  Object this$tdaAcctName = this.getTdaAcctName();
                  Object other$tdaAcctName = other.getTdaAcctName();
                  if (this$tdaAcctName == null) {
                     if (other$tdaAcctName == null) {
                        break label346;
                     }
                  } else if (this$tdaAcctName.equals(other$tdaAcctName)) {
                     break label346;
                  }

                  return false;
               }

               label339: {
                  Object this$tdaAcctProdType = this.getTdaAcctProdType();
                  Object other$tdaAcctProdType = other.getTdaAcctProdType();
                  if (this$tdaAcctProdType == null) {
                     if (other$tdaAcctProdType == null) {
                        break label339;
                     }
                  } else if (this$tdaAcctProdType.equals(other$tdaAcctProdType)) {
                     break label339;
                  }

                  return false;
               }

               Object this$tdaAcctSeqNo = this.getTdaAcctSeqNo();
               Object other$tdaAcctSeqNo = other.getTdaAcctSeqNo();
               if (this$tdaAcctSeqNo == null) {
                  if (other$tdaAcctSeqNo != null) {
                     return false;
                  }
               } else if (!this$tdaAcctSeqNo.equals(other$tdaAcctSeqNo)) {
                  return false;
               }

               Object this$tdaBaseAcctNo = this.getTdaBaseAcctNo();
               Object other$tdaBaseAcctNo = other.getTdaBaseAcctNo();
               if (this$tdaBaseAcctNo == null) {
                  if (other$tdaBaseAcctNo != null) {
                     return false;
                  }
               } else if (!this$tdaBaseAcctNo.equals(other$tdaBaseAcctNo)) {
                  return false;
               }

               label318: {
                  Object this$autoRenewRollover = this.getAutoRenewRollover();
                  Object other$autoRenewRollover = other.getAutoRenewRollover();
                  if (this$autoRenewRollover == null) {
                     if (other$autoRenewRollover == null) {
                        break label318;
                     }
                  } else if (this$autoRenewRollover.equals(other$autoRenewRollover)) {
                     break label318;
                  }

                  return false;
               }

               label311: {
                  Object this$specialSignDate = this.getSpecialSignDate();
                  Object other$specialSignDate = other.getSpecialSignDate();
                  if (this$specialSignDate == null) {
                     if (other$specialSignDate == null) {
                        break label311;
                     }
                  } else if (this$specialSignDate.equals(other$specialSignDate)) {
                     break label311;
                  }

                  return false;
               }

               Object this$signBranch = this.getSignBranch();
               Object other$signBranch = other.getSignBranch();
               if (this$signBranch == null) {
                  if (other$signBranch != null) {
                     return false;
                  }
               } else if (!this$signBranch.equals(other$signBranch)) {
                  return false;
               }

               label297: {
                  Object this$signDate = this.getSignDate();
                  Object other$signDate = other.getSignDate();
                  if (this$signDate == null) {
                     if (other$signDate == null) {
                        break label297;
                     }
                  } else if (this$signDate.equals(other$signDate)) {
                     break label297;
                  }

                  return false;
               }

               Object this$signUserId = this.getSignUserId();
               Object other$signUserId = other.getSignUserId();
               if (this$signUserId == null) {
                  if (other$signUserId != null) {
                     return false;
                  }
               } else if (!this$signUserId.equals(other$signUserId)) {
                  return false;
               }

               label283: {
                  Object this$outSignBranch = this.getOutSignBranch();
                  Object other$outSignBranch = other.getOutSignBranch();
                  if (this$outSignBranch == null) {
                     if (other$outSignBranch == null) {
                        break label283;
                     }
                  } else if (this$outSignBranch.equals(other$outSignBranch)) {
                     break label283;
                  }

                  return false;
               }

               Object this$financialAmount = this.getFinancialAmount();
               Object other$financialAmount = other.getFinancialAmount();
               if (this$financialAmount == null) {
                  if (other$financialAmount != null) {
                     return false;
                  }
               } else if (!this$financialAmount.equals(other$financialAmount)) {
                  return false;
               }

               Object this$acctExec = this.getAcctExec();
               Object other$acctExec = other.getAcctExec();
               if (this$acctExec == null) {
                  if (other$acctExec != null) {
                     return false;
                  }
               } else if (!this$acctExec.equals(other$acctExec)) {
                  return false;
               }

               label262: {
                  Object this$acctExecName = this.getAcctExecName();
                  Object other$acctExecName = other.getAcctExecName();
                  if (this$acctExecName == null) {
                     if (other$acctExecName == null) {
                        break label262;
                     }
                  } else if (this$acctExecName.equals(other$acctExecName)) {
                     break label262;
                  }

                  return false;
               }

               label255: {
                  Object this$clientNo = this.getClientNo();
                  Object other$clientNo = other.getClientNo();
                  if (this$clientNo == null) {
                     if (other$clientNo == null) {
                        break label255;
                     }
                  } else if (this$clientNo.equals(other$clientNo)) {
                     break label255;
                  }

                  return false;
               }

               Object this$transferTotalAmt = this.getTransferTotalAmt();
               Object other$transferTotalAmt = other.getTransferTotalAmt();
               if (this$transferTotalAmt == null) {
                  if (other$transferTotalAmt != null) {
                     return false;
                  }
               } else if (!this$transferTotalAmt.equals(other$transferTotalAmt)) {
                  return false;
               }

               Object this$nextTransferDate = this.getNextTransferDate();
               Object other$nextTransferDate = other.getNextTransferDate();
               if (this$nextTransferDate == null) {
                  if (other$nextTransferDate != null) {
                     return false;
                  }
               } else if (!this$nextTransferDate.equals(other$nextTransferDate)) {
                  return false;
               }

               label234: {
                  Object this$lastTransferDate = this.getLastTransferDate();
                  Object other$lastTransferDate = other.getLastTransferDate();
                  if (this$lastTransferDate == null) {
                     if (other$lastTransferDate == null) {
                        break label234;
                     }
                  } else if (this$lastTransferDate.equals(other$lastTransferDate)) {
                     break label234;
                  }

                  return false;
               }

               label227: {
                  Object this$debtAmt = this.getDebtAmt();
                  Object other$debtAmt = other.getDebtAmt();
                  if (this$debtAmt == null) {
                     if (other$debtAmt == null) {
                        break label227;
                     }
                  } else if (this$debtAmt.equals(other$debtAmt)) {
                     break label227;
                  }

                  return false;
               }

               Object this$unsignOperateDate = this.getUnsignOperateDate();
               Object other$unsignOperateDate = other.getUnsignOperateDate();
               if (this$unsignOperateDate == null) {
                  if (other$unsignOperateDate != null) {
                     return false;
                  }
               } else if (!this$unsignOperateDate.equals(other$unsignOperateDate)) {
                  return false;
               }

               Object this$invalidValue = this.getInvalidValue();
               Object other$invalidValue = other.getInvalidValue();
               if (this$invalidValue == null) {
                  if (other$invalidValue != null) {
                     return false;
                  }
               } else if (!this$invalidValue.equals(other$invalidValue)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100125Out.SignArray;
      }
      public String toString() {
         return "Core1400100125Out.SignArray(baseAcctNo=" + this.getBaseAcctNo() + ", prodType=" + this.getProdType() + ", acctCcy=" + this.getAcctCcy() + ", acctSeqNo=" + this.getAcctSeqNo() + ", acctName=" + this.getAcctName() + ", agreementType=" + this.getAgreementType() + ", agreementStatus=" + this.getAgreementStatus() + ", transferStartDate=" + this.getTransferStartDate() + ", transferEndDate=" + this.getTransferEndDate() + ", signProdType=" + this.getSignProdType() + ", transferDay=" + this.getTransferDay() + ", remainAmt=" + this.getRemainAmt() + ", finFixedAmt=" + this.getFinFixedAmt() + ", intMinAmt=" + this.getIntMinAmt() + ", transferFreq=" + this.getTransferFreq() + ", transferFreqType=" + this.getTransferFreqType() + ", term=" + this.getTerm() + ", termType=" + this.getTermType() + ", tdaAcctCcy=" + this.getTdaAcctCcy() + ", tdaAcctName=" + this.getTdaAcctName() + ", tdaAcctProdType=" + this.getTdaAcctProdType() + ", tdaAcctSeqNo=" + this.getTdaAcctSeqNo() + ", tdaBaseAcctNo=" + this.getTdaBaseAcctNo() + ", autoRenewRollover=" + this.getAutoRenewRollover() + ", specialSignDate=" + this.getSpecialSignDate() + ", signBranch=" + this.getSignBranch() + ", signDate=" + this.getSignDate() + ", signUserId=" + this.getSignUserId() + ", outSignBranch=" + this.getOutSignBranch() + ", financialAmount=" + this.getFinancialAmount() + ", acctExec=" + this.getAcctExec() + ", acctExecName=" + this.getAcctExecName() + ", clientNo=" + this.getClientNo() + ", transferTotalAmt=" + this.getTransferTotalAmt() + ", nextTransferDate=" + this.getNextTransferDate() + ", lastTransferDate=" + this.getLastTransferDate() + ", debtAmt=" + this.getDebtAmt() + ", unsignOperateDate=" + this.getUnsignOperateDate() + ", invalidValue=" + this.getInvalidValue() + ")";
      }
   }
}
