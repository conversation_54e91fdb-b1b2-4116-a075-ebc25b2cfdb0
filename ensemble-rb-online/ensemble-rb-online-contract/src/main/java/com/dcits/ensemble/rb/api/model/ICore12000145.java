package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000145In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000145Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12000145 {
   String URL = "/rb/nfin/acct/verify";


   @ApiDesc("本功能用于对账户进行核实操作，可以按账号核实，支持柜面、PAD、STM、手机银行、个人网银渠道发起的核实。")
   @EnsembleElements(
      serviceCode = "Core",
      messageType = "1200",
      messageCode = "0145"
   )
   @FunctionCategory("RB21-风险管理")
   @ConsumeSys("CBS")
   @ApiUseStatus("PRODUCT-产品")
   Core12000145Out runService(Core12000145In var1);
}
