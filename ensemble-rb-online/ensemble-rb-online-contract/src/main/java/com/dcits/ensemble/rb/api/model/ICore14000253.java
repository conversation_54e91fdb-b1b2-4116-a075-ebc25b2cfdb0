package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000253In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000253Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000253 {
   String URL = "/rb/inq/dc/stageSale";


   @ApiDesc("大额存单期次销售情况查询")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0253"
   )
   @FunctionCategory("RB10-大额存单")
   @ConsumeSys("TLE")
   Core14000253Out runService(Core14000253In var1);
}
