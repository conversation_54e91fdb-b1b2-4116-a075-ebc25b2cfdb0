package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400100126Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "支取金额",
      notNull = false,
      length = "17",
      remark = "支取金额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal debtAmt;
   @V(
      desc = "总金额",
      notNull = false,
      length = "17",
      remark = "总金额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal totalAmt;
   @V(
      desc = "账户条数",
      notNull = false,
      length = "5",
      remark = "账户条数"
   )
   private Integer acctCount;
   @V(
      desc = "成功总笔数",
      notNull = false,
      length = "5",
      remark = "成功总笔数"
   )
   private Integer succCount;
   @V(
      desc = "交易参考号",
      notNull = false,
      length = "50",
      remark = "交易参考号",
      maxSize = 50
   )
   private String reference;
   @V(
      desc = "交易日期",
      notNull = false,
      remark = "交易日期"
   )
   private String tranDate;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100126Out.SubAcctArray> subAcctArray;
   @V(
      desc = "运行状态",
      notNull = false,
      length = "3",
      remark = "运行状态",
      maxSize = 3
   )
   private String jobStatus;
   @V(
      desc = "利息金额",
      notNull = false,
      length = "17",
      remark = "利息金额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal intAmt;
   @V(
      desc = "利息税",
      notNull = false,
      length = "17",
      remark = "利息税",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal tax;

   public BigDecimal getDebtAmt() {
      return this.debtAmt;
   }

   public BigDecimal getTotalAmt() {
      return this.totalAmt;
   }

   public Integer getAcctCount() {
      return this.acctCount;
   }

   public Integer getSuccCount() {
      return this.succCount;
   }

   public String getReference() {
      return this.reference;
   }

   public String getTranDate() {
      return this.tranDate;
   }

   public List<Core1400100126Out.SubAcctArray> getSubAcctArray() {
      return this.subAcctArray;
   }

   public String getJobStatus() {
      return this.jobStatus;
   }

   public BigDecimal getIntAmt() {
      return this.intAmt;
   }

   public BigDecimal getTax() {
      return this.tax;
   }

   public void setDebtAmt(BigDecimal debtAmt) {
      this.debtAmt = debtAmt;
   }

   public void setTotalAmt(BigDecimal totalAmt) {
      this.totalAmt = totalAmt;
   }

   public void setAcctCount(Integer acctCount) {
      this.acctCount = acctCount;
   }

   public void setSuccCount(Integer succCount) {
      this.succCount = succCount;
   }

   public void setReference(String reference) {
      this.reference = reference;
   }

   public void setTranDate(String tranDate) {
      this.tranDate = tranDate;
   }

   public void setSubAcctArray(List<Core1400100126Out.SubAcctArray> subAcctArray) {
      this.subAcctArray = subAcctArray;
   }

   public void setJobStatus(String jobStatus) {
      this.jobStatus = jobStatus;
   }

   public void setIntAmt(BigDecimal intAmt) {
      this.intAmt = intAmt;
   }

   public void setTax(BigDecimal tax) {
      this.tax = tax;
   }

   public String toString() {
      return "Core1400100126Out(debtAmt=" + this.getDebtAmt() + ", totalAmt=" + this.getTotalAmt() + ", acctCount=" + this.getAcctCount() + ", succCount=" + this.getSuccCount() + ", reference=" + this.getReference() + ", tranDate=" + this.getTranDate() + ", subAcctArray=" + this.getSubAcctArray() + ", jobStatus=" + this.getJobStatus() + ", intAmt=" + this.getIntAmt() + ", tax=" + this.getTax() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100126Out)) {
         return false;
      } else {
         Core1400100126Out other = (Core1400100126Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$debtAmt = this.getDebtAmt();
            Object other$debtAmt = other.getDebtAmt();
            if (this$debtAmt == null) {
               if (other$debtAmt != null) {
                  return false;
               }
            } else if (!this$debtAmt.equals(other$debtAmt)) {
               return false;
            }

            Object this$totalAmt = this.getTotalAmt();
            Object other$totalAmt = other.getTotalAmt();
            if (this$totalAmt == null) {
               if (other$totalAmt != null) {
                  return false;
               }
            } else if (!this$totalAmt.equals(other$totalAmt)) {
               return false;
            }

            label119: {
               Object this$acctCount = this.getAcctCount();
               Object other$acctCount = other.getAcctCount();
               if (this$acctCount == null) {
                  if (other$acctCount == null) {
                     break label119;
                  }
               } else if (this$acctCount.equals(other$acctCount)) {
                  break label119;
               }

               return false;
            }

            label112: {
               Object this$succCount = this.getSuccCount();
               Object other$succCount = other.getSuccCount();
               if (this$succCount == null) {
                  if (other$succCount == null) {
                     break label112;
                  }
               } else if (this$succCount.equals(other$succCount)) {
                  break label112;
               }

               return false;
            }

            Object this$reference = this.getReference();
            Object other$reference = other.getReference();
            if (this$reference == null) {
               if (other$reference != null) {
                  return false;
               }
            } else if (!this$reference.equals(other$reference)) {
               return false;
            }

            Object this$tranDate = this.getTranDate();
            Object other$tranDate = other.getTranDate();
            if (this$tranDate == null) {
               if (other$tranDate != null) {
                  return false;
               }
            } else if (!this$tranDate.equals(other$tranDate)) {
               return false;
            }

            label91: {
               Object this$subAcctArray = this.getSubAcctArray();
               Object other$subAcctArray = other.getSubAcctArray();
               if (this$subAcctArray == null) {
                  if (other$subAcctArray == null) {
                     break label91;
                  }
               } else if (this$subAcctArray.equals(other$subAcctArray)) {
                  break label91;
               }

               return false;
            }

            Object this$jobStatus = this.getJobStatus();
            Object other$jobStatus = other.getJobStatus();
            if (this$jobStatus == null) {
               if (other$jobStatus != null) {
                  return false;
               }
            } else if (!this$jobStatus.equals(other$jobStatus)) {
               return false;
            }

            Object this$intAmt = this.getIntAmt();
            Object other$intAmt = other.getIntAmt();
            if (this$intAmt == null) {
               if (other$intAmt != null) {
                  return false;
               }
            } else if (!this$intAmt.equals(other$intAmt)) {
               return false;
            }

            Object this$tax = this.getTax();
            Object other$tax = other.getTax();
            if (this$tax == null) {
               if (other$tax != null) {
                  return false;
               }
            } else if (!this$tax.equals(other$tax)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100126Out;
   }
   public static class SubAcctArray {
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "支取金额",
         notNull = false,
         length = "17",
         remark = "支取金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal debtAmt;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public BigDecimal getDebtAmt() {
         return this.debtAmt;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setDebtAmt(BigDecimal debtAmt) {
         this.debtAmt = debtAmt;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100126Out.SubAcctArray)) {
            return false;
         } else {
            Core1400100126Out.SubAcctArray other = (Core1400100126Out.SubAcctArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label71: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label71;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label71;
                  }

                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               label57: {
                  Object this$acctCcy = this.getAcctCcy();
                  Object other$acctCcy = other.getAcctCcy();
                  if (this$acctCcy == null) {
                     if (other$acctCcy == null) {
                        break label57;
                     }
                  } else if (this$acctCcy.equals(other$acctCcy)) {
                     break label57;
                  }

                  return false;
               }

               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo != null) {
                     return false;
                  }
               } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                  return false;
               }

               Object this$debtAmt = this.getDebtAmt();
               Object other$debtAmt = other.getDebtAmt();
               if (this$debtAmt == null) {
                  if (other$debtAmt == null) {
                     return true;
                  }
               } else if (this$debtAmt.equals(other$debtAmt)) {
                  return true;
               }

               return false;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100126Out.SubAcctArray;
      }
      public String toString() {
         return "Core1400100126Out.SubAcctArray(baseAcctNo=" + this.getBaseAcctNo() + ", prodType=" + this.getProdType() + ", acctCcy=" + this.getAcctCcy() + ", acctSeqNo=" + this.getAcctSeqNo() + ", debtAmt=" + this.getDebtAmt() + ")";
      }
   }
}
