package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;

@MessageOut
public class Core1400100242Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "客户号",
      notNull = false,
      length = "20",
      remark = "客户号",
      maxSize = 20
   )
   private String clientNo;
   @V(
      desc = "预约到期日",
      notNull = false,
      remark = "预约到期日"
   )
   private String applyDueDate;
   @V(
      desc = "赎回日期",
      notNull = false,
      remark = "赎回日期"
   )
   private String tohonorDate;
   @V(
      desc = "赎回日期",
      notNull = false,
      remark = "赎回日期"
   )
   private String redeemDate;
   @V(
      desc = "预约赎回时间",
      notNull = false,
      remark = "预约赎回时间"
   )
   private String applyTohonorDate;

   public String getClientNo() {
      return this.clientNo;
   }

   public String getApplyDueDate() {
      return this.applyDueDate;
   }

   public String getTohonorDate() {
      return this.tohonorDate;
   }

   public String getRedeemDate() {
      return this.redeemDate;
   }

   public String getApplyTohonorDate() {
      return this.applyTohonorDate;
   }

   public void setClientNo(String clientNo) {
      this.clientNo = clientNo;
   }

   public void setApplyDueDate(String applyDueDate) {
      this.applyDueDate = applyDueDate;
   }

   public void setTohonorDate(String tohonorDate) {
      this.tohonorDate = tohonorDate;
   }

   public void setRedeemDate(String redeemDate) {
      this.redeemDate = redeemDate;
   }

   public void setApplyTohonorDate(String applyTohonorDate) {
      this.applyTohonorDate = applyTohonorDate;
   }

   public String toString() {
      return "Core1400100242Out(clientNo=" + this.getClientNo() + ", applyDueDate=" + this.getApplyDueDate() + ", tohonorDate=" + this.getTohonorDate() + ", redeemDate=" + this.getRedeemDate() + ", applyTohonorDate=" + this.getApplyTohonorDate() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100242Out)) {
         return false;
      } else {
         Core1400100242Out other = (Core1400100242Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            label73: {
               Object this$clientNo = this.getClientNo();
               Object other$clientNo = other.getClientNo();
               if (this$clientNo == null) {
                  if (other$clientNo == null) {
                     break label73;
                  }
               } else if (this$clientNo.equals(other$clientNo)) {
                  break label73;
               }

               return false;
            }

            Object this$applyDueDate = this.getApplyDueDate();
            Object other$applyDueDate = other.getApplyDueDate();
            if (this$applyDueDate == null) {
               if (other$applyDueDate != null) {
                  return false;
               }
            } else if (!this$applyDueDate.equals(other$applyDueDate)) {
               return false;
            }

            label59: {
               Object this$tohonorDate = this.getTohonorDate();
               Object other$tohonorDate = other.getTohonorDate();
               if (this$tohonorDate == null) {
                  if (other$tohonorDate == null) {
                     break label59;
                  }
               } else if (this$tohonorDate.equals(other$tohonorDate)) {
                  break label59;
               }

               return false;
            }

            Object this$redeemDate = this.getRedeemDate();
            Object other$redeemDate = other.getRedeemDate();
            if (this$redeemDate == null) {
               if (other$redeemDate != null) {
                  return false;
               }
            } else if (!this$redeemDate.equals(other$redeemDate)) {
               return false;
            }

            Object this$applyTohonorDate = this.getApplyTohonorDate();
            Object other$applyTohonorDate = other.getApplyTohonorDate();
            if (this$applyTohonorDate == null) {
               if (other$applyTohonorDate != null) {
                  return false;
               }
            } else if (!this$applyTohonorDate.equals(other$applyTohonorDate)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100242Out;
   }
}
