package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000402In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000402Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12000402 {
   String URL = "/rb/nfin/aio/uopen";


   @ApiRemark("深度优化")
   @ApiDesc("该接口支持将普通的对公结算账户结构调整升级为AIO账户结构。仅支持对公结算账户，不支持对私。使用场景是对于单位账户因为用途变更，又不想再开立账户的时候，将原有账户结构调整为母虚子实的账户结构，原结算户变为AIO下的活期子账户")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0402"
   )
   @BusinessCategory("1200-非金融")
   @FunctionCategory("RB02-账户管理")
   @ConsumeSys("COS/TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core12000402Out runService(Core12000402In var1);
}
