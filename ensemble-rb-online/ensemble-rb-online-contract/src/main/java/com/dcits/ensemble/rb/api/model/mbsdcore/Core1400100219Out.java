package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400100219Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100219Out.StageCodeInfoArray> stageCodeInfoArray;

   public List<Core1400100219Out.StageCodeInfoArray> getStageCodeInfoArray() {
      return this.stageCodeInfoArray;
   }

   public void setStageCodeInfoArray(List<Core1400100219Out.StageCodeInfoArray> stageCodeInfoArray) {
      this.stageCodeInfoArray = stageCodeInfoArray;
   }

   public String toString() {
      return "Core1400100219Out(stageCodeInfoArray=" + this.getStageCodeInfoArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100219Out)) {
         return false;
      } else {
         Core1400100219Out other = (Core1400100219Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$stageCodeInfoArray = this.getStageCodeInfoArray();
            Object other$stageCodeInfoArray = other.getStageCodeInfoArray();
            if (this$stageCodeInfoArray == null) {
               if (other$stageCodeInfoArray != null) {
                  return false;
               }
            } else if (!this$stageCodeInfoArray.equals(other$stageCodeInfoArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100219Out;
   }
   public static class StageCodeInfoArray {
      @V(
         desc = "期次代码",
         notNull = false,
         length = "50",
         remark = "期次代码",
         maxSize = 50
      )
      private String stageCode;
      @V(
         desc = "期次描述",
         notNull = false,
         length = "200",
         remark = "期次描述",
         maxSize = 200
      )
      private String stageCodeDesc;
      @V(
         desc = "发行年度",
         notNull = false,
         length = "5",
         remark = "发行年度",
         maxSize = 5
      )
      private String issueYear;
      @V(
         desc = "客户类型",
         notNull = false,
         length = "3",
         remark = "客户大类，目前一般分为个人，公司，金融机构和内部客户。取之于CIF_CLIENT_TYPE.CLIENT_TYPE",
         maxSize = 3
      )
      private String clientType;
      @V(
         desc = "所属机构号",
         notNull = false,
         length = "50",
         remark = "机构代码",
         maxSize = 50
      )
      private String branch;
      @V(
         desc = "期次发行金额",
         notNull = false,
         length = "17",
         remark = "期次发行金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal issueAmt;
      @V(
         desc = "开始日期",
         notNull = false,
         remark = "开始日期"
      )
      private String startDate;
      @V(
         desc = "结束日期",
         notNull = false,
         remark = "结束日期"
      )
      private String endDate;
      @V(
         desc = "起售时间",
         notNull = false,
         length = "26",
         remark = "起售时间",
         maxSize = 26
      )
      private String saleStartTime;
      @V(
         desc = "止售时间",
         notNull = false,
         length = "26",
         remark = "止售时间",
         maxSize = 26
      )
      private String saleEndTime;
      @V(
         desc = "期次起存金额",
         notNull = false,
         length = "17",
         remark = "期次起存金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal stageMinAmt;
      @V(
         desc = "最小留存金额",
         notNull = false,
         length = "17",
         remark = "最小留存金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal keepMinBal;
      @V(
         desc = "存期期限",
         notNull = false,
         length = "5",
         remark = "期限",
         maxSize = 5
      )
      private String term;
      @V(
         desc = "利率类型",
         notNull = false,
         length = "5",
         remark = "利率类型",
         maxSize = 5
      )
      private String intType;
      @V(
         desc = "付息方式",
         notNull = false,
         length = "3",
         remark = "付息方式",
         maxSize = 3
      )
      private String payIntType;
      @V(
         desc = "是否允许提前支取",
         notNull = false,
         length = "1",
         remark = "是否允许提前支取",
         maxSize = 1
      )
      private String preWithdrawFlag;
      @V(
         desc = "提前支取次数",
         notNull = false,
         length = "5",
         remark = "提前支取次数"
      )
      private Integer preWithdrawNum;
      @V(
         desc = "是否全额转让",
         notNull = false,
         length = "1",
         remark = "定期转让是否全额转让 Y -是 N -否",
         maxSize = 1
      )
      private String isFullTransfer;
      @V(
         desc = "是否可赎回",
         notNull = false,
         length = "1",
         remark = "是否可赎回",
         maxSize = 1
      )
      private String redemptionFlag;
      @V(
         desc = "赎回利率",
         notNull = false,
         length = "15",
         remark = "赎回利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal tohonorRate;
      @V(
         desc = "赎回日期",
         notNull = false,
         remark = "赎回日期"
      )
      private String tohonorDate;
      @V(
         desc = "浮动利率",
         notNull = false,
         length = "15",
         remark = "浮动利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal floatRate;
      @V(
         desc = "电子邮件",
         notNull = false,
         length = "200",
         remark = "电子邮件",
         maxSize = 200
      )
      private String email;
      @V(
         desc = "转出费用",
         notNull = false,
         length = "17",
         remark = "转出费用",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal trfOutFeeAmt;
      @V(
         desc = "转入费用类型",
         notNull = false,
         length = "20",
         remark = "转入费用类型",
         maxSize = 20
      )
      private String trfInFeeType;
      @V(
         desc = "转出费用类型",
         notNull = false,
         length = "20",
         remark = "转出费用类型",
         maxSize = 20
      )
      private String trfOutFeeType;
      @V(
         desc = "执行利率",
         notNull = false,
         length = "15",
         remark = "执行利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal realRate;
      @V(
         desc = "赎回利率类型",
         notNull = false,
         length = "5",
         remark = "赎回利率类型",
         maxSize = 5
      )
      private String redemptionIntType;
      @V(
         desc = "转入手续费",
         notNull = false,
         length = "17",
         remark = "转入手续费",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal inFee;
      @V(
         desc = "发行方式",
         notNull = false,
         length = "20",
         remark = "发行方式",
         maxSize = 20
      )
      private String issueMethod;
      @V(
         desc = "资金来源是否基于内部户",
         notNull = false,
         length = "2",
         remark = "资金来源是否基于内部户",
         maxSize = 2
      )
      private String isCashFromInnerAcct;
      @V(
         desc = "最小变动额",
         notNull = false,
         length = "17",
         remark = "最小变动额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal minChangeBalance;
      @V(
         desc = "起息日",
         notNull = false,
         remark = "起息日"
      )
      private String intStartDate;
      @V(
         desc = "到期日",
         notNull = false,
         remark = "到期日"
      )
      private String matureDate;
      @V(
         desc = "境内境外标志",
         notNull = false,
         length = "1",
         remark = "境内境外标志",
         maxSize = 1
      )
      private String inlandOffshore;
      @V(
         desc = "账户类型",
         notNull = false,
         length = "1",
         remark = "账户类型",
         maxSize = 1
      )
      private String acctType;
      @V(
         desc = "期限类型",
         notNull = false,
         length = "1",
         remark = "期限类型",
         maxSize = 1
      )
      private String termType;
      @V(
         desc = "数组",
         notNull = false,
         remark = "数组"
      )
      private List<Core1400100219Out.StageCodeInfoArray.ChannelArray> channelArray;
      @V(
         desc = "数组",
         notNull = false,
         remark = "数组"
      )
      private List<Core1400100219Out.StageCodeInfoArray.ClientListArray> clientListArray;
      @V(
         desc = "起息标识",
         notNull = false,
         length = "1",
         remark = "起息标识",
         maxSize = 1
      )
      private String intStartFlag;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "剩余额度",
         notNull = false,
         length = "17",
         remark = "剩余额度",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal leaveQuota;
      @V(
         desc = "单笔认购最大金额",
         notNull = false,
         length = "17",
         remark = "单笔认购最大金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal sgMaxAmt;
      @V(
         desc = "结息频率",
         notNull = false,
         length = "5",
         remark = "结息频率",
         maxSize = 5
      )
      private String cycleFreq;
      @V(
         desc = "是否指定收息",
         notNull = false,
         length = "1",
         remark = "是否指定收息",
         maxSize = 1
      )
      private String directionChargeIntFlag;
      @V(
         desc = "期次详细备注",
         notNull = false,
         length = "200",
         remark = "期次详细备注",
         maxSize = 200
      )
      private String stageRemark;
      @V(
         desc = "冷静期",
         notNull = false,
         length = "5",
         remark = "大额存单兑付冷静期"
      )
      private Integer calmDays;
      @V(
         desc = "客户群体 | 支持大类多选，大类下面选小类",
         notNull = false,
         remark = "客户群体 | 支持大类多选，大类下面选小类"
      )
      private List<Core1400100219Out.StageCodeInfoArray.CustomerBaseClass> customerBaseClass;
      @V(
         desc = "允许购买的机构",
         notNull = false,
         length = "20",
         remark = "允许购买的机构",
         maxSize = 20
      )
      private String allowBranch;
      @V(
         desc = "允许购买的账户分类",
         notNull = false,
         length = "1",
         remark = "允许购买的账户分类 | 1-一类户，2-二类户，3-不限制",
         maxSize = 1
      )
      private String allowAcctClass;
      @V(
         desc = "转让截止日 | 正整数的数字",
         notNull = false,
         length = "5",
         remark = "转让截止日 | 正整数的数字",
         maxSize = 5
      )
      private String trfThruDate;
      @V(
         desc = "转让挂单截止日",
         notNull = false,
         length = "5",
         remark = "转让挂单截止日 | 正整数的数字",
         maxSize = 5
      )
      private String trfOrderThruDate;
      @V(
         desc = "客户群体",
         notNull = false,
         length = "20",
         remark = "客户群体",
         maxSize = 20
      )
      private String customerBase;
      @V(
         desc = "新旧资金标识  | Y-新资金 N-不区分",
         notNull = false,
         length = "1",
         remark = "新旧资金标识  | Y-新资金 N-不区分",
         maxSize = 1
      )
      private String newAndOldFundFlag;
      @V(
         desc = "客户群购买最大金额(包含：客户群体 、最高购买上限)",
         notNull = false,
         remark = "客户群购买最大金额(包含：客户群体 、最高购买上限)。一个客户群体只允许设置一条最高购买上限。"
      )
      private List<Core1400100219Out.StageCodeInfoArray.CustomerBaseArrays> customerBaseArrays;
      @V(
         desc = "英文期次描述",
         notNull = false,
         length = "200",
         remark = "英文期次描述",
         maxSize = 200
      )
      private String stageCodeDescEn;
      @V(
         desc = "取息频率",
         notNull = false,
         length = "5",
         remark = "取息频率",
         maxSize = 5
      )
      private String getIntFreq;
      @V(
         desc = "固定利率",
         notNull = false,
         length = "15",
         remark = "固定利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal fixedRate;
      @V(
         desc = "配售方式",
         notNull = false,
         length = "1",
         remark = "配售方式",
         maxSize = 1
      )
      private String rationType;
      @V(
         desc = "支持组合购买方式",
         notNull = false,
         length = "1",
         remark = "1-单独购买2-组合购买3-单买与组合买",
         maxSize = 1
      )
      private String allowBuyWayCd;
      @V(
         desc = "总额度",
         notNull = false,
         length = "17",
         remark = "总额度",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal totalQuota;
      @V(
         desc = "第二语言期次描述",
         notNull = false,
         length = "50",
         remark = "第二语言期次描述",
         maxSize = 50
      )
      private String stageCodeDescSecond;
      @V(
         desc = "占用额度",
         notNull = false,
         length = "17",
         remark = "占用额度",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal holdingQuota;
      @V(
         desc = "转让标志",
         notNull = false,
         length = "1",
         remark = "转让标志",
         maxSize = 1
      )
      private String trfFlag;
      @V(
         desc = "单次最小支取金额",
         notNull = false,
         length = "17",
         remark = "单次最小支取金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal sgMinAmt;
      @V(
         desc = "是否白名单发售",
         notNull = false,
         length = "1",
         remark = "是否白名单发售Y-是，N-否",
         maxSize = 1
      )
      private String whiteSellFlag;
      @V(
         desc = "第二语言期次详细备注",
         notNull = false,
         length = "200",
         remark = "第二语言期次详细备注",
         maxSize = 200
      )
      private String stageRemarkSecond;
      @V(
         desc = "期次状态",
         notNull = false,
         length = "2",
         remark = "期次状态",
         maxSize = 2
      )
      private String stageStatus;

      public String getStageCode() {
         return this.stageCode;
      }

      public String getStageCodeDesc() {
         return this.stageCodeDesc;
      }

      public String getIssueYear() {
         return this.issueYear;
      }

      public String getClientType() {
         return this.clientType;
      }

      public String getBranch() {
         return this.branch;
      }

      public BigDecimal getIssueAmt() {
         return this.issueAmt;
      }

      public String getStartDate() {
         return this.startDate;
      }

      public String getEndDate() {
         return this.endDate;
      }

      public String getSaleStartTime() {
         return this.saleStartTime;
      }

      public String getSaleEndTime() {
         return this.saleEndTime;
      }

      public BigDecimal getStageMinAmt() {
         return this.stageMinAmt;
      }

      public BigDecimal getKeepMinBal() {
         return this.keepMinBal;
      }

      public String getTerm() {
         return this.term;
      }

      public String getIntType() {
         return this.intType;
      }

      public String getPayIntType() {
         return this.payIntType;
      }

      public String getPreWithdrawFlag() {
         return this.preWithdrawFlag;
      }

      public Integer getPreWithdrawNum() {
         return this.preWithdrawNum;
      }

      public String getIsFullTransfer() {
         return this.isFullTransfer;
      }

      public String getRedemptionFlag() {
         return this.redemptionFlag;
      }

      public BigDecimal getTohonorRate() {
         return this.tohonorRate;
      }

      public String getTohonorDate() {
         return this.tohonorDate;
      }

      public BigDecimal getFloatRate() {
         return this.floatRate;
      }

      public String getEmail() {
         return this.email;
      }

      public BigDecimal getTrfOutFeeAmt() {
         return this.trfOutFeeAmt;
      }

      public String getTrfInFeeType() {
         return this.trfInFeeType;
      }

      public String getTrfOutFeeType() {
         return this.trfOutFeeType;
      }

      public BigDecimal getRealRate() {
         return this.realRate;
      }

      public String getRedemptionIntType() {
         return this.redemptionIntType;
      }

      public BigDecimal getInFee() {
         return this.inFee;
      }

      public String getIssueMethod() {
         return this.issueMethod;
      }

      public String getIsCashFromInnerAcct() {
         return this.isCashFromInnerAcct;
      }

      public BigDecimal getMinChangeBalance() {
         return this.minChangeBalance;
      }

      public String getIntStartDate() {
         return this.intStartDate;
      }

      public String getMatureDate() {
         return this.matureDate;
      }

      public String getInlandOffshore() {
         return this.inlandOffshore;
      }

      public String getAcctType() {
         return this.acctType;
      }

      public String getTermType() {
         return this.termType;
      }

      public List<Core1400100219Out.StageCodeInfoArray.ChannelArray> getChannelArray() {
         return this.channelArray;
      }

      public List<Core1400100219Out.StageCodeInfoArray.ClientListArray> getClientListArray() {
         return this.clientListArray;
      }

      public String getIntStartFlag() {
         return this.intStartFlag;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getCcy() {
         return this.ccy;
      }

      public BigDecimal getLeaveQuota() {
         return this.leaveQuota;
      }

      public BigDecimal getSgMaxAmt() {
         return this.sgMaxAmt;
      }

      public String getCycleFreq() {
         return this.cycleFreq;
      }

      public String getDirectionChargeIntFlag() {
         return this.directionChargeIntFlag;
      }

      public String getStageRemark() {
         return this.stageRemark;
      }

      public Integer getCalmDays() {
         return this.calmDays;
      }

      public List<Core1400100219Out.StageCodeInfoArray.CustomerBaseClass> getCustomerBaseClass() {
         return this.customerBaseClass;
      }

      public String getAllowBranch() {
         return this.allowBranch;
      }

      public String getAllowAcctClass() {
         return this.allowAcctClass;
      }

      public String getTrfThruDate() {
         return this.trfThruDate;
      }

      public String getTrfOrderThruDate() {
         return this.trfOrderThruDate;
      }

      public String getCustomerBase() {
         return this.customerBase;
      }

      public String getNewAndOldFundFlag() {
         return this.newAndOldFundFlag;
      }

      public List<Core1400100219Out.StageCodeInfoArray.CustomerBaseArrays> getCustomerBaseArrays() {
         return this.customerBaseArrays;
      }

      public String getStageCodeDescEn() {
         return this.stageCodeDescEn;
      }

      public String getGetIntFreq() {
         return this.getIntFreq;
      }

      public BigDecimal getFixedRate() {
         return this.fixedRate;
      }

      public String getRationType() {
         return this.rationType;
      }

      public String getAllowBuyWayCd() {
         return this.allowBuyWayCd;
      }

      public BigDecimal getTotalQuota() {
         return this.totalQuota;
      }

      public String getStageCodeDescSecond() {
         return this.stageCodeDescSecond;
      }

      public BigDecimal getHoldingQuota() {
         return this.holdingQuota;
      }

      public String getTrfFlag() {
         return this.trfFlag;
      }

      public BigDecimal getSgMinAmt() {
         return this.sgMinAmt;
      }

      public String getWhiteSellFlag() {
         return this.whiteSellFlag;
      }

      public String getStageRemarkSecond() {
         return this.stageRemarkSecond;
      }

      public String getStageStatus() {
         return this.stageStatus;
      }

      public void setStageCode(String stageCode) {
         this.stageCode = stageCode;
      }

      public void setStageCodeDesc(String stageCodeDesc) {
         this.stageCodeDesc = stageCodeDesc;
      }

      public void setIssueYear(String issueYear) {
         this.issueYear = issueYear;
      }

      public void setClientType(String clientType) {
         this.clientType = clientType;
      }

      public void setBranch(String branch) {
         this.branch = branch;
      }

      public void setIssueAmt(BigDecimal issueAmt) {
         this.issueAmt = issueAmt;
      }

      public void setStartDate(String startDate) {
         this.startDate = startDate;
      }

      public void setEndDate(String endDate) {
         this.endDate = endDate;
      }

      public void setSaleStartTime(String saleStartTime) {
         this.saleStartTime = saleStartTime;
      }

      public void setSaleEndTime(String saleEndTime) {
         this.saleEndTime = saleEndTime;
      }

      public void setStageMinAmt(BigDecimal stageMinAmt) {
         this.stageMinAmt = stageMinAmt;
      }

      public void setKeepMinBal(BigDecimal keepMinBal) {
         this.keepMinBal = keepMinBal;
      }

      public void setTerm(String term) {
         this.term = term;
      }

      public void setIntType(String intType) {
         this.intType = intType;
      }

      public void setPayIntType(String payIntType) {
         this.payIntType = payIntType;
      }

      public void setPreWithdrawFlag(String preWithdrawFlag) {
         this.preWithdrawFlag = preWithdrawFlag;
      }

      public void setPreWithdrawNum(Integer preWithdrawNum) {
         this.preWithdrawNum = preWithdrawNum;
      }

      public void setIsFullTransfer(String isFullTransfer) {
         this.isFullTransfer = isFullTransfer;
      }

      public void setRedemptionFlag(String redemptionFlag) {
         this.redemptionFlag = redemptionFlag;
      }

      public void setTohonorRate(BigDecimal tohonorRate) {
         this.tohonorRate = tohonorRate;
      }

      public void setTohonorDate(String tohonorDate) {
         this.tohonorDate = tohonorDate;
      }

      public void setFloatRate(BigDecimal floatRate) {
         this.floatRate = floatRate;
      }

      public void setEmail(String email) {
         this.email = email;
      }

      public void setTrfOutFeeAmt(BigDecimal trfOutFeeAmt) {
         this.trfOutFeeAmt = trfOutFeeAmt;
      }

      public void setTrfInFeeType(String trfInFeeType) {
         this.trfInFeeType = trfInFeeType;
      }

      public void setTrfOutFeeType(String trfOutFeeType) {
         this.trfOutFeeType = trfOutFeeType;
      }

      public void setRealRate(BigDecimal realRate) {
         this.realRate = realRate;
      }

      public void setRedemptionIntType(String redemptionIntType) {
         this.redemptionIntType = redemptionIntType;
      }

      public void setInFee(BigDecimal inFee) {
         this.inFee = inFee;
      }

      public void setIssueMethod(String issueMethod) {
         this.issueMethod = issueMethod;
      }

      public void setIsCashFromInnerAcct(String isCashFromInnerAcct) {
         this.isCashFromInnerAcct = isCashFromInnerAcct;
      }

      public void setMinChangeBalance(BigDecimal minChangeBalance) {
         this.minChangeBalance = minChangeBalance;
      }

      public void setIntStartDate(String intStartDate) {
         this.intStartDate = intStartDate;
      }

      public void setMatureDate(String matureDate) {
         this.matureDate = matureDate;
      }

      public void setInlandOffshore(String inlandOffshore) {
         this.inlandOffshore = inlandOffshore;
      }

      public void setAcctType(String acctType) {
         this.acctType = acctType;
      }

      public void setTermType(String termType) {
         this.termType = termType;
      }

      public void setChannelArray(List<Core1400100219Out.StageCodeInfoArray.ChannelArray> channelArray) {
         this.channelArray = channelArray;
      }

      public void setClientListArray(List<Core1400100219Out.StageCodeInfoArray.ClientListArray> clientListArray) {
         this.clientListArray = clientListArray;
      }

      public void setIntStartFlag(String intStartFlag) {
         this.intStartFlag = intStartFlag;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setLeaveQuota(BigDecimal leaveQuota) {
         this.leaveQuota = leaveQuota;
      }

      public void setSgMaxAmt(BigDecimal sgMaxAmt) {
         this.sgMaxAmt = sgMaxAmt;
      }

      public void setCycleFreq(String cycleFreq) {
         this.cycleFreq = cycleFreq;
      }

      public void setDirectionChargeIntFlag(String directionChargeIntFlag) {
         this.directionChargeIntFlag = directionChargeIntFlag;
      }

      public void setStageRemark(String stageRemark) {
         this.stageRemark = stageRemark;
      }

      public void setCalmDays(Integer calmDays) {
         this.calmDays = calmDays;
      }

      public void setCustomerBaseClass(List<Core1400100219Out.StageCodeInfoArray.CustomerBaseClass> customerBaseClass) {
         this.customerBaseClass = customerBaseClass;
      }

      public void setAllowBranch(String allowBranch) {
         this.allowBranch = allowBranch;
      }

      public void setAllowAcctClass(String allowAcctClass) {
         this.allowAcctClass = allowAcctClass;
      }

      public void setTrfThruDate(String trfThruDate) {
         this.trfThruDate = trfThruDate;
      }

      public void setTrfOrderThruDate(String trfOrderThruDate) {
         this.trfOrderThruDate = trfOrderThruDate;
      }

      public void setCustomerBase(String customerBase) {
         this.customerBase = customerBase;
      }

      public void setNewAndOldFundFlag(String newAndOldFundFlag) {
         this.newAndOldFundFlag = newAndOldFundFlag;
      }

      public void setCustomerBaseArrays(List<Core1400100219Out.StageCodeInfoArray.CustomerBaseArrays> customerBaseArrays) {
         this.customerBaseArrays = customerBaseArrays;
      }

      public void setStageCodeDescEn(String stageCodeDescEn) {
         this.stageCodeDescEn = stageCodeDescEn;
      }

      public void setGetIntFreq(String getIntFreq) {
         this.getIntFreq = getIntFreq;
      }

      public void setFixedRate(BigDecimal fixedRate) {
         this.fixedRate = fixedRate;
      }

      public void setRationType(String rationType) {
         this.rationType = rationType;
      }

      public void setAllowBuyWayCd(String allowBuyWayCd) {
         this.allowBuyWayCd = allowBuyWayCd;
      }

      public void setTotalQuota(BigDecimal totalQuota) {
         this.totalQuota = totalQuota;
      }

      public void setStageCodeDescSecond(String stageCodeDescSecond) {
         this.stageCodeDescSecond = stageCodeDescSecond;
      }

      public void setHoldingQuota(BigDecimal holdingQuota) {
         this.holdingQuota = holdingQuota;
      }

      public void setTrfFlag(String trfFlag) {
         this.trfFlag = trfFlag;
      }

      public void setSgMinAmt(BigDecimal sgMinAmt) {
         this.sgMinAmt = sgMinAmt;
      }

      public void setWhiteSellFlag(String whiteSellFlag) {
         this.whiteSellFlag = whiteSellFlag;
      }

      public void setStageRemarkSecond(String stageRemarkSecond) {
         this.stageRemarkSecond = stageRemarkSecond;
      }

      public void setStageStatus(String stageStatus) {
         this.stageStatus = stageStatus;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100219Out.StageCodeInfoArray)) {
            return false;
         } else {
            Core1400100219Out.StageCodeInfoArray other = (Core1400100219Out.StageCodeInfoArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label839: {
                  Object this$stageCode = this.getStageCode();
                  Object other$stageCode = other.getStageCode();
                  if (this$stageCode == null) {
                     if (other$stageCode == null) {
                        break label839;
                     }
                  } else if (this$stageCode.equals(other$stageCode)) {
                     break label839;
                  }

                  return false;
               }

               Object this$stageCodeDesc = this.getStageCodeDesc();
               Object other$stageCodeDesc = other.getStageCodeDesc();
               if (this$stageCodeDesc == null) {
                  if (other$stageCodeDesc != null) {
                     return false;
                  }
               } else if (!this$stageCodeDesc.equals(other$stageCodeDesc)) {
                  return false;
               }

               label825: {
                  Object this$issueYear = this.getIssueYear();
                  Object other$issueYear = other.getIssueYear();
                  if (this$issueYear == null) {
                     if (other$issueYear == null) {
                        break label825;
                     }
                  } else if (this$issueYear.equals(other$issueYear)) {
                     break label825;
                  }

                  return false;
               }

               Object this$clientType = this.getClientType();
               Object other$clientType = other.getClientType();
               if (this$clientType == null) {
                  if (other$clientType != null) {
                     return false;
                  }
               } else if (!this$clientType.equals(other$clientType)) {
                  return false;
               }

               label811: {
                  Object this$branch = this.getBranch();
                  Object other$branch = other.getBranch();
                  if (this$branch == null) {
                     if (other$branch == null) {
                        break label811;
                     }
                  } else if (this$branch.equals(other$branch)) {
                     break label811;
                  }

                  return false;
               }

               Object this$issueAmt = this.getIssueAmt();
               Object other$issueAmt = other.getIssueAmt();
               if (this$issueAmt == null) {
                  if (other$issueAmt != null) {
                     return false;
                  }
               } else if (!this$issueAmt.equals(other$issueAmt)) {
                  return false;
               }

               label797: {
                  Object this$startDate = this.getStartDate();
                  Object other$startDate = other.getStartDate();
                  if (this$startDate == null) {
                     if (other$startDate == null) {
                        break label797;
                     }
                  } else if (this$startDate.equals(other$startDate)) {
                     break label797;
                  }

                  return false;
               }

               label790: {
                  Object this$endDate = this.getEndDate();
                  Object other$endDate = other.getEndDate();
                  if (this$endDate == null) {
                     if (other$endDate == null) {
                        break label790;
                     }
                  } else if (this$endDate.equals(other$endDate)) {
                     break label790;
                  }

                  return false;
               }

               Object this$saleStartTime = this.getSaleStartTime();
               Object other$saleStartTime = other.getSaleStartTime();
               if (this$saleStartTime == null) {
                  if (other$saleStartTime != null) {
                     return false;
                  }
               } else if (!this$saleStartTime.equals(other$saleStartTime)) {
                  return false;
               }

               label776: {
                  Object this$saleEndTime = this.getSaleEndTime();
                  Object other$saleEndTime = other.getSaleEndTime();
                  if (this$saleEndTime == null) {
                     if (other$saleEndTime == null) {
                        break label776;
                     }
                  } else if (this$saleEndTime.equals(other$saleEndTime)) {
                     break label776;
                  }

                  return false;
               }

               label769: {
                  Object this$stageMinAmt = this.getStageMinAmt();
                  Object other$stageMinAmt = other.getStageMinAmt();
                  if (this$stageMinAmt == null) {
                     if (other$stageMinAmt == null) {
                        break label769;
                     }
                  } else if (this$stageMinAmt.equals(other$stageMinAmt)) {
                     break label769;
                  }

                  return false;
               }

               Object this$keepMinBal = this.getKeepMinBal();
               Object other$keepMinBal = other.getKeepMinBal();
               if (this$keepMinBal == null) {
                  if (other$keepMinBal != null) {
                     return false;
                  }
               } else if (!this$keepMinBal.equals(other$keepMinBal)) {
                  return false;
               }

               Object this$term = this.getTerm();
               Object other$term = other.getTerm();
               if (this$term == null) {
                  if (other$term != null) {
                     return false;
                  }
               } else if (!this$term.equals(other$term)) {
                  return false;
               }

               label748: {
                  Object this$intType = this.getIntType();
                  Object other$intType = other.getIntType();
                  if (this$intType == null) {
                     if (other$intType == null) {
                        break label748;
                     }
                  } else if (this$intType.equals(other$intType)) {
                     break label748;
                  }

                  return false;
               }

               Object this$payIntType = this.getPayIntType();
               Object other$payIntType = other.getPayIntType();
               if (this$payIntType == null) {
                  if (other$payIntType != null) {
                     return false;
                  }
               } else if (!this$payIntType.equals(other$payIntType)) {
                  return false;
               }

               Object this$preWithdrawFlag = this.getPreWithdrawFlag();
               Object other$preWithdrawFlag = other.getPreWithdrawFlag();
               if (this$preWithdrawFlag == null) {
                  if (other$preWithdrawFlag != null) {
                     return false;
                  }
               } else if (!this$preWithdrawFlag.equals(other$preWithdrawFlag)) {
                  return false;
               }

               label727: {
                  Object this$preWithdrawNum = this.getPreWithdrawNum();
                  Object other$preWithdrawNum = other.getPreWithdrawNum();
                  if (this$preWithdrawNum == null) {
                     if (other$preWithdrawNum == null) {
                        break label727;
                     }
                  } else if (this$preWithdrawNum.equals(other$preWithdrawNum)) {
                     break label727;
                  }

                  return false;
               }

               Object this$isFullTransfer = this.getIsFullTransfer();
               Object other$isFullTransfer = other.getIsFullTransfer();
               if (this$isFullTransfer == null) {
                  if (other$isFullTransfer != null) {
                     return false;
                  }
               } else if (!this$isFullTransfer.equals(other$isFullTransfer)) {
                  return false;
               }

               label713: {
                  Object this$redemptionFlag = this.getRedemptionFlag();
                  Object other$redemptionFlag = other.getRedemptionFlag();
                  if (this$redemptionFlag == null) {
                     if (other$redemptionFlag == null) {
                        break label713;
                     }
                  } else if (this$redemptionFlag.equals(other$redemptionFlag)) {
                     break label713;
                  }

                  return false;
               }

               Object this$tohonorRate = this.getTohonorRate();
               Object other$tohonorRate = other.getTohonorRate();
               if (this$tohonorRate == null) {
                  if (other$tohonorRate != null) {
                     return false;
                  }
               } else if (!this$tohonorRate.equals(other$tohonorRate)) {
                  return false;
               }

               label699: {
                  Object this$tohonorDate = this.getTohonorDate();
                  Object other$tohonorDate = other.getTohonorDate();
                  if (this$tohonorDate == null) {
                     if (other$tohonorDate == null) {
                        break label699;
                     }
                  } else if (this$tohonorDate.equals(other$tohonorDate)) {
                     break label699;
                  }

                  return false;
               }

               Object this$floatRate = this.getFloatRate();
               Object other$floatRate = other.getFloatRate();
               if (this$floatRate == null) {
                  if (other$floatRate != null) {
                     return false;
                  }
               } else if (!this$floatRate.equals(other$floatRate)) {
                  return false;
               }

               label685: {
                  Object this$email = this.getEmail();
                  Object other$email = other.getEmail();
                  if (this$email == null) {
                     if (other$email == null) {
                        break label685;
                     }
                  } else if (this$email.equals(other$email)) {
                     break label685;
                  }

                  return false;
               }

               label678: {
                  Object this$trfOutFeeAmt = this.getTrfOutFeeAmt();
                  Object other$trfOutFeeAmt = other.getTrfOutFeeAmt();
                  if (this$trfOutFeeAmt == null) {
                     if (other$trfOutFeeAmt == null) {
                        break label678;
                     }
                  } else if (this$trfOutFeeAmt.equals(other$trfOutFeeAmt)) {
                     break label678;
                  }

                  return false;
               }

               Object this$trfInFeeType = this.getTrfInFeeType();
               Object other$trfInFeeType = other.getTrfInFeeType();
               if (this$trfInFeeType == null) {
                  if (other$trfInFeeType != null) {
                     return false;
                  }
               } else if (!this$trfInFeeType.equals(other$trfInFeeType)) {
                  return false;
               }

               label664: {
                  Object this$trfOutFeeType = this.getTrfOutFeeType();
                  Object other$trfOutFeeType = other.getTrfOutFeeType();
                  if (this$trfOutFeeType == null) {
                     if (other$trfOutFeeType == null) {
                        break label664;
                     }
                  } else if (this$trfOutFeeType.equals(other$trfOutFeeType)) {
                     break label664;
                  }

                  return false;
               }

               label657: {
                  Object this$realRate = this.getRealRate();
                  Object other$realRate = other.getRealRate();
                  if (this$realRate == null) {
                     if (other$realRate == null) {
                        break label657;
                     }
                  } else if (this$realRate.equals(other$realRate)) {
                     break label657;
                  }

                  return false;
               }

               Object this$redemptionIntType = this.getRedemptionIntType();
               Object other$redemptionIntType = other.getRedemptionIntType();
               if (this$redemptionIntType == null) {
                  if (other$redemptionIntType != null) {
                     return false;
                  }
               } else if (!this$redemptionIntType.equals(other$redemptionIntType)) {
                  return false;
               }

               Object this$inFee = this.getInFee();
               Object other$inFee = other.getInFee();
               if (this$inFee == null) {
                  if (other$inFee != null) {
                     return false;
                  }
               } else if (!this$inFee.equals(other$inFee)) {
                  return false;
               }

               label636: {
                  Object this$issueMethod = this.getIssueMethod();
                  Object other$issueMethod = other.getIssueMethod();
                  if (this$issueMethod == null) {
                     if (other$issueMethod == null) {
                        break label636;
                     }
                  } else if (this$issueMethod.equals(other$issueMethod)) {
                     break label636;
                  }

                  return false;
               }

               Object this$isCashFromInnerAcct = this.getIsCashFromInnerAcct();
               Object other$isCashFromInnerAcct = other.getIsCashFromInnerAcct();
               if (this$isCashFromInnerAcct == null) {
                  if (other$isCashFromInnerAcct != null) {
                     return false;
                  }
               } else if (!this$isCashFromInnerAcct.equals(other$isCashFromInnerAcct)) {
                  return false;
               }

               Object this$minChangeBalance = this.getMinChangeBalance();
               Object other$minChangeBalance = other.getMinChangeBalance();
               if (this$minChangeBalance == null) {
                  if (other$minChangeBalance != null) {
                     return false;
                  }
               } else if (!this$minChangeBalance.equals(other$minChangeBalance)) {
                  return false;
               }

               label615: {
                  Object this$intStartDate = this.getIntStartDate();
                  Object other$intStartDate = other.getIntStartDate();
                  if (this$intStartDate == null) {
                     if (other$intStartDate == null) {
                        break label615;
                     }
                  } else if (this$intStartDate.equals(other$intStartDate)) {
                     break label615;
                  }

                  return false;
               }

               Object this$matureDate = this.getMatureDate();
               Object other$matureDate = other.getMatureDate();
               if (this$matureDate == null) {
                  if (other$matureDate != null) {
                     return false;
                  }
               } else if (!this$matureDate.equals(other$matureDate)) {
                  return false;
               }

               label601: {
                  Object this$inlandOffshore = this.getInlandOffshore();
                  Object other$inlandOffshore = other.getInlandOffshore();
                  if (this$inlandOffshore == null) {
                     if (other$inlandOffshore == null) {
                        break label601;
                     }
                  } else if (this$inlandOffshore.equals(other$inlandOffshore)) {
                     break label601;
                  }

                  return false;
               }

               Object this$acctType = this.getAcctType();
               Object other$acctType = other.getAcctType();
               if (this$acctType == null) {
                  if (other$acctType != null) {
                     return false;
                  }
               } else if (!this$acctType.equals(other$acctType)) {
                  return false;
               }

               label587: {
                  Object this$termType = this.getTermType();
                  Object other$termType = other.getTermType();
                  if (this$termType == null) {
                     if (other$termType == null) {
                        break label587;
                     }
                  } else if (this$termType.equals(other$termType)) {
                     break label587;
                  }

                  return false;
               }

               Object this$channelArray = this.getChannelArray();
               Object other$channelArray = other.getChannelArray();
               if (this$channelArray == null) {
                  if (other$channelArray != null) {
                     return false;
                  }
               } else if (!this$channelArray.equals(other$channelArray)) {
                  return false;
               }

               label573: {
                  Object this$clientListArray = this.getClientListArray();
                  Object other$clientListArray = other.getClientListArray();
                  if (this$clientListArray == null) {
                     if (other$clientListArray == null) {
                        break label573;
                     }
                  } else if (this$clientListArray.equals(other$clientListArray)) {
                     break label573;
                  }

                  return false;
               }

               label566: {
                  Object this$intStartFlag = this.getIntStartFlag();
                  Object other$intStartFlag = other.getIntStartFlag();
                  if (this$intStartFlag == null) {
                     if (other$intStartFlag == null) {
                        break label566;
                     }
                  } else if (this$intStartFlag.equals(other$intStartFlag)) {
                     break label566;
                  }

                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               label552: {
                  Object this$ccy = this.getCcy();
                  Object other$ccy = other.getCcy();
                  if (this$ccy == null) {
                     if (other$ccy == null) {
                        break label552;
                     }
                  } else if (this$ccy.equals(other$ccy)) {
                     break label552;
                  }

                  return false;
               }

               label545: {
                  Object this$leaveQuota = this.getLeaveQuota();
                  Object other$leaveQuota = other.getLeaveQuota();
                  if (this$leaveQuota == null) {
                     if (other$leaveQuota == null) {
                        break label545;
                     }
                  } else if (this$leaveQuota.equals(other$leaveQuota)) {
                     break label545;
                  }

                  return false;
               }

               Object this$sgMaxAmt = this.getSgMaxAmt();
               Object other$sgMaxAmt = other.getSgMaxAmt();
               if (this$sgMaxAmt == null) {
                  if (other$sgMaxAmt != null) {
                     return false;
                  }
               } else if (!this$sgMaxAmt.equals(other$sgMaxAmt)) {
                  return false;
               }

               Object this$cycleFreq = this.getCycleFreq();
               Object other$cycleFreq = other.getCycleFreq();
               if (this$cycleFreq == null) {
                  if (other$cycleFreq != null) {
                     return false;
                  }
               } else if (!this$cycleFreq.equals(other$cycleFreq)) {
                  return false;
               }

               label524: {
                  Object this$directionChargeIntFlag = this.getDirectionChargeIntFlag();
                  Object other$directionChargeIntFlag = other.getDirectionChargeIntFlag();
                  if (this$directionChargeIntFlag == null) {
                     if (other$directionChargeIntFlag == null) {
                        break label524;
                     }
                  } else if (this$directionChargeIntFlag.equals(other$directionChargeIntFlag)) {
                     break label524;
                  }

                  return false;
               }

               Object this$stageRemark = this.getStageRemark();
               Object other$stageRemark = other.getStageRemark();
               if (this$stageRemark == null) {
                  if (other$stageRemark != null) {
                     return false;
                  }
               } else if (!this$stageRemark.equals(other$stageRemark)) {
                  return false;
               }

               Object this$calmDays = this.getCalmDays();
               Object other$calmDays = other.getCalmDays();
               if (this$calmDays == null) {
                  if (other$calmDays != null) {
                     return false;
                  }
               } else if (!this$calmDays.equals(other$calmDays)) {
                  return false;
               }

               label503: {
                  Object this$customerBaseClass = this.getCustomerBaseClass();
                  Object other$customerBaseClass = other.getCustomerBaseClass();
                  if (this$customerBaseClass == null) {
                     if (other$customerBaseClass == null) {
                        break label503;
                     }
                  } else if (this$customerBaseClass.equals(other$customerBaseClass)) {
                     break label503;
                  }

                  return false;
               }

               Object this$allowBranch = this.getAllowBranch();
               Object other$allowBranch = other.getAllowBranch();
               if (this$allowBranch == null) {
                  if (other$allowBranch != null) {
                     return false;
                  }
               } else if (!this$allowBranch.equals(other$allowBranch)) {
                  return false;
               }

               label489: {
                  Object this$allowAcctClass = this.getAllowAcctClass();
                  Object other$allowAcctClass = other.getAllowAcctClass();
                  if (this$allowAcctClass == null) {
                     if (other$allowAcctClass == null) {
                        break label489;
                     }
                  } else if (this$allowAcctClass.equals(other$allowAcctClass)) {
                     break label489;
                  }

                  return false;
               }

               Object this$trfThruDate = this.getTrfThruDate();
               Object other$trfThruDate = other.getTrfThruDate();
               if (this$trfThruDate == null) {
                  if (other$trfThruDate != null) {
                     return false;
                  }
               } else if (!this$trfThruDate.equals(other$trfThruDate)) {
                  return false;
               }

               label475: {
                  Object this$trfOrderThruDate = this.getTrfOrderThruDate();
                  Object other$trfOrderThruDate = other.getTrfOrderThruDate();
                  if (this$trfOrderThruDate == null) {
                     if (other$trfOrderThruDate == null) {
                        break label475;
                     }
                  } else if (this$trfOrderThruDate.equals(other$trfOrderThruDate)) {
                     break label475;
                  }

                  return false;
               }

               Object this$customerBase = this.getCustomerBase();
               Object other$customerBase = other.getCustomerBase();
               if (this$customerBase == null) {
                  if (other$customerBase != null) {
                     return false;
                  }
               } else if (!this$customerBase.equals(other$customerBase)) {
                  return false;
               }

               label461: {
                  Object this$newAndOldFundFlag = this.getNewAndOldFundFlag();
                  Object other$newAndOldFundFlag = other.getNewAndOldFundFlag();
                  if (this$newAndOldFundFlag == null) {
                     if (other$newAndOldFundFlag == null) {
                        break label461;
                     }
                  } else if (this$newAndOldFundFlag.equals(other$newAndOldFundFlag)) {
                     break label461;
                  }

                  return false;
               }

               label454: {
                  Object this$customerBaseArrays = this.getCustomerBaseArrays();
                  Object other$customerBaseArrays = other.getCustomerBaseArrays();
                  if (this$customerBaseArrays == null) {
                     if (other$customerBaseArrays == null) {
                        break label454;
                     }
                  } else if (this$customerBaseArrays.equals(other$customerBaseArrays)) {
                     break label454;
                  }

                  return false;
               }

               Object this$stageCodeDescEn = this.getStageCodeDescEn();
               Object other$stageCodeDescEn = other.getStageCodeDescEn();
               if (this$stageCodeDescEn == null) {
                  if (other$stageCodeDescEn != null) {
                     return false;
                  }
               } else if (!this$stageCodeDescEn.equals(other$stageCodeDescEn)) {
                  return false;
               }

               label440: {
                  Object this$getIntFreq = this.getGetIntFreq();
                  Object other$getIntFreq = other.getGetIntFreq();
                  if (this$getIntFreq == null) {
                     if (other$getIntFreq == null) {
                        break label440;
                     }
                  } else if (this$getIntFreq.equals(other$getIntFreq)) {
                     break label440;
                  }

                  return false;
               }

               label433: {
                  Object this$fixedRate = this.getFixedRate();
                  Object other$fixedRate = other.getFixedRate();
                  if (this$fixedRate == null) {
                     if (other$fixedRate == null) {
                        break label433;
                     }
                  } else if (this$fixedRate.equals(other$fixedRate)) {
                     break label433;
                  }

                  return false;
               }

               Object this$rationType = this.getRationType();
               Object other$rationType = other.getRationType();
               if (this$rationType == null) {
                  if (other$rationType != null) {
                     return false;
                  }
               } else if (!this$rationType.equals(other$rationType)) {
                  return false;
               }

               Object this$allowBuyWayCd = this.getAllowBuyWayCd();
               Object other$allowBuyWayCd = other.getAllowBuyWayCd();
               if (this$allowBuyWayCd == null) {
                  if (other$allowBuyWayCd != null) {
                     return false;
                  }
               } else if (!this$allowBuyWayCd.equals(other$allowBuyWayCd)) {
                  return false;
               }

               label412: {
                  Object this$totalQuota = this.getTotalQuota();
                  Object other$totalQuota = other.getTotalQuota();
                  if (this$totalQuota == null) {
                     if (other$totalQuota == null) {
                        break label412;
                     }
                  } else if (this$totalQuota.equals(other$totalQuota)) {
                     break label412;
                  }

                  return false;
               }

               Object this$stageCodeDescSecond = this.getStageCodeDescSecond();
               Object other$stageCodeDescSecond = other.getStageCodeDescSecond();
               if (this$stageCodeDescSecond == null) {
                  if (other$stageCodeDescSecond != null) {
                     return false;
                  }
               } else if (!this$stageCodeDescSecond.equals(other$stageCodeDescSecond)) {
                  return false;
               }

               Object this$holdingQuota = this.getHoldingQuota();
               Object other$holdingQuota = other.getHoldingQuota();
               if (this$holdingQuota == null) {
                  if (other$holdingQuota != null) {
                     return false;
                  }
               } else if (!this$holdingQuota.equals(other$holdingQuota)) {
                  return false;
               }

               label391: {
                  Object this$trfFlag = this.getTrfFlag();
                  Object other$trfFlag = other.getTrfFlag();
                  if (this$trfFlag == null) {
                     if (other$trfFlag == null) {
                        break label391;
                     }
                  } else if (this$trfFlag.equals(other$trfFlag)) {
                     break label391;
                  }

                  return false;
               }

               Object this$sgMinAmt = this.getSgMinAmt();
               Object other$sgMinAmt = other.getSgMinAmt();
               if (this$sgMinAmt == null) {
                  if (other$sgMinAmt != null) {
                     return false;
                  }
               } else if (!this$sgMinAmt.equals(other$sgMinAmt)) {
                  return false;
               }

               label377: {
                  Object this$whiteSellFlag = this.getWhiteSellFlag();
                  Object other$whiteSellFlag = other.getWhiteSellFlag();
                  if (this$whiteSellFlag == null) {
                     if (other$whiteSellFlag == null) {
                        break label377;
                     }
                  } else if (this$whiteSellFlag.equals(other$whiteSellFlag)) {
                     break label377;
                  }

                  return false;
               }

               Object this$stageRemarkSecond = this.getStageRemarkSecond();
               Object other$stageRemarkSecond = other.getStageRemarkSecond();
               if (this$stageRemarkSecond == null) {
                  if (other$stageRemarkSecond != null) {
                     return false;
                  }
               } else if (!this$stageRemarkSecond.equals(other$stageRemarkSecond)) {
                  return false;
               }

               Object this$stageStatus = this.getStageStatus();
               Object other$stageStatus = other.getStageStatus();
               if (this$stageStatus == null) {
                  if (other$stageStatus == null) {
                     return true;
                  }
               } else if (this$stageStatus.equals(other$stageStatus)) {
                  return true;
               }

               return false;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100219Out.StageCodeInfoArray;
      }
      public String toString() {
         return "Core1400100219Out.StageCodeInfoArray(stageCode=" + this.getStageCode() + ", stageCodeDesc=" + this.getStageCodeDesc() + ", issueYear=" + this.getIssueYear() + ", clientType=" + this.getClientType() + ", branch=" + this.getBranch() + ", issueAmt=" + this.getIssueAmt() + ", startDate=" + this.getStartDate() + ", endDate=" + this.getEndDate() + ", saleStartTime=" + this.getSaleStartTime() + ", saleEndTime=" + this.getSaleEndTime() + ", stageMinAmt=" + this.getStageMinAmt() + ", keepMinBal=" + this.getKeepMinBal() + ", term=" + this.getTerm() + ", intType=" + this.getIntType() + ", payIntType=" + this.getPayIntType() + ", preWithdrawFlag=" + this.getPreWithdrawFlag() + ", preWithdrawNum=" + this.getPreWithdrawNum() + ", isFullTransfer=" + this.getIsFullTransfer() + ", redemptionFlag=" + this.getRedemptionFlag() + ", tohonorRate=" + this.getTohonorRate() + ", tohonorDate=" + this.getTohonorDate() + ", floatRate=" + this.getFloatRate() + ", email=" + this.getEmail() + ", trfOutFeeAmt=" + this.getTrfOutFeeAmt() + ", trfInFeeType=" + this.getTrfInFeeType() + ", trfOutFeeType=" + this.getTrfOutFeeType() + ", realRate=" + this.getRealRate() + ", redemptionIntType=" + this.getRedemptionIntType() + ", inFee=" + this.getInFee() + ", issueMethod=" + this.getIssueMethod() + ", isCashFromInnerAcct=" + this.getIsCashFromInnerAcct() + ", minChangeBalance=" + this.getMinChangeBalance() + ", intStartDate=" + this.getIntStartDate() + ", matureDate=" + this.getMatureDate() + ", inlandOffshore=" + this.getInlandOffshore() + ", acctType=" + this.getAcctType() + ", termType=" + this.getTermType() + ", channelArray=" + this.getChannelArray() + ", clientListArray=" + this.getClientListArray() + ", intStartFlag=" + this.getIntStartFlag() + ", prodType=" + this.getProdType() + ", ccy=" + this.getCcy() + ", leaveQuota=" + this.getLeaveQuota() + ", sgMaxAmt=" + this.getSgMaxAmt() + ", cycleFreq=" + this.getCycleFreq() + ", directionChargeIntFlag=" + this.getDirectionChargeIntFlag() + ", stageRemark=" + this.getStageRemark() + ", calmDays=" + this.getCalmDays() + ", customerBaseClass=" + this.getCustomerBaseClass() + ", allowBranch=" + this.getAllowBranch() + ", allowAcctClass=" + this.getAllowAcctClass() + ", trfThruDate=" + this.getTrfThruDate() + ", trfOrderThruDate=" + this.getTrfOrderThruDate() + ", customerBase=" + this.getCustomerBase() + ", newAndOldFundFlag=" + this.getNewAndOldFundFlag() + ", customerBaseArrays=" + this.getCustomerBaseArrays() + ", stageCodeDescEn=" + this.getStageCodeDescEn() + ", getIntFreq=" + this.getGetIntFreq() + ", fixedRate=" + this.getFixedRate() + ", rationType=" + this.getRationType() + ", allowBuyWayCd=" + this.getAllowBuyWayCd() + ", totalQuota=" + this.getTotalQuota() + ", stageCodeDescSecond=" + this.getStageCodeDescSecond() + ", holdingQuota=" + this.getHoldingQuota() + ", trfFlag=" + this.getTrfFlag() + ", sgMinAmt=" + this.getSgMinAmt() + ", whiteSellFlag=" + this.getWhiteSellFlag() + ", stageRemarkSecond=" + this.getStageRemarkSecond() + ", stageStatus=" + this.getStageStatus() + ")";
      }

      public static class CustomerBaseArrays {
         @V(
            desc = "客户群体最高购买上限",
            notNull = false,
            length = "17",
            remark = "客户群体最高购买上限",
            decimalLength = 2,
            precision = 2
         )
         private BigDecimal customerMaxAmt;
         @V(
            desc = "客户群体",
            notNull = false,
            length = "20",
            remark = "客户群体",
            maxSize = 20
         )
         private String customerBase;

         public BigDecimal getCustomerMaxAmt() {
            return this.customerMaxAmt;
         }

         public String getCustomerBase() {
            return this.customerBase;
         }

         public void setCustomerMaxAmt(BigDecimal customerMaxAmt) {
            this.customerMaxAmt = customerMaxAmt;
         }

         public void setCustomerBase(String customerBase) {
            this.customerBase = customerBase;
         }

         public boolean equals(Object o) {
            if (o == this) {
               return true;
            } else if (!(o instanceof Core1400100219Out.StageCodeInfoArray.CustomerBaseArrays)) {
               return false;
            } else {
               Core1400100219Out.StageCodeInfoArray.CustomerBaseArrays other = (Core1400100219Out.StageCodeInfoArray.CustomerBaseArrays)o;
               if (!other.canEqual(this)) {
                  return false;
               } else {
                  Object this$customerMaxAmt = this.getCustomerMaxAmt();
                  Object other$customerMaxAmt = other.getCustomerMaxAmt();
                  if (this$customerMaxAmt == null) {
                     if (other$customerMaxAmt != null) {
                        return false;
                     }
                  } else if (!this$customerMaxAmt.equals(other$customerMaxAmt)) {
                     return false;
                  }

                  Object this$customerBase = this.getCustomerBase();
                  Object other$customerBase = other.getCustomerBase();
                  if (this$customerBase == null) {
                     if (other$customerBase != null) {
                        return false;
                     }
                  } else if (!this$customerBase.equals(other$customerBase)) {
                     return false;
                  }

                  return true;
               }
            }
         }

         protected boolean canEqual(Object other) {
            return other instanceof Core1400100219Out.StageCodeInfoArray.CustomerBaseArrays;
         }
         public String toString() {
            return "Core1400100219Out.StageCodeInfoArray.CustomerBaseArrays(customerMaxAmt=" + this.getCustomerMaxAmt() + ", customerBase=" + this.getCustomerBase() + ")";
         }
      }

      public static class CustomerBaseClass {
         @V(
            desc = "客户群体大类",
            notNull = false,
            length = "20",
            remark = "客户群体大类",
            maxSize = 20
         )
         private String customerParent;
         @V(
            desc = "客户群体小类",
            notNull = false,
            length = "20",
            remark = "客户群体小类",
            maxSize = 20
         )
         private String customerSon;

         public String getCustomerParent() {
            return this.customerParent;
         }

         public String getCustomerSon() {
            return this.customerSon;
         }

         public void setCustomerParent(String customerParent) {
            this.customerParent = customerParent;
         }

         public void setCustomerSon(String customerSon) {
            this.customerSon = customerSon;
         }

         public boolean equals(Object o) {
            if (o == this) {
               return true;
            } else if (!(o instanceof Core1400100219Out.StageCodeInfoArray.CustomerBaseClass)) {
               return false;
            } else {
               Core1400100219Out.StageCodeInfoArray.CustomerBaseClass other = (Core1400100219Out.StageCodeInfoArray.CustomerBaseClass)o;
               if (!other.canEqual(this)) {
                  return false;
               } else {
                  Object this$customerParent = this.getCustomerParent();
                  Object other$customerParent = other.getCustomerParent();
                  if (this$customerParent == null) {
                     if (other$customerParent != null) {
                        return false;
                     }
                  } else if (!this$customerParent.equals(other$customerParent)) {
                     return false;
                  }

                  Object this$customerSon = this.getCustomerSon();
                  Object other$customerSon = other.getCustomerSon();
                  if (this$customerSon == null) {
                     if (other$customerSon != null) {
                        return false;
                     }
                  } else if (!this$customerSon.equals(other$customerSon)) {
                     return false;
                  }

                  return true;
               }
            }
         }

         protected boolean canEqual(Object other) {
            return other instanceof Core1400100219Out.StageCodeInfoArray.CustomerBaseClass;
         }
         public String toString() {
            return "Core1400100219Out.StageCodeInfoArray.CustomerBaseClass(customerParent=" + this.getCustomerParent() + ", customerSon=" + this.getCustomerSon() + ")";
         }
      }

      public static class ClientListArray {
         @V(
            desc = "客户名称",
            notNull = false,
            length = "200",
            remark = "客户名称",
            maxSize = 200
         )
         private String clientName;
         @V(
            desc = "客户号",
            notNull = false,
            length = "20",
            remark = "客户号",
            maxSize = 20
         )
         private String clientNo;
         @V(
            desc = "证件号码",
            notNull = false,
            length = "50",
            remark = "证件号码",
            maxSize = 50
         )
         private String documentId;
         @V(
            desc = "证件类型",
            notNull = false,
            length = "3",
            remark = "证件类型",
            maxSize = 3
         )
         private String documentType;

         public String getClientName() {
            return this.clientName;
         }

         public String getClientNo() {
            return this.clientNo;
         }

         public String getDocumentId() {
            return this.documentId;
         }

         public String getDocumentType() {
            return this.documentType;
         }

         public void setClientName(String clientName) {
            this.clientName = clientName;
         }

         public void setClientNo(String clientNo) {
            this.clientNo = clientNo;
         }

         public void setDocumentId(String documentId) {
            this.documentId = documentId;
         }

         public void setDocumentType(String documentType) {
            this.documentType = documentType;
         }

         public boolean equals(Object o) {
            if (o == this) {
               return true;
            } else if (!(o instanceof Core1400100219Out.StageCodeInfoArray.ClientListArray)) {
               return false;
            } else {
               Core1400100219Out.StageCodeInfoArray.ClientListArray other = (Core1400100219Out.StageCodeInfoArray.ClientListArray)o;
               if (!other.canEqual(this)) {
                  return false;
               } else {
                  label59: {
                     Object this$clientName = this.getClientName();
                     Object other$clientName = other.getClientName();
                     if (this$clientName == null) {
                        if (other$clientName == null) {
                           break label59;
                        }
                     } else if (this$clientName.equals(other$clientName)) {
                        break label59;
                     }

                     return false;
                  }

                  Object this$clientNo = this.getClientNo();
                  Object other$clientNo = other.getClientNo();
                  if (this$clientNo == null) {
                     if (other$clientNo != null) {
                        return false;
                     }
                  } else if (!this$clientNo.equals(other$clientNo)) {
                     return false;
                  }

                  Object this$documentId = this.getDocumentId();
                  Object other$documentId = other.getDocumentId();
                  if (this$documentId == null) {
                     if (other$documentId != null) {
                        return false;
                     }
                  } else if (!this$documentId.equals(other$documentId)) {
                     return false;
                  }

                  Object this$documentType = this.getDocumentType();
                  Object other$documentType = other.getDocumentType();
                  if (this$documentType == null) {
                     if (other$documentType != null) {
                        return false;
                     }
                  } else if (!this$documentType.equals(other$documentType)) {
                     return false;
                  }

                  return true;
               }
            }
         }

         protected boolean canEqual(Object other) {
            return other instanceof Core1400100219Out.StageCodeInfoArray.ClientListArray;
         }
         public String toString() {
            return "Core1400100219Out.StageCodeInfoArray.ClientListArray(clientName=" + this.getClientName() + ", clientNo=" + this.getClientNo() + ", documentId=" + this.getDocumentId() + ", documentType=" + this.getDocumentType() + ")";
         }
      }

      public static class ChannelArray {
         @V(
            desc = "渠道",
            notNull = false,
            length = "10",
            remark = "渠道细类",
            maxSize = 10
         )
         private String channel;

         public String getChannel() {
            return this.channel;
         }

         public void setChannel(String channel) {
            this.channel = channel;
         }

         public boolean equals(Object o) {
            if (o == this) {
               return true;
            } else if (!(o instanceof Core1400100219Out.StageCodeInfoArray.ChannelArray)) {
               return false;
            } else {
               Core1400100219Out.StageCodeInfoArray.ChannelArray other = (Core1400100219Out.StageCodeInfoArray.ChannelArray)o;
               if (!other.canEqual(this)) {
                  return false;
               } else {
                  Object this$channel = this.getChannel();
                  Object other$channel = other.getChannel();
                  if (this$channel == null) {
                     if (other$channel != null) {
                        return false;
                     }
                  } else if (!this$channel.equals(other$channel)) {
                     return false;
                  }

                  return true;
               }
            }
         }

         protected boolean canEqual(Object other) {
            return other instanceof Core1400100219Out.StageCodeInfoArray.ChannelArray;
         }
         public String toString() {
            return "Core1400100219Out.StageCodeInfoArray.ChannelArray(channel=" + this.getChannel() + ")";
         }
      }
   }
}
