package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400049076In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400049076In.Body body;

   public Core1400049076In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400049076In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400049076In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400049076In)) {
         return false;
      } else {
         Core1400049076In other = (Core1400049076In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400049076In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "证件类型",
         notNull = false,
         length = "3",
         remark = "证件类型",
         maxSize = 3
      )
      private String documentType;
      @V(
         desc = "证件号码",
         notNull = false,
         length = "50",
         remark = "证件号码",
         maxSize = 50
      )
      private String documentId;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "发证国家",
         notNull = false,
         length = "3",
         remark = "发证国家",
         maxSize = 3
      )
      private String issCountry;
      @V(
         desc = "开始日期",
         notNull = false,
         remark = "开始日期"
      )
      private String startDate;
      @V(
         desc = "结束日期",
         notNull = false,
         remark = "结束日期"
      )
      private String endDate;
      @V(
         desc = "查询类型",
         notNull = false,
         length = "10",
         remark = "查询类型",
         maxSize = 10
      )
      private String queryType;
      @V(
         desc = "套餐代码",
         notNull = false,
         length = "50",
         remark = "套餐代码",
         maxSize = 50
      )
      private String packageId;
      @V(
         desc = "协议状态",
         notNull = false,
         length = "2",
         in = "A,E",
         inDesc = "A-生效,E-失效",
         remark = "普通协议使用，可应用于大部分场景，贷款模块用于资产证券化合同状态",
         maxSize = 2
      )
      private String agreementStatus;

      public String getDocumentType() {
         return this.documentType;
      }

      public String getDocumentId() {
         return this.documentId;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public String getIssCountry() {
         return this.issCountry;
      }

      public String getStartDate() {
         return this.startDate;
      }

      public String getEndDate() {
         return this.endDate;
      }

      public String getQueryType() {
         return this.queryType;
      }

      public String getPackageId() {
         return this.packageId;
      }

      public String getAgreementStatus() {
         return this.agreementStatus;
      }

      public void setDocumentType(String documentType) {
         this.documentType = documentType;
      }

      public void setDocumentId(String documentId) {
         this.documentId = documentId;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setIssCountry(String issCountry) {
         this.issCountry = issCountry;
      }

      public void setStartDate(String startDate) {
         this.startDate = startDate;
      }

      public void setEndDate(String endDate) {
         this.endDate = endDate;
      }

      public void setQueryType(String queryType) {
         this.queryType = queryType;
      }

      public void setPackageId(String packageId) {
         this.packageId = packageId;
      }

      public void setAgreementStatus(String agreementStatus) {
         this.agreementStatus = agreementStatus;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400049076In.Body)) {
            return false;
         } else {
            Core1400049076In.Body other = (Core1400049076In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label119: {
                  Object this$documentType = this.getDocumentType();
                  Object other$documentType = other.getDocumentType();
                  if (this$documentType == null) {
                     if (other$documentType == null) {
                        break label119;
                     }
                  } else if (this$documentType.equals(other$documentType)) {
                     break label119;
                  }

                  return false;
               }

               Object this$documentId = this.getDocumentId();
               Object other$documentId = other.getDocumentId();
               if (this$documentId == null) {
                  if (other$documentId != null) {
                     return false;
                  }
               } else if (!this$documentId.equals(other$documentId)) {
                  return false;
               }

               label105: {
                  Object this$clientNo = this.getClientNo();
                  Object other$clientNo = other.getClientNo();
                  if (this$clientNo == null) {
                     if (other$clientNo == null) {
                        break label105;
                     }
                  } else if (this$clientNo.equals(other$clientNo)) {
                     break label105;
                  }

                  return false;
               }

               Object this$issCountry = this.getIssCountry();
               Object other$issCountry = other.getIssCountry();
               if (this$issCountry == null) {
                  if (other$issCountry != null) {
                     return false;
                  }
               } else if (!this$issCountry.equals(other$issCountry)) {
                  return false;
               }

               label91: {
                  Object this$startDate = this.getStartDate();
                  Object other$startDate = other.getStartDate();
                  if (this$startDate == null) {
                     if (other$startDate == null) {
                        break label91;
                     }
                  } else if (this$startDate.equals(other$startDate)) {
                     break label91;
                  }

                  return false;
               }

               Object this$endDate = this.getEndDate();
               Object other$endDate = other.getEndDate();
               if (this$endDate == null) {
                  if (other$endDate != null) {
                     return false;
                  }
               } else if (!this$endDate.equals(other$endDate)) {
                  return false;
               }

               label77: {
                  Object this$queryType = this.getQueryType();
                  Object other$queryType = other.getQueryType();
                  if (this$queryType == null) {
                     if (other$queryType == null) {
                        break label77;
                     }
                  } else if (this$queryType.equals(other$queryType)) {
                     break label77;
                  }

                  return false;
               }

               label70: {
                  Object this$packageId = this.getPackageId();
                  Object other$packageId = other.getPackageId();
                  if (this$packageId == null) {
                     if (other$packageId == null) {
                        break label70;
                     }
                  } else if (this$packageId.equals(other$packageId)) {
                     break label70;
                  }

                  return false;
               }

               Object this$agreementStatus = this.getAgreementStatus();
               Object other$agreementStatus = other.getAgreementStatus();
               if (this$agreementStatus == null) {
                  if (other$agreementStatus != null) {
                     return false;
                  }
               } else if (!this$agreementStatus.equals(other$agreementStatus)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400049076In.Body;
      }
      public String toString() {
         return "Core1400049076In.Body(documentType=" + this.getDocumentType() + ", documentId=" + this.getDocumentId() + ", clientNo=" + this.getClientNo() + ", issCountry=" + this.getIssCountry() + ", startDate=" + this.getStartDate() + ", endDate=" + this.getEndDate() + ", queryType=" + this.getQueryType() + ", packageId=" + this.getPackageId() + ", agreementStatus=" + this.getAgreementStatus() + ")";
      }
   }
}
