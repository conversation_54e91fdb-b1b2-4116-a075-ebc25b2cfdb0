package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000130In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000130Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12000130 {
   String URL = "/rb/nfin/contact/update";


   @ApiRemark("更新账户地址信息，供cif使用")
   @ApiDesc("更新账户地址信息")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0130"
   )
   @FunctionCategory("CIF81-客户信息管理")
   @ApiUseStatus("INNER-内部")
   Core12000130Out runService(Core12000130In var1);
}
