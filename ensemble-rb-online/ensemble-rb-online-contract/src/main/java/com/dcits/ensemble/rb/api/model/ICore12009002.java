package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12009002In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12009002Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12009002 {
   String URL = "/rb/nfin/branch/sign";


   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "9002"
   )
   @BusinessCategory("1200-非金融")
   @FunctionCategory("RB01-公共服务")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core12009002Out runService(Core12009002In var1);
}
