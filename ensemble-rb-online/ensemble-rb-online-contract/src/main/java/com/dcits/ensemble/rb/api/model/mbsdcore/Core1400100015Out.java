package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;

@MessageOut
public class Core1400100015Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "产品类型",
      notNull = false,
      length = "20",
      remark = "产品类型",
      maxSize = 20
   )
   private String prodType;
   @V(
      desc = "凭证类型",
      notNull = false,
      length = "10",
      remark = "凭证类型",
      maxSize = 10
   )
   private String docType;
   @V(
      desc = "凭证起始号码",
      notNull = false,
      length = "50",
      remark = "凭证起始号码",
      maxSize = 50
   )
   private String voucherStartNo;

   public String getProdType() {
      return this.prodType;
   }

   public String getDocType() {
      return this.docType;
   }

   public String getVoucherStartNo() {
      return this.voucherStartNo;
   }

   public void setProdType(String prodType) {
      this.prodType = prodType;
   }

   public void setDocType(String docType) {
      this.docType = docType;
   }

   public void setVoucherStartNo(String voucherStartNo) {
      this.voucherStartNo = voucherStartNo;
   }

   public String toString() {
      return "Core1400100015Out(prodType=" + this.getProdType() + ", docType=" + this.getDocType() + ", voucherStartNo=" + this.getVoucherStartNo() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100015Out)) {
         return false;
      } else {
         Core1400100015Out other = (Core1400100015Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            label49: {
               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType == null) {
                     break label49;
                  }
               } else if (this$prodType.equals(other$prodType)) {
                  break label49;
               }

               return false;
            }

            Object this$docType = this.getDocType();
            Object other$docType = other.getDocType();
            if (this$docType == null) {
               if (other$docType != null) {
                  return false;
               }
            } else if (!this$docType.equals(other$docType)) {
               return false;
            }

            Object this$voucherStartNo = this.getVoucherStartNo();
            Object other$voucherStartNo = other.getVoucherStartNo();
            if (this$voucherStartNo == null) {
               if (other$voucherStartNo != null) {
                  return false;
               }
            } else if (!this$voucherStartNo.equals(other$voucherStartNo)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100015Out;
   }
}
