package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12002601In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12002601Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12002601 {
   String URL = "/rb/nfin/agreement/addIntAgreement";


   @ApiRemark("外围系统同步利率审批单，利率审批单功能使用")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "2601"
   )
   @BusinessCategory("1200-非金融")
   @FunctionCategory("PF93-定价利率")
   @ConsumeSys("COS/TLE/ATM")
   @ApiUseStatus("PROJECT-项目")
   Core12002601Out runService(Core12002601In var1);
}
