package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400100128In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400100128In.Body body;

   public Core1400100128In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400100128In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400100128In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100128In)) {
         return false;
      } else {
         Core1400100128In other = (Core1400100128In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100128In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "期次产品预约状态",
         notNull = false,
         length = "1",
         inDesc = "A-已预约,B-已完成,C-已取消 ,D-违约 ,P-预约,S-认购,R-撤销,O-开户",
         remark = "期次产品预约状态",
         maxSize = 1
      )
      private String precontractStatus;
      @V(
         desc = "期次代码",
         notNull = false,
         length = "50",
         remark = "期次代码",
         maxSize = 50
      )
      private String stageCode;
      @V(
         desc = "开始日期",
         notNull = false,
         remark = "开始日期"
      )
      private String startDate;
      @V(
         desc = "结束日期",
         notNull = false,
         remark = "结束日期"
      )
      private String endDate;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "预约号",
         notNull = false,
         length = "50",
         remark = "预约编号",
         maxSize = 50
      )
      private String precontractNo;

      public String getClientNo() {
         return this.clientNo;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getPrecontractStatus() {
         return this.precontractStatus;
      }

      public String getStageCode() {
         return this.stageCode;
      }

      public String getStartDate() {
         return this.startDate;
      }

      public String getEndDate() {
         return this.endDate;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getPrecontractNo() {
         return this.precontractNo;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setPrecontractStatus(String precontractStatus) {
         this.precontractStatus = precontractStatus;
      }

      public void setStageCode(String stageCode) {
         this.stageCode = stageCode;
      }

      public void setStartDate(String startDate) {
         this.startDate = startDate;
      }

      public void setEndDate(String endDate) {
         this.endDate = endDate;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setPrecontractNo(String precontractNo) {
         this.precontractNo = precontractNo;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100128In.Body)) {
            return false;
         } else {
            Core1400100128In.Body other = (Core1400100128In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$clientNo = this.getClientNo();
               Object other$clientNo = other.getClientNo();
               if (this$clientNo == null) {
                  if (other$clientNo != null) {
                     return false;
                  }
               } else if (!this$clientNo.equals(other$clientNo)) {
                  return false;
               }

               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo != null) {
                     return false;
                  }
               } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               label110: {
                  Object this$precontractStatus = this.getPrecontractStatus();
                  Object other$precontractStatus = other.getPrecontractStatus();
                  if (this$precontractStatus == null) {
                     if (other$precontractStatus == null) {
                        break label110;
                     }
                  } else if (this$precontractStatus.equals(other$precontractStatus)) {
                     break label110;
                  }

                  return false;
               }

               label103: {
                  Object this$stageCode = this.getStageCode();
                  Object other$stageCode = other.getStageCode();
                  if (this$stageCode == null) {
                     if (other$stageCode == null) {
                        break label103;
                     }
                  } else if (this$stageCode.equals(other$stageCode)) {
                     break label103;
                  }

                  return false;
               }

               Object this$startDate = this.getStartDate();
               Object other$startDate = other.getStartDate();
               if (this$startDate == null) {
                  if (other$startDate != null) {
                     return false;
                  }
               } else if (!this$startDate.equals(other$startDate)) {
                  return false;
               }

               label89: {
                  Object this$endDate = this.getEndDate();
                  Object other$endDate = other.getEndDate();
                  if (this$endDate == null) {
                     if (other$endDate == null) {
                        break label89;
                     }
                  } else if (this$endDate.equals(other$endDate)) {
                     break label89;
                  }

                  return false;
               }

               label82: {
                  Object this$acctSeqNo = this.getAcctSeqNo();
                  Object other$acctSeqNo = other.getAcctSeqNo();
                  if (this$acctSeqNo == null) {
                     if (other$acctSeqNo == null) {
                        break label82;
                     }
                  } else if (this$acctSeqNo.equals(other$acctSeqNo)) {
                     break label82;
                  }

                  return false;
               }

               Object this$acctCcy = this.getAcctCcy();
               Object other$acctCcy = other.getAcctCcy();
               if (this$acctCcy == null) {
                  if (other$acctCcy != null) {
                     return false;
                  }
               } else if (!this$acctCcy.equals(other$acctCcy)) {
                  return false;
               }

               Object this$precontractNo = this.getPrecontractNo();
               Object other$precontractNo = other.getPrecontractNo();
               if (this$precontractNo == null) {
                  if (other$precontractNo != null) {
                     return false;
                  }
               } else if (!this$precontractNo.equals(other$precontractNo)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100128In.Body;
      }
      public String toString() {
         return "Core1400100128In.Body(clientNo=" + this.getClientNo() + ", baseAcctNo=" + this.getBaseAcctNo() + ", prodType=" + this.getProdType() + ", precontractStatus=" + this.getPrecontractStatus() + ", stageCode=" + this.getStageCode() + ", startDate=" + this.getStartDate() + ", endDate=" + this.getEndDate() + ", acctSeqNo=" + this.getAcctSeqNo() + ", acctCcy=" + this.getAcctCcy() + ", precontractNo=" + this.getPrecontractNo() + ")";
      }
   }
}
