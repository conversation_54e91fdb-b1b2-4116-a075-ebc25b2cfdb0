package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000410In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000410Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000410 {
   String URL = "/rb/inq/unc/inner/acct";


   @ApiRemark("结售汇对应币种、机构内部户查询")
   @ApiDesc("结售汇对应币种、机构内部户查询")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0410"
   )
   Core14000410Out runService(Core14000410In var1);
}
