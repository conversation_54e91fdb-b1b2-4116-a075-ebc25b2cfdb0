package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;

@MessageIn
public class Core1400023405In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400023405In.Body body;

   public Core1400023405In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400023405In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400023405In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400023405In)) {
         return false;
      } else {
         Core1400023405In other = (Core1400023405In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400023405In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "所属机构号",
         notNull = false,
         length = "50",
         remark = "所属机构号",
         maxSize = 50
      )
      private String branch;
      @V(
         desc = "凭证类型",
         notNull = false,
         length = "10",
         remark = "凭证类型",
         maxSize = 10
      )
      private String docType;
      @V(
         desc = "凭证种类",
         notNull = false,
         length = "3",
         inDesc = "PBK-存折,CHK-支票,DCT-存单,CRD-卡,CFT-存款证明,BNK-银行票据,COL-托收票据,DFT-银行汇票,TCH-旅行支票,BAT-银行承兑汇票,CAT-商业承兑汇票,CHQ-支票,OTH-其他,SCV-印鉴",
         remark = "凭证种类",
         maxSize = 3
      )
      private String docClass;
      @V(
         desc = "生效标识",
         notNull = false,
         length = "1",
         inDesc = "Y-是,N-否",
         remark = "生效标识",
         maxSize = 1
      )
      private String tranValidFlag;
      @V(
         desc = "最大限额",
         notNull = false,
         length = "17",
         remark = "最大限额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal limitMaxAmt;
      @V(
         desc = "限额最小金额",
         notNull = false,
         length = "17",
         remark = "限额最小金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal limitMinAmt;

      public String getBranch() {
         return this.branch;
      }

      public String getDocType() {
         return this.docType;
      }

      public String getDocClass() {
         return this.docClass;
      }

      public String getTranValidFlag() {
         return this.tranValidFlag;
      }

      public BigDecimal getLimitMaxAmt() {
         return this.limitMaxAmt;
      }

      public BigDecimal getLimitMinAmt() {
         return this.limitMinAmt;
      }

      public void setBranch(String branch) {
         this.branch = branch;
      }

      public void setDocType(String docType) {
         this.docType = docType;
      }

      public void setDocClass(String docClass) {
         this.docClass = docClass;
      }

      public void setTranValidFlag(String tranValidFlag) {
         this.tranValidFlag = tranValidFlag;
      }

      public void setLimitMaxAmt(BigDecimal limitMaxAmt) {
         this.limitMaxAmt = limitMaxAmt;
      }

      public void setLimitMinAmt(BigDecimal limitMinAmt) {
         this.limitMinAmt = limitMinAmt;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400023405In.Body)) {
            return false;
         } else {
            Core1400023405In.Body other = (Core1400023405In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$branch = this.getBranch();
               Object other$branch = other.getBranch();
               if (this$branch == null) {
                  if (other$branch != null) {
                     return false;
                  }
               } else if (!this$branch.equals(other$branch)) {
                  return false;
               }

               Object this$docType = this.getDocType();
               Object other$docType = other.getDocType();
               if (this$docType == null) {
                  if (other$docType != null) {
                     return false;
                  }
               } else if (!this$docType.equals(other$docType)) {
                  return false;
               }

               Object this$docClass = this.getDocClass();
               Object other$docClass = other.getDocClass();
               if (this$docClass == null) {
                  if (other$docClass != null) {
                     return false;
                  }
               } else if (!this$docClass.equals(other$docClass)) {
                  return false;
               }

               label62: {
                  Object this$tranValidFlag = this.getTranValidFlag();
                  Object other$tranValidFlag = other.getTranValidFlag();
                  if (this$tranValidFlag == null) {
                     if (other$tranValidFlag == null) {
                        break label62;
                     }
                  } else if (this$tranValidFlag.equals(other$tranValidFlag)) {
                     break label62;
                  }

                  return false;
               }

               label55: {
                  Object this$limitMaxAmt = this.getLimitMaxAmt();
                  Object other$limitMaxAmt = other.getLimitMaxAmt();
                  if (this$limitMaxAmt == null) {
                     if (other$limitMaxAmt == null) {
                        break label55;
                     }
                  } else if (this$limitMaxAmt.equals(other$limitMaxAmt)) {
                     break label55;
                  }

                  return false;
               }

               Object this$limitMinAmt = this.getLimitMinAmt();
               Object other$limitMinAmt = other.getLimitMinAmt();
               if (this$limitMinAmt == null) {
                  if (other$limitMinAmt != null) {
                     return false;
                  }
               } else if (!this$limitMinAmt.equals(other$limitMinAmt)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400023405In.Body;
      }
      public String toString() {
         return "Core1400023405In.Body(branch=" + this.getBranch() + ", docType=" + this.getDocType() + ", docClass=" + this.getDocClass() + ", tranValidFlag=" + this.getTranValidFlag() + ", limitMaxAmt=" + this.getLimitMaxAmt() + ", limitMinAmt=" + this.getLimitMinAmt() + ")";
      }
   }
}
