package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;

@MessageOut
public class Core1400056111Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "累计额度金额",
      notNull = false,
      length = "17",
      remark = "累计额度金额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal limitAmt;
   @V(
      desc = "免密状态",
      notNull = false,
      length = "1",
      remark = "免密状态",
      maxSize = 1
   )
   private String noPasswordStatus;
   @V(
      desc = "法人",
      notNull = false,
      length = "20",
      remark = "法人",
      maxSize = 20
   )
   private String company;
   @V(
      desc = "账户单笔交易限额",
      notNull = false,
      length = "17",
      remark = "账户单笔交易限额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal singleLimit;
   @V(
      desc = "当日可用限额",
      notNull = false,
      length = "17",
      remark = "当日可用限额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal dayLimitAvail;

   public BigDecimal getLimitAmt() {
      return this.limitAmt;
   }

   public String getNoPasswordStatus() {
      return this.noPasswordStatus;
   }

   public String getCompany() {
      return this.company;
   }

   public BigDecimal getSingleLimit() {
      return this.singleLimit;
   }

   public BigDecimal getDayLimitAvail() {
      return this.dayLimitAvail;
   }

   public void setLimitAmt(BigDecimal limitAmt) {
      this.limitAmt = limitAmt;
   }

   public void setNoPasswordStatus(String noPasswordStatus) {
      this.noPasswordStatus = noPasswordStatus;
   }

   public void setCompany(String company) {
      this.company = company;
   }

   public void setSingleLimit(BigDecimal singleLimit) {
      this.singleLimit = singleLimit;
   }

   public void setDayLimitAvail(BigDecimal dayLimitAvail) {
      this.dayLimitAvail = dayLimitAvail;
   }

   public String toString() {
      return "Core1400056111Out(limitAmt=" + this.getLimitAmt() + ", noPasswordStatus=" + this.getNoPasswordStatus() + ", company=" + this.getCompany() + ", singleLimit=" + this.getSingleLimit() + ", dayLimitAvail=" + this.getDayLimitAvail() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400056111Out)) {
         return false;
      } else {
         Core1400056111Out other = (Core1400056111Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            label73: {
               Object this$limitAmt = this.getLimitAmt();
               Object other$limitAmt = other.getLimitAmt();
               if (this$limitAmt == null) {
                  if (other$limitAmt == null) {
                     break label73;
                  }
               } else if (this$limitAmt.equals(other$limitAmt)) {
                  break label73;
               }

               return false;
            }

            Object this$noPasswordStatus = this.getNoPasswordStatus();
            Object other$noPasswordStatus = other.getNoPasswordStatus();
            if (this$noPasswordStatus == null) {
               if (other$noPasswordStatus != null) {
                  return false;
               }
            } else if (!this$noPasswordStatus.equals(other$noPasswordStatus)) {
               return false;
            }

            label59: {
               Object this$company = this.getCompany();
               Object other$company = other.getCompany();
               if (this$company == null) {
                  if (other$company == null) {
                     break label59;
                  }
               } else if (this$company.equals(other$company)) {
                  break label59;
               }

               return false;
            }

            Object this$singleLimit = this.getSingleLimit();
            Object other$singleLimit = other.getSingleLimit();
            if (this$singleLimit == null) {
               if (other$singleLimit != null) {
                  return false;
               }
            } else if (!this$singleLimit.equals(other$singleLimit)) {
               return false;
            }

            Object this$dayLimitAvail = this.getDayLimitAvail();
            Object other$dayLimitAvail = other.getDayLimitAvail();
            if (this$dayLimitAvail == null) {
               if (other$dayLimitAvail != null) {
                  return false;
               }
            } else if (!this$dayLimitAvail.equals(other$dayLimitAvail)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400056111Out;
   }
}
