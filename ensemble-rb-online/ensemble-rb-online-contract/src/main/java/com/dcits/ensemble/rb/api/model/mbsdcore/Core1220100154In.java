package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1220100154In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1220100154In.Body body;

   public Core1220100154In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1220100154In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1220100154In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1220100154In)) {
         return false;
      } else {
         Core1220100154In other = (Core1220100154In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1220100154In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "文件路径",
         notNull = true,
         length = "200",
         remark = "文件路径",
         maxSize = 200
      )
      private String filePath;

      public String getFilePath() {
         return this.filePath;
      }

      public void setFilePath(String filePath) {
         this.filePath = filePath;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1220100154In.Body)) {
            return false;
         } else {
            Core1220100154In.Body other = (Core1220100154In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$filePath = this.getFilePath();
               Object other$filePath = other.getFilePath();
               if (this$filePath == null) {
                  if (other$filePath != null) {
                     return false;
                  }
               } else if (!this$filePath.equals(other$filePath)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1220100154In.Body;
      }
      public String toString() {
         return "Core1220100154In.Body(filePath=" + this.getFilePath() + ")";
      }
   }
}
