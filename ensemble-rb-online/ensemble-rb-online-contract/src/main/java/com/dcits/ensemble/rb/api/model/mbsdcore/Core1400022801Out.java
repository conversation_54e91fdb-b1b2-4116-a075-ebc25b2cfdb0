package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400022801Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400022801Out.StageArray> stageArray;

   public List<Core1400022801Out.StageArray> getStageArray() {
      return this.stageArray;
   }

   public void setStageArray(List<Core1400022801Out.StageArray> stageArray) {
      this.stageArray = stageArray;
   }

   public String toString() {
      return "Core1400022801Out(stageArray=" + this.getStageArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400022801Out)) {
         return false;
      } else {
         Core1400022801Out other = (Core1400022801Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$stageArray = this.getStageArray();
            Object other$stageArray = other.getStageArray();
            if (this$stageArray == null) {
               if (other$stageArray != null) {
                  return false;
               }
            } else if (!this$stageArray.equals(other$stageArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400022801Out;
   }
   public static class StageArray {
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "存期期限",
         notNull = false,
         length = "5",
         remark = "期限",
         maxSize = 5
      )
      private String term;
      @V(
         desc = "期限类型",
         notNull = false,
         length = "1",
         remark = "期限类型",
         maxSize = 1
      )
      private String termType;
      @V(
         desc = "发行起始日期",
         notNull = false,
         remark = "发行起始日期"
      )
      private String issueStartDate;
      @V(
         desc = "发行终止日期",
         notNull = false,
         remark = "发行终止日期"
      )
      private String issueEndDate;
      @V(
         desc = "期次发行金额",
         notNull = false,
         length = "17",
         remark = "期次发行金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal issueAmt;
      @V(
         desc = "协议留存金额",
         notNull = false,
         length = "17",
         remark = "协议留存金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal remainAmt;
      @V(
         desc = "最大金额",
         notNull = false,
         length = "17",
         remark = "最大金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal maxAmt;
      @V(
         desc = "预约金额",
         notNull = false,
         length = "17",
         remark = "预约金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal precontractAmt;
      @V(
         desc = "最小金额",
         notNull = false,
         length = "17",
         remark = "最小金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal minAmt;
      @V(
         desc = "剩余额度",
         notNull = false,
         length = "17",
         remark = "剩余额度",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal leaveLimit;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "取息频率",
         notNull = false,
         length = "5",
         remark = "取息频率",
         maxSize = 5
      )
      private String getIntFreq;
      @V(
         desc = "计息类型",
         notNull = false,
         length = "1",
         remark = "计息类型",
         maxSize = 1
      )
      private String intCalcType;
      @V(
         desc = "付息方式",
         notNull = false,
         length = "3",
         remark = "付息方式",
         maxSize = 3
      )
      private String payIntType;
      @V(
         desc = "执行利率",
         notNull = false,
         length = "15",
         remark = "执行利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal realRate;
      @V(
         desc = "转账标志",
         notNull = false,
         length = "1",
         remark = "转账标志",
         maxSize = 1
      )
      private String transferFlag;
      @V(
         desc = "是否允许提前支取",
         notNull = false,
         length = "1",
         remark = "是否允许提前支取",
         maxSize = 1
      )
      private String preWithdrawFlag;
      @V(
         desc = "销售方式",
         notNull = false,
         length = "1",
         remark = "销售方式",
         maxSize = 1
      )
      private String saleType;
      @V(
         desc = "是否可赎回",
         notNull = false,
         length = "1",
         remark = "是否可赎回",
         maxSize = 1
      )
      private String redemptionFlag;
      @V(
         desc = "配售方式",
         notNull = false,
         length = "1",
         remark = "配售方式",
         maxSize = 1
      )
      private String rationType;
      @V(
         desc = "期次描述",
         notNull = false,
         length = "200",
         remark = "期次描述",
         maxSize = 200
      )
      private String stageCodeDesc;
      @V(
         desc = "部提次数",
         notNull = false,
         length = "5",
         remark = "部提次数"
      )
      private Integer partWithdrawNum;
      @V(
         desc = "利率重置频率",
         notNull = false,
         length = "5",
         remark = "期次定义利率重置频率",
         maxSize = 5
      )
      private String resetIntFreq;
      @V(
         desc = "发行年度",
         notNull = false,
         length = "5",
         remark = "发行年度",
         maxSize = 5
      )
      private String issueYear;
      @V(
         desc = "期次代码",
         notNull = false,
         length = "50",
         remark = "期次代码",
         maxSize = 50
      )
      private String stageCode;
      @V(
         desc = "期次状态",
         notNull = false,
         length = "2",
         remark = "期次状态",
         maxSize = 2
      )
      private String stageStatus;
      @V(
         desc = "总额度",
         notNull = false,
         length = "17",
         remark = "总额度",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal totalLimit;
      @V(
         desc = "可用预约额度",
         notNull = false,
         length = "17",
         remark = "可用预约额度",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal precontractLeaveLimit;
      @V(
         desc = "起售时间",
         notNull = false,
         length = "26",
         remark = "起售时间",
         maxSize = 26
      )
      private String saleStartTime;
      @V(
         desc = "止售时间",
         notNull = false,
         length = "26",
         remark = "止售时间",
         maxSize = 26
      )
      private String saleEndTime;
      @V(
         desc = "预约结束时间",
         notNull = false,
         length = "26",
         remark = "预约结束时间",
         maxSize = 26
      )
      private String precontractEndTime;
      @V(
         desc = "预约开始时间",
         notNull = false,
         length = "26",
         remark = "预约开始时间",
         maxSize = 26
      )
      private String precontractStartTime;
      @V(
         desc = "数组",
         notNull = false,
         remark = "数组"
      )
      private List<Core1400022801Out.StageArray.IntArray> intArray;
      @V(
         desc = "法人",
         notNull = false,
         length = "20",
         remark = "法人",
         maxSize = 20
      )
      private String company;
      @V(
         desc = "最小留存金额",
         notNull = false,
         length = "17",
         remark = "最小留存金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal keepMinBal;
      @V(
         desc = "最小变动金额",
         notNull = false,
         length = "17",
         remark = "最小变动金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal changeMinAmt;
      @V(
         desc = "单次最小支取金额",
         notNull = false,
         length = "17",
         remark = "单次最小支取金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal sgMinAmt;
      @V(
         desc = "出售分行或者出售机构",
         notNull = false,
         length = "500",
         remark = "出售分行或者出售机构，多可以是一个或者多个，定义多个时，需要用分隔符 | 进行分开存储",
         maxSize = 500
      )
      private String sellBranch;
      @V(
         desc = "自动结清标志",
         notNull = false,
         length = "1",
         remark = "自动结清标志",
         maxSize = 1
      )
      private String autoSettleFlag;
      @V(
         desc = "是否白名单发售",
         notNull = false,
         length = "1",
         remark = "是否白名单发售Y-是，N-否",
         maxSize = 1
      )
      private String whiteSellFlag;
      @V(
         desc = "可售渠道",
         notNull = false,
         length = "100",
         remark = "可售渠道",
         maxSize = 100
      )
      private String onSaleChannel;
      @V(
         desc = "数组",
         notNull = false,
         remark = "数组"
      )
      private List<Core1400022801Out.StageArray.ClientListArray> clientListArray;
      @V(
         desc = "支持组合购买方式",
         notNull = false,
         length = "1",
         remark = "1-单独购买2-组合购买3-单买与组合买",
         maxSize = 1
      )
      private String allowBuyWayCd;

      public String getProdType() {
         return this.prodType;
      }

      public String getTerm() {
         return this.term;
      }

      public String getTermType() {
         return this.termType;
      }

      public String getIssueStartDate() {
         return this.issueStartDate;
      }

      public String getIssueEndDate() {
         return this.issueEndDate;
      }

      public BigDecimal getIssueAmt() {
         return this.issueAmt;
      }

      public BigDecimal getRemainAmt() {
         return this.remainAmt;
      }

      public BigDecimal getMaxAmt() {
         return this.maxAmt;
      }

      public BigDecimal getPrecontractAmt() {
         return this.precontractAmt;
      }

      public BigDecimal getMinAmt() {
         return this.minAmt;
      }

      public BigDecimal getLeaveLimit() {
         return this.leaveLimit;
      }

      public String getCcy() {
         return this.ccy;
      }

      public String getGetIntFreq() {
         return this.getIntFreq;
      }

      public String getIntCalcType() {
         return this.intCalcType;
      }

      public String getPayIntType() {
         return this.payIntType;
      }

      public BigDecimal getRealRate() {
         return this.realRate;
      }

      public String getTransferFlag() {
         return this.transferFlag;
      }

      public String getPreWithdrawFlag() {
         return this.preWithdrawFlag;
      }

      public String getSaleType() {
         return this.saleType;
      }

      public String getRedemptionFlag() {
         return this.redemptionFlag;
      }

      public String getRationType() {
         return this.rationType;
      }

      public String getStageCodeDesc() {
         return this.stageCodeDesc;
      }

      public Integer getPartWithdrawNum() {
         return this.partWithdrawNum;
      }

      public String getResetIntFreq() {
         return this.resetIntFreq;
      }

      public String getIssueYear() {
         return this.issueYear;
      }

      public String getStageCode() {
         return this.stageCode;
      }

      public String getStageStatus() {
         return this.stageStatus;
      }

      public BigDecimal getTotalLimit() {
         return this.totalLimit;
      }

      public BigDecimal getPrecontractLeaveLimit() {
         return this.precontractLeaveLimit;
      }

      public String getSaleStartTime() {
         return this.saleStartTime;
      }

      public String getSaleEndTime() {
         return this.saleEndTime;
      }

      public String getPrecontractEndTime() {
         return this.precontractEndTime;
      }

      public String getPrecontractStartTime() {
         return this.precontractStartTime;
      }

      public List<Core1400022801Out.StageArray.IntArray> getIntArray() {
         return this.intArray;
      }

      public String getCompany() {
         return this.company;
      }

      public BigDecimal getKeepMinBal() {
         return this.keepMinBal;
      }

      public BigDecimal getChangeMinAmt() {
         return this.changeMinAmt;
      }

      public BigDecimal getSgMinAmt() {
         return this.sgMinAmt;
      }

      public String getSellBranch() {
         return this.sellBranch;
      }

      public String getAutoSettleFlag() {
         return this.autoSettleFlag;
      }

      public String getWhiteSellFlag() {
         return this.whiteSellFlag;
      }

      public String getOnSaleChannel() {
         return this.onSaleChannel;
      }

      public List<Core1400022801Out.StageArray.ClientListArray> getClientListArray() {
         return this.clientListArray;
      }

      public String getAllowBuyWayCd() {
         return this.allowBuyWayCd;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setTerm(String term) {
         this.term = term;
      }

      public void setTermType(String termType) {
         this.termType = termType;
      }

      public void setIssueStartDate(String issueStartDate) {
         this.issueStartDate = issueStartDate;
      }

      public void setIssueEndDate(String issueEndDate) {
         this.issueEndDate = issueEndDate;
      }

      public void setIssueAmt(BigDecimal issueAmt) {
         this.issueAmt = issueAmt;
      }

      public void setRemainAmt(BigDecimal remainAmt) {
         this.remainAmt = remainAmt;
      }

      public void setMaxAmt(BigDecimal maxAmt) {
         this.maxAmt = maxAmt;
      }

      public void setPrecontractAmt(BigDecimal precontractAmt) {
         this.precontractAmt = precontractAmt;
      }

      public void setMinAmt(BigDecimal minAmt) {
         this.minAmt = minAmt;
      }

      public void setLeaveLimit(BigDecimal leaveLimit) {
         this.leaveLimit = leaveLimit;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setGetIntFreq(String getIntFreq) {
         this.getIntFreq = getIntFreq;
      }

      public void setIntCalcType(String intCalcType) {
         this.intCalcType = intCalcType;
      }

      public void setPayIntType(String payIntType) {
         this.payIntType = payIntType;
      }

      public void setRealRate(BigDecimal realRate) {
         this.realRate = realRate;
      }

      public void setTransferFlag(String transferFlag) {
         this.transferFlag = transferFlag;
      }

      public void setPreWithdrawFlag(String preWithdrawFlag) {
         this.preWithdrawFlag = preWithdrawFlag;
      }

      public void setSaleType(String saleType) {
         this.saleType = saleType;
      }

      public void setRedemptionFlag(String redemptionFlag) {
         this.redemptionFlag = redemptionFlag;
      }

      public void setRationType(String rationType) {
         this.rationType = rationType;
      }

      public void setStageCodeDesc(String stageCodeDesc) {
         this.stageCodeDesc = stageCodeDesc;
      }

      public void setPartWithdrawNum(Integer partWithdrawNum) {
         this.partWithdrawNum = partWithdrawNum;
      }

      public void setResetIntFreq(String resetIntFreq) {
         this.resetIntFreq = resetIntFreq;
      }

      public void setIssueYear(String issueYear) {
         this.issueYear = issueYear;
      }

      public void setStageCode(String stageCode) {
         this.stageCode = stageCode;
      }

      public void setStageStatus(String stageStatus) {
         this.stageStatus = stageStatus;
      }

      public void setTotalLimit(BigDecimal totalLimit) {
         this.totalLimit = totalLimit;
      }

      public void setPrecontractLeaveLimit(BigDecimal precontractLeaveLimit) {
         this.precontractLeaveLimit = precontractLeaveLimit;
      }

      public void setSaleStartTime(String saleStartTime) {
         this.saleStartTime = saleStartTime;
      }

      public void setSaleEndTime(String saleEndTime) {
         this.saleEndTime = saleEndTime;
      }

      public void setPrecontractEndTime(String precontractEndTime) {
         this.precontractEndTime = precontractEndTime;
      }

      public void setPrecontractStartTime(String precontractStartTime) {
         this.precontractStartTime = precontractStartTime;
      }

      public void setIntArray(List<Core1400022801Out.StageArray.IntArray> intArray) {
         this.intArray = intArray;
      }

      public void setCompany(String company) {
         this.company = company;
      }

      public void setKeepMinBal(BigDecimal keepMinBal) {
         this.keepMinBal = keepMinBal;
      }

      public void setChangeMinAmt(BigDecimal changeMinAmt) {
         this.changeMinAmt = changeMinAmt;
      }

      public void setSgMinAmt(BigDecimal sgMinAmt) {
         this.sgMinAmt = sgMinAmt;
      }

      public void setSellBranch(String sellBranch) {
         this.sellBranch = sellBranch;
      }

      public void setAutoSettleFlag(String autoSettleFlag) {
         this.autoSettleFlag = autoSettleFlag;
      }

      public void setWhiteSellFlag(String whiteSellFlag) {
         this.whiteSellFlag = whiteSellFlag;
      }

      public void setOnSaleChannel(String onSaleChannel) {
         this.onSaleChannel = onSaleChannel;
      }

      public void setClientListArray(List<Core1400022801Out.StageArray.ClientListArray> clientListArray) {
         this.clientListArray = clientListArray;
      }

      public void setAllowBuyWayCd(String allowBuyWayCd) {
         this.allowBuyWayCd = allowBuyWayCd;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400022801Out.StageArray)) {
            return false;
         } else {
            Core1400022801Out.StageArray other = (Core1400022801Out.StageArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label539: {
                  Object this$prodType = this.getProdType();
                  Object other$prodType = other.getProdType();
                  if (this$prodType == null) {
                     if (other$prodType == null) {
                        break label539;
                     }
                  } else if (this$prodType.equals(other$prodType)) {
                     break label539;
                  }

                  return false;
               }

               Object this$term = this.getTerm();
               Object other$term = other.getTerm();
               if (this$term == null) {
                  if (other$term != null) {
                     return false;
                  }
               } else if (!this$term.equals(other$term)) {
                  return false;
               }

               Object this$termType = this.getTermType();
               Object other$termType = other.getTermType();
               if (this$termType == null) {
                  if (other$termType != null) {
                     return false;
                  }
               } else if (!this$termType.equals(other$termType)) {
                  return false;
               }

               label518: {
                  Object this$issueStartDate = this.getIssueStartDate();
                  Object other$issueStartDate = other.getIssueStartDate();
                  if (this$issueStartDate == null) {
                     if (other$issueStartDate == null) {
                        break label518;
                     }
                  } else if (this$issueStartDate.equals(other$issueStartDate)) {
                     break label518;
                  }

                  return false;
               }

               label511: {
                  Object this$issueEndDate = this.getIssueEndDate();
                  Object other$issueEndDate = other.getIssueEndDate();
                  if (this$issueEndDate == null) {
                     if (other$issueEndDate == null) {
                        break label511;
                     }
                  } else if (this$issueEndDate.equals(other$issueEndDate)) {
                     break label511;
                  }

                  return false;
               }

               label504: {
                  Object this$issueAmt = this.getIssueAmt();
                  Object other$issueAmt = other.getIssueAmt();
                  if (this$issueAmt == null) {
                     if (other$issueAmt == null) {
                        break label504;
                     }
                  } else if (this$issueAmt.equals(other$issueAmt)) {
                     break label504;
                  }

                  return false;
               }

               Object this$remainAmt = this.getRemainAmt();
               Object other$remainAmt = other.getRemainAmt();
               if (this$remainAmt == null) {
                  if (other$remainAmt != null) {
                     return false;
                  }
               } else if (!this$remainAmt.equals(other$remainAmt)) {
                  return false;
               }

               label490: {
                  Object this$maxAmt = this.getMaxAmt();
                  Object other$maxAmt = other.getMaxAmt();
                  if (this$maxAmt == null) {
                     if (other$maxAmt == null) {
                        break label490;
                     }
                  } else if (this$maxAmt.equals(other$maxAmt)) {
                     break label490;
                  }

                  return false;
               }

               Object this$precontractAmt = this.getPrecontractAmt();
               Object other$precontractAmt = other.getPrecontractAmt();
               if (this$precontractAmt == null) {
                  if (other$precontractAmt != null) {
                     return false;
                  }
               } else if (!this$precontractAmt.equals(other$precontractAmt)) {
                  return false;
               }

               label476: {
                  Object this$minAmt = this.getMinAmt();
                  Object other$minAmt = other.getMinAmt();
                  if (this$minAmt == null) {
                     if (other$minAmt == null) {
                        break label476;
                     }
                  } else if (this$minAmt.equals(other$minAmt)) {
                     break label476;
                  }

                  return false;
               }

               Object this$leaveLimit = this.getLeaveLimit();
               Object other$leaveLimit = other.getLeaveLimit();
               if (this$leaveLimit == null) {
                  if (other$leaveLimit != null) {
                     return false;
                  }
               } else if (!this$leaveLimit.equals(other$leaveLimit)) {
                  return false;
               }

               Object this$ccy = this.getCcy();
               Object other$ccy = other.getCcy();
               if (this$ccy == null) {
                  if (other$ccy != null) {
                     return false;
                  }
               } else if (!this$ccy.equals(other$ccy)) {
                  return false;
               }

               label455: {
                  Object this$getIntFreq = this.getGetIntFreq();
                  Object other$getIntFreq = other.getGetIntFreq();
                  if (this$getIntFreq == null) {
                     if (other$getIntFreq == null) {
                        break label455;
                     }
                  } else if (this$getIntFreq.equals(other$getIntFreq)) {
                     break label455;
                  }

                  return false;
               }

               label448: {
                  Object this$intCalcType = this.getIntCalcType();
                  Object other$intCalcType = other.getIntCalcType();
                  if (this$intCalcType == null) {
                     if (other$intCalcType == null) {
                        break label448;
                     }
                  } else if (this$intCalcType.equals(other$intCalcType)) {
                     break label448;
                  }

                  return false;
               }

               Object this$payIntType = this.getPayIntType();
               Object other$payIntType = other.getPayIntType();
               if (this$payIntType == null) {
                  if (other$payIntType != null) {
                     return false;
                  }
               } else if (!this$payIntType.equals(other$payIntType)) {
                  return false;
               }

               Object this$realRate = this.getRealRate();
               Object other$realRate = other.getRealRate();
               if (this$realRate == null) {
                  if (other$realRate != null) {
                     return false;
                  }
               } else if (!this$realRate.equals(other$realRate)) {
                  return false;
               }

               label427: {
                  Object this$transferFlag = this.getTransferFlag();
                  Object other$transferFlag = other.getTransferFlag();
                  if (this$transferFlag == null) {
                     if (other$transferFlag == null) {
                        break label427;
                     }
                  } else if (this$transferFlag.equals(other$transferFlag)) {
                     break label427;
                  }

                  return false;
               }

               Object this$preWithdrawFlag = this.getPreWithdrawFlag();
               Object other$preWithdrawFlag = other.getPreWithdrawFlag();
               if (this$preWithdrawFlag == null) {
                  if (other$preWithdrawFlag != null) {
                     return false;
                  }
               } else if (!this$preWithdrawFlag.equals(other$preWithdrawFlag)) {
                  return false;
               }

               Object this$saleType = this.getSaleType();
               Object other$saleType = other.getSaleType();
               if (this$saleType == null) {
                  if (other$saleType != null) {
                     return false;
                  }
               } else if (!this$saleType.equals(other$saleType)) {
                  return false;
               }

               label406: {
                  Object this$redemptionFlag = this.getRedemptionFlag();
                  Object other$redemptionFlag = other.getRedemptionFlag();
                  if (this$redemptionFlag == null) {
                     if (other$redemptionFlag == null) {
                        break label406;
                     }
                  } else if (this$redemptionFlag.equals(other$redemptionFlag)) {
                     break label406;
                  }

                  return false;
               }

               label399: {
                  Object this$rationType = this.getRationType();
                  Object other$rationType = other.getRationType();
                  if (this$rationType == null) {
                     if (other$rationType == null) {
                        break label399;
                     }
                  } else if (this$rationType.equals(other$rationType)) {
                     break label399;
                  }

                  return false;
               }

               label392: {
                  Object this$stageCodeDesc = this.getStageCodeDesc();
                  Object other$stageCodeDesc = other.getStageCodeDesc();
                  if (this$stageCodeDesc == null) {
                     if (other$stageCodeDesc == null) {
                        break label392;
                     }
                  } else if (this$stageCodeDesc.equals(other$stageCodeDesc)) {
                     break label392;
                  }

                  return false;
               }

               Object this$partWithdrawNum = this.getPartWithdrawNum();
               Object other$partWithdrawNum = other.getPartWithdrawNum();
               if (this$partWithdrawNum == null) {
                  if (other$partWithdrawNum != null) {
                     return false;
                  }
               } else if (!this$partWithdrawNum.equals(other$partWithdrawNum)) {
                  return false;
               }

               label378: {
                  Object this$resetIntFreq = this.getResetIntFreq();
                  Object other$resetIntFreq = other.getResetIntFreq();
                  if (this$resetIntFreq == null) {
                     if (other$resetIntFreq == null) {
                        break label378;
                     }
                  } else if (this$resetIntFreq.equals(other$resetIntFreq)) {
                     break label378;
                  }

                  return false;
               }

               Object this$issueYear = this.getIssueYear();
               Object other$issueYear = other.getIssueYear();
               if (this$issueYear == null) {
                  if (other$issueYear != null) {
                     return false;
                  }
               } else if (!this$issueYear.equals(other$issueYear)) {
                  return false;
               }

               label364: {
                  Object this$stageCode = this.getStageCode();
                  Object other$stageCode = other.getStageCode();
                  if (this$stageCode == null) {
                     if (other$stageCode == null) {
                        break label364;
                     }
                  } else if (this$stageCode.equals(other$stageCode)) {
                     break label364;
                  }

                  return false;
               }

               Object this$stageStatus = this.getStageStatus();
               Object other$stageStatus = other.getStageStatus();
               if (this$stageStatus == null) {
                  if (other$stageStatus != null) {
                     return false;
                  }
               } else if (!this$stageStatus.equals(other$stageStatus)) {
                  return false;
               }

               Object this$totalLimit = this.getTotalLimit();
               Object other$totalLimit = other.getTotalLimit();
               if (this$totalLimit == null) {
                  if (other$totalLimit != null) {
                     return false;
                  }
               } else if (!this$totalLimit.equals(other$totalLimit)) {
                  return false;
               }

               label343: {
                  Object this$precontractLeaveLimit = this.getPrecontractLeaveLimit();
                  Object other$precontractLeaveLimit = other.getPrecontractLeaveLimit();
                  if (this$precontractLeaveLimit == null) {
                     if (other$precontractLeaveLimit == null) {
                        break label343;
                     }
                  } else if (this$precontractLeaveLimit.equals(other$precontractLeaveLimit)) {
                     break label343;
                  }

                  return false;
               }

               label336: {
                  Object this$saleStartTime = this.getSaleStartTime();
                  Object other$saleStartTime = other.getSaleStartTime();
                  if (this$saleStartTime == null) {
                     if (other$saleStartTime == null) {
                        break label336;
                     }
                  } else if (this$saleStartTime.equals(other$saleStartTime)) {
                     break label336;
                  }

                  return false;
               }

               Object this$saleEndTime = this.getSaleEndTime();
               Object other$saleEndTime = other.getSaleEndTime();
               if (this$saleEndTime == null) {
                  if (other$saleEndTime != null) {
                     return false;
                  }
               } else if (!this$saleEndTime.equals(other$saleEndTime)) {
                  return false;
               }

               Object this$precontractEndTime = this.getPrecontractEndTime();
               Object other$precontractEndTime = other.getPrecontractEndTime();
               if (this$precontractEndTime == null) {
                  if (other$precontractEndTime != null) {
                     return false;
                  }
               } else if (!this$precontractEndTime.equals(other$precontractEndTime)) {
                  return false;
               }

               label315: {
                  Object this$precontractStartTime = this.getPrecontractStartTime();
                  Object other$precontractStartTime = other.getPrecontractStartTime();
                  if (this$precontractStartTime == null) {
                     if (other$precontractStartTime == null) {
                        break label315;
                     }
                  } else if (this$precontractStartTime.equals(other$precontractStartTime)) {
                     break label315;
                  }

                  return false;
               }

               Object this$intArray = this.getIntArray();
               Object other$intArray = other.getIntArray();
               if (this$intArray == null) {
                  if (other$intArray != null) {
                     return false;
                  }
               } else if (!this$intArray.equals(other$intArray)) {
                  return false;
               }

               Object this$company = this.getCompany();
               Object other$company = other.getCompany();
               if (this$company == null) {
                  if (other$company != null) {
                     return false;
                  }
               } else if (!this$company.equals(other$company)) {
                  return false;
               }

               label294: {
                  Object this$keepMinBal = this.getKeepMinBal();
                  Object other$keepMinBal = other.getKeepMinBal();
                  if (this$keepMinBal == null) {
                     if (other$keepMinBal == null) {
                        break label294;
                     }
                  } else if (this$keepMinBal.equals(other$keepMinBal)) {
                     break label294;
                  }

                  return false;
               }

               label287: {
                  Object this$changeMinAmt = this.getChangeMinAmt();
                  Object other$changeMinAmt = other.getChangeMinAmt();
                  if (this$changeMinAmt == null) {
                     if (other$changeMinAmt == null) {
                        break label287;
                     }
                  } else if (this$changeMinAmt.equals(other$changeMinAmt)) {
                     break label287;
                  }

                  return false;
               }

               label280: {
                  Object this$sgMinAmt = this.getSgMinAmt();
                  Object other$sgMinAmt = other.getSgMinAmt();
                  if (this$sgMinAmt == null) {
                     if (other$sgMinAmt == null) {
                        break label280;
                     }
                  } else if (this$sgMinAmt.equals(other$sgMinAmt)) {
                     break label280;
                  }

                  return false;
               }

               Object this$sellBranch = this.getSellBranch();
               Object other$sellBranch = other.getSellBranch();
               if (this$sellBranch == null) {
                  if (other$sellBranch != null) {
                     return false;
                  }
               } else if (!this$sellBranch.equals(other$sellBranch)) {
                  return false;
               }

               label266: {
                  Object this$autoSettleFlag = this.getAutoSettleFlag();
                  Object other$autoSettleFlag = other.getAutoSettleFlag();
                  if (this$autoSettleFlag == null) {
                     if (other$autoSettleFlag == null) {
                        break label266;
                     }
                  } else if (this$autoSettleFlag.equals(other$autoSettleFlag)) {
                     break label266;
                  }

                  return false;
               }

               Object this$whiteSellFlag = this.getWhiteSellFlag();
               Object other$whiteSellFlag = other.getWhiteSellFlag();
               if (this$whiteSellFlag == null) {
                  if (other$whiteSellFlag != null) {
                     return false;
                  }
               } else if (!this$whiteSellFlag.equals(other$whiteSellFlag)) {
                  return false;
               }

               label252: {
                  Object this$onSaleChannel = this.getOnSaleChannel();
                  Object other$onSaleChannel = other.getOnSaleChannel();
                  if (this$onSaleChannel == null) {
                     if (other$onSaleChannel == null) {
                        break label252;
                     }
                  } else if (this$onSaleChannel.equals(other$onSaleChannel)) {
                     break label252;
                  }

                  return false;
               }

               Object this$clientListArray = this.getClientListArray();
               Object other$clientListArray = other.getClientListArray();
               if (this$clientListArray == null) {
                  if (other$clientListArray != null) {
                     return false;
                  }
               } else if (!this$clientListArray.equals(other$clientListArray)) {
                  return false;
               }

               Object this$allowBuyWayCd = this.getAllowBuyWayCd();
               Object other$allowBuyWayCd = other.getAllowBuyWayCd();
               if (this$allowBuyWayCd == null) {
                  if (other$allowBuyWayCd != null) {
                     return false;
                  }
               } else if (!this$allowBuyWayCd.equals(other$allowBuyWayCd)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400022801Out.StageArray;
      }
      public String toString() {
         return "Core1400022801Out.StageArray(prodType=" + this.getProdType() + ", term=" + this.getTerm() + ", termType=" + this.getTermType() + ", issueStartDate=" + this.getIssueStartDate() + ", issueEndDate=" + this.getIssueEndDate() + ", issueAmt=" + this.getIssueAmt() + ", remainAmt=" + this.getRemainAmt() + ", maxAmt=" + this.getMaxAmt() + ", precontractAmt=" + this.getPrecontractAmt() + ", minAmt=" + this.getMinAmt() + ", leaveLimit=" + this.getLeaveLimit() + ", ccy=" + this.getCcy() + ", getIntFreq=" + this.getGetIntFreq() + ", intCalcType=" + this.getIntCalcType() + ", payIntType=" + this.getPayIntType() + ", realRate=" + this.getRealRate() + ", transferFlag=" + this.getTransferFlag() + ", preWithdrawFlag=" + this.getPreWithdrawFlag() + ", saleType=" + this.getSaleType() + ", redemptionFlag=" + this.getRedemptionFlag() + ", rationType=" + this.getRationType() + ", stageCodeDesc=" + this.getStageCodeDesc() + ", partWithdrawNum=" + this.getPartWithdrawNum() + ", resetIntFreq=" + this.getResetIntFreq() + ", issueYear=" + this.getIssueYear() + ", stageCode=" + this.getStageCode() + ", stageStatus=" + this.getStageStatus() + ", totalLimit=" + this.getTotalLimit() + ", precontractLeaveLimit=" + this.getPrecontractLeaveLimit() + ", saleStartTime=" + this.getSaleStartTime() + ", saleEndTime=" + this.getSaleEndTime() + ", precontractEndTime=" + this.getPrecontractEndTime() + ", precontractStartTime=" + this.getPrecontractStartTime() + ", intArray=" + this.getIntArray() + ", company=" + this.getCompany() + ", keepMinBal=" + this.getKeepMinBal() + ", changeMinAmt=" + this.getChangeMinAmt() + ", sgMinAmt=" + this.getSgMinAmt() + ", sellBranch=" + this.getSellBranch() + ", autoSettleFlag=" + this.getAutoSettleFlag() + ", whiteSellFlag=" + this.getWhiteSellFlag() + ", onSaleChannel=" + this.getOnSaleChannel() + ", clientListArray=" + this.getClientListArray() + ", allowBuyWayCd=" + this.getAllowBuyWayCd() + ")";
      }

      public static class ClientListArray {
         @V(
            desc = "客户名称",
            notNull = false,
            length = "200",
            remark = "客户名称",
            maxSize = 200
         )
         private String clientName;
         @V(
            desc = "客户号",
            notNull = false,
            length = "20",
            remark = "客户号",
            maxSize = 20
         )
         private String clientNo;
         @V(
            desc = "证件号码",
            notNull = false,
            length = "50",
            remark = "证件号码",
            maxSize = 50
         )
         private String documentId;
         @V(
            desc = "证件类型",
            notNull = false,
            length = "3",
            remark = "证件类型",
            maxSize = 3
         )
         private String documentType;

         public String getClientName() {
            return this.clientName;
         }

         public String getClientNo() {
            return this.clientNo;
         }

         public String getDocumentId() {
            return this.documentId;
         }

         public String getDocumentType() {
            return this.documentType;
         }

         public void setClientName(String clientName) {
            this.clientName = clientName;
         }

         public void setClientNo(String clientNo) {
            this.clientNo = clientNo;
         }

         public void setDocumentId(String documentId) {
            this.documentId = documentId;
         }

         public void setDocumentType(String documentType) {
            this.documentType = documentType;
         }

         public boolean equals(Object o) {
            if (o == this) {
               return true;
            } else if (!(o instanceof Core1400022801Out.StageArray.ClientListArray)) {
               return false;
            } else {
               Core1400022801Out.StageArray.ClientListArray other = (Core1400022801Out.StageArray.ClientListArray)o;
               if (!other.canEqual(this)) {
                  return false;
               } else {
                  label59: {
                     Object this$clientName = this.getClientName();
                     Object other$clientName = other.getClientName();
                     if (this$clientName == null) {
                        if (other$clientName == null) {
                           break label59;
                        }
                     } else if (this$clientName.equals(other$clientName)) {
                        break label59;
                     }

                     return false;
                  }

                  Object this$clientNo = this.getClientNo();
                  Object other$clientNo = other.getClientNo();
                  if (this$clientNo == null) {
                     if (other$clientNo != null) {
                        return false;
                     }
                  } else if (!this$clientNo.equals(other$clientNo)) {
                     return false;
                  }

                  Object this$documentId = this.getDocumentId();
                  Object other$documentId = other.getDocumentId();
                  if (this$documentId == null) {
                     if (other$documentId != null) {
                        return false;
                     }
                  } else if (!this$documentId.equals(other$documentId)) {
                     return false;
                  }

                  Object this$documentType = this.getDocumentType();
                  Object other$documentType = other.getDocumentType();
                  if (this$documentType == null) {
                     if (other$documentType != null) {
                        return false;
                     }
                  } else if (!this$documentType.equals(other$documentType)) {
                     return false;
                  }

                  return true;
               }
            }
         }

         protected boolean canEqual(Object other) {
            return other instanceof Core1400022801Out.StageArray.ClientListArray;
         }
         public String toString() {
            return "Core1400022801Out.StageArray.ClientListArray(clientName=" + this.getClientName() + ", clientNo=" + this.getClientNo() + ", documentId=" + this.getDocumentId() + ", documentType=" + this.getDocumentType() + ")";
         }
      }

      public static class IntArray {
         @V(
            desc = "序号",
            notNull = false,
            length = "50",
            remark = "序号",
            maxSize = 50
         )
         private String seqNo;
         @V(
            desc = "计息类型",
            notNull = false,
            length = "1",
            remark = "计息类型",
            maxSize = 1
         )
         private String intCalcType;
         @V(
            desc = "行内利率",
            notNull = false,
            length = "15",
            remark = "在人行基准利率调整后对客发布的行内利率",
            decimalLength = 8,
            precision = 8
         )
         private BigDecimal actualRate;
         @V(
            desc = "浮动利率",
            notNull = false,
            length = "15",
            remark = "浮动利率",
            decimalLength = 8,
            precision = 8
         )
         private BigDecimal floatRate;
         @V(
            desc = "执行利率",
            notNull = false,
            length = "15",
            remark = "执行利率",
            decimalLength = 8,
            precision = 8
         )
         private BigDecimal realRate;
         @V(
            desc = "事件类型",
            notNull = false,
            length = "20",
            remark = "事件类型",
            maxSize = 20
         )
         private String eventType;
         @V(
            desc = "利率类型",
            notNull = false,
            length = "5",
            remark = "利率类型",
            maxSize = 5
         )
         private String intType;

         public String getSeqNo() {
            return this.seqNo;
         }

         public String getIntCalcType() {
            return this.intCalcType;
         }

         public BigDecimal getActualRate() {
            return this.actualRate;
         }

         public BigDecimal getFloatRate() {
            return this.floatRate;
         }

         public BigDecimal getRealRate() {
            return this.realRate;
         }

         public String getEventType() {
            return this.eventType;
         }

         public String getIntType() {
            return this.intType;
         }

         public void setSeqNo(String seqNo) {
            this.seqNo = seqNo;
         }

         public void setIntCalcType(String intCalcType) {
            this.intCalcType = intCalcType;
         }

         public void setActualRate(BigDecimal actualRate) {
            this.actualRate = actualRate;
         }

         public void setFloatRate(BigDecimal floatRate) {
            this.floatRate = floatRate;
         }

         public void setRealRate(BigDecimal realRate) {
            this.realRate = realRate;
         }

         public void setEventType(String eventType) {
            this.eventType = eventType;
         }

         public void setIntType(String intType) {
            this.intType = intType;
         }

         public boolean equals(Object o) {
            if (o == this) {
               return true;
            } else if (!(o instanceof Core1400022801Out.StageArray.IntArray)) {
               return false;
            } else {
               Core1400022801Out.StageArray.IntArray other = (Core1400022801Out.StageArray.IntArray)o;
               if (!other.canEqual(this)) {
                  return false;
               } else {
                  label95: {
                     Object this$seqNo = this.getSeqNo();
                     Object other$seqNo = other.getSeqNo();
                     if (this$seqNo == null) {
                        if (other$seqNo == null) {
                           break label95;
                        }
                     } else if (this$seqNo.equals(other$seqNo)) {
                        break label95;
                     }

                     return false;
                  }

                  Object this$intCalcType = this.getIntCalcType();
                  Object other$intCalcType = other.getIntCalcType();
                  if (this$intCalcType == null) {
                     if (other$intCalcType != null) {
                        return false;
                     }
                  } else if (!this$intCalcType.equals(other$intCalcType)) {
                     return false;
                  }

                  Object this$actualRate = this.getActualRate();
                  Object other$actualRate = other.getActualRate();
                  if (this$actualRate == null) {
                     if (other$actualRate != null) {
                        return false;
                     }
                  } else if (!this$actualRate.equals(other$actualRate)) {
                     return false;
                  }

                  label74: {
                     Object this$floatRate = this.getFloatRate();
                     Object other$floatRate = other.getFloatRate();
                     if (this$floatRate == null) {
                        if (other$floatRate == null) {
                           break label74;
                        }
                     } else if (this$floatRate.equals(other$floatRate)) {
                        break label74;
                     }

                     return false;
                  }

                  label67: {
                     Object this$realRate = this.getRealRate();
                     Object other$realRate = other.getRealRate();
                     if (this$realRate == null) {
                        if (other$realRate == null) {
                           break label67;
                        }
                     } else if (this$realRate.equals(other$realRate)) {
                        break label67;
                     }

                     return false;
                  }

                  Object this$eventType = this.getEventType();
                  Object other$eventType = other.getEventType();
                  if (this$eventType == null) {
                     if (other$eventType != null) {
                        return false;
                     }
                  } else if (!this$eventType.equals(other$eventType)) {
                     return false;
                  }

                  Object this$intType = this.getIntType();
                  Object other$intType = other.getIntType();
                  if (this$intType == null) {
                     if (other$intType != null) {
                        return false;
                     }
                  } else if (!this$intType.equals(other$intType)) {
                     return false;
                  }

                  return true;
               }
            }
         }

         protected boolean canEqual(Object other) {
            return other instanceof Core1400022801Out.StageArray.IntArray;
         }
         public String toString() {
            return "Core1400022801Out.StageArray.IntArray(seqNo=" + this.getSeqNo() + ", intCalcType=" + this.getIntCalcType() + ", actualRate=" + this.getActualRate() + ", floatRate=" + this.getFloatRate() + ", realRate=" + this.getRealRate() + ", eventType=" + this.getEventType() + ", intType=" + this.getIntType() + ")";
         }
      }
   }
}
