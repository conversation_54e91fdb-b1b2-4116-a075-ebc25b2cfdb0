package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.util.List;

@MessageOut
public class Core1400049077Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400049077Out.OutArray> outArray;

   public List<Core1400049077Out.OutArray> getOutArray() {
      return this.outArray;
   }

   public void setOutArray(List<Core1400049077Out.OutArray> outArray) {
      this.outArray = outArray;
   }

   public String toString() {
      return "Core1400049077Out(outArray=" + this.getOutArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400049077Out)) {
         return false;
      } else {
         Core1400049077Out other = (Core1400049077Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$outArray = this.getOutArray();
            Object other$outArray = other.getOutArray();
            if (this$outArray == null) {
               if (other$outArray != null) {
                  return false;
               }
            } else if (!this$outArray.equals(other$outArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400049077Out;
   }
   public static class OutArray {
      @V(
         desc = "证件类型",
         notNull = false,
         length = "3",
         remark = "证件类型",
         maxSize = 3
      )
      private String documentType;
      @V(
         desc = "证件号码",
         notNull = false,
         length = "50",
         remark = "证件号码",
         maxSize = 50
      )
      private String documentId;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "账户用途",
         notNull = false,
         length = "10",
         remark = "账户用途",
         maxSize = 10
      )
      private String reasonCode;
      @V(
         desc = "协议签订日期",
         notNull = false,
         remark = "签约日期"
      )
      private String agreementOpenDate;
      @V(
         desc = "协议编号",
         notNull = false,
         length = "50",
         remark = "协议编号",
         maxSize = 50
      )
      private String agreementId;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "法人",
         notNull = false,
         length = "20",
         remark = "法人",
         maxSize = 20
      )
      private String company;

      public String getDocumentType() {
         return this.documentType;
      }

      public String getDocumentId() {
         return this.documentId;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getReasonCode() {
         return this.reasonCode;
      }

      public String getAgreementOpenDate() {
         return this.agreementOpenDate;
      }

      public String getAgreementId() {
         return this.agreementId;
      }

      public String getCcy() {
         return this.ccy;
      }

      public String getCompany() {
         return this.company;
      }

      public void setDocumentType(String documentType) {
         this.documentType = documentType;
      }

      public void setDocumentId(String documentId) {
         this.documentId = documentId;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setReasonCode(String reasonCode) {
         this.reasonCode = reasonCode;
      }

      public void setAgreementOpenDate(String agreementOpenDate) {
         this.agreementOpenDate = agreementOpenDate;
      }

      public void setAgreementId(String agreementId) {
         this.agreementId = agreementId;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setCompany(String company) {
         this.company = company;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400049077Out.OutArray)) {
            return false;
         } else {
            Core1400049077Out.OutArray other = (Core1400049077Out.OutArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label143: {
                  Object this$documentType = this.getDocumentType();
                  Object other$documentType = other.getDocumentType();
                  if (this$documentType == null) {
                     if (other$documentType == null) {
                        break label143;
                     }
                  } else if (this$documentType.equals(other$documentType)) {
                     break label143;
                  }

                  return false;
               }

               Object this$documentId = this.getDocumentId();
               Object other$documentId = other.getDocumentId();
               if (this$documentId == null) {
                  if (other$documentId != null) {
                     return false;
                  }
               } else if (!this$documentId.equals(other$documentId)) {
                  return false;
               }

               Object this$clientNo = this.getClientNo();
               Object other$clientNo = other.getClientNo();
               if (this$clientNo == null) {
                  if (other$clientNo != null) {
                     return false;
                  }
               } else if (!this$clientNo.equals(other$clientNo)) {
                  return false;
               }

               label122: {
                  Object this$prodType = this.getProdType();
                  Object other$prodType = other.getProdType();
                  if (this$prodType == null) {
                     if (other$prodType == null) {
                        break label122;
                     }
                  } else if (this$prodType.equals(other$prodType)) {
                     break label122;
                  }

                  return false;
               }

               label115: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label115;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label115;
                  }

                  return false;
               }

               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo != null) {
                     return false;
                  }
               } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                  return false;
               }

               Object this$reasonCode = this.getReasonCode();
               Object other$reasonCode = other.getReasonCode();
               if (this$reasonCode == null) {
                  if (other$reasonCode != null) {
                     return false;
                  }
               } else if (!this$reasonCode.equals(other$reasonCode)) {
                  return false;
               }

               label94: {
                  Object this$agreementOpenDate = this.getAgreementOpenDate();
                  Object other$agreementOpenDate = other.getAgreementOpenDate();
                  if (this$agreementOpenDate == null) {
                     if (other$agreementOpenDate == null) {
                        break label94;
                     }
                  } else if (this$agreementOpenDate.equals(other$agreementOpenDate)) {
                     break label94;
                  }

                  return false;
               }

               label87: {
                  Object this$agreementId = this.getAgreementId();
                  Object other$agreementId = other.getAgreementId();
                  if (this$agreementId == null) {
                     if (other$agreementId == null) {
                        break label87;
                     }
                  } else if (this$agreementId.equals(other$agreementId)) {
                     break label87;
                  }

                  return false;
               }

               Object this$ccy = this.getCcy();
               Object other$ccy = other.getCcy();
               if (this$ccy == null) {
                  if (other$ccy != null) {
                     return false;
                  }
               } else if (!this$ccy.equals(other$ccy)) {
                  return false;
               }

               Object this$company = this.getCompany();
               Object other$company = other.getCompany();
               if (this$company == null) {
                  if (other$company != null) {
                     return false;
                  }
               } else if (!this$company.equals(other$company)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400049077Out.OutArray;
      }
      public String toString() {
         return "Core1400049077Out.OutArray(documentType=" + this.getDocumentType() + ", documentId=" + this.getDocumentId() + ", clientNo=" + this.getClientNo() + ", prodType=" + this.getProdType() + ", baseAcctNo=" + this.getBaseAcctNo() + ", acctSeqNo=" + this.getAcctSeqNo() + ", reasonCode=" + this.getReasonCode() + ", agreementOpenDate=" + this.getAgreementOpenDate() + ", agreementId=" + this.getAgreementId() + ", ccy=" + this.getCcy() + ", company=" + this.getCompany() + ")";
      }
   }
}
