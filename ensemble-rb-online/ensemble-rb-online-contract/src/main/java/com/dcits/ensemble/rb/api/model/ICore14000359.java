package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000359In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000359Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000359 {
   String URL = "/rb/inq/cheque/defense";


   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0359"
   )
   @BusinessCategory("1400-查询")
   @FunctionCategory("RB06-凭证处理")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core14000359Out runService(Core14000359In var1);
}
