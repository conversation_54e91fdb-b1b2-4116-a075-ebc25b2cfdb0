package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12009113In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12009113Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12009113 {
   String URL = "/rb/nfin/teller/operate";


   @ApiDesc("用于柜员信息在核心进行注册，可进行新增，修改，删除，并支持给虚拟柜员自动分配尾箱。")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "9113"
   )
   @BusinessCategory("1200-非金融")
   @FunctionCategory("RB01-公共服务")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core12009113Out runService(Core12009113In var1);
}
