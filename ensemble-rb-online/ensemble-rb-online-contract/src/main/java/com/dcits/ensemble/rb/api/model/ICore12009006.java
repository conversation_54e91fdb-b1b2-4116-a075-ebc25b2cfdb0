package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12009006In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12009006Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12009006 {
   String URL = "/rb/nfin/rundate/change";

   
   @ApiDesc("外部系统调用此接口,向核心推送新的营业日期,核心接受后根据此结果进行日切")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "9006"
   )
   @BusinessCategory("1200-非金融")
   @FunctionCategory("RB01-公共服务")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core12009006Out runService(Core12009006In var1);
}
