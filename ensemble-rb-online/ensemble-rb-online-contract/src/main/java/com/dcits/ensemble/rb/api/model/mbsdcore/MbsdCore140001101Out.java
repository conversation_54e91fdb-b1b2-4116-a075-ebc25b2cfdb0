package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class MbsdCore140001101Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "客户号",
      notNull = false,
      length = "20",
      remark = "客户号",
      maxSize = 20
   )
   private String clientNo;
   @V(
      desc = "产品类型",
      notNull = false,
      length = "20",
      remark = "产品类型",
      maxSize = 20
   )
   private String prodType;
   @V(
      desc = "账号/卡号",
      notNull = false,
      length = "50",
      remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
      maxSize = 50
   )
   private String baseAcctNo;
   @V(
      desc = "账户币种",
      notNull = false,
      length = "3",
      remark = "账户币种 对于AIO账户和一本通账户",
      maxSize = 3
   )
   private String acctCcy;
   @V(
      desc = "账户序号",
      notNull = false,
      length = "5",
      remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
      maxSize = 5
   )
   private String acctSeqNo;
   @V(
      desc = "客户中文名称",
      notNull = false,
      length = "200",
      remark = "客户中文名称",
      maxSize = 200
   )
   private String chClientName;
   @V(
      desc = "账户名称",
      notNull = false,
      length = "200",
      remark = "账户名称，一般指中文账户名称",
      maxSize = 200
   )
   private String acctName;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<MbsdCore140001101Out.IntDetailArray> intDetailArray;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<MbsdCore140001101Out.IntSplitArray> intSplitArray;
   @V(
      desc = "数组",
      notNull = false,
      remark = "利息计提数组"
   )
   private List<MbsdCore140001101Out.IntAccrHistArray> intAccrHistArray;
   @V(
      desc = "数组",
      notNull = false,
      remark = "利息计算明细数组"
   )
   private List<MbsdCore140001101Out.IntCalcDetailArray> intCalcDetailArray;
   @V(
      desc = "法人",
      notNull = false,
      length = "20",
      remark = "法人",
      maxSize = 20
   )
   private String company;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<MbsdCore140001101Out.IntAdjustArray> intAdjustArray;

   public String getClientNo() {
      return this.clientNo;
   }

   public String getProdType() {
      return this.prodType;
   }

   public String getBaseAcctNo() {
      return this.baseAcctNo;
   }

   public String getAcctCcy() {
      return this.acctCcy;
   }

   public String getAcctSeqNo() {
      return this.acctSeqNo;
   }

   public String getChClientName() {
      return this.chClientName;
   }

   public String getAcctName() {
      return this.acctName;
   }

   public List<MbsdCore140001101Out.IntDetailArray> getIntDetailArray() {
      return this.intDetailArray;
   }

   public List<MbsdCore140001101Out.IntSplitArray> getIntSplitArray() {
      return this.intSplitArray;
   }

   public List<MbsdCore140001101Out.IntAccrHistArray> getIntAccrHistArray() {
      return this.intAccrHistArray;
   }

   public List<MbsdCore140001101Out.IntCalcDetailArray> getIntCalcDetailArray() {
      return this.intCalcDetailArray;
   }

   public String getCompany() {
      return this.company;
   }

   public List<MbsdCore140001101Out.IntAdjustArray> getIntAdjustArray() {
      return this.intAdjustArray;
   }

   public void setClientNo(String clientNo) {
      this.clientNo = clientNo;
   }

   public void setProdType(String prodType) {
      this.prodType = prodType;
   }

   public void setBaseAcctNo(String baseAcctNo) {
      this.baseAcctNo = baseAcctNo;
   }

   public void setAcctCcy(String acctCcy) {
      this.acctCcy = acctCcy;
   }

   public void setAcctSeqNo(String acctSeqNo) {
      this.acctSeqNo = acctSeqNo;
   }

   public void setChClientName(String chClientName) {
      this.chClientName = chClientName;
   }

   public void setAcctName(String acctName) {
      this.acctName = acctName;
   }

   public void setIntDetailArray(List<MbsdCore140001101Out.IntDetailArray> intDetailArray) {
      this.intDetailArray = intDetailArray;
   }

   public void setIntSplitArray(List<MbsdCore140001101Out.IntSplitArray> intSplitArray) {
      this.intSplitArray = intSplitArray;
   }

   public void setIntAccrHistArray(List<MbsdCore140001101Out.IntAccrHistArray> intAccrHistArray) {
      this.intAccrHistArray = intAccrHistArray;
   }

   public void setIntCalcDetailArray(List<MbsdCore140001101Out.IntCalcDetailArray> intCalcDetailArray) {
      this.intCalcDetailArray = intCalcDetailArray;
   }

   public void setCompany(String company) {
      this.company = company;
   }

   public void setIntAdjustArray(List<MbsdCore140001101Out.IntAdjustArray> intAdjustArray) {
      this.intAdjustArray = intAdjustArray;
   }

   public String toString() {
      return "MbsdCore140001101Out(clientNo=" + this.getClientNo() + ", prodType=" + this.getProdType() + ", baseAcctNo=" + this.getBaseAcctNo() + ", acctCcy=" + this.getAcctCcy() + ", acctSeqNo=" + this.getAcctSeqNo() + ", chClientName=" + this.getChClientName() + ", acctName=" + this.getAcctName() + ", intDetailArray=" + this.getIntDetailArray() + ", intSplitArray=" + this.getIntSplitArray() + ", intAccrHistArray=" + this.getIntAccrHistArray() + ", intCalcDetailArray=" + this.getIntCalcDetailArray() + ", company=" + this.getCompany() + ", intAdjustArray=" + this.getIntAdjustArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof MbsdCore140001101Out)) {
         return false;
      } else {
         MbsdCore140001101Out other = (MbsdCore140001101Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            label169: {
               Object this$clientNo = this.getClientNo();
               Object other$clientNo = other.getClientNo();
               if (this$clientNo == null) {
                  if (other$clientNo == null) {
                     break label169;
                  }
               } else if (this$clientNo.equals(other$clientNo)) {
                  break label169;
               }

               return false;
            }

            Object this$prodType = this.getProdType();
            Object other$prodType = other.getProdType();
            if (this$prodType == null) {
               if (other$prodType != null) {
                  return false;
               }
            } else if (!this$prodType.equals(other$prodType)) {
               return false;
            }

            label155: {
               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo == null) {
                     break label155;
                  }
               } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                  break label155;
               }

               return false;
            }

            Object this$acctCcy = this.getAcctCcy();
            Object other$acctCcy = other.getAcctCcy();
            if (this$acctCcy == null) {
               if (other$acctCcy != null) {
                  return false;
               }
            } else if (!this$acctCcy.equals(other$acctCcy)) {
               return false;
            }

            Object this$acctSeqNo = this.getAcctSeqNo();
            Object other$acctSeqNo = other.getAcctSeqNo();
            if (this$acctSeqNo == null) {
               if (other$acctSeqNo != null) {
                  return false;
               }
            } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
               return false;
            }

            label134: {
               Object this$chClientName = this.getChClientName();
               Object other$chClientName = other.getChClientName();
               if (this$chClientName == null) {
                  if (other$chClientName == null) {
                     break label134;
                  }
               } else if (this$chClientName.equals(other$chClientName)) {
                  break label134;
               }

               return false;
            }

            label127: {
               Object this$acctName = this.getAcctName();
               Object other$acctName = other.getAcctName();
               if (this$acctName == null) {
                  if (other$acctName == null) {
                     break label127;
                  }
               } else if (this$acctName.equals(other$acctName)) {
                  break label127;
               }

               return false;
            }

            Object this$intDetailArray = this.getIntDetailArray();
            Object other$intDetailArray = other.getIntDetailArray();
            if (this$intDetailArray == null) {
               if (other$intDetailArray != null) {
                  return false;
               }
            } else if (!this$intDetailArray.equals(other$intDetailArray)) {
               return false;
            }

            Object this$intSplitArray = this.getIntSplitArray();
            Object other$intSplitArray = other.getIntSplitArray();
            if (this$intSplitArray == null) {
               if (other$intSplitArray != null) {
                  return false;
               }
            } else if (!this$intSplitArray.equals(other$intSplitArray)) {
               return false;
            }

            label106: {
               Object this$intAccrHistArray = this.getIntAccrHistArray();
               Object other$intAccrHistArray = other.getIntAccrHistArray();
               if (this$intAccrHistArray == null) {
                  if (other$intAccrHistArray == null) {
                     break label106;
                  }
               } else if (this$intAccrHistArray.equals(other$intAccrHistArray)) {
                  break label106;
               }

               return false;
            }

            label99: {
               Object this$intCalcDetailArray = this.getIntCalcDetailArray();
               Object other$intCalcDetailArray = other.getIntCalcDetailArray();
               if (this$intCalcDetailArray == null) {
                  if (other$intCalcDetailArray == null) {
                     break label99;
                  }
               } else if (this$intCalcDetailArray.equals(other$intCalcDetailArray)) {
                  break label99;
               }

               return false;
            }

            Object this$company = this.getCompany();
            Object other$company = other.getCompany();
            if (this$company == null) {
               if (other$company != null) {
                  return false;
               }
            } else if (!this$company.equals(other$company)) {
               return false;
            }

            Object this$intAdjustArray = this.getIntAdjustArray();
            Object other$intAdjustArray = other.getIntAdjustArray();
            if (this$intAdjustArray == null) {
               if (other$intAdjustArray != null) {
                  return false;
               }
            } else if (!this$intAdjustArray.equals(other$intAdjustArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof MbsdCore140001101Out;
   }
   public static class IntAdjustArray {
      @V(
         desc = "所属机构号",
         notNull = false,
         length = "50",
         remark = "所属机构号",
         maxSize = 50
      )
      private String branch;
      @V(
         desc = "计提日利息调整",
         notNull = false,
         length = "17",
         remark = "计提日利息调整",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal intAdjCtd;
      @V(
         desc = "备注",
         notNull = false,
         length = "200",
         remark = "备注",
         maxSize = 200
      )
      private String remark;
      @V(
         desc = "调整序号",
         notNull = false,
         length = "50",
         remark = "调整序号",
         maxSize = 50
      )
      private String adjustSeqNo;
      @V(
         desc = "法人",
         notNull = false,
         length = "20",
         remark = "法人",
         maxSize = 20
      )
      private String company;
      @V(
         desc = "调整日期",
         notNull = false,
         remark = "调整日期"
      )
      private String adjustDate;

      public String getBranch() {
         return this.branch;
      }

      public BigDecimal getIntAdjCtd() {
         return this.intAdjCtd;
      }

      public String getRemark() {
         return this.remark;
      }

      public String getAdjustSeqNo() {
         return this.adjustSeqNo;
      }

      public String getCompany() {
         return this.company;
      }

      public String getAdjustDate() {
         return this.adjustDate;
      }

      public void setBranch(String branch) {
         this.branch = branch;
      }

      public void setIntAdjCtd(BigDecimal intAdjCtd) {
         this.intAdjCtd = intAdjCtd;
      }

      public void setRemark(String remark) {
         this.remark = remark;
      }

      public void setAdjustSeqNo(String adjustSeqNo) {
         this.adjustSeqNo = adjustSeqNo;
      }

      public void setCompany(String company) {
         this.company = company;
      }

      public void setAdjustDate(String adjustDate) {
         this.adjustDate = adjustDate;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof MbsdCore140001101Out.IntAdjustArray)) {
            return false;
         } else {
            MbsdCore140001101Out.IntAdjustArray other = (MbsdCore140001101Out.IntAdjustArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$branch = this.getBranch();
               Object other$branch = other.getBranch();
               if (this$branch == null) {
                  if (other$branch != null) {
                     return false;
                  }
               } else if (!this$branch.equals(other$branch)) {
                  return false;
               }

               Object this$intAdjCtd = this.getIntAdjCtd();
               Object other$intAdjCtd = other.getIntAdjCtd();
               if (this$intAdjCtd == null) {
                  if (other$intAdjCtd != null) {
                     return false;
                  }
               } else if (!this$intAdjCtd.equals(other$intAdjCtd)) {
                  return false;
               }

               Object this$remark = this.getRemark();
               Object other$remark = other.getRemark();
               if (this$remark == null) {
                  if (other$remark != null) {
                     return false;
                  }
               } else if (!this$remark.equals(other$remark)) {
                  return false;
               }

               label62: {
                  Object this$adjustSeqNo = this.getAdjustSeqNo();
                  Object other$adjustSeqNo = other.getAdjustSeqNo();
                  if (this$adjustSeqNo == null) {
                     if (other$adjustSeqNo == null) {
                        break label62;
                     }
                  } else if (this$adjustSeqNo.equals(other$adjustSeqNo)) {
                     break label62;
                  }

                  return false;
               }

               label55: {
                  Object this$company = this.getCompany();
                  Object other$company = other.getCompany();
                  if (this$company == null) {
                     if (other$company == null) {
                        break label55;
                     }
                  } else if (this$company.equals(other$company)) {
                     break label55;
                  }

                  return false;
               }

               Object this$adjustDate = this.getAdjustDate();
               Object other$adjustDate = other.getAdjustDate();
               if (this$adjustDate == null) {
                  if (other$adjustDate != null) {
                     return false;
                  }
               } else if (!this$adjustDate.equals(other$adjustDate)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof MbsdCore140001101Out.IntAdjustArray;
      }
      public String toString() {
         return "MbsdCore140001101Out.IntAdjustArray(branch=" + this.getBranch() + ", intAdjCtd=" + this.getIntAdjCtd() + ", remark=" + this.getRemark() + ", adjustSeqNo=" + this.getAdjustSeqNo() + ", company=" + this.getCompany() + ", adjustDate=" + this.getAdjustDate() + ")";
      }
   }

   public static class IntCalcDetailArray {
      @V(
         desc = "序号",
         notNull = false,
         length = "50",
         remark = "序号",
         maxSize = 50
      )
      private String seqNo;
      @V(
         desc = "利息分类",
         notNull = false,
         length = "5",
         remark = "利息分类",
         maxSize = 5
      )
      private String intClass;
      @V(
         desc = "计提日期",
         notNull = false,
         remark = "计提日期指需要计提的分户进行计提和实际产生利息的日期一般为昨天-YESTER_DAY"
      )
      private String accrDate;
      @V(
         desc = "金额",
         notNull = false,
         length = "17",
         remark = "金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal amount;
      @V(
         desc = "算息金额",
         notNull = false,
         length = "38",
         remark = "算息金额包含积数",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal calcIntAmt;
      @V(
         desc = "基准汇率",
         notNull = false,
         length = "15",
         remark = "基准汇率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal basisRate;
      @V(
         desc = "行内利率",
         notNull = false,
         length = "15",
         remark = "在人行基准利率调整后对客发布的行内利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal actualRate;
      @V(
         desc = "浮动利率",
         notNull = false,
         length = "15",
         remark = "浮动利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal floatRate;
      @V(
         desc = "最后实际利率",
         notNull = false,
         length = "15",
         remark = "最后实际利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal lastRealRate;
      @V(
         desc = "天数",
         notNull = false,
         length = "5",
         remark = "天数"
      )
      private Integer days;
      @V(
         desc = "靠档天数",
         notNull = false,
         length = "5",
         remark = "靠档天数"
      )
      private Integer nearDays;
      @V(
         desc = "靠档金额",
         notNull = false,
         length = "17",
         remark = "靠档金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal nearAmt;
      @V(
         desc = "年基准天数",
         notNull = false,
         length = "3",
         remark = "年基准天数",
         maxSize = 3
      )
      private String yearBasis;
      @V(
         desc = "浮动百分比",
         notNull = false,
         length = "15",
         remark = "浮动百分比",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal spreadPercent;
      @V(
         desc = "频率id",
         notNull = false,
         length = "5",
         remark = "频率id",
         maxSize = 5
      )
      private String periodFreq;
      @V(
         desc = "法人",
         notNull = false,
         length = "20",
         remark = "法人",
         maxSize = 20
      )
      private String company;

      public String getSeqNo() {
         return this.seqNo;
      }

      public String getIntClass() {
         return this.intClass;
      }

      public String getAccrDate() {
         return this.accrDate;
      }

      public BigDecimal getAmount() {
         return this.amount;
      }

      public BigDecimal getCalcIntAmt() {
         return this.calcIntAmt;
      }

      public BigDecimal getBasisRate() {
         return this.basisRate;
      }

      public BigDecimal getActualRate() {
         return this.actualRate;
      }

      public BigDecimal getFloatRate() {
         return this.floatRate;
      }

      public BigDecimal getLastRealRate() {
         return this.lastRealRate;
      }

      public Integer getDays() {
         return this.days;
      }

      public Integer getNearDays() {
         return this.nearDays;
      }

      public BigDecimal getNearAmt() {
         return this.nearAmt;
      }

      public String getYearBasis() {
         return this.yearBasis;
      }

      public BigDecimal getSpreadPercent() {
         return this.spreadPercent;
      }

      public String getPeriodFreq() {
         return this.periodFreq;
      }

      public String getCompany() {
         return this.company;
      }

      public void setSeqNo(String seqNo) {
         this.seqNo = seqNo;
      }

      public void setIntClass(String intClass) {
         this.intClass = intClass;
      }

      public void setAccrDate(String accrDate) {
         this.accrDate = accrDate;
      }

      public void setAmount(BigDecimal amount) {
         this.amount = amount;
      }

      public void setCalcIntAmt(BigDecimal calcIntAmt) {
         this.calcIntAmt = calcIntAmt;
      }

      public void setBasisRate(BigDecimal basisRate) {
         this.basisRate = basisRate;
      }

      public void setActualRate(BigDecimal actualRate) {
         this.actualRate = actualRate;
      }

      public void setFloatRate(BigDecimal floatRate) {
         this.floatRate = floatRate;
      }

      public void setLastRealRate(BigDecimal lastRealRate) {
         this.lastRealRate = lastRealRate;
      }

      public void setDays(Integer days) {
         this.days = days;
      }

      public void setNearDays(Integer nearDays) {
         this.nearDays = nearDays;
      }

      public void setNearAmt(BigDecimal nearAmt) {
         this.nearAmt = nearAmt;
      }

      public void setYearBasis(String yearBasis) {
         this.yearBasis = yearBasis;
      }

      public void setSpreadPercent(BigDecimal spreadPercent) {
         this.spreadPercent = spreadPercent;
      }

      public void setPeriodFreq(String periodFreq) {
         this.periodFreq = periodFreq;
      }

      public void setCompany(String company) {
         this.company = company;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof MbsdCore140001101Out.IntCalcDetailArray)) {
            return false;
         } else {
            MbsdCore140001101Out.IntCalcDetailArray other = (MbsdCore140001101Out.IntCalcDetailArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label203: {
                  Object this$seqNo = this.getSeqNo();
                  Object other$seqNo = other.getSeqNo();
                  if (this$seqNo == null) {
                     if (other$seqNo == null) {
                        break label203;
                     }
                  } else if (this$seqNo.equals(other$seqNo)) {
                     break label203;
                  }

                  return false;
               }

               Object this$intClass = this.getIntClass();
               Object other$intClass = other.getIntClass();
               if (this$intClass == null) {
                  if (other$intClass != null) {
                     return false;
                  }
               } else if (!this$intClass.equals(other$intClass)) {
                  return false;
               }

               Object this$accrDate = this.getAccrDate();
               Object other$accrDate = other.getAccrDate();
               if (this$accrDate == null) {
                  if (other$accrDate != null) {
                     return false;
                  }
               } else if (!this$accrDate.equals(other$accrDate)) {
                  return false;
               }

               label182: {
                  Object this$amount = this.getAmount();
                  Object other$amount = other.getAmount();
                  if (this$amount == null) {
                     if (other$amount == null) {
                        break label182;
                     }
                  } else if (this$amount.equals(other$amount)) {
                     break label182;
                  }

                  return false;
               }

               label175: {
                  Object this$calcIntAmt = this.getCalcIntAmt();
                  Object other$calcIntAmt = other.getCalcIntAmt();
                  if (this$calcIntAmt == null) {
                     if (other$calcIntAmt == null) {
                        break label175;
                     }
                  } else if (this$calcIntAmt.equals(other$calcIntAmt)) {
                     break label175;
                  }

                  return false;
               }

               label168: {
                  Object this$basisRate = this.getBasisRate();
                  Object other$basisRate = other.getBasisRate();
                  if (this$basisRate == null) {
                     if (other$basisRate == null) {
                        break label168;
                     }
                  } else if (this$basisRate.equals(other$basisRate)) {
                     break label168;
                  }

                  return false;
               }

               Object this$actualRate = this.getActualRate();
               Object other$actualRate = other.getActualRate();
               if (this$actualRate == null) {
                  if (other$actualRate != null) {
                     return false;
                  }
               } else if (!this$actualRate.equals(other$actualRate)) {
                  return false;
               }

               label154: {
                  Object this$floatRate = this.getFloatRate();
                  Object other$floatRate = other.getFloatRate();
                  if (this$floatRate == null) {
                     if (other$floatRate == null) {
                        break label154;
                     }
                  } else if (this$floatRate.equals(other$floatRate)) {
                     break label154;
                  }

                  return false;
               }

               Object this$lastRealRate = this.getLastRealRate();
               Object other$lastRealRate = other.getLastRealRate();
               if (this$lastRealRate == null) {
                  if (other$lastRealRate != null) {
                     return false;
                  }
               } else if (!this$lastRealRate.equals(other$lastRealRate)) {
                  return false;
               }

               label140: {
                  Object this$days = this.getDays();
                  Object other$days = other.getDays();
                  if (this$days == null) {
                     if (other$days == null) {
                        break label140;
                     }
                  } else if (this$days.equals(other$days)) {
                     break label140;
                  }

                  return false;
               }

               Object this$nearDays = this.getNearDays();
               Object other$nearDays = other.getNearDays();
               if (this$nearDays == null) {
                  if (other$nearDays != null) {
                     return false;
                  }
               } else if (!this$nearDays.equals(other$nearDays)) {
                  return false;
               }

               Object this$nearAmt = this.getNearAmt();
               Object other$nearAmt = other.getNearAmt();
               if (this$nearAmt == null) {
                  if (other$nearAmt != null) {
                     return false;
                  }
               } else if (!this$nearAmt.equals(other$nearAmt)) {
                  return false;
               }

               label119: {
                  Object this$yearBasis = this.getYearBasis();
                  Object other$yearBasis = other.getYearBasis();
                  if (this$yearBasis == null) {
                     if (other$yearBasis == null) {
                        break label119;
                     }
                  } else if (this$yearBasis.equals(other$yearBasis)) {
                     break label119;
                  }

                  return false;
               }

               label112: {
                  Object this$spreadPercent = this.getSpreadPercent();
                  Object other$spreadPercent = other.getSpreadPercent();
                  if (this$spreadPercent == null) {
                     if (other$spreadPercent == null) {
                        break label112;
                     }
                  } else if (this$spreadPercent.equals(other$spreadPercent)) {
                     break label112;
                  }

                  return false;
               }

               Object this$periodFreq = this.getPeriodFreq();
               Object other$periodFreq = other.getPeriodFreq();
               if (this$periodFreq == null) {
                  if (other$periodFreq != null) {
                     return false;
                  }
               } else if (!this$periodFreq.equals(other$periodFreq)) {
                  return false;
               }

               Object this$company = this.getCompany();
               Object other$company = other.getCompany();
               if (this$company == null) {
                  if (other$company != null) {
                     return false;
                  }
               } else if (!this$company.equals(other$company)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof MbsdCore140001101Out.IntCalcDetailArray;
      }
      public String toString() {
         return "MbsdCore140001101Out.IntCalcDetailArray(seqNo=" + this.getSeqNo() + ", intClass=" + this.getIntClass() + ", accrDate=" + this.getAccrDate() + ", amount=" + this.getAmount() + ", calcIntAmt=" + this.getCalcIntAmt() + ", basisRate=" + this.getBasisRate() + ", actualRate=" + this.getActualRate() + ", floatRate=" + this.getFloatRate() + ", lastRealRate=" + this.getLastRealRate() + ", days=" + this.getDays() + ", nearDays=" + this.getNearDays() + ", nearAmt=" + this.getNearAmt() + ", yearBasis=" + this.getYearBasis() + ", spreadPercent=" + this.getSpreadPercent() + ", periodFreq=" + this.getPeriodFreq() + ", company=" + this.getCompany() + ")";
      }
   }

   public static class IntAccrHistArray {
      @V(
         desc = "费率编号",
         notNull = false,
         length = "50",
         remark = "费率编号",
         maxSize = 50
      )
      private String irlSeqNo;
      @V(
         desc = "计提日期",
         notNull = false,
         remark = "计提日期指需要计提的分户进行计提和实际产生利息的日期一般为昨天-YESTER_DAY"
      )
      private String accrDate;
      @V(
         desc = "利息金额",
         notNull = false,
         length = "17",
         remark = "利息金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal intAmt;
      @V(
         desc = "利息分类",
         notNull = false,
         length = "5",
         remark = "利息分类",
         maxSize = 5
      )
      private String intClass;
      @V(
         desc = "利率类型",
         notNull = false,
         length = "5",
         remark = "利率类型",
         maxSize = 5
      )
      private String intType;
      @V(
         desc = "浮动利率",
         notNull = false,
         length = "15",
         remark = "浮动利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal floatRate;
      @V(
         desc = "执行利率",
         notNull = false,
         length = "15",
         remark = "执行利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal realRate;
      @V(
         desc = "累计计提",
         notNull = false,
         length = "17",
         remark = "累计计提",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal intAccrued;
      @V(
         desc = "计提日计提利息",
         notNull = false,
         length = "17",
         remark = "计提日计提利息",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal intAccruedCtd;
      @V(
         desc = "计提日计提实际金额",
         notNull = false,
         length = "25",
         remark = "计提日计提实际金额",
         decimalLength = 10,
         precision = 10
      )
      private BigDecimal intAccruedCalcCtd;
      @V(
         desc = "计息方式",
         notNull = false,
         length = "2",
         remark = "计息方式",
         maxSize = 2
      )
      private String intCalcBal;
      @V(
         desc = "计提天数",
         notNull = false,
         length = "5",
         remark = "产生的利息对应的总计提天数"
      )
      private Integer accrDays;
      @V(
         desc = "年基准天数",
         notNull = false,
         length = "3",
         remark = "年基准天数",
         maxSize = 3
      )
      private String yearBasis;
      @V(
         desc = "法人",
         notNull = false,
         length = "20",
         remark = "法人",
         maxSize = 20
      )
      private String company;

      public String getIrlSeqNo() {
         return this.irlSeqNo;
      }

      public String getAccrDate() {
         return this.accrDate;
      }

      public BigDecimal getIntAmt() {
         return this.intAmt;
      }

      public String getIntClass() {
         return this.intClass;
      }

      public String getIntType() {
         return this.intType;
      }

      public BigDecimal getFloatRate() {
         return this.floatRate;
      }

      public BigDecimal getRealRate() {
         return this.realRate;
      }

      public BigDecimal getIntAccrued() {
         return this.intAccrued;
      }

      public BigDecimal getIntAccruedCtd() {
         return this.intAccruedCtd;
      }

      public BigDecimal getIntAccruedCalcCtd() {
         return this.intAccruedCalcCtd;
      }

      public String getIntCalcBal() {
         return this.intCalcBal;
      }

      public Integer getAccrDays() {
         return this.accrDays;
      }

      public String getYearBasis() {
         return this.yearBasis;
      }

      public String getCompany() {
         return this.company;
      }

      public void setIrlSeqNo(String irlSeqNo) {
         this.irlSeqNo = irlSeqNo;
      }

      public void setAccrDate(String accrDate) {
         this.accrDate = accrDate;
      }

      public void setIntAmt(BigDecimal intAmt) {
         this.intAmt = intAmt;
      }

      public void setIntClass(String intClass) {
         this.intClass = intClass;
      }

      public void setIntType(String intType) {
         this.intType = intType;
      }

      public void setFloatRate(BigDecimal floatRate) {
         this.floatRate = floatRate;
      }

      public void setRealRate(BigDecimal realRate) {
         this.realRate = realRate;
      }

      public void setIntAccrued(BigDecimal intAccrued) {
         this.intAccrued = intAccrued;
      }

      public void setIntAccruedCtd(BigDecimal intAccruedCtd) {
         this.intAccruedCtd = intAccruedCtd;
      }

      public void setIntAccruedCalcCtd(BigDecimal intAccruedCalcCtd) {
         this.intAccruedCalcCtd = intAccruedCalcCtd;
      }

      public void setIntCalcBal(String intCalcBal) {
         this.intCalcBal = intCalcBal;
      }

      public void setAccrDays(Integer accrDays) {
         this.accrDays = accrDays;
      }

      public void setYearBasis(String yearBasis) {
         this.yearBasis = yearBasis;
      }

      public void setCompany(String company) {
         this.company = company;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof MbsdCore140001101Out.IntAccrHistArray)) {
            return false;
         } else {
            MbsdCore140001101Out.IntAccrHistArray other = (MbsdCore140001101Out.IntAccrHistArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$irlSeqNo = this.getIrlSeqNo();
               Object other$irlSeqNo = other.getIrlSeqNo();
               if (this$irlSeqNo == null) {
                  if (other$irlSeqNo != null) {
                     return false;
                  }
               } else if (!this$irlSeqNo.equals(other$irlSeqNo)) {
                  return false;
               }

               Object this$accrDate = this.getAccrDate();
               Object other$accrDate = other.getAccrDate();
               if (this$accrDate == null) {
                  if (other$accrDate != null) {
                     return false;
                  }
               } else if (!this$accrDate.equals(other$accrDate)) {
                  return false;
               }

               Object this$intAmt = this.getIntAmt();
               Object other$intAmt = other.getIntAmt();
               if (this$intAmt == null) {
                  if (other$intAmt != null) {
                     return false;
                  }
               } else if (!this$intAmt.equals(other$intAmt)) {
                  return false;
               }

               label158: {
                  Object this$intClass = this.getIntClass();
                  Object other$intClass = other.getIntClass();
                  if (this$intClass == null) {
                     if (other$intClass == null) {
                        break label158;
                     }
                  } else if (this$intClass.equals(other$intClass)) {
                     break label158;
                  }

                  return false;
               }

               label151: {
                  Object this$intType = this.getIntType();
                  Object other$intType = other.getIntType();
                  if (this$intType == null) {
                     if (other$intType == null) {
                        break label151;
                     }
                  } else if (this$intType.equals(other$intType)) {
                     break label151;
                  }

                  return false;
               }

               Object this$floatRate = this.getFloatRate();
               Object other$floatRate = other.getFloatRate();
               if (this$floatRate == null) {
                  if (other$floatRate != null) {
                     return false;
                  }
               } else if (!this$floatRate.equals(other$floatRate)) {
                  return false;
               }

               label137: {
                  Object this$realRate = this.getRealRate();
                  Object other$realRate = other.getRealRate();
                  if (this$realRate == null) {
                     if (other$realRate == null) {
                        break label137;
                     }
                  } else if (this$realRate.equals(other$realRate)) {
                     break label137;
                  }

                  return false;
               }

               label130: {
                  Object this$intAccrued = this.getIntAccrued();
                  Object other$intAccrued = other.getIntAccrued();
                  if (this$intAccrued == null) {
                     if (other$intAccrued == null) {
                        break label130;
                     }
                  } else if (this$intAccrued.equals(other$intAccrued)) {
                     break label130;
                  }

                  return false;
               }

               Object this$intAccruedCtd = this.getIntAccruedCtd();
               Object other$intAccruedCtd = other.getIntAccruedCtd();
               if (this$intAccruedCtd == null) {
                  if (other$intAccruedCtd != null) {
                     return false;
                  }
               } else if (!this$intAccruedCtd.equals(other$intAccruedCtd)) {
                  return false;
               }

               Object this$intAccruedCalcCtd = this.getIntAccruedCalcCtd();
               Object other$intAccruedCalcCtd = other.getIntAccruedCalcCtd();
               if (this$intAccruedCalcCtd == null) {
                  if (other$intAccruedCalcCtd != null) {
                     return false;
                  }
               } else if (!this$intAccruedCalcCtd.equals(other$intAccruedCalcCtd)) {
                  return false;
               }

               label109: {
                  Object this$intCalcBal = this.getIntCalcBal();
                  Object other$intCalcBal = other.getIntCalcBal();
                  if (this$intCalcBal == null) {
                     if (other$intCalcBal == null) {
                        break label109;
                     }
                  } else if (this$intCalcBal.equals(other$intCalcBal)) {
                     break label109;
                  }

                  return false;
               }

               label102: {
                  Object this$accrDays = this.getAccrDays();
                  Object other$accrDays = other.getAccrDays();
                  if (this$accrDays == null) {
                     if (other$accrDays == null) {
                        break label102;
                     }
                  } else if (this$accrDays.equals(other$accrDays)) {
                     break label102;
                  }

                  return false;
               }

               Object this$yearBasis = this.getYearBasis();
               Object other$yearBasis = other.getYearBasis();
               if (this$yearBasis == null) {
                  if (other$yearBasis != null) {
                     return false;
                  }
               } else if (!this$yearBasis.equals(other$yearBasis)) {
                  return false;
               }

               Object this$company = this.getCompany();
               Object other$company = other.getCompany();
               if (this$company == null) {
                  if (other$company != null) {
                     return false;
                  }
               } else if (!this$company.equals(other$company)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof MbsdCore140001101Out.IntAccrHistArray;
      }
      public String toString() {
         return "MbsdCore140001101Out.IntAccrHistArray(irlSeqNo=" + this.getIrlSeqNo() + ", accrDate=" + this.getAccrDate() + ", intAmt=" + this.getIntAmt() + ", intClass=" + this.getIntClass() + ", intType=" + this.getIntType() + ", floatRate=" + this.getFloatRate() + ", realRate=" + this.getRealRate() + ", intAccrued=" + this.getIntAccrued() + ", intAccruedCtd=" + this.getIntAccruedCtd() + ", intAccruedCalcCtd=" + this.getIntAccruedCalcCtd() + ", intCalcBal=" + this.getIntCalcBal() + ", accrDays=" + this.getAccrDays() + ", yearBasis=" + this.getYearBasis() + ", company=" + this.getCompany() + ")";
      }
   }

   public static class IntSplitArray {
      @V(
         desc = "开始日期",
         notNull = false,
         remark = "开始日期"
      )
      private String startDate;
      @V(
         desc = "结束日期",
         notNull = false,
         remark = "结束日期"
      )
      private String endDate;
      @V(
         desc = "计提金额",
         notNull = false,
         length = "17",
         remark = "计提金额全额算息时为上日余额或积数差额算息时为金额分层金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal accrAmt;
      @V(
         desc = "利息分类",
         notNull = false,
         length = "5",
         remark = "利息分类",
         maxSize = 5
      )
      private String intClass;
      @V(
         desc = "行内利率",
         notNull = false,
         length = "15",
         remark = "在人行基准利率调整后对客发布的行内利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal actualRate;
      @V(
         desc = "浮动利率",
         notNull = false,
         length = "15",
         remark = "浮动利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal floatRate;
      @V(
         desc = "执行利率",
         notNull = false,
         length = "15",
         remark = "执行利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal realRate;
      @V(
         desc = "计提天数",
         notNull = false,
         length = "5",
         remark = "产生的利息对应的总计提天数"
      )
      private Integer accrDays;
      @V(
         desc = "利率类型",
         notNull = false,
         length = "5",
         remark = "利率类型",
         maxSize = 5
      )
      private String intType;
      @V(
         desc = "事件类型",
         notNull = false,
         length = "20",
         remark = "事件类型",
         maxSize = 20
      )
      private String eventType;
      @V(
         desc = "年基准天数",
         notNull = false,
         length = "3",
         remark = "年基准天数",
         maxSize = 3
      )
      private String yearBasis;
      @V(
         desc = "法人",
         notNull = false,
         length = "20",
         remark = "法人",
         maxSize = 20
      )
      private String company;

      public String getStartDate() {
         return this.startDate;
      }

      public String getEndDate() {
         return this.endDate;
      }

      public BigDecimal getAccrAmt() {
         return this.accrAmt;
      }

      public String getIntClass() {
         return this.intClass;
      }

      public BigDecimal getActualRate() {
         return this.actualRate;
      }

      public BigDecimal getFloatRate() {
         return this.floatRate;
      }

      public BigDecimal getRealRate() {
         return this.realRate;
      }

      public Integer getAccrDays() {
         return this.accrDays;
      }

      public String getIntType() {
         return this.intType;
      }

      public String getEventType() {
         return this.eventType;
      }

      public String getYearBasis() {
         return this.yearBasis;
      }

      public String getCompany() {
         return this.company;
      }

      public void setStartDate(String startDate) {
         this.startDate = startDate;
      }

      public void setEndDate(String endDate) {
         this.endDate = endDate;
      }

      public void setAccrAmt(BigDecimal accrAmt) {
         this.accrAmt = accrAmt;
      }

      public void setIntClass(String intClass) {
         this.intClass = intClass;
      }

      public void setActualRate(BigDecimal actualRate) {
         this.actualRate = actualRate;
      }

      public void setFloatRate(BigDecimal floatRate) {
         this.floatRate = floatRate;
      }

      public void setRealRate(BigDecimal realRate) {
         this.realRate = realRate;
      }

      public void setAccrDays(Integer accrDays) {
         this.accrDays = accrDays;
      }

      public void setIntType(String intType) {
         this.intType = intType;
      }

      public void setEventType(String eventType) {
         this.eventType = eventType;
      }

      public void setYearBasis(String yearBasis) {
         this.yearBasis = yearBasis;
      }

      public void setCompany(String company) {
         this.company = company;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof MbsdCore140001101Out.IntSplitArray)) {
            return false;
         } else {
            MbsdCore140001101Out.IntSplitArray other = (MbsdCore140001101Out.IntSplitArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label155: {
                  Object this$startDate = this.getStartDate();
                  Object other$startDate = other.getStartDate();
                  if (this$startDate == null) {
                     if (other$startDate == null) {
                        break label155;
                     }
                  } else if (this$startDate.equals(other$startDate)) {
                     break label155;
                  }

                  return false;
               }

               Object this$endDate = this.getEndDate();
               Object other$endDate = other.getEndDate();
               if (this$endDate == null) {
                  if (other$endDate != null) {
                     return false;
                  }
               } else if (!this$endDate.equals(other$endDate)) {
                  return false;
               }

               Object this$accrAmt = this.getAccrAmt();
               Object other$accrAmt = other.getAccrAmt();
               if (this$accrAmt == null) {
                  if (other$accrAmt != null) {
                     return false;
                  }
               } else if (!this$accrAmt.equals(other$accrAmt)) {
                  return false;
               }

               label134: {
                  Object this$intClass = this.getIntClass();
                  Object other$intClass = other.getIntClass();
                  if (this$intClass == null) {
                     if (other$intClass == null) {
                        break label134;
                     }
                  } else if (this$intClass.equals(other$intClass)) {
                     break label134;
                  }

                  return false;
               }

               label127: {
                  Object this$actualRate = this.getActualRate();
                  Object other$actualRate = other.getActualRate();
                  if (this$actualRate == null) {
                     if (other$actualRate == null) {
                        break label127;
                     }
                  } else if (this$actualRate.equals(other$actualRate)) {
                     break label127;
                  }

                  return false;
               }

               label120: {
                  Object this$floatRate = this.getFloatRate();
                  Object other$floatRate = other.getFloatRate();
                  if (this$floatRate == null) {
                     if (other$floatRate == null) {
                        break label120;
                     }
                  } else if (this$floatRate.equals(other$floatRate)) {
                     break label120;
                  }

                  return false;
               }

               Object this$realRate = this.getRealRate();
               Object other$realRate = other.getRealRate();
               if (this$realRate == null) {
                  if (other$realRate != null) {
                     return false;
                  }
               } else if (!this$realRate.equals(other$realRate)) {
                  return false;
               }

               label106: {
                  Object this$accrDays = this.getAccrDays();
                  Object other$accrDays = other.getAccrDays();
                  if (this$accrDays == null) {
                     if (other$accrDays == null) {
                        break label106;
                     }
                  } else if (this$accrDays.equals(other$accrDays)) {
                     break label106;
                  }

                  return false;
               }

               Object this$intType = this.getIntType();
               Object other$intType = other.getIntType();
               if (this$intType == null) {
                  if (other$intType != null) {
                     return false;
                  }
               } else if (!this$intType.equals(other$intType)) {
                  return false;
               }

               label92: {
                  Object this$eventType = this.getEventType();
                  Object other$eventType = other.getEventType();
                  if (this$eventType == null) {
                     if (other$eventType == null) {
                        break label92;
                     }
                  } else if (this$eventType.equals(other$eventType)) {
                     break label92;
                  }

                  return false;
               }

               Object this$yearBasis = this.getYearBasis();
               Object other$yearBasis = other.getYearBasis();
               if (this$yearBasis == null) {
                  if (other$yearBasis != null) {
                     return false;
                  }
               } else if (!this$yearBasis.equals(other$yearBasis)) {
                  return false;
               }

               Object this$company = this.getCompany();
               Object other$company = other.getCompany();
               if (this$company == null) {
                  if (other$company != null) {
                     return false;
                  }
               } else if (!this$company.equals(other$company)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof MbsdCore140001101Out.IntSplitArray;
      }
      public String toString() {
         return "MbsdCore140001101Out.IntSplitArray(startDate=" + this.getStartDate() + ", endDate=" + this.getEndDate() + ", accrAmt=" + this.getAccrAmt() + ", intClass=" + this.getIntClass() + ", actualRate=" + this.getActualRate() + ", floatRate=" + this.getFloatRate() + ", realRate=" + this.getRealRate() + ", accrDays=" + this.getAccrDays() + ", intType=" + this.getIntType() + ", eventType=" + this.getEventType() + ", yearBasis=" + this.getYearBasis() + ", company=" + this.getCompany() + ")";
      }
   }

   public static class IntDetailArray {
      @V(
         desc = "客户中文名称",
         notNull = false,
         length = "200",
         remark = "客户中文名称",
         maxSize = 200
      )
      private String chClientName;
      @V(
         desc = "分户级固定利率",
         notNull = false,
         length = "15",
         remark = "分户固定利率一般与客户协商后的固定利率优先级最高不受行内基准利率调整的影响",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal acctFixedRate;
      @V(
         desc = "分户级利率浮动百分点",
         notNull = false,
         length = "15",
         remark = "分户固定利率一般与客户协商后的利率上浮百分点只在客户正常计提和支取的时候生效",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal acctSpreadRate;
      @V(
         desc = "分户级利率浮动百分比",
         notNull = false,
         length = "5",
         remark = "分户级利率浮动百分比",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal acctPercentRate;
      @V(
         desc = "下一结息日",
         notNull = false,
         remark = "下一结息日"
      )
      private String nextCycleDate;
      @V(
         desc = "上一利息计提日",
         notNull = false,
         remark = "上一利息计提日"
      )
      private String lastAccrualDate;
      @V(
         desc = "下一个利率变更日期",
         notNull = false,
         remark = "下一个利率变更日期"
      )
      private String nextRollDate;
      @V(
         desc = "利息计算起始日",
         notNull = false,
         remark = "利息计算起始日"
      )
      private String calcBeginDate;
      @V(
         desc = "利息计算截止日",
         notNull = false,
         remark = "利息计算截止日"
      )
      private String calcEndDate;
      @V(
         desc = "上一结息日",
         notNull = false,
         remark = "上一结息日"
      )
      private String lastCycleDate;
      @V(
         desc = "上一真实结息日",
         notNull = false,
         remark = "增加特殊结息日如到期日时使用"
      )
      private String lastTrueCycleDate;
      @V(
         desc = "利息资本化金额",
         notNull = false,
         length = "17",
         remark = "利息资本化金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal intCapAmt;
      @V(
         desc = "折扣罚息金额",
         notNull = false,
         length = "17",
         remark = "折扣罚息金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal uiPenaltyAmt;
      @V(
         desc = "利息分类",
         notNull = false,
         length = "5",
         remark = "利息分类",
         maxSize = 5
      )
      private String intClass;
      @V(
         desc = "浮动点数",
         notNull = false,
         length = "15",
         remark = "浮动点数",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal spreadRate;
      @V(
         desc = "执行利率",
         notNull = false,
         length = "15",
         remark = "执行利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal realRate;
      @V(
         desc = "罚息利率使用方式",
         notNull = false,
         length = "1",
         remark = "罚息利率使用方式",
         maxSize = 1
      )
      private String penaltyOdiRateType;
      @V(
         desc = "计提日计提利息",
         notNull = false,
         length = "17",
         remark = "计提日计提利息",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal intAccruedCtd;
      @V(
         desc = "累计计提",
         notNull = false,
         length = "17",
         remark = "累计计提",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal intAccrued;
      @V(
         desc = "计提日利息调整",
         notNull = false,
         length = "17",
         remark = "计提日利息调整",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal intAdjCtd;
      @V(
         desc = "利息调增金额",
         notNull = false,
         length = "17",
         remark = "利息调增金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal intAdj;
      @V(
         desc = "结息日利息金额",
         notNull = false,
         length = "17",
         remark = "结息日利息金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal intPostedCtd;
      @V(
         desc = "结息金额",
         notNull = false,
         length = "17",
         remark = "结息金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal intPosted;
      @V(
         desc = "逾期利息",
         notNull = false,
         length = "17",
         remark = "逾期利息",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal intPastDue;
      @V(
         desc = "上日逾期利息",
         notNull = false,
         length = "17",
         remark = "上日逾期利息",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal lastIntPastDue;
      @V(
         desc = "结息频率",
         notNull = false,
         length = "5",
         remark = "结息频率",
         maxSize = 5
      )
      private String cycleFreq;
      @V(
         desc = "未实现利息",
         notNull = false,
         length = "17",
         remark = "未实现利息",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal discntRetainInt;
      @V(
         desc = "折扣利息标志",
         notNull = false,
         length = "1",
         remark = "折扣利息标志",
         maxSize = 1
      )
      private String discntUiFlag;
      @V(
         desc = "折扣付出利息",
         notNull = false,
         length = "17",
         remark = "折扣付出利息",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal uiInt;
      @V(
         desc = "折扣利息",
         notNull = false,
         length = "17",
         remark = "折扣利息",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal discntInt;
      @V(
         desc = "结息日",
         notNull = false,
         length = "2",
         remark = "结息日",
         maxSize = 2
      )
      private String intDay;
      @V(
         desc = "当期累计计息天数",
         notNull = false,
         length = "5",
         remark = "当期累计计息天数"
      )
      private Integer tdIntNumDays;
      @V(
         desc = "计息剩余天数",
         notNull = false,
         length = "5",
         remark = "计息剩余天数"
      )
      private Integer intRemDays;
      @V(
         desc = "执行利率下限",
         notNull = false,
         length = "15",
         remark = "执行利率下限",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal minIntRate;
      @V(
         desc = "执行利率上限",
         notNull = false,
         length = "15",
         remark = "执行利率上限",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal maxIntRate;
      @V(
         desc = "行内利率",
         notNull = false,
         length = "15",
         remark = "在人行基准利率调整后对客发布的行内利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal actualRate;
      @V(
         desc = "浮动利率",
         notNull = false,
         length = "15",
         remark = "浮动利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal floatRate;
      @V(
         desc = "税种",
         notNull = false,
         length = "5",
         remark = "税种",
         maxSize = 5
      )
      private String taxType;
      @V(
         desc = "税率",
         notNull = false,
         length = "15",
         remark = "税率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal taxRate;
      @V(
         desc = "计提日利息税",
         notNull = false,
         length = "17",
         remark = "计提日利息税",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal taxAccruedCtd;
      @V(
         desc = "结息周期内利息税累计金额",
         notNull = false,
         length = "17",
         remark = "结息周期内利息税累计金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal taxAccrued;
      @V(
         desc = "结息日利息税",
         notNull = false,
         length = "17",
         remark = "结息日利息税",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal taxPostedCtd;
      @V(
         desc = "利息税累计金额",
         notNull = false,
         length = "17",
         remark = "利息税累计金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal taxPosted;
      @V(
         desc = "资本化标志",
         notNull = false,
         length = "1",
         remark = "资本化标志",
         maxSize = 1
      )
      private String intCapFlag;
      @V(
         desc = "利率启用方式",
         notNull = false,
         length = "1",
         remark = "利率启用方式",
         maxSize = 1
      )
      private String intApplType;
      @V(
         desc = "利率类型",
         notNull = false,
         length = "5",
         remark = "利率类型",
         maxSize = 5
      )
      private String intType;
      @V(
         desc = "浮动百分比",
         notNull = false,
         length = "15",
         remark = "浮动百分比",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal spreadPercent;
      @V(
         desc = "利率变更周期",
         notNull = false,
         length = "5",
         remark = "利率变更周期",
         maxSize = 5
      )
      private String rollFreq;
      @V(
         desc = "利率变更日",
         notNull = false,
         length = "2",
         remark = "定义利率变动日期如果利率启用方式为R时需要指定利率变更的日系统根据上一个利率变更日利率变更周期和这个日计算下一个利率变更日",
         maxSize = 2
      )
      private String rollDay;
      @V(
         desc = "年基准天数",
         notNull = false,
         length = "3",
         remark = "年基准天数",
         maxSize = 3
      )
      private String yearBasis;
      @V(
         desc = "月基准",
         notNull = false,
         length = "3",
         remark = "月基准",
         maxSize = 3
      )
      private String monthBasis;
      @V(
         desc = "DAC值防篡改加密",
         notNull = false,
         length = "200",
         remark = "DAC值防篡改加密",
         maxSize = 200
      )
      private String dacValue;
      @V(
         desc = "最后修改柜员",
         notNull = false,
         length = "30",
         remark = "最后修改柜员",
         maxSize = 30
      )
      private String lastChangeUserId;
      @V(
         desc = "最后修改日期",
         notNull = false,
         remark = "最后修改日期"
      )
      private String lastChangeDate;
      @V(
         desc = "法人",
         notNull = false,
         length = "20",
         remark = "法人",
         maxSize = 20
      )
      private String company;

      public String getChClientName() {
         return this.chClientName;
      }

      public BigDecimal getAcctFixedRate() {
         return this.acctFixedRate;
      }

      public BigDecimal getAcctSpreadRate() {
         return this.acctSpreadRate;
      }

      public BigDecimal getAcctPercentRate() {
         return this.acctPercentRate;
      }

      public String getNextCycleDate() {
         return this.nextCycleDate;
      }

      public String getLastAccrualDate() {
         return this.lastAccrualDate;
      }

      public String getNextRollDate() {
         return this.nextRollDate;
      }

      public String getCalcBeginDate() {
         return this.calcBeginDate;
      }

      public String getCalcEndDate() {
         return this.calcEndDate;
      }

      public String getLastCycleDate() {
         return this.lastCycleDate;
      }

      public String getLastTrueCycleDate() {
         return this.lastTrueCycleDate;
      }

      public BigDecimal getIntCapAmt() {
         return this.intCapAmt;
      }

      public BigDecimal getUiPenaltyAmt() {
         return this.uiPenaltyAmt;
      }

      public String getIntClass() {
         return this.intClass;
      }

      public BigDecimal getSpreadRate() {
         return this.spreadRate;
      }

      public BigDecimal getRealRate() {
         return this.realRate;
      }

      public String getPenaltyOdiRateType() {
         return this.penaltyOdiRateType;
      }

      public BigDecimal getIntAccruedCtd() {
         return this.intAccruedCtd;
      }

      public BigDecimal getIntAccrued() {
         return this.intAccrued;
      }

      public BigDecimal getIntAdjCtd() {
         return this.intAdjCtd;
      }

      public BigDecimal getIntAdj() {
         return this.intAdj;
      }

      public BigDecimal getIntPostedCtd() {
         return this.intPostedCtd;
      }

      public BigDecimal getIntPosted() {
         return this.intPosted;
      }

      public BigDecimal getIntPastDue() {
         return this.intPastDue;
      }

      public BigDecimal getLastIntPastDue() {
         return this.lastIntPastDue;
      }

      public String getCycleFreq() {
         return this.cycleFreq;
      }

      public BigDecimal getDiscntRetainInt() {
         return this.discntRetainInt;
      }

      public String getDiscntUiFlag() {
         return this.discntUiFlag;
      }

      public BigDecimal getUiInt() {
         return this.uiInt;
      }

      public BigDecimal getDiscntInt() {
         return this.discntInt;
      }

      public String getIntDay() {
         return this.intDay;
      }

      public Integer getTdIntNumDays() {
         return this.tdIntNumDays;
      }

      public Integer getIntRemDays() {
         return this.intRemDays;
      }

      public BigDecimal getMinIntRate() {
         return this.minIntRate;
      }

      public BigDecimal getMaxIntRate() {
         return this.maxIntRate;
      }

      public BigDecimal getActualRate() {
         return this.actualRate;
      }

      public BigDecimal getFloatRate() {
         return this.floatRate;
      }

      public String getTaxType() {
         return this.taxType;
      }

      public BigDecimal getTaxRate() {
         return this.taxRate;
      }

      public BigDecimal getTaxAccruedCtd() {
         return this.taxAccruedCtd;
      }

      public BigDecimal getTaxAccrued() {
         return this.taxAccrued;
      }

      public BigDecimal getTaxPostedCtd() {
         return this.taxPostedCtd;
      }

      public BigDecimal getTaxPosted() {
         return this.taxPosted;
      }

      public String getIntCapFlag() {
         return this.intCapFlag;
      }

      public String getIntApplType() {
         return this.intApplType;
      }

      public String getIntType() {
         return this.intType;
      }

      public BigDecimal getSpreadPercent() {
         return this.spreadPercent;
      }

      public String getRollFreq() {
         return this.rollFreq;
      }

      public String getRollDay() {
         return this.rollDay;
      }

      public String getYearBasis() {
         return this.yearBasis;
      }

      public String getMonthBasis() {
         return this.monthBasis;
      }

      public String getDacValue() {
         return this.dacValue;
      }

      public String getLastChangeUserId() {
         return this.lastChangeUserId;
      }

      public String getLastChangeDate() {
         return this.lastChangeDate;
      }

      public String getCompany() {
         return this.company;
      }

      public void setChClientName(String chClientName) {
         this.chClientName = chClientName;
      }

      public void setAcctFixedRate(BigDecimal acctFixedRate) {
         this.acctFixedRate = acctFixedRate;
      }

      public void setAcctSpreadRate(BigDecimal acctSpreadRate) {
         this.acctSpreadRate = acctSpreadRate;
      }

      public void setAcctPercentRate(BigDecimal acctPercentRate) {
         this.acctPercentRate = acctPercentRate;
      }

      public void setNextCycleDate(String nextCycleDate) {
         this.nextCycleDate = nextCycleDate;
      }

      public void setLastAccrualDate(String lastAccrualDate) {
         this.lastAccrualDate = lastAccrualDate;
      }

      public void setNextRollDate(String nextRollDate) {
         this.nextRollDate = nextRollDate;
      }

      public void setCalcBeginDate(String calcBeginDate) {
         this.calcBeginDate = calcBeginDate;
      }

      public void setCalcEndDate(String calcEndDate) {
         this.calcEndDate = calcEndDate;
      }

      public void setLastCycleDate(String lastCycleDate) {
         this.lastCycleDate = lastCycleDate;
      }

      public void setLastTrueCycleDate(String lastTrueCycleDate) {
         this.lastTrueCycleDate = lastTrueCycleDate;
      }

      public void setIntCapAmt(BigDecimal intCapAmt) {
         this.intCapAmt = intCapAmt;
      }

      public void setUiPenaltyAmt(BigDecimal uiPenaltyAmt) {
         this.uiPenaltyAmt = uiPenaltyAmt;
      }

      public void setIntClass(String intClass) {
         this.intClass = intClass;
      }

      public void setSpreadRate(BigDecimal spreadRate) {
         this.spreadRate = spreadRate;
      }

      public void setRealRate(BigDecimal realRate) {
         this.realRate = realRate;
      }

      public void setPenaltyOdiRateType(String penaltyOdiRateType) {
         this.penaltyOdiRateType = penaltyOdiRateType;
      }

      public void setIntAccruedCtd(BigDecimal intAccruedCtd) {
         this.intAccruedCtd = intAccruedCtd;
      }

      public void setIntAccrued(BigDecimal intAccrued) {
         this.intAccrued = intAccrued;
      }

      public void setIntAdjCtd(BigDecimal intAdjCtd) {
         this.intAdjCtd = intAdjCtd;
      }

      public void setIntAdj(BigDecimal intAdj) {
         this.intAdj = intAdj;
      }

      public void setIntPostedCtd(BigDecimal intPostedCtd) {
         this.intPostedCtd = intPostedCtd;
      }

      public void setIntPosted(BigDecimal intPosted) {
         this.intPosted = intPosted;
      }

      public void setIntPastDue(BigDecimal intPastDue) {
         this.intPastDue = intPastDue;
      }

      public void setLastIntPastDue(BigDecimal lastIntPastDue) {
         this.lastIntPastDue = lastIntPastDue;
      }

      public void setCycleFreq(String cycleFreq) {
         this.cycleFreq = cycleFreq;
      }

      public void setDiscntRetainInt(BigDecimal discntRetainInt) {
         this.discntRetainInt = discntRetainInt;
      }

      public void setDiscntUiFlag(String discntUiFlag) {
         this.discntUiFlag = discntUiFlag;
      }

      public void setUiInt(BigDecimal uiInt) {
         this.uiInt = uiInt;
      }

      public void setDiscntInt(BigDecimal discntInt) {
         this.discntInt = discntInt;
      }

      public void setIntDay(String intDay) {
         this.intDay = intDay;
      }

      public void setTdIntNumDays(Integer tdIntNumDays) {
         this.tdIntNumDays = tdIntNumDays;
      }

      public void setIntRemDays(Integer intRemDays) {
         this.intRemDays = intRemDays;
      }

      public void setMinIntRate(BigDecimal minIntRate) {
         this.minIntRate = minIntRate;
      }

      public void setMaxIntRate(BigDecimal maxIntRate) {
         this.maxIntRate = maxIntRate;
      }

      public void setActualRate(BigDecimal actualRate) {
         this.actualRate = actualRate;
      }

      public void setFloatRate(BigDecimal floatRate) {
         this.floatRate = floatRate;
      }

      public void setTaxType(String taxType) {
         this.taxType = taxType;
      }

      public void setTaxRate(BigDecimal taxRate) {
         this.taxRate = taxRate;
      }

      public void setTaxAccruedCtd(BigDecimal taxAccruedCtd) {
         this.taxAccruedCtd = taxAccruedCtd;
      }

      public void setTaxAccrued(BigDecimal taxAccrued) {
         this.taxAccrued = taxAccrued;
      }

      public void setTaxPostedCtd(BigDecimal taxPostedCtd) {
         this.taxPostedCtd = taxPostedCtd;
      }

      public void setTaxPosted(BigDecimal taxPosted) {
         this.taxPosted = taxPosted;
      }

      public void setIntCapFlag(String intCapFlag) {
         this.intCapFlag = intCapFlag;
      }

      public void setIntApplType(String intApplType) {
         this.intApplType = intApplType;
      }

      public void setIntType(String intType) {
         this.intType = intType;
      }

      public void setSpreadPercent(BigDecimal spreadPercent) {
         this.spreadPercent = spreadPercent;
      }

      public void setRollFreq(String rollFreq) {
         this.rollFreq = rollFreq;
      }

      public void setRollDay(String rollDay) {
         this.rollDay = rollDay;
      }

      public void setYearBasis(String yearBasis) {
         this.yearBasis = yearBasis;
      }

      public void setMonthBasis(String monthBasis) {
         this.monthBasis = monthBasis;
      }

      public void setDacValue(String dacValue) {
         this.dacValue = dacValue;
      }

      public void setLastChangeUserId(String lastChangeUserId) {
         this.lastChangeUserId = lastChangeUserId;
      }

      public void setLastChangeDate(String lastChangeDate) {
         this.lastChangeDate = lastChangeDate;
      }

      public void setCompany(String company) {
         this.company = company;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof MbsdCore140001101Out.IntDetailArray)) {
            return false;
         } else {
            MbsdCore140001101Out.IntDetailArray other = (MbsdCore140001101Out.IntDetailArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label671: {
                  Object this$chClientName = this.getChClientName();
                  Object other$chClientName = other.getChClientName();
                  if (this$chClientName == null) {
                     if (other$chClientName == null) {
                        break label671;
                     }
                  } else if (this$chClientName.equals(other$chClientName)) {
                     break label671;
                  }

                  return false;
               }

               Object this$acctFixedRate = this.getAcctFixedRate();
               Object other$acctFixedRate = other.getAcctFixedRate();
               if (this$acctFixedRate == null) {
                  if (other$acctFixedRate != null) {
                     return false;
                  }
               } else if (!this$acctFixedRate.equals(other$acctFixedRate)) {
                  return false;
               }

               Object this$acctSpreadRate = this.getAcctSpreadRate();
               Object other$acctSpreadRate = other.getAcctSpreadRate();
               if (this$acctSpreadRate == null) {
                  if (other$acctSpreadRate != null) {
                     return false;
                  }
               } else if (!this$acctSpreadRate.equals(other$acctSpreadRate)) {
                  return false;
               }

               label650: {
                  Object this$acctPercentRate = this.getAcctPercentRate();
                  Object other$acctPercentRate = other.getAcctPercentRate();
                  if (this$acctPercentRate == null) {
                     if (other$acctPercentRate == null) {
                        break label650;
                     }
                  } else if (this$acctPercentRate.equals(other$acctPercentRate)) {
                     break label650;
                  }

                  return false;
               }

               label643: {
                  Object this$nextCycleDate = this.getNextCycleDate();
                  Object other$nextCycleDate = other.getNextCycleDate();
                  if (this$nextCycleDate == null) {
                     if (other$nextCycleDate == null) {
                        break label643;
                     }
                  } else if (this$nextCycleDate.equals(other$nextCycleDate)) {
                     break label643;
                  }

                  return false;
               }

               Object this$lastAccrualDate = this.getLastAccrualDate();
               Object other$lastAccrualDate = other.getLastAccrualDate();
               if (this$lastAccrualDate == null) {
                  if (other$lastAccrualDate != null) {
                     return false;
                  }
               } else if (!this$lastAccrualDate.equals(other$lastAccrualDate)) {
                  return false;
               }

               Object this$nextRollDate = this.getNextRollDate();
               Object other$nextRollDate = other.getNextRollDate();
               if (this$nextRollDate == null) {
                  if (other$nextRollDate != null) {
                     return false;
                  }
               } else if (!this$nextRollDate.equals(other$nextRollDate)) {
                  return false;
               }

               label622: {
                  Object this$calcBeginDate = this.getCalcBeginDate();
                  Object other$calcBeginDate = other.getCalcBeginDate();
                  if (this$calcBeginDate == null) {
                     if (other$calcBeginDate == null) {
                        break label622;
                     }
                  } else if (this$calcBeginDate.equals(other$calcBeginDate)) {
                     break label622;
                  }

                  return false;
               }

               label615: {
                  Object this$calcEndDate = this.getCalcEndDate();
                  Object other$calcEndDate = other.getCalcEndDate();
                  if (this$calcEndDate == null) {
                     if (other$calcEndDate == null) {
                        break label615;
                     }
                  } else if (this$calcEndDate.equals(other$calcEndDate)) {
                     break label615;
                  }

                  return false;
               }

               Object this$lastCycleDate = this.getLastCycleDate();
               Object other$lastCycleDate = other.getLastCycleDate();
               if (this$lastCycleDate == null) {
                  if (other$lastCycleDate != null) {
                     return false;
                  }
               } else if (!this$lastCycleDate.equals(other$lastCycleDate)) {
                  return false;
               }

               label601: {
                  Object this$lastTrueCycleDate = this.getLastTrueCycleDate();
                  Object other$lastTrueCycleDate = other.getLastTrueCycleDate();
                  if (this$lastTrueCycleDate == null) {
                     if (other$lastTrueCycleDate == null) {
                        break label601;
                     }
                  } else if (this$lastTrueCycleDate.equals(other$lastTrueCycleDate)) {
                     break label601;
                  }

                  return false;
               }

               Object this$intCapAmt = this.getIntCapAmt();
               Object other$intCapAmt = other.getIntCapAmt();
               if (this$intCapAmt == null) {
                  if (other$intCapAmt != null) {
                     return false;
                  }
               } else if (!this$intCapAmt.equals(other$intCapAmt)) {
                  return false;
               }

               label587: {
                  Object this$uiPenaltyAmt = this.getUiPenaltyAmt();
                  Object other$uiPenaltyAmt = other.getUiPenaltyAmt();
                  if (this$uiPenaltyAmt == null) {
                     if (other$uiPenaltyAmt == null) {
                        break label587;
                     }
                  } else if (this$uiPenaltyAmt.equals(other$uiPenaltyAmt)) {
                     break label587;
                  }

                  return false;
               }

               Object this$intClass = this.getIntClass();
               Object other$intClass = other.getIntClass();
               if (this$intClass == null) {
                  if (other$intClass != null) {
                     return false;
                  }
               } else if (!this$intClass.equals(other$intClass)) {
                  return false;
               }

               Object this$spreadRate = this.getSpreadRate();
               Object other$spreadRate = other.getSpreadRate();
               if (this$spreadRate == null) {
                  if (other$spreadRate != null) {
                     return false;
                  }
               } else if (!this$spreadRate.equals(other$spreadRate)) {
                  return false;
               }

               label566: {
                  Object this$realRate = this.getRealRate();
                  Object other$realRate = other.getRealRate();
                  if (this$realRate == null) {
                     if (other$realRate == null) {
                        break label566;
                     }
                  } else if (this$realRate.equals(other$realRate)) {
                     break label566;
                  }

                  return false;
               }

               label559: {
                  Object this$penaltyOdiRateType = this.getPenaltyOdiRateType();
                  Object other$penaltyOdiRateType = other.getPenaltyOdiRateType();
                  if (this$penaltyOdiRateType == null) {
                     if (other$penaltyOdiRateType == null) {
                        break label559;
                     }
                  } else if (this$penaltyOdiRateType.equals(other$penaltyOdiRateType)) {
                     break label559;
                  }

                  return false;
               }

               Object this$intAccruedCtd = this.getIntAccruedCtd();
               Object other$intAccruedCtd = other.getIntAccruedCtd();
               if (this$intAccruedCtd == null) {
                  if (other$intAccruedCtd != null) {
                     return false;
                  }
               } else if (!this$intAccruedCtd.equals(other$intAccruedCtd)) {
                  return false;
               }

               Object this$intAccrued = this.getIntAccrued();
               Object other$intAccrued = other.getIntAccrued();
               if (this$intAccrued == null) {
                  if (other$intAccrued != null) {
                     return false;
                  }
               } else if (!this$intAccrued.equals(other$intAccrued)) {
                  return false;
               }

               label538: {
                  Object this$intAdjCtd = this.getIntAdjCtd();
                  Object other$intAdjCtd = other.getIntAdjCtd();
                  if (this$intAdjCtd == null) {
                     if (other$intAdjCtd == null) {
                        break label538;
                     }
                  } else if (this$intAdjCtd.equals(other$intAdjCtd)) {
                     break label538;
                  }

                  return false;
               }

               label531: {
                  Object this$intAdj = this.getIntAdj();
                  Object other$intAdj = other.getIntAdj();
                  if (this$intAdj == null) {
                     if (other$intAdj == null) {
                        break label531;
                     }
                  } else if (this$intAdj.equals(other$intAdj)) {
                     break label531;
                  }

                  return false;
               }

               Object this$intPostedCtd = this.getIntPostedCtd();
               Object other$intPostedCtd = other.getIntPostedCtd();
               if (this$intPostedCtd == null) {
                  if (other$intPostedCtd != null) {
                     return false;
                  }
               } else if (!this$intPostedCtd.equals(other$intPostedCtd)) {
                  return false;
               }

               Object this$intPosted = this.getIntPosted();
               Object other$intPosted = other.getIntPosted();
               if (this$intPosted == null) {
                  if (other$intPosted != null) {
                     return false;
                  }
               } else if (!this$intPosted.equals(other$intPosted)) {
                  return false;
               }

               label510: {
                  Object this$intPastDue = this.getIntPastDue();
                  Object other$intPastDue = other.getIntPastDue();
                  if (this$intPastDue == null) {
                     if (other$intPastDue == null) {
                        break label510;
                     }
                  } else if (this$intPastDue.equals(other$intPastDue)) {
                     break label510;
                  }

                  return false;
               }

               label503: {
                  Object this$lastIntPastDue = this.getLastIntPastDue();
                  Object other$lastIntPastDue = other.getLastIntPastDue();
                  if (this$lastIntPastDue == null) {
                     if (other$lastIntPastDue == null) {
                        break label503;
                     }
                  } else if (this$lastIntPastDue.equals(other$lastIntPastDue)) {
                     break label503;
                  }

                  return false;
               }

               Object this$cycleFreq = this.getCycleFreq();
               Object other$cycleFreq = other.getCycleFreq();
               if (this$cycleFreq == null) {
                  if (other$cycleFreq != null) {
                     return false;
                  }
               } else if (!this$cycleFreq.equals(other$cycleFreq)) {
                  return false;
               }

               label489: {
                  Object this$discntRetainInt = this.getDiscntRetainInt();
                  Object other$discntRetainInt = other.getDiscntRetainInt();
                  if (this$discntRetainInt == null) {
                     if (other$discntRetainInt == null) {
                        break label489;
                     }
                  } else if (this$discntRetainInt.equals(other$discntRetainInt)) {
                     break label489;
                  }

                  return false;
               }

               Object this$discntUiFlag = this.getDiscntUiFlag();
               Object other$discntUiFlag = other.getDiscntUiFlag();
               if (this$discntUiFlag == null) {
                  if (other$discntUiFlag != null) {
                     return false;
                  }
               } else if (!this$discntUiFlag.equals(other$discntUiFlag)) {
                  return false;
               }

               label475: {
                  Object this$uiInt = this.getUiInt();
                  Object other$uiInt = other.getUiInt();
                  if (this$uiInt == null) {
                     if (other$uiInt == null) {
                        break label475;
                     }
                  } else if (this$uiInt.equals(other$uiInt)) {
                     break label475;
                  }

                  return false;
               }

               Object this$discntInt = this.getDiscntInt();
               Object other$discntInt = other.getDiscntInt();
               if (this$discntInt == null) {
                  if (other$discntInt != null) {
                     return false;
                  }
               } else if (!this$discntInt.equals(other$discntInt)) {
                  return false;
               }

               Object this$intDay = this.getIntDay();
               Object other$intDay = other.getIntDay();
               if (this$intDay == null) {
                  if (other$intDay != null) {
                     return false;
                  }
               } else if (!this$intDay.equals(other$intDay)) {
                  return false;
               }

               label454: {
                  Object this$tdIntNumDays = this.getTdIntNumDays();
                  Object other$tdIntNumDays = other.getTdIntNumDays();
                  if (this$tdIntNumDays == null) {
                     if (other$tdIntNumDays == null) {
                        break label454;
                     }
                  } else if (this$tdIntNumDays.equals(other$tdIntNumDays)) {
                     break label454;
                  }

                  return false;
               }

               label447: {
                  Object this$intRemDays = this.getIntRemDays();
                  Object other$intRemDays = other.getIntRemDays();
                  if (this$intRemDays == null) {
                     if (other$intRemDays == null) {
                        break label447;
                     }
                  } else if (this$intRemDays.equals(other$intRemDays)) {
                     break label447;
                  }

                  return false;
               }

               Object this$minIntRate = this.getMinIntRate();
               Object other$minIntRate = other.getMinIntRate();
               if (this$minIntRate == null) {
                  if (other$minIntRate != null) {
                     return false;
                  }
               } else if (!this$minIntRate.equals(other$minIntRate)) {
                  return false;
               }

               Object this$maxIntRate = this.getMaxIntRate();
               Object other$maxIntRate = other.getMaxIntRate();
               if (this$maxIntRate == null) {
                  if (other$maxIntRate != null) {
                     return false;
                  }
               } else if (!this$maxIntRate.equals(other$maxIntRate)) {
                  return false;
               }

               label426: {
                  Object this$actualRate = this.getActualRate();
                  Object other$actualRate = other.getActualRate();
                  if (this$actualRate == null) {
                     if (other$actualRate == null) {
                        break label426;
                     }
                  } else if (this$actualRate.equals(other$actualRate)) {
                     break label426;
                  }

                  return false;
               }

               label419: {
                  Object this$floatRate = this.getFloatRate();
                  Object other$floatRate = other.getFloatRate();
                  if (this$floatRate == null) {
                     if (other$floatRate == null) {
                        break label419;
                     }
                  } else if (this$floatRate.equals(other$floatRate)) {
                     break label419;
                  }

                  return false;
               }

               Object this$taxType = this.getTaxType();
               Object other$taxType = other.getTaxType();
               if (this$taxType == null) {
                  if (other$taxType != null) {
                     return false;
                  }
               } else if (!this$taxType.equals(other$taxType)) {
                  return false;
               }

               Object this$taxRate = this.getTaxRate();
               Object other$taxRate = other.getTaxRate();
               if (this$taxRate == null) {
                  if (other$taxRate != null) {
                     return false;
                  }
               } else if (!this$taxRate.equals(other$taxRate)) {
                  return false;
               }

               label398: {
                  Object this$taxAccruedCtd = this.getTaxAccruedCtd();
                  Object other$taxAccruedCtd = other.getTaxAccruedCtd();
                  if (this$taxAccruedCtd == null) {
                     if (other$taxAccruedCtd == null) {
                        break label398;
                     }
                  } else if (this$taxAccruedCtd.equals(other$taxAccruedCtd)) {
                     break label398;
                  }

                  return false;
               }

               label391: {
                  Object this$taxAccrued = this.getTaxAccrued();
                  Object other$taxAccrued = other.getTaxAccrued();
                  if (this$taxAccrued == null) {
                     if (other$taxAccrued == null) {
                        break label391;
                     }
                  } else if (this$taxAccrued.equals(other$taxAccrued)) {
                     break label391;
                  }

                  return false;
               }

               Object this$taxPostedCtd = this.getTaxPostedCtd();
               Object other$taxPostedCtd = other.getTaxPostedCtd();
               if (this$taxPostedCtd == null) {
                  if (other$taxPostedCtd != null) {
                     return false;
                  }
               } else if (!this$taxPostedCtd.equals(other$taxPostedCtd)) {
                  return false;
               }

               label377: {
                  Object this$taxPosted = this.getTaxPosted();
                  Object other$taxPosted = other.getTaxPosted();
                  if (this$taxPosted == null) {
                     if (other$taxPosted == null) {
                        break label377;
                     }
                  } else if (this$taxPosted.equals(other$taxPosted)) {
                     break label377;
                  }

                  return false;
               }

               Object this$intCapFlag = this.getIntCapFlag();
               Object other$intCapFlag = other.getIntCapFlag();
               if (this$intCapFlag == null) {
                  if (other$intCapFlag != null) {
                     return false;
                  }
               } else if (!this$intCapFlag.equals(other$intCapFlag)) {
                  return false;
               }

               label363: {
                  Object this$intApplType = this.getIntApplType();
                  Object other$intApplType = other.getIntApplType();
                  if (this$intApplType == null) {
                     if (other$intApplType == null) {
                        break label363;
                     }
                  } else if (this$intApplType.equals(other$intApplType)) {
                     break label363;
                  }

                  return false;
               }

               Object this$intType = this.getIntType();
               Object other$intType = other.getIntType();
               if (this$intType == null) {
                  if (other$intType != null) {
                     return false;
                  }
               } else if (!this$intType.equals(other$intType)) {
                  return false;
               }

               Object this$spreadPercent = this.getSpreadPercent();
               Object other$spreadPercent = other.getSpreadPercent();
               if (this$spreadPercent == null) {
                  if (other$spreadPercent != null) {
                     return false;
                  }
               } else if (!this$spreadPercent.equals(other$spreadPercent)) {
                  return false;
               }

               label342: {
                  Object this$rollFreq = this.getRollFreq();
                  Object other$rollFreq = other.getRollFreq();
                  if (this$rollFreq == null) {
                     if (other$rollFreq == null) {
                        break label342;
                     }
                  } else if (this$rollFreq.equals(other$rollFreq)) {
                     break label342;
                  }

                  return false;
               }

               label335: {
                  Object this$rollDay = this.getRollDay();
                  Object other$rollDay = other.getRollDay();
                  if (this$rollDay == null) {
                     if (other$rollDay == null) {
                        break label335;
                     }
                  } else if (this$rollDay.equals(other$rollDay)) {
                     break label335;
                  }

                  return false;
               }

               Object this$yearBasis = this.getYearBasis();
               Object other$yearBasis = other.getYearBasis();
               if (this$yearBasis == null) {
                  if (other$yearBasis != null) {
                     return false;
                  }
               } else if (!this$yearBasis.equals(other$yearBasis)) {
                  return false;
               }

               Object this$monthBasis = this.getMonthBasis();
               Object other$monthBasis = other.getMonthBasis();
               if (this$monthBasis == null) {
                  if (other$monthBasis != null) {
                     return false;
                  }
               } else if (!this$monthBasis.equals(other$monthBasis)) {
                  return false;
               }

               label314: {
                  Object this$dacValue = this.getDacValue();
                  Object other$dacValue = other.getDacValue();
                  if (this$dacValue == null) {
                     if (other$dacValue == null) {
                        break label314;
                     }
                  } else if (this$dacValue.equals(other$dacValue)) {
                     break label314;
                  }

                  return false;
               }

               label307: {
                  Object this$lastChangeUserId = this.getLastChangeUserId();
                  Object other$lastChangeUserId = other.getLastChangeUserId();
                  if (this$lastChangeUserId == null) {
                     if (other$lastChangeUserId == null) {
                        break label307;
                     }
                  } else if (this$lastChangeUserId.equals(other$lastChangeUserId)) {
                     break label307;
                  }

                  return false;
               }

               Object this$lastChangeDate = this.getLastChangeDate();
               Object other$lastChangeDate = other.getLastChangeDate();
               if (this$lastChangeDate == null) {
                  if (other$lastChangeDate != null) {
                     return false;
                  }
               } else if (!this$lastChangeDate.equals(other$lastChangeDate)) {
                  return false;
               }

               Object this$company = this.getCompany();
               Object other$company = other.getCompany();
               if (this$company == null) {
                  if (other$company != null) {
                     return false;
                  }
               } else if (!this$company.equals(other$company)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof MbsdCore140001101Out.IntDetailArray;
      }
      public String toString() {
         return "MbsdCore140001101Out.IntDetailArray(chClientName=" + this.getChClientName() + ", acctFixedRate=" + this.getAcctFixedRate() + ", acctSpreadRate=" + this.getAcctSpreadRate() + ", acctPercentRate=" + this.getAcctPercentRate() + ", nextCycleDate=" + this.getNextCycleDate() + ", lastAccrualDate=" + this.getLastAccrualDate() + ", nextRollDate=" + this.getNextRollDate() + ", calcBeginDate=" + this.getCalcBeginDate() + ", calcEndDate=" + this.getCalcEndDate() + ", lastCycleDate=" + this.getLastCycleDate() + ", lastTrueCycleDate=" + this.getLastTrueCycleDate() + ", intCapAmt=" + this.getIntCapAmt() + ", uiPenaltyAmt=" + this.getUiPenaltyAmt() + ", intClass=" + this.getIntClass() + ", spreadRate=" + this.getSpreadRate() + ", realRate=" + this.getRealRate() + ", penaltyOdiRateType=" + this.getPenaltyOdiRateType() + ", intAccruedCtd=" + this.getIntAccruedCtd() + ", intAccrued=" + this.getIntAccrued() + ", intAdjCtd=" + this.getIntAdjCtd() + ", intAdj=" + this.getIntAdj() + ", intPostedCtd=" + this.getIntPostedCtd() + ", intPosted=" + this.getIntPosted() + ", intPastDue=" + this.getIntPastDue() + ", lastIntPastDue=" + this.getLastIntPastDue() + ", cycleFreq=" + this.getCycleFreq() + ", discntRetainInt=" + this.getDiscntRetainInt() + ", discntUiFlag=" + this.getDiscntUiFlag() + ", uiInt=" + this.getUiInt() + ", discntInt=" + this.getDiscntInt() + ", intDay=" + this.getIntDay() + ", tdIntNumDays=" + this.getTdIntNumDays() + ", intRemDays=" + this.getIntRemDays() + ", minIntRate=" + this.getMinIntRate() + ", maxIntRate=" + this.getMaxIntRate() + ", actualRate=" + this.getActualRate() + ", floatRate=" + this.getFloatRate() + ", taxType=" + this.getTaxType() + ", taxRate=" + this.getTaxRate() + ", taxAccruedCtd=" + this.getTaxAccruedCtd() + ", taxAccrued=" + this.getTaxAccrued() + ", taxPostedCtd=" + this.getTaxPostedCtd() + ", taxPosted=" + this.getTaxPosted() + ", intCapFlag=" + this.getIntCapFlag() + ", intApplType=" + this.getIntApplType() + ", intType=" + this.getIntType() + ", spreadPercent=" + this.getSpreadPercent() + ", rollFreq=" + this.getRollFreq() + ", rollDay=" + this.getRollDay() + ", yearBasis=" + this.getYearBasis() + ", monthBasis=" + this.getMonthBasis() + ", dacValue=" + this.getDacValue() + ", lastChangeUserId=" + this.getLastChangeUserId() + ", lastChangeDate=" + this.getLastChangeDate() + ", company=" + this.getCompany() + ")";
      }
   }
}
