package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400100002In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400100002In.Body body;

   public Core1400100002In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400100002In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400100002In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100002In)) {
         return false;
      } else {
         Core1400100002In other = (Core1400100002In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100002In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "费用计提编号",
         notNull = false,
         length = "50",
         remark = "费用计提编号",
         maxSize = 50
      )
      private String feeIntNo;
      @V(
         desc = "所属机构号",
         notNull = false,
         length = "50",
         remark = "所属机构号",
         maxSize = 50
      )
      private String branch;
      @V(
         desc = "开始日期",
         notNull = false,
         remark = "开始日期"
      )
      private String startDate;
      @V(
         desc = "结束日期",
         notNull = false,
         remark = "结束日期"
      )
      private String endDate;
      @V(
         desc = "交易柜员",
         notNull = false,
         length = "30",
         remark = "交易柜员",
         maxSize = 30
      )
      private String userId;

      public String getFeeIntNo() {
         return this.feeIntNo;
      }

      public String getBranch() {
         return this.branch;
      }

      public String getStartDate() {
         return this.startDate;
      }

      public String getEndDate() {
         return this.endDate;
      }

      public String getUserId() {
         return this.userId;
      }

      public void setFeeIntNo(String feeIntNo) {
         this.feeIntNo = feeIntNo;
      }

      public void setBranch(String branch) {
         this.branch = branch;
      }

      public void setStartDate(String startDate) {
         this.startDate = startDate;
      }

      public void setEndDate(String endDate) {
         this.endDate = endDate;
      }

      public void setUserId(String userId) {
         this.userId = userId;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100002In.Body)) {
            return false;
         } else {
            Core1400100002In.Body other = (Core1400100002In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label71: {
                  Object this$feeIntNo = this.getFeeIntNo();
                  Object other$feeIntNo = other.getFeeIntNo();
                  if (this$feeIntNo == null) {
                     if (other$feeIntNo == null) {
                        break label71;
                     }
                  } else if (this$feeIntNo.equals(other$feeIntNo)) {
                     break label71;
                  }

                  return false;
               }

               Object this$branch = this.getBranch();
               Object other$branch = other.getBranch();
               if (this$branch == null) {
                  if (other$branch != null) {
                     return false;
                  }
               } else if (!this$branch.equals(other$branch)) {
                  return false;
               }

               label57: {
                  Object this$startDate = this.getStartDate();
                  Object other$startDate = other.getStartDate();
                  if (this$startDate == null) {
                     if (other$startDate == null) {
                        break label57;
                     }
                  } else if (this$startDate.equals(other$startDate)) {
                     break label57;
                  }

                  return false;
               }

               Object this$endDate = this.getEndDate();
               Object other$endDate = other.getEndDate();
               if (this$endDate == null) {
                  if (other$endDate != null) {
                     return false;
                  }
               } else if (!this$endDate.equals(other$endDate)) {
                  return false;
               }

               Object this$userId = this.getUserId();
               Object other$userId = other.getUserId();
               if (this$userId == null) {
                  if (other$userId == null) {
                     return true;
                  }
               } else if (this$userId.equals(other$userId)) {
                  return true;
               }

               return false;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100002In.Body;
      }
      public String toString() {
         return "Core1400100002In.Body(feeIntNo=" + this.getFeeIntNo() + ", branch=" + this.getBranch() + ", startDate=" + this.getStartDate() + ", endDate=" + this.getEndDate() + ", userId=" + this.getUserId() + ")";
      }
   }
}
