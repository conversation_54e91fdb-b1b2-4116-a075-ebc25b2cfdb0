package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;

@MessageIn
public class Core1200100034In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1200100034In.Body body;

   public Core1200100034In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1200100034In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1200100034In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200100034In)) {
         return false;
      } else {
         Core1200100034In other = (Core1200100034In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200100034In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "支取金额",
         notNull = false,
         length = "17",
         remark = "支取金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal debtAmt;
      @V(
         desc = "起息日",
         notNull = false,
         remark = "起息日"
      )
      private String intStartDate;
      @V(
         desc = "到期日",
         notNull = false,
         remark = "到期日"
      )
      private String matureDate;
      @V(
         desc = "年基准天数",
         notNull = false,
         length = "3",
         inDesc = "360-按360天计算日利率,365-按365天计算日利率,366-按366天计算日利率,ACT-按照每年实际天数计算日利率",
         remark = "年基准天数",
         maxSize = 3
      )
      private String yearBasis;
      @V(
         desc = "执行利率",
         notNull = false,
         length = "15",
         remark = "执行利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal realRate;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public BigDecimal getDebtAmt() {
         return this.debtAmt;
      }

      public String getIntStartDate() {
         return this.intStartDate;
      }

      public String getMatureDate() {
         return this.matureDate;
      }

      public String getYearBasis() {
         return this.yearBasis;
      }

      public BigDecimal getRealRate() {
         return this.realRate;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setDebtAmt(BigDecimal debtAmt) {
         this.debtAmt = debtAmt;
      }

      public void setIntStartDate(String intStartDate) {
         this.intStartDate = intStartDate;
      }

      public void setMatureDate(String matureDate) {
         this.matureDate = matureDate;
      }

      public void setYearBasis(String yearBasis) {
         this.yearBasis = yearBasis;
      }

      public void setRealRate(BigDecimal realRate) {
         this.realRate = realRate;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1200100034In.Body)) {
            return false;
         } else {
            Core1200100034In.Body other = (Core1200100034In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label95: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label95;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label95;
                  }

                  return false;
               }

               Object this$debtAmt = this.getDebtAmt();
               Object other$debtAmt = other.getDebtAmt();
               if (this$debtAmt == null) {
                  if (other$debtAmt != null) {
                     return false;
                  }
               } else if (!this$debtAmt.equals(other$debtAmt)) {
                  return false;
               }

               Object this$intStartDate = this.getIntStartDate();
               Object other$intStartDate = other.getIntStartDate();
               if (this$intStartDate == null) {
                  if (other$intStartDate != null) {
                     return false;
                  }
               } else if (!this$intStartDate.equals(other$intStartDate)) {
                  return false;
               }

               label74: {
                  Object this$matureDate = this.getMatureDate();
                  Object other$matureDate = other.getMatureDate();
                  if (this$matureDate == null) {
                     if (other$matureDate == null) {
                        break label74;
                     }
                  } else if (this$matureDate.equals(other$matureDate)) {
                     break label74;
                  }

                  return false;
               }

               label67: {
                  Object this$yearBasis = this.getYearBasis();
                  Object other$yearBasis = other.getYearBasis();
                  if (this$yearBasis == null) {
                     if (other$yearBasis == null) {
                        break label67;
                     }
                  } else if (this$yearBasis.equals(other$yearBasis)) {
                     break label67;
                  }

                  return false;
               }

               Object this$realRate = this.getRealRate();
               Object other$realRate = other.getRealRate();
               if (this$realRate == null) {
                  if (other$realRate != null) {
                     return false;
                  }
               } else if (!this$realRate.equals(other$realRate)) {
                  return false;
               }

               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo != null) {
                     return false;
                  }
               } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1200100034In.Body;
      }
      public String toString() {
         return "Core1200100034In.Body(baseAcctNo=" + this.getBaseAcctNo() + ", debtAmt=" + this.getDebtAmt() + ", intStartDate=" + this.getIntStartDate() + ", matureDate=" + this.getMatureDate() + ", yearBasis=" + this.getYearBasis() + ", realRate=" + this.getRealRate() + ", acctSeqNo=" + this.getAcctSeqNo() + ")";
      }
   }
}
