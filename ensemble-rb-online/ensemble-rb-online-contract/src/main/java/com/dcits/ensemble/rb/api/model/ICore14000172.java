package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000172In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000172Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000172 {
   String URL = "/rb/inq/book/printline";


   @ApiRemark("标准优化")
   @ApiDesc("根据上送账号查询对账簿打印信息")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0172"
   )
   @BusinessCategory("1400-查询")
   @FunctionCategory("RB06-凭证处理")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core14000172Out runService(Core14000172In var1);
}
