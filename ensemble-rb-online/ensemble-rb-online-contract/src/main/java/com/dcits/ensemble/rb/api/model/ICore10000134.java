package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10000134In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10000134Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore10000134 {
   String URL = "/rb/fin/current/cent/deal";


   @ApiRemark("分位金额处理")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "0134"
   )
   @FunctionCategory("RB08-特殊业务")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core10000134Out runService(Core10000134In var1);
}
