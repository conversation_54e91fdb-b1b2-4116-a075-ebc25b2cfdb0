package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;

@MessageOut
public class Core1400100221Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "期次代码",
      notNull = false,
      length = "50",
      remark = "期次代码",
      maxSize = 50
   )
   private String stageCode;

   public String getStageCode() {
      return this.stageCode;
   }

   public void setStageCode(String stageCode) {
      this.stageCode = stageCode;
   }

   public String toString() {
      return "Core1400100221Out(stageCode=" + this.getStageCode() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100221Out)) {
         return false;
      } else {
         Core1400100221Out other = (Core1400100221Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$stageCode = this.getStageCode();
            Object other$stageCode = other.getStageCode();
            if (this$stageCode == null) {
               if (other$stageCode != null) {
                  return false;
               }
            } else if (!this$stageCode.equals(other$stageCode)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100221Out;
   }
}
