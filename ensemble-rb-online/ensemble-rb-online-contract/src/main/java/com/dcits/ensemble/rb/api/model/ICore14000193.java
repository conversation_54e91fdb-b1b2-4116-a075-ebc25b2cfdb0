package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000193In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000193Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000193 {
   String URL = "/rb/nfin/card/in/check";


   @ApiRemark("预制卡入库检查")
   @ApiDesc("用于预制卡分段入库检查，检查入库号段是否在申请编号的号段内")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0193"
   )
   @BusinessCategory("1400-查询")
   @FunctionCategory("RB20-借记卡")
   @ConsumeSys("CBS/TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core14000193Out runService(Core14000193In var1);
}
