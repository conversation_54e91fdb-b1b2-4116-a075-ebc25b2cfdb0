package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000146In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000146Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000146 {
   String URL = "/rb/inq/service/charge/free";

   
   @ApiDesc("根据上送账号、客户号、起始时间、终止时间查询手续费相关信息")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0146"
   )
   @BusinessCategory("1400-查询")
   @FunctionCategory("RB18-费用管理")
   @ConsumeSys("CMG/CIS/EOS/STM/SFC/PR")
   @ApiUseStatus("PRODUCT-产品")
   Core14000146Out runService(Core14000146In var1);
}
