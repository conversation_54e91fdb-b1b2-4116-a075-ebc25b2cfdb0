package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;

@MessageOut
public class Core1200100200Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "期次代码",
      notNull = false,
      length = "50",
      remark = "期次代码",
      maxSize = 50
   )
   private String stageCode;
   @V(
      desc = "期次描述",
      notNull = false,
      length = "200",
      remark = "期次描述",
      maxSize = 200
   )
   private String stageCodeDesc;

   public String getStageCode() {
      return this.stageCode;
   }

   public String getStageCodeDesc() {
      return this.stageCodeDesc;
   }

   public void setStageCode(String stageCode) {
      this.stageCode = stageCode;
   }

   public void setStageCodeDesc(String stageCodeDesc) {
      this.stageCodeDesc = stageCodeDesc;
   }

   public String toString() {
      return "Core1200100200Out(stageCode=" + this.getStageCode() + ", stageCodeDesc=" + this.getStageCodeDesc() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200100200Out)) {
         return false;
      } else {
         Core1200100200Out other = (Core1200100200Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$stageCode = this.getStageCode();
            Object other$stageCode = other.getStageCode();
            if (this$stageCode == null) {
               if (other$stageCode != null) {
                  return false;
               }
            } else if (!this$stageCode.equals(other$stageCode)) {
               return false;
            }

            Object this$stageCodeDesc = this.getStageCodeDesc();
            Object other$stageCodeDesc = other.getStageCodeDesc();
            if (this$stageCodeDesc == null) {
               if (other$stageCodeDesc != null) {
                  return false;
               }
            } else if (!this$stageCodeDesc.equals(other$stageCodeDesc)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200100200Out;
   }
}
