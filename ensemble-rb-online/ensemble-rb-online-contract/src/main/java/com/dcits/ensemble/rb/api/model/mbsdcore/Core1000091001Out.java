package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1000091001Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1000091001Out.AcctArray> acctArray;

   public List<Core1000091001Out.AcctArray> getAcctArray() {
      return this.acctArray;
   }

   public void setAcctArray(List<Core1000091001Out.AcctArray> acctArray) {
      this.acctArray = acctArray;
   }

   public String toString() {
      return "Core1000091001Out(acctArray=" + this.getAcctArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1000091001Out)) {
         return false;
      } else {
         Core1000091001Out other = (Core1000091001Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$acctArray = this.getAcctArray();
            Object other$acctArray = other.getAcctArray();
            if (this$acctArray == null) {
               if (other$acctArray != null) {
                  return false;
               }
            } else if (!this$acctArray.equals(other$acctArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1000091001Out;
   }
   public static class AcctArray {
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "交易类型",
         notNull = false,
         length = "10",
         remark = "交易类型",
         maxSize = 10
      )
      private String tranType;
      @V(
         desc = "结算金额",
         notNull = false,
         length = "17",
         remark = "结算金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal settleAmt;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "还款金额控制标志",
         notNull = false,
         length = "1",
         remark = "扣款方式",
         maxSize = 1
      )
      private String recAmtCtrl;
      @V(
         desc = "交易日期",
         notNull = false,
         remark = "交易日期"
      )
      private String tranDate;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getTranType() {
         return this.tranType;
      }

      public BigDecimal getSettleAmt() {
         return this.settleAmt;
      }

      public String getCcy() {
         return this.ccy;
      }

      public String getRecAmtCtrl() {
         return this.recAmtCtrl;
      }

      public String getTranDate() {
         return this.tranDate;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setTranType(String tranType) {
         this.tranType = tranType;
      }

      public void setSettleAmt(BigDecimal settleAmt) {
         this.settleAmt = settleAmt;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setRecAmtCtrl(String recAmtCtrl) {
         this.recAmtCtrl = recAmtCtrl;
      }

      public void setTranDate(String tranDate) {
         this.tranDate = tranDate;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1000091001Out.AcctArray)) {
            return false;
         } else {
            Core1000091001Out.AcctArray other = (Core1000091001Out.AcctArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label119: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label119;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label119;
                  }

                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               label105: {
                  Object this$acctCcy = this.getAcctCcy();
                  Object other$acctCcy = other.getAcctCcy();
                  if (this$acctCcy == null) {
                     if (other$acctCcy == null) {
                        break label105;
                     }
                  } else if (this$acctCcy.equals(other$acctCcy)) {
                     break label105;
                  }

                  return false;
               }

               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo != null) {
                     return false;
                  }
               } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                  return false;
               }

               label91: {
                  Object this$tranType = this.getTranType();
                  Object other$tranType = other.getTranType();
                  if (this$tranType == null) {
                     if (other$tranType == null) {
                        break label91;
                     }
                  } else if (this$tranType.equals(other$tranType)) {
                     break label91;
                  }

                  return false;
               }

               Object this$settleAmt = this.getSettleAmt();
               Object other$settleAmt = other.getSettleAmt();
               if (this$settleAmt == null) {
                  if (other$settleAmt != null) {
                     return false;
                  }
               } else if (!this$settleAmt.equals(other$settleAmt)) {
                  return false;
               }

               label77: {
                  Object this$ccy = this.getCcy();
                  Object other$ccy = other.getCcy();
                  if (this$ccy == null) {
                     if (other$ccy == null) {
                        break label77;
                     }
                  } else if (this$ccy.equals(other$ccy)) {
                     break label77;
                  }

                  return false;
               }

               label70: {
                  Object this$recAmtCtrl = this.getRecAmtCtrl();
                  Object other$recAmtCtrl = other.getRecAmtCtrl();
                  if (this$recAmtCtrl == null) {
                     if (other$recAmtCtrl == null) {
                        break label70;
                     }
                  } else if (this$recAmtCtrl.equals(other$recAmtCtrl)) {
                     break label70;
                  }

                  return false;
               }

               Object this$tranDate = this.getTranDate();
               Object other$tranDate = other.getTranDate();
               if (this$tranDate == null) {
                  if (other$tranDate != null) {
                     return false;
                  }
               } else if (!this$tranDate.equals(other$tranDate)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1000091001Out.AcctArray;
      }
      public String toString() {
         return "Core1000091001Out.AcctArray(baseAcctNo=" + this.getBaseAcctNo() + ", prodType=" + this.getProdType() + ", acctCcy=" + this.getAcctCcy() + ", acctSeqNo=" + this.getAcctSeqNo() + ", tranType=" + this.getTranType() + ", settleAmt=" + this.getSettleAmt() + ", ccy=" + this.getCcy() + ", recAmtCtrl=" + this.getRecAmtCtrl() + ", tranDate=" + this.getTranDate() + ")";
      }
   }
}
