package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400109527In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400109527In.Body body;

   public Core1400109527In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400109527In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400109527In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400109527In)) {
         return false;
      } else {
         Core1400109527In other = (Core1400109527In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400109527In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "交易参考号",
         notNull = false,
         length = "50",
         remark = "交易参考号",
         maxSize = 50
      )
      private String reference;
      @V(
         desc = "渠道流水号",
         notNull = false,
         length = "50",
         remark = "渠道流水号",
         maxSize = 50
      )
      private String channelSeqNo;
      @V(
         desc = "子流水号",
         notNull = false,
         length = "100",
         remark = "子流水号",
         maxSize = 100
      )
      private String subSeqNo;
      @V(
         desc = "序号",
         notNull = false,
         length = "50",
         remark = "序号",
         maxSize = 50
      )
      private String seqNo;

      public String getReference() {
         return this.reference;
      }

      public String getChannelSeqNo() {
         return this.channelSeqNo;
      }

      public String getSubSeqNo() {
         return this.subSeqNo;
      }

      public String getSeqNo() {
         return this.seqNo;
      }

      public void setReference(String reference) {
         this.reference = reference;
      }

      public void setChannelSeqNo(String channelSeqNo) {
         this.channelSeqNo = channelSeqNo;
      }

      public void setSubSeqNo(String subSeqNo) {
         this.subSeqNo = subSeqNo;
      }

      public void setSeqNo(String seqNo) {
         this.seqNo = seqNo;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400109527In.Body)) {
            return false;
         } else {
            Core1400109527In.Body other = (Core1400109527In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label59: {
                  Object this$reference = this.getReference();
                  Object other$reference = other.getReference();
                  if (this$reference == null) {
                     if (other$reference == null) {
                        break label59;
                     }
                  } else if (this$reference.equals(other$reference)) {
                     break label59;
                  }

                  return false;
               }

               Object this$channelSeqNo = this.getChannelSeqNo();
               Object other$channelSeqNo = other.getChannelSeqNo();
               if (this$channelSeqNo == null) {
                  if (other$channelSeqNo != null) {
                     return false;
                  }
               } else if (!this$channelSeqNo.equals(other$channelSeqNo)) {
                  return false;
               }

               Object this$subSeqNo = this.getSubSeqNo();
               Object other$subSeqNo = other.getSubSeqNo();
               if (this$subSeqNo == null) {
                  if (other$subSeqNo != null) {
                     return false;
                  }
               } else if (!this$subSeqNo.equals(other$subSeqNo)) {
                  return false;
               }

               Object this$seqNo = this.getSeqNo();
               Object other$seqNo = other.getSeqNo();
               if (this$seqNo == null) {
                  if (other$seqNo != null) {
                     return false;
                  }
               } else if (!this$seqNo.equals(other$seqNo)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400109527In.Body;
      }
      public String toString() {
         return "Core1400109527In.Body(reference=" + this.getReference() + ", channelSeqNo=" + this.getChannelSeqNo() + ", subSeqNo=" + this.getSubSeqNo() + ", seqNo=" + this.getSeqNo() + ")";
      }
   }
}
