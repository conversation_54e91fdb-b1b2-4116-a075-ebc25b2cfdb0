package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400033401In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400033401In.Body body;

   public Core1400033401In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400033401In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400033401In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400033401In)) {
         return false;
      } else {
         Core1400033401In other = (Core1400033401In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400033401In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "开始日期",
         notNull = false,
         remark = "开始日期"
      )
      private String startDate;
      @V(
         desc = "结束日期",
         notNull = false,
         remark = "结束日期"
      )
      private String endDate;
      @V(
         desc = "所属机构号",
         notNull = false,
         length = "50",
         remark = "机构代码",
         maxSize = 50
      )
      private String branch;
      @V(
         desc = "凭证类型",
         notNull = false,
         length = "10",
         remark = "凭证类型",
         maxSize = 10
      )
      private String docType;
      @V(
         desc = "业务流水号",
         notNull = false,
         length = "50",
         remark = "支付流水号",
         maxSize = 50
      )
      private String serialNo;
      @V(
         desc = "交易参考号",
         notNull = false,
         length = "50",
         remark = "交易参考号",
         maxSize = 50
      )
      private String reference;
      @V(
         desc = "限额维护类型",
         notNull = false,
         length = "1",
         in = "A,U,D",
         inDesc = "A-新增 ,U-修改 ,D-删除",
         remark = "限额维护类型",
         maxSize = 1
      )
      private String operType;
      @V(
         desc = "票据类型",
         notNull = false,
         length = "5",
         in = "P,E",
         inDesc = "P-纸质,E-电子,CT00-可转让汇票,CT01-不可转让汇票,CT02-现金汇票",
         remark = "票据类型",
         maxSize = 5
      )
      private String billType;
      @V(
         desc = "票据号码",
         notNull = false,
         length = "50",
         remark = "票据号码",
         maxSize = 50
      )
      private String billNo;
      @V(
         desc = "票据状态",
         notNull = false,
         length = "2",
         in = "00,01,02,03,04,05,06",
         inDesc = "00-录入,01-签发,02-兑付,03-退回,04-挂失,05-解挂,06-删除,08-挂失止付,09-公示催告,11-未复核,12-已复核,13-已打印,14-已签章核对,15-已签发冲正,16-已移存",
         remark = "票据状态",
         maxSize = 2
      )
      private String billStatus;

      public String getStartDate() {
         return this.startDate;
      }

      public String getEndDate() {
         return this.endDate;
      }

      public String getBranch() {
         return this.branch;
      }

      public String getDocType() {
         return this.docType;
      }

      public String getSerialNo() {
         return this.serialNo;
      }

      public String getReference() {
         return this.reference;
      }

      public String getOperType() {
         return this.operType;
      }

      public String getBillType() {
         return this.billType;
      }

      public String getBillNo() {
         return this.billNo;
      }

      public String getBillStatus() {
         return this.billStatus;
      }

      public void setStartDate(String startDate) {
         this.startDate = startDate;
      }

      public void setEndDate(String endDate) {
         this.endDate = endDate;
      }

      public void setBranch(String branch) {
         this.branch = branch;
      }

      public void setDocType(String docType) {
         this.docType = docType;
      }

      public void setSerialNo(String serialNo) {
         this.serialNo = serialNo;
      }

      public void setReference(String reference) {
         this.reference = reference;
      }

      public void setOperType(String operType) {
         this.operType = operType;
      }

      public void setBillType(String billType) {
         this.billType = billType;
      }

      public void setBillNo(String billNo) {
         this.billNo = billNo;
      }

      public void setBillStatus(String billStatus) {
         this.billStatus = billStatus;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400033401In.Body)) {
            return false;
         } else {
            Core1400033401In.Body other = (Core1400033401In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$startDate = this.getStartDate();
               Object other$startDate = other.getStartDate();
               if (this$startDate == null) {
                  if (other$startDate != null) {
                     return false;
                  }
               } else if (!this$startDate.equals(other$startDate)) {
                  return false;
               }

               Object this$endDate = this.getEndDate();
               Object other$endDate = other.getEndDate();
               if (this$endDate == null) {
                  if (other$endDate != null) {
                     return false;
                  }
               } else if (!this$endDate.equals(other$endDate)) {
                  return false;
               }

               Object this$branch = this.getBranch();
               Object other$branch = other.getBranch();
               if (this$branch == null) {
                  if (other$branch != null) {
                     return false;
                  }
               } else if (!this$branch.equals(other$branch)) {
                  return false;
               }

               label110: {
                  Object this$docType = this.getDocType();
                  Object other$docType = other.getDocType();
                  if (this$docType == null) {
                     if (other$docType == null) {
                        break label110;
                     }
                  } else if (this$docType.equals(other$docType)) {
                     break label110;
                  }

                  return false;
               }

               label103: {
                  Object this$serialNo = this.getSerialNo();
                  Object other$serialNo = other.getSerialNo();
                  if (this$serialNo == null) {
                     if (other$serialNo == null) {
                        break label103;
                     }
                  } else if (this$serialNo.equals(other$serialNo)) {
                     break label103;
                  }

                  return false;
               }

               Object this$reference = this.getReference();
               Object other$reference = other.getReference();
               if (this$reference == null) {
                  if (other$reference != null) {
                     return false;
                  }
               } else if (!this$reference.equals(other$reference)) {
                  return false;
               }

               label89: {
                  Object this$operType = this.getOperType();
                  Object other$operType = other.getOperType();
                  if (this$operType == null) {
                     if (other$operType == null) {
                        break label89;
                     }
                  } else if (this$operType.equals(other$operType)) {
                     break label89;
                  }

                  return false;
               }

               label82: {
                  Object this$billType = this.getBillType();
                  Object other$billType = other.getBillType();
                  if (this$billType == null) {
                     if (other$billType == null) {
                        break label82;
                     }
                  } else if (this$billType.equals(other$billType)) {
                     break label82;
                  }

                  return false;
               }

               Object this$billNo = this.getBillNo();
               Object other$billNo = other.getBillNo();
               if (this$billNo == null) {
                  if (other$billNo != null) {
                     return false;
                  }
               } else if (!this$billNo.equals(other$billNo)) {
                  return false;
               }

               Object this$billStatus = this.getBillStatus();
               Object other$billStatus = other.getBillStatus();
               if (this$billStatus == null) {
                  if (other$billStatus != null) {
                     return false;
                  }
               } else if (!this$billStatus.equals(other$billStatus)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400033401In.Body;
      }
      public String toString() {
         return "Core1400033401In.Body(startDate=" + this.getStartDate() + ", endDate=" + this.getEndDate() + ", branch=" + this.getBranch() + ", docType=" + this.getDocType() + ", serialNo=" + this.getSerialNo() + ", reference=" + this.getReference() + ", operType=" + this.getOperType() + ", billType=" + this.getBillType() + ", billNo=" + this.getBillNo() + ", billStatus=" + this.getBillStatus() + ")";
      }
   }
}
