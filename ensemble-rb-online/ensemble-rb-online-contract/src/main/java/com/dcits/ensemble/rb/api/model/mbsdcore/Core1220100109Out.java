package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;

@MessageOut
public class Core1220100109Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "批次号",
      notNull = false,
      length = "50",
      remark = "批次号",
      maxSize = 50
   )
   private String batchNo;

   public String getBatchNo() {
      return this.batchNo;
   }

   public void setBatchNo(String batchNo) {
      this.batchNo = batchNo;
   }

   public String toString() {
      return "Core1220100109Out(batchNo=" + this.getBatchNo() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1220100109Out)) {
         return false;
      } else {
         Core1220100109Out other = (Core1220100109Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$batchNo = this.getBatchNo();
            Object other$batchNo = other.getBatchNo();
            if (this$batchNo == null) {
               if (other$batchNo != null) {
                  return false;
               }
            } else if (!this$batchNo.equals(other$batchNo)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1220100109Out;
   }
}
