package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1220091001In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1220091001In.Body body;

   public Core1220091001In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1220091001In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1220091001In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1220091001In)) {
         return false;
      } else {
         Core1220091001In other = (Core1220091001In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1220091001In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "文件路径",
         notNull = true,
         length = "200",
         remark = "文件路径",
         maxSize = 200
      )
      private String filePath;
      @V(
         desc = "文件名称",
         notNull = true,
         length = "200",
         remark = "文件名称",
         maxSize = 200
      )
      private String fileName;

      public String getFilePath() {
         return this.filePath;
      }

      public String getFileName() {
         return this.fileName;
      }

      public void setFilePath(String filePath) {
         this.filePath = filePath;
      }

      public void setFileName(String fileName) {
         this.fileName = fileName;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1220091001In.Body)) {
            return false;
         } else {
            Core1220091001In.Body other = (Core1220091001In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$filePath = this.getFilePath();
               Object other$filePath = other.getFilePath();
               if (this$filePath == null) {
                  if (other$filePath != null) {
                     return false;
                  }
               } else if (!this$filePath.equals(other$filePath)) {
                  return false;
               }

               Object this$fileName = this.getFileName();
               Object other$fileName = other.getFileName();
               if (this$fileName == null) {
                  if (other$fileName != null) {
                     return false;
                  }
               } else if (!this$fileName.equals(other$fileName)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1220091001In.Body;
      }
      public String toString() {
         return "Core1220091001In.Body(filePath=" + this.getFilePath() + ", fileName=" + this.getFileName() + ")";
      }
   }
}
