package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400001001Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400001001Out.TranHistArray> tranHistArray;
   @V(
      desc = "总贷方金额",
      notNull = false,
      length = "17",
      remark = "总贷方金额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal totalCrAmt;
   @V(
      desc = "总借方金额",
      notNull = false,
      length = "17",
      remark = "总借方金额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal totalDrAmt;

   public List<Core1400001001Out.TranHistArray> getTranHistArray() {
      return this.tranHistArray;
   }

   public BigDecimal getTotalCrAmt() {
      return this.totalCrAmt;
   }

   public BigDecimal getTotalDrAmt() {
      return this.totalDrAmt;
   }

   public void setTranHistArray(List<Core1400001001Out.TranHistArray> tranHistArray) {
      this.tranHistArray = tranHistArray;
   }

   public void setTotalCrAmt(BigDecimal totalCrAmt) {
      this.totalCrAmt = totalCrAmt;
   }

   public void setTotalDrAmt(BigDecimal totalDrAmt) {
      this.totalDrAmt = totalDrAmt;
   }

   public String toString() {
      return "Core1400001001Out(tranHistArray=" + this.getTranHistArray() + ", totalCrAmt=" + this.getTotalCrAmt() + ", totalDrAmt=" + this.getTotalDrAmt() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400001001Out)) {
         return false;
      } else {
         Core1400001001Out other = (Core1400001001Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            label49: {
               Object this$tranHistArray = this.getTranHistArray();
               Object other$tranHistArray = other.getTranHistArray();
               if (this$tranHistArray == null) {
                  if (other$tranHistArray == null) {
                     break label49;
                  }
               } else if (this$tranHistArray.equals(other$tranHistArray)) {
                  break label49;
               }

               return false;
            }

            Object this$totalCrAmt = this.getTotalCrAmt();
            Object other$totalCrAmt = other.getTotalCrAmt();
            if (this$totalCrAmt == null) {
               if (other$totalCrAmt != null) {
                  return false;
               }
            } else if (!this$totalCrAmt.equals(other$totalCrAmt)) {
               return false;
            }

            Object this$totalDrAmt = this.getTotalDrAmt();
            Object other$totalDrAmt = other.getTotalDrAmt();
            if (this$totalDrAmt == null) {
               if (other$totalDrAmt != null) {
                  return false;
               }
            } else if (!this$totalDrAmt.equals(other$totalDrAmt)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400001001Out;
   }
   public static class TranHistArray {
      @V(
         desc = "对方账户产品类型",
         notNull = false,
         length = "20",
         remark = "对方账户产品类型",
         maxSize = 20
      )
      private String othProdType;
      @V(
         desc = "对方账户币种",
         notNull = false,
         length = "3",
         remark = "对方账户币种",
         maxSize = 3
      )
      private String othAcctCcy;
      @V(
         desc = "证件类型",
         notNull = false,
         length = "3",
         remark = "证件类型",
         maxSize = 3
      )
      private String documentType;
      @V(
         desc = "客户名称",
         notNull = false,
         length = "200",
         remark = "客户名称",
         maxSize = 200
      )
      private String clientName;
      @V(
         desc = "渠道流水号",
         notNull = false,
         length = "50",
         remark = "渠道流水号",
         maxSize = 50
      )
      private String channelSeqNo;
      @V(
         desc = "期限类型",
         notNull = false,
         length = "1",
         remark = "期限类型",
         maxSize = 1
      )
      private String termType;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "账户描述",
         notNull = false,
         length = "200",
         remark = "账户描述,目前同账户名称",
         maxSize = 200
      )
      private String acctDesc;
      @V(
         desc = "交易类型",
         notNull = false,
         length = "10",
         remark = "交易类型",
         maxSize = 10
      )
      private String tranType;
      @V(
         desc = "客户类型",
         notNull = false,
         length = "3",
         remark = "客户大类，目前一般分为个人，公司，金融机构和内部客户。取之于CIF_CLIENT_TYPE.CLIENT_TYPE",
         maxSize = 3
      )
      private String clientType;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "账户开户行",
         notNull = false,
         length = "50",
         remark = "账户实际开户机构，柜面为实际网点机构，线上渠道一般为对应主账户的实际开户机构",
         maxSize = 50
      )
      private String acctBranch;
      @V(
         desc = "实际余额",
         notNull = false,
         length = "17",
         remark = "实际余额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal actualBal;
      @V(
         desc = "存期期限",
         notNull = false,
         length = "5",
         remark = "期限",
         maxSize = 5
      )
      private String term;
      @V(
         desc = "客户中文名称",
         notNull = false,
         length = "200",
         remark = "客户中文名称",
         maxSize = 200
      )
      private String chClientName;
      @V(
         desc = "交易描述",
         notNull = false,
         length = "200",
         remark = "交易描述",
         maxSize = 200
      )
      private String tranDesc;
      @V(
         desc = "交易对手身份证件/证明文件类型",
         notNull = false,
         length = "3",
         remark = "交易对手身份证件/证明文件类型",
         maxSize = 3
      )
      private String othDocumentType;
      @V(
         desc = "对方账户描述",
         notNull = false,
         length = "200",
         remark = "对方账户描述",
         maxSize = 200
      )
      private String othAcctDesc;
      @V(
         desc = "账户开户日期",
         notNull = false,
         remark = "账户开户日期"
      )
      private String acctOpenDate;
      @V(
         desc = "对方账户序列号",
         notNull = false,
         length = "5",
         remark = "对方账户序列号",
         maxSize = 5
      )
      private String othAcctSeqNo;
      @V(
         desc = "金额类型",
         notNull = false,
         length = "10",
         remark = "金额类型",
         maxSize = 10
      )
      private String amtType;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "借贷标识",
         notNull = false,
         length = "1",
         remark = "借贷标识",
         maxSize = 1
      )
      private String crDrMaintInd;
      @V(
         desc = "交易对手身份证件/证明文件号码",
         notNull = false,
         length = "50",
         remark = "交易对手身份证件/证明文件号码",
         maxSize = 50
      )
      private String othDocumentId;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "对方账号/卡号",
         notNull = false,
         length = "50",
         remark = "对方账号/卡号",
         maxSize = 50
      )
      private String othBaseAcctNo;
      @V(
         desc = "对方账户开户机构",
         notNull = false,
         length = "50",
         remark = "对方账户开户机构",
         maxSize = 50
      )
      private String othBranch;
      @V(
         desc = "交易金额",
         notNull = false,
         length = "17",
         remark = "交易金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal tranAmt;
      @V(
         desc = "证件号码",
         notNull = false,
         length = "50",
         remark = "证件号码",
         maxSize = 50
      )
      private String documentId;
      @V(
         desc = "交易日期",
         notNull = false,
         remark = "交易日期"
      )
      private String tranDate;
      @V(
         desc = "到期日",
         notNull = false,
         remark = "到期日"
      )
      private String matureDate;
      @V(
         desc = "交易前余额",
         notNull = false,
         length = "17",
         remark = "交易前余额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal previousBalAmt;
      @V(
         desc = "原交易金额",
         notNull = false,
         length = "17",
         remark = "原交易金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal origTranAmt;
      @V(
         desc = "发放金额",
         notNull = false,
         length = "17",
         remark = "发放金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal ddAmt;
      @V(
         desc = "透支金额",
         notNull = false,
         length = "17",
         remark = "透支金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal odAmount;
      @V(
         desc = "基础等值金额",
         notNull = false,
         length = "17",
         remark = "基础等值金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal baseEquivAmt;
      @V(
         desc = "买方汇率值",
         notNull = false,
         length = "15",
         remark = "买方汇率值",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal fromXrate;
      @V(
         desc = "卖方汇率值",
         notNull = false,
         length = "15",
         remark = "卖方汇率值",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal toXrate;
      @V(
         desc = "子流水号",
         notNull = false,
         length = "100",
         remark = "子流水号",
         maxSize = 100
      )
      private String subSeqNo;
      @V(
         desc = "对方账户名称",
         notNull = false,
         length = "200",
         remark = "对方账户名称",
         maxSize = 200
      )
      private String othAcctName;
      @V(
         desc = "对方银行名称",
         notNull = false,
         length = "50",
         remark = "对方银行名称",
         maxSize = 50
      )
      private String othBankName;
      @V(
         desc = "对方银行代码",
         notNull = false,
         length = "20",
         remark = "对方银行代码",
         maxSize = 20
      )
      private String othBankCode;
      @V(
         desc = "收款行行名",
         notNull = false,
         length = "200",
         remark = "收款行行名",
         maxSize = 200
      )
      private String othBranchName;
      @V(
         desc = "摘要码",
         notNull = false,
         length = "30",
         remark = "摘要码",
         maxSize = 30
      )
      private String narrativeCode;
      @V(
         desc = "摘要码描述",
         notNull = false,
         length = "100",
         remark = "摘要码描述",
         maxSize = 100
      )
      private String narrativeCodeDesc;
      @V(
         desc = "渠道类型",
         notNull = false,
         length = "10",
         remark = "渠道类型",
         maxSize = 10
      )
      private String sourceType;
      @V(
         desc = "源模块",
         notNull = false,
         length = "3",
         remark = "源模块",
         maxSize = 3
      )
      private String sourceModule;
      @V(
         desc = "小额免密标志",
         notNull = false,
         length = "1",
         remark = "小额免密标志",
         maxSize = 1
      )
      private String piFlag;
      @V(
         desc = "卖方牌价类型",
         notNull = false,
         length = "1",
         remark = "牌价类型",
         maxSize = 1
      )
      private String toId;
      @V(
         desc = "授权柜员",
         notNull = false,
         length = "30",
         remark = "授权柜员",
         maxSize = 30
      )
      private String authUserId;
      @V(
         desc = "余额类型",
         notNull = false,
         length = "2",
         remark = "余额类型",
         maxSize = 2
      )
      private String balType;
      @V(
         desc = "交易柜员",
         notNull = false,
         length = "30",
         remark = "交易柜员",
         maxSize = 30
      )
      private String userId;
      @V(
         desc = "所属机构号",
         notNull = false,
         length = "50",
         remark = "所属机构号",
         maxSize = 50
      )
      private String branch;
      @V(
         desc = "银行名称",
         notNull = false,
         length = "50",
         remark = "银行名称",
         maxSize = 50
      )
      private String bankName;
      @V(
         desc = "银行代码",
         notNull = false,
         length = "20",
         remark = "银行代码",
         maxSize = 20
      )
      private String bankCode;
      @V(
         desc = "他行交易日期",
         notNull = false,
         length = "30",
         remark = "他行交易日期",
         maxSize = 30
      )
      private String contraTranDate;
      @V(
         desc = "对方交易流水号",
         notNull = false,
         length = "50",
         remark = "对方交易流水号",
         maxSize = 50
      )
      private String othSeqNo;
      @V(
         desc = "对方交易参考号",
         notNull = false,
         length = "50",
         remark = "对方交易参考号",
         maxSize = 50
      )
      private String othReference;
      @V(
         desc = "凭证类型",
         notNull = false,
         length = "10",
         remark = "凭证类型",
         maxSize = 10
      )
      private String docType;
      @V(
         desc = "前缀",
         notNull = false,
         length = "10",
         remark = "前缀",
         maxSize = 10
      )
      private String prefix;
      @V(
         desc = "凭证号码",
         notNull = false,
         length = "50",
         remark = "凭证号码",
         maxSize = 50
      )
      private String voucherNo;
      @V(
         desc = "支取方式",
         notNull = false,
         length = "1",
         remark = "支取方式",
         maxSize = 1
      )
      private String withdrawalType;
      @V(
         desc = "银行交易序号",
         notNull = false,
         length = "50",
         remark = "银行交易序号,单一机构下发生交易序号，按顺序递增 格式为 \"机构_序号",
         maxSize = 50
      )
      private String bankSeqNo;
      @V(
         desc = "复核柜员",
         notNull = false,
         length = "30",
         remark = "复核柜员",
         maxSize = 30
      )
      private String apprUserId;
      @V(
         desc = "交易参考号",
         notNull = false,
         length = "50",
         remark = "交易参考号",
         maxSize = 50
      )
      private String reference;
      @V(
         desc = "交易种类",
         notNull = false,
         length = "5",
         remark = "交易种类",
         maxSize = 5
      )
      private String tranCategory;
      @V(
         desc = "冲正交易类型",
         notNull = false,
         length = "10",
         remark = "冲正交易类型",
         maxSize = 10
      )
      private String reversalTranType;
      @V(
         desc = "冲正日期",
         notNull = false,
         remark = "冲正日期"
      )
      private String reversalDate;
      @V(
         desc = "序号",
         notNull = false,
         length = "50",
         remark = "序号",
         maxSize = 50
      )
      private String seqNo;
      @V(
         desc = "摘要",
         notNull = false,
         length = "500",
         remark = "开户时的账号用途，销户时的销户原因",
         maxSize = 500
      )
      private String narrative;
      @V(
         desc = "交易附言",
         notNull = false,
         length = "2000",
         remark = "交易附言",
         maxSize = 2000
      )
      private String tranNote;
      @V(
         desc = "主交易序号",
         notNull = false,
         length = "50",
         remark = "主交易序号",
         maxSize = 50
      )
      private String primaryTranSeqNo;
      @V(
         desc = "资金冻结流水号",
         notNull = false,
         length = "50",
         remark = "记录冻结编号信息",
         maxSize = 50
      )
      private String fhSeqNo;
      @V(
         desc = "他行等值金额",
         notNull = false,
         length = "17",
         remark = "他行等值金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal contraEquivAmt;
      @V(
         desc = "交叉汇率",
         notNull = false,
         length = "15",
         remark = "交叉汇率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal crossRate;
      @V(
         desc = "起始币种",
         notNull = false,
         length = "3",
         remark = "源币种",
         maxSize = 3
      )
      private String fromCcy;
      @V(
         desc = "移出金额",
         notNull = false,
         length = "17",
         remark = "移出金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal fromAmount;
      @V(
         desc = "买方交易汇率标志",
         notNull = false,
         length = "1",
         remark = "买方交易汇率标志",
         maxSize = 1
      )
      private String fromRateFlag;
      @V(
         desc = "目的币种",
         notNull = false,
         length = "3",
         remark = "目标币种",
         maxSize = 3
      )
      private String toCcy;
      @V(
         desc = "移入金额",
         notNull = false,
         length = "17",
         remark = "移入金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal toAmount;
      @V(
         desc = "卖方交易汇率标志",
         notNull = false,
         length = "1",
         remark = "卖方交易汇率标志",
         maxSize = 1
      )
      private String toRateFlag;
      @V(
         desc = "根据实际交易时修改交叉汇率计算的金额",
         notNull = false,
         length = "17",
         remark = "根据实际交易时修改交叉汇率计算的金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal ovToAmount;
      @V(
         desc = "牌价类型",
         notNull = false,
         length = "1",
         remark = "牌价类型",
         maxSize = 1
      )
      private String quoteType;
      @V(
         desc = "终端号",
         notNull = false,
         length = "50",
         remark = "终端号",
         maxSize = 50
      )
      private String terminalId;
      @V(
         desc = "跟踪ID",
         notNull = false,
         length = "200",
         remark = "跟踪ID",
         maxSize = 200
      )
      private String traceId;
      @V(
         desc = "现金项目",
         notNull = false,
         length = "10",
         remark = "现金项目",
         maxSize = 10
      )
      private String cashItem;
      @V(
         desc = "业务处理状态",
         notNull = false,
         length = "1",
         remark = "业务处理状态",
         maxSize = 1
      )
      private String tranStatus;
      @V(
         desc = "利润中心",
         notNull = false,
         length = "20",
         remark = "利润中心",
         maxSize = 20
      )
      private String profitCenter;
      @V(
         desc = "账套",
         notNull = false,
         length = "10",
         remark = "账套",
         maxSize = 10
      )
      private String businessUnit;
      @V(
         desc = "事件类型",
         notNull = false,
         length = "20",
         remark = "事件类型",
         maxSize = 20
      )
      private String eventType;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "打印次数",
         notNull = false,
         length = "5",
         remark = "银行承兑汇票登记簿凭证打印次数"
      )
      private Integer printCnt;
      @V(
         desc = "批次号",
         notNull = false,
         length = "50",
         remark = "批次号",
         maxSize = 50
      )
      private String batchNo;
      @V(
         desc = "中间业务类型",
         notNull = false,
         length = "10",
         remark = "中间业务类型",
         maxSize = 10
      )
      private String bizType;
      @V(
         desc = "是否补登存",
         notNull = false,
         length = "1",
         remark = "是否补登存",
         maxSize = 1
      )
      private String pbkUpdFlag;
      @V(
         desc = "是否冲正标志",
         notNull = false,
         length = "1",
         remark = "是否冲正标志",
         maxSize = 1
      )
      private String reversal;
      @V(
         desc = "交款单位",
         notNull = false,
         length = "200",
         remark = "交款单位",
         maxSize = 200
      )
      private String payUnit;
      @V(
         desc = "服务费标识",
         notNull = false,
         length = "1",
         remark = "服务费标识",
         maxSize = 1
      )
      private String servCharge;
      @V(
         desc = "交易时间戳",
         notNull = false,
         length = "26",
         remark = "交易时间戳",
         maxSize = 26
      )
      private String tranTimestamp;
      @V(
         desc = "原始交易时间戳",
         notNull = false,
         length = "26",
         remark = "原始交易时间戳",
         maxSize = 26
      )
      private String origTranTimestamp;

      public String getOthProdType() {
         return this.othProdType;
      }

      public String getOthAcctCcy() {
         return this.othAcctCcy;
      }

      public String getDocumentType() {
         return this.documentType;
      }

      public String getClientName() {
         return this.clientName;
      }

      public String getChannelSeqNo() {
         return this.channelSeqNo;
      }

      public String getTermType() {
         return this.termType;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public String getAcctDesc() {
         return this.acctDesc;
      }

      public String getTranType() {
         return this.tranType;
      }

      public String getClientType() {
         return this.clientType;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getAcctBranch() {
         return this.acctBranch;
      }

      public BigDecimal getActualBal() {
         return this.actualBal;
      }

      public String getTerm() {
         return this.term;
      }

      public String getChClientName() {
         return this.chClientName;
      }

      public String getTranDesc() {
         return this.tranDesc;
      }

      public String getOthDocumentType() {
         return this.othDocumentType;
      }

      public String getOthAcctDesc() {
         return this.othAcctDesc;
      }

      public String getAcctOpenDate() {
         return this.acctOpenDate;
      }

      public String getOthAcctSeqNo() {
         return this.othAcctSeqNo;
      }

      public String getAmtType() {
         return this.amtType;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getCrDrMaintInd() {
         return this.crDrMaintInd;
      }

      public String getOthDocumentId() {
         return this.othDocumentId;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getOthBaseAcctNo() {
         return this.othBaseAcctNo;
      }

      public String getOthBranch() {
         return this.othBranch;
      }

      public BigDecimal getTranAmt() {
         return this.tranAmt;
      }

      public String getDocumentId() {
         return this.documentId;
      }

      public String getTranDate() {
         return this.tranDate;
      }

      public String getMatureDate() {
         return this.matureDate;
      }

      public BigDecimal getPreviousBalAmt() {
         return this.previousBalAmt;
      }

      public BigDecimal getOrigTranAmt() {
         return this.origTranAmt;
      }

      public BigDecimal getDdAmt() {
         return this.ddAmt;
      }

      public BigDecimal getOdAmount() {
         return this.odAmount;
      }

      public BigDecimal getBaseEquivAmt() {
         return this.baseEquivAmt;
      }

      public BigDecimal getFromXrate() {
         return this.fromXrate;
      }

      public BigDecimal getToXrate() {
         return this.toXrate;
      }

      public String getSubSeqNo() {
         return this.subSeqNo;
      }

      public String getOthAcctName() {
         return this.othAcctName;
      }

      public String getOthBankName() {
         return this.othBankName;
      }

      public String getOthBankCode() {
         return this.othBankCode;
      }

      public String getOthBranchName() {
         return this.othBranchName;
      }

      public String getNarrativeCode() {
         return this.narrativeCode;
      }

      public String getNarrativeCodeDesc() {
         return this.narrativeCodeDesc;
      }

      public String getSourceType() {
         return this.sourceType;
      }

      public String getSourceModule() {
         return this.sourceModule;
      }

      public String getPiFlag() {
         return this.piFlag;
      }

      public String getToId() {
         return this.toId;
      }

      public String getAuthUserId() {
         return this.authUserId;
      }

      public String getBalType() {
         return this.balType;
      }

      public String getUserId() {
         return this.userId;
      }

      public String getBranch() {
         return this.branch;
      }

      public String getBankName() {
         return this.bankName;
      }

      public String getBankCode() {
         return this.bankCode;
      }

      public String getContraTranDate() {
         return this.contraTranDate;
      }

      public String getOthSeqNo() {
         return this.othSeqNo;
      }

      public String getOthReference() {
         return this.othReference;
      }

      public String getDocType() {
         return this.docType;
      }

      public String getPrefix() {
         return this.prefix;
      }

      public String getVoucherNo() {
         return this.voucherNo;
      }

      public String getWithdrawalType() {
         return this.withdrawalType;
      }

      public String getBankSeqNo() {
         return this.bankSeqNo;
      }

      public String getApprUserId() {
         return this.apprUserId;
      }

      public String getReference() {
         return this.reference;
      }

      public String getTranCategory() {
         return this.tranCategory;
      }

      public String getReversalTranType() {
         return this.reversalTranType;
      }

      public String getReversalDate() {
         return this.reversalDate;
      }

      public String getSeqNo() {
         return this.seqNo;
      }

      public String getNarrative() {
         return this.narrative;
      }

      public String getTranNote() {
         return this.tranNote;
      }

      public String getPrimaryTranSeqNo() {
         return this.primaryTranSeqNo;
      }

      public String getFhSeqNo() {
         return this.fhSeqNo;
      }

      public BigDecimal getContraEquivAmt() {
         return this.contraEquivAmt;
      }

      public BigDecimal getCrossRate() {
         return this.crossRate;
      }

      public String getFromCcy() {
         return this.fromCcy;
      }

      public BigDecimal getFromAmount() {
         return this.fromAmount;
      }

      public String getFromRateFlag() {
         return this.fromRateFlag;
      }

      public String getToCcy() {
         return this.toCcy;
      }

      public BigDecimal getToAmount() {
         return this.toAmount;
      }

      public String getToRateFlag() {
         return this.toRateFlag;
      }

      public BigDecimal getOvToAmount() {
         return this.ovToAmount;
      }

      public String getQuoteType() {
         return this.quoteType;
      }

      public String getTerminalId() {
         return this.terminalId;
      }

      public String getTraceId() {
         return this.traceId;
      }

      public String getCashItem() {
         return this.cashItem;
      }

      public String getTranStatus() {
         return this.tranStatus;
      }

      public String getProfitCenter() {
         return this.profitCenter;
      }

      public String getBusinessUnit() {
         return this.businessUnit;
      }

      public String getEventType() {
         return this.eventType;
      }

      public String getCcy() {
         return this.ccy;
      }

      public Integer getPrintCnt() {
         return this.printCnt;
      }

      public String getBatchNo() {
         return this.batchNo;
      }

      public String getBizType() {
         return this.bizType;
      }

      public String getPbkUpdFlag() {
         return this.pbkUpdFlag;
      }

      public String getReversal() {
         return this.reversal;
      }

      public String getPayUnit() {
         return this.payUnit;
      }

      public String getServCharge() {
         return this.servCharge;
      }

      public String getTranTimestamp() {
         return this.tranTimestamp;
      }

      public String getOrigTranTimestamp() {
         return this.origTranTimestamp;
      }

      public void setOthProdType(String othProdType) {
         this.othProdType = othProdType;
      }

      public void setOthAcctCcy(String othAcctCcy) {
         this.othAcctCcy = othAcctCcy;
      }

      public void setDocumentType(String documentType) {
         this.documentType = documentType;
      }

      public void setClientName(String clientName) {
         this.clientName = clientName;
      }

      public void setChannelSeqNo(String channelSeqNo) {
         this.channelSeqNo = channelSeqNo;
      }

      public void setTermType(String termType) {
         this.termType = termType;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setAcctDesc(String acctDesc) {
         this.acctDesc = acctDesc;
      }

      public void setTranType(String tranType) {
         this.tranType = tranType;
      }

      public void setClientType(String clientType) {
         this.clientType = clientType;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setAcctBranch(String acctBranch) {
         this.acctBranch = acctBranch;
      }

      public void setActualBal(BigDecimal actualBal) {
         this.actualBal = actualBal;
      }

      public void setTerm(String term) {
         this.term = term;
      }

      public void setChClientName(String chClientName) {
         this.chClientName = chClientName;
      }

      public void setTranDesc(String tranDesc) {
         this.tranDesc = tranDesc;
      }

      public void setOthDocumentType(String othDocumentType) {
         this.othDocumentType = othDocumentType;
      }

      public void setOthAcctDesc(String othAcctDesc) {
         this.othAcctDesc = othAcctDesc;
      }

      public void setAcctOpenDate(String acctOpenDate) {
         this.acctOpenDate = acctOpenDate;
      }

      public void setOthAcctSeqNo(String othAcctSeqNo) {
         this.othAcctSeqNo = othAcctSeqNo;
      }

      public void setAmtType(String amtType) {
         this.amtType = amtType;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setCrDrMaintInd(String crDrMaintInd) {
         this.crDrMaintInd = crDrMaintInd;
      }

      public void setOthDocumentId(String othDocumentId) {
         this.othDocumentId = othDocumentId;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setOthBaseAcctNo(String othBaseAcctNo) {
         this.othBaseAcctNo = othBaseAcctNo;
      }

      public void setOthBranch(String othBranch) {
         this.othBranch = othBranch;
      }

      public void setTranAmt(BigDecimal tranAmt) {
         this.tranAmt = tranAmt;
      }

      public void setDocumentId(String documentId) {
         this.documentId = documentId;
      }

      public void setTranDate(String tranDate) {
         this.tranDate = tranDate;
      }

      public void setMatureDate(String matureDate) {
         this.matureDate = matureDate;
      }

      public void setPreviousBalAmt(BigDecimal previousBalAmt) {
         this.previousBalAmt = previousBalAmt;
      }

      public void setOrigTranAmt(BigDecimal origTranAmt) {
         this.origTranAmt = origTranAmt;
      }

      public void setDdAmt(BigDecimal ddAmt) {
         this.ddAmt = ddAmt;
      }

      public void setOdAmount(BigDecimal odAmount) {
         this.odAmount = odAmount;
      }

      public void setBaseEquivAmt(BigDecimal baseEquivAmt) {
         this.baseEquivAmt = baseEquivAmt;
      }

      public void setFromXrate(BigDecimal fromXrate) {
         this.fromXrate = fromXrate;
      }

      public void setToXrate(BigDecimal toXrate) {
         this.toXrate = toXrate;
      }

      public void setSubSeqNo(String subSeqNo) {
         this.subSeqNo = subSeqNo;
      }

      public void setOthAcctName(String othAcctName) {
         this.othAcctName = othAcctName;
      }

      public void setOthBankName(String othBankName) {
         this.othBankName = othBankName;
      }

      public void setOthBankCode(String othBankCode) {
         this.othBankCode = othBankCode;
      }

      public void setOthBranchName(String othBranchName) {
         this.othBranchName = othBranchName;
      }

      public void setNarrativeCode(String narrativeCode) {
         this.narrativeCode = narrativeCode;
      }

      public void setNarrativeCodeDesc(String narrativeCodeDesc) {
         this.narrativeCodeDesc = narrativeCodeDesc;
      }

      public void setSourceType(String sourceType) {
         this.sourceType = sourceType;
      }

      public void setSourceModule(String sourceModule) {
         this.sourceModule = sourceModule;
      }

      public void setPiFlag(String piFlag) {
         this.piFlag = piFlag;
      }

      public void setToId(String toId) {
         this.toId = toId;
      }

      public void setAuthUserId(String authUserId) {
         this.authUserId = authUserId;
      }

      public void setBalType(String balType) {
         this.balType = balType;
      }

      public void setUserId(String userId) {
         this.userId = userId;
      }

      public void setBranch(String branch) {
         this.branch = branch;
      }

      public void setBankName(String bankName) {
         this.bankName = bankName;
      }

      public void setBankCode(String bankCode) {
         this.bankCode = bankCode;
      }

      public void setContraTranDate(String contraTranDate) {
         this.contraTranDate = contraTranDate;
      }

      public void setOthSeqNo(String othSeqNo) {
         this.othSeqNo = othSeqNo;
      }

      public void setOthReference(String othReference) {
         this.othReference = othReference;
      }

      public void setDocType(String docType) {
         this.docType = docType;
      }

      public void setPrefix(String prefix) {
         this.prefix = prefix;
      }

      public void setVoucherNo(String voucherNo) {
         this.voucherNo = voucherNo;
      }

      public void setWithdrawalType(String withdrawalType) {
         this.withdrawalType = withdrawalType;
      }

      public void setBankSeqNo(String bankSeqNo) {
         this.bankSeqNo = bankSeqNo;
      }

      public void setApprUserId(String apprUserId) {
         this.apprUserId = apprUserId;
      }

      public void setReference(String reference) {
         this.reference = reference;
      }

      public void setTranCategory(String tranCategory) {
         this.tranCategory = tranCategory;
      }

      public void setReversalTranType(String reversalTranType) {
         this.reversalTranType = reversalTranType;
      }

      public void setReversalDate(String reversalDate) {
         this.reversalDate = reversalDate;
      }

      public void setSeqNo(String seqNo) {
         this.seqNo = seqNo;
      }

      public void setNarrative(String narrative) {
         this.narrative = narrative;
      }

      public void setTranNote(String tranNote) {
         this.tranNote = tranNote;
      }

      public void setPrimaryTranSeqNo(String primaryTranSeqNo) {
         this.primaryTranSeqNo = primaryTranSeqNo;
      }

      public void setFhSeqNo(String fhSeqNo) {
         this.fhSeqNo = fhSeqNo;
      }

      public void setContraEquivAmt(BigDecimal contraEquivAmt) {
         this.contraEquivAmt = contraEquivAmt;
      }

      public void setCrossRate(BigDecimal crossRate) {
         this.crossRate = crossRate;
      }

      public void setFromCcy(String fromCcy) {
         this.fromCcy = fromCcy;
      }

      public void setFromAmount(BigDecimal fromAmount) {
         this.fromAmount = fromAmount;
      }

      public void setFromRateFlag(String fromRateFlag) {
         this.fromRateFlag = fromRateFlag;
      }

      public void setToCcy(String toCcy) {
         this.toCcy = toCcy;
      }

      public void setToAmount(BigDecimal toAmount) {
         this.toAmount = toAmount;
      }

      public void setToRateFlag(String toRateFlag) {
         this.toRateFlag = toRateFlag;
      }

      public void setOvToAmount(BigDecimal ovToAmount) {
         this.ovToAmount = ovToAmount;
      }

      public void setQuoteType(String quoteType) {
         this.quoteType = quoteType;
      }

      public void setTerminalId(String terminalId) {
         this.terminalId = terminalId;
      }

      public void setTraceId(String traceId) {
         this.traceId = traceId;
      }

      public void setCashItem(String cashItem) {
         this.cashItem = cashItem;
      }

      public void setTranStatus(String tranStatus) {
         this.tranStatus = tranStatus;
      }

      public void setProfitCenter(String profitCenter) {
         this.profitCenter = profitCenter;
      }

      public void setBusinessUnit(String businessUnit) {
         this.businessUnit = businessUnit;
      }

      public void setEventType(String eventType) {
         this.eventType = eventType;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setPrintCnt(Integer printCnt) {
         this.printCnt = printCnt;
      }

      public void setBatchNo(String batchNo) {
         this.batchNo = batchNo;
      }

      public void setBizType(String bizType) {
         this.bizType = bizType;
      }

      public void setPbkUpdFlag(String pbkUpdFlag) {
         this.pbkUpdFlag = pbkUpdFlag;
      }

      public void setReversal(String reversal) {
         this.reversal = reversal;
      }

      public void setPayUnit(String payUnit) {
         this.payUnit = payUnit;
      }

      public void setServCharge(String servCharge) {
         this.servCharge = servCharge;
      }

      public void setTranTimestamp(String tranTimestamp) {
         this.tranTimestamp = tranTimestamp;
      }

      public void setOrigTranTimestamp(String origTranTimestamp) {
         this.origTranTimestamp = origTranTimestamp;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400001001Out.TranHistArray)) {
            return false;
         } else {
            Core1400001001Out.TranHistArray other = (Core1400001001Out.TranHistArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label1223: {
                  Object this$othProdType = this.getOthProdType();
                  Object other$othProdType = other.getOthProdType();
                  if (this$othProdType == null) {
                     if (other$othProdType == null) {
                        break label1223;
                     }
                  } else if (this$othProdType.equals(other$othProdType)) {
                     break label1223;
                  }

                  return false;
               }

               Object this$othAcctCcy = this.getOthAcctCcy();
               Object other$othAcctCcy = other.getOthAcctCcy();
               if (this$othAcctCcy == null) {
                  if (other$othAcctCcy != null) {
                     return false;
                  }
               } else if (!this$othAcctCcy.equals(other$othAcctCcy)) {
                  return false;
               }

               label1209: {
                  Object this$documentType = this.getDocumentType();
                  Object other$documentType = other.getDocumentType();
                  if (this$documentType == null) {
                     if (other$documentType == null) {
                        break label1209;
                     }
                  } else if (this$documentType.equals(other$documentType)) {
                     break label1209;
                  }

                  return false;
               }

               Object this$clientName = this.getClientName();
               Object other$clientName = other.getClientName();
               if (this$clientName == null) {
                  if (other$clientName != null) {
                     return false;
                  }
               } else if (!this$clientName.equals(other$clientName)) {
                  return false;
               }

               label1195: {
                  Object this$channelSeqNo = this.getChannelSeqNo();
                  Object other$channelSeqNo = other.getChannelSeqNo();
                  if (this$channelSeqNo == null) {
                     if (other$channelSeqNo == null) {
                        break label1195;
                     }
                  } else if (this$channelSeqNo.equals(other$channelSeqNo)) {
                     break label1195;
                  }

                  return false;
               }

               Object this$termType = this.getTermType();
               Object other$termType = other.getTermType();
               if (this$termType == null) {
                  if (other$termType != null) {
                     return false;
                  }
               } else if (!this$termType.equals(other$termType)) {
                  return false;
               }

               label1181: {
                  Object this$clientNo = this.getClientNo();
                  Object other$clientNo = other.getClientNo();
                  if (this$clientNo == null) {
                     if (other$clientNo == null) {
                        break label1181;
                     }
                  } else if (this$clientNo.equals(other$clientNo)) {
                     break label1181;
                  }

                  return false;
               }

               label1174: {
                  Object this$acctDesc = this.getAcctDesc();
                  Object other$acctDesc = other.getAcctDesc();
                  if (this$acctDesc == null) {
                     if (other$acctDesc == null) {
                        break label1174;
                     }
                  } else if (this$acctDesc.equals(other$acctDesc)) {
                     break label1174;
                  }

                  return false;
               }

               Object this$tranType = this.getTranType();
               Object other$tranType = other.getTranType();
               if (this$tranType == null) {
                  if (other$tranType != null) {
                     return false;
                  }
               } else if (!this$tranType.equals(other$tranType)) {
                  return false;
               }

               label1160: {
                  Object this$clientType = this.getClientType();
                  Object other$clientType = other.getClientType();
                  if (this$clientType == null) {
                     if (other$clientType == null) {
                        break label1160;
                     }
                  } else if (this$clientType.equals(other$clientType)) {
                     break label1160;
                  }

                  return false;
               }

               label1153: {
                  Object this$acctCcy = this.getAcctCcy();
                  Object other$acctCcy = other.getAcctCcy();
                  if (this$acctCcy == null) {
                     if (other$acctCcy == null) {
                        break label1153;
                     }
                  } else if (this$acctCcy.equals(other$acctCcy)) {
                     break label1153;
                  }

                  return false;
               }

               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo != null) {
                     return false;
                  }
               } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                  return false;
               }

               Object this$acctBranch = this.getAcctBranch();
               Object other$acctBranch = other.getAcctBranch();
               if (this$acctBranch == null) {
                  if (other$acctBranch != null) {
                     return false;
                  }
               } else if (!this$acctBranch.equals(other$acctBranch)) {
                  return false;
               }

               label1132: {
                  Object this$actualBal = this.getActualBal();
                  Object other$actualBal = other.getActualBal();
                  if (this$actualBal == null) {
                     if (other$actualBal == null) {
                        break label1132;
                     }
                  } else if (this$actualBal.equals(other$actualBal)) {
                     break label1132;
                  }

                  return false;
               }

               Object this$term = this.getTerm();
               Object other$term = other.getTerm();
               if (this$term == null) {
                  if (other$term != null) {
                     return false;
                  }
               } else if (!this$term.equals(other$term)) {
                  return false;
               }

               Object this$chClientName = this.getChClientName();
               Object other$chClientName = other.getChClientName();
               if (this$chClientName == null) {
                  if (other$chClientName != null) {
                     return false;
                  }
               } else if (!this$chClientName.equals(other$chClientName)) {
                  return false;
               }

               label1111: {
                  Object this$tranDesc = this.getTranDesc();
                  Object other$tranDesc = other.getTranDesc();
                  if (this$tranDesc == null) {
                     if (other$tranDesc == null) {
                        break label1111;
                     }
                  } else if (this$tranDesc.equals(other$tranDesc)) {
                     break label1111;
                  }

                  return false;
               }

               Object this$othDocumentType = this.getOthDocumentType();
               Object other$othDocumentType = other.getOthDocumentType();
               if (this$othDocumentType == null) {
                  if (other$othDocumentType != null) {
                     return false;
                  }
               } else if (!this$othDocumentType.equals(other$othDocumentType)) {
                  return false;
               }

               label1097: {
                  Object this$othAcctDesc = this.getOthAcctDesc();
                  Object other$othAcctDesc = other.getOthAcctDesc();
                  if (this$othAcctDesc == null) {
                     if (other$othAcctDesc == null) {
                        break label1097;
                     }
                  } else if (this$othAcctDesc.equals(other$othAcctDesc)) {
                     break label1097;
                  }

                  return false;
               }

               Object this$acctOpenDate = this.getAcctOpenDate();
               Object other$acctOpenDate = other.getAcctOpenDate();
               if (this$acctOpenDate == null) {
                  if (other$acctOpenDate != null) {
                     return false;
                  }
               } else if (!this$acctOpenDate.equals(other$acctOpenDate)) {
                  return false;
               }

               label1083: {
                  Object this$othAcctSeqNo = this.getOthAcctSeqNo();
                  Object other$othAcctSeqNo = other.getOthAcctSeqNo();
                  if (this$othAcctSeqNo == null) {
                     if (other$othAcctSeqNo == null) {
                        break label1083;
                     }
                  } else if (this$othAcctSeqNo.equals(other$othAcctSeqNo)) {
                     break label1083;
                  }

                  return false;
               }

               Object this$amtType = this.getAmtType();
               Object other$amtType = other.getAmtType();
               if (this$amtType == null) {
                  if (other$amtType != null) {
                     return false;
                  }
               } else if (!this$amtType.equals(other$amtType)) {
                  return false;
               }

               label1069: {
                  Object this$prodType = this.getProdType();
                  Object other$prodType = other.getProdType();
                  if (this$prodType == null) {
                     if (other$prodType == null) {
                        break label1069;
                     }
                  } else if (this$prodType.equals(other$prodType)) {
                     break label1069;
                  }

                  return false;
               }

               label1062: {
                  Object this$crDrMaintInd = this.getCrDrMaintInd();
                  Object other$crDrMaintInd = other.getCrDrMaintInd();
                  if (this$crDrMaintInd == null) {
                     if (other$crDrMaintInd == null) {
                        break label1062;
                     }
                  } else if (this$crDrMaintInd.equals(other$crDrMaintInd)) {
                     break label1062;
                  }

                  return false;
               }

               Object this$othDocumentId = this.getOthDocumentId();
               Object other$othDocumentId = other.getOthDocumentId();
               if (this$othDocumentId == null) {
                  if (other$othDocumentId != null) {
                     return false;
                  }
               } else if (!this$othDocumentId.equals(other$othDocumentId)) {
                  return false;
               }

               label1048: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label1048;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label1048;
                  }

                  return false;
               }

               label1041: {
                  Object this$othBaseAcctNo = this.getOthBaseAcctNo();
                  Object other$othBaseAcctNo = other.getOthBaseAcctNo();
                  if (this$othBaseAcctNo == null) {
                     if (other$othBaseAcctNo == null) {
                        break label1041;
                     }
                  } else if (this$othBaseAcctNo.equals(other$othBaseAcctNo)) {
                     break label1041;
                  }

                  return false;
               }

               Object this$othBranch = this.getOthBranch();
               Object other$othBranch = other.getOthBranch();
               if (this$othBranch == null) {
                  if (other$othBranch != null) {
                     return false;
                  }
               } else if (!this$othBranch.equals(other$othBranch)) {
                  return false;
               }

               Object this$tranAmt = this.getTranAmt();
               Object other$tranAmt = other.getTranAmt();
               if (this$tranAmt == null) {
                  if (other$tranAmt != null) {
                     return false;
                  }
               } else if (!this$tranAmt.equals(other$tranAmt)) {
                  return false;
               }

               label1020: {
                  Object this$documentId = this.getDocumentId();
                  Object other$documentId = other.getDocumentId();
                  if (this$documentId == null) {
                     if (other$documentId == null) {
                        break label1020;
                     }
                  } else if (this$documentId.equals(other$documentId)) {
                     break label1020;
                  }

                  return false;
               }

               Object this$tranDate = this.getTranDate();
               Object other$tranDate = other.getTranDate();
               if (this$tranDate == null) {
                  if (other$tranDate != null) {
                     return false;
                  }
               } else if (!this$tranDate.equals(other$tranDate)) {
                  return false;
               }

               Object this$matureDate = this.getMatureDate();
               Object other$matureDate = other.getMatureDate();
               if (this$matureDate == null) {
                  if (other$matureDate != null) {
                     return false;
                  }
               } else if (!this$matureDate.equals(other$matureDate)) {
                  return false;
               }

               label999: {
                  Object this$previousBalAmt = this.getPreviousBalAmt();
                  Object other$previousBalAmt = other.getPreviousBalAmt();
                  if (this$previousBalAmt == null) {
                     if (other$previousBalAmt == null) {
                        break label999;
                     }
                  } else if (this$previousBalAmt.equals(other$previousBalAmt)) {
                     break label999;
                  }

                  return false;
               }

               Object this$origTranAmt = this.getOrigTranAmt();
               Object other$origTranAmt = other.getOrigTranAmt();
               if (this$origTranAmt == null) {
                  if (other$origTranAmt != null) {
                     return false;
                  }
               } else if (!this$origTranAmt.equals(other$origTranAmt)) {
                  return false;
               }

               label985: {
                  Object this$ddAmt = this.getDdAmt();
                  Object other$ddAmt = other.getDdAmt();
                  if (this$ddAmt == null) {
                     if (other$ddAmt == null) {
                        break label985;
                     }
                  } else if (this$ddAmt.equals(other$ddAmt)) {
                     break label985;
                  }

                  return false;
               }

               Object this$odAmount = this.getOdAmount();
               Object other$odAmount = other.getOdAmount();
               if (this$odAmount == null) {
                  if (other$odAmount != null) {
                     return false;
                  }
               } else if (!this$odAmount.equals(other$odAmount)) {
                  return false;
               }

               label971: {
                  Object this$baseEquivAmt = this.getBaseEquivAmt();
                  Object other$baseEquivAmt = other.getBaseEquivAmt();
                  if (this$baseEquivAmt == null) {
                     if (other$baseEquivAmt == null) {
                        break label971;
                     }
                  } else if (this$baseEquivAmt.equals(other$baseEquivAmt)) {
                     break label971;
                  }

                  return false;
               }

               Object this$fromXrate = this.getFromXrate();
               Object other$fromXrate = other.getFromXrate();
               if (this$fromXrate == null) {
                  if (other$fromXrate != null) {
                     return false;
                  }
               } else if (!this$fromXrate.equals(other$fromXrate)) {
                  return false;
               }

               label957: {
                  Object this$toXrate = this.getToXrate();
                  Object other$toXrate = other.getToXrate();
                  if (this$toXrate == null) {
                     if (other$toXrate == null) {
                        break label957;
                     }
                  } else if (this$toXrate.equals(other$toXrate)) {
                     break label957;
                  }

                  return false;
               }

               label950: {
                  Object this$subSeqNo = this.getSubSeqNo();
                  Object other$subSeqNo = other.getSubSeqNo();
                  if (this$subSeqNo == null) {
                     if (other$subSeqNo == null) {
                        break label950;
                     }
                  } else if (this$subSeqNo.equals(other$subSeqNo)) {
                     break label950;
                  }

                  return false;
               }

               Object this$othAcctName = this.getOthAcctName();
               Object other$othAcctName = other.getOthAcctName();
               if (this$othAcctName == null) {
                  if (other$othAcctName != null) {
                     return false;
                  }
               } else if (!this$othAcctName.equals(other$othAcctName)) {
                  return false;
               }

               label936: {
                  Object this$othBankName = this.getOthBankName();
                  Object other$othBankName = other.getOthBankName();
                  if (this$othBankName == null) {
                     if (other$othBankName == null) {
                        break label936;
                     }
                  } else if (this$othBankName.equals(other$othBankName)) {
                     break label936;
                  }

                  return false;
               }

               label929: {
                  Object this$othBankCode = this.getOthBankCode();
                  Object other$othBankCode = other.getOthBankCode();
                  if (this$othBankCode == null) {
                     if (other$othBankCode == null) {
                        break label929;
                     }
                  } else if (this$othBankCode.equals(other$othBankCode)) {
                     break label929;
                  }

                  return false;
               }

               Object this$othBranchName = this.getOthBranchName();
               Object other$othBranchName = other.getOthBranchName();
               if (this$othBranchName == null) {
                  if (other$othBranchName != null) {
                     return false;
                  }
               } else if (!this$othBranchName.equals(other$othBranchName)) {
                  return false;
               }

               Object this$narrativeCode = this.getNarrativeCode();
               Object other$narrativeCode = other.getNarrativeCode();
               if (this$narrativeCode == null) {
                  if (other$narrativeCode != null) {
                     return false;
                  }
               } else if (!this$narrativeCode.equals(other$narrativeCode)) {
                  return false;
               }

               label908: {
                  Object this$narrativeCodeDesc = this.getNarrativeCodeDesc();
                  Object other$narrativeCodeDesc = other.getNarrativeCodeDesc();
                  if (this$narrativeCodeDesc == null) {
                     if (other$narrativeCodeDesc == null) {
                        break label908;
                     }
                  } else if (this$narrativeCodeDesc.equals(other$narrativeCodeDesc)) {
                     break label908;
                  }

                  return false;
               }

               Object this$sourceType = this.getSourceType();
               Object other$sourceType = other.getSourceType();
               if (this$sourceType == null) {
                  if (other$sourceType != null) {
                     return false;
                  }
               } else if (!this$sourceType.equals(other$sourceType)) {
                  return false;
               }

               Object this$sourceModule = this.getSourceModule();
               Object other$sourceModule = other.getSourceModule();
               if (this$sourceModule == null) {
                  if (other$sourceModule != null) {
                     return false;
                  }
               } else if (!this$sourceModule.equals(other$sourceModule)) {
                  return false;
               }

               label887: {
                  Object this$piFlag = this.getPiFlag();
                  Object other$piFlag = other.getPiFlag();
                  if (this$piFlag == null) {
                     if (other$piFlag == null) {
                        break label887;
                     }
                  } else if (this$piFlag.equals(other$piFlag)) {
                     break label887;
                  }

                  return false;
               }

               Object this$toId = this.getToId();
               Object other$toId = other.getToId();
               if (this$toId == null) {
                  if (other$toId != null) {
                     return false;
                  }
               } else if (!this$toId.equals(other$toId)) {
                  return false;
               }

               label873: {
                  Object this$authUserId = this.getAuthUserId();
                  Object other$authUserId = other.getAuthUserId();
                  if (this$authUserId == null) {
                     if (other$authUserId == null) {
                        break label873;
                     }
                  } else if (this$authUserId.equals(other$authUserId)) {
                     break label873;
                  }

                  return false;
               }

               Object this$balType = this.getBalType();
               Object other$balType = other.getBalType();
               if (this$balType == null) {
                  if (other$balType != null) {
                     return false;
                  }
               } else if (!this$balType.equals(other$balType)) {
                  return false;
               }

               label859: {
                  Object this$userId = this.getUserId();
                  Object other$userId = other.getUserId();
                  if (this$userId == null) {
                     if (other$userId == null) {
                        break label859;
                     }
                  } else if (this$userId.equals(other$userId)) {
                     break label859;
                  }

                  return false;
               }

               Object this$branch = this.getBranch();
               Object other$branch = other.getBranch();
               if (this$branch == null) {
                  if (other$branch != null) {
                     return false;
                  }
               } else if (!this$branch.equals(other$branch)) {
                  return false;
               }

               label845: {
                  Object this$bankName = this.getBankName();
                  Object other$bankName = other.getBankName();
                  if (this$bankName == null) {
                     if (other$bankName == null) {
                        break label845;
                     }
                  } else if (this$bankName.equals(other$bankName)) {
                     break label845;
                  }

                  return false;
               }

               label838: {
                  Object this$bankCode = this.getBankCode();
                  Object other$bankCode = other.getBankCode();
                  if (this$bankCode == null) {
                     if (other$bankCode == null) {
                        break label838;
                     }
                  } else if (this$bankCode.equals(other$bankCode)) {
                     break label838;
                  }

                  return false;
               }

               Object this$contraTranDate = this.getContraTranDate();
               Object other$contraTranDate = other.getContraTranDate();
               if (this$contraTranDate == null) {
                  if (other$contraTranDate != null) {
                     return false;
                  }
               } else if (!this$contraTranDate.equals(other$contraTranDate)) {
                  return false;
               }

               label824: {
                  Object this$othSeqNo = this.getOthSeqNo();
                  Object other$othSeqNo = other.getOthSeqNo();
                  if (this$othSeqNo == null) {
                     if (other$othSeqNo == null) {
                        break label824;
                     }
                  } else if (this$othSeqNo.equals(other$othSeqNo)) {
                     break label824;
                  }

                  return false;
               }

               label817: {
                  Object this$othReference = this.getOthReference();
                  Object other$othReference = other.getOthReference();
                  if (this$othReference == null) {
                     if (other$othReference == null) {
                        break label817;
                     }
                  } else if (this$othReference.equals(other$othReference)) {
                     break label817;
                  }

                  return false;
               }

               Object this$docType = this.getDocType();
               Object other$docType = other.getDocType();
               if (this$docType == null) {
                  if (other$docType != null) {
                     return false;
                  }
               } else if (!this$docType.equals(other$docType)) {
                  return false;
               }

               Object this$prefix = this.getPrefix();
               Object other$prefix = other.getPrefix();
               if (this$prefix == null) {
                  if (other$prefix != null) {
                     return false;
                  }
               } else if (!this$prefix.equals(other$prefix)) {
                  return false;
               }

               label796: {
                  Object this$voucherNo = this.getVoucherNo();
                  Object other$voucherNo = other.getVoucherNo();
                  if (this$voucherNo == null) {
                     if (other$voucherNo == null) {
                        break label796;
                     }
                  } else if (this$voucherNo.equals(other$voucherNo)) {
                     break label796;
                  }

                  return false;
               }

               Object this$withdrawalType = this.getWithdrawalType();
               Object other$withdrawalType = other.getWithdrawalType();
               if (this$withdrawalType == null) {
                  if (other$withdrawalType != null) {
                     return false;
                  }
               } else if (!this$withdrawalType.equals(other$withdrawalType)) {
                  return false;
               }

               Object this$bankSeqNo = this.getBankSeqNo();
               Object other$bankSeqNo = other.getBankSeqNo();
               if (this$bankSeqNo == null) {
                  if (other$bankSeqNo != null) {
                     return false;
                  }
               } else if (!this$bankSeqNo.equals(other$bankSeqNo)) {
                  return false;
               }

               label775: {
                  Object this$apprUserId = this.getApprUserId();
                  Object other$apprUserId = other.getApprUserId();
                  if (this$apprUserId == null) {
                     if (other$apprUserId == null) {
                        break label775;
                     }
                  } else if (this$apprUserId.equals(other$apprUserId)) {
                     break label775;
                  }

                  return false;
               }

               Object this$reference = this.getReference();
               Object other$reference = other.getReference();
               if (this$reference == null) {
                  if (other$reference != null) {
                     return false;
                  }
               } else if (!this$reference.equals(other$reference)) {
                  return false;
               }

               label761: {
                  Object this$tranCategory = this.getTranCategory();
                  Object other$tranCategory = other.getTranCategory();
                  if (this$tranCategory == null) {
                     if (other$tranCategory == null) {
                        break label761;
                     }
                  } else if (this$tranCategory.equals(other$tranCategory)) {
                     break label761;
                  }

                  return false;
               }

               Object this$reversalTranType = this.getReversalTranType();
               Object other$reversalTranType = other.getReversalTranType();
               if (this$reversalTranType == null) {
                  if (other$reversalTranType != null) {
                     return false;
                  }
               } else if (!this$reversalTranType.equals(other$reversalTranType)) {
                  return false;
               }

               label747: {
                  Object this$reversalDate = this.getReversalDate();
                  Object other$reversalDate = other.getReversalDate();
                  if (this$reversalDate == null) {
                     if (other$reversalDate == null) {
                        break label747;
                     }
                  } else if (this$reversalDate.equals(other$reversalDate)) {
                     break label747;
                  }

                  return false;
               }

               Object this$seqNo = this.getSeqNo();
               Object other$seqNo = other.getSeqNo();
               if (this$seqNo == null) {
                  if (other$seqNo != null) {
                     return false;
                  }
               } else if (!this$seqNo.equals(other$seqNo)) {
                  return false;
               }

               label733: {
                  Object this$narrative = this.getNarrative();
                  Object other$narrative = other.getNarrative();
                  if (this$narrative == null) {
                     if (other$narrative == null) {
                        break label733;
                     }
                  } else if (this$narrative.equals(other$narrative)) {
                     break label733;
                  }

                  return false;
               }

               label726: {
                  Object this$tranNote = this.getTranNote();
                  Object other$tranNote = other.getTranNote();
                  if (this$tranNote == null) {
                     if (other$tranNote == null) {
                        break label726;
                     }
                  } else if (this$tranNote.equals(other$tranNote)) {
                     break label726;
                  }

                  return false;
               }

               Object this$primaryTranSeqNo = this.getPrimaryTranSeqNo();
               Object other$primaryTranSeqNo = other.getPrimaryTranSeqNo();
               if (this$primaryTranSeqNo == null) {
                  if (other$primaryTranSeqNo != null) {
                     return false;
                  }
               } else if (!this$primaryTranSeqNo.equals(other$primaryTranSeqNo)) {
                  return false;
               }

               label712: {
                  Object this$fhSeqNo = this.getFhSeqNo();
                  Object other$fhSeqNo = other.getFhSeqNo();
                  if (this$fhSeqNo == null) {
                     if (other$fhSeqNo == null) {
                        break label712;
                     }
                  } else if (this$fhSeqNo.equals(other$fhSeqNo)) {
                     break label712;
                  }

                  return false;
               }

               label705: {
                  Object this$contraEquivAmt = this.getContraEquivAmt();
                  Object other$contraEquivAmt = other.getContraEquivAmt();
                  if (this$contraEquivAmt == null) {
                     if (other$contraEquivAmt == null) {
                        break label705;
                     }
                  } else if (this$contraEquivAmt.equals(other$contraEquivAmt)) {
                     break label705;
                  }

                  return false;
               }

               Object this$crossRate = this.getCrossRate();
               Object other$crossRate = other.getCrossRate();
               if (this$crossRate == null) {
                  if (other$crossRate != null) {
                     return false;
                  }
               } else if (!this$crossRate.equals(other$crossRate)) {
                  return false;
               }

               Object this$fromCcy = this.getFromCcy();
               Object other$fromCcy = other.getFromCcy();
               if (this$fromCcy == null) {
                  if (other$fromCcy != null) {
                     return false;
                  }
               } else if (!this$fromCcy.equals(other$fromCcy)) {
                  return false;
               }

               label684: {
                  Object this$fromAmount = this.getFromAmount();
                  Object other$fromAmount = other.getFromAmount();
                  if (this$fromAmount == null) {
                     if (other$fromAmount == null) {
                        break label684;
                     }
                  } else if (this$fromAmount.equals(other$fromAmount)) {
                     break label684;
                  }

                  return false;
               }

               Object this$fromRateFlag = this.getFromRateFlag();
               Object other$fromRateFlag = other.getFromRateFlag();
               if (this$fromRateFlag == null) {
                  if (other$fromRateFlag != null) {
                     return false;
                  }
               } else if (!this$fromRateFlag.equals(other$fromRateFlag)) {
                  return false;
               }

               Object this$toCcy = this.getToCcy();
               Object other$toCcy = other.getToCcy();
               if (this$toCcy == null) {
                  if (other$toCcy != null) {
                     return false;
                  }
               } else if (!this$toCcy.equals(other$toCcy)) {
                  return false;
               }

               label663: {
                  Object this$toAmount = this.getToAmount();
                  Object other$toAmount = other.getToAmount();
                  if (this$toAmount == null) {
                     if (other$toAmount == null) {
                        break label663;
                     }
                  } else if (this$toAmount.equals(other$toAmount)) {
                     break label663;
                  }

                  return false;
               }

               Object this$toRateFlag = this.getToRateFlag();
               Object other$toRateFlag = other.getToRateFlag();
               if (this$toRateFlag == null) {
                  if (other$toRateFlag != null) {
                     return false;
                  }
               } else if (!this$toRateFlag.equals(other$toRateFlag)) {
                  return false;
               }

               label649: {
                  Object this$ovToAmount = this.getOvToAmount();
                  Object other$ovToAmount = other.getOvToAmount();
                  if (this$ovToAmount == null) {
                     if (other$ovToAmount == null) {
                        break label649;
                     }
                  } else if (this$ovToAmount.equals(other$ovToAmount)) {
                     break label649;
                  }

                  return false;
               }

               Object this$quoteType = this.getQuoteType();
               Object other$quoteType = other.getQuoteType();
               if (this$quoteType == null) {
                  if (other$quoteType != null) {
                     return false;
                  }
               } else if (!this$quoteType.equals(other$quoteType)) {
                  return false;
               }

               label635: {
                  Object this$terminalId = this.getTerminalId();
                  Object other$terminalId = other.getTerminalId();
                  if (this$terminalId == null) {
                     if (other$terminalId == null) {
                        break label635;
                     }
                  } else if (this$terminalId.equals(other$terminalId)) {
                     break label635;
                  }

                  return false;
               }

               Object this$traceId = this.getTraceId();
               Object other$traceId = other.getTraceId();
               if (this$traceId == null) {
                  if (other$traceId != null) {
                     return false;
                  }
               } else if (!this$traceId.equals(other$traceId)) {
                  return false;
               }

               label621: {
                  Object this$cashItem = this.getCashItem();
                  Object other$cashItem = other.getCashItem();
                  if (this$cashItem == null) {
                     if (other$cashItem == null) {
                        break label621;
                     }
                  } else if (this$cashItem.equals(other$cashItem)) {
                     break label621;
                  }

                  return false;
               }

               label614: {
                  Object this$tranStatus = this.getTranStatus();
                  Object other$tranStatus = other.getTranStatus();
                  if (this$tranStatus == null) {
                     if (other$tranStatus == null) {
                        break label614;
                     }
                  } else if (this$tranStatus.equals(other$tranStatus)) {
                     break label614;
                  }

                  return false;
               }

               Object this$profitCenter = this.getProfitCenter();
               Object other$profitCenter = other.getProfitCenter();
               if (this$profitCenter == null) {
                  if (other$profitCenter != null) {
                     return false;
                  }
               } else if (!this$profitCenter.equals(other$profitCenter)) {
                  return false;
               }

               label600: {
                  Object this$businessUnit = this.getBusinessUnit();
                  Object other$businessUnit = other.getBusinessUnit();
                  if (this$businessUnit == null) {
                     if (other$businessUnit == null) {
                        break label600;
                     }
                  } else if (this$businessUnit.equals(other$businessUnit)) {
                     break label600;
                  }

                  return false;
               }

               label593: {
                  Object this$eventType = this.getEventType();
                  Object other$eventType = other.getEventType();
                  if (this$eventType == null) {
                     if (other$eventType == null) {
                        break label593;
                     }
                  } else if (this$eventType.equals(other$eventType)) {
                     break label593;
                  }

                  return false;
               }

               Object this$ccy = this.getCcy();
               Object other$ccy = other.getCcy();
               if (this$ccy == null) {
                  if (other$ccy != null) {
                     return false;
                  }
               } else if (!this$ccy.equals(other$ccy)) {
                  return false;
               }

               Object this$printCnt = this.getPrintCnt();
               Object other$printCnt = other.getPrintCnt();
               if (this$printCnt == null) {
                  if (other$printCnt != null) {
                     return false;
                  }
               } else if (!this$printCnt.equals(other$printCnt)) {
                  return false;
               }

               label572: {
                  Object this$batchNo = this.getBatchNo();
                  Object other$batchNo = other.getBatchNo();
                  if (this$batchNo == null) {
                     if (other$batchNo == null) {
                        break label572;
                     }
                  } else if (this$batchNo.equals(other$batchNo)) {
                     break label572;
                  }

                  return false;
               }

               Object this$bizType = this.getBizType();
               Object other$bizType = other.getBizType();
               if (this$bizType == null) {
                  if (other$bizType != null) {
                     return false;
                  }
               } else if (!this$bizType.equals(other$bizType)) {
                  return false;
               }

               Object this$pbkUpdFlag = this.getPbkUpdFlag();
               Object other$pbkUpdFlag = other.getPbkUpdFlag();
               if (this$pbkUpdFlag == null) {
                  if (other$pbkUpdFlag != null) {
                     return false;
                  }
               } else if (!this$pbkUpdFlag.equals(other$pbkUpdFlag)) {
                  return false;
               }

               label551: {
                  Object this$reversal = this.getReversal();
                  Object other$reversal = other.getReversal();
                  if (this$reversal == null) {
                     if (other$reversal == null) {
                        break label551;
                     }
                  } else if (this$reversal.equals(other$reversal)) {
                     break label551;
                  }

                  return false;
               }

               Object this$payUnit = this.getPayUnit();
               Object other$payUnit = other.getPayUnit();
               if (this$payUnit == null) {
                  if (other$payUnit != null) {
                     return false;
                  }
               } else if (!this$payUnit.equals(other$payUnit)) {
                  return false;
               }

               label537: {
                  Object this$servCharge = this.getServCharge();
                  Object other$servCharge = other.getServCharge();
                  if (this$servCharge == null) {
                     if (other$servCharge == null) {
                        break label537;
                     }
                  } else if (this$servCharge.equals(other$servCharge)) {
                     break label537;
                  }

                  return false;
               }

               Object this$tranTimestamp = this.getTranTimestamp();
               Object other$tranTimestamp = other.getTranTimestamp();
               if (this$tranTimestamp == null) {
                  if (other$tranTimestamp != null) {
                     return false;
                  }
               } else if (!this$tranTimestamp.equals(other$tranTimestamp)) {
                  return false;
               }

               Object this$origTranTimestamp = this.getOrigTranTimestamp();
               Object other$origTranTimestamp = other.getOrigTranTimestamp();
               if (this$origTranTimestamp == null) {
                  if (other$origTranTimestamp == null) {
                     return true;
                  }
               } else if (this$origTranTimestamp.equals(other$origTranTimestamp)) {
                  return true;
               }

               return false;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400001001Out.TranHistArray;
      }
      public String toString() {
         return "Core1400001001Out.TranHistArray(othProdType=" + this.getOthProdType() + ", othAcctCcy=" + this.getOthAcctCcy() + ", documentType=" + this.getDocumentType() + ", clientName=" + this.getClientName() + ", channelSeqNo=" + this.getChannelSeqNo() + ", termType=" + this.getTermType() + ", clientNo=" + this.getClientNo() + ", acctDesc=" + this.getAcctDesc() + ", tranType=" + this.getTranType() + ", clientType=" + this.getClientType() + ", acctCcy=" + this.getAcctCcy() + ", acctSeqNo=" + this.getAcctSeqNo() + ", acctBranch=" + this.getAcctBranch() + ", actualBal=" + this.getActualBal() + ", term=" + this.getTerm() + ", chClientName=" + this.getChClientName() + ", tranDesc=" + this.getTranDesc() + ", othDocumentType=" + this.getOthDocumentType() + ", othAcctDesc=" + this.getOthAcctDesc() + ", acctOpenDate=" + this.getAcctOpenDate() + ", othAcctSeqNo=" + this.getOthAcctSeqNo() + ", amtType=" + this.getAmtType() + ", prodType=" + this.getProdType() + ", crDrMaintInd=" + this.getCrDrMaintInd() + ", othDocumentId=" + this.getOthDocumentId() + ", baseAcctNo=" + this.getBaseAcctNo() + ", othBaseAcctNo=" + this.getOthBaseAcctNo() + ", othBranch=" + this.getOthBranch() + ", tranAmt=" + this.getTranAmt() + ", documentId=" + this.getDocumentId() + ", tranDate=" + this.getTranDate() + ", matureDate=" + this.getMatureDate() + ", previousBalAmt=" + this.getPreviousBalAmt() + ", origTranAmt=" + this.getOrigTranAmt() + ", ddAmt=" + this.getDdAmt() + ", odAmount=" + this.getOdAmount() + ", baseEquivAmt=" + this.getBaseEquivAmt() + ", fromXrate=" + this.getFromXrate() + ", toXrate=" + this.getToXrate() + ", subSeqNo=" + this.getSubSeqNo() + ", othAcctName=" + this.getOthAcctName() + ", othBankName=" + this.getOthBankName() + ", othBankCode=" + this.getOthBankCode() + ", othBranchName=" + this.getOthBranchName() + ", narrativeCode=" + this.getNarrativeCode() + ", narrativeCodeDesc=" + this.getNarrativeCodeDesc() + ", sourceType=" + this.getSourceType() + ", sourceModule=" + this.getSourceModule() + ", piFlag=" + this.getPiFlag() + ", toId=" + this.getToId() + ", authUserId=" + this.getAuthUserId() + ", balType=" + this.getBalType() + ", userId=" + this.getUserId() + ", branch=" + this.getBranch() + ", bankName=" + this.getBankName() + ", bankCode=" + this.getBankCode() + ", contraTranDate=" + this.getContraTranDate() + ", othSeqNo=" + this.getOthSeqNo() + ", othReference=" + this.getOthReference() + ", docType=" + this.getDocType() + ", prefix=" + this.getPrefix() + ", voucherNo=" + this.getVoucherNo() + ", withdrawalType=" + this.getWithdrawalType() + ", bankSeqNo=" + this.getBankSeqNo() + ", apprUserId=" + this.getApprUserId() + ", reference=" + this.getReference() + ", tranCategory=" + this.getTranCategory() + ", reversalTranType=" + this.getReversalTranType() + ", reversalDate=" + this.getReversalDate() + ", seqNo=" + this.getSeqNo() + ", narrative=" + this.getNarrative() + ", tranNote=" + this.getTranNote() + ", primaryTranSeqNo=" + this.getPrimaryTranSeqNo() + ", fhSeqNo=" + this.getFhSeqNo() + ", contraEquivAmt=" + this.getContraEquivAmt() + ", crossRate=" + this.getCrossRate() + ", fromCcy=" + this.getFromCcy() + ", fromAmount=" + this.getFromAmount() + ", fromRateFlag=" + this.getFromRateFlag() + ", toCcy=" + this.getToCcy() + ", toAmount=" + this.getToAmount() + ", toRateFlag=" + this.getToRateFlag() + ", ovToAmount=" + this.getOvToAmount() + ", quoteType=" + this.getQuoteType() + ", terminalId=" + this.getTerminalId() + ", traceId=" + this.getTraceId() + ", cashItem=" + this.getCashItem() + ", tranStatus=" + this.getTranStatus() + ", profitCenter=" + this.getProfitCenter() + ", businessUnit=" + this.getBusinessUnit() + ", eventType=" + this.getEventType() + ", ccy=" + this.getCcy() + ", printCnt=" + this.getPrintCnt() + ", batchNo=" + this.getBatchNo() + ", bizType=" + this.getBizType() + ", pbkUpdFlag=" + this.getPbkUpdFlag() + ", reversal=" + this.getReversal() + ", payUnit=" + this.getPayUnit() + ", servCharge=" + this.getServCharge() + ", tranTimestamp=" + this.getTranTimestamp() + ", origTranTimestamp=" + this.getOrigTranTimestamp() + ")";
      }
   }
}
