package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;

@MessageOut
public class Core1200100220Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "所属机构号",
      notNull = false,
      length = "50",
      remark = "机构代码",
      maxSize = 50
   )
   private String branch;
   @V(
      desc = "已分配额度",
      notNull = false,
      length = "17",
      remark = "已经分配的下放额度，由年度分配、或分配给机构、或分配给渠道，可以由下游合法使用的额度",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal distributeLimit;
   @V(
      desc = "已占用额度",
      notNull = false,
      length = "17",
      remark = "已占用额度",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal holdingLimit;
   @V(
      desc = "剩余额度",
      notNull = false,
      length = "17",
      remark = "剩余额度",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal leaveLimit;
   @V(
      desc = "OA申请额度",
      notNull = false,
      length = "17",
      remark = "OA申请额度",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal oaAmt;
   @V(
      desc = "本机构可用额度额度",
      notNull = false,
      length = "17",
      remark = "本机构可用额度额度",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal branchLeaveLimit;

   public String getBranch() {
      return this.branch;
   }

   public BigDecimal getDistributeLimit() {
      return this.distributeLimit;
   }

   public BigDecimal getHoldingLimit() {
      return this.holdingLimit;
   }

   public BigDecimal getLeaveLimit() {
      return this.leaveLimit;
   }

   public BigDecimal getOaAmt() {
      return this.oaAmt;
   }

   public BigDecimal getBranchLeaveLimit() {
      return this.branchLeaveLimit;
   }

   public void setBranch(String branch) {
      this.branch = branch;
   }

   public void setDistributeLimit(BigDecimal distributeLimit) {
      this.distributeLimit = distributeLimit;
   }

   public void setHoldingLimit(BigDecimal holdingLimit) {
      this.holdingLimit = holdingLimit;
   }

   public void setLeaveLimit(BigDecimal leaveLimit) {
      this.leaveLimit = leaveLimit;
   }

   public void setOaAmt(BigDecimal oaAmt) {
      this.oaAmt = oaAmt;
   }

   public void setBranchLeaveLimit(BigDecimal branchLeaveLimit) {
      this.branchLeaveLimit = branchLeaveLimit;
   }

   public String toString() {
      return "Core1200100220Out(branch=" + this.getBranch() + ", distributeLimit=" + this.getDistributeLimit() + ", holdingLimit=" + this.getHoldingLimit() + ", leaveLimit=" + this.getLeaveLimit() + ", oaAmt=" + this.getOaAmt() + ", branchLeaveLimit=" + this.getBranchLeaveLimit() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200100220Out)) {
         return false;
      } else {
         Core1200100220Out other = (Core1200100220Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$branch = this.getBranch();
            Object other$branch = other.getBranch();
            if (this$branch == null) {
               if (other$branch != null) {
                  return false;
               }
            } else if (!this$branch.equals(other$branch)) {
               return false;
            }

            Object this$distributeLimit = this.getDistributeLimit();
            Object other$distributeLimit = other.getDistributeLimit();
            if (this$distributeLimit == null) {
               if (other$distributeLimit != null) {
                  return false;
               }
            } else if (!this$distributeLimit.equals(other$distributeLimit)) {
               return false;
            }

            label71: {
               Object this$holdingLimit = this.getHoldingLimit();
               Object other$holdingLimit = other.getHoldingLimit();
               if (this$holdingLimit == null) {
                  if (other$holdingLimit == null) {
                     break label71;
                  }
               } else if (this$holdingLimit.equals(other$holdingLimit)) {
                  break label71;
               }

               return false;
            }

            label64: {
               Object this$leaveLimit = this.getLeaveLimit();
               Object other$leaveLimit = other.getLeaveLimit();
               if (this$leaveLimit == null) {
                  if (other$leaveLimit == null) {
                     break label64;
                  }
               } else if (this$leaveLimit.equals(other$leaveLimit)) {
                  break label64;
               }

               return false;
            }

            Object this$oaAmt = this.getOaAmt();
            Object other$oaAmt = other.getOaAmt();
            if (this$oaAmt == null) {
               if (other$oaAmt != null) {
                  return false;
               }
            } else if (!this$oaAmt.equals(other$oaAmt)) {
               return false;
            }

            Object this$branchLeaveLimit = this.getBranchLeaveLimit();
            Object other$branchLeaveLimit = other.getBranchLeaveLimit();
            if (this$branchLeaveLimit == null) {
               if (other$branchLeaveLimit != null) {
                  return false;
               }
            } else if (!this$branchLeaveLimit.equals(other$branchLeaveLimit)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200100220Out;
   }
}
