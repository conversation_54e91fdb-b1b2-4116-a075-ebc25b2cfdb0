package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.util.List;

@MessageOut
public class Core1000100002Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1000100002Out.FeeArray> feeArray;

   public List<Core1000100002Out.FeeArray> getFeeArray() {
      return this.feeArray;
   }

   public void setFeeArray(List<Core1000100002Out.FeeArray> feeArray) {
      this.feeArray = feeArray;
   }

   public String toString() {
      return "Core1000100002Out(feeArray=" + this.getFeeArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1000100002Out)) {
         return false;
      } else {
         Core1000100002Out other = (Core1000100002Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$feeArray = this.getFeeArray();
            Object other$feeArray = other.getFeeArray();
            if (this$feeArray == null) {
               if (other$feeArray != null) {
                  return false;
               }
            } else if (!this$feeArray.equals(other$feeArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1000100002Out;
   }
   public static class FeeArray {
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "收费序号",
         notNull = false,
         length = "50",
         remark = "收费序号",
         maxSize = 50
      )
      private String scSeqNo;

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getScSeqNo() {
         return this.scSeqNo;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setScSeqNo(String scSeqNo) {
         this.scSeqNo = scSeqNo;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1000100002Out.FeeArray)) {
            return false;
         } else {
            Core1000100002Out.FeeArray other = (Core1000100002Out.FeeArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo != null) {
                     return false;
                  }
               } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                  return false;
               }

               Object this$scSeqNo = this.getScSeqNo();
               Object other$scSeqNo = other.getScSeqNo();
               if (this$scSeqNo == null) {
                  if (other$scSeqNo != null) {
                     return false;
                  }
               } else if (!this$scSeqNo.equals(other$scSeqNo)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1000100002Out.FeeArray;
      }
      public String toString() {
         return "Core1000100002Out.FeeArray(acctSeqNo=" + this.getAcctSeqNo() + ", scSeqNo=" + this.getScSeqNo() + ")";
      }
   }
}
