package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000219In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000219Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000219 {
   String URL = "/rb/inq/term/hang/detail";


   @ApiRemark("定期账户开户资金来源挂销账明细查询")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0219"
   )
   @BusinessCategory("1400查询")
   @FunctionCategory("RB47-客户账户查询")
   @ApiUseStatus("PRODUCT-产品")
   Core14000219Out runService(Core14000219In var1);
}
