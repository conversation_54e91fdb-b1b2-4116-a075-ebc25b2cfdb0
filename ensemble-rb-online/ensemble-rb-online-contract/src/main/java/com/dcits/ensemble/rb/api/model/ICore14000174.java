package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000174In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000174Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000174 {
   String URL = "/rb/inqury/register/delaypayint";


   @ApiDesc("延迟付息信息登记查询")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0174"
   )
   @FunctionCategory("RB02-账户管理")
   Core14000174Out runService(Core14000174In var1);
}
