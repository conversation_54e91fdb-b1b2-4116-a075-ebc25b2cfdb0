package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400100219In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400100219In.Body body;

   public Core1400100219In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400100219In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400100219In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100219In)) {
         return false;
      } else {
         Core1400100219In other = (Core1400100219In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100219In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "发行年度",
         notNull = false,
         length = "5",
         remark = "发行年度",
         maxSize = 5
      )
      private String issueYear;
      @V(
         desc = "期次代码",
         notNull = false,
         length = "50",
         remark = "期次代码",
         maxSize = 50
      )
      private String stageCode;
      @V(
         desc = "支持组合购买方式",
         notNull = false,
         length = "1",
         remark = "1-单独购买2-组合购买3-单买与组合买",
         maxSize = 1
      )
      private String allowBuyWayCd;
      @V(
         desc = "期次描述",
         notNull = false,
         length = "200",
         remark = "期次描述",
         maxSize = 200
      )
      private String stageCodeDesc;

      public String getProdType() {
         return this.prodType;
      }

      public String getCcy() {
         return this.ccy;
      }

      public String getIssueYear() {
         return this.issueYear;
      }

      public String getStageCode() {
         return this.stageCode;
      }

      public String getAllowBuyWayCd() {
         return this.allowBuyWayCd;
      }

      public String getStageCodeDesc() {
         return this.stageCodeDesc;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setIssueYear(String issueYear) {
         this.issueYear = issueYear;
      }

      public void setStageCode(String stageCode) {
         this.stageCode = stageCode;
      }

      public void setAllowBuyWayCd(String allowBuyWayCd) {
         this.allowBuyWayCd = allowBuyWayCd;
      }

      public void setStageCodeDesc(String stageCodeDesc) {
         this.stageCodeDesc = stageCodeDesc;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100219In.Body)) {
            return false;
         } else {
            Core1400100219In.Body other = (Core1400100219In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               Object this$ccy = this.getCcy();
               Object other$ccy = other.getCcy();
               if (this$ccy == null) {
                  if (other$ccy != null) {
                     return false;
                  }
               } else if (!this$ccy.equals(other$ccy)) {
                  return false;
               }

               Object this$issueYear = this.getIssueYear();
               Object other$issueYear = other.getIssueYear();
               if (this$issueYear == null) {
                  if (other$issueYear != null) {
                     return false;
                  }
               } else if (!this$issueYear.equals(other$issueYear)) {
                  return false;
               }

               label62: {
                  Object this$stageCode = this.getStageCode();
                  Object other$stageCode = other.getStageCode();
                  if (this$stageCode == null) {
                     if (other$stageCode == null) {
                        break label62;
                     }
                  } else if (this$stageCode.equals(other$stageCode)) {
                     break label62;
                  }

                  return false;
               }

               label55: {
                  Object this$allowBuyWayCd = this.getAllowBuyWayCd();
                  Object other$allowBuyWayCd = other.getAllowBuyWayCd();
                  if (this$allowBuyWayCd == null) {
                     if (other$allowBuyWayCd == null) {
                        break label55;
                     }
                  } else if (this$allowBuyWayCd.equals(other$allowBuyWayCd)) {
                     break label55;
                  }

                  return false;
               }

               Object this$stageCodeDesc = this.getStageCodeDesc();
               Object other$stageCodeDesc = other.getStageCodeDesc();
               if (this$stageCodeDesc == null) {
                  if (other$stageCodeDesc != null) {
                     return false;
                  }
               } else if (!this$stageCodeDesc.equals(other$stageCodeDesc)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100219In.Body;
      }
      public String toString() {
         return "Core1400100219In.Body(prodType=" + this.getProdType() + ", ccy=" + this.getCcy() + ", issueYear=" + this.getIssueYear() + ", stageCode=" + this.getStageCode() + ", allowBuyWayCd=" + this.getAllowBuyWayCd() + ", stageCodeDesc=" + this.getStageCodeDesc() + ")";
      }
   }
}
