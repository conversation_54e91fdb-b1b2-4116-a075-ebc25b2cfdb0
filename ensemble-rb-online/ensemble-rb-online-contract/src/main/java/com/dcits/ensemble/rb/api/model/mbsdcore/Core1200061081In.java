package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;

@MessageIn
public class Core1200061081In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1200061081In.Body body;

   public Core1200061081In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1200061081In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1200061081In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200061081In)) {
         return false;
      } else {
         Core1200061081In other = (Core1200061081In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200061081In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "操作类型",
         notNull = false,
         length = "3",
         remark = "操作类型",
         maxSize = 3
      )
      private String option;
      @V(
         desc = "交割类型",
         notNull = false,
         length = "2",
         inDesc = "0-到期交割,1-提前交割,2-展期,3-违约",
         remark = "0-到期交割；1--提前交割；2-展期；3-违约",
         maxSize = 2
      )
      private String crossType;
      @V(
         desc = "兑换类型",
         notNull = false,
         length = "1",
         inDesc = "B-结汇,S-售汇,E-外币兑换",
         remark = "兑换类型",
         maxSize = 1
      )
      private String exType;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "业务编号",
         notNull = false,
         length = "50",
         remark = "业务编号",
         maxSize = 50
      )
      private String busiNo;
      @V(
         desc = "交易日期",
         notNull = false,
         remark = "交易日期"
      )
      private String tranDate;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "交易对手",
         notNull = false,
         length = "1",
         inDesc = "1-对客,2-合作行",
         remark = "交易对手",
         maxSize = 1
      )
      private String dealRecAcct;
      @V(
         desc = "交易机构",
         notNull = false,
         length = "50",
         remark = "交易机构",
         maxSize = 50
      )
      private String tranBranch;
      @V(
         desc = "交割日",
         notNull = false,
         remark = "交割日"
      )
      private String crossDate;
      @V(
         desc = "业务种类",
         notNull = false,
         length = "20",
         remark = "业务种类",
         maxSize = 20
      )
      private String busiType;
      @V(
         desc = "外币币种",
         notNull = false,
         length = "3",
         remark = "外币币种",
         maxSize = 3
      )
      private String foreCcy;
      @V(
         desc = "交割总金额",
         notNull = false,
         length = "17",
         remark = "交割总金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal crossTotalAmt;
      @V(
         desc = "交割金额",
         notNull = false,
         length = "17",
         remark = "交割金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal crossAmt;
      @V(
         desc = "未交割金额",
         notNull = false,
         length = "17",
         remark = "未交割金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal noCrossAmt;
      @V(
         desc = "我行报价",
         notNull = false,
         length = "17",
         remark = "我行报价",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal ourOffer;
      @V(
         desc = "合作行报价",
         notNull = false,
         length = "17",
         remark = "合作行报价",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal compBankOffer;
      @V(
         desc = "合作行名称",
         notNull = false,
         length = "30",
         remark = "合作行名称",
         maxSize = 30
      )
      private String compBankName;
      @V(
         desc = "本次交割金额折人民币金额",
         notNull = false,
         length = "17",
         remark = "本次交割金额折人民币金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal crossCnyAmt;
      @V(
         desc = "原交易日期",
         notNull = false,
         length = "10",
         remark = "原交易日期",
         maxSize = 10
      )
      private String orgTranDate;
      @V(
         desc = "sellttflag",
         notNull = false,
         length = "5",
         remark = "sellTtFlag",
         maxSize = 5
      )
      private String sellTtFlag;
      @V(
         desc = "原交易币种",
         notNull = false,
         length = "3",
         remark = "原交易币种",
         maxSize = 3
      )
      private String orgTranCcy;
      @V(
         desc = "原交易金额",
         notNull = false,
         length = "17",
         remark = "原交易金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal orgTranAmt;
      @V(
         desc = "原约定报价",
         notNull = false,
         length = "17",
         remark = "原约定报价",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal orgAppointOffer;
      @V(
         desc = "原交割类型",
         notNull = false,
         length = "3",
         remark = "原交割类型",
         maxSize = 3
      )
      private String orgCrossType;
      @V(
         desc = "原约定交割日期",
         notNull = false,
         length = "10",
         remark = "原约定交割日期",
         maxSize = 10
      )
      private String orgAppointCrossDate;
      @V(
         desc = "提前交割报价",
         notNull = false,
         length = "17",
         remark = "提前交割报价",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal preCrossOffer;
      @V(
         desc = "远端对冲汇率",
         notNull = false,
         length = "17",
         remark = "远端对冲汇率",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal farRate;
      @V(
         desc = "提前交割日期",
         notNull = false,
         length = "10",
         remark = "提前交割日期",
         maxSize = 10
      )
      private String preCrossDate;
      @V(
         desc = "盈损扣账金额",
         notNull = false,
         length = "17",
         remark = "盈损扣账金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal lossTranAmt;
      @V(
         desc = "盈余金额",
         notNull = false,
         length = "17",
         remark = "盈余金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal surplusAmt;
      @V(
         desc = "损失金额",
         notNull = false,
         length = "17",
         remark = "损失金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal lossAmt;
      @V(
         desc = "展期交易日期",
         notNull = false,
         length = "10",
         remark = "展期交易日期",
         maxSize = 10
      )
      private String renewTranDate;
      @V(
         desc = "近端对冲汇率",
         notNull = false,
         length = "15",
         remark = "近端对冲汇率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal nearRate;
      @V(
         desc = "展期后约定报价",
         notNull = false,
         length = "17",
         remark = "展期后约定报价",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal renewAppointOffer;
      @V(
         desc = "展期择期交割日",
         notNull = false,
         length = "10",
         remark = "展期择期交割日",
         maxSize = 10
      )
      private String renewSelectCrossDay;
      @V(
         desc = "占用额度币种",
         notNull = false,
         length = "3",
         remark = "占用额度总金额",
         maxSize = 3
      )
      private String holdLimitCcy;
      @V(
         desc = "占用额度金额",
         notNull = false,
         length = "17",
         remark = "占用额度总金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal holdLimitAmt;
      @V(
         desc = "保证金金额",
         notNull = false,
         length = "17",
         remark = "保证金金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal bondAmt;
      @V(
         desc = "违约金额",
         notNull = false,
         length = "17",
         remark = "违约金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal preAmt;
      @V(
         desc = "违约报价",
         notNull = false,
         length = "17",
         remark = "违约报价",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal preOffer;
      @V(
         desc = "违约交易日期",
         notNull = false,
         remark = "违约交易日期"
      )
      private String preTranDate;
      @V(
         desc = "清算账户币种",
         notNull = false,
         length = "3",
         remark = "清算账户币种",
         maxSize = 3
      )
      private String clearAcctCcy;
      @V(
         desc = "备注",
         notNull = false,
         length = "200",
         remark = "备注",
         maxSize = 200
      )
      private String remark;
      @V(
         desc = "待清算过度账户",
         notNull = false,
         length = "50",
         remark = "待清算过度账户（人名币）",
         maxSize = 50
      )
      private String toClearAcctNoCny;
      @V(
         desc = "代清算过渡账户（人民币）序号",
         notNull = false,
         length = "50",
         remark = "待清算过渡账户(人民币)序号",
         maxSize = 50
      )
      private String toClearCnySeqNo;
      @V(
         desc = "待清算过渡账户（外币）",
         notNull = false,
         length = "50",
         remark = "待清算过渡账户（外币）",
         maxSize = 50
      )
      private String toClearAcctNoFore;
      @V(
         desc = "待清算过渡账户（外币）序号",
         notNull = false,
         length = "50",
         remark = "待清算过渡账户(外币)序号",
         maxSize = 50
      )
      private String toClearForeSeqNo;
      @V(
         desc = "清算账户",
         notNull = false,
         length = "50",
         remark = "清算账户",
         maxSize = 50
      )
      private String clearAcctNo;
      @V(
         desc = "清算账户序号",
         notNull = false,
         length = "20",
         remark = "清算账户序号",
         maxSize = 20
      )
      private String clearAcctSeqNo;
      @V(
         desc = "扣款账号",
         notNull = false,
         length = "50",
         remark = "扣款账号",
         maxSize = 50
      )
      private String deductBaseAcctNo;
      @V(
         desc = "扣款账号产品",
         notNull = false,
         length = "10",
         remark = "扣款账号产品",
         maxSize = 10
      )
      private String deductProdType;
      @V(
         desc = "扣款账号币种",
         notNull = false,
         length = "3",
         remark = "扣款账号币种",
         maxSize = 3
      )
      private String deductCcy;
      @V(
         desc = "扣款账号序号",
         notNull = false,
         length = "10",
         remark = "扣款账号序号",
         maxSize = 10
      )
      private String deductSeqNo;
      @V(
         desc = "人民币账号",
         notNull = false,
         length = "50",
         remark = "人民币账号",
         maxSize = 50
      )
      private String cnyAcctNo;
      @V(
         desc = "保证金账户序号",
         notNull = false,
         length = "5",
         remark = "保证金账户序号",
         maxSize = 5
      )
      private String bondAcctSeqNo;
      @V(
         desc = "保证金账号",
         notNull = false,
         length = "50",
         remark = "保证金账号",
         maxSize = 50
      )
      private String depBaseAcctNo;
      @V(
         desc = "当日存入金额",
         notNull = false,
         length = "17",
         remark = "当日存入金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal depAmt;
      @V(
         desc = "保证金币种",
         notNull = false,
         length = "3",
         remark = "保证金币种",
         maxSize = 3
      )
      private String depCcy;
      @V(
         desc = "展期交割方式",
         notNull = false,
         length = "2",
         remark = "展期交割方式",
         maxSize = 2
      )
      private String deferDealType;
      @V(
         desc = "保证金账号序号",
         notNull = false,
         length = "5",
         remark = "保证金账号序号",
         maxSize = 5
      )
      private String depAcctSeqNo;
      @V(
         desc = "外币账号",
         notNull = false,
         length = "50",
         remark = "外币账号",
         maxSize = 50
      )
      private String foreBaseAcctNo;
      @V(
         desc = "展期业务编号",
         notNull = false,
         length = "30",
         remark = "展期业务编号",
         maxSize = 30
      )
      private String deferBusiNo;
      @V(
         desc = "占用额度金额",
         notNull = false,
         length = "17",
         remark = "占用额度金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal occAmt;
      @V(
         desc = "展期固定交割日",
         notNull = false,
         remark = "展期固定交割日"
      )
      private String deferFixDate;
      @V(
         desc = "占用额度币种",
         notNull = false,
         length = "3",
         remark = "占用额度币种",
         maxSize = 3
      )
      private String occCcy;
      @V(
         desc = "展期择期交割日",
         notNull = false,
         remark = "展期择期交割日"
      )
      private String deferChangeDate;
      @V(
         desc = "展期择期交割结束日",
         notNull = false,
         remark = "展期择期交割结束日"
      )
      private String deferChangeDateEnd;
      @V(
         desc = "展期交易金额",
         notNull = false,
         length = "17",
         remark = "展期交易金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal deferTranAmt;
      @V(
         desc = "展期交易币种",
         notNull = false,
         length = "3",
         remark = "展期交易币种",
         maxSize = 3
      )
      private String deferCcy;
      @V(
         desc = "展期约定报价",
         notNull = false,
         length = "17",
         remark = "展期约定报价",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal deferRate;

      public String getOption() {
         return this.option;
      }

      public String getCrossType() {
         return this.crossType;
      }

      public String getExType() {
         return this.exType;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public String getBusiNo() {
         return this.busiNo;
      }

      public String getTranDate() {
         return this.tranDate;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getDealRecAcct() {
         return this.dealRecAcct;
      }

      public String getTranBranch() {
         return this.tranBranch;
      }

      public String getCrossDate() {
         return this.crossDate;
      }

      public String getBusiType() {
         return this.busiType;
      }

      public String getForeCcy() {
         return this.foreCcy;
      }

      public BigDecimal getCrossTotalAmt() {
         return this.crossTotalAmt;
      }

      public BigDecimal getCrossAmt() {
         return this.crossAmt;
      }

      public BigDecimal getNoCrossAmt() {
         return this.noCrossAmt;
      }

      public BigDecimal getOurOffer() {
         return this.ourOffer;
      }

      public BigDecimal getCompBankOffer() {
         return this.compBankOffer;
      }

      public String getCompBankName() {
         return this.compBankName;
      }

      public BigDecimal getCrossCnyAmt() {
         return this.crossCnyAmt;
      }

      public String getOrgTranDate() {
         return this.orgTranDate;
      }

      public String getSellTtFlag() {
         return this.sellTtFlag;
      }

      public String getOrgTranCcy() {
         return this.orgTranCcy;
      }

      public BigDecimal getOrgTranAmt() {
         return this.orgTranAmt;
      }

      public BigDecimal getOrgAppointOffer() {
         return this.orgAppointOffer;
      }

      public String getOrgCrossType() {
         return this.orgCrossType;
      }

      public String getOrgAppointCrossDate() {
         return this.orgAppointCrossDate;
      }

      public BigDecimal getPreCrossOffer() {
         return this.preCrossOffer;
      }

      public BigDecimal getFarRate() {
         return this.farRate;
      }

      public String getPreCrossDate() {
         return this.preCrossDate;
      }

      public BigDecimal getLossTranAmt() {
         return this.lossTranAmt;
      }

      public BigDecimal getSurplusAmt() {
         return this.surplusAmt;
      }

      public BigDecimal getLossAmt() {
         return this.lossAmt;
      }

      public String getRenewTranDate() {
         return this.renewTranDate;
      }

      public BigDecimal getNearRate() {
         return this.nearRate;
      }

      public BigDecimal getRenewAppointOffer() {
         return this.renewAppointOffer;
      }

      public String getRenewSelectCrossDay() {
         return this.renewSelectCrossDay;
      }

      public String getHoldLimitCcy() {
         return this.holdLimitCcy;
      }

      public BigDecimal getHoldLimitAmt() {
         return this.holdLimitAmt;
      }

      public BigDecimal getBondAmt() {
         return this.bondAmt;
      }

      public BigDecimal getPreAmt() {
         return this.preAmt;
      }

      public BigDecimal getPreOffer() {
         return this.preOffer;
      }

      public String getPreTranDate() {
         return this.preTranDate;
      }

      public String getClearAcctCcy() {
         return this.clearAcctCcy;
      }

      public String getRemark() {
         return this.remark;
      }

      public String getToClearAcctNoCny() {
         return this.toClearAcctNoCny;
      }

      public String getToClearCnySeqNo() {
         return this.toClearCnySeqNo;
      }

      public String getToClearAcctNoFore() {
         return this.toClearAcctNoFore;
      }

      public String getToClearForeSeqNo() {
         return this.toClearForeSeqNo;
      }

      public String getClearAcctNo() {
         return this.clearAcctNo;
      }

      public String getClearAcctSeqNo() {
         return this.clearAcctSeqNo;
      }

      public String getDeductBaseAcctNo() {
         return this.deductBaseAcctNo;
      }

      public String getDeductProdType() {
         return this.deductProdType;
      }

      public String getDeductCcy() {
         return this.deductCcy;
      }

      public String getDeductSeqNo() {
         return this.deductSeqNo;
      }

      public String getCnyAcctNo() {
         return this.cnyAcctNo;
      }

      public String getBondAcctSeqNo() {
         return this.bondAcctSeqNo;
      }

      public String getDepBaseAcctNo() {
         return this.depBaseAcctNo;
      }

      public BigDecimal getDepAmt() {
         return this.depAmt;
      }

      public String getDepCcy() {
         return this.depCcy;
      }

      public String getDeferDealType() {
         return this.deferDealType;
      }

      public String getDepAcctSeqNo() {
         return this.depAcctSeqNo;
      }

      public String getForeBaseAcctNo() {
         return this.foreBaseAcctNo;
      }

      public String getDeferBusiNo() {
         return this.deferBusiNo;
      }

      public BigDecimal getOccAmt() {
         return this.occAmt;
      }

      public String getDeferFixDate() {
         return this.deferFixDate;
      }

      public String getOccCcy() {
         return this.occCcy;
      }

      public String getDeferChangeDate() {
         return this.deferChangeDate;
      }

      public String getDeferChangeDateEnd() {
         return this.deferChangeDateEnd;
      }

      public BigDecimal getDeferTranAmt() {
         return this.deferTranAmt;
      }

      public String getDeferCcy() {
         return this.deferCcy;
      }

      public BigDecimal getDeferRate() {
         return this.deferRate;
      }

      public void setOption(String option) {
         this.option = option;
      }

      public void setCrossType(String crossType) {
         this.crossType = crossType;
      }

      public void setExType(String exType) {
         this.exType = exType;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setBusiNo(String busiNo) {
         this.busiNo = busiNo;
      }

      public void setTranDate(String tranDate) {
         this.tranDate = tranDate;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setDealRecAcct(String dealRecAcct) {
         this.dealRecAcct = dealRecAcct;
      }

      public void setTranBranch(String tranBranch) {
         this.tranBranch = tranBranch;
      }

      public void setCrossDate(String crossDate) {
         this.crossDate = crossDate;
      }

      public void setBusiType(String busiType) {
         this.busiType = busiType;
      }

      public void setForeCcy(String foreCcy) {
         this.foreCcy = foreCcy;
      }

      public void setCrossTotalAmt(BigDecimal crossTotalAmt) {
         this.crossTotalAmt = crossTotalAmt;
      }

      public void setCrossAmt(BigDecimal crossAmt) {
         this.crossAmt = crossAmt;
      }

      public void setNoCrossAmt(BigDecimal noCrossAmt) {
         this.noCrossAmt = noCrossAmt;
      }

      public void setOurOffer(BigDecimal ourOffer) {
         this.ourOffer = ourOffer;
      }

      public void setCompBankOffer(BigDecimal compBankOffer) {
         this.compBankOffer = compBankOffer;
      }

      public void setCompBankName(String compBankName) {
         this.compBankName = compBankName;
      }

      public void setCrossCnyAmt(BigDecimal crossCnyAmt) {
         this.crossCnyAmt = crossCnyAmt;
      }

      public void setOrgTranDate(String orgTranDate) {
         this.orgTranDate = orgTranDate;
      }

      public void setSellTtFlag(String sellTtFlag) {
         this.sellTtFlag = sellTtFlag;
      }

      public void setOrgTranCcy(String orgTranCcy) {
         this.orgTranCcy = orgTranCcy;
      }

      public void setOrgTranAmt(BigDecimal orgTranAmt) {
         this.orgTranAmt = orgTranAmt;
      }

      public void setOrgAppointOffer(BigDecimal orgAppointOffer) {
         this.orgAppointOffer = orgAppointOffer;
      }

      public void setOrgCrossType(String orgCrossType) {
         this.orgCrossType = orgCrossType;
      }

      public void setOrgAppointCrossDate(String orgAppointCrossDate) {
         this.orgAppointCrossDate = orgAppointCrossDate;
      }

      public void setPreCrossOffer(BigDecimal preCrossOffer) {
         this.preCrossOffer = preCrossOffer;
      }

      public void setFarRate(BigDecimal farRate) {
         this.farRate = farRate;
      }

      public void setPreCrossDate(String preCrossDate) {
         this.preCrossDate = preCrossDate;
      }

      public void setLossTranAmt(BigDecimal lossTranAmt) {
         this.lossTranAmt = lossTranAmt;
      }

      public void setSurplusAmt(BigDecimal surplusAmt) {
         this.surplusAmt = surplusAmt;
      }

      public void setLossAmt(BigDecimal lossAmt) {
         this.lossAmt = lossAmt;
      }

      public void setRenewTranDate(String renewTranDate) {
         this.renewTranDate = renewTranDate;
      }

      public void setNearRate(BigDecimal nearRate) {
         this.nearRate = nearRate;
      }

      public void setRenewAppointOffer(BigDecimal renewAppointOffer) {
         this.renewAppointOffer = renewAppointOffer;
      }

      public void setRenewSelectCrossDay(String renewSelectCrossDay) {
         this.renewSelectCrossDay = renewSelectCrossDay;
      }

      public void setHoldLimitCcy(String holdLimitCcy) {
         this.holdLimitCcy = holdLimitCcy;
      }

      public void setHoldLimitAmt(BigDecimal holdLimitAmt) {
         this.holdLimitAmt = holdLimitAmt;
      }

      public void setBondAmt(BigDecimal bondAmt) {
         this.bondAmt = bondAmt;
      }

      public void setPreAmt(BigDecimal preAmt) {
         this.preAmt = preAmt;
      }

      public void setPreOffer(BigDecimal preOffer) {
         this.preOffer = preOffer;
      }

      public void setPreTranDate(String preTranDate) {
         this.preTranDate = preTranDate;
      }

      public void setClearAcctCcy(String clearAcctCcy) {
         this.clearAcctCcy = clearAcctCcy;
      }

      public void setRemark(String remark) {
         this.remark = remark;
      }

      public void setToClearAcctNoCny(String toClearAcctNoCny) {
         this.toClearAcctNoCny = toClearAcctNoCny;
      }

      public void setToClearCnySeqNo(String toClearCnySeqNo) {
         this.toClearCnySeqNo = toClearCnySeqNo;
      }

      public void setToClearAcctNoFore(String toClearAcctNoFore) {
         this.toClearAcctNoFore = toClearAcctNoFore;
      }

      public void setToClearForeSeqNo(String toClearForeSeqNo) {
         this.toClearForeSeqNo = toClearForeSeqNo;
      }

      public void setClearAcctNo(String clearAcctNo) {
         this.clearAcctNo = clearAcctNo;
      }

      public void setClearAcctSeqNo(String clearAcctSeqNo) {
         this.clearAcctSeqNo = clearAcctSeqNo;
      }

      public void setDeductBaseAcctNo(String deductBaseAcctNo) {
         this.deductBaseAcctNo = deductBaseAcctNo;
      }

      public void setDeductProdType(String deductProdType) {
         this.deductProdType = deductProdType;
      }

      public void setDeductCcy(String deductCcy) {
         this.deductCcy = deductCcy;
      }

      public void setDeductSeqNo(String deductSeqNo) {
         this.deductSeqNo = deductSeqNo;
      }

      public void setCnyAcctNo(String cnyAcctNo) {
         this.cnyAcctNo = cnyAcctNo;
      }

      public void setBondAcctSeqNo(String bondAcctSeqNo) {
         this.bondAcctSeqNo = bondAcctSeqNo;
      }

      public void setDepBaseAcctNo(String depBaseAcctNo) {
         this.depBaseAcctNo = depBaseAcctNo;
      }

      public void setDepAmt(BigDecimal depAmt) {
         this.depAmt = depAmt;
      }

      public void setDepCcy(String depCcy) {
         this.depCcy = depCcy;
      }

      public void setDeferDealType(String deferDealType) {
         this.deferDealType = deferDealType;
      }

      public void setDepAcctSeqNo(String depAcctSeqNo) {
         this.depAcctSeqNo = depAcctSeqNo;
      }

      public void setForeBaseAcctNo(String foreBaseAcctNo) {
         this.foreBaseAcctNo = foreBaseAcctNo;
      }

      public void setDeferBusiNo(String deferBusiNo) {
         this.deferBusiNo = deferBusiNo;
      }

      public void setOccAmt(BigDecimal occAmt) {
         this.occAmt = occAmt;
      }

      public void setDeferFixDate(String deferFixDate) {
         this.deferFixDate = deferFixDate;
      }

      public void setOccCcy(String occCcy) {
         this.occCcy = occCcy;
      }

      public void setDeferChangeDate(String deferChangeDate) {
         this.deferChangeDate = deferChangeDate;
      }

      public void setDeferChangeDateEnd(String deferChangeDateEnd) {
         this.deferChangeDateEnd = deferChangeDateEnd;
      }

      public void setDeferTranAmt(BigDecimal deferTranAmt) {
         this.deferTranAmt = deferTranAmt;
      }

      public void setDeferCcy(String deferCcy) {
         this.deferCcy = deferCcy;
      }

      public void setDeferRate(BigDecimal deferRate) {
         this.deferRate = deferRate;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1200061081In.Body)) {
            return false;
         } else {
            Core1200061081In.Body other = (Core1200061081In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label863: {
                  Object this$option = this.getOption();
                  Object other$option = other.getOption();
                  if (this$option == null) {
                     if (other$option == null) {
                        break label863;
                     }
                  } else if (this$option.equals(other$option)) {
                     break label863;
                  }

                  return false;
               }

               Object this$crossType = this.getCrossType();
               Object other$crossType = other.getCrossType();
               if (this$crossType == null) {
                  if (other$crossType != null) {
                     return false;
                  }
               } else if (!this$crossType.equals(other$crossType)) {
                  return false;
               }

               Object this$exType = this.getExType();
               Object other$exType = other.getExType();
               if (this$exType == null) {
                  if (other$exType != null) {
                     return false;
                  }
               } else if (!this$exType.equals(other$exType)) {
                  return false;
               }

               label842: {
                  Object this$clientNo = this.getClientNo();
                  Object other$clientNo = other.getClientNo();
                  if (this$clientNo == null) {
                     if (other$clientNo == null) {
                        break label842;
                     }
                  } else if (this$clientNo.equals(other$clientNo)) {
                     break label842;
                  }

                  return false;
               }

               label835: {
                  Object this$busiNo = this.getBusiNo();
                  Object other$busiNo = other.getBusiNo();
                  if (this$busiNo == null) {
                     if (other$busiNo == null) {
                        break label835;
                     }
                  } else if (this$busiNo.equals(other$busiNo)) {
                     break label835;
                  }

                  return false;
               }

               Object this$tranDate = this.getTranDate();
               Object other$tranDate = other.getTranDate();
               if (this$tranDate == null) {
                  if (other$tranDate != null) {
                     return false;
                  }
               } else if (!this$tranDate.equals(other$tranDate)) {
                  return false;
               }

               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo != null) {
                     return false;
                  }
               } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
                  return false;
               }

               label814: {
                  Object this$dealRecAcct = this.getDealRecAcct();
                  Object other$dealRecAcct = other.getDealRecAcct();
                  if (this$dealRecAcct == null) {
                     if (other$dealRecAcct == null) {
                        break label814;
                     }
                  } else if (this$dealRecAcct.equals(other$dealRecAcct)) {
                     break label814;
                  }

                  return false;
               }

               label807: {
                  Object this$tranBranch = this.getTranBranch();
                  Object other$tranBranch = other.getTranBranch();
                  if (this$tranBranch == null) {
                     if (other$tranBranch == null) {
                        break label807;
                     }
                  } else if (this$tranBranch.equals(other$tranBranch)) {
                     break label807;
                  }

                  return false;
               }

               Object this$crossDate = this.getCrossDate();
               Object other$crossDate = other.getCrossDate();
               if (this$crossDate == null) {
                  if (other$crossDate != null) {
                     return false;
                  }
               } else if (!this$crossDate.equals(other$crossDate)) {
                  return false;
               }

               label793: {
                  Object this$busiType = this.getBusiType();
                  Object other$busiType = other.getBusiType();
                  if (this$busiType == null) {
                     if (other$busiType == null) {
                        break label793;
                     }
                  } else if (this$busiType.equals(other$busiType)) {
                     break label793;
                  }

                  return false;
               }

               Object this$foreCcy = this.getForeCcy();
               Object other$foreCcy = other.getForeCcy();
               if (this$foreCcy == null) {
                  if (other$foreCcy != null) {
                     return false;
                  }
               } else if (!this$foreCcy.equals(other$foreCcy)) {
                  return false;
               }

               label779: {
                  Object this$crossTotalAmt = this.getCrossTotalAmt();
                  Object other$crossTotalAmt = other.getCrossTotalAmt();
                  if (this$crossTotalAmt == null) {
                     if (other$crossTotalAmt == null) {
                        break label779;
                     }
                  } else if (this$crossTotalAmt.equals(other$crossTotalAmt)) {
                     break label779;
                  }

                  return false;
               }

               Object this$crossAmt = this.getCrossAmt();
               Object other$crossAmt = other.getCrossAmt();
               if (this$crossAmt == null) {
                  if (other$crossAmt != null) {
                     return false;
                  }
               } else if (!this$crossAmt.equals(other$crossAmt)) {
                  return false;
               }

               Object this$noCrossAmt = this.getNoCrossAmt();
               Object other$noCrossAmt = other.getNoCrossAmt();
               if (this$noCrossAmt == null) {
                  if (other$noCrossAmt != null) {
                     return false;
                  }
               } else if (!this$noCrossAmt.equals(other$noCrossAmt)) {
                  return false;
               }

               label758: {
                  Object this$ourOffer = this.getOurOffer();
                  Object other$ourOffer = other.getOurOffer();
                  if (this$ourOffer == null) {
                     if (other$ourOffer == null) {
                        break label758;
                     }
                  } else if (this$ourOffer.equals(other$ourOffer)) {
                     break label758;
                  }

                  return false;
               }

               label751: {
                  Object this$compBankOffer = this.getCompBankOffer();
                  Object other$compBankOffer = other.getCompBankOffer();
                  if (this$compBankOffer == null) {
                     if (other$compBankOffer == null) {
                        break label751;
                     }
                  } else if (this$compBankOffer.equals(other$compBankOffer)) {
                     break label751;
                  }

                  return false;
               }

               Object this$compBankName = this.getCompBankName();
               Object other$compBankName = other.getCompBankName();
               if (this$compBankName == null) {
                  if (other$compBankName != null) {
                     return false;
                  }
               } else if (!this$compBankName.equals(other$compBankName)) {
                  return false;
               }

               Object this$crossCnyAmt = this.getCrossCnyAmt();
               Object other$crossCnyAmt = other.getCrossCnyAmt();
               if (this$crossCnyAmt == null) {
                  if (other$crossCnyAmt != null) {
                     return false;
                  }
               } else if (!this$crossCnyAmt.equals(other$crossCnyAmt)) {
                  return false;
               }

               label730: {
                  Object this$orgTranDate = this.getOrgTranDate();
                  Object other$orgTranDate = other.getOrgTranDate();
                  if (this$orgTranDate == null) {
                     if (other$orgTranDate == null) {
                        break label730;
                     }
                  } else if (this$orgTranDate.equals(other$orgTranDate)) {
                     break label730;
                  }

                  return false;
               }

               label723: {
                  Object this$sellTtFlag = this.getSellTtFlag();
                  Object other$sellTtFlag = other.getSellTtFlag();
                  if (this$sellTtFlag == null) {
                     if (other$sellTtFlag == null) {
                        break label723;
                     }
                  } else if (this$sellTtFlag.equals(other$sellTtFlag)) {
                     break label723;
                  }

                  return false;
               }

               Object this$orgTranCcy = this.getOrgTranCcy();
               Object other$orgTranCcy = other.getOrgTranCcy();
               if (this$orgTranCcy == null) {
                  if (other$orgTranCcy != null) {
                     return false;
                  }
               } else if (!this$orgTranCcy.equals(other$orgTranCcy)) {
                  return false;
               }

               Object this$orgTranAmt = this.getOrgTranAmt();
               Object other$orgTranAmt = other.getOrgTranAmt();
               if (this$orgTranAmt == null) {
                  if (other$orgTranAmt != null) {
                     return false;
                  }
               } else if (!this$orgTranAmt.equals(other$orgTranAmt)) {
                  return false;
               }

               label702: {
                  Object this$orgAppointOffer = this.getOrgAppointOffer();
                  Object other$orgAppointOffer = other.getOrgAppointOffer();
                  if (this$orgAppointOffer == null) {
                     if (other$orgAppointOffer == null) {
                        break label702;
                     }
                  } else if (this$orgAppointOffer.equals(other$orgAppointOffer)) {
                     break label702;
                  }

                  return false;
               }

               label695: {
                  Object this$orgCrossType = this.getOrgCrossType();
                  Object other$orgCrossType = other.getOrgCrossType();
                  if (this$orgCrossType == null) {
                     if (other$orgCrossType == null) {
                        break label695;
                     }
                  } else if (this$orgCrossType.equals(other$orgCrossType)) {
                     break label695;
                  }

                  return false;
               }

               Object this$orgAppointCrossDate = this.getOrgAppointCrossDate();
               Object other$orgAppointCrossDate = other.getOrgAppointCrossDate();
               if (this$orgAppointCrossDate == null) {
                  if (other$orgAppointCrossDate != null) {
                     return false;
                  }
               } else if (!this$orgAppointCrossDate.equals(other$orgAppointCrossDate)) {
                  return false;
               }

               label681: {
                  Object this$preCrossOffer = this.getPreCrossOffer();
                  Object other$preCrossOffer = other.getPreCrossOffer();
                  if (this$preCrossOffer == null) {
                     if (other$preCrossOffer == null) {
                        break label681;
                     }
                  } else if (this$preCrossOffer.equals(other$preCrossOffer)) {
                     break label681;
                  }

                  return false;
               }

               Object this$farRate = this.getFarRate();
               Object other$farRate = other.getFarRate();
               if (this$farRate == null) {
                  if (other$farRate != null) {
                     return false;
                  }
               } else if (!this$farRate.equals(other$farRate)) {
                  return false;
               }

               label667: {
                  Object this$preCrossDate = this.getPreCrossDate();
                  Object other$preCrossDate = other.getPreCrossDate();
                  if (this$preCrossDate == null) {
                     if (other$preCrossDate == null) {
                        break label667;
                     }
                  } else if (this$preCrossDate.equals(other$preCrossDate)) {
                     break label667;
                  }

                  return false;
               }

               Object this$lossTranAmt = this.getLossTranAmt();
               Object other$lossTranAmt = other.getLossTranAmt();
               if (this$lossTranAmt == null) {
                  if (other$lossTranAmt != null) {
                     return false;
                  }
               } else if (!this$lossTranAmt.equals(other$lossTranAmt)) {
                  return false;
               }

               Object this$surplusAmt = this.getSurplusAmt();
               Object other$surplusAmt = other.getSurplusAmt();
               if (this$surplusAmt == null) {
                  if (other$surplusAmt != null) {
                     return false;
                  }
               } else if (!this$surplusAmt.equals(other$surplusAmt)) {
                  return false;
               }

               label646: {
                  Object this$lossAmt = this.getLossAmt();
                  Object other$lossAmt = other.getLossAmt();
                  if (this$lossAmt == null) {
                     if (other$lossAmt == null) {
                        break label646;
                     }
                  } else if (this$lossAmt.equals(other$lossAmt)) {
                     break label646;
                  }

                  return false;
               }

               label639: {
                  Object this$renewTranDate = this.getRenewTranDate();
                  Object other$renewTranDate = other.getRenewTranDate();
                  if (this$renewTranDate == null) {
                     if (other$renewTranDate == null) {
                        break label639;
                     }
                  } else if (this$renewTranDate.equals(other$renewTranDate)) {
                     break label639;
                  }

                  return false;
               }

               Object this$nearRate = this.getNearRate();
               Object other$nearRate = other.getNearRate();
               if (this$nearRate == null) {
                  if (other$nearRate != null) {
                     return false;
                  }
               } else if (!this$nearRate.equals(other$nearRate)) {
                  return false;
               }

               Object this$renewAppointOffer = this.getRenewAppointOffer();
               Object other$renewAppointOffer = other.getRenewAppointOffer();
               if (this$renewAppointOffer == null) {
                  if (other$renewAppointOffer != null) {
                     return false;
                  }
               } else if (!this$renewAppointOffer.equals(other$renewAppointOffer)) {
                  return false;
               }

               label618: {
                  Object this$renewSelectCrossDay = this.getRenewSelectCrossDay();
                  Object other$renewSelectCrossDay = other.getRenewSelectCrossDay();
                  if (this$renewSelectCrossDay == null) {
                     if (other$renewSelectCrossDay == null) {
                        break label618;
                     }
                  } else if (this$renewSelectCrossDay.equals(other$renewSelectCrossDay)) {
                     break label618;
                  }

                  return false;
               }

               label611: {
                  Object this$holdLimitCcy = this.getHoldLimitCcy();
                  Object other$holdLimitCcy = other.getHoldLimitCcy();
                  if (this$holdLimitCcy == null) {
                     if (other$holdLimitCcy == null) {
                        break label611;
                     }
                  } else if (this$holdLimitCcy.equals(other$holdLimitCcy)) {
                     break label611;
                  }

                  return false;
               }

               Object this$holdLimitAmt = this.getHoldLimitAmt();
               Object other$holdLimitAmt = other.getHoldLimitAmt();
               if (this$holdLimitAmt == null) {
                  if (other$holdLimitAmt != null) {
                     return false;
                  }
               } else if (!this$holdLimitAmt.equals(other$holdLimitAmt)) {
                  return false;
               }

               Object this$bondAmt = this.getBondAmt();
               Object other$bondAmt = other.getBondAmt();
               if (this$bondAmt == null) {
                  if (other$bondAmt != null) {
                     return false;
                  }
               } else if (!this$bondAmt.equals(other$bondAmt)) {
                  return false;
               }

               label590: {
                  Object this$preAmt = this.getPreAmt();
                  Object other$preAmt = other.getPreAmt();
                  if (this$preAmt == null) {
                     if (other$preAmt == null) {
                        break label590;
                     }
                  } else if (this$preAmt.equals(other$preAmt)) {
                     break label590;
                  }

                  return false;
               }

               label583: {
                  Object this$preOffer = this.getPreOffer();
                  Object other$preOffer = other.getPreOffer();
                  if (this$preOffer == null) {
                     if (other$preOffer == null) {
                        break label583;
                     }
                  } else if (this$preOffer.equals(other$preOffer)) {
                     break label583;
                  }

                  return false;
               }

               Object this$preTranDate = this.getPreTranDate();
               Object other$preTranDate = other.getPreTranDate();
               if (this$preTranDate == null) {
                  if (other$preTranDate != null) {
                     return false;
                  }
               } else if (!this$preTranDate.equals(other$preTranDate)) {
                  return false;
               }

               label569: {
                  Object this$clearAcctCcy = this.getClearAcctCcy();
                  Object other$clearAcctCcy = other.getClearAcctCcy();
                  if (this$clearAcctCcy == null) {
                     if (other$clearAcctCcy == null) {
                        break label569;
                     }
                  } else if (this$clearAcctCcy.equals(other$clearAcctCcy)) {
                     break label569;
                  }

                  return false;
               }

               Object this$remark = this.getRemark();
               Object other$remark = other.getRemark();
               if (this$remark == null) {
                  if (other$remark != null) {
                     return false;
                  }
               } else if (!this$remark.equals(other$remark)) {
                  return false;
               }

               label555: {
                  Object this$toClearAcctNoCny = this.getToClearAcctNoCny();
                  Object other$toClearAcctNoCny = other.getToClearAcctNoCny();
                  if (this$toClearAcctNoCny == null) {
                     if (other$toClearAcctNoCny == null) {
                        break label555;
                     }
                  } else if (this$toClearAcctNoCny.equals(other$toClearAcctNoCny)) {
                     break label555;
                  }

                  return false;
               }

               Object this$toClearCnySeqNo = this.getToClearCnySeqNo();
               Object other$toClearCnySeqNo = other.getToClearCnySeqNo();
               if (this$toClearCnySeqNo == null) {
                  if (other$toClearCnySeqNo != null) {
                     return false;
                  }
               } else if (!this$toClearCnySeqNo.equals(other$toClearCnySeqNo)) {
                  return false;
               }

               Object this$toClearAcctNoFore = this.getToClearAcctNoFore();
               Object other$toClearAcctNoFore = other.getToClearAcctNoFore();
               if (this$toClearAcctNoFore == null) {
                  if (other$toClearAcctNoFore != null) {
                     return false;
                  }
               } else if (!this$toClearAcctNoFore.equals(other$toClearAcctNoFore)) {
                  return false;
               }

               label534: {
                  Object this$toClearForeSeqNo = this.getToClearForeSeqNo();
                  Object other$toClearForeSeqNo = other.getToClearForeSeqNo();
                  if (this$toClearForeSeqNo == null) {
                     if (other$toClearForeSeqNo == null) {
                        break label534;
                     }
                  } else if (this$toClearForeSeqNo.equals(other$toClearForeSeqNo)) {
                     break label534;
                  }

                  return false;
               }

               label527: {
                  Object this$clearAcctNo = this.getClearAcctNo();
                  Object other$clearAcctNo = other.getClearAcctNo();
                  if (this$clearAcctNo == null) {
                     if (other$clearAcctNo == null) {
                        break label527;
                     }
                  } else if (this$clearAcctNo.equals(other$clearAcctNo)) {
                     break label527;
                  }

                  return false;
               }

               Object this$clearAcctSeqNo = this.getClearAcctSeqNo();
               Object other$clearAcctSeqNo = other.getClearAcctSeqNo();
               if (this$clearAcctSeqNo == null) {
                  if (other$clearAcctSeqNo != null) {
                     return false;
                  }
               } else if (!this$clearAcctSeqNo.equals(other$clearAcctSeqNo)) {
                  return false;
               }

               Object this$deductBaseAcctNo = this.getDeductBaseAcctNo();
               Object other$deductBaseAcctNo = other.getDeductBaseAcctNo();
               if (this$deductBaseAcctNo == null) {
                  if (other$deductBaseAcctNo != null) {
                     return false;
                  }
               } else if (!this$deductBaseAcctNo.equals(other$deductBaseAcctNo)) {
                  return false;
               }

               label506: {
                  Object this$deductProdType = this.getDeductProdType();
                  Object other$deductProdType = other.getDeductProdType();
                  if (this$deductProdType == null) {
                     if (other$deductProdType == null) {
                        break label506;
                     }
                  } else if (this$deductProdType.equals(other$deductProdType)) {
                     break label506;
                  }

                  return false;
               }

               label499: {
                  Object this$deductCcy = this.getDeductCcy();
                  Object other$deductCcy = other.getDeductCcy();
                  if (this$deductCcy == null) {
                     if (other$deductCcy == null) {
                        break label499;
                     }
                  } else if (this$deductCcy.equals(other$deductCcy)) {
                     break label499;
                  }

                  return false;
               }

               Object this$deductSeqNo = this.getDeductSeqNo();
               Object other$deductSeqNo = other.getDeductSeqNo();
               if (this$deductSeqNo == null) {
                  if (other$deductSeqNo != null) {
                     return false;
                  }
               } else if (!this$deductSeqNo.equals(other$deductSeqNo)) {
                  return false;
               }

               Object this$cnyAcctNo = this.getCnyAcctNo();
               Object other$cnyAcctNo = other.getCnyAcctNo();
               if (this$cnyAcctNo == null) {
                  if (other$cnyAcctNo != null) {
                     return false;
                  }
               } else if (!this$cnyAcctNo.equals(other$cnyAcctNo)) {
                  return false;
               }

               label478: {
                  Object this$bondAcctSeqNo = this.getBondAcctSeqNo();
                  Object other$bondAcctSeqNo = other.getBondAcctSeqNo();
                  if (this$bondAcctSeqNo == null) {
                     if (other$bondAcctSeqNo == null) {
                        break label478;
                     }
                  } else if (this$bondAcctSeqNo.equals(other$bondAcctSeqNo)) {
                     break label478;
                  }

                  return false;
               }

               label471: {
                  Object this$depBaseAcctNo = this.getDepBaseAcctNo();
                  Object other$depBaseAcctNo = other.getDepBaseAcctNo();
                  if (this$depBaseAcctNo == null) {
                     if (other$depBaseAcctNo == null) {
                        break label471;
                     }
                  } else if (this$depBaseAcctNo.equals(other$depBaseAcctNo)) {
                     break label471;
                  }

                  return false;
               }

               Object this$depAmt = this.getDepAmt();
               Object other$depAmt = other.getDepAmt();
               if (this$depAmt == null) {
                  if (other$depAmt != null) {
                     return false;
                  }
               } else if (!this$depAmt.equals(other$depAmt)) {
                  return false;
               }

               label457: {
                  Object this$depCcy = this.getDepCcy();
                  Object other$depCcy = other.getDepCcy();
                  if (this$depCcy == null) {
                     if (other$depCcy == null) {
                        break label457;
                     }
                  } else if (this$depCcy.equals(other$depCcy)) {
                     break label457;
                  }

                  return false;
               }

               Object this$deferDealType = this.getDeferDealType();
               Object other$deferDealType = other.getDeferDealType();
               if (this$deferDealType == null) {
                  if (other$deferDealType != null) {
                     return false;
                  }
               } else if (!this$deferDealType.equals(other$deferDealType)) {
                  return false;
               }

               label443: {
                  Object this$depAcctSeqNo = this.getDepAcctSeqNo();
                  Object other$depAcctSeqNo = other.getDepAcctSeqNo();
                  if (this$depAcctSeqNo == null) {
                     if (other$depAcctSeqNo == null) {
                        break label443;
                     }
                  } else if (this$depAcctSeqNo.equals(other$depAcctSeqNo)) {
                     break label443;
                  }

                  return false;
               }

               Object this$foreBaseAcctNo = this.getForeBaseAcctNo();
               Object other$foreBaseAcctNo = other.getForeBaseAcctNo();
               if (this$foreBaseAcctNo == null) {
                  if (other$foreBaseAcctNo != null) {
                     return false;
                  }
               } else if (!this$foreBaseAcctNo.equals(other$foreBaseAcctNo)) {
                  return false;
               }

               Object this$deferBusiNo = this.getDeferBusiNo();
               Object other$deferBusiNo = other.getDeferBusiNo();
               if (this$deferBusiNo == null) {
                  if (other$deferBusiNo != null) {
                     return false;
                  }
               } else if (!this$deferBusiNo.equals(other$deferBusiNo)) {
                  return false;
               }

               label422: {
                  Object this$occAmt = this.getOccAmt();
                  Object other$occAmt = other.getOccAmt();
                  if (this$occAmt == null) {
                     if (other$occAmt == null) {
                        break label422;
                     }
                  } else if (this$occAmt.equals(other$occAmt)) {
                     break label422;
                  }

                  return false;
               }

               label415: {
                  Object this$deferFixDate = this.getDeferFixDate();
                  Object other$deferFixDate = other.getDeferFixDate();
                  if (this$deferFixDate == null) {
                     if (other$deferFixDate == null) {
                        break label415;
                     }
                  } else if (this$deferFixDate.equals(other$deferFixDate)) {
                     break label415;
                  }

                  return false;
               }

               Object this$occCcy = this.getOccCcy();
               Object other$occCcy = other.getOccCcy();
               if (this$occCcy == null) {
                  if (other$occCcy != null) {
                     return false;
                  }
               } else if (!this$occCcy.equals(other$occCcy)) {
                  return false;
               }

               Object this$deferChangeDate = this.getDeferChangeDate();
               Object other$deferChangeDate = other.getDeferChangeDate();
               if (this$deferChangeDate == null) {
                  if (other$deferChangeDate != null) {
                     return false;
                  }
               } else if (!this$deferChangeDate.equals(other$deferChangeDate)) {
                  return false;
               }

               label394: {
                  Object this$deferChangeDateEnd = this.getDeferChangeDateEnd();
                  Object other$deferChangeDateEnd = other.getDeferChangeDateEnd();
                  if (this$deferChangeDateEnd == null) {
                     if (other$deferChangeDateEnd == null) {
                        break label394;
                     }
                  } else if (this$deferChangeDateEnd.equals(other$deferChangeDateEnd)) {
                     break label394;
                  }

                  return false;
               }

               label387: {
                  Object this$deferTranAmt = this.getDeferTranAmt();
                  Object other$deferTranAmt = other.getDeferTranAmt();
                  if (this$deferTranAmt == null) {
                     if (other$deferTranAmt == null) {
                        break label387;
                     }
                  } else if (this$deferTranAmt.equals(other$deferTranAmt)) {
                     break label387;
                  }

                  return false;
               }

               Object this$deferCcy = this.getDeferCcy();
               Object other$deferCcy = other.getDeferCcy();
               if (this$deferCcy == null) {
                  if (other$deferCcy != null) {
                     return false;
                  }
               } else if (!this$deferCcy.equals(other$deferCcy)) {
                  return false;
               }

               Object this$deferRate = this.getDeferRate();
               Object other$deferRate = other.getDeferRate();
               if (this$deferRate == null) {
                  if (other$deferRate != null) {
                     return false;
                  }
               } else if (!this$deferRate.equals(other$deferRate)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1200061081In.Body;
      }
      public String toString() {
         return "Core1200061081In.Body(option=" + this.getOption() + ", crossType=" + this.getCrossType() + ", exType=" + this.getExType() + ", clientNo=" + this.getClientNo() + ", busiNo=" + this.getBusiNo() + ", tranDate=" + this.getTranDate() + ", baseAcctNo=" + this.getBaseAcctNo() + ", dealRecAcct=" + this.getDealRecAcct() + ", tranBranch=" + this.getTranBranch() + ", crossDate=" + this.getCrossDate() + ", busiType=" + this.getBusiType() + ", foreCcy=" + this.getForeCcy() + ", crossTotalAmt=" + this.getCrossTotalAmt() + ", crossAmt=" + this.getCrossAmt() + ", noCrossAmt=" + this.getNoCrossAmt() + ", ourOffer=" + this.getOurOffer() + ", compBankOffer=" + this.getCompBankOffer() + ", compBankName=" + this.getCompBankName() + ", crossCnyAmt=" + this.getCrossCnyAmt() + ", orgTranDate=" + this.getOrgTranDate() + ", sellTtFlag=" + this.getSellTtFlag() + ", orgTranCcy=" + this.getOrgTranCcy() + ", orgTranAmt=" + this.getOrgTranAmt() + ", orgAppointOffer=" + this.getOrgAppointOffer() + ", orgCrossType=" + this.getOrgCrossType() + ", orgAppointCrossDate=" + this.getOrgAppointCrossDate() + ", preCrossOffer=" + this.getPreCrossOffer() + ", farRate=" + this.getFarRate() + ", preCrossDate=" + this.getPreCrossDate() + ", lossTranAmt=" + this.getLossTranAmt() + ", surplusAmt=" + this.getSurplusAmt() + ", lossAmt=" + this.getLossAmt() + ", renewTranDate=" + this.getRenewTranDate() + ", nearRate=" + this.getNearRate() + ", renewAppointOffer=" + this.getRenewAppointOffer() + ", renewSelectCrossDay=" + this.getRenewSelectCrossDay() + ", holdLimitCcy=" + this.getHoldLimitCcy() + ", holdLimitAmt=" + this.getHoldLimitAmt() + ", bondAmt=" + this.getBondAmt() + ", preAmt=" + this.getPreAmt() + ", preOffer=" + this.getPreOffer() + ", preTranDate=" + this.getPreTranDate() + ", clearAcctCcy=" + this.getClearAcctCcy() + ", remark=" + this.getRemark() + ", toClearAcctNoCny=" + this.getToClearAcctNoCny() + ", toClearCnySeqNo=" + this.getToClearCnySeqNo() + ", toClearAcctNoFore=" + this.getToClearAcctNoFore() + ", toClearForeSeqNo=" + this.getToClearForeSeqNo() + ", clearAcctNo=" + this.getClearAcctNo() + ", clearAcctSeqNo=" + this.getClearAcctSeqNo() + ", deductBaseAcctNo=" + this.getDeductBaseAcctNo() + ", deductProdType=" + this.getDeductProdType() + ", deductCcy=" + this.getDeductCcy() + ", deductSeqNo=" + this.getDeductSeqNo() + ", cnyAcctNo=" + this.getCnyAcctNo() + ", bondAcctSeqNo=" + this.getBondAcctSeqNo() + ", depBaseAcctNo=" + this.getDepBaseAcctNo() + ", depAmt=" + this.getDepAmt() + ", depCcy=" + this.getDepCcy() + ", deferDealType=" + this.getDeferDealType() + ", depAcctSeqNo=" + this.getDepAcctSeqNo() + ", foreBaseAcctNo=" + this.getForeBaseAcctNo() + ", deferBusiNo=" + this.getDeferBusiNo() + ", occAmt=" + this.getOccAmt() + ", deferFixDate=" + this.getDeferFixDate() + ", occCcy=" + this.getOccCcy() + ", deferChangeDate=" + this.getDeferChangeDate() + ", deferChangeDateEnd=" + this.getDeferChangeDateEnd() + ", deferTranAmt=" + this.getDeferTranAmt() + ", deferCcy=" + this.getDeferCcy() + ", deferRate=" + this.getDeferRate() + ")";
      }
   }
}
