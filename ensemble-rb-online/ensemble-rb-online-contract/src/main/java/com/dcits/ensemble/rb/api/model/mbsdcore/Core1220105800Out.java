package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;

@MessageOut
public class Core1220105800Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "批次号",
      notNull = false,
      length = "50",
      remark = "批次号",
      maxSize = 50
   )
   private String batchNo;
   @V(
      desc = "文件路径",
      notNull = false,
      length = "200",
      remark = "文件路径",
      maxSize = 200
   )
   private String filePath;

   public String getBatchNo() {
      return this.batchNo;
   }

   public String getFilePath() {
      return this.filePath;
   }

   public void setBatchNo(String batchNo) {
      this.batchNo = batchNo;
   }

   public void setFilePath(String filePath) {
      this.filePath = filePath;
   }

   public String toString() {
      return "Core1220105800Out(batchNo=" + this.getBatchNo() + ", filePath=" + this.getFilePath() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1220105800Out)) {
         return false;
      } else {
         Core1220105800Out other = (Core1220105800Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$batchNo = this.getBatchNo();
            Object other$batchNo = other.getBatchNo();
            if (this$batchNo == null) {
               if (other$batchNo != null) {
                  return false;
               }
            } else if (!this$batchNo.equals(other$batchNo)) {
               return false;
            }

            Object this$filePath = this.getFilePath();
            Object other$filePath = other.getFilePath();
            if (this$filePath == null) {
               if (other$filePath != null) {
                  return false;
               }
            } else if (!this$filePath.equals(other$filePath)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1220105800Out;
   }
}
