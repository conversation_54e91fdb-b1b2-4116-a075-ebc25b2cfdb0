package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12200117In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12200117Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12200117 {
   String URL = "/rb/file/batch/acctupdate";


   @ApiRemark("批量账户信息维护")
   @ApiDesc("批量账户信息维护")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1220",
      messageCode = "0117"
   )
   @BusinessCategory("1220-文件")
   @FunctionCategory("RB02-账户管理")
   @ConsumeSys("137")
   @ApiUseStatus("PRODUCT-产品")
   Core12200117Out runService(Core12200117In var1);
}
