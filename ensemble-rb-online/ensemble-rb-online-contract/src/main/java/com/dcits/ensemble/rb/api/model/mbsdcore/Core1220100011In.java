package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1220100011In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1220100011In.Body body;

   public Core1220100011In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1220100011In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1220100011In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1220100011In)) {
         return false;
      } else {
         Core1220100011In other = (Core1220100011In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1220100011In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "交易起始日期",
         notNull = false,
         remark = "交易起始日期"
      )
      private String tranStartDate;
      @V(
         desc = "交易结束日期",
         notNull = false,
         remark = "交易结束日期"
      )
      private String tranEndDate;
      @V(
         desc = "客户类型",
         notNull = false,
         length = "3",
         remark = "客户大类，目前一般分为个人，公司，金融机构和内部客户。取之于CIF_CLIENT_TYPE.CLIENT_TYPE",
         maxSize = 3
      )
      private String clientType;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "买入币种",
         notNull = false,
         length = "3",
         remark = "买入币种",
         maxSize = 3
      )
      private String buyCcy;
      @V(
         desc = "卖出币种",
         notNull = false,
         length = "3",
         remark = "卖出币种",
         maxSize = 3
      )
      private String sellCcy;
      @V(
         desc = "所属机构号",
         notNull = false,
         length = "50",
         remark = "所属机构号",
         maxSize = 50
      )
      private String branch;

      public String getTranStartDate() {
         return this.tranStartDate;
      }

      public String getTranEndDate() {
         return this.tranEndDate;
      }

      public String getClientType() {
         return this.clientType;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public String getBuyCcy() {
         return this.buyCcy;
      }

      public String getSellCcy() {
         return this.sellCcy;
      }

      public String getBranch() {
         return this.branch;
      }

      public void setTranStartDate(String tranStartDate) {
         this.tranStartDate = tranStartDate;
      }

      public void setTranEndDate(String tranEndDate) {
         this.tranEndDate = tranEndDate;
      }

      public void setClientType(String clientType) {
         this.clientType = clientType;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setBuyCcy(String buyCcy) {
         this.buyCcy = buyCcy;
      }

      public void setSellCcy(String sellCcy) {
         this.sellCcy = sellCcy;
      }

      public void setBranch(String branch) {
         this.branch = branch;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1220100011In.Body)) {
            return false;
         } else {
            Core1220100011In.Body other = (Core1220100011In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label143: {
                  Object this$tranStartDate = this.getTranStartDate();
                  Object other$tranStartDate = other.getTranStartDate();
                  if (this$tranStartDate == null) {
                     if (other$tranStartDate == null) {
                        break label143;
                     }
                  } else if (this$tranStartDate.equals(other$tranStartDate)) {
                     break label143;
                  }

                  return false;
               }

               Object this$tranEndDate = this.getTranEndDate();
               Object other$tranEndDate = other.getTranEndDate();
               if (this$tranEndDate == null) {
                  if (other$tranEndDate != null) {
                     return false;
                  }
               } else if (!this$tranEndDate.equals(other$tranEndDate)) {
                  return false;
               }

               Object this$clientType = this.getClientType();
               Object other$clientType = other.getClientType();
               if (this$clientType == null) {
                  if (other$clientType != null) {
                     return false;
                  }
               } else if (!this$clientType.equals(other$clientType)) {
                  return false;
               }

               label122: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label122;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label122;
                  }

                  return false;
               }

               label115: {
                  Object this$acctCcy = this.getAcctCcy();
                  Object other$acctCcy = other.getAcctCcy();
                  if (this$acctCcy == null) {
                     if (other$acctCcy == null) {
                        break label115;
                     }
                  } else if (this$acctCcy.equals(other$acctCcy)) {
                     break label115;
                  }

                  return false;
               }

               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo != null) {
                     return false;
                  }
               } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               label94: {
                  Object this$clientNo = this.getClientNo();
                  Object other$clientNo = other.getClientNo();
                  if (this$clientNo == null) {
                     if (other$clientNo == null) {
                        break label94;
                     }
                  } else if (this$clientNo.equals(other$clientNo)) {
                     break label94;
                  }

                  return false;
               }

               label87: {
                  Object this$buyCcy = this.getBuyCcy();
                  Object other$buyCcy = other.getBuyCcy();
                  if (this$buyCcy == null) {
                     if (other$buyCcy == null) {
                        break label87;
                     }
                  } else if (this$buyCcy.equals(other$buyCcy)) {
                     break label87;
                  }

                  return false;
               }

               Object this$sellCcy = this.getSellCcy();
               Object other$sellCcy = other.getSellCcy();
               if (this$sellCcy == null) {
                  if (other$sellCcy != null) {
                     return false;
                  }
               } else if (!this$sellCcy.equals(other$sellCcy)) {
                  return false;
               }

               Object this$branch = this.getBranch();
               Object other$branch = other.getBranch();
               if (this$branch == null) {
                  if (other$branch != null) {
                     return false;
                  }
               } else if (!this$branch.equals(other$branch)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1220100011In.Body;
      }
      public String toString() {
         return "Core1220100011In.Body(tranStartDate=" + this.getTranStartDate() + ", tranEndDate=" + this.getTranEndDate() + ", clientType=" + this.getClientType() + ", baseAcctNo=" + this.getBaseAcctNo() + ", acctCcy=" + this.getAcctCcy() + ", acctSeqNo=" + this.getAcctSeqNo() + ", prodType=" + this.getProdType() + ", clientNo=" + this.getClientNo() + ", buyCcy=" + this.getBuyCcy() + ", sellCcy=" + this.getSellCcy() + ", branch=" + this.getBranch() + ")";
      }
   }
}
