package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400109527Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "真实交易对手数组",
      notNull = false,
      remark = "真实交易对手数组"
   )
   private List<Core1400109527Out.OthRealArray> othRealArray;

   public List<Core1400109527Out.OthRealArray> getOthRealArray() {
      return this.othRealArray;
   }

   public void setOthRealArray(List<Core1400109527Out.OthRealArray> othRealArray) {
      this.othRealArray = othRealArray;
   }

   public String toString() {
      return "Core1400109527Out(othRealArray=" + this.getOthRealArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400109527Out)) {
         return false;
      } else {
         Core1400109527Out other = (Core1400109527Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$othRealArray = this.getOthRealArray();
            Object other$othRealArray = other.getOthRealArray();
            if (this$othRealArray == null) {
               if (other$othRealArray != null) {
                  return false;
               }
            } else if (!this$othRealArray.equals(other$othRealArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400109527Out;
   }
   public static class OthRealArray {
      @V(
         desc = "源模块",
         notNull = false,
         length = "3",
         remark = "源模块",
         maxSize = 3
      )
      private String sourceModule;
      @V(
         desc = "序号",
         notNull = false,
         length = "50",
         remark = "序号",
         maxSize = 50
      )
      private String seqNo;
      @V(
         desc = "真实交易对手账号",
         notNull = false,
         length = "50",
         remark = "真实交易对手账号",
         maxSize = 50
      )
      private String othRealBaseAcctNo;
      @V(
         desc = "真实对方账户序列号",
         notNull = false,
         length = "5",
         remark = "真实对方账户序列号",
         maxSize = 5
      )
      private String othRealAcctSeqNo;
      @V(
         desc = "真实交易对手名称",
         notNull = false,
         length = "200",
         remark = "真实交易对手名称",
         maxSize = 200
      )
      private String othRealTranName;
      @V(
         desc = "他行行号",
         notNull = false,
         length = "20",
         remark = "他行行号",
         maxSize = 20
      )
      private String contraBankCode;
      @V(
         desc = "补录子序号",
         notNull = false,
         length = "5",
         remark = "补录子序号"
      )
      private Integer registerSeqNo;
      @V(
         desc = "交易金额",
         notNull = false,
         length = "17",
         remark = "交易金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal tranAmt;

      public String getSourceModule() {
         return this.sourceModule;
      }

      public String getSeqNo() {
         return this.seqNo;
      }

      public String getOthRealBaseAcctNo() {
         return this.othRealBaseAcctNo;
      }

      public String getOthRealAcctSeqNo() {
         return this.othRealAcctSeqNo;
      }

      public String getOthRealTranName() {
         return this.othRealTranName;
      }

      public String getContraBankCode() {
         return this.contraBankCode;
      }

      public Integer getRegisterSeqNo() {
         return this.registerSeqNo;
      }

      public BigDecimal getTranAmt() {
         return this.tranAmt;
      }

      public void setSourceModule(String sourceModule) {
         this.sourceModule = sourceModule;
      }

      public void setSeqNo(String seqNo) {
         this.seqNo = seqNo;
      }

      public void setOthRealBaseAcctNo(String othRealBaseAcctNo) {
         this.othRealBaseAcctNo = othRealBaseAcctNo;
      }

      public void setOthRealAcctSeqNo(String othRealAcctSeqNo) {
         this.othRealAcctSeqNo = othRealAcctSeqNo;
      }

      public void setOthRealTranName(String othRealTranName) {
         this.othRealTranName = othRealTranName;
      }

      public void setContraBankCode(String contraBankCode) {
         this.contraBankCode = contraBankCode;
      }

      public void setRegisterSeqNo(Integer registerSeqNo) {
         this.registerSeqNo = registerSeqNo;
      }

      public void setTranAmt(BigDecimal tranAmt) {
         this.tranAmt = tranAmt;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400109527Out.OthRealArray)) {
            return false;
         } else {
            Core1400109527Out.OthRealArray other = (Core1400109527Out.OthRealArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label107: {
                  Object this$sourceModule = this.getSourceModule();
                  Object other$sourceModule = other.getSourceModule();
                  if (this$sourceModule == null) {
                     if (other$sourceModule == null) {
                        break label107;
                     }
                  } else if (this$sourceModule.equals(other$sourceModule)) {
                     break label107;
                  }

                  return false;
               }

               Object this$seqNo = this.getSeqNo();
               Object other$seqNo = other.getSeqNo();
               if (this$seqNo == null) {
                  if (other$seqNo != null) {
                     return false;
                  }
               } else if (!this$seqNo.equals(other$seqNo)) {
                  return false;
               }

               Object this$othRealBaseAcctNo = this.getOthRealBaseAcctNo();
               Object other$othRealBaseAcctNo = other.getOthRealBaseAcctNo();
               if (this$othRealBaseAcctNo == null) {
                  if (other$othRealBaseAcctNo != null) {
                     return false;
                  }
               } else if (!this$othRealBaseAcctNo.equals(other$othRealBaseAcctNo)) {
                  return false;
               }

               label86: {
                  Object this$othRealAcctSeqNo = this.getOthRealAcctSeqNo();
                  Object other$othRealAcctSeqNo = other.getOthRealAcctSeqNo();
                  if (this$othRealAcctSeqNo == null) {
                     if (other$othRealAcctSeqNo == null) {
                        break label86;
                     }
                  } else if (this$othRealAcctSeqNo.equals(other$othRealAcctSeqNo)) {
                     break label86;
                  }

                  return false;
               }

               label79: {
                  Object this$othRealTranName = this.getOthRealTranName();
                  Object other$othRealTranName = other.getOthRealTranName();
                  if (this$othRealTranName == null) {
                     if (other$othRealTranName == null) {
                        break label79;
                     }
                  } else if (this$othRealTranName.equals(other$othRealTranName)) {
                     break label79;
                  }

                  return false;
               }

               label72: {
                  Object this$contraBankCode = this.getContraBankCode();
                  Object other$contraBankCode = other.getContraBankCode();
                  if (this$contraBankCode == null) {
                     if (other$contraBankCode == null) {
                        break label72;
                     }
                  } else if (this$contraBankCode.equals(other$contraBankCode)) {
                     break label72;
                  }

                  return false;
               }

               Object this$registerSeqNo = this.getRegisterSeqNo();
               Object other$registerSeqNo = other.getRegisterSeqNo();
               if (this$registerSeqNo == null) {
                  if (other$registerSeqNo != null) {
                     return false;
                  }
               } else if (!this$registerSeqNo.equals(other$registerSeqNo)) {
                  return false;
               }

               Object this$tranAmt = this.getTranAmt();
               Object other$tranAmt = other.getTranAmt();
               if (this$tranAmt == null) {
                  if (other$tranAmt != null) {
                     return false;
                  }
               } else if (!this$tranAmt.equals(other$tranAmt)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400109527Out.OthRealArray;
      }
      public String toString() {
         return "Core1400109527Out.OthRealArray(sourceModule=" + this.getSourceModule() + ", seqNo=" + this.getSeqNo() + ", othRealBaseAcctNo=" + this.getOthRealBaseAcctNo() + ", othRealAcctSeqNo=" + this.getOthRealAcctSeqNo() + ", othRealTranName=" + this.getOthRealTranName() + ", contraBankCode=" + this.getContraBankCode() + ", registerSeqNo=" + this.getRegisterSeqNo() + ", tranAmt=" + this.getTranAmt() + ")";
      }
   }
}
