package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;

@MessageOut
public class Core1200023403Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "挂失编号",
      notNull = false,
      length = "50",
      remark = "挂失编号",
      maxSize = 50
   )
   private String lostNo;
   @V(
      desc = "交易参考号",
      notNull = false,
      length = "50",
      remark = "交易参考号",
      maxSize = 50
   )
   private String reference;
   @V(
      desc = "挂账序列号",
      notNull = false,
      length = "50",
      remark = "挂账账户序列号",
      maxSize = 50
   )
   private String hangSeqNo;
   @V(
      desc = "挂失申请书编号",
      notNull = false,
      length = "50",
      remark = "挂失申请书编号",
      maxSize = 50
   )
   private String lossNo;

   public String getLostNo() {
      return this.lostNo;
   }

   public String getReference() {
      return this.reference;
   }

   public String getHangSeqNo() {
      return this.hangSeqNo;
   }

   public String getLossNo() {
      return this.lossNo;
   }

   public void setLostNo(String lostNo) {
      this.lostNo = lostNo;
   }

   public void setReference(String reference) {
      this.reference = reference;
   }

   public void setHangSeqNo(String hangSeqNo) {
      this.hangSeqNo = hangSeqNo;
   }

   public void setLossNo(String lossNo) {
      this.lossNo = lossNo;
   }

   public String toString() {
      return "Core1200023403Out(lostNo=" + this.getLostNo() + ", reference=" + this.getReference() + ", hangSeqNo=" + this.getHangSeqNo() + ", lossNo=" + this.getLossNo() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200023403Out)) {
         return false;
      } else {
         Core1200023403Out other = (Core1200023403Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            label61: {
               Object this$lostNo = this.getLostNo();
               Object other$lostNo = other.getLostNo();
               if (this$lostNo == null) {
                  if (other$lostNo == null) {
                     break label61;
                  }
               } else if (this$lostNo.equals(other$lostNo)) {
                  break label61;
               }

               return false;
            }

            label54: {
               Object this$reference = this.getReference();
               Object other$reference = other.getReference();
               if (this$reference == null) {
                  if (other$reference == null) {
                     break label54;
                  }
               } else if (this$reference.equals(other$reference)) {
                  break label54;
               }

               return false;
            }

            Object this$hangSeqNo = this.getHangSeqNo();
            Object other$hangSeqNo = other.getHangSeqNo();
            if (this$hangSeqNo == null) {
               if (other$hangSeqNo != null) {
                  return false;
               }
            } else if (!this$hangSeqNo.equals(other$hangSeqNo)) {
               return false;
            }

            Object this$lossNo = this.getLossNo();
            Object other$lossNo = other.getLossNo();
            if (this$lossNo == null) {
               if (other$lossNo != null) {
                  return false;
               }
            } else if (!this$lossNo.equals(other$lossNo)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200023403Out;
   }
}
