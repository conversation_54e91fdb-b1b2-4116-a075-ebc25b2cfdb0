package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000708In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000708Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12000708 {
   String URL = "/rb/nfin/pcp/agreement/deal";


   @ApiDesc("提供资金池基本账户组建立,签约统一处理，签约信息维护，解约处理")
   @EnsembleElements(
      serviceCode = "Core",
      messageType = "1200",
      messageCode = "0708"
   )
   @FunctionCategory("RB13-现金池")
   @ConsumeSys("EOS/TLE/CMS")
   @ApiUseStatus("PRODUCT-产品")
   Core12000708Out runService(Core12000708In var1);
}
