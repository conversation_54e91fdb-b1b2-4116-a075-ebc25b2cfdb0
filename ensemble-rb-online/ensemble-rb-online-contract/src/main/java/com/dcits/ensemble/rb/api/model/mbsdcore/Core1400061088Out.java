package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400061088Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "结售汇交易登记簿数组",
      notNull = false,
      remark = "结售汇交易登记簿数组"
   )
   private List<Core1400061088Out.ExchangeTranHistList> exchangeTranHistList;

   public List<Core1400061088Out.ExchangeTranHistList> getExchangeTranHistList() {
      return this.exchangeTranHistList;
   }

   public void setExchangeTranHistList(List<Core1400061088Out.ExchangeTranHistList> exchangeTranHistList) {
      this.exchangeTranHistList = exchangeTranHistList;
   }

   public String toString() {
      return "Core1400061088Out(exchangeTranHistList=" + this.getExchangeTranHistList() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400061088Out)) {
         return false;
      } else {
         Core1400061088Out other = (Core1400061088Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$exchangeTranHistList = this.getExchangeTranHistList();
            Object other$exchangeTranHistList = other.getExchangeTranHistList();
            if (this$exchangeTranHistList == null) {
               if (other$exchangeTranHistList != null) {
                  return false;
               }
            } else if (!this$exchangeTranHistList.equals(other$exchangeTranHistList)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400061088Out;
   }
   public static class ExchangeTranHistList {
      @V(
         desc = "序号",
         notNull = false,
         length = "50",
         remark = "序号",
         maxSize = 50
      )
      private String seqNo;
      @V(
         desc = "交易类型",
         notNull = false,
         length = "10",
         remark = "交易类型",
         maxSize = 10
      )
      private String tranType;
      @V(
         desc = "交易日期",
         notNull = false,
         remark = "交易日期"
      )
      private String tranDate;
      @V(
         desc = "记账日期",
         notNull = false,
         remark = "记账日期"
      )
      private String valueDate;
      @V(
         desc = "贷方交易序号",
         notNull = false,
         length = "50",
         remark = "贷方交易序号",
         maxSize = 50
      )
      private String depositSeqNo;
      @V(
         desc = "贷方账户主键",
         notNull = false,
         length = "15",
         remark = "贷方账户主键"
      )
      private Long depositInternalKey;
      @V(
         desc = "贷方账户账号",
         notNull = false,
         length = "50",
         remark = "贷方账户账号",
         maxSize = 50
      )
      private String depositBaseAcctNo;
      @V(
         desc = "贷方账户币种",
         notNull = false,
         length = "3",
         remark = "贷方账户币种",
         maxSize = 3
      )
      private String depositAcctCcy;
      @V(
         desc = "贷方余额类型",
         notNull = false,
         length = "2",
         remark = "贷方余额类型",
         maxSize = 2
      )
      private String depositBalanceType;
      @V(
         desc = "借方交易序号",
         notNull = false,
         length = "50",
         remark = "借方交易序号",
         maxSize = 50
      )
      private String withdrawSeqNo;
      @V(
         desc = "借方账户主键",
         notNull = false,
         length = "15",
         remark = "借方账户主键"
      )
      private Long withdrawInternalKey;
      @V(
         desc = "借方账户账号",
         notNull = false,
         length = "50",
         remark = "借方账户账号",
         maxSize = 50
      )
      private String withdrawBaseAcctNo;
      @V(
         desc = "方账户产品类型",
         notNull = false,
         length = "20",
         remark = "方账户产品类型",
         maxSize = 20
      )
      private String withdrawProdType;
      @V(
         desc = "借方方账户币种",
         notNull = false,
         length = "3",
         remark = "借方方账户币种",
         maxSize = 3
      )
      private String withdrawAcctCcy;
      @V(
         desc = "借方账户序列号",
         notNull = false,
         length = "5",
         remark = "借方账户序列号",
         maxSize = 5
      )
      private String withdrawAcctSeqNo;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "现金交易序号",
         notNull = false,
         length = "50",
         remark = "现金交易序号",
         maxSize = 50
      )
      private String cashSeqNo;
      @V(
         desc = "买卖固定方",
         notNull = false,
         length = "1",
         remark = "买卖固定方",
         maxSize = 1
      )
      private String sellBuyInd;
      @V(
         desc = "买入币种",
         notNull = false,
         length = "3",
         remark = "买入币种",
         maxSize = 3
      )
      private String buyCcy;
      @V(
         desc = "买入金额",
         notNull = false,
         length = "17",
         remark = "买入金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal buyAmount;
      @V(
         desc = "卖出币种",
         notNull = false,
         length = "3",
         remark = "卖出币种",
         maxSize = 3
      )
      private String sellCcy;
      @V(
         desc = "卖出金额",
         notNull = false,
         length = "17",
         remark = "卖出金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal sellAmount;
      @V(
         desc = "汇率类型",
         notNull = false,
         length = "10",
         remark = "汇率类型",
         maxSize = 10
      )
      private String rateType;
      @V(
         desc = "占用额度金额",
         notNull = false,
         length = "17",
         remark = "占用额度金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal occAmt;
      @V(
         desc = "牌价类型",
         notNull = false,
         length = "1",
         remark = "牌价类型",
         maxSize = 1
      )
      private String quoteType;
      @V(
         desc = "交叉汇率",
         notNull = false,
         length = "15",
         remark = "交叉汇率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal crossRate;
      @V(
         desc = "浮动利率",
         notNull = false,
         length = "15",
         remark = "浮动利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal floatRate;
      @V(
         desc = "买方汇率",
         notNull = false,
         length = "15",
         remark = "买方汇率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal buyRate;
      @V(
         desc = "卖方汇率",
         notNull = false,
         length = "15",
         remark = "卖方汇率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal sellRate;
      @V(
         desc = "内部价",
         notNull = false,
         length = "15",
         remark = "内部价",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal innerRate;
      @V(
         desc = "执行汇率",
         notNull = false,
         length = "15",
         remark = "执行汇率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal exchRate;
      @V(
         desc = "交叉汇率属性",
         notNull = false,
         length = "10",
         remark = "交叉汇率属性",
         maxSize = 10
      )
      private String crossRateAttr;
      @V(
         desc = "基础汇率类型",
         notNull = false,
         length = "10",
         remark = "基础汇率类型",
         maxSize = 10
      )
      private String baseRateType;
      @V(
         desc = "基础报价方式",
         notNull = false,
         length = "1",
         remark = "基础报价方式",
         maxSize = 1
      )
      private String baseQuoteType;
      @V(
         desc = "基础汇率",
         notNull = false,
         length = "15",
         remark = "基础汇率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal baseRate;
      @V(
         desc = "基础等值金额",
         notNull = false,
         length = "17",
         remark = "基础等值金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal baseEquivAmt;
      @V(
         desc = "找零金额",
         notNull = false,
         length = "17",
         remark = "找零金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal changeSellAmount;
      @V(
         desc = "找零金额",
         notNull = false,
         length = "17",
         remark = "找零金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal changeCnyAmount;
      @V(
         desc = "找零交易序号",
         notNull = false,
         length = "50",
         remark = "找零交易序号",
         maxSize = 50
      )
      private String changeSeqNo;
      @V(
         desc = "找零利率类型",
         notNull = false,
         length = "10",
         remark = "找零利率类型",
         maxSize = 10
      )
      private String changeRateType;
      @V(
         desc = "找零报价方式",
         notNull = false,
         length = "1",
         remark = "找零报价方式",
         maxSize = 1
      )
      private String changeQuoteType;
      @V(
         desc = "找零利率",
         notNull = false,
         length = "15",
         remark = "找零利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal changeRate;
      @V(
         desc = "找零基础利率类型",
         notNull = false,
         length = "10",
         remark = "找零基础利率类型",
         maxSize = 10
      )
      private String changeBaseRateType;
      @V(
         desc = "找零基础报价方式",
         notNull = false,
         length = "10",
         remark = "找零基础报价方式",
         maxSize = 10
      )
      private String changeBaseQuoteType;
      @V(
         desc = "找零基础利率",
         notNull = false,
         length = "15",
         remark = "找零基础利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal changeBaseRate;
      @V(
         desc = "找零等值金额",
         notNull = false,
         length = "17",
         remark = "找零等值金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal changeBaseEquivAmt;
      @V(
         desc = "平盘交叉汇率",
         notNull = false,
         length = "15",
         remark = "平盘交叉汇率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal uncCrossRate;
      @V(
         desc = "备注",
         notNull = false,
         length = "200",
         remark = "备注",
         maxSize = 200
      )
      private String remark;
      @V(
         desc = "源模块",
         notNull = false,
         length = "3",
         remark = "源模块",
         maxSize = 3
      )
      private String sourceModule;
      @V(
         desc = "渠道类型",
         notNull = false,
         length = "10",
         remark = "渠道类型",
         maxSize = 10
      )
      private String sourceType;
      @V(
         desc = "所属机构号",
         notNull = false,
         length = "50",
         remark = "所属机构号",
         maxSize = 50
      )
      private String branch;
      @V(
         desc = "利润中心",
         notNull = false,
         length = "20",
         remark = "利润中心",
         maxSize = 20
      )
      private String profitCenter;
      @V(
         desc = "交易柜员",
         notNull = false,
         length = "30",
         remark = "交易柜员",
         maxSize = 30
      )
      private String userId;
      @V(
         desc = "交易时间戳",
         notNull = false,
         length = "26",
         remark = "交易时间戳",
         maxSize = 26
      )
      private String tranTimestamp;
      @V(
         desc = "授权柜员",
         notNull = false,
         length = "30",
         remark = "授权柜员",
         maxSize = 30
      )
      private String authUserId;
      @V(
         desc = "终端号",
         notNull = false,
         length = "50",
         remark = "终端号",
         maxSize = 50
      )
      private String terminalId;
      @V(
         desc = "冲正交易类型",
         notNull = false,
         length = "10",
         remark = "冲正交易类型",
         maxSize = 10
      )
      private String reversalTranType;
      @V(
         desc = "冲正日期",
         notNull = false,
         remark = "冲正日期"
      )
      private String reversalDate;
      @V(
         desc = "冲正时间戳",
         notNull = false,
         length = "26",
         remark = "冲正时间戳",
         maxSize = 26
      )
      private String reversalTranTimestamp;
      @V(
         desc = "冲正柜员",
         notNull = false,
         length = "30",
         remark = "冲正柜员",
         maxSize = 30
      )
      private String reversalUserId;
      @V(
         desc = "冲正授权柜员",
         notNull = false,
         length = "30",
         remark = "冲正授权柜员",
         maxSize = 30
      )
      private String reversalAuthUserId;
      @V(
         desc = "复核日期",
         notNull = false,
         remark = "复核日期"
      )
      private String approvalDate;
      @V(
         desc = "复核柜员",
         notNull = false,
         length = "30",
         remark = "复核柜员",
         maxSize = 30
      )
      private String apprUserId;
      @V(
         desc = "复核授权柜员",
         notNull = false,
         length = "30",
         remark = "复核授权柜员",
         maxSize = 30
      )
      private String apprAuthUserId;
      @V(
         desc = "银行交易序号",
         notNull = false,
         length = "50",
         remark = "银行交易序号,单一机构下发生交易序号，按顺序递增 格式为 \"机构_序号",
         maxSize = 50
      )
      private String bankSeqNo;
      @V(
         desc = "交易参考号",
         notNull = false,
         length = "50",
         remark = "交易参考号",
         maxSize = 50
      )
      private String reference;
      @V(
         desc = "跟踪参考号",
         notNull = false,
         length = "50",
         remark = "跟踪参考号",
         maxSize = 50
      )
      private String traceRefNo;
      @V(
         desc = "跟踪编码",
         notNull = false,
         length = "200",
         remark = "跟踪编码",
         maxSize = 200
      )
      private String traceRefCode;
      @V(
         desc = "状态",
         notNull = false,
         length = "1",
         remark = "状态",
         maxSize = 1
      )
      private String status;
      @V(
         desc = "法人",
         notNull = false,
         length = "20",
         remark = "法人",
         maxSize = 20
      )
      private String company;
      @V(
         desc = "TAE子流水序号",
         notNull = false,
         length = "200",
         remark = "用于登记tae子流水号的序号",
         maxSize = 200
      )
      private String taeSubSeqNo;
      @V(
         desc = "结售汇申报类型",
         notNull = false,
         length = "2",
         remark = "申报类型",
         maxSize = 2
      )
      private String exchangeClass;
      @V(
         desc = "客户名称",
         notNull = false,
         length = "200",
         remark = "客户名称",
         maxSize = 200
      )
      private String clientName;
      @V(
         desc = "结售汇项目编码",
         notNull = false,
         length = "50",
         remark = "结售汇项目编码",
         maxSize = 50
      )
      private String exchangeItemCode;
      @V(
         desc = "国家地区",
         notNull = false,
         length = "50",
         remark = "国家地区",
         maxSize = 50
      )
      private String area;
      @V(
         desc = "证件类型",
         notNull = false,
         length = "3",
         remark = "证件类型",
         maxSize = 3
      )
      private String documentType;
      @V(
         desc = "证件号码",
         notNull = false,
         length = "50",
         remark = "证件号码",
         maxSize = 50
      )
      private String documentId;
      @V(
         desc = "申报客户类型",
         notNull = false,
         length = "1",
         remark = "申报客户类型",
         maxSize = 1
      )
      private String exchangeReportType;
      @V(
         desc = "收入方交易编码",
         notNull = false,
         length = "10",
         remark = "收入方交易编码",
         maxSize = 10
      )
      private String exchangeTranCode;
      @V(
         desc = "支出方交易编码",
         notNull = false,
         length = "10",
         remark = "支出方交易编码",
         maxSize = 10
      )
      private String exchangeTranCodet;
      @V(
         desc = "结售汇用途详细信息",
         notNull = false,
         length = "200",
         remark = "用途详细信息",
         maxSize = 200
      )
      private String exchangePurposeDetails;
      @V(
         desc = "结售汇用途",
         notNull = false,
         length = "10",
         remark = "结售汇用途",
         maxSize = 10
      )
      private String exchangePurpose;
      @V(
         desc = "业务编号",
         notNull = false,
         length = "50",
         remark = "业务编号",
         maxSize = 50
      )
      private String busiNo;

      public String getSeqNo() {
         return this.seqNo;
      }

      public String getTranType() {
         return this.tranType;
      }

      public String getTranDate() {
         return this.tranDate;
      }

      public String getValueDate() {
         return this.valueDate;
      }

      public String getDepositSeqNo() {
         return this.depositSeqNo;
      }

      public Long getDepositInternalKey() {
         return this.depositInternalKey;
      }

      public String getDepositBaseAcctNo() {
         return this.depositBaseAcctNo;
      }

      public String getDepositAcctCcy() {
         return this.depositAcctCcy;
      }

      public String getDepositBalanceType() {
         return this.depositBalanceType;
      }

      public String getWithdrawSeqNo() {
         return this.withdrawSeqNo;
      }

      public Long getWithdrawInternalKey() {
         return this.withdrawInternalKey;
      }

      public String getWithdrawBaseAcctNo() {
         return this.withdrawBaseAcctNo;
      }

      public String getWithdrawProdType() {
         return this.withdrawProdType;
      }

      public String getWithdrawAcctCcy() {
         return this.withdrawAcctCcy;
      }

      public String getWithdrawAcctSeqNo() {
         return this.withdrawAcctSeqNo;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public String getCashSeqNo() {
         return this.cashSeqNo;
      }

      public String getSellBuyInd() {
         return this.sellBuyInd;
      }

      public String getBuyCcy() {
         return this.buyCcy;
      }

      public BigDecimal getBuyAmount() {
         return this.buyAmount;
      }

      public String getSellCcy() {
         return this.sellCcy;
      }

      public BigDecimal getSellAmount() {
         return this.sellAmount;
      }

      public String getRateType() {
         return this.rateType;
      }

      public BigDecimal getOccAmt() {
         return this.occAmt;
      }

      public String getQuoteType() {
         return this.quoteType;
      }

      public BigDecimal getCrossRate() {
         return this.crossRate;
      }

      public BigDecimal getFloatRate() {
         return this.floatRate;
      }

      public BigDecimal getBuyRate() {
         return this.buyRate;
      }

      public BigDecimal getSellRate() {
         return this.sellRate;
      }

      public BigDecimal getInnerRate() {
         return this.innerRate;
      }

      public BigDecimal getExchRate() {
         return this.exchRate;
      }

      public String getCrossRateAttr() {
         return this.crossRateAttr;
      }

      public String getBaseRateType() {
         return this.baseRateType;
      }

      public String getBaseQuoteType() {
         return this.baseQuoteType;
      }

      public BigDecimal getBaseRate() {
         return this.baseRate;
      }

      public BigDecimal getBaseEquivAmt() {
         return this.baseEquivAmt;
      }

      public BigDecimal getChangeSellAmount() {
         return this.changeSellAmount;
      }

      public BigDecimal getChangeCnyAmount() {
         return this.changeCnyAmount;
      }

      public String getChangeSeqNo() {
         return this.changeSeqNo;
      }

      public String getChangeRateType() {
         return this.changeRateType;
      }

      public String getChangeQuoteType() {
         return this.changeQuoteType;
      }

      public BigDecimal getChangeRate() {
         return this.changeRate;
      }

      public String getChangeBaseRateType() {
         return this.changeBaseRateType;
      }

      public String getChangeBaseQuoteType() {
         return this.changeBaseQuoteType;
      }

      public BigDecimal getChangeBaseRate() {
         return this.changeBaseRate;
      }

      public BigDecimal getChangeBaseEquivAmt() {
         return this.changeBaseEquivAmt;
      }

      public BigDecimal getUncCrossRate() {
         return this.uncCrossRate;
      }

      public String getRemark() {
         return this.remark;
      }

      public String getSourceModule() {
         return this.sourceModule;
      }

      public String getSourceType() {
         return this.sourceType;
      }

      public String getBranch() {
         return this.branch;
      }

      public String getProfitCenter() {
         return this.profitCenter;
      }

      public String getUserId() {
         return this.userId;
      }

      public String getTranTimestamp() {
         return this.tranTimestamp;
      }

      public String getAuthUserId() {
         return this.authUserId;
      }

      public String getTerminalId() {
         return this.terminalId;
      }

      public String getReversalTranType() {
         return this.reversalTranType;
      }

      public String getReversalDate() {
         return this.reversalDate;
      }

      public String getReversalTranTimestamp() {
         return this.reversalTranTimestamp;
      }

      public String getReversalUserId() {
         return this.reversalUserId;
      }

      public String getReversalAuthUserId() {
         return this.reversalAuthUserId;
      }

      public String getApprovalDate() {
         return this.approvalDate;
      }

      public String getApprUserId() {
         return this.apprUserId;
      }

      public String getApprAuthUserId() {
         return this.apprAuthUserId;
      }

      public String getBankSeqNo() {
         return this.bankSeqNo;
      }

      public String getReference() {
         return this.reference;
      }

      public String getTraceRefNo() {
         return this.traceRefNo;
      }

      public String getTraceRefCode() {
         return this.traceRefCode;
      }

      public String getStatus() {
         return this.status;
      }

      public String getCompany() {
         return this.company;
      }

      public String getTaeSubSeqNo() {
         return this.taeSubSeqNo;
      }

      public String getExchangeClass() {
         return this.exchangeClass;
      }

      public String getClientName() {
         return this.clientName;
      }

      public String getExchangeItemCode() {
         return this.exchangeItemCode;
      }

      public String getArea() {
         return this.area;
      }

      public String getDocumentType() {
         return this.documentType;
      }

      public String getDocumentId() {
         return this.documentId;
      }

      public String getExchangeReportType() {
         return this.exchangeReportType;
      }

      public String getExchangeTranCode() {
         return this.exchangeTranCode;
      }

      public String getExchangeTranCodet() {
         return this.exchangeTranCodet;
      }

      public String getExchangePurposeDetails() {
         return this.exchangePurposeDetails;
      }

      public String getExchangePurpose() {
         return this.exchangePurpose;
      }

      public String getBusiNo() {
         return this.busiNo;
      }

      public void setSeqNo(String seqNo) {
         this.seqNo = seqNo;
      }

      public void setTranType(String tranType) {
         this.tranType = tranType;
      }

      public void setTranDate(String tranDate) {
         this.tranDate = tranDate;
      }

      public void setValueDate(String valueDate) {
         this.valueDate = valueDate;
      }

      public void setDepositSeqNo(String depositSeqNo) {
         this.depositSeqNo = depositSeqNo;
      }

      public void setDepositInternalKey(Long depositInternalKey) {
         this.depositInternalKey = depositInternalKey;
      }

      public void setDepositBaseAcctNo(String depositBaseAcctNo) {
         this.depositBaseAcctNo = depositBaseAcctNo;
      }

      public void setDepositAcctCcy(String depositAcctCcy) {
         this.depositAcctCcy = depositAcctCcy;
      }

      public void setDepositBalanceType(String depositBalanceType) {
         this.depositBalanceType = depositBalanceType;
      }

      public void setWithdrawSeqNo(String withdrawSeqNo) {
         this.withdrawSeqNo = withdrawSeqNo;
      }

      public void setWithdrawInternalKey(Long withdrawInternalKey) {
         this.withdrawInternalKey = withdrawInternalKey;
      }

      public void setWithdrawBaseAcctNo(String withdrawBaseAcctNo) {
         this.withdrawBaseAcctNo = withdrawBaseAcctNo;
      }

      public void setWithdrawProdType(String withdrawProdType) {
         this.withdrawProdType = withdrawProdType;
      }

      public void setWithdrawAcctCcy(String withdrawAcctCcy) {
         this.withdrawAcctCcy = withdrawAcctCcy;
      }

      public void setWithdrawAcctSeqNo(String withdrawAcctSeqNo) {
         this.withdrawAcctSeqNo = withdrawAcctSeqNo;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setCashSeqNo(String cashSeqNo) {
         this.cashSeqNo = cashSeqNo;
      }

      public void setSellBuyInd(String sellBuyInd) {
         this.sellBuyInd = sellBuyInd;
      }

      public void setBuyCcy(String buyCcy) {
         this.buyCcy = buyCcy;
      }

      public void setBuyAmount(BigDecimal buyAmount) {
         this.buyAmount = buyAmount;
      }

      public void setSellCcy(String sellCcy) {
         this.sellCcy = sellCcy;
      }

      public void setSellAmount(BigDecimal sellAmount) {
         this.sellAmount = sellAmount;
      }

      public void setRateType(String rateType) {
         this.rateType = rateType;
      }

      public void setOccAmt(BigDecimal occAmt) {
         this.occAmt = occAmt;
      }

      public void setQuoteType(String quoteType) {
         this.quoteType = quoteType;
      }

      public void setCrossRate(BigDecimal crossRate) {
         this.crossRate = crossRate;
      }

      public void setFloatRate(BigDecimal floatRate) {
         this.floatRate = floatRate;
      }

      public void setBuyRate(BigDecimal buyRate) {
         this.buyRate = buyRate;
      }

      public void setSellRate(BigDecimal sellRate) {
         this.sellRate = sellRate;
      }

      public void setInnerRate(BigDecimal innerRate) {
         this.innerRate = innerRate;
      }

      public void setExchRate(BigDecimal exchRate) {
         this.exchRate = exchRate;
      }

      public void setCrossRateAttr(String crossRateAttr) {
         this.crossRateAttr = crossRateAttr;
      }

      public void setBaseRateType(String baseRateType) {
         this.baseRateType = baseRateType;
      }

      public void setBaseQuoteType(String baseQuoteType) {
         this.baseQuoteType = baseQuoteType;
      }

      public void setBaseRate(BigDecimal baseRate) {
         this.baseRate = baseRate;
      }

      public void setBaseEquivAmt(BigDecimal baseEquivAmt) {
         this.baseEquivAmt = baseEquivAmt;
      }

      public void setChangeSellAmount(BigDecimal changeSellAmount) {
         this.changeSellAmount = changeSellAmount;
      }

      public void setChangeCnyAmount(BigDecimal changeCnyAmount) {
         this.changeCnyAmount = changeCnyAmount;
      }

      public void setChangeSeqNo(String changeSeqNo) {
         this.changeSeqNo = changeSeqNo;
      }

      public void setChangeRateType(String changeRateType) {
         this.changeRateType = changeRateType;
      }

      public void setChangeQuoteType(String changeQuoteType) {
         this.changeQuoteType = changeQuoteType;
      }

      public void setChangeRate(BigDecimal changeRate) {
         this.changeRate = changeRate;
      }

      public void setChangeBaseRateType(String changeBaseRateType) {
         this.changeBaseRateType = changeBaseRateType;
      }

      public void setChangeBaseQuoteType(String changeBaseQuoteType) {
         this.changeBaseQuoteType = changeBaseQuoteType;
      }

      public void setChangeBaseRate(BigDecimal changeBaseRate) {
         this.changeBaseRate = changeBaseRate;
      }

      public void setChangeBaseEquivAmt(BigDecimal changeBaseEquivAmt) {
         this.changeBaseEquivAmt = changeBaseEquivAmt;
      }

      public void setUncCrossRate(BigDecimal uncCrossRate) {
         this.uncCrossRate = uncCrossRate;
      }

      public void setRemark(String remark) {
         this.remark = remark;
      }

      public void setSourceModule(String sourceModule) {
         this.sourceModule = sourceModule;
      }

      public void setSourceType(String sourceType) {
         this.sourceType = sourceType;
      }

      public void setBranch(String branch) {
         this.branch = branch;
      }

      public void setProfitCenter(String profitCenter) {
         this.profitCenter = profitCenter;
      }

      public void setUserId(String userId) {
         this.userId = userId;
      }

      public void setTranTimestamp(String tranTimestamp) {
         this.tranTimestamp = tranTimestamp;
      }

      public void setAuthUserId(String authUserId) {
         this.authUserId = authUserId;
      }

      public void setTerminalId(String terminalId) {
         this.terminalId = terminalId;
      }

      public void setReversalTranType(String reversalTranType) {
         this.reversalTranType = reversalTranType;
      }

      public void setReversalDate(String reversalDate) {
         this.reversalDate = reversalDate;
      }

      public void setReversalTranTimestamp(String reversalTranTimestamp) {
         this.reversalTranTimestamp = reversalTranTimestamp;
      }

      public void setReversalUserId(String reversalUserId) {
         this.reversalUserId = reversalUserId;
      }

      public void setReversalAuthUserId(String reversalAuthUserId) {
         this.reversalAuthUserId = reversalAuthUserId;
      }

      public void setApprovalDate(String approvalDate) {
         this.approvalDate = approvalDate;
      }

      public void setApprUserId(String apprUserId) {
         this.apprUserId = apprUserId;
      }

      public void setApprAuthUserId(String apprAuthUserId) {
         this.apprAuthUserId = apprAuthUserId;
      }

      public void setBankSeqNo(String bankSeqNo) {
         this.bankSeqNo = bankSeqNo;
      }

      public void setReference(String reference) {
         this.reference = reference;
      }

      public void setTraceRefNo(String traceRefNo) {
         this.traceRefNo = traceRefNo;
      }

      public void setTraceRefCode(String traceRefCode) {
         this.traceRefCode = traceRefCode;
      }

      public void setStatus(String status) {
         this.status = status;
      }

      public void setCompany(String company) {
         this.company = company;
      }

      public void setTaeSubSeqNo(String taeSubSeqNo) {
         this.taeSubSeqNo = taeSubSeqNo;
      }

      public void setExchangeClass(String exchangeClass) {
         this.exchangeClass = exchangeClass;
      }

      public void setClientName(String clientName) {
         this.clientName = clientName;
      }

      public void setExchangeItemCode(String exchangeItemCode) {
         this.exchangeItemCode = exchangeItemCode;
      }

      public void setArea(String area) {
         this.area = area;
      }

      public void setDocumentType(String documentType) {
         this.documentType = documentType;
      }

      public void setDocumentId(String documentId) {
         this.documentId = documentId;
      }

      public void setExchangeReportType(String exchangeReportType) {
         this.exchangeReportType = exchangeReportType;
      }

      public void setExchangeTranCode(String exchangeTranCode) {
         this.exchangeTranCode = exchangeTranCode;
      }

      public void setExchangeTranCodet(String exchangeTranCodet) {
         this.exchangeTranCodet = exchangeTranCodet;
      }

      public void setExchangePurposeDetails(String exchangePurposeDetails) {
         this.exchangePurposeDetails = exchangePurposeDetails;
      }

      public void setExchangePurpose(String exchangePurpose) {
         this.exchangePurpose = exchangePurpose;
      }

      public void setBusiNo(String busiNo) {
         this.busiNo = busiNo;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400061088Out.ExchangeTranHistList)) {
            return false;
         } else {
            Core1400061088Out.ExchangeTranHistList other = (Core1400061088Out.ExchangeTranHistList)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label1007: {
                  Object this$seqNo = this.getSeqNo();
                  Object other$seqNo = other.getSeqNo();
                  if (this$seqNo == null) {
                     if (other$seqNo == null) {
                        break label1007;
                     }
                  } else if (this$seqNo.equals(other$seqNo)) {
                     break label1007;
                  }

                  return false;
               }

               Object this$tranType = this.getTranType();
               Object other$tranType = other.getTranType();
               if (this$tranType == null) {
                  if (other$tranType != null) {
                     return false;
                  }
               } else if (!this$tranType.equals(other$tranType)) {
                  return false;
               }

               Object this$tranDate = this.getTranDate();
               Object other$tranDate = other.getTranDate();
               if (this$tranDate == null) {
                  if (other$tranDate != null) {
                     return false;
                  }
               } else if (!this$tranDate.equals(other$tranDate)) {
                  return false;
               }

               label986: {
                  Object this$valueDate = this.getValueDate();
                  Object other$valueDate = other.getValueDate();
                  if (this$valueDate == null) {
                     if (other$valueDate == null) {
                        break label986;
                     }
                  } else if (this$valueDate.equals(other$valueDate)) {
                     break label986;
                  }

                  return false;
               }

               label979: {
                  Object this$depositSeqNo = this.getDepositSeqNo();
                  Object other$depositSeqNo = other.getDepositSeqNo();
                  if (this$depositSeqNo == null) {
                     if (other$depositSeqNo == null) {
                        break label979;
                     }
                  } else if (this$depositSeqNo.equals(other$depositSeqNo)) {
                     break label979;
                  }

                  return false;
               }

               Object this$depositInternalKey = this.getDepositInternalKey();
               Object other$depositInternalKey = other.getDepositInternalKey();
               if (this$depositInternalKey == null) {
                  if (other$depositInternalKey != null) {
                     return false;
                  }
               } else if (!this$depositInternalKey.equals(other$depositInternalKey)) {
                  return false;
               }

               Object this$depositBaseAcctNo = this.getDepositBaseAcctNo();
               Object other$depositBaseAcctNo = other.getDepositBaseAcctNo();
               if (this$depositBaseAcctNo == null) {
                  if (other$depositBaseAcctNo != null) {
                     return false;
                  }
               } else if (!this$depositBaseAcctNo.equals(other$depositBaseAcctNo)) {
                  return false;
               }

               label958: {
                  Object this$depositAcctCcy = this.getDepositAcctCcy();
                  Object other$depositAcctCcy = other.getDepositAcctCcy();
                  if (this$depositAcctCcy == null) {
                     if (other$depositAcctCcy == null) {
                        break label958;
                     }
                  } else if (this$depositAcctCcy.equals(other$depositAcctCcy)) {
                     break label958;
                  }

                  return false;
               }

               label951: {
                  Object this$depositBalanceType = this.getDepositBalanceType();
                  Object other$depositBalanceType = other.getDepositBalanceType();
                  if (this$depositBalanceType == null) {
                     if (other$depositBalanceType == null) {
                        break label951;
                     }
                  } else if (this$depositBalanceType.equals(other$depositBalanceType)) {
                     break label951;
                  }

                  return false;
               }

               Object this$withdrawSeqNo = this.getWithdrawSeqNo();
               Object other$withdrawSeqNo = other.getWithdrawSeqNo();
               if (this$withdrawSeqNo == null) {
                  if (other$withdrawSeqNo != null) {
                     return false;
                  }
               } else if (!this$withdrawSeqNo.equals(other$withdrawSeqNo)) {
                  return false;
               }

               label937: {
                  Object this$withdrawInternalKey = this.getWithdrawInternalKey();
                  Object other$withdrawInternalKey = other.getWithdrawInternalKey();
                  if (this$withdrawInternalKey == null) {
                     if (other$withdrawInternalKey == null) {
                        break label937;
                     }
                  } else if (this$withdrawInternalKey.equals(other$withdrawInternalKey)) {
                     break label937;
                  }

                  return false;
               }

               Object this$withdrawBaseAcctNo = this.getWithdrawBaseAcctNo();
               Object other$withdrawBaseAcctNo = other.getWithdrawBaseAcctNo();
               if (this$withdrawBaseAcctNo == null) {
                  if (other$withdrawBaseAcctNo != null) {
                     return false;
                  }
               } else if (!this$withdrawBaseAcctNo.equals(other$withdrawBaseAcctNo)) {
                  return false;
               }

               label923: {
                  Object this$withdrawProdType = this.getWithdrawProdType();
                  Object other$withdrawProdType = other.getWithdrawProdType();
                  if (this$withdrawProdType == null) {
                     if (other$withdrawProdType == null) {
                        break label923;
                     }
                  } else if (this$withdrawProdType.equals(other$withdrawProdType)) {
                     break label923;
                  }

                  return false;
               }

               Object this$withdrawAcctCcy = this.getWithdrawAcctCcy();
               Object other$withdrawAcctCcy = other.getWithdrawAcctCcy();
               if (this$withdrawAcctCcy == null) {
                  if (other$withdrawAcctCcy != null) {
                     return false;
                  }
               } else if (!this$withdrawAcctCcy.equals(other$withdrawAcctCcy)) {
                  return false;
               }

               Object this$withdrawAcctSeqNo = this.getWithdrawAcctSeqNo();
               Object other$withdrawAcctSeqNo = other.getWithdrawAcctSeqNo();
               if (this$withdrawAcctSeqNo == null) {
                  if (other$withdrawAcctSeqNo != null) {
                     return false;
                  }
               } else if (!this$withdrawAcctSeqNo.equals(other$withdrawAcctSeqNo)) {
                  return false;
               }

               label902: {
                  Object this$clientNo = this.getClientNo();
                  Object other$clientNo = other.getClientNo();
                  if (this$clientNo == null) {
                     if (other$clientNo == null) {
                        break label902;
                     }
                  } else if (this$clientNo.equals(other$clientNo)) {
                     break label902;
                  }

                  return false;
               }

               label895: {
                  Object this$cashSeqNo = this.getCashSeqNo();
                  Object other$cashSeqNo = other.getCashSeqNo();
                  if (this$cashSeqNo == null) {
                     if (other$cashSeqNo == null) {
                        break label895;
                     }
                  } else if (this$cashSeqNo.equals(other$cashSeqNo)) {
                     break label895;
                  }

                  return false;
               }

               Object this$sellBuyInd = this.getSellBuyInd();
               Object other$sellBuyInd = other.getSellBuyInd();
               if (this$sellBuyInd == null) {
                  if (other$sellBuyInd != null) {
                     return false;
                  }
               } else if (!this$sellBuyInd.equals(other$sellBuyInd)) {
                  return false;
               }

               Object this$buyCcy = this.getBuyCcy();
               Object other$buyCcy = other.getBuyCcy();
               if (this$buyCcy == null) {
                  if (other$buyCcy != null) {
                     return false;
                  }
               } else if (!this$buyCcy.equals(other$buyCcy)) {
                  return false;
               }

               label874: {
                  Object this$buyAmount = this.getBuyAmount();
                  Object other$buyAmount = other.getBuyAmount();
                  if (this$buyAmount == null) {
                     if (other$buyAmount == null) {
                        break label874;
                     }
                  } else if (this$buyAmount.equals(other$buyAmount)) {
                     break label874;
                  }

                  return false;
               }

               label867: {
                  Object this$sellCcy = this.getSellCcy();
                  Object other$sellCcy = other.getSellCcy();
                  if (this$sellCcy == null) {
                     if (other$sellCcy == null) {
                        break label867;
                     }
                  } else if (this$sellCcy.equals(other$sellCcy)) {
                     break label867;
                  }

                  return false;
               }

               Object this$sellAmount = this.getSellAmount();
               Object other$sellAmount = other.getSellAmount();
               if (this$sellAmount == null) {
                  if (other$sellAmount != null) {
                     return false;
                  }
               } else if (!this$sellAmount.equals(other$sellAmount)) {
                  return false;
               }

               Object this$rateType = this.getRateType();
               Object other$rateType = other.getRateType();
               if (this$rateType == null) {
                  if (other$rateType != null) {
                     return false;
                  }
               } else if (!this$rateType.equals(other$rateType)) {
                  return false;
               }

               label846: {
                  Object this$occAmt = this.getOccAmt();
                  Object other$occAmt = other.getOccAmt();
                  if (this$occAmt == null) {
                     if (other$occAmt == null) {
                        break label846;
                     }
                  } else if (this$occAmt.equals(other$occAmt)) {
                     break label846;
                  }

                  return false;
               }

               label839: {
                  Object this$quoteType = this.getQuoteType();
                  Object other$quoteType = other.getQuoteType();
                  if (this$quoteType == null) {
                     if (other$quoteType == null) {
                        break label839;
                     }
                  } else if (this$quoteType.equals(other$quoteType)) {
                     break label839;
                  }

                  return false;
               }

               Object this$crossRate = this.getCrossRate();
               Object other$crossRate = other.getCrossRate();
               if (this$crossRate == null) {
                  if (other$crossRate != null) {
                     return false;
                  }
               } else if (!this$crossRate.equals(other$crossRate)) {
                  return false;
               }

               label825: {
                  Object this$floatRate = this.getFloatRate();
                  Object other$floatRate = other.getFloatRate();
                  if (this$floatRate == null) {
                     if (other$floatRate == null) {
                        break label825;
                     }
                  } else if (this$floatRate.equals(other$floatRate)) {
                     break label825;
                  }

                  return false;
               }

               Object this$buyRate = this.getBuyRate();
               Object other$buyRate = other.getBuyRate();
               if (this$buyRate == null) {
                  if (other$buyRate != null) {
                     return false;
                  }
               } else if (!this$buyRate.equals(other$buyRate)) {
                  return false;
               }

               label811: {
                  Object this$sellRate = this.getSellRate();
                  Object other$sellRate = other.getSellRate();
                  if (this$sellRate == null) {
                     if (other$sellRate == null) {
                        break label811;
                     }
                  } else if (this$sellRate.equals(other$sellRate)) {
                     break label811;
                  }

                  return false;
               }

               Object this$innerRate = this.getInnerRate();
               Object other$innerRate = other.getInnerRate();
               if (this$innerRate == null) {
                  if (other$innerRate != null) {
                     return false;
                  }
               } else if (!this$innerRate.equals(other$innerRate)) {
                  return false;
               }

               Object this$exchRate = this.getExchRate();
               Object other$exchRate = other.getExchRate();
               if (this$exchRate == null) {
                  if (other$exchRate != null) {
                     return false;
                  }
               } else if (!this$exchRate.equals(other$exchRate)) {
                  return false;
               }

               label790: {
                  Object this$crossRateAttr = this.getCrossRateAttr();
                  Object other$crossRateAttr = other.getCrossRateAttr();
                  if (this$crossRateAttr == null) {
                     if (other$crossRateAttr == null) {
                        break label790;
                     }
                  } else if (this$crossRateAttr.equals(other$crossRateAttr)) {
                     break label790;
                  }

                  return false;
               }

               label783: {
                  Object this$baseRateType = this.getBaseRateType();
                  Object other$baseRateType = other.getBaseRateType();
                  if (this$baseRateType == null) {
                     if (other$baseRateType == null) {
                        break label783;
                     }
                  } else if (this$baseRateType.equals(other$baseRateType)) {
                     break label783;
                  }

                  return false;
               }

               Object this$baseQuoteType = this.getBaseQuoteType();
               Object other$baseQuoteType = other.getBaseQuoteType();
               if (this$baseQuoteType == null) {
                  if (other$baseQuoteType != null) {
                     return false;
                  }
               } else if (!this$baseQuoteType.equals(other$baseQuoteType)) {
                  return false;
               }

               Object this$baseRate = this.getBaseRate();
               Object other$baseRate = other.getBaseRate();
               if (this$baseRate == null) {
                  if (other$baseRate != null) {
                     return false;
                  }
               } else if (!this$baseRate.equals(other$baseRate)) {
                  return false;
               }

               label762: {
                  Object this$baseEquivAmt = this.getBaseEquivAmt();
                  Object other$baseEquivAmt = other.getBaseEquivAmt();
                  if (this$baseEquivAmt == null) {
                     if (other$baseEquivAmt == null) {
                        break label762;
                     }
                  } else if (this$baseEquivAmt.equals(other$baseEquivAmt)) {
                     break label762;
                  }

                  return false;
               }

               label755: {
                  Object this$changeSellAmount = this.getChangeSellAmount();
                  Object other$changeSellAmount = other.getChangeSellAmount();
                  if (this$changeSellAmount == null) {
                     if (other$changeSellAmount == null) {
                        break label755;
                     }
                  } else if (this$changeSellAmount.equals(other$changeSellAmount)) {
                     break label755;
                  }

                  return false;
               }

               Object this$changeCnyAmount = this.getChangeCnyAmount();
               Object other$changeCnyAmount = other.getChangeCnyAmount();
               if (this$changeCnyAmount == null) {
                  if (other$changeCnyAmount != null) {
                     return false;
                  }
               } else if (!this$changeCnyAmount.equals(other$changeCnyAmount)) {
                  return false;
               }

               Object this$changeSeqNo = this.getChangeSeqNo();
               Object other$changeSeqNo = other.getChangeSeqNo();
               if (this$changeSeqNo == null) {
                  if (other$changeSeqNo != null) {
                     return false;
                  }
               } else if (!this$changeSeqNo.equals(other$changeSeqNo)) {
                  return false;
               }

               label734: {
                  Object this$changeRateType = this.getChangeRateType();
                  Object other$changeRateType = other.getChangeRateType();
                  if (this$changeRateType == null) {
                     if (other$changeRateType == null) {
                        break label734;
                     }
                  } else if (this$changeRateType.equals(other$changeRateType)) {
                     break label734;
                  }

                  return false;
               }

               label727: {
                  Object this$changeQuoteType = this.getChangeQuoteType();
                  Object other$changeQuoteType = other.getChangeQuoteType();
                  if (this$changeQuoteType == null) {
                     if (other$changeQuoteType == null) {
                        break label727;
                     }
                  } else if (this$changeQuoteType.equals(other$changeQuoteType)) {
                     break label727;
                  }

                  return false;
               }

               Object this$changeRate = this.getChangeRate();
               Object other$changeRate = other.getChangeRate();
               if (this$changeRate == null) {
                  if (other$changeRate != null) {
                     return false;
                  }
               } else if (!this$changeRate.equals(other$changeRate)) {
                  return false;
               }

               label713: {
                  Object this$changeBaseRateType = this.getChangeBaseRateType();
                  Object other$changeBaseRateType = other.getChangeBaseRateType();
                  if (this$changeBaseRateType == null) {
                     if (other$changeBaseRateType == null) {
                        break label713;
                     }
                  } else if (this$changeBaseRateType.equals(other$changeBaseRateType)) {
                     break label713;
                  }

                  return false;
               }

               Object this$changeBaseQuoteType = this.getChangeBaseQuoteType();
               Object other$changeBaseQuoteType = other.getChangeBaseQuoteType();
               if (this$changeBaseQuoteType == null) {
                  if (other$changeBaseQuoteType != null) {
                     return false;
                  }
               } else if (!this$changeBaseQuoteType.equals(other$changeBaseQuoteType)) {
                  return false;
               }

               label699: {
                  Object this$changeBaseRate = this.getChangeBaseRate();
                  Object other$changeBaseRate = other.getChangeBaseRate();
                  if (this$changeBaseRate == null) {
                     if (other$changeBaseRate == null) {
                        break label699;
                     }
                  } else if (this$changeBaseRate.equals(other$changeBaseRate)) {
                     break label699;
                  }

                  return false;
               }

               Object this$changeBaseEquivAmt = this.getChangeBaseEquivAmt();
               Object other$changeBaseEquivAmt = other.getChangeBaseEquivAmt();
               if (this$changeBaseEquivAmt == null) {
                  if (other$changeBaseEquivAmt != null) {
                     return false;
                  }
               } else if (!this$changeBaseEquivAmt.equals(other$changeBaseEquivAmt)) {
                  return false;
               }

               Object this$uncCrossRate = this.getUncCrossRate();
               Object other$uncCrossRate = other.getUncCrossRate();
               if (this$uncCrossRate == null) {
                  if (other$uncCrossRate != null) {
                     return false;
                  }
               } else if (!this$uncCrossRate.equals(other$uncCrossRate)) {
                  return false;
               }

               label678: {
                  Object this$remark = this.getRemark();
                  Object other$remark = other.getRemark();
                  if (this$remark == null) {
                     if (other$remark == null) {
                        break label678;
                     }
                  } else if (this$remark.equals(other$remark)) {
                     break label678;
                  }

                  return false;
               }

               label671: {
                  Object this$sourceModule = this.getSourceModule();
                  Object other$sourceModule = other.getSourceModule();
                  if (this$sourceModule == null) {
                     if (other$sourceModule == null) {
                        break label671;
                     }
                  } else if (this$sourceModule.equals(other$sourceModule)) {
                     break label671;
                  }

                  return false;
               }

               Object this$sourceType = this.getSourceType();
               Object other$sourceType = other.getSourceType();
               if (this$sourceType == null) {
                  if (other$sourceType != null) {
                     return false;
                  }
               } else if (!this$sourceType.equals(other$sourceType)) {
                  return false;
               }

               Object this$branch = this.getBranch();
               Object other$branch = other.getBranch();
               if (this$branch == null) {
                  if (other$branch != null) {
                     return false;
                  }
               } else if (!this$branch.equals(other$branch)) {
                  return false;
               }

               label650: {
                  Object this$profitCenter = this.getProfitCenter();
                  Object other$profitCenter = other.getProfitCenter();
                  if (this$profitCenter == null) {
                     if (other$profitCenter == null) {
                        break label650;
                     }
                  } else if (this$profitCenter.equals(other$profitCenter)) {
                     break label650;
                  }

                  return false;
               }

               label643: {
                  Object this$userId = this.getUserId();
                  Object other$userId = other.getUserId();
                  if (this$userId == null) {
                     if (other$userId == null) {
                        break label643;
                     }
                  } else if (this$userId.equals(other$userId)) {
                     break label643;
                  }

                  return false;
               }

               Object this$tranTimestamp = this.getTranTimestamp();
               Object other$tranTimestamp = other.getTranTimestamp();
               if (this$tranTimestamp == null) {
                  if (other$tranTimestamp != null) {
                     return false;
                  }
               } else if (!this$tranTimestamp.equals(other$tranTimestamp)) {
                  return false;
               }

               Object this$authUserId = this.getAuthUserId();
               Object other$authUserId = other.getAuthUserId();
               if (this$authUserId == null) {
                  if (other$authUserId != null) {
                     return false;
                  }
               } else if (!this$authUserId.equals(other$authUserId)) {
                  return false;
               }

               label622: {
                  Object this$terminalId = this.getTerminalId();
                  Object other$terminalId = other.getTerminalId();
                  if (this$terminalId == null) {
                     if (other$terminalId == null) {
                        break label622;
                     }
                  } else if (this$terminalId.equals(other$terminalId)) {
                     break label622;
                  }

                  return false;
               }

               label615: {
                  Object this$reversalTranType = this.getReversalTranType();
                  Object other$reversalTranType = other.getReversalTranType();
                  if (this$reversalTranType == null) {
                     if (other$reversalTranType == null) {
                        break label615;
                     }
                  } else if (this$reversalTranType.equals(other$reversalTranType)) {
                     break label615;
                  }

                  return false;
               }

               Object this$reversalDate = this.getReversalDate();
               Object other$reversalDate = other.getReversalDate();
               if (this$reversalDate == null) {
                  if (other$reversalDate != null) {
                     return false;
                  }
               } else if (!this$reversalDate.equals(other$reversalDate)) {
                  return false;
               }

               label601: {
                  Object this$reversalTranTimestamp = this.getReversalTranTimestamp();
                  Object other$reversalTranTimestamp = other.getReversalTranTimestamp();
                  if (this$reversalTranTimestamp == null) {
                     if (other$reversalTranTimestamp == null) {
                        break label601;
                     }
                  } else if (this$reversalTranTimestamp.equals(other$reversalTranTimestamp)) {
                     break label601;
                  }

                  return false;
               }

               Object this$reversalUserId = this.getReversalUserId();
               Object other$reversalUserId = other.getReversalUserId();
               if (this$reversalUserId == null) {
                  if (other$reversalUserId != null) {
                     return false;
                  }
               } else if (!this$reversalUserId.equals(other$reversalUserId)) {
                  return false;
               }

               label587: {
                  Object this$reversalAuthUserId = this.getReversalAuthUserId();
                  Object other$reversalAuthUserId = other.getReversalAuthUserId();
                  if (this$reversalAuthUserId == null) {
                     if (other$reversalAuthUserId == null) {
                        break label587;
                     }
                  } else if (this$reversalAuthUserId.equals(other$reversalAuthUserId)) {
                     break label587;
                  }

                  return false;
               }

               Object this$approvalDate = this.getApprovalDate();
               Object other$approvalDate = other.getApprovalDate();
               if (this$approvalDate == null) {
                  if (other$approvalDate != null) {
                     return false;
                  }
               } else if (!this$approvalDate.equals(other$approvalDate)) {
                  return false;
               }

               Object this$apprUserId = this.getApprUserId();
               Object other$apprUserId = other.getApprUserId();
               if (this$apprUserId == null) {
                  if (other$apprUserId != null) {
                     return false;
                  }
               } else if (!this$apprUserId.equals(other$apprUserId)) {
                  return false;
               }

               label566: {
                  Object this$apprAuthUserId = this.getApprAuthUserId();
                  Object other$apprAuthUserId = other.getApprAuthUserId();
                  if (this$apprAuthUserId == null) {
                     if (other$apprAuthUserId == null) {
                        break label566;
                     }
                  } else if (this$apprAuthUserId.equals(other$apprAuthUserId)) {
                     break label566;
                  }

                  return false;
               }

               label559: {
                  Object this$bankSeqNo = this.getBankSeqNo();
                  Object other$bankSeqNo = other.getBankSeqNo();
                  if (this$bankSeqNo == null) {
                     if (other$bankSeqNo == null) {
                        break label559;
                     }
                  } else if (this$bankSeqNo.equals(other$bankSeqNo)) {
                     break label559;
                  }

                  return false;
               }

               Object this$reference = this.getReference();
               Object other$reference = other.getReference();
               if (this$reference == null) {
                  if (other$reference != null) {
                     return false;
                  }
               } else if (!this$reference.equals(other$reference)) {
                  return false;
               }

               Object this$traceRefNo = this.getTraceRefNo();
               Object other$traceRefNo = other.getTraceRefNo();
               if (this$traceRefNo == null) {
                  if (other$traceRefNo != null) {
                     return false;
                  }
               } else if (!this$traceRefNo.equals(other$traceRefNo)) {
                  return false;
               }

               label538: {
                  Object this$traceRefCode = this.getTraceRefCode();
                  Object other$traceRefCode = other.getTraceRefCode();
                  if (this$traceRefCode == null) {
                     if (other$traceRefCode == null) {
                        break label538;
                     }
                  } else if (this$traceRefCode.equals(other$traceRefCode)) {
                     break label538;
                  }

                  return false;
               }

               label531: {
                  Object this$status = this.getStatus();
                  Object other$status = other.getStatus();
                  if (this$status == null) {
                     if (other$status == null) {
                        break label531;
                     }
                  } else if (this$status.equals(other$status)) {
                     break label531;
                  }

                  return false;
               }

               Object this$company = this.getCompany();
               Object other$company = other.getCompany();
               if (this$company == null) {
                  if (other$company != null) {
                     return false;
                  }
               } else if (!this$company.equals(other$company)) {
                  return false;
               }

               Object this$taeSubSeqNo = this.getTaeSubSeqNo();
               Object other$taeSubSeqNo = other.getTaeSubSeqNo();
               if (this$taeSubSeqNo == null) {
                  if (other$taeSubSeqNo != null) {
                     return false;
                  }
               } else if (!this$taeSubSeqNo.equals(other$taeSubSeqNo)) {
                  return false;
               }

               label510: {
                  Object this$exchangeClass = this.getExchangeClass();
                  Object other$exchangeClass = other.getExchangeClass();
                  if (this$exchangeClass == null) {
                     if (other$exchangeClass == null) {
                        break label510;
                     }
                  } else if (this$exchangeClass.equals(other$exchangeClass)) {
                     break label510;
                  }

                  return false;
               }

               label503: {
                  Object this$clientName = this.getClientName();
                  Object other$clientName = other.getClientName();
                  if (this$clientName == null) {
                     if (other$clientName == null) {
                        break label503;
                     }
                  } else if (this$clientName.equals(other$clientName)) {
                     break label503;
                  }

                  return false;
               }

               Object this$exchangeItemCode = this.getExchangeItemCode();
               Object other$exchangeItemCode = other.getExchangeItemCode();
               if (this$exchangeItemCode == null) {
                  if (other$exchangeItemCode != null) {
                     return false;
                  }
               } else if (!this$exchangeItemCode.equals(other$exchangeItemCode)) {
                  return false;
               }

               label489: {
                  Object this$area = this.getArea();
                  Object other$area = other.getArea();
                  if (this$area == null) {
                     if (other$area == null) {
                        break label489;
                     }
                  } else if (this$area.equals(other$area)) {
                     break label489;
                  }

                  return false;
               }

               Object this$documentType = this.getDocumentType();
               Object other$documentType = other.getDocumentType();
               if (this$documentType == null) {
                  if (other$documentType != null) {
                     return false;
                  }
               } else if (!this$documentType.equals(other$documentType)) {
                  return false;
               }

               label475: {
                  Object this$documentId = this.getDocumentId();
                  Object other$documentId = other.getDocumentId();
                  if (this$documentId == null) {
                     if (other$documentId == null) {
                        break label475;
                     }
                  } else if (this$documentId.equals(other$documentId)) {
                     break label475;
                  }

                  return false;
               }

               Object this$exchangeReportType = this.getExchangeReportType();
               Object other$exchangeReportType = other.getExchangeReportType();
               if (this$exchangeReportType == null) {
                  if (other$exchangeReportType != null) {
                     return false;
                  }
               } else if (!this$exchangeReportType.equals(other$exchangeReportType)) {
                  return false;
               }

               Object this$exchangeTranCode = this.getExchangeTranCode();
               Object other$exchangeTranCode = other.getExchangeTranCode();
               if (this$exchangeTranCode == null) {
                  if (other$exchangeTranCode != null) {
                     return false;
                  }
               } else if (!this$exchangeTranCode.equals(other$exchangeTranCode)) {
                  return false;
               }

               label454: {
                  Object this$exchangeTranCodet = this.getExchangeTranCodet();
                  Object other$exchangeTranCodet = other.getExchangeTranCodet();
                  if (this$exchangeTranCodet == null) {
                     if (other$exchangeTranCodet == null) {
                        break label454;
                     }
                  } else if (this$exchangeTranCodet.equals(other$exchangeTranCodet)) {
                     break label454;
                  }

                  return false;
               }

               label447: {
                  Object this$exchangePurposeDetails = this.getExchangePurposeDetails();
                  Object other$exchangePurposeDetails = other.getExchangePurposeDetails();
                  if (this$exchangePurposeDetails == null) {
                     if (other$exchangePurposeDetails == null) {
                        break label447;
                     }
                  } else if (this$exchangePurposeDetails.equals(other$exchangePurposeDetails)) {
                     break label447;
                  }

                  return false;
               }

               Object this$exchangePurpose = this.getExchangePurpose();
               Object other$exchangePurpose = other.getExchangePurpose();
               if (this$exchangePurpose == null) {
                  if (other$exchangePurpose != null) {
                     return false;
                  }
               } else if (!this$exchangePurpose.equals(other$exchangePurpose)) {
                  return false;
               }

               Object this$busiNo = this.getBusiNo();
               Object other$busiNo = other.getBusiNo();
               if (this$busiNo == null) {
                  if (other$busiNo != null) {
                     return false;
                  }
               } else if (!this$busiNo.equals(other$busiNo)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400061088Out.ExchangeTranHistList;
      }
      public String toString() {
         return "Core1400061088Out.ExchangeTranHistList(seqNo=" + this.getSeqNo() + ", tranType=" + this.getTranType() + ", tranDate=" + this.getTranDate() + ", valueDate=" + this.getValueDate() + ", depositSeqNo=" + this.getDepositSeqNo() + ", depositInternalKey=" + this.getDepositInternalKey() + ", depositBaseAcctNo=" + this.getDepositBaseAcctNo() + ", depositAcctCcy=" + this.getDepositAcctCcy() + ", depositBalanceType=" + this.getDepositBalanceType() + ", withdrawSeqNo=" + this.getWithdrawSeqNo() + ", withdrawInternalKey=" + this.getWithdrawInternalKey() + ", withdrawBaseAcctNo=" + this.getWithdrawBaseAcctNo() + ", withdrawProdType=" + this.getWithdrawProdType() + ", withdrawAcctCcy=" + this.getWithdrawAcctCcy() + ", withdrawAcctSeqNo=" + this.getWithdrawAcctSeqNo() + ", clientNo=" + this.getClientNo() + ", cashSeqNo=" + this.getCashSeqNo() + ", sellBuyInd=" + this.getSellBuyInd() + ", buyCcy=" + this.getBuyCcy() + ", buyAmount=" + this.getBuyAmount() + ", sellCcy=" + this.getSellCcy() + ", sellAmount=" + this.getSellAmount() + ", rateType=" + this.getRateType() + ", occAmt=" + this.getOccAmt() + ", quoteType=" + this.getQuoteType() + ", crossRate=" + this.getCrossRate() + ", floatRate=" + this.getFloatRate() + ", buyRate=" + this.getBuyRate() + ", sellRate=" + this.getSellRate() + ", innerRate=" + this.getInnerRate() + ", exchRate=" + this.getExchRate() + ", crossRateAttr=" + this.getCrossRateAttr() + ", baseRateType=" + this.getBaseRateType() + ", baseQuoteType=" + this.getBaseQuoteType() + ", baseRate=" + this.getBaseRate() + ", baseEquivAmt=" + this.getBaseEquivAmt() + ", changeSellAmount=" + this.getChangeSellAmount() + ", changeCnyAmount=" + this.getChangeCnyAmount() + ", changeSeqNo=" + this.getChangeSeqNo() + ", changeRateType=" + this.getChangeRateType() + ", changeQuoteType=" + this.getChangeQuoteType() + ", changeRate=" + this.getChangeRate() + ", changeBaseRateType=" + this.getChangeBaseRateType() + ", changeBaseQuoteType=" + this.getChangeBaseQuoteType() + ", changeBaseRate=" + this.getChangeBaseRate() + ", changeBaseEquivAmt=" + this.getChangeBaseEquivAmt() + ", uncCrossRate=" + this.getUncCrossRate() + ", remark=" + this.getRemark() + ", sourceModule=" + this.getSourceModule() + ", sourceType=" + this.getSourceType() + ", branch=" + this.getBranch() + ", profitCenter=" + this.getProfitCenter() + ", userId=" + this.getUserId() + ", tranTimestamp=" + this.getTranTimestamp() + ", authUserId=" + this.getAuthUserId() + ", terminalId=" + this.getTerminalId() + ", reversalTranType=" + this.getReversalTranType() + ", reversalDate=" + this.getReversalDate() + ", reversalTranTimestamp=" + this.getReversalTranTimestamp() + ", reversalUserId=" + this.getReversalUserId() + ", reversalAuthUserId=" + this.getReversalAuthUserId() + ", approvalDate=" + this.getApprovalDate() + ", apprUserId=" + this.getApprUserId() + ", apprAuthUserId=" + this.getApprAuthUserId() + ", bankSeqNo=" + this.getBankSeqNo() + ", reference=" + this.getReference() + ", traceRefNo=" + this.getTraceRefNo() + ", traceRefCode=" + this.getTraceRefCode() + ", status=" + this.getStatus() + ", company=" + this.getCompany() + ", taeSubSeqNo=" + this.getTaeSubSeqNo() + ", exchangeClass=" + this.getExchangeClass() + ", clientName=" + this.getClientName() + ", exchangeItemCode=" + this.getExchangeItemCode() + ", area=" + this.getArea() + ", documentType=" + this.getDocumentType() + ", documentId=" + this.getDocumentId() + ", exchangeReportType=" + this.getExchangeReportType() + ", exchangeTranCode=" + this.getExchangeTranCode() + ", exchangeTranCodet=" + this.getExchangeTranCodet() + ", exchangePurposeDetails=" + this.getExchangePurposeDetails() + ", exchangePurpose=" + this.getExchangePurpose() + ", busiNo=" + this.getBusiNo() + ")";
      }
   }
}
