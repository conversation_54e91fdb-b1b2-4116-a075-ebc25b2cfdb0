package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000250In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000250Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000250 {
   String URL = "/rb/inq/dc/balance";


   @ApiRemark("大额存单产品余额查询")
   @ApiDesc("大额存单产品余额查询")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0250"
   )
   @ConsumeSys("EOS")
   Core14000250Out runService(Core14000250In var1);
}
