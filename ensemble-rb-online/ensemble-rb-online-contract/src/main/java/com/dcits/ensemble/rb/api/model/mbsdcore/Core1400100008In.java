package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400100008In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400100008In.Body body;

   public Core1400100008In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400100008In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400100008In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100008In)) {
         return false;
      } else {
         Core1400100008In other = (Core1400100008In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100008In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "账户状态",
         notNull = false,
         length = "1",
         in = "N,H,A,D,S,O,P,C,R,I",
         inDesc = "N-新建,H-待激活,A-活动,D-睡眠,S-久悬,O-转营业外,P-逾期,C-关闭,I-预开户,R-预销户",
         remark = "描述账户生命周期不同阶段的划分",
         maxSize = 1
      )
      private String acctStatus;
      @V(
         desc = "开始日期",
         notNull = false,
         remark = "开始日期"
      )
      private String startDate;
      @V(
         desc = "结束日期",
         notNull = false,
         remark = "结束日期"
      )
      private String endDate;
      @V(
         desc = "查询所有信息（是否包含已销户状态）",
         notNull = false,
         length = "2",
         inDesc = "0-不包含已销户状态账户的交易信息,1-包含已销户状态账户的交易信息",
         remark = "查询所有信息（是否包含已销户状态）",
         maxSize = 2
      )
      private String statusFlag;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public String getAcctStatus() {
         return this.acctStatus;
      }

      public String getStartDate() {
         return this.startDate;
      }

      public String getEndDate() {
         return this.endDate;
      }

      public String getStatusFlag() {
         return this.statusFlag;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setAcctStatus(String acctStatus) {
         this.acctStatus = acctStatus;
      }

      public void setStartDate(String startDate) {
         this.startDate = startDate;
      }

      public void setEndDate(String endDate) {
         this.endDate = endDate;
      }

      public void setStatusFlag(String statusFlag) {
         this.statusFlag = statusFlag;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100008In.Body)) {
            return false;
         } else {
            Core1400100008In.Body other = (Core1400100008In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label119: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label119;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label119;
                  }

                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               label105: {
                  Object this$acctSeqNo = this.getAcctSeqNo();
                  Object other$acctSeqNo = other.getAcctSeqNo();
                  if (this$acctSeqNo == null) {
                     if (other$acctSeqNo == null) {
                        break label105;
                     }
                  } else if (this$acctSeqNo.equals(other$acctSeqNo)) {
                     break label105;
                  }

                  return false;
               }

               Object this$acctCcy = this.getAcctCcy();
               Object other$acctCcy = other.getAcctCcy();
               if (this$acctCcy == null) {
                  if (other$acctCcy != null) {
                     return false;
                  }
               } else if (!this$acctCcy.equals(other$acctCcy)) {
                  return false;
               }

               label91: {
                  Object this$clientNo = this.getClientNo();
                  Object other$clientNo = other.getClientNo();
                  if (this$clientNo == null) {
                     if (other$clientNo == null) {
                        break label91;
                     }
                  } else if (this$clientNo.equals(other$clientNo)) {
                     break label91;
                  }

                  return false;
               }

               Object this$acctStatus = this.getAcctStatus();
               Object other$acctStatus = other.getAcctStatus();
               if (this$acctStatus == null) {
                  if (other$acctStatus != null) {
                     return false;
                  }
               } else if (!this$acctStatus.equals(other$acctStatus)) {
                  return false;
               }

               label77: {
                  Object this$startDate = this.getStartDate();
                  Object other$startDate = other.getStartDate();
                  if (this$startDate == null) {
                     if (other$startDate == null) {
                        break label77;
                     }
                  } else if (this$startDate.equals(other$startDate)) {
                     break label77;
                  }

                  return false;
               }

               label70: {
                  Object this$endDate = this.getEndDate();
                  Object other$endDate = other.getEndDate();
                  if (this$endDate == null) {
                     if (other$endDate == null) {
                        break label70;
                     }
                  } else if (this$endDate.equals(other$endDate)) {
                     break label70;
                  }

                  return false;
               }

               Object this$statusFlag = this.getStatusFlag();
               Object other$statusFlag = other.getStatusFlag();
               if (this$statusFlag == null) {
                  if (other$statusFlag != null) {
                     return false;
                  }
               } else if (!this$statusFlag.equals(other$statusFlag)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100008In.Body;
      }
      public String toString() {
         return "Core1400100008In.Body(baseAcctNo=" + this.getBaseAcctNo() + ", prodType=" + this.getProdType() + ", acctSeqNo=" + this.getAcctSeqNo() + ", acctCcy=" + this.getAcctCcy() + ", clientNo=" + this.getClientNo() + ", acctStatus=" + this.getAcctStatus() + ", startDate=" + this.getStartDate() + ", endDate=" + this.getEndDate() + ", statusFlag=" + this.getStatusFlag() + ")";
      }
   }
}
