package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.util.List;

@MessageOut
public class Core1400069960Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400069960Out.AttachedBranchArray> attachedBranchArray;
   @V(
      desc = "本机构及下级机构信息",
      notNull = false,
      remark = "本机构及下级机构信息"
   )
   private List<Core1400069960Out.UnderBranchArray> underBranchArray;

   public List<Core1400069960Out.AttachedBranchArray> getAttachedBranchArray() {
      return this.attachedBranchArray;
   }

   public List<Core1400069960Out.UnderBranchArray> getUnderBranchArray() {
      return this.underBranchArray;
   }

   public void setAttachedBranchArray(List<Core1400069960Out.AttachedBranchArray> attachedBranchArray) {
      this.attachedBranchArray = attachedBranchArray;
   }

   public void setUnderBranchArray(List<Core1400069960Out.UnderBranchArray> underBranchArray) {
      this.underBranchArray = underBranchArray;
   }

   public String toString() {
      return "Core1400069960Out(attachedBranchArray=" + this.getAttachedBranchArray() + ", underBranchArray=" + this.getUnderBranchArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400069960Out)) {
         return false;
      } else {
         Core1400069960Out other = (Core1400069960Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$attachedBranchArray = this.getAttachedBranchArray();
            Object other$attachedBranchArray = other.getAttachedBranchArray();
            if (this$attachedBranchArray == null) {
               if (other$attachedBranchArray != null) {
                  return false;
               }
            } else if (!this$attachedBranchArray.equals(other$attachedBranchArray)) {
               return false;
            }

            Object this$underBranchArray = this.getUnderBranchArray();
            Object other$underBranchArray = other.getUnderBranchArray();
            if (this$underBranchArray == null) {
               if (other$underBranchArray != null) {
                  return false;
               }
            } else if (!this$underBranchArray.equals(other$underBranchArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400069960Out;
   }
   public static class UnderBranchArray {
      @V(
         desc = "所属机构号",
         notNull = false,
         length = "50",
         remark = "机构代码",
         maxSize = 50
      )
      private String branch;
      @V(
         desc = "机构名称",
         notNull = false,
         length = "200",
         remark = "机构名称",
         maxSize = 200
      )
      private String branchName;
      @V(
         desc = "上级机构",
         notNull = false,
         length = "50",
         remark = "上级机构",
         maxSize = 50
      )
      private String attachBranch;
      @V(
         desc = "上级机构名称",
         notNull = false,
         length = "50",
         remark = "上级机构名称",
         maxSize = 50
      )
      private String attachBranchName;
      @V(
         desc = "上级机构开关门状态",
         notNull = false,
         length = "1",
         remark = "上级机构开关门状态",
         maxSize = 1
      )
      private String attachBranchStatus;
      @V(
         desc = "机构开关门状态",
         notNull = false,
         length = "1",
         remark = "机构开关门状态",
         maxSize = 1
      )
      private String branchStatus;
      @V(
         desc = "国家",
         notNull = false,
         length = "3",
         remark = "国家",
         maxSize = 3
      )
      private String country;
      @V(
         desc = "区号",
         notNull = false,
         length = "10",
         remark = "区号",
         maxSize = 10
      )
      private String district;
      @V(
         desc = "城市",
         notNull = false,
         length = "10",
         remark = "城市",
         maxSize = 10
      )
      private String city;
      @V(
         desc = "省别代码",
         notNull = false,
         length = "10",
         remark = "省、州",
         maxSize = 10
      )
      private String state;
      @V(
         desc = "地址",
         notNull = false,
         length = "500",
         remark = "地址",
         maxSize = 500
      )
      private String address;
      @V(
         desc = "邮政编码",
         notNull = false,
         length = "10",
         remark = "邮政编码",
         maxSize = 10
      )
      private String postalCode;
      @V(
         desc = "联系人",
         notNull = false,
         length = "50",
         remark = "联系人",
         maxSize = 50
      )
      private String contactMan;
      @V(
         desc = "联系电话",
         notNull = false,
         length = "50",
         remark = "联系电话",
         maxSize = 50
      )
      private String contactTel;
      @V(
         desc = "层级代码",
         notNull = false,
         length = "50",
         remark = "层级代码",
         maxSize = 50
      )
      private String hierarchyCode;
      @V(
         desc = "法人",
         notNull = false,
         length = "20",
         remark = "法人",
         maxSize = 20
      )
      private String company;
      @V(
         desc = "是否交易机构",
         notNull = false,
         length = "1",
         remark = "表示当前机构是否是交易机构",
         maxSize = 1
      )
      private String tranBrInd;
      @V(
         desc = "开立日期",
         notNull = false,
         remark = "开立日期"
      )
      private String openDate;
      @V(
         desc = "生效日期",
         notNull = false,
         remark = "生效日期"
      )
      private String effectDate;

      public String getBranch() {
         return this.branch;
      }

      public String getBranchName() {
         return this.branchName;
      }

      public String getAttachBranch() {
         return this.attachBranch;
      }

      public String getAttachBranchName() {
         return this.attachBranchName;
      }

      public String getAttachBranchStatus() {
         return this.attachBranchStatus;
      }

      public String getBranchStatus() {
         return this.branchStatus;
      }

      public String getCountry() {
         return this.country;
      }

      public String getDistrict() {
         return this.district;
      }

      public String getCity() {
         return this.city;
      }

      public String getState() {
         return this.state;
      }

      public String getAddress() {
         return this.address;
      }

      public String getPostalCode() {
         return this.postalCode;
      }

      public String getContactMan() {
         return this.contactMan;
      }

      public String getContactTel() {
         return this.contactTel;
      }

      public String getHierarchyCode() {
         return this.hierarchyCode;
      }

      public String getCompany() {
         return this.company;
      }

      public String getTranBrInd() {
         return this.tranBrInd;
      }

      public String getOpenDate() {
         return this.openDate;
      }

      public String getEffectDate() {
         return this.effectDate;
      }

      public void setBranch(String branch) {
         this.branch = branch;
      }

      public void setBranchName(String branchName) {
         this.branchName = branchName;
      }

      public void setAttachBranch(String attachBranch) {
         this.attachBranch = attachBranch;
      }

      public void setAttachBranchName(String attachBranchName) {
         this.attachBranchName = attachBranchName;
      }

      public void setAttachBranchStatus(String attachBranchStatus) {
         this.attachBranchStatus = attachBranchStatus;
      }

      public void setBranchStatus(String branchStatus) {
         this.branchStatus = branchStatus;
      }

      public void setCountry(String country) {
         this.country = country;
      }

      public void setDistrict(String district) {
         this.district = district;
      }

      public void setCity(String city) {
         this.city = city;
      }

      public void setState(String state) {
         this.state = state;
      }

      public void setAddress(String address) {
         this.address = address;
      }

      public void setPostalCode(String postalCode) {
         this.postalCode = postalCode;
      }

      public void setContactMan(String contactMan) {
         this.contactMan = contactMan;
      }

      public void setContactTel(String contactTel) {
         this.contactTel = contactTel;
      }

      public void setHierarchyCode(String hierarchyCode) {
         this.hierarchyCode = hierarchyCode;
      }

      public void setCompany(String company) {
         this.company = company;
      }

      public void setTranBrInd(String tranBrInd) {
         this.tranBrInd = tranBrInd;
      }

      public void setOpenDate(String openDate) {
         this.openDate = openDate;
      }

      public void setEffectDate(String effectDate) {
         this.effectDate = effectDate;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400069960Out.UnderBranchArray)) {
            return false;
         } else {
            Core1400069960Out.UnderBranchArray other = (Core1400069960Out.UnderBranchArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label239: {
                  Object this$branch = this.getBranch();
                  Object other$branch = other.getBranch();
                  if (this$branch == null) {
                     if (other$branch == null) {
                        break label239;
                     }
                  } else if (this$branch.equals(other$branch)) {
                     break label239;
                  }

                  return false;
               }

               Object this$branchName = this.getBranchName();
               Object other$branchName = other.getBranchName();
               if (this$branchName == null) {
                  if (other$branchName != null) {
                     return false;
                  }
               } else if (!this$branchName.equals(other$branchName)) {
                  return false;
               }

               Object this$attachBranch = this.getAttachBranch();
               Object other$attachBranch = other.getAttachBranch();
               if (this$attachBranch == null) {
                  if (other$attachBranch != null) {
                     return false;
                  }
               } else if (!this$attachBranch.equals(other$attachBranch)) {
                  return false;
               }

               label218: {
                  Object this$attachBranchName = this.getAttachBranchName();
                  Object other$attachBranchName = other.getAttachBranchName();
                  if (this$attachBranchName == null) {
                     if (other$attachBranchName == null) {
                        break label218;
                     }
                  } else if (this$attachBranchName.equals(other$attachBranchName)) {
                     break label218;
                  }

                  return false;
               }

               label211: {
                  Object this$attachBranchStatus = this.getAttachBranchStatus();
                  Object other$attachBranchStatus = other.getAttachBranchStatus();
                  if (this$attachBranchStatus == null) {
                     if (other$attachBranchStatus == null) {
                        break label211;
                     }
                  } else if (this$attachBranchStatus.equals(other$attachBranchStatus)) {
                     break label211;
                  }

                  return false;
               }

               Object this$branchStatus = this.getBranchStatus();
               Object other$branchStatus = other.getBranchStatus();
               if (this$branchStatus == null) {
                  if (other$branchStatus != null) {
                     return false;
                  }
               } else if (!this$branchStatus.equals(other$branchStatus)) {
                  return false;
               }

               Object this$country = this.getCountry();
               Object other$country = other.getCountry();
               if (this$country == null) {
                  if (other$country != null) {
                     return false;
                  }
               } else if (!this$country.equals(other$country)) {
                  return false;
               }

               label190: {
                  Object this$district = this.getDistrict();
                  Object other$district = other.getDistrict();
                  if (this$district == null) {
                     if (other$district == null) {
                        break label190;
                     }
                  } else if (this$district.equals(other$district)) {
                     break label190;
                  }

                  return false;
               }

               label183: {
                  Object this$city = this.getCity();
                  Object other$city = other.getCity();
                  if (this$city == null) {
                     if (other$city == null) {
                        break label183;
                     }
                  } else if (this$city.equals(other$city)) {
                     break label183;
                  }

                  return false;
               }

               Object this$state = this.getState();
               Object other$state = other.getState();
               if (this$state == null) {
                  if (other$state != null) {
                     return false;
                  }
               } else if (!this$state.equals(other$state)) {
                  return false;
               }

               label169: {
                  Object this$address = this.getAddress();
                  Object other$address = other.getAddress();
                  if (this$address == null) {
                     if (other$address == null) {
                        break label169;
                     }
                  } else if (this$address.equals(other$address)) {
                     break label169;
                  }

                  return false;
               }

               Object this$postalCode = this.getPostalCode();
               Object other$postalCode = other.getPostalCode();
               if (this$postalCode == null) {
                  if (other$postalCode != null) {
                     return false;
                  }
               } else if (!this$postalCode.equals(other$postalCode)) {
                  return false;
               }

               label155: {
                  Object this$contactMan = this.getContactMan();
                  Object other$contactMan = other.getContactMan();
                  if (this$contactMan == null) {
                     if (other$contactMan == null) {
                        break label155;
                     }
                  } else if (this$contactMan.equals(other$contactMan)) {
                     break label155;
                  }

                  return false;
               }

               Object this$contactTel = this.getContactTel();
               Object other$contactTel = other.getContactTel();
               if (this$contactTel == null) {
                  if (other$contactTel != null) {
                     return false;
                  }
               } else if (!this$contactTel.equals(other$contactTel)) {
                  return false;
               }

               Object this$hierarchyCode = this.getHierarchyCode();
               Object other$hierarchyCode = other.getHierarchyCode();
               if (this$hierarchyCode == null) {
                  if (other$hierarchyCode != null) {
                     return false;
                  }
               } else if (!this$hierarchyCode.equals(other$hierarchyCode)) {
                  return false;
               }

               label134: {
                  Object this$company = this.getCompany();
                  Object other$company = other.getCompany();
                  if (this$company == null) {
                     if (other$company == null) {
                        break label134;
                     }
                  } else if (this$company.equals(other$company)) {
                     break label134;
                  }

                  return false;
               }

               label127: {
                  Object this$tranBrInd = this.getTranBrInd();
                  Object other$tranBrInd = other.getTranBrInd();
                  if (this$tranBrInd == null) {
                     if (other$tranBrInd == null) {
                        break label127;
                     }
                  } else if (this$tranBrInd.equals(other$tranBrInd)) {
                     break label127;
                  }

                  return false;
               }

               Object this$openDate = this.getOpenDate();
               Object other$openDate = other.getOpenDate();
               if (this$openDate == null) {
                  if (other$openDate != null) {
                     return false;
                  }
               } else if (!this$openDate.equals(other$openDate)) {
                  return false;
               }

               Object this$effectDate = this.getEffectDate();
               Object other$effectDate = other.getEffectDate();
               if (this$effectDate == null) {
                  if (other$effectDate != null) {
                     return false;
                  }
               } else if (!this$effectDate.equals(other$effectDate)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400069960Out.UnderBranchArray;
      }
      public String toString() {
         return "Core1400069960Out.UnderBranchArray(branch=" + this.getBranch() + ", branchName=" + this.getBranchName() + ", attachBranch=" + this.getAttachBranch() + ", attachBranchName=" + this.getAttachBranchName() + ", attachBranchStatus=" + this.getAttachBranchStatus() + ", branchStatus=" + this.getBranchStatus() + ", country=" + this.getCountry() + ", district=" + this.getDistrict() + ", city=" + this.getCity() + ", state=" + this.getState() + ", address=" + this.getAddress() + ", postalCode=" + this.getPostalCode() + ", contactMan=" + this.getContactMan() + ", contactTel=" + this.getContactTel() + ", hierarchyCode=" + this.getHierarchyCode() + ", company=" + this.getCompany() + ", tranBrInd=" + this.getTranBrInd() + ", openDate=" + this.getOpenDate() + ", effectDate=" + this.getEffectDate() + ")";
      }
   }

   public static class AttachedBranchArray {
      @V(
         desc = "所属机构号",
         notNull = false,
         length = "50",
         remark = "机构代码",
         maxSize = 50
      )
      private String branch;
      @V(
         desc = "机构名称",
         notNull = false,
         length = "200",
         remark = "机构名称",
         maxSize = 200
      )
      private String branchName;
      @V(
         desc = "上级机构",
         notNull = false,
         length = "50",
         remark = "上级机构",
         maxSize = 50
      )
      private String attachBranch;
      @V(
         desc = "上级机构名称",
         notNull = false,
         length = "50",
         remark = "上级机构名称",
         maxSize = 50
      )
      private String attachBranchName;
      @V(
         desc = "上级机构开关门状态",
         notNull = false,
         length = "1",
         remark = "上级机构开关门状态",
         maxSize = 1
      )
      private String attachBranchStatus;
      @V(
         desc = "机构开关门状态",
         notNull = false,
         length = "1",
         remark = "机构开关门状态",
         maxSize = 1
      )
      private String branchStatus;
      @V(
         desc = "国家",
         notNull = false,
         length = "3",
         remark = "国家",
         maxSize = 3
      )
      private String country;
      @V(
         desc = "区号",
         notNull = false,
         length = "10",
         remark = "区号",
         maxSize = 10
      )
      private String district;
      @V(
         desc = "城市",
         notNull = false,
         length = "10",
         remark = "城市",
         maxSize = 10
      )
      private String city;
      @V(
         desc = "省别代码",
         notNull = false,
         length = "10",
         remark = "省、州",
         maxSize = 10
      )
      private String state;
      @V(
         desc = "地址",
         notNull = false,
         length = "500",
         remark = "地址",
         maxSize = 500
      )
      private String address;
      @V(
         desc = "邮政编码",
         notNull = false,
         length = "10",
         remark = "邮政编码",
         maxSize = 10
      )
      private String postalCode;
      @V(
         desc = "联系人",
         notNull = false,
         length = "50",
         remark = "联系人",
         maxSize = 50
      )
      private String contactMan;
      @V(
         desc = "联系电话",
         notNull = false,
         length = "50",
         remark = "联系电话",
         maxSize = 50
      )
      private String contactTel;
      @V(
         desc = "层级代码",
         notNull = false,
         length = "50",
         remark = "层级代码",
         maxSize = 50
      )
      private String hierarchyCode;
      @V(
         desc = "法人",
         notNull = false,
         length = "20",
         remark = "法人",
         maxSize = 20
      )
      private String company;
      @V(
         desc = "是否交易机构",
         notNull = false,
         length = "1",
         remark = "表示当前机构是否是交易机构",
         maxSize = 1
      )
      private String tranBrInd;
      @V(
         desc = "开立日期",
         notNull = false,
         remark = "开立日期"
      )
      private String openDate;
      @V(
         desc = "生效日期",
         notNull = false,
         remark = "生效日期"
      )
      private String effectDate;

      public String getBranch() {
         return this.branch;
      }

      public String getBranchName() {
         return this.branchName;
      }

      public String getAttachBranch() {
         return this.attachBranch;
      }

      public String getAttachBranchName() {
         return this.attachBranchName;
      }

      public String getAttachBranchStatus() {
         return this.attachBranchStatus;
      }

      public String getBranchStatus() {
         return this.branchStatus;
      }

      public String getCountry() {
         return this.country;
      }

      public String getDistrict() {
         return this.district;
      }

      public String getCity() {
         return this.city;
      }

      public String getState() {
         return this.state;
      }

      public String getAddress() {
         return this.address;
      }

      public String getPostalCode() {
         return this.postalCode;
      }

      public String getContactMan() {
         return this.contactMan;
      }

      public String getContactTel() {
         return this.contactTel;
      }

      public String getHierarchyCode() {
         return this.hierarchyCode;
      }

      public String getCompany() {
         return this.company;
      }

      public String getTranBrInd() {
         return this.tranBrInd;
      }

      public String getOpenDate() {
         return this.openDate;
      }

      public String getEffectDate() {
         return this.effectDate;
      }

      public void setBranch(String branch) {
         this.branch = branch;
      }

      public void setBranchName(String branchName) {
         this.branchName = branchName;
      }

      public void setAttachBranch(String attachBranch) {
         this.attachBranch = attachBranch;
      }

      public void setAttachBranchName(String attachBranchName) {
         this.attachBranchName = attachBranchName;
      }

      public void setAttachBranchStatus(String attachBranchStatus) {
         this.attachBranchStatus = attachBranchStatus;
      }

      public void setBranchStatus(String branchStatus) {
         this.branchStatus = branchStatus;
      }

      public void setCountry(String country) {
         this.country = country;
      }

      public void setDistrict(String district) {
         this.district = district;
      }

      public void setCity(String city) {
         this.city = city;
      }

      public void setState(String state) {
         this.state = state;
      }

      public void setAddress(String address) {
         this.address = address;
      }

      public void setPostalCode(String postalCode) {
         this.postalCode = postalCode;
      }

      public void setContactMan(String contactMan) {
         this.contactMan = contactMan;
      }

      public void setContactTel(String contactTel) {
         this.contactTel = contactTel;
      }

      public void setHierarchyCode(String hierarchyCode) {
         this.hierarchyCode = hierarchyCode;
      }

      public void setCompany(String company) {
         this.company = company;
      }

      public void setTranBrInd(String tranBrInd) {
         this.tranBrInd = tranBrInd;
      }

      public void setOpenDate(String openDate) {
         this.openDate = openDate;
      }

      public void setEffectDate(String effectDate) {
         this.effectDate = effectDate;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400069960Out.AttachedBranchArray)) {
            return false;
         } else {
            Core1400069960Out.AttachedBranchArray other = (Core1400069960Out.AttachedBranchArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label239: {
                  Object this$branch = this.getBranch();
                  Object other$branch = other.getBranch();
                  if (this$branch == null) {
                     if (other$branch == null) {
                        break label239;
                     }
                  } else if (this$branch.equals(other$branch)) {
                     break label239;
                  }

                  return false;
               }

               Object this$branchName = this.getBranchName();
               Object other$branchName = other.getBranchName();
               if (this$branchName == null) {
                  if (other$branchName != null) {
                     return false;
                  }
               } else if (!this$branchName.equals(other$branchName)) {
                  return false;
               }

               Object this$attachBranch = this.getAttachBranch();
               Object other$attachBranch = other.getAttachBranch();
               if (this$attachBranch == null) {
                  if (other$attachBranch != null) {
                     return false;
                  }
               } else if (!this$attachBranch.equals(other$attachBranch)) {
                  return false;
               }

               label218: {
                  Object this$attachBranchName = this.getAttachBranchName();
                  Object other$attachBranchName = other.getAttachBranchName();
                  if (this$attachBranchName == null) {
                     if (other$attachBranchName == null) {
                        break label218;
                     }
                  } else if (this$attachBranchName.equals(other$attachBranchName)) {
                     break label218;
                  }

                  return false;
               }

               label211: {
                  Object this$attachBranchStatus = this.getAttachBranchStatus();
                  Object other$attachBranchStatus = other.getAttachBranchStatus();
                  if (this$attachBranchStatus == null) {
                     if (other$attachBranchStatus == null) {
                        break label211;
                     }
                  } else if (this$attachBranchStatus.equals(other$attachBranchStatus)) {
                     break label211;
                  }

                  return false;
               }

               Object this$branchStatus = this.getBranchStatus();
               Object other$branchStatus = other.getBranchStatus();
               if (this$branchStatus == null) {
                  if (other$branchStatus != null) {
                     return false;
                  }
               } else if (!this$branchStatus.equals(other$branchStatus)) {
                  return false;
               }

               Object this$country = this.getCountry();
               Object other$country = other.getCountry();
               if (this$country == null) {
                  if (other$country != null) {
                     return false;
                  }
               } else if (!this$country.equals(other$country)) {
                  return false;
               }

               label190: {
                  Object this$district = this.getDistrict();
                  Object other$district = other.getDistrict();
                  if (this$district == null) {
                     if (other$district == null) {
                        break label190;
                     }
                  } else if (this$district.equals(other$district)) {
                     break label190;
                  }

                  return false;
               }

               label183: {
                  Object this$city = this.getCity();
                  Object other$city = other.getCity();
                  if (this$city == null) {
                     if (other$city == null) {
                        break label183;
                     }
                  } else if (this$city.equals(other$city)) {
                     break label183;
                  }

                  return false;
               }

               Object this$state = this.getState();
               Object other$state = other.getState();
               if (this$state == null) {
                  if (other$state != null) {
                     return false;
                  }
               } else if (!this$state.equals(other$state)) {
                  return false;
               }

               label169: {
                  Object this$address = this.getAddress();
                  Object other$address = other.getAddress();
                  if (this$address == null) {
                     if (other$address == null) {
                        break label169;
                     }
                  } else if (this$address.equals(other$address)) {
                     break label169;
                  }

                  return false;
               }

               Object this$postalCode = this.getPostalCode();
               Object other$postalCode = other.getPostalCode();
               if (this$postalCode == null) {
                  if (other$postalCode != null) {
                     return false;
                  }
               } else if (!this$postalCode.equals(other$postalCode)) {
                  return false;
               }

               label155: {
                  Object this$contactMan = this.getContactMan();
                  Object other$contactMan = other.getContactMan();
                  if (this$contactMan == null) {
                     if (other$contactMan == null) {
                        break label155;
                     }
                  } else if (this$contactMan.equals(other$contactMan)) {
                     break label155;
                  }

                  return false;
               }

               Object this$contactTel = this.getContactTel();
               Object other$contactTel = other.getContactTel();
               if (this$contactTel == null) {
                  if (other$contactTel != null) {
                     return false;
                  }
               } else if (!this$contactTel.equals(other$contactTel)) {
                  return false;
               }

               Object this$hierarchyCode = this.getHierarchyCode();
               Object other$hierarchyCode = other.getHierarchyCode();
               if (this$hierarchyCode == null) {
                  if (other$hierarchyCode != null) {
                     return false;
                  }
               } else if (!this$hierarchyCode.equals(other$hierarchyCode)) {
                  return false;
               }

               label134: {
                  Object this$company = this.getCompany();
                  Object other$company = other.getCompany();
                  if (this$company == null) {
                     if (other$company == null) {
                        break label134;
                     }
                  } else if (this$company.equals(other$company)) {
                     break label134;
                  }

                  return false;
               }

               label127: {
                  Object this$tranBrInd = this.getTranBrInd();
                  Object other$tranBrInd = other.getTranBrInd();
                  if (this$tranBrInd == null) {
                     if (other$tranBrInd == null) {
                        break label127;
                     }
                  } else if (this$tranBrInd.equals(other$tranBrInd)) {
                     break label127;
                  }

                  return false;
               }

               Object this$openDate = this.getOpenDate();
               Object other$openDate = other.getOpenDate();
               if (this$openDate == null) {
                  if (other$openDate != null) {
                     return false;
                  }
               } else if (!this$openDate.equals(other$openDate)) {
                  return false;
               }

               Object this$effectDate = this.getEffectDate();
               Object other$effectDate = other.getEffectDate();
               if (this$effectDate == null) {
                  if (other$effectDate != null) {
                     return false;
                  }
               } else if (!this$effectDate.equals(other$effectDate)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400069960Out.AttachedBranchArray;
      }
      public String toString() {
         return "Core1400069960Out.AttachedBranchArray(branch=" + this.getBranch() + ", branchName=" + this.getBranchName() + ", attachBranch=" + this.getAttachBranch() + ", attachBranchName=" + this.getAttachBranchName() + ", attachBranchStatus=" + this.getAttachBranchStatus() + ", branchStatus=" + this.getBranchStatus() + ", country=" + this.getCountry() + ", district=" + this.getDistrict() + ", city=" + this.getCity() + ", state=" + this.getState() + ", address=" + this.getAddress() + ", postalCode=" + this.getPostalCode() + ", contactMan=" + this.getContactMan() + ", contactTel=" + this.getContactTel() + ", hierarchyCode=" + this.getHierarchyCode() + ", company=" + this.getCompany() + ", tranBrInd=" + this.getTranBrInd() + ", openDate=" + this.getOpenDate() + ", effectDate=" + this.getEffectDate() + ")";
      }
   }
}
