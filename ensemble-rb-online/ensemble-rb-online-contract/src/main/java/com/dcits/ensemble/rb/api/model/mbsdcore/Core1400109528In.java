package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400109528In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400109528In.Body body;

   public Core1400109528In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400109528In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400109528In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400109528In)) {
         return false;
      } else {
         Core1400109528In other = (Core1400109528In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400109528In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "备付金账户类型",
         notNull = false,
         length = "1",
         remark = "0-资金归集账户1-零余额账户",
         maxSize = 1
      )
      private String fundAcctType;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getFundAcctType() {
         return this.fundAcctType;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setFundAcctType(String fundAcctType) {
         this.fundAcctType = fundAcctType;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400109528In.Body)) {
            return false;
         } else {
            Core1400109528In.Body other = (Core1400109528In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo != null) {
                     return false;
                  }
               } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
                  return false;
               }

               Object this$fundAcctType = this.getFundAcctType();
               Object other$fundAcctType = other.getFundAcctType();
               if (this$fundAcctType == null) {
                  if (other$fundAcctType != null) {
                     return false;
                  }
               } else if (!this$fundAcctType.equals(other$fundAcctType)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400109528In.Body;
      }
      public String toString() {
         return "Core1400109528In.Body(baseAcctNo=" + this.getBaseAcctNo() + ", fundAcctType=" + this.getFundAcctType() + ")";
      }
   }
}
