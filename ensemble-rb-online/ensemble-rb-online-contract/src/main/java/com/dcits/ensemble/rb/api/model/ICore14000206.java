package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000206In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000206Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000206 {
   String URL = "/rb/inq/agreement/group";


   @ApiRemark("存款组签约解约查询")
   @ApiDesc("存款组签约解约查询")
   @EnsembleElements(
      serviceCode = "MbcdCore",
      messageType = "1400",
      messageCode = "0206"
   )
   @BusinessCategory("1400-查询")
   @FunctionCategory("RB05-协议管理")
   @ConsumeSys("CBS")
   @ApiUseStatus("PRODUCT-产品")
   Core14000206Out runService(Core14000206In var1);
}
