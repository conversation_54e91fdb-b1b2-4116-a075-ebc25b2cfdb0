package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400051702Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400051702Out.IeArray> ieArray;

   public List<Core1400051702Out.IeArray> getIeArray() {
      return this.ieArray;
   }

   public void setIeArray(List<Core1400051702Out.IeArray> ieArray) {
      this.ieArray = ieArray;
   }

   public String toString() {
      return "Core1400051702Out(ieArray=" + this.getIeArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400051702Out)) {
         return false;
      } else {
         Core1400051702Out other = (Core1400051702Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$ieArray = this.getIeArray();
            Object other$ieArray = other.getIeArray();
            if (this$ieArray == null) {
               if (other$ieArray != null) {
                  return false;
               }
            } else if (!this$ieArray.equals(other$ieArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400051702Out;
   }
   public static class IeArray {
      @V(
         desc = "收入总额",
         notNull = false,
         length = "17",
         remark = "收入总额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal totIncome;
      @V(
         desc = "支出总额",
         notNull = false,
         length = "17",
         remark = "支出总额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal totExpense;
      @V(
         desc = "交易月",
         notNull = false,
         length = "2",
         remark = "交易月",
         maxSize = 2
      )
      private String tranMonth;
      @V(
         desc = "法人",
         notNull = false,
         length = "20",
         remark = "法人",
         maxSize = 20
      )
      private String company;

      public BigDecimal getTotIncome() {
         return this.totIncome;
      }

      public BigDecimal getTotExpense() {
         return this.totExpense;
      }

      public String getTranMonth() {
         return this.tranMonth;
      }

      public String getCompany() {
         return this.company;
      }

      public void setTotIncome(BigDecimal totIncome) {
         this.totIncome = totIncome;
      }

      public void setTotExpense(BigDecimal totExpense) {
         this.totExpense = totExpense;
      }

      public void setTranMonth(String tranMonth) {
         this.tranMonth = tranMonth;
      }

      public void setCompany(String company) {
         this.company = company;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400051702Out.IeArray)) {
            return false;
         } else {
            Core1400051702Out.IeArray other = (Core1400051702Out.IeArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label59: {
                  Object this$totIncome = this.getTotIncome();
                  Object other$totIncome = other.getTotIncome();
                  if (this$totIncome == null) {
                     if (other$totIncome == null) {
                        break label59;
                     }
                  } else if (this$totIncome.equals(other$totIncome)) {
                     break label59;
                  }

                  return false;
               }

               Object this$totExpense = this.getTotExpense();
               Object other$totExpense = other.getTotExpense();
               if (this$totExpense == null) {
                  if (other$totExpense != null) {
                     return false;
                  }
               } else if (!this$totExpense.equals(other$totExpense)) {
                  return false;
               }

               Object this$tranMonth = this.getTranMonth();
               Object other$tranMonth = other.getTranMonth();
               if (this$tranMonth == null) {
                  if (other$tranMonth != null) {
                     return false;
                  }
               } else if (!this$tranMonth.equals(other$tranMonth)) {
                  return false;
               }

               Object this$company = this.getCompany();
               Object other$company = other.getCompany();
               if (this$company == null) {
                  if (other$company != null) {
                     return false;
                  }
               } else if (!this$company.equals(other$company)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400051702Out.IeArray;
      }
      public String toString() {
         return "Core1400051702Out.IeArray(totIncome=" + this.getTotIncome() + ", totExpense=" + this.getTotExpense() + ", tranMonth=" + this.getTranMonth() + ", company=" + this.getCompany() + ")";
      }
   }
}
