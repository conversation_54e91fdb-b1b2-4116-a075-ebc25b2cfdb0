package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400049078In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400049078In.Body body;

   public Core1400049078In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400049078In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400049078In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400049078In)) {
         return false;
      } else {
         Core1400049078In other = (Core1400049078In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400049078In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "客户中文名称",
         notNull = false,
         length = "200",
         remark = "客户中文名称",
         maxSize = 200
      )
      private String chClientName;
      @V(
         desc = "客户名称",
         notNull = false,
         length = "200",
         remark = "客户名称",
         maxSize = 200
      )
      private String clientName;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "开始日期",
         notNull = false,
         remark = "开始日期"
      )
      private String startDate;
      @V(
         desc = "结束日期",
         notNull = false,
         remark = "结束日期"
      )
      private String endDate;
      @V(
         desc = "所属机构号",
         notNull = false,
         length = "50",
         remark = "机构代码",
         maxSize = 50
      )
      private String branch;
      @V(
         desc = "交易参考号",
         notNull = false,
         length = "50",
         remark = "交易参考号",
         maxSize = 50
      )
      private String reference;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;

      public String getChClientName() {
         return this.chClientName;
      }

      public String getClientName() {
         return this.clientName;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getStartDate() {
         return this.startDate;
      }

      public String getEndDate() {
         return this.endDate;
      }

      public String getBranch() {
         return this.branch;
      }

      public String getReference() {
         return this.reference;
      }

      public String getCcy() {
         return this.ccy;
      }

      public void setChClientName(String chClientName) {
         this.chClientName = chClientName;
      }

      public void setClientName(String clientName) {
         this.clientName = clientName;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setStartDate(String startDate) {
         this.startDate = startDate;
      }

      public void setEndDate(String endDate) {
         this.endDate = endDate;
      }

      public void setBranch(String branch) {
         this.branch = branch;
      }

      public void setReference(String reference) {
         this.reference = reference;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400049078In.Body)) {
            return false;
         } else {
            Core1400049078In.Body other = (Core1400049078In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$chClientName = this.getChClientName();
               Object other$chClientName = other.getChClientName();
               if (this$chClientName == null) {
                  if (other$chClientName != null) {
                     return false;
                  }
               } else if (!this$chClientName.equals(other$chClientName)) {
                  return false;
               }

               Object this$clientName = this.getClientName();
               Object other$clientName = other.getClientName();
               if (this$clientName == null) {
                  if (other$clientName != null) {
                     return false;
                  }
               } else if (!this$clientName.equals(other$clientName)) {
                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               label110: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label110;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label110;
                  }

                  return false;
               }

               label103: {
                  Object this$acctSeqNo = this.getAcctSeqNo();
                  Object other$acctSeqNo = other.getAcctSeqNo();
                  if (this$acctSeqNo == null) {
                     if (other$acctSeqNo == null) {
                        break label103;
                     }
                  } else if (this$acctSeqNo.equals(other$acctSeqNo)) {
                     break label103;
                  }

                  return false;
               }

               Object this$startDate = this.getStartDate();
               Object other$startDate = other.getStartDate();
               if (this$startDate == null) {
                  if (other$startDate != null) {
                     return false;
                  }
               } else if (!this$startDate.equals(other$startDate)) {
                  return false;
               }

               label89: {
                  Object this$endDate = this.getEndDate();
                  Object other$endDate = other.getEndDate();
                  if (this$endDate == null) {
                     if (other$endDate == null) {
                        break label89;
                     }
                  } else if (this$endDate.equals(other$endDate)) {
                     break label89;
                  }

                  return false;
               }

               label82: {
                  Object this$branch = this.getBranch();
                  Object other$branch = other.getBranch();
                  if (this$branch == null) {
                     if (other$branch == null) {
                        break label82;
                     }
                  } else if (this$branch.equals(other$branch)) {
                     break label82;
                  }

                  return false;
               }

               Object this$reference = this.getReference();
               Object other$reference = other.getReference();
               if (this$reference == null) {
                  if (other$reference != null) {
                     return false;
                  }
               } else if (!this$reference.equals(other$reference)) {
                  return false;
               }

               Object this$ccy = this.getCcy();
               Object other$ccy = other.getCcy();
               if (this$ccy == null) {
                  if (other$ccy != null) {
                     return false;
                  }
               } else if (!this$ccy.equals(other$ccy)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400049078In.Body;
      }
      public String toString() {
         return "Core1400049078In.Body(chClientName=" + this.getChClientName() + ", clientName=" + this.getClientName() + ", prodType=" + this.getProdType() + ", baseAcctNo=" + this.getBaseAcctNo() + ", acctSeqNo=" + this.getAcctSeqNo() + ", startDate=" + this.getStartDate() + ", endDate=" + this.getEndDate() + ", branch=" + this.getBranch() + ", reference=" + this.getReference() + ", ccy=" + this.getCcy() + ")";
      }
   }
}
