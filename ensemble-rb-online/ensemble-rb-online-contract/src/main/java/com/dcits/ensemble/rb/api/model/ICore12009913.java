package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12009913In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12009913Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12009913 {
   String URL = "/rb/nfin/tailbox/sign/out";


   @ApiDesc("该接口本质上进行柜员的尾箱上缴处理，支持将别人的指定尾箱脱离，不提供签退检查功能。正式签退提交和强制签退提交，都需要调用该接口。为了支持强制签退，机构代码和柜员编号，从上送接口取得，不获取系统头数据。")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "9913"
   )
   @BusinessCategory("1200-非金融")
   @FunctionCategory("RB01-公共服务")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core12009913Out runService(Core12009913In var1);
}
