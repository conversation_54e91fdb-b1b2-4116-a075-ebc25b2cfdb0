package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000412In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000412Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000412 {
   String URL = "/rb/inq/unc/tran/type";


   @ApiRemark("根据tranType查询RB_EXCHANGE_TRAN_TYPE")
   @ApiDesc("根据tranType查询RB_EXCHANGE_TRAN_TYPE")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0412"
   )
   Core14000412Out runService(Core14000412In var1);
}
