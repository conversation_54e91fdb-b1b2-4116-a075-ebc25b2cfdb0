package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000139In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000139Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000139 {
   String URL = "/rb/inq/interest/cycled";


   @ApiRemark("往期利率信息查询")
   @ApiDesc("往期利率查询")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0139"
   )
   @BusinessCategory("1400-查询")
   @FunctionCategory("RB04-计结息")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core14000139Out runService(Core14000139In var1);
}
