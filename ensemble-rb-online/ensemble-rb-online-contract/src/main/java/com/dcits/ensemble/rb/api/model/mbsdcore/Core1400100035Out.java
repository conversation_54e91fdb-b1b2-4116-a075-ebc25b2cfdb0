package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400100035Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100035Out.LimitArray> limitArray;

   public List<Core1400100035Out.LimitArray> getLimitArray() {
      return this.limitArray;
   }

   public void setLimitArray(List<Core1400100035Out.LimitArray> limitArray) {
      this.limitArray = limitArray;
   }

   public String toString() {
      return "Core1400100035Out(limitArray=" + this.getLimitArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100035Out)) {
         return false;
      } else {
         Core1400100035Out other = (Core1400100035Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$limitArray = this.getLimitArray();
            Object other$limitArray = other.getLimitArray();
            if (this$limitArray == null) {
               if (other$limitArray != null) {
                  return false;
               }
            } else if (!this$limitArray.equals(other$limitArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100035Out;
   }
   public static class LimitArray {
      @V(
         desc = "交易流水识别号",
         notNull = false,
         length = "50",
         remark = "交易流水识别号",
         maxSize = 50
      )
      private String tradeNo;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "账户名称",
         notNull = false,
         length = "200",
         remark = "账户名称，一般指中文账户名称",
         maxSize = 200
      )
      private String acctName;
      @V(
         desc = "总额度",
         notNull = false,
         length = "17",
         remark = "总额度",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal totalLimit;
      @V(
         desc = "已结汇人民币金额",
         notNull = false,
         length = "17",
         remark = "已结汇人民币金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal settlementEdAmt;
      @V(
         desc = "已结汇币种",
         notNull = false,
         length = "3",
         remark = "已结汇币种",
         maxSize = 3
      )
      private String settlementEdCcy;
      @V(
         desc = "已结汇金额",
         notNull = false,
         length = "17",
         remark = "已结汇金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal settlementEdOthAmt;
      @V(
         desc = "支出金额",
         notNull = false,
         length = "17",
         remark = "支出金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal expenses;
      @V(
         desc = "待支付金额",
         notNull = false,
         length = "17",
         remark = "待支付金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal dueAmount;

      public String getTradeNo() {
         return this.tradeNo;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getAcctName() {
         return this.acctName;
      }

      public BigDecimal getTotalLimit() {
         return this.totalLimit;
      }

      public BigDecimal getSettlementEdAmt() {
         return this.settlementEdAmt;
      }

      public String getSettlementEdCcy() {
         return this.settlementEdCcy;
      }

      public BigDecimal getSettlementEdOthAmt() {
         return this.settlementEdOthAmt;
      }

      public BigDecimal getExpenses() {
         return this.expenses;
      }

      public BigDecimal getDueAmount() {
         return this.dueAmount;
      }

      public void setTradeNo(String tradeNo) {
         this.tradeNo = tradeNo;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setAcctName(String acctName) {
         this.acctName = acctName;
      }

      public void setTotalLimit(BigDecimal totalLimit) {
         this.totalLimit = totalLimit;
      }

      public void setSettlementEdAmt(BigDecimal settlementEdAmt) {
         this.settlementEdAmt = settlementEdAmt;
      }

      public void setSettlementEdCcy(String settlementEdCcy) {
         this.settlementEdCcy = settlementEdCcy;
      }

      public void setSettlementEdOthAmt(BigDecimal settlementEdOthAmt) {
         this.settlementEdOthAmt = settlementEdOthAmt;
      }

      public void setExpenses(BigDecimal expenses) {
         this.expenses = expenses;
      }

      public void setDueAmount(BigDecimal dueAmount) {
         this.dueAmount = dueAmount;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100035Out.LimitArray)) {
            return false;
         } else {
            Core1400100035Out.LimitArray other = (Core1400100035Out.LimitArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label119: {
                  Object this$tradeNo = this.getTradeNo();
                  Object other$tradeNo = other.getTradeNo();
                  if (this$tradeNo == null) {
                     if (other$tradeNo == null) {
                        break label119;
                     }
                  } else if (this$tradeNo.equals(other$tradeNo)) {
                     break label119;
                  }

                  return false;
               }

               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo != null) {
                     return false;
                  }
               } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
                  return false;
               }

               label105: {
                  Object this$acctName = this.getAcctName();
                  Object other$acctName = other.getAcctName();
                  if (this$acctName == null) {
                     if (other$acctName == null) {
                        break label105;
                     }
                  } else if (this$acctName.equals(other$acctName)) {
                     break label105;
                  }

                  return false;
               }

               Object this$totalLimit = this.getTotalLimit();
               Object other$totalLimit = other.getTotalLimit();
               if (this$totalLimit == null) {
                  if (other$totalLimit != null) {
                     return false;
                  }
               } else if (!this$totalLimit.equals(other$totalLimit)) {
                  return false;
               }

               label91: {
                  Object this$settlementEdAmt = this.getSettlementEdAmt();
                  Object other$settlementEdAmt = other.getSettlementEdAmt();
                  if (this$settlementEdAmt == null) {
                     if (other$settlementEdAmt == null) {
                        break label91;
                     }
                  } else if (this$settlementEdAmt.equals(other$settlementEdAmt)) {
                     break label91;
                  }

                  return false;
               }

               Object this$settlementEdCcy = this.getSettlementEdCcy();
               Object other$settlementEdCcy = other.getSettlementEdCcy();
               if (this$settlementEdCcy == null) {
                  if (other$settlementEdCcy != null) {
                     return false;
                  }
               } else if (!this$settlementEdCcy.equals(other$settlementEdCcy)) {
                  return false;
               }

               label77: {
                  Object this$settlementEdOthAmt = this.getSettlementEdOthAmt();
                  Object other$settlementEdOthAmt = other.getSettlementEdOthAmt();
                  if (this$settlementEdOthAmt == null) {
                     if (other$settlementEdOthAmt == null) {
                        break label77;
                     }
                  } else if (this$settlementEdOthAmt.equals(other$settlementEdOthAmt)) {
                     break label77;
                  }

                  return false;
               }

               label70: {
                  Object this$expenses = this.getExpenses();
                  Object other$expenses = other.getExpenses();
                  if (this$expenses == null) {
                     if (other$expenses == null) {
                        break label70;
                     }
                  } else if (this$expenses.equals(other$expenses)) {
                     break label70;
                  }

                  return false;
               }

               Object this$dueAmount = this.getDueAmount();
               Object other$dueAmount = other.getDueAmount();
               if (this$dueAmount == null) {
                  if (other$dueAmount != null) {
                     return false;
                  }
               } else if (!this$dueAmount.equals(other$dueAmount)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100035Out.LimitArray;
      }
      public String toString() {
         return "Core1400100035Out.LimitArray(tradeNo=" + this.getTradeNo() + ", baseAcctNo=" + this.getBaseAcctNo() + ", acctName=" + this.getAcctName() + ", totalLimit=" + this.getTotalLimit() + ", settlementEdAmt=" + this.getSettlementEdAmt() + ", settlementEdCcy=" + this.getSettlementEdCcy() + ", settlementEdOthAmt=" + this.getSettlementEdOthAmt() + ", expenses=" + this.getExpenses() + ", dueAmount=" + this.getDueAmount() + ")";
      }
   }
}
