package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400061085Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "远期结售汇交易日登记簿数组",
      notNull = false,
      remark = "远期结售汇交易日登记簿数组"
   )
   private List<Core1400061085Out.ExchangeDistantRegList> exchangeDistantRegList;

   public List<Core1400061085Out.ExchangeDistantRegList> getExchangeDistantRegList() {
      return this.exchangeDistantRegList;
   }

   public void setExchangeDistantRegList(List<Core1400061085Out.ExchangeDistantRegList> exchangeDistantRegList) {
      this.exchangeDistantRegList = exchangeDistantRegList;
   }

   public String toString() {
      return "Core1400061085Out(exchangeDistantRegList=" + this.getExchangeDistantRegList() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400061085Out)) {
         return false;
      } else {
         Core1400061085Out other = (Core1400061085Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$exchangeDistantRegList = this.getExchangeDistantRegList();
            Object other$exchangeDistantRegList = other.getExchangeDistantRegList();
            if (this$exchangeDistantRegList == null) {
               if (other$exchangeDistantRegList != null) {
                  return false;
               }
            } else if (!this$exchangeDistantRegList.equals(other$exchangeDistantRegList)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400061085Out;
   }
   public static class ExchangeDistantRegList {
      @V(
         desc = "保证金账号",
         notNull = false,
         length = "50",
         remark = "保证金账号",
         maxSize = 50
      )
      private String depBaseAcctNo;
      @V(
         desc = "交易日期",
         notNull = false,
         remark = "交易日期"
      )
      private String tranDate;
      @V(
         desc = "兑换类型",
         notNull = false,
         length = "1",
         remark = "兑换类型",
         maxSize = 1
      )
      private String exType;
      @V(
         desc = "操作类型",
         notNull = false,
         length = "2",
         remark = "操作类型",
         maxSize = 2
      )
      private String options;
      @V(
         desc = "交割方式",
         notNull = false,
         length = "1",
         remark = "远期结售汇交割方式",
         maxSize = 1
      )
      private String deliveryMethod;
      @V(
         desc = "固定交割日",
         notNull = false,
         remark = "固定交割日"
      )
      private String fixDealDate;
      @V(
         desc = "择期交割日",
         notNull = false,
         remark = "择期交割日"
      )
      private String changeDealDate;
      @V(
         desc = "交易对手",
         notNull = false,
         length = "1",
         remark = "交易对手",
         maxSize = 1
      )
      private String dealRecAcct;
      @V(
         desc = "业务编号",
         notNull = false,
         length = "50",
         remark = "业务编号",
         maxSize = 50
      )
      private String busiNo;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "客户中文名称",
         notNull = false,
         length = "200",
         remark = "客户中文名称",
         maxSize = 200
      )
      private String chClientName;
      @V(
         desc = "客户英文名称",
         notNull = false,
         length = "200",
         remark = "客户英文名称",
         maxSize = 200
      )
      private String enClientName;
      @V(
         desc = "外币币种",
         notNull = false,
         length = "3",
         remark = "外币币种",
         maxSize = 3
      )
      private String foreCcy;
      @V(
         desc = "外币账号",
         notNull = false,
         length = "50",
         remark = "外币账号",
         maxSize = 50
      )
      private String foreBaseAcctNo;
      @V(
         desc = "外币账户序号",
         notNull = false,
         length = "5",
         remark = "外币账户序号",
         maxSize = 5
      )
      private String foreAcctSeqNo;
      @V(
         desc = "人民币账户",
         notNull = false,
         length = "50",
         remark = "人名币账户",
         maxSize = 50
      )
      private String cnyBaseAcctNo;
      @V(
         desc = "人民币账户序号",
         notNull = false,
         length = "5",
         remark = "人名币账户序号",
         maxSize = 5
      )
      private String cnyAcctSeqNo;
      @V(
         desc = "我行对客户报价",
         notNull = false,
         length = "25",
         remark = "我行对客户报价",
         decimalLength = 10,
         precision = 10
      )
      private BigDecimal offerLetter;
      @V(
         desc = "外币金额",
         notNull = false,
         length = "17",
         remark = "外币金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal foreAmt;
      @V(
         desc = "人民币金额",
         notNull = false,
         length = "17",
         remark = "人名币金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal cnyAmt;
      @V(
         desc = "占用额度币种",
         notNull = false,
         length = "3",
         remark = "占用额度币种",
         maxSize = 3
      )
      private String occCcy;
      @V(
         desc = "占用额度金额",
         notNull = false,
         length = "17",
         remark = "占用额度金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal occAmt;
      @V(
         desc = "保证金币种",
         notNull = false,
         length = "3",
         remark = "保证金币种",
         maxSize = 3
      )
      private String depCcy;
      @V(
         desc = "当日存入金额",
         notNull = false,
         length = "17",
         remark = "当日存入金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal depAmt;
      @V(
         desc = "保证金账号序号",
         notNull = false,
         length = "5",
         remark = "保证金账户序号",
         maxSize = 5
      )
      private String depAcctSeqNo;
      @V(
         desc = "合作行名称",
         notNull = false,
         length = "50",
         remark = "合作行名称",
         maxSize = 50
      )
      private String teamBankName;
      @V(
         desc = "合作行报价",
         notNull = false,
         length = "25",
         remark = "合作行报价",
         decimalLength = 10,
         precision = 10
      )
      private BigDecimal teamBankRate;
      @V(
         desc = "损失外币金额",
         notNull = false,
         length = "17",
         remark = "损失外币金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal lossForeAmt;
      @V(
         desc = "损失人民币金额",
         notNull = false,
         length = "17",
         remark = "损失人名币金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal lossCnyAmt;
      @V(
         desc = "初次预警",
         notNull = false,
         length = "1",
         remark = "0-未触发 1-补缴保证金",
         maxSize = 1
      )
      private String waring;
      @V(
         desc = "再次预警",
         notNull = false,
         length = "1",
         remark = "再次预警 0-未达到 1-强制平仓",
         maxSize = 1
      )
      private String thenWaring;
      @V(
         desc = "追缴保证金冻结编号",
         notNull = false,
         length = "200",
         remark = "追缴保证金冻结编号",
         maxSize = 200
      )
      private String depBlockNo;
      @V(
         desc = "交割或特殊处理日期",
         notNull = false,
         remark = "交割或特殊处理日期"
      )
      private String exTranDate;
      @V(
         desc = "交割或特殊处理方式",
         notNull = false,
         length = "2",
         remark = "交割或特殊处理方式",
         maxSize = 2
      )
      private String exDealType;
      @V(
         desc = "交割机构",
         notNull = false,
         length = "20",
         remark = "交割机构",
         maxSize = 20
      )
      private String exBranch;
      @V(
         desc = "交割柜员",
         notNull = false,
         length = "30",
         remark = "交割柜员",
         maxSize = 30
      )
      private String exUserId;
      @V(
         desc = "交割交易金额",
         notNull = false,
         length = "17",
         remark = "交割交易金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal exTranAmt;
      @V(
         desc = "待清算过渡账户（外币）",
         notNull = false,
         length = "50",
         remark = "待清算过渡账户（外币）",
         maxSize = 50
      )
      private String toClearAcctNoFore;
      @V(
         desc = "待清算过度账户",
         notNull = false,
         length = "50",
         remark = "待清算过度账户（人名币）",
         maxSize = 50
      )
      private String toClearAcctNoCny;
      @V(
         desc = "清算账户",
         notNull = false,
         length = "50",
         remark = "清算账户",
         maxSize = 50
      )
      private String clearAcctNo;
      @V(
         desc = "提前交割报价",
         notNull = false,
         length = "17",
         remark = "提前交割报价",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal beforeExRate;
      @V(
         desc = "损失金额",
         notNull = false,
         length = "17",
         remark = "损失金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal lossAmt;
      @V(
         desc = "收入金额",
         notNull = false,
         length = "17",
         remark = "收入金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal incomeAmt;
      @V(
         desc = "盈余 损扣账金额",
         notNull = false,
         length = "17",
         remark = "盈余损扣账金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal deductAmt;
      @V(
         desc = "远端对冲汇率",
         notNull = false,
         length = "17",
         remark = "远端对冲汇率",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal farRate;
      @V(
         desc = "近端对冲汇率",
         notNull = false,
         length = "15",
         remark = "近端对冲汇率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal nearRate;
      @V(
         desc = "展期业务编号",
         notNull = false,
         length = "30",
         remark = "展期业务编号",
         maxSize = 30
      )
      private String deferBusiNo;
      @V(
         desc = "展期交易币种",
         notNull = false,
         length = "3",
         remark = "展期交易币种",
         maxSize = 3
      )
      private String deferCcy;
      @V(
         desc = "展期交易金额",
         notNull = false,
         length = "17",
         remark = "展期交易金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal deferTranAmt;
      @V(
         desc = "展期约定报价",
         notNull = false,
         length = "17",
         remark = "展期约定报价",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal deferRate;
      @V(
         desc = "展期交割方式",
         notNull = false,
         length = "2",
         remark = "展期交割方式",
         maxSize = 2
      )
      private String deferDealType;
      @V(
         desc = "展期固定交割日",
         notNull = false,
         remark = "展期固定交割日"
      )
      private String deferFixDate;
      @V(
         desc = "展期择期交割日",
         notNull = false,
         remark = "展期择期交割日"
      )
      private String deferChangeDate;
      @V(
         desc = "展期择期交割结束日",
         notNull = false,
         remark = "展期择期交割结束日"
      )
      private String deferChangeDateEnd;
      @V(
         desc = "所属机构号",
         notNull = false,
         length = "50",
         remark = "所属机构号",
         maxSize = 50
      )
      private String branch;
      @V(
         desc = "违约金额",
         notNull = false,
         length = "17",
         remark = "违约金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal preAmt;
      @V(
         desc = "违约报价",
         notNull = false,
         length = "17",
         remark = "违约报价",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal preOffer;
      @V(
         desc = "违约交易日期",
         notNull = false,
         remark = "违约交易日期"
      )
      private String preTranDate;
      @V(
         desc = "担保方式",
         notNull = false,
         length = "5",
         remark = "担保方式",
         maxSize = 5
      )
      private String guarantyStyle;
      @V(
         desc = "渠道流水号",
         notNull = false,
         length = "50",
         remark = "渠道流水号",
         maxSize = 50
      )
      private String channelSeqNo;
      @V(
         desc = "去介质登记状态",
         notNull = false,
         length = "1",
         remark = "去介质登记状态",
         maxSize = 1
      )
      private String regStatus;
      @V(
         desc = "剩余交割金额",
         notNull = false,
         length = "17",
         remark = "剩余交割金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal remainExAmt;
      @V(
         desc = "交割清算全局流水号(对客)",
         notNull = false,
         length = "50",
         remark = "交割清算全局流水号(对客)",
         maxSize = 50
      )
      private String channelSeqNoClient;
      @V(
         desc = "交易日登记全局流水号(对客)",
         notNull = false,
         length = "50",
         remark = "交易日登记全局流水号(对客)",
         maxSize = 50
      )
      private String recordChannelSeqNoClient;
      @V(
         desc = "交割清算全局流水号(对合作行)",
         notNull = false,
         length = "50",
         remark = "交割清算全局流水号(对合作行)",
         maxSize = 50
      )
      private String channelSeqNoBank;
      @V(
         desc = "交易日登记全局流水号(对合作行)",
         notNull = false,
         length = "50",
         remark = "交易日登记全局流水号(对合作行)",
         maxSize = 50
      )
      private String recordChannelSeqNoBank;
      @V(
         desc = "保证金冻结日期",
         notNull = false,
         remark = "保证金冻结日期"
      )
      private String depTranDate;
      @V(
         desc = "相关业务类型",
         notNull = false,
         length = "20",
         remark = "相关业务类型",
         maxSize = 20
      )
      private String exBusiType;
      @V(
         desc = "择期交割结束日",
         notNull = false,
         remark = "择期交割结束日"
      )
      private String changeDealDateEnd;
      @V(
         desc = "存入账户产品类型",
         notNull = false,
         length = "10",
         remark = "存入账户产品类型",
         maxSize = 10
      )
      private String depProdType;

      public String getDepBaseAcctNo() {
         return this.depBaseAcctNo;
      }

      public String getTranDate() {
         return this.tranDate;
      }

      public String getExType() {
         return this.exType;
      }

      public String getOptions() {
         return this.options;
      }

      public String getDeliveryMethod() {
         return this.deliveryMethod;
      }

      public String getFixDealDate() {
         return this.fixDealDate;
      }

      public String getChangeDealDate() {
         return this.changeDealDate;
      }

      public String getDealRecAcct() {
         return this.dealRecAcct;
      }

      public String getBusiNo() {
         return this.busiNo;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public String getChClientName() {
         return this.chClientName;
      }

      public String getEnClientName() {
         return this.enClientName;
      }

      public String getForeCcy() {
         return this.foreCcy;
      }

      public String getForeBaseAcctNo() {
         return this.foreBaseAcctNo;
      }

      public String getForeAcctSeqNo() {
         return this.foreAcctSeqNo;
      }

      public String getCnyBaseAcctNo() {
         return this.cnyBaseAcctNo;
      }

      public String getCnyAcctSeqNo() {
         return this.cnyAcctSeqNo;
      }

      public BigDecimal getOfferLetter() {
         return this.offerLetter;
      }

      public BigDecimal getForeAmt() {
         return this.foreAmt;
      }

      public BigDecimal getCnyAmt() {
         return this.cnyAmt;
      }

      public String getOccCcy() {
         return this.occCcy;
      }

      public BigDecimal getOccAmt() {
         return this.occAmt;
      }

      public String getDepCcy() {
         return this.depCcy;
      }

      public BigDecimal getDepAmt() {
         return this.depAmt;
      }

      public String getDepAcctSeqNo() {
         return this.depAcctSeqNo;
      }

      public String getTeamBankName() {
         return this.teamBankName;
      }

      public BigDecimal getTeamBankRate() {
         return this.teamBankRate;
      }

      public BigDecimal getLossForeAmt() {
         return this.lossForeAmt;
      }

      public BigDecimal getLossCnyAmt() {
         return this.lossCnyAmt;
      }

      public String getWaring() {
         return this.waring;
      }

      public String getThenWaring() {
         return this.thenWaring;
      }

      public String getDepBlockNo() {
         return this.depBlockNo;
      }

      public String getExTranDate() {
         return this.exTranDate;
      }

      public String getExDealType() {
         return this.exDealType;
      }

      public String getExBranch() {
         return this.exBranch;
      }

      public String getExUserId() {
         return this.exUserId;
      }

      public BigDecimal getExTranAmt() {
         return this.exTranAmt;
      }

      public String getToClearAcctNoFore() {
         return this.toClearAcctNoFore;
      }

      public String getToClearAcctNoCny() {
         return this.toClearAcctNoCny;
      }

      public String getClearAcctNo() {
         return this.clearAcctNo;
      }

      public BigDecimal getBeforeExRate() {
         return this.beforeExRate;
      }

      public BigDecimal getLossAmt() {
         return this.lossAmt;
      }

      public BigDecimal getIncomeAmt() {
         return this.incomeAmt;
      }

      public BigDecimal getDeductAmt() {
         return this.deductAmt;
      }

      public BigDecimal getFarRate() {
         return this.farRate;
      }

      public BigDecimal getNearRate() {
         return this.nearRate;
      }

      public String getDeferBusiNo() {
         return this.deferBusiNo;
      }

      public String getDeferCcy() {
         return this.deferCcy;
      }

      public BigDecimal getDeferTranAmt() {
         return this.deferTranAmt;
      }

      public BigDecimal getDeferRate() {
         return this.deferRate;
      }

      public String getDeferDealType() {
         return this.deferDealType;
      }

      public String getDeferFixDate() {
         return this.deferFixDate;
      }

      public String getDeferChangeDate() {
         return this.deferChangeDate;
      }

      public String getDeferChangeDateEnd() {
         return this.deferChangeDateEnd;
      }

      public String getBranch() {
         return this.branch;
      }

      public BigDecimal getPreAmt() {
         return this.preAmt;
      }

      public BigDecimal getPreOffer() {
         return this.preOffer;
      }

      public String getPreTranDate() {
         return this.preTranDate;
      }

      public String getGuarantyStyle() {
         return this.guarantyStyle;
      }

      public String getChannelSeqNo() {
         return this.channelSeqNo;
      }

      public String getRegStatus() {
         return this.regStatus;
      }

      public BigDecimal getRemainExAmt() {
         return this.remainExAmt;
      }

      public String getChannelSeqNoClient() {
         return this.channelSeqNoClient;
      }

      public String getRecordChannelSeqNoClient() {
         return this.recordChannelSeqNoClient;
      }

      public String getChannelSeqNoBank() {
         return this.channelSeqNoBank;
      }

      public String getRecordChannelSeqNoBank() {
         return this.recordChannelSeqNoBank;
      }

      public String getDepTranDate() {
         return this.depTranDate;
      }

      public String getExBusiType() {
         return this.exBusiType;
      }

      public String getChangeDealDateEnd() {
         return this.changeDealDateEnd;
      }

      public String getDepProdType() {
         return this.depProdType;
      }

      public void setDepBaseAcctNo(String depBaseAcctNo) {
         this.depBaseAcctNo = depBaseAcctNo;
      }

      public void setTranDate(String tranDate) {
         this.tranDate = tranDate;
      }

      public void setExType(String exType) {
         this.exType = exType;
      }

      public void setOptions(String options) {
         this.options = options;
      }

      public void setDeliveryMethod(String deliveryMethod) {
         this.deliveryMethod = deliveryMethod;
      }

      public void setFixDealDate(String fixDealDate) {
         this.fixDealDate = fixDealDate;
      }

      public void setChangeDealDate(String changeDealDate) {
         this.changeDealDate = changeDealDate;
      }

      public void setDealRecAcct(String dealRecAcct) {
         this.dealRecAcct = dealRecAcct;
      }

      public void setBusiNo(String busiNo) {
         this.busiNo = busiNo;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setChClientName(String chClientName) {
         this.chClientName = chClientName;
      }

      public void setEnClientName(String enClientName) {
         this.enClientName = enClientName;
      }

      public void setForeCcy(String foreCcy) {
         this.foreCcy = foreCcy;
      }

      public void setForeBaseAcctNo(String foreBaseAcctNo) {
         this.foreBaseAcctNo = foreBaseAcctNo;
      }

      public void setForeAcctSeqNo(String foreAcctSeqNo) {
         this.foreAcctSeqNo = foreAcctSeqNo;
      }

      public void setCnyBaseAcctNo(String cnyBaseAcctNo) {
         this.cnyBaseAcctNo = cnyBaseAcctNo;
      }

      public void setCnyAcctSeqNo(String cnyAcctSeqNo) {
         this.cnyAcctSeqNo = cnyAcctSeqNo;
      }

      public void setOfferLetter(BigDecimal offerLetter) {
         this.offerLetter = offerLetter;
      }

      public void setForeAmt(BigDecimal foreAmt) {
         this.foreAmt = foreAmt;
      }

      public void setCnyAmt(BigDecimal cnyAmt) {
         this.cnyAmt = cnyAmt;
      }

      public void setOccCcy(String occCcy) {
         this.occCcy = occCcy;
      }

      public void setOccAmt(BigDecimal occAmt) {
         this.occAmt = occAmt;
      }

      public void setDepCcy(String depCcy) {
         this.depCcy = depCcy;
      }

      public void setDepAmt(BigDecimal depAmt) {
         this.depAmt = depAmt;
      }

      public void setDepAcctSeqNo(String depAcctSeqNo) {
         this.depAcctSeqNo = depAcctSeqNo;
      }

      public void setTeamBankName(String teamBankName) {
         this.teamBankName = teamBankName;
      }

      public void setTeamBankRate(BigDecimal teamBankRate) {
         this.teamBankRate = teamBankRate;
      }

      public void setLossForeAmt(BigDecimal lossForeAmt) {
         this.lossForeAmt = lossForeAmt;
      }

      public void setLossCnyAmt(BigDecimal lossCnyAmt) {
         this.lossCnyAmt = lossCnyAmt;
      }

      public void setWaring(String waring) {
         this.waring = waring;
      }

      public void setThenWaring(String thenWaring) {
         this.thenWaring = thenWaring;
      }

      public void setDepBlockNo(String depBlockNo) {
         this.depBlockNo = depBlockNo;
      }

      public void setExTranDate(String exTranDate) {
         this.exTranDate = exTranDate;
      }

      public void setExDealType(String exDealType) {
         this.exDealType = exDealType;
      }

      public void setExBranch(String exBranch) {
         this.exBranch = exBranch;
      }

      public void setExUserId(String exUserId) {
         this.exUserId = exUserId;
      }

      public void setExTranAmt(BigDecimal exTranAmt) {
         this.exTranAmt = exTranAmt;
      }

      public void setToClearAcctNoFore(String toClearAcctNoFore) {
         this.toClearAcctNoFore = toClearAcctNoFore;
      }

      public void setToClearAcctNoCny(String toClearAcctNoCny) {
         this.toClearAcctNoCny = toClearAcctNoCny;
      }

      public void setClearAcctNo(String clearAcctNo) {
         this.clearAcctNo = clearAcctNo;
      }

      public void setBeforeExRate(BigDecimal beforeExRate) {
         this.beforeExRate = beforeExRate;
      }

      public void setLossAmt(BigDecimal lossAmt) {
         this.lossAmt = lossAmt;
      }

      public void setIncomeAmt(BigDecimal incomeAmt) {
         this.incomeAmt = incomeAmt;
      }

      public void setDeductAmt(BigDecimal deductAmt) {
         this.deductAmt = deductAmt;
      }

      public void setFarRate(BigDecimal farRate) {
         this.farRate = farRate;
      }

      public void setNearRate(BigDecimal nearRate) {
         this.nearRate = nearRate;
      }

      public void setDeferBusiNo(String deferBusiNo) {
         this.deferBusiNo = deferBusiNo;
      }

      public void setDeferCcy(String deferCcy) {
         this.deferCcy = deferCcy;
      }

      public void setDeferTranAmt(BigDecimal deferTranAmt) {
         this.deferTranAmt = deferTranAmt;
      }

      public void setDeferRate(BigDecimal deferRate) {
         this.deferRate = deferRate;
      }

      public void setDeferDealType(String deferDealType) {
         this.deferDealType = deferDealType;
      }

      public void setDeferFixDate(String deferFixDate) {
         this.deferFixDate = deferFixDate;
      }

      public void setDeferChangeDate(String deferChangeDate) {
         this.deferChangeDate = deferChangeDate;
      }

      public void setDeferChangeDateEnd(String deferChangeDateEnd) {
         this.deferChangeDateEnd = deferChangeDateEnd;
      }

      public void setBranch(String branch) {
         this.branch = branch;
      }

      public void setPreAmt(BigDecimal preAmt) {
         this.preAmt = preAmt;
      }

      public void setPreOffer(BigDecimal preOffer) {
         this.preOffer = preOffer;
      }

      public void setPreTranDate(String preTranDate) {
         this.preTranDate = preTranDate;
      }

      public void setGuarantyStyle(String guarantyStyle) {
         this.guarantyStyle = guarantyStyle;
      }

      public void setChannelSeqNo(String channelSeqNo) {
         this.channelSeqNo = channelSeqNo;
      }

      public void setRegStatus(String regStatus) {
         this.regStatus = regStatus;
      }

      public void setRemainExAmt(BigDecimal remainExAmt) {
         this.remainExAmt = remainExAmt;
      }

      public void setChannelSeqNoClient(String channelSeqNoClient) {
         this.channelSeqNoClient = channelSeqNoClient;
      }

      public void setRecordChannelSeqNoClient(String recordChannelSeqNoClient) {
         this.recordChannelSeqNoClient = recordChannelSeqNoClient;
      }

      public void setChannelSeqNoBank(String channelSeqNoBank) {
         this.channelSeqNoBank = channelSeqNoBank;
      }

      public void setRecordChannelSeqNoBank(String recordChannelSeqNoBank) {
         this.recordChannelSeqNoBank = recordChannelSeqNoBank;
      }

      public void setDepTranDate(String depTranDate) {
         this.depTranDate = depTranDate;
      }

      public void setExBusiType(String exBusiType) {
         this.exBusiType = exBusiType;
      }

      public void setChangeDealDateEnd(String changeDealDateEnd) {
         this.changeDealDateEnd = changeDealDateEnd;
      }

      public void setDepProdType(String depProdType) {
         this.depProdType = depProdType;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400061085Out.ExchangeDistantRegList)) {
            return false;
         } else {
            Core1400061085Out.ExchangeDistantRegList other = (Core1400061085Out.ExchangeDistantRegList)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label875: {
                  Object this$depBaseAcctNo = this.getDepBaseAcctNo();
                  Object other$depBaseAcctNo = other.getDepBaseAcctNo();
                  if (this$depBaseAcctNo == null) {
                     if (other$depBaseAcctNo == null) {
                        break label875;
                     }
                  } else if (this$depBaseAcctNo.equals(other$depBaseAcctNo)) {
                     break label875;
                  }

                  return false;
               }

               Object this$tranDate = this.getTranDate();
               Object other$tranDate = other.getTranDate();
               if (this$tranDate == null) {
                  if (other$tranDate != null) {
                     return false;
                  }
               } else if (!this$tranDate.equals(other$tranDate)) {
                  return false;
               }

               Object this$exType = this.getExType();
               Object other$exType = other.getExType();
               if (this$exType == null) {
                  if (other$exType != null) {
                     return false;
                  }
               } else if (!this$exType.equals(other$exType)) {
                  return false;
               }

               label854: {
                  Object this$options = this.getOptions();
                  Object other$options = other.getOptions();
                  if (this$options == null) {
                     if (other$options == null) {
                        break label854;
                     }
                  } else if (this$options.equals(other$options)) {
                     break label854;
                  }

                  return false;
               }

               label847: {
                  Object this$deliveryMethod = this.getDeliveryMethod();
                  Object other$deliveryMethod = other.getDeliveryMethod();
                  if (this$deliveryMethod == null) {
                     if (other$deliveryMethod == null) {
                        break label847;
                     }
                  } else if (this$deliveryMethod.equals(other$deliveryMethod)) {
                     break label847;
                  }

                  return false;
               }

               label840: {
                  Object this$fixDealDate = this.getFixDealDate();
                  Object other$fixDealDate = other.getFixDealDate();
                  if (this$fixDealDate == null) {
                     if (other$fixDealDate == null) {
                        break label840;
                     }
                  } else if (this$fixDealDate.equals(other$fixDealDate)) {
                     break label840;
                  }

                  return false;
               }

               Object this$changeDealDate = this.getChangeDealDate();
               Object other$changeDealDate = other.getChangeDealDate();
               if (this$changeDealDate == null) {
                  if (other$changeDealDate != null) {
                     return false;
                  }
               } else if (!this$changeDealDate.equals(other$changeDealDate)) {
                  return false;
               }

               label826: {
                  Object this$dealRecAcct = this.getDealRecAcct();
                  Object other$dealRecAcct = other.getDealRecAcct();
                  if (this$dealRecAcct == null) {
                     if (other$dealRecAcct == null) {
                        break label826;
                     }
                  } else if (this$dealRecAcct.equals(other$dealRecAcct)) {
                     break label826;
                  }

                  return false;
               }

               Object this$busiNo = this.getBusiNo();
               Object other$busiNo = other.getBusiNo();
               if (this$busiNo == null) {
                  if (other$busiNo != null) {
                     return false;
                  }
               } else if (!this$busiNo.equals(other$busiNo)) {
                  return false;
               }

               label812: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label812;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label812;
                  }

                  return false;
               }

               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo != null) {
                     return false;
                  }
               } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                  return false;
               }

               Object this$clientNo = this.getClientNo();
               Object other$clientNo = other.getClientNo();
               if (this$clientNo == null) {
                  if (other$clientNo != null) {
                     return false;
                  }
               } else if (!this$clientNo.equals(other$clientNo)) {
                  return false;
               }

               label791: {
                  Object this$chClientName = this.getChClientName();
                  Object other$chClientName = other.getChClientName();
                  if (this$chClientName == null) {
                     if (other$chClientName == null) {
                        break label791;
                     }
                  } else if (this$chClientName.equals(other$chClientName)) {
                     break label791;
                  }

                  return false;
               }

               label784: {
                  Object this$enClientName = this.getEnClientName();
                  Object other$enClientName = other.getEnClientName();
                  if (this$enClientName == null) {
                     if (other$enClientName == null) {
                        break label784;
                     }
                  } else if (this$enClientName.equals(other$enClientName)) {
                     break label784;
                  }

                  return false;
               }

               Object this$foreCcy = this.getForeCcy();
               Object other$foreCcy = other.getForeCcy();
               if (this$foreCcy == null) {
                  if (other$foreCcy != null) {
                     return false;
                  }
               } else if (!this$foreCcy.equals(other$foreCcy)) {
                  return false;
               }

               Object this$foreBaseAcctNo = this.getForeBaseAcctNo();
               Object other$foreBaseAcctNo = other.getForeBaseAcctNo();
               if (this$foreBaseAcctNo == null) {
                  if (other$foreBaseAcctNo != null) {
                     return false;
                  }
               } else if (!this$foreBaseAcctNo.equals(other$foreBaseAcctNo)) {
                  return false;
               }

               label763: {
                  Object this$foreAcctSeqNo = this.getForeAcctSeqNo();
                  Object other$foreAcctSeqNo = other.getForeAcctSeqNo();
                  if (this$foreAcctSeqNo == null) {
                     if (other$foreAcctSeqNo == null) {
                        break label763;
                     }
                  } else if (this$foreAcctSeqNo.equals(other$foreAcctSeqNo)) {
                     break label763;
                  }

                  return false;
               }

               Object this$cnyBaseAcctNo = this.getCnyBaseAcctNo();
               Object other$cnyBaseAcctNo = other.getCnyBaseAcctNo();
               if (this$cnyBaseAcctNo == null) {
                  if (other$cnyBaseAcctNo != null) {
                     return false;
                  }
               } else if (!this$cnyBaseAcctNo.equals(other$cnyBaseAcctNo)) {
                  return false;
               }

               Object this$cnyAcctSeqNo = this.getCnyAcctSeqNo();
               Object other$cnyAcctSeqNo = other.getCnyAcctSeqNo();
               if (this$cnyAcctSeqNo == null) {
                  if (other$cnyAcctSeqNo != null) {
                     return false;
                  }
               } else if (!this$cnyAcctSeqNo.equals(other$cnyAcctSeqNo)) {
                  return false;
               }

               label742: {
                  Object this$offerLetter = this.getOfferLetter();
                  Object other$offerLetter = other.getOfferLetter();
                  if (this$offerLetter == null) {
                     if (other$offerLetter == null) {
                        break label742;
                     }
                  } else if (this$offerLetter.equals(other$offerLetter)) {
                     break label742;
                  }

                  return false;
               }

               label735: {
                  Object this$foreAmt = this.getForeAmt();
                  Object other$foreAmt = other.getForeAmt();
                  if (this$foreAmt == null) {
                     if (other$foreAmt == null) {
                        break label735;
                     }
                  } else if (this$foreAmt.equals(other$foreAmt)) {
                     break label735;
                  }

                  return false;
               }

               label728: {
                  Object this$cnyAmt = this.getCnyAmt();
                  Object other$cnyAmt = other.getCnyAmt();
                  if (this$cnyAmt == null) {
                     if (other$cnyAmt == null) {
                        break label728;
                     }
                  } else if (this$cnyAmt.equals(other$cnyAmt)) {
                     break label728;
                  }

                  return false;
               }

               Object this$occCcy = this.getOccCcy();
               Object other$occCcy = other.getOccCcy();
               if (this$occCcy == null) {
                  if (other$occCcy != null) {
                     return false;
                  }
               } else if (!this$occCcy.equals(other$occCcy)) {
                  return false;
               }

               label714: {
                  Object this$occAmt = this.getOccAmt();
                  Object other$occAmt = other.getOccAmt();
                  if (this$occAmt == null) {
                     if (other$occAmt == null) {
                        break label714;
                     }
                  } else if (this$occAmt.equals(other$occAmt)) {
                     break label714;
                  }

                  return false;
               }

               Object this$depCcy = this.getDepCcy();
               Object other$depCcy = other.getDepCcy();
               if (this$depCcy == null) {
                  if (other$depCcy != null) {
                     return false;
                  }
               } else if (!this$depCcy.equals(other$depCcy)) {
                  return false;
               }

               label700: {
                  Object this$depAmt = this.getDepAmt();
                  Object other$depAmt = other.getDepAmt();
                  if (this$depAmt == null) {
                     if (other$depAmt == null) {
                        break label700;
                     }
                  } else if (this$depAmt.equals(other$depAmt)) {
                     break label700;
                  }

                  return false;
               }

               Object this$depAcctSeqNo = this.getDepAcctSeqNo();
               Object other$depAcctSeqNo = other.getDepAcctSeqNo();
               if (this$depAcctSeqNo == null) {
                  if (other$depAcctSeqNo != null) {
                     return false;
                  }
               } else if (!this$depAcctSeqNo.equals(other$depAcctSeqNo)) {
                  return false;
               }

               Object this$teamBankName = this.getTeamBankName();
               Object other$teamBankName = other.getTeamBankName();
               if (this$teamBankName == null) {
                  if (other$teamBankName != null) {
                     return false;
                  }
               } else if (!this$teamBankName.equals(other$teamBankName)) {
                  return false;
               }

               label679: {
                  Object this$teamBankRate = this.getTeamBankRate();
                  Object other$teamBankRate = other.getTeamBankRate();
                  if (this$teamBankRate == null) {
                     if (other$teamBankRate == null) {
                        break label679;
                     }
                  } else if (this$teamBankRate.equals(other$teamBankRate)) {
                     break label679;
                  }

                  return false;
               }

               label672: {
                  Object this$lossForeAmt = this.getLossForeAmt();
                  Object other$lossForeAmt = other.getLossForeAmt();
                  if (this$lossForeAmt == null) {
                     if (other$lossForeAmt == null) {
                        break label672;
                     }
                  } else if (this$lossForeAmt.equals(other$lossForeAmt)) {
                     break label672;
                  }

                  return false;
               }

               Object this$lossCnyAmt = this.getLossCnyAmt();
               Object other$lossCnyAmt = other.getLossCnyAmt();
               if (this$lossCnyAmt == null) {
                  if (other$lossCnyAmt != null) {
                     return false;
                  }
               } else if (!this$lossCnyAmt.equals(other$lossCnyAmt)) {
                  return false;
               }

               Object this$waring = this.getWaring();
               Object other$waring = other.getWaring();
               if (this$waring == null) {
                  if (other$waring != null) {
                     return false;
                  }
               } else if (!this$waring.equals(other$waring)) {
                  return false;
               }

               label651: {
                  Object this$thenWaring = this.getThenWaring();
                  Object other$thenWaring = other.getThenWaring();
                  if (this$thenWaring == null) {
                     if (other$thenWaring == null) {
                        break label651;
                     }
                  } else if (this$thenWaring.equals(other$thenWaring)) {
                     break label651;
                  }

                  return false;
               }

               Object this$depBlockNo = this.getDepBlockNo();
               Object other$depBlockNo = other.getDepBlockNo();
               if (this$depBlockNo == null) {
                  if (other$depBlockNo != null) {
                     return false;
                  }
               } else if (!this$depBlockNo.equals(other$depBlockNo)) {
                  return false;
               }

               Object this$exTranDate = this.getExTranDate();
               Object other$exTranDate = other.getExTranDate();
               if (this$exTranDate == null) {
                  if (other$exTranDate != null) {
                     return false;
                  }
               } else if (!this$exTranDate.equals(other$exTranDate)) {
                  return false;
               }

               label630: {
                  Object this$exDealType = this.getExDealType();
                  Object other$exDealType = other.getExDealType();
                  if (this$exDealType == null) {
                     if (other$exDealType == null) {
                        break label630;
                     }
                  } else if (this$exDealType.equals(other$exDealType)) {
                     break label630;
                  }

                  return false;
               }

               label623: {
                  Object this$exBranch = this.getExBranch();
                  Object other$exBranch = other.getExBranch();
                  if (this$exBranch == null) {
                     if (other$exBranch == null) {
                        break label623;
                     }
                  } else if (this$exBranch.equals(other$exBranch)) {
                     break label623;
                  }

                  return false;
               }

               label616: {
                  Object this$exUserId = this.getExUserId();
                  Object other$exUserId = other.getExUserId();
                  if (this$exUserId == null) {
                     if (other$exUserId == null) {
                        break label616;
                     }
                  } else if (this$exUserId.equals(other$exUserId)) {
                     break label616;
                  }

                  return false;
               }

               Object this$exTranAmt = this.getExTranAmt();
               Object other$exTranAmt = other.getExTranAmt();
               if (this$exTranAmt == null) {
                  if (other$exTranAmt != null) {
                     return false;
                  }
               } else if (!this$exTranAmt.equals(other$exTranAmt)) {
                  return false;
               }

               label602: {
                  Object this$toClearAcctNoFore = this.getToClearAcctNoFore();
                  Object other$toClearAcctNoFore = other.getToClearAcctNoFore();
                  if (this$toClearAcctNoFore == null) {
                     if (other$toClearAcctNoFore == null) {
                        break label602;
                     }
                  } else if (this$toClearAcctNoFore.equals(other$toClearAcctNoFore)) {
                     break label602;
                  }

                  return false;
               }

               Object this$toClearAcctNoCny = this.getToClearAcctNoCny();
               Object other$toClearAcctNoCny = other.getToClearAcctNoCny();
               if (this$toClearAcctNoCny == null) {
                  if (other$toClearAcctNoCny != null) {
                     return false;
                  }
               } else if (!this$toClearAcctNoCny.equals(other$toClearAcctNoCny)) {
                  return false;
               }

               label588: {
                  Object this$clearAcctNo = this.getClearAcctNo();
                  Object other$clearAcctNo = other.getClearAcctNo();
                  if (this$clearAcctNo == null) {
                     if (other$clearAcctNo == null) {
                        break label588;
                     }
                  } else if (this$clearAcctNo.equals(other$clearAcctNo)) {
                     break label588;
                  }

                  return false;
               }

               Object this$beforeExRate = this.getBeforeExRate();
               Object other$beforeExRate = other.getBeforeExRate();
               if (this$beforeExRate == null) {
                  if (other$beforeExRate != null) {
                     return false;
                  }
               } else if (!this$beforeExRate.equals(other$beforeExRate)) {
                  return false;
               }

               Object this$lossAmt = this.getLossAmt();
               Object other$lossAmt = other.getLossAmt();
               if (this$lossAmt == null) {
                  if (other$lossAmt != null) {
                     return false;
                  }
               } else if (!this$lossAmt.equals(other$lossAmt)) {
                  return false;
               }

               label567: {
                  Object this$incomeAmt = this.getIncomeAmt();
                  Object other$incomeAmt = other.getIncomeAmt();
                  if (this$incomeAmt == null) {
                     if (other$incomeAmt == null) {
                        break label567;
                     }
                  } else if (this$incomeAmt.equals(other$incomeAmt)) {
                     break label567;
                  }

                  return false;
               }

               label560: {
                  Object this$deductAmt = this.getDeductAmt();
                  Object other$deductAmt = other.getDeductAmt();
                  if (this$deductAmt == null) {
                     if (other$deductAmt == null) {
                        break label560;
                     }
                  } else if (this$deductAmt.equals(other$deductAmt)) {
                     break label560;
                  }

                  return false;
               }

               Object this$farRate = this.getFarRate();
               Object other$farRate = other.getFarRate();
               if (this$farRate == null) {
                  if (other$farRate != null) {
                     return false;
                  }
               } else if (!this$farRate.equals(other$farRate)) {
                  return false;
               }

               Object this$nearRate = this.getNearRate();
               Object other$nearRate = other.getNearRate();
               if (this$nearRate == null) {
                  if (other$nearRate != null) {
                     return false;
                  }
               } else if (!this$nearRate.equals(other$nearRate)) {
                  return false;
               }

               label539: {
                  Object this$deferBusiNo = this.getDeferBusiNo();
                  Object other$deferBusiNo = other.getDeferBusiNo();
                  if (this$deferBusiNo == null) {
                     if (other$deferBusiNo == null) {
                        break label539;
                     }
                  } else if (this$deferBusiNo.equals(other$deferBusiNo)) {
                     break label539;
                  }

                  return false;
               }

               Object this$deferCcy = this.getDeferCcy();
               Object other$deferCcy = other.getDeferCcy();
               if (this$deferCcy == null) {
                  if (other$deferCcy != null) {
                     return false;
                  }
               } else if (!this$deferCcy.equals(other$deferCcy)) {
                  return false;
               }

               Object this$deferTranAmt = this.getDeferTranAmt();
               Object other$deferTranAmt = other.getDeferTranAmt();
               if (this$deferTranAmt == null) {
                  if (other$deferTranAmt != null) {
                     return false;
                  }
               } else if (!this$deferTranAmt.equals(other$deferTranAmt)) {
                  return false;
               }

               label518: {
                  Object this$deferRate = this.getDeferRate();
                  Object other$deferRate = other.getDeferRate();
                  if (this$deferRate == null) {
                     if (other$deferRate == null) {
                        break label518;
                     }
                  } else if (this$deferRate.equals(other$deferRate)) {
                     break label518;
                  }

                  return false;
               }

               label511: {
                  Object this$deferDealType = this.getDeferDealType();
                  Object other$deferDealType = other.getDeferDealType();
                  if (this$deferDealType == null) {
                     if (other$deferDealType == null) {
                        break label511;
                     }
                  } else if (this$deferDealType.equals(other$deferDealType)) {
                     break label511;
                  }

                  return false;
               }

               label504: {
                  Object this$deferFixDate = this.getDeferFixDate();
                  Object other$deferFixDate = other.getDeferFixDate();
                  if (this$deferFixDate == null) {
                     if (other$deferFixDate == null) {
                        break label504;
                     }
                  } else if (this$deferFixDate.equals(other$deferFixDate)) {
                     break label504;
                  }

                  return false;
               }

               Object this$deferChangeDate = this.getDeferChangeDate();
               Object other$deferChangeDate = other.getDeferChangeDate();
               if (this$deferChangeDate == null) {
                  if (other$deferChangeDate != null) {
                     return false;
                  }
               } else if (!this$deferChangeDate.equals(other$deferChangeDate)) {
                  return false;
               }

               label490: {
                  Object this$deferChangeDateEnd = this.getDeferChangeDateEnd();
                  Object other$deferChangeDateEnd = other.getDeferChangeDateEnd();
                  if (this$deferChangeDateEnd == null) {
                     if (other$deferChangeDateEnd == null) {
                        break label490;
                     }
                  } else if (this$deferChangeDateEnd.equals(other$deferChangeDateEnd)) {
                     break label490;
                  }

                  return false;
               }

               Object this$branch = this.getBranch();
               Object other$branch = other.getBranch();
               if (this$branch == null) {
                  if (other$branch != null) {
                     return false;
                  }
               } else if (!this$branch.equals(other$branch)) {
                  return false;
               }

               label476: {
                  Object this$preAmt = this.getPreAmt();
                  Object other$preAmt = other.getPreAmt();
                  if (this$preAmt == null) {
                     if (other$preAmt == null) {
                        break label476;
                     }
                  } else if (this$preAmt.equals(other$preAmt)) {
                     break label476;
                  }

                  return false;
               }

               Object this$preOffer = this.getPreOffer();
               Object other$preOffer = other.getPreOffer();
               if (this$preOffer == null) {
                  if (other$preOffer != null) {
                     return false;
                  }
               } else if (!this$preOffer.equals(other$preOffer)) {
                  return false;
               }

               Object this$preTranDate = this.getPreTranDate();
               Object other$preTranDate = other.getPreTranDate();
               if (this$preTranDate == null) {
                  if (other$preTranDate != null) {
                     return false;
                  }
               } else if (!this$preTranDate.equals(other$preTranDate)) {
                  return false;
               }

               label455: {
                  Object this$guarantyStyle = this.getGuarantyStyle();
                  Object other$guarantyStyle = other.getGuarantyStyle();
                  if (this$guarantyStyle == null) {
                     if (other$guarantyStyle == null) {
                        break label455;
                     }
                  } else if (this$guarantyStyle.equals(other$guarantyStyle)) {
                     break label455;
                  }

                  return false;
               }

               label448: {
                  Object this$channelSeqNo = this.getChannelSeqNo();
                  Object other$channelSeqNo = other.getChannelSeqNo();
                  if (this$channelSeqNo == null) {
                     if (other$channelSeqNo == null) {
                        break label448;
                     }
                  } else if (this$channelSeqNo.equals(other$channelSeqNo)) {
                     break label448;
                  }

                  return false;
               }

               Object this$regStatus = this.getRegStatus();
               Object other$regStatus = other.getRegStatus();
               if (this$regStatus == null) {
                  if (other$regStatus != null) {
                     return false;
                  }
               } else if (!this$regStatus.equals(other$regStatus)) {
                  return false;
               }

               Object this$remainExAmt = this.getRemainExAmt();
               Object other$remainExAmt = other.getRemainExAmt();
               if (this$remainExAmt == null) {
                  if (other$remainExAmt != null) {
                     return false;
                  }
               } else if (!this$remainExAmt.equals(other$remainExAmt)) {
                  return false;
               }

               label427: {
                  Object this$channelSeqNoClient = this.getChannelSeqNoClient();
                  Object other$channelSeqNoClient = other.getChannelSeqNoClient();
                  if (this$channelSeqNoClient == null) {
                     if (other$channelSeqNoClient == null) {
                        break label427;
                     }
                  } else if (this$channelSeqNoClient.equals(other$channelSeqNoClient)) {
                     break label427;
                  }

                  return false;
               }

               Object this$recordChannelSeqNoClient = this.getRecordChannelSeqNoClient();
               Object other$recordChannelSeqNoClient = other.getRecordChannelSeqNoClient();
               if (this$recordChannelSeqNoClient == null) {
                  if (other$recordChannelSeqNoClient != null) {
                     return false;
                  }
               } else if (!this$recordChannelSeqNoClient.equals(other$recordChannelSeqNoClient)) {
                  return false;
               }

               Object this$channelSeqNoBank = this.getChannelSeqNoBank();
               Object other$channelSeqNoBank = other.getChannelSeqNoBank();
               if (this$channelSeqNoBank == null) {
                  if (other$channelSeqNoBank != null) {
                     return false;
                  }
               } else if (!this$channelSeqNoBank.equals(other$channelSeqNoBank)) {
                  return false;
               }

               label406: {
                  Object this$recordChannelSeqNoBank = this.getRecordChannelSeqNoBank();
                  Object other$recordChannelSeqNoBank = other.getRecordChannelSeqNoBank();
                  if (this$recordChannelSeqNoBank == null) {
                     if (other$recordChannelSeqNoBank == null) {
                        break label406;
                     }
                  } else if (this$recordChannelSeqNoBank.equals(other$recordChannelSeqNoBank)) {
                     break label406;
                  }

                  return false;
               }

               label399: {
                  Object this$depTranDate = this.getDepTranDate();
                  Object other$depTranDate = other.getDepTranDate();
                  if (this$depTranDate == null) {
                     if (other$depTranDate == null) {
                        break label399;
                     }
                  } else if (this$depTranDate.equals(other$depTranDate)) {
                     break label399;
                  }

                  return false;
               }

               label392: {
                  Object this$exBusiType = this.getExBusiType();
                  Object other$exBusiType = other.getExBusiType();
                  if (this$exBusiType == null) {
                     if (other$exBusiType == null) {
                        break label392;
                     }
                  } else if (this$exBusiType.equals(other$exBusiType)) {
                     break label392;
                  }

                  return false;
               }

               Object this$changeDealDateEnd = this.getChangeDealDateEnd();
               Object other$changeDealDateEnd = other.getChangeDealDateEnd();
               if (this$changeDealDateEnd == null) {
                  if (other$changeDealDateEnd != null) {
                     return false;
                  }
               } else if (!this$changeDealDateEnd.equals(other$changeDealDateEnd)) {
                  return false;
               }

               Object this$depProdType = this.getDepProdType();
               Object other$depProdType = other.getDepProdType();
               if (this$depProdType == null) {
                  if (other$depProdType != null) {
                     return false;
                  }
               } else if (!this$depProdType.equals(other$depProdType)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400061085Out.ExchangeDistantRegList;
      }
      public String toString() {
         return "Core1400061085Out.ExchangeDistantRegList(depBaseAcctNo=" + this.getDepBaseAcctNo() + ", tranDate=" + this.getTranDate() + ", exType=" + this.getExType() + ", options=" + this.getOptions() + ", deliveryMethod=" + this.getDeliveryMethod() + ", fixDealDate=" + this.getFixDealDate() + ", changeDealDate=" + this.getChangeDealDate() + ", dealRecAcct=" + this.getDealRecAcct() + ", busiNo=" + this.getBusiNo() + ", baseAcctNo=" + this.getBaseAcctNo() + ", acctSeqNo=" + this.getAcctSeqNo() + ", clientNo=" + this.getClientNo() + ", chClientName=" + this.getChClientName() + ", enClientName=" + this.getEnClientName() + ", foreCcy=" + this.getForeCcy() + ", foreBaseAcctNo=" + this.getForeBaseAcctNo() + ", foreAcctSeqNo=" + this.getForeAcctSeqNo() + ", cnyBaseAcctNo=" + this.getCnyBaseAcctNo() + ", cnyAcctSeqNo=" + this.getCnyAcctSeqNo() + ", offerLetter=" + this.getOfferLetter() + ", foreAmt=" + this.getForeAmt() + ", cnyAmt=" + this.getCnyAmt() + ", occCcy=" + this.getOccCcy() + ", occAmt=" + this.getOccAmt() + ", depCcy=" + this.getDepCcy() + ", depAmt=" + this.getDepAmt() + ", depAcctSeqNo=" + this.getDepAcctSeqNo() + ", teamBankName=" + this.getTeamBankName() + ", teamBankRate=" + this.getTeamBankRate() + ", lossForeAmt=" + this.getLossForeAmt() + ", lossCnyAmt=" + this.getLossCnyAmt() + ", waring=" + this.getWaring() + ", thenWaring=" + this.getThenWaring() + ", depBlockNo=" + this.getDepBlockNo() + ", exTranDate=" + this.getExTranDate() + ", exDealType=" + this.getExDealType() + ", exBranch=" + this.getExBranch() + ", exUserId=" + this.getExUserId() + ", exTranAmt=" + this.getExTranAmt() + ", toClearAcctNoFore=" + this.getToClearAcctNoFore() + ", toClearAcctNoCny=" + this.getToClearAcctNoCny() + ", clearAcctNo=" + this.getClearAcctNo() + ", beforeExRate=" + this.getBeforeExRate() + ", lossAmt=" + this.getLossAmt() + ", incomeAmt=" + this.getIncomeAmt() + ", deductAmt=" + this.getDeductAmt() + ", farRate=" + this.getFarRate() + ", nearRate=" + this.getNearRate() + ", deferBusiNo=" + this.getDeferBusiNo() + ", deferCcy=" + this.getDeferCcy() + ", deferTranAmt=" + this.getDeferTranAmt() + ", deferRate=" + this.getDeferRate() + ", deferDealType=" + this.getDeferDealType() + ", deferFixDate=" + this.getDeferFixDate() + ", deferChangeDate=" + this.getDeferChangeDate() + ", deferChangeDateEnd=" + this.getDeferChangeDateEnd() + ", branch=" + this.getBranch() + ", preAmt=" + this.getPreAmt() + ", preOffer=" + this.getPreOffer() + ", preTranDate=" + this.getPreTranDate() + ", guarantyStyle=" + this.getGuarantyStyle() + ", channelSeqNo=" + this.getChannelSeqNo() + ", regStatus=" + this.getRegStatus() + ", remainExAmt=" + this.getRemainExAmt() + ", channelSeqNoClient=" + this.getChannelSeqNoClient() + ", recordChannelSeqNoClient=" + this.getRecordChannelSeqNoClient() + ", channelSeqNoBank=" + this.getChannelSeqNoBank() + ", recordChannelSeqNoBank=" + this.getRecordChannelSeqNoBank() + ", depTranDate=" + this.getDepTranDate() + ", exBusiType=" + this.getExBusiType() + ", changeDealDateEnd=" + this.getChangeDealDateEnd() + ", depProdType=" + this.getDepProdType() + ")";
      }
   }
}
