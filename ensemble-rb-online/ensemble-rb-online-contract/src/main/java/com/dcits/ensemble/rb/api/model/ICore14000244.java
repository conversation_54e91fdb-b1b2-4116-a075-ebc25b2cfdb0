package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000244In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000244Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000244 {
   String URL = "/rb/inq/commision/card/query";


   @ApiDesc("本交易提供对代办人开卡信息查询功能，支持查询代办人最大允许卡数、代办人已办卡数等信息。")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0244"
   )
   @FunctionCategory("RB21-风险管理")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core14000244Out runService(Core14000244In var1);
}
