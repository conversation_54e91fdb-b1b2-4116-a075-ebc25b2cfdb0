package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.util.List;

@MessageOut
public class Core1400100706Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "客户号",
      notNull = false,
      length = "20",
      remark = "客户号",
      maxSize = 20
   )
   private String clientNo;
   @V(
      desc = "账号/卡号",
      notNull = false,
      length = "50",
      remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
      maxSize = 50
   )
   private String baseAcctNo;
   @V(
      desc = "产品类型",
      notNull = false,
      length = "20",
      remark = "产品类型",
      maxSize = 20
   )
   private String prodType;
   @V(
      desc = "账户币种",
      notNull = false,
      length = "3",
      remark = "账户币种 对于AIO账户和一本通账户",
      maxSize = 3
   )
   private String acctCcy;
   @V(
      desc = "账户序号",
      notNull = false,
      length = "5",
      remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
      maxSize = 5
   )
   private String acctSeqNo;
   @V(
      desc = "外围系统协议编号",
      notNull = false,
      length = "50",
      remark = "外围系统协议编号",
      maxSize = 50
   )
   private String signId;
   @V(
      desc = "协议状态",
      notNull = false,
      length = "2",
      remark = "普通协议使用，可应用于大部分场景",
      maxSize = 2
   )
   private String agreementStatus;
   @V(
      desc = "备案通知书编写",
      notNull = false,
      length = "50",
      remark = "备案通知书编写",
      maxSize = 50
   )
   private String recordNoticeNo;
   @V(
      desc = "资金池产品类型",
      notNull = false,
      length = "20",
      remark = "资金池产品类型",
      maxSize = 20
   )
   private String pcpProdType;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100706Out.SubAcctArray> subAcctArray;

   public String getClientNo() {
      return this.clientNo;
   }

   public String getBaseAcctNo() {
      return this.baseAcctNo;
   }

   public String getProdType() {
      return this.prodType;
   }

   public String getAcctCcy() {
      return this.acctCcy;
   }

   public String getAcctSeqNo() {
      return this.acctSeqNo;
   }

   public String getSignId() {
      return this.signId;
   }

   public String getAgreementStatus() {
      return this.agreementStatus;
   }

   public String getRecordNoticeNo() {
      return this.recordNoticeNo;
   }

   public String getPcpProdType() {
      return this.pcpProdType;
   }

   public List<Core1400100706Out.SubAcctArray> getSubAcctArray() {
      return this.subAcctArray;
   }

   public void setClientNo(String clientNo) {
      this.clientNo = clientNo;
   }

   public void setBaseAcctNo(String baseAcctNo) {
      this.baseAcctNo = baseAcctNo;
   }

   public void setProdType(String prodType) {
      this.prodType = prodType;
   }

   public void setAcctCcy(String acctCcy) {
      this.acctCcy = acctCcy;
   }

   public void setAcctSeqNo(String acctSeqNo) {
      this.acctSeqNo = acctSeqNo;
   }

   public void setSignId(String signId) {
      this.signId = signId;
   }

   public void setAgreementStatus(String agreementStatus) {
      this.agreementStatus = agreementStatus;
   }

   public void setRecordNoticeNo(String recordNoticeNo) {
      this.recordNoticeNo = recordNoticeNo;
   }

   public void setPcpProdType(String pcpProdType) {
      this.pcpProdType = pcpProdType;
   }

   public void setSubAcctArray(List<Core1400100706Out.SubAcctArray> subAcctArray) {
      this.subAcctArray = subAcctArray;
   }

   public String toString() {
      return "Core1400100706Out(clientNo=" + this.getClientNo() + ", baseAcctNo=" + this.getBaseAcctNo() + ", prodType=" + this.getProdType() + ", acctCcy=" + this.getAcctCcy() + ", acctSeqNo=" + this.getAcctSeqNo() + ", signId=" + this.getSignId() + ", agreementStatus=" + this.getAgreementStatus() + ", recordNoticeNo=" + this.getRecordNoticeNo() + ", pcpProdType=" + this.getPcpProdType() + ", subAcctArray=" + this.getSubAcctArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100706Out)) {
         return false;
      } else {
         Core1400100706Out other = (Core1400100706Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$clientNo = this.getClientNo();
            Object other$clientNo = other.getClientNo();
            if (this$clientNo == null) {
               if (other$clientNo != null) {
                  return false;
               }
            } else if (!this$clientNo.equals(other$clientNo)) {
               return false;
            }

            Object this$baseAcctNo = this.getBaseAcctNo();
            Object other$baseAcctNo = other.getBaseAcctNo();
            if (this$baseAcctNo == null) {
               if (other$baseAcctNo != null) {
                  return false;
               }
            } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
               return false;
            }

            label119: {
               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType == null) {
                     break label119;
                  }
               } else if (this$prodType.equals(other$prodType)) {
                  break label119;
               }

               return false;
            }

            label112: {
               Object this$acctCcy = this.getAcctCcy();
               Object other$acctCcy = other.getAcctCcy();
               if (this$acctCcy == null) {
                  if (other$acctCcy == null) {
                     break label112;
                  }
               } else if (this$acctCcy.equals(other$acctCcy)) {
                  break label112;
               }

               return false;
            }

            Object this$acctSeqNo = this.getAcctSeqNo();
            Object other$acctSeqNo = other.getAcctSeqNo();
            if (this$acctSeqNo == null) {
               if (other$acctSeqNo != null) {
                  return false;
               }
            } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
               return false;
            }

            Object this$signId = this.getSignId();
            Object other$signId = other.getSignId();
            if (this$signId == null) {
               if (other$signId != null) {
                  return false;
               }
            } else if (!this$signId.equals(other$signId)) {
               return false;
            }

            label91: {
               Object this$agreementStatus = this.getAgreementStatus();
               Object other$agreementStatus = other.getAgreementStatus();
               if (this$agreementStatus == null) {
                  if (other$agreementStatus == null) {
                     break label91;
                  }
               } else if (this$agreementStatus.equals(other$agreementStatus)) {
                  break label91;
               }

               return false;
            }

            Object this$recordNoticeNo = this.getRecordNoticeNo();
            Object other$recordNoticeNo = other.getRecordNoticeNo();
            if (this$recordNoticeNo == null) {
               if (other$recordNoticeNo != null) {
                  return false;
               }
            } else if (!this$recordNoticeNo.equals(other$recordNoticeNo)) {
               return false;
            }

            Object this$pcpProdType = this.getPcpProdType();
            Object other$pcpProdType = other.getPcpProdType();
            if (this$pcpProdType == null) {
               if (other$pcpProdType != null) {
                  return false;
               }
            } else if (!this$pcpProdType.equals(other$pcpProdType)) {
               return false;
            }

            Object this$subAcctArray = this.getSubAcctArray();
            Object other$subAcctArray = other.getSubAcctArray();
            if (this$subAcctArray == null) {
               if (other$subAcctArray != null) {
                  return false;
               }
            } else if (!this$subAcctArray.equals(other$subAcctArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100706Out;
   }
   public static class SubAcctArray {
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "账户名称",
         notNull = false,
         length = "200",
         remark = "账户名称，一般指中文账户名称",
         maxSize = 200
      )
      private String acctName;
      @V(
         desc = "是否行内行外",
         notNull = false,
         length = "1",
         remark = "是否行内行外",
         maxSize = 1
      )
      private String bankInOut;
      @V(
         desc = "境内境外标志",
         notNull = false,
         length = "1",
         remark = "境内境外标志",
         maxSize = 1
      )
      private String inlandOffshore;
      @V(
         desc = "外汇项目",
         notNull = false,
         length = "50",
         remark = "跨境资金池登记子账户外汇项目",
         maxSize = 50
      )
      private String fxProject;
      @V(
         desc = "银行行号",
         notNull = false,
         length = "20",
         remark = "银行行号",
         maxSize = 20
      )
      private String bankNo;
      @V(
         desc = "银行名称",
         notNull = false,
         length = "50",
         remark = "银行名称",
         maxSize = 50
      )
      private String bankName;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getAcctName() {
         return this.acctName;
      }

      public String getBankInOut() {
         return this.bankInOut;
      }

      public String getInlandOffshore() {
         return this.inlandOffshore;
      }

      public String getFxProject() {
         return this.fxProject;
      }

      public String getBankNo() {
         return this.bankNo;
      }

      public String getBankName() {
         return this.bankName;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setAcctName(String acctName) {
         this.acctName = acctName;
      }

      public void setBankInOut(String bankInOut) {
         this.bankInOut = bankInOut;
      }

      public void setInlandOffshore(String inlandOffshore) {
         this.inlandOffshore = inlandOffshore;
      }

      public void setFxProject(String fxProject) {
         this.fxProject = fxProject;
      }

      public void setBankNo(String bankNo) {
         this.bankNo = bankNo;
      }

      public void setBankName(String bankName) {
         this.bankName = bankName;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100706Out.SubAcctArray)) {
            return false;
         } else {
            Core1400100706Out.SubAcctArray other = (Core1400100706Out.SubAcctArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo != null) {
                     return false;
                  }
               } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo != null) {
                     return false;
                  }
               } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                  return false;
               }

               label110: {
                  Object this$acctCcy = this.getAcctCcy();
                  Object other$acctCcy = other.getAcctCcy();
                  if (this$acctCcy == null) {
                     if (other$acctCcy == null) {
                        break label110;
                     }
                  } else if (this$acctCcy.equals(other$acctCcy)) {
                     break label110;
                  }

                  return false;
               }

               label103: {
                  Object this$acctName = this.getAcctName();
                  Object other$acctName = other.getAcctName();
                  if (this$acctName == null) {
                     if (other$acctName == null) {
                        break label103;
                     }
                  } else if (this$acctName.equals(other$acctName)) {
                     break label103;
                  }

                  return false;
               }

               Object this$bankInOut = this.getBankInOut();
               Object other$bankInOut = other.getBankInOut();
               if (this$bankInOut == null) {
                  if (other$bankInOut != null) {
                     return false;
                  }
               } else if (!this$bankInOut.equals(other$bankInOut)) {
                  return false;
               }

               label89: {
                  Object this$inlandOffshore = this.getInlandOffshore();
                  Object other$inlandOffshore = other.getInlandOffshore();
                  if (this$inlandOffshore == null) {
                     if (other$inlandOffshore == null) {
                        break label89;
                     }
                  } else if (this$inlandOffshore.equals(other$inlandOffshore)) {
                     break label89;
                  }

                  return false;
               }

               label82: {
                  Object this$fxProject = this.getFxProject();
                  Object other$fxProject = other.getFxProject();
                  if (this$fxProject == null) {
                     if (other$fxProject == null) {
                        break label82;
                     }
                  } else if (this$fxProject.equals(other$fxProject)) {
                     break label82;
                  }

                  return false;
               }

               Object this$bankNo = this.getBankNo();
               Object other$bankNo = other.getBankNo();
               if (this$bankNo == null) {
                  if (other$bankNo != null) {
                     return false;
                  }
               } else if (!this$bankNo.equals(other$bankNo)) {
                  return false;
               }

               Object this$bankName = this.getBankName();
               Object other$bankName = other.getBankName();
               if (this$bankName == null) {
                  if (other$bankName != null) {
                     return false;
                  }
               } else if (!this$bankName.equals(other$bankName)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100706Out.SubAcctArray;
      }
      public String toString() {
         return "Core1400100706Out.SubAcctArray(baseAcctNo=" + this.getBaseAcctNo() + ", prodType=" + this.getProdType() + ", acctSeqNo=" + this.getAcctSeqNo() + ", acctCcy=" + this.getAcctCcy() + ", acctName=" + this.getAcctName() + ", bankInOut=" + this.getBankInOut() + ", inlandOffshore=" + this.getInlandOffshore() + ", fxProject=" + this.getFxProject() + ", bankNo=" + this.getBankNo() + ", bankName=" + this.getBankName() + ")";
      }
   }
}
