package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000714In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000714Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000714 {
   String URL = "/rb/inq/password";


   @ApiRemark("标准优化")
   @ApiDesc("根据卡号查询密码")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0714"
   )
   @BusinessCategory("密码管理")
   @FunctionCategory("RB08-特殊业务")
   @ConsumeSys("CIS/EOS/TLE/PR")
   @ApiUseStatus("PRODUCT-产品")
   Core14000714Out runService(Core14000714In var1);
}
