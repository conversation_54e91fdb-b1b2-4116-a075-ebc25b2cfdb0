package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400100027Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100027Out.HbdrateArray> hbdrateArray;

   public List<Core1400100027Out.HbdrateArray> getHbdrateArray() {
      return this.hbdrateArray;
   }

   public void setHbdrateArray(List<Core1400100027Out.HbdrateArray> hbdrateArray) {
      this.hbdrateArray = hbdrateArray;
   }

   public String toString() {
      return "Core1400100027Out(hbdrateArray=" + this.getHbdrateArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100027Out)) {
         return false;
      } else {
         Core1400100027Out other = (Core1400100027Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$hbdrateArray = this.getHbdrateArray();
            Object other$hbdrateArray = other.getHbdrateArray();
            if (this$hbdrateArray == null) {
               if (other$hbdrateArray != null) {
                  return false;
               }
            } else if (!this$hbdrateArray.equals(other$hbdrateArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100027Out;
   }
   public static class HbdrateArray {
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "业务服务类型",
         notNull = false,
         length = "10",
         remark = "业务服务类型",
         maxSize = 10
      )
      private String busiServiceType;
      @V(
         desc = "浮动点差",
         notNull = false,
         length = "15",
         remark = "浮动点差",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal floatPoint;
      @V(
         desc = "所属机构号",
         notNull = false,
         length = "50",
         remark = "所属机构号",
         maxSize = 50
      )
      private String branch;
      @V(
         desc = "使用状态",
         notNull = false,
         length = "1",
         remark = "使用状态",
         maxSize = 1
      )
      private String useStatus;
      @V(
         desc = "平盘优惠点数",
         notNull = false,
         length = "15",
         remark = "平盘优惠点数",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal uncFloatPoint;

      public String getClientNo() {
         return this.clientNo;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getBusiServiceType() {
         return this.busiServiceType;
      }

      public BigDecimal getFloatPoint() {
         return this.floatPoint;
      }

      public String getBranch() {
         return this.branch;
      }

      public String getUseStatus() {
         return this.useStatus;
      }

      public BigDecimal getUncFloatPoint() {
         return this.uncFloatPoint;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setBusiServiceType(String busiServiceType) {
         this.busiServiceType = busiServiceType;
      }

      public void setFloatPoint(BigDecimal floatPoint) {
         this.floatPoint = floatPoint;
      }

      public void setBranch(String branch) {
         this.branch = branch;
      }

      public void setUseStatus(String useStatus) {
         this.useStatus = useStatus;
      }

      public void setUncFloatPoint(BigDecimal uncFloatPoint) {
         this.uncFloatPoint = uncFloatPoint;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100027Out.HbdrateArray)) {
            return false;
         } else {
            Core1400100027Out.HbdrateArray other = (Core1400100027Out.HbdrateArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label95: {
                  Object this$clientNo = this.getClientNo();
                  Object other$clientNo = other.getClientNo();
                  if (this$clientNo == null) {
                     if (other$clientNo == null) {
                        break label95;
                     }
                  } else if (this$clientNo.equals(other$clientNo)) {
                     break label95;
                  }

                  return false;
               }

               Object this$acctCcy = this.getAcctCcy();
               Object other$acctCcy = other.getAcctCcy();
               if (this$acctCcy == null) {
                  if (other$acctCcy != null) {
                     return false;
                  }
               } else if (!this$acctCcy.equals(other$acctCcy)) {
                  return false;
               }

               Object this$busiServiceType = this.getBusiServiceType();
               Object other$busiServiceType = other.getBusiServiceType();
               if (this$busiServiceType == null) {
                  if (other$busiServiceType != null) {
                     return false;
                  }
               } else if (!this$busiServiceType.equals(other$busiServiceType)) {
                  return false;
               }

               label74: {
                  Object this$floatPoint = this.getFloatPoint();
                  Object other$floatPoint = other.getFloatPoint();
                  if (this$floatPoint == null) {
                     if (other$floatPoint == null) {
                        break label74;
                     }
                  } else if (this$floatPoint.equals(other$floatPoint)) {
                     break label74;
                  }

                  return false;
               }

               label67: {
                  Object this$branch = this.getBranch();
                  Object other$branch = other.getBranch();
                  if (this$branch == null) {
                     if (other$branch == null) {
                        break label67;
                     }
                  } else if (this$branch.equals(other$branch)) {
                     break label67;
                  }

                  return false;
               }

               Object this$useStatus = this.getUseStatus();
               Object other$useStatus = other.getUseStatus();
               if (this$useStatus == null) {
                  if (other$useStatus != null) {
                     return false;
                  }
               } else if (!this$useStatus.equals(other$useStatus)) {
                  return false;
               }

               Object this$uncFloatPoint = this.getUncFloatPoint();
               Object other$uncFloatPoint = other.getUncFloatPoint();
               if (this$uncFloatPoint == null) {
                  if (other$uncFloatPoint != null) {
                     return false;
                  }
               } else if (!this$uncFloatPoint.equals(other$uncFloatPoint)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100027Out.HbdrateArray;
      }
      public String toString() {
         return "Core1400100027Out.HbdrateArray(clientNo=" + this.getClientNo() + ", acctCcy=" + this.getAcctCcy() + ", busiServiceType=" + this.getBusiServiceType() + ", floatPoint=" + this.getFloatPoint() + ", branch=" + this.getBranch() + ", useStatus=" + this.getUseStatus() + ", uncFloatPoint=" + this.getUncFloatPoint() + ")";
      }
   }
}
