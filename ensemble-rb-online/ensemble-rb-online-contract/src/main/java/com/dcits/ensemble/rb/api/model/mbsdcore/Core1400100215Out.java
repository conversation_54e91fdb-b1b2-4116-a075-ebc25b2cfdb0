package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;

@MessageOut
public class Core1400100215Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "余额",
      notNull = false,
      length = "17",
      remark = "余额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal balance;

   public BigDecimal getBalance() {
      return this.balance;
   }

   public void setBalance(BigDecimal balance) {
      this.balance = balance;
   }

   public String toString() {
      return "Core1400100215Out(balance=" + this.getBalance() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100215Out)) {
         return false;
      } else {
         Core1400100215Out other = (Core1400100215Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$balance = this.getBalance();
            Object other$balance = other.getBalance();
            if (this$balance == null) {
               if (other$balance != null) {
                  return false;
               }
            } else if (!this$balance.equals(other$balance)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100215Out;
   }
}
