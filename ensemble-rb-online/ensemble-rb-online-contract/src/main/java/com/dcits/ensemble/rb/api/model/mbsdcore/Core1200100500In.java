package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;

@MessageIn
public class Core1200100500In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1200100500In.Body body;

   public Core1200100500In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1200100500In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1200100500In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200100500In)) {
         return false;
      } else {
         Core1200100500In other = (Core1200100500In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200100500In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "账号/卡号",
         notNull = true,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "账户序号",
         notNull = true,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "转让利率",
         notNull = true,
         length = "15",
         remark = "转让利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal trfRate;
      @V(
         desc = "转让本金",
         notNull = true,
         length = "17",
         remark = "转让本金",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal trfPriAmt;
      @V(
         desc = "转让类型",
         notNull = true,
         length = "2",
         inDesc = "01-全部转让,02-部分转让",
         remark = "转让类型",
         maxSize = 2
      )
      private String trfType;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public BigDecimal getTrfRate() {
         return this.trfRate;
      }

      public BigDecimal getTrfPriAmt() {
         return this.trfPriAmt;
      }

      public String getTrfType() {
         return this.trfType;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setTrfRate(BigDecimal trfRate) {
         this.trfRate = trfRate;
      }

      public void setTrfPriAmt(BigDecimal trfPriAmt) {
         this.trfPriAmt = trfPriAmt;
      }

      public void setTrfType(String trfType) {
         this.trfType = trfType;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1200100500In.Body)) {
            return false;
         } else {
            Core1200100500In.Body other = (Core1200100500In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label95: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label95;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label95;
                  }

                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               Object this$acctCcy = this.getAcctCcy();
               Object other$acctCcy = other.getAcctCcy();
               if (this$acctCcy == null) {
                  if (other$acctCcy != null) {
                     return false;
                  }
               } else if (!this$acctCcy.equals(other$acctCcy)) {
                  return false;
               }

               label74: {
                  Object this$acctSeqNo = this.getAcctSeqNo();
                  Object other$acctSeqNo = other.getAcctSeqNo();
                  if (this$acctSeqNo == null) {
                     if (other$acctSeqNo == null) {
                        break label74;
                     }
                  } else if (this$acctSeqNo.equals(other$acctSeqNo)) {
                     break label74;
                  }

                  return false;
               }

               label67: {
                  Object this$trfRate = this.getTrfRate();
                  Object other$trfRate = other.getTrfRate();
                  if (this$trfRate == null) {
                     if (other$trfRate == null) {
                        break label67;
                     }
                  } else if (this$trfRate.equals(other$trfRate)) {
                     break label67;
                  }

                  return false;
               }

               Object this$trfPriAmt = this.getTrfPriAmt();
               Object other$trfPriAmt = other.getTrfPriAmt();
               if (this$trfPriAmt == null) {
                  if (other$trfPriAmt != null) {
                     return false;
                  }
               } else if (!this$trfPriAmt.equals(other$trfPriAmt)) {
                  return false;
               }

               Object this$trfType = this.getTrfType();
               Object other$trfType = other.getTrfType();
               if (this$trfType == null) {
                  if (other$trfType != null) {
                     return false;
                  }
               } else if (!this$trfType.equals(other$trfType)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1200100500In.Body;
      }
      public String toString() {
         return "Core1200100500In.Body(baseAcctNo=" + this.getBaseAcctNo() + ", prodType=" + this.getProdType() + ", acctCcy=" + this.getAcctCcy() + ", acctSeqNo=" + this.getAcctSeqNo() + ", trfRate=" + this.getTrfRate() + ", trfPriAmt=" + this.getTrfPriAmt() + ", trfType=" + this.getTrfType() + ")";
      }
   }
}
