package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12006011In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12006011Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12006011 {
   String URL = "/rb/nfin/card/reserve/apply/create";


   @ApiRemark("自编卡号生成,teller:2903-手续费收取,3315-银行卡自编卡号申请")
   @ApiDesc("银行自编卡号申请。")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "6011"
   )
   @BusinessCategory("银行卡")
   @FunctionCategory("RB20-借记卡")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core12006011Out runService(Core12006011In var1);
}
