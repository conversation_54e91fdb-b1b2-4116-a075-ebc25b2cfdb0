package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.util.List;

@MessageOut
public class Core1400055114Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400055114Out.StopArray> stopArray;

   public List<Core1400055114Out.StopArray> getStopArray() {
      return this.stopArray;
   }

   public void setStopArray(List<Core1400055114Out.StopArray> stopArray) {
      this.stopArray = stopArray;
   }

   public String toString() {
      return "Core1400055114Out(stopArray=" + this.getStopArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400055114Out)) {
         return false;
      } else {
         Core1400055114Out other = (Core1400055114Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$stopArray = this.getStopArray();
            Object other$stopArray = other.getStopArray();
            if (this$stopArray == null) {
               if (other$stopArray != null) {
                  return false;
               }
            } else if (!this$stopArray.equals(other$stopArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400055114Out;
   }
   public static class StopArray {
      @V(
         desc = "证件类型",
         notNull = false,
         length = "3",
         remark = "证件类型",
         maxSize = 3
      )
      private String documentType;
      @V(
         desc = "证件号码",
         notNull = false,
         length = "50",
         remark = "证件号码",
         maxSize = 50
      )
      private String documentId;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "客户名称",
         notNull = false,
         length = "200",
         remark = "客户名称",
         maxSize = 200
      )
      private String clientName;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "生效日期",
         notNull = false,
         remark = "生效日期"
      )
      private String effectDate;
      @V(
         desc = "结束日期",
         notNull = false,
         remark = "结束日期"
      )
      private String endDate;
      @V(
         desc = "更新日期",
         notNull = false,
         remark = "更新日期"
      )
      private String updateDate;
      @V(
         desc = "账户来源类型",
         notNull = false,
         length = "1",
         in = "H,C,Z",
         remark = "账户来源类型",
         maxSize = 1
      )
      private String acctSourceType;
      @V(
         desc = "暂记非柜面限制类型",
         notNull = false,
         length = "3",
         remark = "暂记非柜面限制类型",
         maxSize = 3
      )
      private String uncounterRestraintType;
      @V(
         desc = "暂停非柜面标记",
         notNull = false,
         length = "1",
         in = "1,2,3,4",
         remark = "暂停非柜面标记",
         maxSize = 1
      )
      private String uncounterRestraintStatus;
      @V(
         desc = "入表原因",
         notNull = false,
         length = "50",
         remark = "入表原因",
         maxSize = 50
      )
      private String uncounterDesc;
      @V(
         desc = "修改柜员",
         notNull = false,
         length = "30",
         remark = "修改柜员",
         maxSize = 30
      )
      private String updateUserId;
      @V(
         desc = "法人",
         notNull = false,
         length = "20",
         remark = "法人",
         maxSize = 20
      )
      private String company;

      public String getDocumentType() {
         return this.documentType;
      }

      public String getDocumentId() {
         return this.documentId;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public String getClientName() {
         return this.clientName;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getEffectDate() {
         return this.effectDate;
      }

      public String getEndDate() {
         return this.endDate;
      }

      public String getUpdateDate() {
         return this.updateDate;
      }

      public String getAcctSourceType() {
         return this.acctSourceType;
      }

      public String getUncounterRestraintType() {
         return this.uncounterRestraintType;
      }

      public String getUncounterRestraintStatus() {
         return this.uncounterRestraintStatus;
      }

      public String getUncounterDesc() {
         return this.uncounterDesc;
      }

      public String getUpdateUserId() {
         return this.updateUserId;
      }

      public String getCompany() {
         return this.company;
      }

      public void setDocumentType(String documentType) {
         this.documentType = documentType;
      }

      public void setDocumentId(String documentId) {
         this.documentId = documentId;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setClientName(String clientName) {
         this.clientName = clientName;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setEffectDate(String effectDate) {
         this.effectDate = effectDate;
      }

      public void setEndDate(String endDate) {
         this.endDate = endDate;
      }

      public void setUpdateDate(String updateDate) {
         this.updateDate = updateDate;
      }

      public void setAcctSourceType(String acctSourceType) {
         this.acctSourceType = acctSourceType;
      }

      public void setUncounterRestraintType(String uncounterRestraintType) {
         this.uncounterRestraintType = uncounterRestraintType;
      }

      public void setUncounterRestraintStatus(String uncounterRestraintStatus) {
         this.uncounterRestraintStatus = uncounterRestraintStatus;
      }

      public void setUncounterDesc(String uncounterDesc) {
         this.uncounterDesc = uncounterDesc;
      }

      public void setUpdateUserId(String updateUserId) {
         this.updateUserId = updateUserId;
      }

      public void setCompany(String company) {
         this.company = company;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400055114Out.StopArray)) {
            return false;
         } else {
            Core1400055114Out.StopArray other = (Core1400055114Out.StopArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$documentType = this.getDocumentType();
               Object other$documentType = other.getDocumentType();
               if (this$documentType == null) {
                  if (other$documentType != null) {
                     return false;
                  }
               } else if (!this$documentType.equals(other$documentType)) {
                  return false;
               }

               Object this$documentId = this.getDocumentId();
               Object other$documentId = other.getDocumentId();
               if (this$documentId == null) {
                  if (other$documentId != null) {
                     return false;
                  }
               } else if (!this$documentId.equals(other$documentId)) {
                  return false;
               }

               Object this$clientNo = this.getClientNo();
               Object other$clientNo = other.getClientNo();
               if (this$clientNo == null) {
                  if (other$clientNo != null) {
                     return false;
                  }
               } else if (!this$clientNo.equals(other$clientNo)) {
                  return false;
               }

               label158: {
                  Object this$clientName = this.getClientName();
                  Object other$clientName = other.getClientName();
                  if (this$clientName == null) {
                     if (other$clientName == null) {
                        break label158;
                     }
                  } else if (this$clientName.equals(other$clientName)) {
                     break label158;
                  }

                  return false;
               }

               label151: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label151;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label151;
                  }

                  return false;
               }

               Object this$effectDate = this.getEffectDate();
               Object other$effectDate = other.getEffectDate();
               if (this$effectDate == null) {
                  if (other$effectDate != null) {
                     return false;
                  }
               } else if (!this$effectDate.equals(other$effectDate)) {
                  return false;
               }

               label137: {
                  Object this$endDate = this.getEndDate();
                  Object other$endDate = other.getEndDate();
                  if (this$endDate == null) {
                     if (other$endDate == null) {
                        break label137;
                     }
                  } else if (this$endDate.equals(other$endDate)) {
                     break label137;
                  }

                  return false;
               }

               label130: {
                  Object this$updateDate = this.getUpdateDate();
                  Object other$updateDate = other.getUpdateDate();
                  if (this$updateDate == null) {
                     if (other$updateDate == null) {
                        break label130;
                     }
                  } else if (this$updateDate.equals(other$updateDate)) {
                     break label130;
                  }

                  return false;
               }

               Object this$acctSourceType = this.getAcctSourceType();
               Object other$acctSourceType = other.getAcctSourceType();
               if (this$acctSourceType == null) {
                  if (other$acctSourceType != null) {
                     return false;
                  }
               } else if (!this$acctSourceType.equals(other$acctSourceType)) {
                  return false;
               }

               Object this$uncounterRestraintType = this.getUncounterRestraintType();
               Object other$uncounterRestraintType = other.getUncounterRestraintType();
               if (this$uncounterRestraintType == null) {
                  if (other$uncounterRestraintType != null) {
                     return false;
                  }
               } else if (!this$uncounterRestraintType.equals(other$uncounterRestraintType)) {
                  return false;
               }

               label109: {
                  Object this$uncounterRestraintStatus = this.getUncounterRestraintStatus();
                  Object other$uncounterRestraintStatus = other.getUncounterRestraintStatus();
                  if (this$uncounterRestraintStatus == null) {
                     if (other$uncounterRestraintStatus == null) {
                        break label109;
                     }
                  } else if (this$uncounterRestraintStatus.equals(other$uncounterRestraintStatus)) {
                     break label109;
                  }

                  return false;
               }

               label102: {
                  Object this$uncounterDesc = this.getUncounterDesc();
                  Object other$uncounterDesc = other.getUncounterDesc();
                  if (this$uncounterDesc == null) {
                     if (other$uncounterDesc == null) {
                        break label102;
                     }
                  } else if (this$uncounterDesc.equals(other$uncounterDesc)) {
                     break label102;
                  }

                  return false;
               }

               Object this$updateUserId = this.getUpdateUserId();
               Object other$updateUserId = other.getUpdateUserId();
               if (this$updateUserId == null) {
                  if (other$updateUserId != null) {
                     return false;
                  }
               } else if (!this$updateUserId.equals(other$updateUserId)) {
                  return false;
               }

               Object this$company = this.getCompany();
               Object other$company = other.getCompany();
               if (this$company == null) {
                  if (other$company != null) {
                     return false;
                  }
               } else if (!this$company.equals(other$company)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400055114Out.StopArray;
      }
      public String toString() {
         return "Core1400055114Out.StopArray(documentType=" + this.getDocumentType() + ", documentId=" + this.getDocumentId() + ", clientNo=" + this.getClientNo() + ", clientName=" + this.getClientName() + ", baseAcctNo=" + this.getBaseAcctNo() + ", effectDate=" + this.getEffectDate() + ", endDate=" + this.getEndDate() + ", updateDate=" + this.getUpdateDate() + ", acctSourceType=" + this.getAcctSourceType() + ", uncounterRestraintType=" + this.getUncounterRestraintType() + ", uncounterRestraintStatus=" + this.getUncounterRestraintStatus() + ", uncounterDesc=" + this.getUncounterDesc() + ", updateUserId=" + this.getUpdateUserId() + ", company=" + this.getCompany() + ")";
      }
   }
}
