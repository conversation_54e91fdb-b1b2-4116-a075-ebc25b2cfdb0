package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;

@MessageOut
public class Core1200100103Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "限制编号",
      notNull = false,
      length = "50",
      remark = "限制编号",
      maxSize = 50
   )
   private String resSeqNo;

   public String getResSeqNo() {
      return this.resSeqNo;
   }

   public void setResSeqNo(String resSeqNo) {
      this.resSeqNo = resSeqNo;
   }

   public String toString() {
      return "Core1200100103Out(resSeqNo=" + this.getResSeqNo() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200100103Out)) {
         return false;
      } else {
         Core1200100103Out other = (Core1200100103Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$resSeqNo = this.getResSeqNo();
            Object other$resSeqNo = other.getResSeqNo();
            if (this$resSeqNo == null) {
               if (other$resSeqNo != null) {
                  return false;
               }
            } else if (!this$resSeqNo.equals(other$resSeqNo)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200100103Out;
   }
}
