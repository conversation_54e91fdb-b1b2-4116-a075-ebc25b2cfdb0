package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000236In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000236Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000236 {
   String URL = "/rb/inq/dc/stageDescQuery";

   
   @ApiRemark("大额存单期次代码信息查询")
   @ApiDesc("大额存单期次代码信息查询")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0236"
   )
   @FunctionCategory("RB10-大额存单")
   @ConsumeSys("EOS")
   Core14000236Out runService(Core14000236In var1);
}
