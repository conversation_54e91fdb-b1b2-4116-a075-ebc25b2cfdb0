package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000243In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000243Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000243 {
   String URL = "/rb/inq/term/dc/hist";


   @ApiRemark("大额存单交易历史查询")
   @ApiDesc("大额存单账户交易明细查询业务,包含流水信息和账户信息")
   @EnsembleElements(
      serviceCode = "Core",
      messageType = "1400",
      messageCode = "0243"
   )
   @FunctionCategory("RB10-大额存单")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core14000243Out runService(Core14000243In var1);
}
