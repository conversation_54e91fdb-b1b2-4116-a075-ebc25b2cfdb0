package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;

@MessageIn
public class Core1400100711In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400100711In.Body body;

   public Core1400100711In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400100711In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400100711In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100711In)) {
         return false;
      } else {
         Core1400100711In other = (Core1400100711In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100711In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "账号/卡号",
         notNull = true,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "账户序号",
         notNull = true,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "资金池账户组ID",
         notNull = false,
         length = "30",
         remark = "资金池账户组ID",
         maxSize = 30
      )
      private String pcpGroupId;
      @V(
         desc = "资金池产品类型",
         notNull = false,
         length = "20",
         remark = "资金池产品类型",
         maxSize = 20
      )
      private String pcpProdType;
      @V(
         desc = "账户升降级表示符",
         notNull = false,
         length = "10",
         inDesc = "UP-升级,DOWN-降级",
         remark = "账户升降级表示符",
         maxSize = 10
      )
      private String upDownType;
      @V(
         desc = "开始日期",
         notNull = false,
         remark = "开始日期"
      )
      private String startDate;
      @V(
         desc = "结束日期",
         notNull = false,
         remark = "结束日期"
      )
      private String endDate;
      @V(
         desc = "起始金额",
         notNull = false,
         length = "17",
         remark = "起始金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal startAmt;
      @V(
         desc = "截止金额",
         notNull = false,
         length = "17",
         remark = "截止金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal endAmt;
      @V(
         desc = "交易参考号",
         notNull = false,
         length = "50",
         remark = "交易参考号",
         maxSize = 50
      )
      private String reference;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getPcpGroupId() {
         return this.pcpGroupId;
      }

      public String getPcpProdType() {
         return this.pcpProdType;
      }

      public String getUpDownType() {
         return this.upDownType;
      }

      public String getStartDate() {
         return this.startDate;
      }

      public String getEndDate() {
         return this.endDate;
      }

      public BigDecimal getStartAmt() {
         return this.startAmt;
      }

      public BigDecimal getEndAmt() {
         return this.endAmt;
      }

      public String getReference() {
         return this.reference;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setPcpGroupId(String pcpGroupId) {
         this.pcpGroupId = pcpGroupId;
      }

      public void setPcpProdType(String pcpProdType) {
         this.pcpProdType = pcpProdType;
      }

      public void setUpDownType(String upDownType) {
         this.upDownType = upDownType;
      }

      public void setStartDate(String startDate) {
         this.startDate = startDate;
      }

      public void setEndDate(String endDate) {
         this.endDate = endDate;
      }

      public void setStartAmt(BigDecimal startAmt) {
         this.startAmt = startAmt;
      }

      public void setEndAmt(BigDecimal endAmt) {
         this.endAmt = endAmt;
      }

      public void setReference(String reference) {
         this.reference = reference;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100711In.Body)) {
            return false;
         } else {
            Core1400100711In.Body other = (Core1400100711In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label155: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label155;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label155;
                  }

                  return false;
               }

               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo != null) {
                     return false;
                  }
               } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               label134: {
                  Object this$acctCcy = this.getAcctCcy();
                  Object other$acctCcy = other.getAcctCcy();
                  if (this$acctCcy == null) {
                     if (other$acctCcy == null) {
                        break label134;
                     }
                  } else if (this$acctCcy.equals(other$acctCcy)) {
                     break label134;
                  }

                  return false;
               }

               label127: {
                  Object this$pcpGroupId = this.getPcpGroupId();
                  Object other$pcpGroupId = other.getPcpGroupId();
                  if (this$pcpGroupId == null) {
                     if (other$pcpGroupId == null) {
                        break label127;
                     }
                  } else if (this$pcpGroupId.equals(other$pcpGroupId)) {
                     break label127;
                  }

                  return false;
               }

               label120: {
                  Object this$pcpProdType = this.getPcpProdType();
                  Object other$pcpProdType = other.getPcpProdType();
                  if (this$pcpProdType == null) {
                     if (other$pcpProdType == null) {
                        break label120;
                     }
                  } else if (this$pcpProdType.equals(other$pcpProdType)) {
                     break label120;
                  }

                  return false;
               }

               Object this$upDownType = this.getUpDownType();
               Object other$upDownType = other.getUpDownType();
               if (this$upDownType == null) {
                  if (other$upDownType != null) {
                     return false;
                  }
               } else if (!this$upDownType.equals(other$upDownType)) {
                  return false;
               }

               label106: {
                  Object this$startDate = this.getStartDate();
                  Object other$startDate = other.getStartDate();
                  if (this$startDate == null) {
                     if (other$startDate == null) {
                        break label106;
                     }
                  } else if (this$startDate.equals(other$startDate)) {
                     break label106;
                  }

                  return false;
               }

               Object this$endDate = this.getEndDate();
               Object other$endDate = other.getEndDate();
               if (this$endDate == null) {
                  if (other$endDate != null) {
                     return false;
                  }
               } else if (!this$endDate.equals(other$endDate)) {
                  return false;
               }

               label92: {
                  Object this$startAmt = this.getStartAmt();
                  Object other$startAmt = other.getStartAmt();
                  if (this$startAmt == null) {
                     if (other$startAmt == null) {
                        break label92;
                     }
                  } else if (this$startAmt.equals(other$startAmt)) {
                     break label92;
                  }

                  return false;
               }

               Object this$endAmt = this.getEndAmt();
               Object other$endAmt = other.getEndAmt();
               if (this$endAmt == null) {
                  if (other$endAmt != null) {
                     return false;
                  }
               } else if (!this$endAmt.equals(other$endAmt)) {
                  return false;
               }

               Object this$reference = this.getReference();
               Object other$reference = other.getReference();
               if (this$reference == null) {
                  if (other$reference != null) {
                     return false;
                  }
               } else if (!this$reference.equals(other$reference)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100711In.Body;
      }
      public String toString() {
         return "Core1400100711In.Body(baseAcctNo=" + this.getBaseAcctNo() + ", acctSeqNo=" + this.getAcctSeqNo() + ", prodType=" + this.getProdType() + ", acctCcy=" + this.getAcctCcy() + ", pcpGroupId=" + this.getPcpGroupId() + ", pcpProdType=" + this.getPcpProdType() + ", upDownType=" + this.getUpDownType() + ", startDate=" + this.getStartDate() + ", endDate=" + this.getEndDate() + ", startAmt=" + this.getStartAmt() + ", endAmt=" + this.getEndAmt() + ", reference=" + this.getReference() + ")";
      }
   }
}
