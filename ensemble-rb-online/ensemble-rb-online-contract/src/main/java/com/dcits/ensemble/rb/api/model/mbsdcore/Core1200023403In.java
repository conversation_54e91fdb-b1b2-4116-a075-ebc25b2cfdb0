package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;

@MessageIn
public class Core1200023403In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1200023403In.Body body;

   public Core1200023403In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1200023403In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1200023403In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200023403In)) {
         return false;
      } else {
         Core1200023403In other = (Core1200023403In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200023403In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "挂失编号",
         notNull = false,
         length = "50",
         remark = "挂失编号",
         maxSize = 50
      )
      private String lostNo;
      @V(
         desc = "凭证类型",
         notNull = false,
         length = "10",
         remark = "凭证类型",
         maxSize = 10
      )
      private String docType;
      @V(
         desc = "票据号码",
         notNull = false,
         length = "50",
         remark = "票据号码",
         maxSize = 50
      )
      private String billNo;
      @V(
         desc = "票据登记日期",
         notNull = false,
         remark = "票据登记日期"
      )
      private String billSignDate;
      @V(
         desc = "票据类型",
         notNull = false,
         length = "5",
         inDesc = "P-纸质,E-电子,CT00-可转让汇票,CT01-不可转让汇票,CT02-现金汇票",
         remark = "票据类型1-现金本票/现金汇票,2-可转让转账本票/转账汇票,3-不可转让转账本票",
         restraint = "1-现金本票/现金汇票,2-可转让转账本票/转账汇票,3-不可转让转账本票",
         maxSize = 5
      )
      private String billType;
      @V(
         desc = "签约类型",
         notNull = false,
         length = "20",
         remark = "签约类型1-现金,2-转账,3-销挂账",
         restraint = "1-现金,2-转账,3-销挂账",
         maxSize = 20
      )
      private String signType;
      @V(
         desc = "票面金额",
         notNull = false,
         length = "17",
         remark = "票面金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal billAmt;
      @V(
         desc = "付款人账号",
         notNull = false,
         length = "50",
         remark = "付款人账号",
         maxSize = 50
      )
      private String payerBaseAcctNo;
      @V(
         desc = "付款人账户名称",
         notNull = false,
         length = "200",
         remark = "付款人账户名称",
         maxSize = 200
      )
      private String payerAcctName;
      @V(
         desc = "到期日期",
         notNull = false,
         remark = "到期日期"
      )
      private String maturityDate;
      @V(
         desc = "通知书编号",
         notNull = false,
         length = "50",
         remark = "通知书编号",
         maxSize = 50
      )
      private String adviceNoteNo;
      @V(
         desc = "收款人账号",
         notNull = false,
         length = "50",
         remark = "收款人账号",
         maxSize = 50
      )
      private String payeeBaseAcctNo;
      @V(
         desc = "收款人账户序列号",
         notNull = false,
         length = "5",
         remark = "收款人账户序列号",
         maxSize = 5
      )
      private String payeeAcctSeqNo;
      @V(
         desc = "收款人账户产品类型",
         notNull = false,
         length = "20",
         remark = "收款人账户产品类型",
         maxSize = 20
      )
      private String payeeProdType;
      @V(
         desc = "收款人账户币种",
         notNull = false,
         length = "3",
         remark = "收款人账户币种",
         maxSize = 3
      )
      private String payeeAcctCcy;
      @V(
         desc = "收款人名称",
         notNull = false,
         length = "200",
         remark = "收款人名称",
         maxSize = 200
      )
      private String payeeName;
      @V(
         desc = "申请人名称",
         notNull = false,
         length = "200",
         remark = "申请人名称",
         maxSize = 200
      )
      private String applyerAcctName;
      @V(
         desc = "执法人1姓名",
         notNull = false,
         length = "200",
         remark = "执法人1姓名",
         maxSize = 200
      )
      private String judiciaryOfficerName;
      @V(
         desc = "执法人1证件类型",
         notNull = false,
         length = "3",
         remark = "执法人1证件类型",
         maxSize = 3
      )
      private String judiciaryDocumentType;
      @V(
         desc = "执法人1证件类型2",
         notNull = false,
         length = "3",
         remark = "执法人1证件类型2",
         maxSize = 3
      )
      private String judiciaryDocumentType2;
      @V(
         desc = "执法人1证件号码",
         notNull = false,
         length = "50",
         remark = "执法人1证件号码",
         maxSize = 50
      )
      private String judiciaryDocumentId;
      @V(
         desc = "执法人1证件号码2",
         notNull = false,
         length = "50",
         remark = "执法人1证件号码2",
         maxSize = 50
      )
      private String judiciaryDocumentId2;
      @V(
         desc = "执法人2姓名",
         notNull = false,
         length = "200",
         remark = "执法人2姓名",
         maxSize = 200
      )
      private String judiciaryOthOfficerName;
      @V(
         desc = "执法人2证件类型",
         notNull = false,
         length = "3",
         remark = "执法人2证件类型",
         maxSize = 3
      )
      private String judiciaryOthDocumentType;
      @V(
         desc = "执法人2证件类型2",
         notNull = false,
         length = "3",
         remark = "执法人2证件类型2",
         maxSize = 3
      )
      private String judiciaryOthDocumentType2;
      @V(
         desc = "执法人2证件号码",
         notNull = false,
         length = "50",
         remark = "执法人2证件号码",
         maxSize = 50
      )
      private String judiciaryOthDocumentId;
      @V(
         desc = "执法人2证件号码2",
         notNull = false,
         length = "50",
         remark = "执法人2证件号码2",
         maxSize = 50
      )
      private String judiciaryOthDocumentId2;
      @V(
         desc = "挂失起始日期",
         notNull = false,
         remark = "挂失起始日期"
      )
      private String lostBeginDate;
      @V(
         desc = "挂失原因",
         notNull = false,
         length = "200",
         remark = "挂失原因",
         maxSize = 200
      )
      private String lostReason;
      @V(
         desc = "变更操作方式",
         notNull = false,
         length = "2",
         inDesc = "SC-单笔,BC-批量,00-签发录入,01-复核,02-兑付,03-退回,04-挂失,05-解挂,06-签发修改,07-签发删除,11-复核,12-核对,13-移存,14-退回申请,15-挂失,16-解挂",
         remark = "变更操作方式",
         restraint = "0-正常,1-口挂,2-书面挂失,3-撤挂,4-法院冻结,5-解冻结",
         maxSize = 2
      )
      private String operateType;
      @V(
         desc = "解挂原因",
         notNull = false,
         length = "200",
         remark = "解挂原因",
         maxSize = 200
      )
      private String unlostReason;
      @V(
         desc = "解挂日期",
         notNull = false,
         remark = "解挂日期"
      )
      private String unlostDate;
      @V(
         desc = "挂失申请书编号",
         notNull = false,
         length = "50",
         remark = "挂失申请书编号",
         maxSize = 50
      )
      private String lossNo;
      @V(
         desc = "挂失截至日期",
         notNull = false,
         remark = "挂失截至日期"
      )
      private String lostEndDate;
      @V(
         desc = "入账方式",
         notNull = false,
         length = "1",
         inDesc = "B-批量插入状态为,O-流水插入状态为",
         remark = "入账方式1-客户账 2-销挂账",
         restraint = "1-客户账,2-销挂账",
         maxSize = 1
      )
      private String inStatus;

      public String getLostNo() {
         return this.lostNo;
      }

      public String getDocType() {
         return this.docType;
      }

      public String getBillNo() {
         return this.billNo;
      }

      public String getBillSignDate() {
         return this.billSignDate;
      }

      public String getBillType() {
         return this.billType;
      }

      public String getSignType() {
         return this.signType;
      }

      public BigDecimal getBillAmt() {
         return this.billAmt;
      }

      public String getPayerBaseAcctNo() {
         return this.payerBaseAcctNo;
      }

      public String getPayerAcctName() {
         return this.payerAcctName;
      }

      public String getMaturityDate() {
         return this.maturityDate;
      }

      public String getAdviceNoteNo() {
         return this.adviceNoteNo;
      }

      public String getPayeeBaseAcctNo() {
         return this.payeeBaseAcctNo;
      }

      public String getPayeeAcctSeqNo() {
         return this.payeeAcctSeqNo;
      }

      public String getPayeeProdType() {
         return this.payeeProdType;
      }

      public String getPayeeAcctCcy() {
         return this.payeeAcctCcy;
      }

      public String getPayeeName() {
         return this.payeeName;
      }

      public String getApplyerAcctName() {
         return this.applyerAcctName;
      }

      public String getJudiciaryOfficerName() {
         return this.judiciaryOfficerName;
      }

      public String getJudiciaryDocumentType() {
         return this.judiciaryDocumentType;
      }

      public String getJudiciaryDocumentType2() {
         return this.judiciaryDocumentType2;
      }

      public String getJudiciaryDocumentId() {
         return this.judiciaryDocumentId;
      }

      public String getJudiciaryDocumentId2() {
         return this.judiciaryDocumentId2;
      }

      public String getJudiciaryOthOfficerName() {
         return this.judiciaryOthOfficerName;
      }

      public String getJudiciaryOthDocumentType() {
         return this.judiciaryOthDocumentType;
      }

      public String getJudiciaryOthDocumentType2() {
         return this.judiciaryOthDocumentType2;
      }

      public String getJudiciaryOthDocumentId() {
         return this.judiciaryOthDocumentId;
      }

      public String getJudiciaryOthDocumentId2() {
         return this.judiciaryOthDocumentId2;
      }

      public String getLostBeginDate() {
         return this.lostBeginDate;
      }

      public String getLostReason() {
         return this.lostReason;
      }

      public String getOperateType() {
         return this.operateType;
      }

      public String getUnlostReason() {
         return this.unlostReason;
      }

      public String getUnlostDate() {
         return this.unlostDate;
      }

      public String getLossNo() {
         return this.lossNo;
      }

      public String getLostEndDate() {
         return this.lostEndDate;
      }

      public String getInStatus() {
         return this.inStatus;
      }

      public void setLostNo(String lostNo) {
         this.lostNo = lostNo;
      }

      public void setDocType(String docType) {
         this.docType = docType;
      }

      public void setBillNo(String billNo) {
         this.billNo = billNo;
      }

      public void setBillSignDate(String billSignDate) {
         this.billSignDate = billSignDate;
      }

      public void setBillType(String billType) {
         this.billType = billType;
      }

      public void setSignType(String signType) {
         this.signType = signType;
      }

      public void setBillAmt(BigDecimal billAmt) {
         this.billAmt = billAmt;
      }

      public void setPayerBaseAcctNo(String payerBaseAcctNo) {
         this.payerBaseAcctNo = payerBaseAcctNo;
      }

      public void setPayerAcctName(String payerAcctName) {
         this.payerAcctName = payerAcctName;
      }

      public void setMaturityDate(String maturityDate) {
         this.maturityDate = maturityDate;
      }

      public void setAdviceNoteNo(String adviceNoteNo) {
         this.adviceNoteNo = adviceNoteNo;
      }

      public void setPayeeBaseAcctNo(String payeeBaseAcctNo) {
         this.payeeBaseAcctNo = payeeBaseAcctNo;
      }

      public void setPayeeAcctSeqNo(String payeeAcctSeqNo) {
         this.payeeAcctSeqNo = payeeAcctSeqNo;
      }

      public void setPayeeProdType(String payeeProdType) {
         this.payeeProdType = payeeProdType;
      }

      public void setPayeeAcctCcy(String payeeAcctCcy) {
         this.payeeAcctCcy = payeeAcctCcy;
      }

      public void setPayeeName(String payeeName) {
         this.payeeName = payeeName;
      }

      public void setApplyerAcctName(String applyerAcctName) {
         this.applyerAcctName = applyerAcctName;
      }

      public void setJudiciaryOfficerName(String judiciaryOfficerName) {
         this.judiciaryOfficerName = judiciaryOfficerName;
      }

      public void setJudiciaryDocumentType(String judiciaryDocumentType) {
         this.judiciaryDocumentType = judiciaryDocumentType;
      }

      public void setJudiciaryDocumentType2(String judiciaryDocumentType2) {
         this.judiciaryDocumentType2 = judiciaryDocumentType2;
      }

      public void setJudiciaryDocumentId(String judiciaryDocumentId) {
         this.judiciaryDocumentId = judiciaryDocumentId;
      }

      public void setJudiciaryDocumentId2(String judiciaryDocumentId2) {
         this.judiciaryDocumentId2 = judiciaryDocumentId2;
      }

      public void setJudiciaryOthOfficerName(String judiciaryOthOfficerName) {
         this.judiciaryOthOfficerName = judiciaryOthOfficerName;
      }

      public void setJudiciaryOthDocumentType(String judiciaryOthDocumentType) {
         this.judiciaryOthDocumentType = judiciaryOthDocumentType;
      }

      public void setJudiciaryOthDocumentType2(String judiciaryOthDocumentType2) {
         this.judiciaryOthDocumentType2 = judiciaryOthDocumentType2;
      }

      public void setJudiciaryOthDocumentId(String judiciaryOthDocumentId) {
         this.judiciaryOthDocumentId = judiciaryOthDocumentId;
      }

      public void setJudiciaryOthDocumentId2(String judiciaryOthDocumentId2) {
         this.judiciaryOthDocumentId2 = judiciaryOthDocumentId2;
      }

      public void setLostBeginDate(String lostBeginDate) {
         this.lostBeginDate = lostBeginDate;
      }

      public void setLostReason(String lostReason) {
         this.lostReason = lostReason;
      }

      public void setOperateType(String operateType) {
         this.operateType = operateType;
      }

      public void setUnlostReason(String unlostReason) {
         this.unlostReason = unlostReason;
      }

      public void setUnlostDate(String unlostDate) {
         this.unlostDate = unlostDate;
      }

      public void setLossNo(String lossNo) {
         this.lossNo = lossNo;
      }

      public void setLostEndDate(String lostEndDate) {
         this.lostEndDate = lostEndDate;
      }

      public void setInStatus(String inStatus) {
         this.inStatus = inStatus;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1200023403In.Body)) {
            return false;
         } else {
            Core1200023403In.Body other = (Core1200023403In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label431: {
                  Object this$lostNo = this.getLostNo();
                  Object other$lostNo = other.getLostNo();
                  if (this$lostNo == null) {
                     if (other$lostNo == null) {
                        break label431;
                     }
                  } else if (this$lostNo.equals(other$lostNo)) {
                     break label431;
                  }

                  return false;
               }

               Object this$docType = this.getDocType();
               Object other$docType = other.getDocType();
               if (this$docType == null) {
                  if (other$docType != null) {
                     return false;
                  }
               } else if (!this$docType.equals(other$docType)) {
                  return false;
               }

               Object this$billNo = this.getBillNo();
               Object other$billNo = other.getBillNo();
               if (this$billNo == null) {
                  if (other$billNo != null) {
                     return false;
                  }
               } else if (!this$billNo.equals(other$billNo)) {
                  return false;
               }

               label410: {
                  Object this$billSignDate = this.getBillSignDate();
                  Object other$billSignDate = other.getBillSignDate();
                  if (this$billSignDate == null) {
                     if (other$billSignDate == null) {
                        break label410;
                     }
                  } else if (this$billSignDate.equals(other$billSignDate)) {
                     break label410;
                  }

                  return false;
               }

               label403: {
                  Object this$billType = this.getBillType();
                  Object other$billType = other.getBillType();
                  if (this$billType == null) {
                     if (other$billType == null) {
                        break label403;
                     }
                  } else if (this$billType.equals(other$billType)) {
                     break label403;
                  }

                  return false;
               }

               Object this$signType = this.getSignType();
               Object other$signType = other.getSignType();
               if (this$signType == null) {
                  if (other$signType != null) {
                     return false;
                  }
               } else if (!this$signType.equals(other$signType)) {
                  return false;
               }

               Object this$billAmt = this.getBillAmt();
               Object other$billAmt = other.getBillAmt();
               if (this$billAmt == null) {
                  if (other$billAmt != null) {
                     return false;
                  }
               } else if (!this$billAmt.equals(other$billAmt)) {
                  return false;
               }

               label382: {
                  Object this$payerBaseAcctNo = this.getPayerBaseAcctNo();
                  Object other$payerBaseAcctNo = other.getPayerBaseAcctNo();
                  if (this$payerBaseAcctNo == null) {
                     if (other$payerBaseAcctNo == null) {
                        break label382;
                     }
                  } else if (this$payerBaseAcctNo.equals(other$payerBaseAcctNo)) {
                     break label382;
                  }

                  return false;
               }

               label375: {
                  Object this$payerAcctName = this.getPayerAcctName();
                  Object other$payerAcctName = other.getPayerAcctName();
                  if (this$payerAcctName == null) {
                     if (other$payerAcctName == null) {
                        break label375;
                     }
                  } else if (this$payerAcctName.equals(other$payerAcctName)) {
                     break label375;
                  }

                  return false;
               }

               Object this$maturityDate = this.getMaturityDate();
               Object other$maturityDate = other.getMaturityDate();
               if (this$maturityDate == null) {
                  if (other$maturityDate != null) {
                     return false;
                  }
               } else if (!this$maturityDate.equals(other$maturityDate)) {
                  return false;
               }

               label361: {
                  Object this$adviceNoteNo = this.getAdviceNoteNo();
                  Object other$adviceNoteNo = other.getAdviceNoteNo();
                  if (this$adviceNoteNo == null) {
                     if (other$adviceNoteNo == null) {
                        break label361;
                     }
                  } else if (this$adviceNoteNo.equals(other$adviceNoteNo)) {
                     break label361;
                  }

                  return false;
               }

               Object this$payeeBaseAcctNo = this.getPayeeBaseAcctNo();
               Object other$payeeBaseAcctNo = other.getPayeeBaseAcctNo();
               if (this$payeeBaseAcctNo == null) {
                  if (other$payeeBaseAcctNo != null) {
                     return false;
                  }
               } else if (!this$payeeBaseAcctNo.equals(other$payeeBaseAcctNo)) {
                  return false;
               }

               label347: {
                  Object this$payeeAcctSeqNo = this.getPayeeAcctSeqNo();
                  Object other$payeeAcctSeqNo = other.getPayeeAcctSeqNo();
                  if (this$payeeAcctSeqNo == null) {
                     if (other$payeeAcctSeqNo == null) {
                        break label347;
                     }
                  } else if (this$payeeAcctSeqNo.equals(other$payeeAcctSeqNo)) {
                     break label347;
                  }

                  return false;
               }

               Object this$payeeProdType = this.getPayeeProdType();
               Object other$payeeProdType = other.getPayeeProdType();
               if (this$payeeProdType == null) {
                  if (other$payeeProdType != null) {
                     return false;
                  }
               } else if (!this$payeeProdType.equals(other$payeeProdType)) {
                  return false;
               }

               Object this$payeeAcctCcy = this.getPayeeAcctCcy();
               Object other$payeeAcctCcy = other.getPayeeAcctCcy();
               if (this$payeeAcctCcy == null) {
                  if (other$payeeAcctCcy != null) {
                     return false;
                  }
               } else if (!this$payeeAcctCcy.equals(other$payeeAcctCcy)) {
                  return false;
               }

               label326: {
                  Object this$payeeName = this.getPayeeName();
                  Object other$payeeName = other.getPayeeName();
                  if (this$payeeName == null) {
                     if (other$payeeName == null) {
                        break label326;
                     }
                  } else if (this$payeeName.equals(other$payeeName)) {
                     break label326;
                  }

                  return false;
               }

               label319: {
                  Object this$applyerAcctName = this.getApplyerAcctName();
                  Object other$applyerAcctName = other.getApplyerAcctName();
                  if (this$applyerAcctName == null) {
                     if (other$applyerAcctName == null) {
                        break label319;
                     }
                  } else if (this$applyerAcctName.equals(other$applyerAcctName)) {
                     break label319;
                  }

                  return false;
               }

               Object this$judiciaryOfficerName = this.getJudiciaryOfficerName();
               Object other$judiciaryOfficerName = other.getJudiciaryOfficerName();
               if (this$judiciaryOfficerName == null) {
                  if (other$judiciaryOfficerName != null) {
                     return false;
                  }
               } else if (!this$judiciaryOfficerName.equals(other$judiciaryOfficerName)) {
                  return false;
               }

               Object this$judiciaryDocumentType = this.getJudiciaryDocumentType();
               Object other$judiciaryDocumentType = other.getJudiciaryDocumentType();
               if (this$judiciaryDocumentType == null) {
                  if (other$judiciaryDocumentType != null) {
                     return false;
                  }
               } else if (!this$judiciaryDocumentType.equals(other$judiciaryDocumentType)) {
                  return false;
               }

               label298: {
                  Object this$judiciaryDocumentType2 = this.getJudiciaryDocumentType2();
                  Object other$judiciaryDocumentType2 = other.getJudiciaryDocumentType2();
                  if (this$judiciaryDocumentType2 == null) {
                     if (other$judiciaryDocumentType2 == null) {
                        break label298;
                     }
                  } else if (this$judiciaryDocumentType2.equals(other$judiciaryDocumentType2)) {
                     break label298;
                  }

                  return false;
               }

               label291: {
                  Object this$judiciaryDocumentId = this.getJudiciaryDocumentId();
                  Object other$judiciaryDocumentId = other.getJudiciaryDocumentId();
                  if (this$judiciaryDocumentId == null) {
                     if (other$judiciaryDocumentId == null) {
                        break label291;
                     }
                  } else if (this$judiciaryDocumentId.equals(other$judiciaryDocumentId)) {
                     break label291;
                  }

                  return false;
               }

               Object this$judiciaryDocumentId2 = this.getJudiciaryDocumentId2();
               Object other$judiciaryDocumentId2 = other.getJudiciaryDocumentId2();
               if (this$judiciaryDocumentId2 == null) {
                  if (other$judiciaryDocumentId2 != null) {
                     return false;
                  }
               } else if (!this$judiciaryDocumentId2.equals(other$judiciaryDocumentId2)) {
                  return false;
               }

               Object this$judiciaryOthOfficerName = this.getJudiciaryOthOfficerName();
               Object other$judiciaryOthOfficerName = other.getJudiciaryOthOfficerName();
               if (this$judiciaryOthOfficerName == null) {
                  if (other$judiciaryOthOfficerName != null) {
                     return false;
                  }
               } else if (!this$judiciaryOthOfficerName.equals(other$judiciaryOthOfficerName)) {
                  return false;
               }

               label270: {
                  Object this$judiciaryOthDocumentType = this.getJudiciaryOthDocumentType();
                  Object other$judiciaryOthDocumentType = other.getJudiciaryOthDocumentType();
                  if (this$judiciaryOthDocumentType == null) {
                     if (other$judiciaryOthDocumentType == null) {
                        break label270;
                     }
                  } else if (this$judiciaryOthDocumentType.equals(other$judiciaryOthDocumentType)) {
                     break label270;
                  }

                  return false;
               }

               label263: {
                  Object this$judiciaryOthDocumentType2 = this.getJudiciaryOthDocumentType2();
                  Object other$judiciaryOthDocumentType2 = other.getJudiciaryOthDocumentType2();
                  if (this$judiciaryOthDocumentType2 == null) {
                     if (other$judiciaryOthDocumentType2 == null) {
                        break label263;
                     }
                  } else if (this$judiciaryOthDocumentType2.equals(other$judiciaryOthDocumentType2)) {
                     break label263;
                  }

                  return false;
               }

               Object this$judiciaryOthDocumentId = this.getJudiciaryOthDocumentId();
               Object other$judiciaryOthDocumentId = other.getJudiciaryOthDocumentId();
               if (this$judiciaryOthDocumentId == null) {
                  if (other$judiciaryOthDocumentId != null) {
                     return false;
                  }
               } else if (!this$judiciaryOthDocumentId.equals(other$judiciaryOthDocumentId)) {
                  return false;
               }

               label249: {
                  Object this$judiciaryOthDocumentId2 = this.getJudiciaryOthDocumentId2();
                  Object other$judiciaryOthDocumentId2 = other.getJudiciaryOthDocumentId2();
                  if (this$judiciaryOthDocumentId2 == null) {
                     if (other$judiciaryOthDocumentId2 == null) {
                        break label249;
                     }
                  } else if (this$judiciaryOthDocumentId2.equals(other$judiciaryOthDocumentId2)) {
                     break label249;
                  }

                  return false;
               }

               Object this$lostBeginDate = this.getLostBeginDate();
               Object other$lostBeginDate = other.getLostBeginDate();
               if (this$lostBeginDate == null) {
                  if (other$lostBeginDate != null) {
                     return false;
                  }
               } else if (!this$lostBeginDate.equals(other$lostBeginDate)) {
                  return false;
               }

               label235: {
                  Object this$lostReason = this.getLostReason();
                  Object other$lostReason = other.getLostReason();
                  if (this$lostReason == null) {
                     if (other$lostReason == null) {
                        break label235;
                     }
                  } else if (this$lostReason.equals(other$lostReason)) {
                     break label235;
                  }

                  return false;
               }

               Object this$operateType = this.getOperateType();
               Object other$operateType = other.getOperateType();
               if (this$operateType == null) {
                  if (other$operateType != null) {
                     return false;
                  }
               } else if (!this$operateType.equals(other$operateType)) {
                  return false;
               }

               Object this$unlostReason = this.getUnlostReason();
               Object other$unlostReason = other.getUnlostReason();
               if (this$unlostReason == null) {
                  if (other$unlostReason != null) {
                     return false;
                  }
               } else if (!this$unlostReason.equals(other$unlostReason)) {
                  return false;
               }

               label214: {
                  Object this$unlostDate = this.getUnlostDate();
                  Object other$unlostDate = other.getUnlostDate();
                  if (this$unlostDate == null) {
                     if (other$unlostDate == null) {
                        break label214;
                     }
                  } else if (this$unlostDate.equals(other$unlostDate)) {
                     break label214;
                  }

                  return false;
               }

               label207: {
                  Object this$lossNo = this.getLossNo();
                  Object other$lossNo = other.getLossNo();
                  if (this$lossNo == null) {
                     if (other$lossNo == null) {
                        break label207;
                     }
                  } else if (this$lossNo.equals(other$lossNo)) {
                     break label207;
                  }

                  return false;
               }

               Object this$lostEndDate = this.getLostEndDate();
               Object other$lostEndDate = other.getLostEndDate();
               if (this$lostEndDate == null) {
                  if (other$lostEndDate != null) {
                     return false;
                  }
               } else if (!this$lostEndDate.equals(other$lostEndDate)) {
                  return false;
               }

               Object this$inStatus = this.getInStatus();
               Object other$inStatus = other.getInStatus();
               if (this$inStatus == null) {
                  if (other$inStatus != null) {
                     return false;
                  }
               } else if (!this$inStatus.equals(other$inStatus)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1200023403In.Body;
      }
      public String toString() {
         return "Core1200023403In.Body(lostNo=" + this.getLostNo() + ", docType=" + this.getDocType() + ", billNo=" + this.getBillNo() + ", billSignDate=" + this.getBillSignDate() + ", billType=" + this.getBillType() + ", signType=" + this.getSignType() + ", billAmt=" + this.getBillAmt() + ", payerBaseAcctNo=" + this.getPayerBaseAcctNo() + ", payerAcctName=" + this.getPayerAcctName() + ", maturityDate=" + this.getMaturityDate() + ", adviceNoteNo=" + this.getAdviceNoteNo() + ", payeeBaseAcctNo=" + this.getPayeeBaseAcctNo() + ", payeeAcctSeqNo=" + this.getPayeeAcctSeqNo() + ", payeeProdType=" + this.getPayeeProdType() + ", payeeAcctCcy=" + this.getPayeeAcctCcy() + ", payeeName=" + this.getPayeeName() + ", applyerAcctName=" + this.getApplyerAcctName() + ", judiciaryOfficerName=" + this.getJudiciaryOfficerName() + ", judiciaryDocumentType=" + this.getJudiciaryDocumentType() + ", judiciaryDocumentType2=" + this.getJudiciaryDocumentType2() + ", judiciaryDocumentId=" + this.getJudiciaryDocumentId() + ", judiciaryDocumentId2=" + this.getJudiciaryDocumentId2() + ", judiciaryOthOfficerName=" + this.getJudiciaryOthOfficerName() + ", judiciaryOthDocumentType=" + this.getJudiciaryOthDocumentType() + ", judiciaryOthDocumentType2=" + this.getJudiciaryOthDocumentType2() + ", judiciaryOthDocumentId=" + this.getJudiciaryOthDocumentId() + ", judiciaryOthDocumentId2=" + this.getJudiciaryOthDocumentId2() + ", lostBeginDate=" + this.getLostBeginDate() + ", lostReason=" + this.getLostReason() + ", operateType=" + this.getOperateType() + ", unlostReason=" + this.getUnlostReason() + ", unlostDate=" + this.getUnlostDate() + ", lossNo=" + this.getLossNo() + ", lostEndDate=" + this.getLostEndDate() + ", inStatus=" + this.getInStatus() + ")";
      }
   }
}
