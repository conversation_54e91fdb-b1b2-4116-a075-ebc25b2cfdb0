package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400100020Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "协议类型",
      notNull = false,
      length = "10",
      remark = "协议类型",
      maxSize = 10
   )
   private String agreementType;
   @V(
      desc = "汇总金额",
      notNull = false,
      length = "17",
      remark = "汇总金额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal totalAmount;
   @V(
      desc = "总可用余额",
      notNull = false,
      length = "17",
      remark = "总可用余额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal totalAvailBal;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100020Out.SignAcctArray> signAcctArray;

   public String getAgreementType() {
      return this.agreementType;
   }

   public BigDecimal getTotalAmount() {
      return this.totalAmount;
   }

   public BigDecimal getTotalAvailBal() {
      return this.totalAvailBal;
   }

   public List<Core1400100020Out.SignAcctArray> getSignAcctArray() {
      return this.signAcctArray;
   }

   public void setAgreementType(String agreementType) {
      this.agreementType = agreementType;
   }

   public void setTotalAmount(BigDecimal totalAmount) {
      this.totalAmount = totalAmount;
   }

   public void setTotalAvailBal(BigDecimal totalAvailBal) {
      this.totalAvailBal = totalAvailBal;
   }

   public void setSignAcctArray(List<Core1400100020Out.SignAcctArray> signAcctArray) {
      this.signAcctArray = signAcctArray;
   }

   public String toString() {
      return "Core1400100020Out(agreementType=" + this.getAgreementType() + ", totalAmount=" + this.getTotalAmount() + ", totalAvailBal=" + this.getTotalAvailBal() + ", signAcctArray=" + this.getSignAcctArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100020Out)) {
         return false;
      } else {
         Core1400100020Out other = (Core1400100020Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            label61: {
               Object this$agreementType = this.getAgreementType();
               Object other$agreementType = other.getAgreementType();
               if (this$agreementType == null) {
                  if (other$agreementType == null) {
                     break label61;
                  }
               } else if (this$agreementType.equals(other$agreementType)) {
                  break label61;
               }

               return false;
            }

            label54: {
               Object this$totalAmount = this.getTotalAmount();
               Object other$totalAmount = other.getTotalAmount();
               if (this$totalAmount == null) {
                  if (other$totalAmount == null) {
                     break label54;
                  }
               } else if (this$totalAmount.equals(other$totalAmount)) {
                  break label54;
               }

               return false;
            }

            Object this$totalAvailBal = this.getTotalAvailBal();
            Object other$totalAvailBal = other.getTotalAvailBal();
            if (this$totalAvailBal == null) {
               if (other$totalAvailBal != null) {
                  return false;
               }
            } else if (!this$totalAvailBal.equals(other$totalAvailBal)) {
               return false;
            }

            Object this$signAcctArray = this.getSignAcctArray();
            Object other$signAcctArray = other.getSignAcctArray();
            if (this$signAcctArray == null) {
               if (other$signAcctArray != null) {
                  return false;
               }
            } else if (!this$signAcctArray.equals(other$signAcctArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100020Out;
   }
   public static class SignAcctArray {
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "账户状态",
         notNull = false,
         length = "1",
         remark = "描述账户生命周期不同阶段的划分",
         maxSize = 1
      )
      private String acctStatus;
      @V(
         desc = "账户名称",
         notNull = false,
         length = "200",
         remark = "账户名称，一般指中文账户名称",
         maxSize = 200
      )
      private String acctName;
      @V(
         desc = "账户开户日期",
         notNull = false,
         remark = "账户开户日期"
      )
      private String acctOpenDate;
      @V(
         desc = "生效日期",
         notNull = false,
         remark = "生效日期"
      )
      private String effectDate;
      @V(
         desc = "销户日期",
         notNull = false,
         remark = "账户销户日期"
      )
      private String acctCloseDate;
      @V(
         desc = "存期期限",
         notNull = false,
         length = "5",
         remark = "期限",
         maxSize = 5
      )
      private String term;
      @V(
         desc = "期限类型",
         notNull = false,
         length = "1",
         remark = "期限类型",
         maxSize = 1
      )
      private String termType;
      @V(
         desc = "账面余额",
         notNull = false,
         length = "17",
         remark = "账面余额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal ledgerBal;
      @V(
         desc = "可用额度",
         notNull = false,
         length = "17",
         remark = "可用额度",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal availableLimit;
      @V(
         desc = "限制金额",
         notNull = false,
         length = "17",
         remark = "限制金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal pledgedAmt;
      @V(
         desc = "执行利率",
         notNull = false,
         length = "15",
         remark = "执行利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal realRate;
      @V(
         desc = "利息金额",
         notNull = false,
         length = "17",
         remark = "利息金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal intAmt;
      @V(
         desc = "到期日期",
         notNull = false,
         remark = "到期日期"
      )
      private String maturityDate;
      @V(
         desc = "利息计算起始日",
         notNull = false,
         remark = "利息计算起始日"
      )
      private String calcBeginDate;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getAcctStatus() {
         return this.acctStatus;
      }

      public String getAcctName() {
         return this.acctName;
      }

      public String getAcctOpenDate() {
         return this.acctOpenDate;
      }

      public String getEffectDate() {
         return this.effectDate;
      }

      public String getAcctCloseDate() {
         return this.acctCloseDate;
      }

      public String getTerm() {
         return this.term;
      }

      public String getTermType() {
         return this.termType;
      }

      public BigDecimal getLedgerBal() {
         return this.ledgerBal;
      }

      public BigDecimal getAvailableLimit() {
         return this.availableLimit;
      }

      public BigDecimal getPledgedAmt() {
         return this.pledgedAmt;
      }

      public BigDecimal getRealRate() {
         return this.realRate;
      }

      public BigDecimal getIntAmt() {
         return this.intAmt;
      }

      public String getMaturityDate() {
         return this.maturityDate;
      }

      public String getCalcBeginDate() {
         return this.calcBeginDate;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setAcctStatus(String acctStatus) {
         this.acctStatus = acctStatus;
      }

      public void setAcctName(String acctName) {
         this.acctName = acctName;
      }

      public void setAcctOpenDate(String acctOpenDate) {
         this.acctOpenDate = acctOpenDate;
      }

      public void setEffectDate(String effectDate) {
         this.effectDate = effectDate;
      }

      public void setAcctCloseDate(String acctCloseDate) {
         this.acctCloseDate = acctCloseDate;
      }

      public void setTerm(String term) {
         this.term = term;
      }

      public void setTermType(String termType) {
         this.termType = termType;
      }

      public void setLedgerBal(BigDecimal ledgerBal) {
         this.ledgerBal = ledgerBal;
      }

      public void setAvailableLimit(BigDecimal availableLimit) {
         this.availableLimit = availableLimit;
      }

      public void setPledgedAmt(BigDecimal pledgedAmt) {
         this.pledgedAmt = pledgedAmt;
      }

      public void setRealRate(BigDecimal realRate) {
         this.realRate = realRate;
      }

      public void setIntAmt(BigDecimal intAmt) {
         this.intAmt = intAmt;
      }

      public void setMaturityDate(String maturityDate) {
         this.maturityDate = maturityDate;
      }

      public void setCalcBeginDate(String calcBeginDate) {
         this.calcBeginDate = calcBeginDate;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100020Out.SignAcctArray)) {
            return false;
         } else {
            Core1400100020Out.SignAcctArray other = (Core1400100020Out.SignAcctArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo != null) {
                     return false;
                  }
               } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo != null) {
                     return false;
                  }
               } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                  return false;
               }

               label206: {
                  Object this$acctCcy = this.getAcctCcy();
                  Object other$acctCcy = other.getAcctCcy();
                  if (this$acctCcy == null) {
                     if (other$acctCcy == null) {
                        break label206;
                     }
                  } else if (this$acctCcy.equals(other$acctCcy)) {
                     break label206;
                  }

                  return false;
               }

               label199: {
                  Object this$acctStatus = this.getAcctStatus();
                  Object other$acctStatus = other.getAcctStatus();
                  if (this$acctStatus == null) {
                     if (other$acctStatus == null) {
                        break label199;
                     }
                  } else if (this$acctStatus.equals(other$acctStatus)) {
                     break label199;
                  }

                  return false;
               }

               Object this$acctName = this.getAcctName();
               Object other$acctName = other.getAcctName();
               if (this$acctName == null) {
                  if (other$acctName != null) {
                     return false;
                  }
               } else if (!this$acctName.equals(other$acctName)) {
                  return false;
               }

               label185: {
                  Object this$acctOpenDate = this.getAcctOpenDate();
                  Object other$acctOpenDate = other.getAcctOpenDate();
                  if (this$acctOpenDate == null) {
                     if (other$acctOpenDate == null) {
                        break label185;
                     }
                  } else if (this$acctOpenDate.equals(other$acctOpenDate)) {
                     break label185;
                  }

                  return false;
               }

               label178: {
                  Object this$effectDate = this.getEffectDate();
                  Object other$effectDate = other.getEffectDate();
                  if (this$effectDate == null) {
                     if (other$effectDate == null) {
                        break label178;
                     }
                  } else if (this$effectDate.equals(other$effectDate)) {
                     break label178;
                  }

                  return false;
               }

               Object this$acctCloseDate = this.getAcctCloseDate();
               Object other$acctCloseDate = other.getAcctCloseDate();
               if (this$acctCloseDate == null) {
                  if (other$acctCloseDate != null) {
                     return false;
                  }
               } else if (!this$acctCloseDate.equals(other$acctCloseDate)) {
                  return false;
               }

               Object this$term = this.getTerm();
               Object other$term = other.getTerm();
               if (this$term == null) {
                  if (other$term != null) {
                     return false;
                  }
               } else if (!this$term.equals(other$term)) {
                  return false;
               }

               label157: {
                  Object this$termType = this.getTermType();
                  Object other$termType = other.getTermType();
                  if (this$termType == null) {
                     if (other$termType == null) {
                        break label157;
                     }
                  } else if (this$termType.equals(other$termType)) {
                     break label157;
                  }

                  return false;
               }

               label150: {
                  Object this$ledgerBal = this.getLedgerBal();
                  Object other$ledgerBal = other.getLedgerBal();
                  if (this$ledgerBal == null) {
                     if (other$ledgerBal == null) {
                        break label150;
                     }
                  } else if (this$ledgerBal.equals(other$ledgerBal)) {
                     break label150;
                  }

                  return false;
               }

               Object this$availableLimit = this.getAvailableLimit();
               Object other$availableLimit = other.getAvailableLimit();
               if (this$availableLimit == null) {
                  if (other$availableLimit != null) {
                     return false;
                  }
               } else if (!this$availableLimit.equals(other$availableLimit)) {
                  return false;
               }

               label136: {
                  Object this$pledgedAmt = this.getPledgedAmt();
                  Object other$pledgedAmt = other.getPledgedAmt();
                  if (this$pledgedAmt == null) {
                     if (other$pledgedAmt == null) {
                        break label136;
                     }
                  } else if (this$pledgedAmt.equals(other$pledgedAmt)) {
                     break label136;
                  }

                  return false;
               }

               Object this$realRate = this.getRealRate();
               Object other$realRate = other.getRealRate();
               if (this$realRate == null) {
                  if (other$realRate != null) {
                     return false;
                  }
               } else if (!this$realRate.equals(other$realRate)) {
                  return false;
               }

               label122: {
                  Object this$intAmt = this.getIntAmt();
                  Object other$intAmt = other.getIntAmt();
                  if (this$intAmt == null) {
                     if (other$intAmt == null) {
                        break label122;
                     }
                  } else if (this$intAmt.equals(other$intAmt)) {
                     break label122;
                  }

                  return false;
               }

               Object this$maturityDate = this.getMaturityDate();
               Object other$maturityDate = other.getMaturityDate();
               if (this$maturityDate == null) {
                  if (other$maturityDate != null) {
                     return false;
                  }
               } else if (!this$maturityDate.equals(other$maturityDate)) {
                  return false;
               }

               Object this$calcBeginDate = this.getCalcBeginDate();
               Object other$calcBeginDate = other.getCalcBeginDate();
               if (this$calcBeginDate == null) {
                  if (other$calcBeginDate != null) {
                     return false;
                  }
               } else if (!this$calcBeginDate.equals(other$calcBeginDate)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100020Out.SignAcctArray;
      }
      public String toString() {
         return "Core1400100020Out.SignAcctArray(baseAcctNo=" + this.getBaseAcctNo() + ", prodType=" + this.getProdType() + ", acctSeqNo=" + this.getAcctSeqNo() + ", acctCcy=" + this.getAcctCcy() + ", acctStatus=" + this.getAcctStatus() + ", acctName=" + this.getAcctName() + ", acctOpenDate=" + this.getAcctOpenDate() + ", effectDate=" + this.getEffectDate() + ", acctCloseDate=" + this.getAcctCloseDate() + ", term=" + this.getTerm() + ", termType=" + this.getTermType() + ", ledgerBal=" + this.getLedgerBal() + ", availableLimit=" + this.getAvailableLimit() + ", pledgedAmt=" + this.getPledgedAmt() + ", realRate=" + this.getRealRate() + ", intAmt=" + this.getIntAmt() + ", maturityDate=" + this.getMaturityDate() + ", calcBeginDate=" + this.getCalcBeginDate() + ")";
      }
   }
}
