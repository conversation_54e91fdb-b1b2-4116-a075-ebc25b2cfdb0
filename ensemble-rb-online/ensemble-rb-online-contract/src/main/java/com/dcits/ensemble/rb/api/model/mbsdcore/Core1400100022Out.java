package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.util.List;

@MessageOut
public class Core1400100022Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100022Out.MsgArray> msgArray;

   public List<Core1400100022Out.MsgArray> getMsgArray() {
      return this.msgArray;
   }

   public void setMsgArray(List<Core1400100022Out.MsgArray> msgArray) {
      this.msgArray = msgArray;
   }

   public String toString() {
      return "Core1400100022Out(msgArray=" + this.getMsgArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100022Out)) {
         return false;
      } else {
         Core1400100022Out other = (Core1400100022Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$msgArray = this.getMsgArray();
            Object other$msgArray = other.getMsgArray();
            if (this$msgArray == null) {
               if (other$msgArray != null) {
                  return false;
               }
            } else if (!this$msgArray.equals(other$msgArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100022Out;
   }
   public static class MsgArray {
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "设置类型",
         notNull = false,
         length = "2",
         remark = "设置类型",
         maxSize = 2
      )
      private String msgSetType;
      @V(
         desc = "开关状态",
         notNull = false,
         length = "1",
         remark = "开关状态",
         maxSize = 1
      )
      private String tbSwitchState;

      public String getClientNo() {
         return this.clientNo;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getMsgSetType() {
         return this.msgSetType;
      }

      public String getTbSwitchState() {
         return this.tbSwitchState;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setMsgSetType(String msgSetType) {
         this.msgSetType = msgSetType;
      }

      public void setTbSwitchState(String tbSwitchState) {
         this.tbSwitchState = tbSwitchState;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100022Out.MsgArray)) {
            return false;
         } else {
            Core1400100022Out.MsgArray other = (Core1400100022Out.MsgArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label95: {
                  Object this$clientNo = this.getClientNo();
                  Object other$clientNo = other.getClientNo();
                  if (this$clientNo == null) {
                     if (other$clientNo == null) {
                        break label95;
                     }
                  } else if (this$clientNo.equals(other$clientNo)) {
                     break label95;
                  }

                  return false;
               }

               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo != null) {
                     return false;
                  }
               } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               label74: {
                  Object this$acctCcy = this.getAcctCcy();
                  Object other$acctCcy = other.getAcctCcy();
                  if (this$acctCcy == null) {
                     if (other$acctCcy == null) {
                        break label74;
                     }
                  } else if (this$acctCcy.equals(other$acctCcy)) {
                     break label74;
                  }

                  return false;
               }

               label67: {
                  Object this$acctSeqNo = this.getAcctSeqNo();
                  Object other$acctSeqNo = other.getAcctSeqNo();
                  if (this$acctSeqNo == null) {
                     if (other$acctSeqNo == null) {
                        break label67;
                     }
                  } else if (this$acctSeqNo.equals(other$acctSeqNo)) {
                     break label67;
                  }

                  return false;
               }

               Object this$msgSetType = this.getMsgSetType();
               Object other$msgSetType = other.getMsgSetType();
               if (this$msgSetType == null) {
                  if (other$msgSetType != null) {
                     return false;
                  }
               } else if (!this$msgSetType.equals(other$msgSetType)) {
                  return false;
               }

               Object this$tbSwitchState = this.getTbSwitchState();
               Object other$tbSwitchState = other.getTbSwitchState();
               if (this$tbSwitchState == null) {
                  if (other$tbSwitchState != null) {
                     return false;
                  }
               } else if (!this$tbSwitchState.equals(other$tbSwitchState)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100022Out.MsgArray;
      }
      public String toString() {
         return "Core1400100022Out.MsgArray(clientNo=" + this.getClientNo() + ", baseAcctNo=" + this.getBaseAcctNo() + ", prodType=" + this.getProdType() + ", acctCcy=" + this.getAcctCcy() + ", acctSeqNo=" + this.getAcctSeqNo() + ", msgSetType=" + this.getMsgSetType() + ", tbSwitchState=" + this.getTbSwitchState() + ")";
      }
   }
}
