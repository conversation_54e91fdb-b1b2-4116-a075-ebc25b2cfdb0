package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000029In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000029Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000029 {
   String URL = "/rb/inq/all/agreement";


   @ApiDesc("通过账/卡号查询该账/卡号的签约理财信息。由于目前仅存抵贷使用，不需关注具体理财信息，所以只返回是否存在有效状态的签约。未来若有需求可扩充接口添加理财信息数组。20230516 新增此接口。")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0029"
   )
   @BusinessCategory("1400-查询")
   @FunctionCategory("RB05-协议管理")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core14000029Out runService(Core14000029In var1);
}
