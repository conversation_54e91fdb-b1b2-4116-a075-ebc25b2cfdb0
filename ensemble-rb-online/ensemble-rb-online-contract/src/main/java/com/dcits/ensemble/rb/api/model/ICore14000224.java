package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000224In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000224Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000224 {
   String URL = "/rb/inq/dc/acct/redeem";


   @ApiRemark("赎回信息查询RB_DC_TOHONOR_REC_INFO")
   @ApiDesc("用于大额存单赎回账户信息查询。")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0224"
   )
   @BusinessCategory("1400-查询")
   @FunctionCategory("RB10-大额存单")
   @ConsumeSys("EOS/TLE/PR")
   @ApiUseStatus("PRODUCT-产品")
   Core14000224Out runService(Core14000224In var1);
}
