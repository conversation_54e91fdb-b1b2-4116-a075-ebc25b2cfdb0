package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;

@MessageIn
public class Core1400056111In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400056111In.Body body;

   public Core1400056111In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400056111In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400056111In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400056111In)) {
         return false;
      } else {
         Core1400056111In other = (Core1400056111In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400056111In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "卡号",
         notNull = true,
         length = "50",
         remark = "卡号",
         maxSize = 50
      )
      private String cardNo;
      @V(
         desc = "卡状态",
         notNull = false,
         length = "1",
         in = "A,B,C,D,E,F,H,I",
         inDesc = "A-申请,B-待发,C-活动,D-注销,E-CVN锁定,F-单日密码锁定,H-密码错误次数累计达到上限锁定,I-密码失效",
         remark = "卡状态",
         maxSize = 1
      )
      private String cardStatus;
      @V(
         desc = "签发日期",
         notNull = false,
         remark = "签发日期"
      )
      private String issDate;
      @V(
         desc = "单日累计小额免密限额",
         notNull = false,
         length = "17",
         remark = "单日累计小额免密限额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal totalDayAmt;

      public String getCardNo() {
         return this.cardNo;
      }

      public String getCardStatus() {
         return this.cardStatus;
      }

      public String getIssDate() {
         return this.issDate;
      }

      public BigDecimal getTotalDayAmt() {
         return this.totalDayAmt;
      }

      public void setCardNo(String cardNo) {
         this.cardNo = cardNo;
      }

      public void setCardStatus(String cardStatus) {
         this.cardStatus = cardStatus;
      }

      public void setIssDate(String issDate) {
         this.issDate = issDate;
      }

      public void setTotalDayAmt(BigDecimal totalDayAmt) {
         this.totalDayAmt = totalDayAmt;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400056111In.Body)) {
            return false;
         } else {
            Core1400056111In.Body other = (Core1400056111In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label59: {
                  Object this$cardNo = this.getCardNo();
                  Object other$cardNo = other.getCardNo();
                  if (this$cardNo == null) {
                     if (other$cardNo == null) {
                        break label59;
                     }
                  } else if (this$cardNo.equals(other$cardNo)) {
                     break label59;
                  }

                  return false;
               }

               Object this$cardStatus = this.getCardStatus();
               Object other$cardStatus = other.getCardStatus();
               if (this$cardStatus == null) {
                  if (other$cardStatus != null) {
                     return false;
                  }
               } else if (!this$cardStatus.equals(other$cardStatus)) {
                  return false;
               }

               Object this$issDate = this.getIssDate();
               Object other$issDate = other.getIssDate();
               if (this$issDate == null) {
                  if (other$issDate != null) {
                     return false;
                  }
               } else if (!this$issDate.equals(other$issDate)) {
                  return false;
               }

               Object this$totalDayAmt = this.getTotalDayAmt();
               Object other$totalDayAmt = other.getTotalDayAmt();
               if (this$totalDayAmt == null) {
                  if (other$totalDayAmt != null) {
                     return false;
                  }
               } else if (!this$totalDayAmt.equals(other$totalDayAmt)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400056111In.Body;
      }
      public String toString() {
         return "Core1400056111In.Body(cardNo=" + this.getCardNo() + ", cardStatus=" + this.getCardStatus() + ", issDate=" + this.getIssDate() + ", totalDayAmt=" + this.getTotalDayAmt() + ")";
      }
   }
}
