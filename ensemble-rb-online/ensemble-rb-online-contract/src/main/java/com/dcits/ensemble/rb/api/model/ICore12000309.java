package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000309In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000309Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12000309 {
   String URL = "/rb/nfin/cheque/defense";


   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0309"
   )
   @ConsumeSys("TLE")
   Core12000309Out runService(Core12000309In var1);
}
