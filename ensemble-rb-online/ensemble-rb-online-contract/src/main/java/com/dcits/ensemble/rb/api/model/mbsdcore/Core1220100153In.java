package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1220100153In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1220100153In.Body body;

   public Core1220100153In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1220100153In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1220100153In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1220100153In)) {
         return false;
      } else {
         Core1220100153In other = (Core1220100153In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1220100153In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "审批单号",
         notNull = true,
         length = "50",
         remark = "审批单号",
         maxSize = 50
      )
      private String approvalNo;
      @V(
         desc = "文件路径",
         notNull = false,
         length = "200",
         remark = "文件路径",
         maxSize = 200
      )
      private String filePath;
      @V(
         desc = "销户重开标志",
         notNull = true,
         length = "1",
         remark = "销户重开标志",
         restraint = "1-销户 2-销户重开",
         maxSize = 1
      )
      private String closeOpenFlag;

      public String getApprovalNo() {
         return this.approvalNo;
      }

      public String getFilePath() {
         return this.filePath;
      }

      public String getCloseOpenFlag() {
         return this.closeOpenFlag;
      }

      public void setApprovalNo(String approvalNo) {
         this.approvalNo = approvalNo;
      }

      public void setFilePath(String filePath) {
         this.filePath = filePath;
      }

      public void setCloseOpenFlag(String closeOpenFlag) {
         this.closeOpenFlag = closeOpenFlag;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1220100153In.Body)) {
            return false;
         } else {
            Core1220100153In.Body other = (Core1220100153In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label47: {
                  Object this$approvalNo = this.getApprovalNo();
                  Object other$approvalNo = other.getApprovalNo();
                  if (this$approvalNo == null) {
                     if (other$approvalNo == null) {
                        break label47;
                     }
                  } else if (this$approvalNo.equals(other$approvalNo)) {
                     break label47;
                  }

                  return false;
               }

               Object this$filePath = this.getFilePath();
               Object other$filePath = other.getFilePath();
               if (this$filePath == null) {
                  if (other$filePath != null) {
                     return false;
                  }
               } else if (!this$filePath.equals(other$filePath)) {
                  return false;
               }

               Object this$closeOpenFlag = this.getCloseOpenFlag();
               Object other$closeOpenFlag = other.getCloseOpenFlag();
               if (this$closeOpenFlag == null) {
                  if (other$closeOpenFlag != null) {
                     return false;
                  }
               } else if (!this$closeOpenFlag.equals(other$closeOpenFlag)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1220100153In.Body;
      }
      public String toString() {
         return "Core1220100153In.Body(approvalNo=" + this.getApprovalNo() + ", filePath=" + this.getFilePath() + ", closeOpenFlag=" + this.getCloseOpenFlag() + ")";
      }
   }
}
