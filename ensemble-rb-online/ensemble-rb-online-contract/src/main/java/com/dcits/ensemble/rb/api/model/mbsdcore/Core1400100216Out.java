package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400100216Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "产品类型",
      notNull = false,
      length = "20",
      remark = "产品类型",
      maxSize = 20
   )
   private String prodType;
   @V(
      desc = "发行年度",
      notNull = false,
      length = "5",
      remark = "发行年度",
      maxSize = 5
   )
   private String issueYear;
   @V(
      desc = "币种",
      notNull = false,
      length = "3",
      remark = "币种",
      maxSize = 3
   )
   private String ccy;
   @V(
      desc = "限额总金额",
      notNull = false,
      length = "17",
      remark = "限额总金额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal limitTotalAmt;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100216Out.DevLimitArray> devLimitArray;

   public String getProdType() {
      return this.prodType;
   }

   public String getIssueYear() {
      return this.issueYear;
   }

   public String getCcy() {
      return this.ccy;
   }

   public BigDecimal getLimitTotalAmt() {
      return this.limitTotalAmt;
   }

   public List<Core1400100216Out.DevLimitArray> getDevLimitArray() {
      return this.devLimitArray;
   }

   public void setProdType(String prodType) {
      this.prodType = prodType;
   }

   public void setIssueYear(String issueYear) {
      this.issueYear = issueYear;
   }

   public void setCcy(String ccy) {
      this.ccy = ccy;
   }

   public void setLimitTotalAmt(BigDecimal limitTotalAmt) {
      this.limitTotalAmt = limitTotalAmt;
   }

   public void setDevLimitArray(List<Core1400100216Out.DevLimitArray> devLimitArray) {
      this.devLimitArray = devLimitArray;
   }

   public String toString() {
      return "Core1400100216Out(prodType=" + this.getProdType() + ", issueYear=" + this.getIssueYear() + ", ccy=" + this.getCcy() + ", limitTotalAmt=" + this.getLimitTotalAmt() + ", devLimitArray=" + this.getDevLimitArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100216Out)) {
         return false;
      } else {
         Core1400100216Out other = (Core1400100216Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            label73: {
               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType == null) {
                     break label73;
                  }
               } else if (this$prodType.equals(other$prodType)) {
                  break label73;
               }

               return false;
            }

            Object this$issueYear = this.getIssueYear();
            Object other$issueYear = other.getIssueYear();
            if (this$issueYear == null) {
               if (other$issueYear != null) {
                  return false;
               }
            } else if (!this$issueYear.equals(other$issueYear)) {
               return false;
            }

            label59: {
               Object this$ccy = this.getCcy();
               Object other$ccy = other.getCcy();
               if (this$ccy == null) {
                  if (other$ccy == null) {
                     break label59;
                  }
               } else if (this$ccy.equals(other$ccy)) {
                  break label59;
               }

               return false;
            }

            Object this$limitTotalAmt = this.getLimitTotalAmt();
            Object other$limitTotalAmt = other.getLimitTotalAmt();
            if (this$limitTotalAmt == null) {
               if (other$limitTotalAmt != null) {
                  return false;
               }
            } else if (!this$limitTotalAmt.equals(other$limitTotalAmt)) {
               return false;
            }

            Object this$devLimitArray = this.getDevLimitArray();
            Object other$devLimitArray = other.getDevLimitArray();
            if (this$devLimitArray == null) {
               if (other$devLimitArray != null) {
                  return false;
               }
            } else if (!this$devLimitArray.equals(other$devLimitArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100216Out;
   }
   public static class DevLimitArray {
      @V(
         desc = "本机构发行额度",
         notNull = false,
         length = "17",
         remark = "本机构发行额度",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal branchTotalLimit;
      @V(
         desc = "本机构可用额度额度",
         notNull = false,
         length = "17",
         remark = "本机构可用额度额度",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal branchLeaveLimit;
      @V(
         desc = "所属机构号",
         notNull = false,
         length = "50",
         remark = "机构代码",
         maxSize = 50
      )
      private String branch;
      @V(
         desc = "已分配额度",
         notNull = false,
         length = "17",
         remark = "已经分配的下放额度，由年度分配、或分配给机构、或分配给渠道，可以由下游合法使用的额度",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal distributeLimit;
      @V(
         desc = "产品余额",
         notNull = false,
         length = "17",
         remark = "产品余额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal prodBalance;
      @V(
         desc = "OA申请额度",
         notNull = false,
         length = "17",
         remark = "OA申请额度",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal oaAmt;
      @V(
         desc = "汇总金额（发行中产品额度）",
         notNull = false,
         length = "17",
         remark = "汇总金额（发行中产品额度）",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal mainBalance;

      public BigDecimal getBranchTotalLimit() {
         return this.branchTotalLimit;
      }

      public BigDecimal getBranchLeaveLimit() {
         return this.branchLeaveLimit;
      }

      public String getBranch() {
         return this.branch;
      }

      public BigDecimal getDistributeLimit() {
         return this.distributeLimit;
      }

      public BigDecimal getProdBalance() {
         return this.prodBalance;
      }

      public BigDecimal getOaAmt() {
         return this.oaAmt;
      }

      public BigDecimal getMainBalance() {
         return this.mainBalance;
      }

      public void setBranchTotalLimit(BigDecimal branchTotalLimit) {
         this.branchTotalLimit = branchTotalLimit;
      }

      public void setBranchLeaveLimit(BigDecimal branchLeaveLimit) {
         this.branchLeaveLimit = branchLeaveLimit;
      }

      public void setBranch(String branch) {
         this.branch = branch;
      }

      public void setDistributeLimit(BigDecimal distributeLimit) {
         this.distributeLimit = distributeLimit;
      }

      public void setProdBalance(BigDecimal prodBalance) {
         this.prodBalance = prodBalance;
      }

      public void setOaAmt(BigDecimal oaAmt) {
         this.oaAmt = oaAmt;
      }

      public void setMainBalance(BigDecimal mainBalance) {
         this.mainBalance = mainBalance;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100216Out.DevLimitArray)) {
            return false;
         } else {
            Core1400100216Out.DevLimitArray other = (Core1400100216Out.DevLimitArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label95: {
                  Object this$branchTotalLimit = this.getBranchTotalLimit();
                  Object other$branchTotalLimit = other.getBranchTotalLimit();
                  if (this$branchTotalLimit == null) {
                     if (other$branchTotalLimit == null) {
                        break label95;
                     }
                  } else if (this$branchTotalLimit.equals(other$branchTotalLimit)) {
                     break label95;
                  }

                  return false;
               }

               Object this$branchLeaveLimit = this.getBranchLeaveLimit();
               Object other$branchLeaveLimit = other.getBranchLeaveLimit();
               if (this$branchLeaveLimit == null) {
                  if (other$branchLeaveLimit != null) {
                     return false;
                  }
               } else if (!this$branchLeaveLimit.equals(other$branchLeaveLimit)) {
                  return false;
               }

               Object this$branch = this.getBranch();
               Object other$branch = other.getBranch();
               if (this$branch == null) {
                  if (other$branch != null) {
                     return false;
                  }
               } else if (!this$branch.equals(other$branch)) {
                  return false;
               }

               label74: {
                  Object this$distributeLimit = this.getDistributeLimit();
                  Object other$distributeLimit = other.getDistributeLimit();
                  if (this$distributeLimit == null) {
                     if (other$distributeLimit == null) {
                        break label74;
                     }
                  } else if (this$distributeLimit.equals(other$distributeLimit)) {
                     break label74;
                  }

                  return false;
               }

               label67: {
                  Object this$prodBalance = this.getProdBalance();
                  Object other$prodBalance = other.getProdBalance();
                  if (this$prodBalance == null) {
                     if (other$prodBalance == null) {
                        break label67;
                     }
                  } else if (this$prodBalance.equals(other$prodBalance)) {
                     break label67;
                  }

                  return false;
               }

               Object this$oaAmt = this.getOaAmt();
               Object other$oaAmt = other.getOaAmt();
               if (this$oaAmt == null) {
                  if (other$oaAmt != null) {
                     return false;
                  }
               } else if (!this$oaAmt.equals(other$oaAmt)) {
                  return false;
               }

               Object this$mainBalance = this.getMainBalance();
               Object other$mainBalance = other.getMainBalance();
               if (this$mainBalance == null) {
                  if (other$mainBalance != null) {
                     return false;
                  }
               } else if (!this$mainBalance.equals(other$mainBalance)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100216Out.DevLimitArray;
      }
      public String toString() {
         return "Core1400100216Out.DevLimitArray(branchTotalLimit=" + this.getBranchTotalLimit() + ", branchLeaveLimit=" + this.getBranchLeaveLimit() + ", branch=" + this.getBranch() + ", distributeLimit=" + this.getDistributeLimit() + ", prodBalance=" + this.getProdBalance() + ", oaAmt=" + this.getOaAmt() + ", mainBalance=" + this.getMainBalance() + ")";
      }
   }
}
