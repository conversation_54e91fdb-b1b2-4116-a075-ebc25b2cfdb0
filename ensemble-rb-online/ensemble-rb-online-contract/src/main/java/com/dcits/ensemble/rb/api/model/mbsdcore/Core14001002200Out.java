package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;

@MessageOut
public class Core14001002200Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "发行年度",
      notNull = false,
      length = "5",
      remark = "发行年度",
      maxSize = 5
   )
   private String issueYear;
   @V(
      desc = "币种",
      notNull = false,
      length = "3",
      remark = "币种",
      maxSize = 3
   )
   private String ccy;
   @V(
      desc = "备案额度",
      notNull = false,
      length = "17",
      remark = "调整后备案额度",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal recordLimit;
   @V(
      desc = "剩余额度",
      notNull = false,
      length = "17",
      remark = "剩余额度",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal leaveLimit;
   @V(
      desc = "调整额度",
      notNull = false,
      length = "17",
      remark = "调整额度",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal adjustLimit;
   @V(
      desc = "单位额度",
      notNull = false,
      length = "17",
      remark = "单位额度",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal coprLimit;
   @V(
      desc = "零售额度",
      notNull = false,
      length = "17",
      remark = "零售额度",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal personalLimit;
   @V(
      desc = "单位剩余额度",
      notNull = false,
      length = "17",
      remark = "单位剩余额度",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal coprLeaveLimit;
   @V(
      desc = "个人剩余额度",
      notNull = false,
      length = "17",
      remark = "个人剩余额度",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal personalLeaveLimit;
   @V(
      desc = "机构额度",
      notNull = false,
      length = "17",
      remark = "机构额度",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal institutionLimit;
   @V(
      desc = "机构剩余额度",
      notNull = false,
      length = "17",
      remark = "机构剩余额度",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal institutionLeaveLimit;

   public String getIssueYear() {
      return this.issueYear;
   }

   public String getCcy() {
      return this.ccy;
   }

   public BigDecimal getRecordLimit() {
      return this.recordLimit;
   }

   public BigDecimal getLeaveLimit() {
      return this.leaveLimit;
   }

   public BigDecimal getAdjustLimit() {
      return this.adjustLimit;
   }

   public BigDecimal getCoprLimit() {
      return this.coprLimit;
   }

   public BigDecimal getPersonalLimit() {
      return this.personalLimit;
   }

   public BigDecimal getCoprLeaveLimit() {
      return this.coprLeaveLimit;
   }

   public BigDecimal getPersonalLeaveLimit() {
      return this.personalLeaveLimit;
   }

   public BigDecimal getInstitutionLimit() {
      return this.institutionLimit;
   }

   public BigDecimal getInstitutionLeaveLimit() {
      return this.institutionLeaveLimit;
   }

   public void setIssueYear(String issueYear) {
      this.issueYear = issueYear;
   }

   public void setCcy(String ccy) {
      this.ccy = ccy;
   }

   public void setRecordLimit(BigDecimal recordLimit) {
      this.recordLimit = recordLimit;
   }

   public void setLeaveLimit(BigDecimal leaveLimit) {
      this.leaveLimit = leaveLimit;
   }

   public void setAdjustLimit(BigDecimal adjustLimit) {
      this.adjustLimit = adjustLimit;
   }

   public void setCoprLimit(BigDecimal coprLimit) {
      this.coprLimit = coprLimit;
   }

   public void setPersonalLimit(BigDecimal personalLimit) {
      this.personalLimit = personalLimit;
   }

   public void setCoprLeaveLimit(BigDecimal coprLeaveLimit) {
      this.coprLeaveLimit = coprLeaveLimit;
   }

   public void setPersonalLeaveLimit(BigDecimal personalLeaveLimit) {
      this.personalLeaveLimit = personalLeaveLimit;
   }

   public void setInstitutionLimit(BigDecimal institutionLimit) {
      this.institutionLimit = institutionLimit;
   }

   public void setInstitutionLeaveLimit(BigDecimal institutionLeaveLimit) {
      this.institutionLeaveLimit = institutionLeaveLimit;
   }

   public String toString() {
      return "Core14001002200Out(issueYear=" + this.getIssueYear() + ", ccy=" + this.getCcy() + ", recordLimit=" + this.getRecordLimit() + ", leaveLimit=" + this.getLeaveLimit() + ", adjustLimit=" + this.getAdjustLimit() + ", coprLimit=" + this.getCoprLimit() + ", personalLimit=" + this.getPersonalLimit() + ", coprLeaveLimit=" + this.getCoprLeaveLimit() + ", personalLeaveLimit=" + this.getPersonalLeaveLimit() + ", institutionLimit=" + this.getInstitutionLimit() + ", institutionLeaveLimit=" + this.getInstitutionLeaveLimit() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core14001002200Out)) {
         return false;
      } else {
         Core14001002200Out other = (Core14001002200Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            label145: {
               Object this$issueYear = this.getIssueYear();
               Object other$issueYear = other.getIssueYear();
               if (this$issueYear == null) {
                  if (other$issueYear == null) {
                     break label145;
                  }
               } else if (this$issueYear.equals(other$issueYear)) {
                  break label145;
               }

               return false;
            }

            Object this$ccy = this.getCcy();
            Object other$ccy = other.getCcy();
            if (this$ccy == null) {
               if (other$ccy != null) {
                  return false;
               }
            } else if (!this$ccy.equals(other$ccy)) {
               return false;
            }

            Object this$recordLimit = this.getRecordLimit();
            Object other$recordLimit = other.getRecordLimit();
            if (this$recordLimit == null) {
               if (other$recordLimit != null) {
                  return false;
               }
            } else if (!this$recordLimit.equals(other$recordLimit)) {
               return false;
            }

            label124: {
               Object this$leaveLimit = this.getLeaveLimit();
               Object other$leaveLimit = other.getLeaveLimit();
               if (this$leaveLimit == null) {
                  if (other$leaveLimit == null) {
                     break label124;
                  }
               } else if (this$leaveLimit.equals(other$leaveLimit)) {
                  break label124;
               }

               return false;
            }

            Object this$adjustLimit = this.getAdjustLimit();
            Object other$adjustLimit = other.getAdjustLimit();
            if (this$adjustLimit == null) {
               if (other$adjustLimit != null) {
                  return false;
               }
            } else if (!this$adjustLimit.equals(other$adjustLimit)) {
               return false;
            }

            Object this$coprLimit = this.getCoprLimit();
            Object other$coprLimit = other.getCoprLimit();
            if (this$coprLimit == null) {
               if (other$coprLimit != null) {
                  return false;
               }
            } else if (!this$coprLimit.equals(other$coprLimit)) {
               return false;
            }

            label103: {
               Object this$personalLimit = this.getPersonalLimit();
               Object other$personalLimit = other.getPersonalLimit();
               if (this$personalLimit == null) {
                  if (other$personalLimit == null) {
                     break label103;
                  }
               } else if (this$personalLimit.equals(other$personalLimit)) {
                  break label103;
               }

               return false;
            }

            Object this$coprLeaveLimit = this.getCoprLeaveLimit();
            Object other$coprLeaveLimit = other.getCoprLeaveLimit();
            if (this$coprLeaveLimit == null) {
               if (other$coprLeaveLimit != null) {
                  return false;
               }
            } else if (!this$coprLeaveLimit.equals(other$coprLeaveLimit)) {
               return false;
            }

            label89: {
               Object this$personalLeaveLimit = this.getPersonalLeaveLimit();
               Object other$personalLeaveLimit = other.getPersonalLeaveLimit();
               if (this$personalLeaveLimit == null) {
                  if (other$personalLeaveLimit == null) {
                     break label89;
                  }
               } else if (this$personalLeaveLimit.equals(other$personalLeaveLimit)) {
                  break label89;
               }

               return false;
            }

            Object this$institutionLimit = this.getInstitutionLimit();
            Object other$institutionLimit = other.getInstitutionLimit();
            if (this$institutionLimit == null) {
               if (other$institutionLimit != null) {
                  return false;
               }
            } else if (!this$institutionLimit.equals(other$institutionLimit)) {
               return false;
            }

            Object this$institutionLeaveLimit = this.getInstitutionLeaveLimit();
            Object other$institutionLeaveLimit = other.getInstitutionLeaveLimit();
            if (this$institutionLeaveLimit == null) {
               if (other$institutionLeaveLimit == null) {
                  return true;
               }
            } else if (this$institutionLeaveLimit.equals(other$institutionLeaveLimit)) {
               return true;
            }

            return false;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core14001002200Out;
   }
}
