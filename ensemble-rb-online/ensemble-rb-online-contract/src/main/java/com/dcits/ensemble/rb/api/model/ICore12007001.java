package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12007001In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12007001Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12007001 {
   String URL = "/rb/nfin/list/maint";


   @ApiRemark("行内名单维护")
   @ApiDesc("新增或删除行内黑白名单信息")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "7001"
   )
   @BusinessCategory("1200-非金融")
   @FunctionCategory("RB01-公共服务")
   Core12007001Out runService(Core12007001In var1);
}
