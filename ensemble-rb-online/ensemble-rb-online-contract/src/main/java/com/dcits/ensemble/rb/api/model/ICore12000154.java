package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000154In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000154Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12000154 {
   String URL = "/rb/nfin/card/lucky/receive";


   @ApiRemark("吉祥卡签收")
   @ApiDesc("该交易用于在总行没有库管的情况下，想实现对吉祥卡号的管控，则使用该交易进行吉祥卡的签收，该过程不涉及库存")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0154"
   )
   @BusinessCategory("1200-非金融")
   @FunctionCategory("RB20-借记卡")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core12000154Out runService(Core12000154In var1);
}
