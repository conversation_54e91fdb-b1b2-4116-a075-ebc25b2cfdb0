package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400049077In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400049077In.Body body;

   public Core1400049077In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400049077In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400049077In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400049077In)) {
         return false;
      } else {
         Core1400049077In other = (Core1400049077In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400049077In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "证件类型",
         notNull = false,
         length = "3",
         remark = "证件类型",
         maxSize = 3
      )
      private String documentType;
      @V(
         desc = "证件号码",
         notNull = false,
         length = "50",
         remark = "证件号码",
         maxSize = 50
      )
      private String documentId;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "发证国家",
         notNull = false,
         length = "3",
         remark = "发证国家",
         maxSize = 3
      )
      private String issCountry;
      @V(
         desc = "协议编号",
         notNull = false,
         length = "50",
         remark = "协议编号",
         maxSize = 50
      )
      private String agreementId;

      public String getDocumentType() {
         return this.documentType;
      }

      public String getDocumentId() {
         return this.documentId;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public String getIssCountry() {
         return this.issCountry;
      }

      public String getAgreementId() {
         return this.agreementId;
      }

      public void setDocumentType(String documentType) {
         this.documentType = documentType;
      }

      public void setDocumentId(String documentId) {
         this.documentId = documentId;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setIssCountry(String issCountry) {
         this.issCountry = issCountry;
      }

      public void setAgreementId(String agreementId) {
         this.agreementId = agreementId;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400049077In.Body)) {
            return false;
         } else {
            Core1400049077In.Body other = (Core1400049077In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label71: {
                  Object this$documentType = this.getDocumentType();
                  Object other$documentType = other.getDocumentType();
                  if (this$documentType == null) {
                     if (other$documentType == null) {
                        break label71;
                     }
                  } else if (this$documentType.equals(other$documentType)) {
                     break label71;
                  }

                  return false;
               }

               Object this$documentId = this.getDocumentId();
               Object other$documentId = other.getDocumentId();
               if (this$documentId == null) {
                  if (other$documentId != null) {
                     return false;
                  }
               } else if (!this$documentId.equals(other$documentId)) {
                  return false;
               }

               label57: {
                  Object this$clientNo = this.getClientNo();
                  Object other$clientNo = other.getClientNo();
                  if (this$clientNo == null) {
                     if (other$clientNo == null) {
                        break label57;
                     }
                  } else if (this$clientNo.equals(other$clientNo)) {
                     break label57;
                  }

                  return false;
               }

               Object this$issCountry = this.getIssCountry();
               Object other$issCountry = other.getIssCountry();
               if (this$issCountry == null) {
                  if (other$issCountry != null) {
                     return false;
                  }
               } else if (!this$issCountry.equals(other$issCountry)) {
                  return false;
               }

               Object this$agreementId = this.getAgreementId();
               Object other$agreementId = other.getAgreementId();
               if (this$agreementId == null) {
                  if (other$agreementId == null) {
                     return true;
                  }
               } else if (this$agreementId.equals(other$agreementId)) {
                  return true;
               }

               return false;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400049077In.Body;
      }
      public String toString() {
         return "Core1400049077In.Body(documentType=" + this.getDocumentType() + ", documentId=" + this.getDocumentId() + ", clientNo=" + this.getClientNo() + ", issCountry=" + this.getIssCountry() + ", agreementId=" + this.getAgreementId() + ")";
      }
   }
}
