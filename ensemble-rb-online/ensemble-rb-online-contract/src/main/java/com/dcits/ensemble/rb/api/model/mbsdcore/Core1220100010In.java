package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;

@MessageIn
public class Core1220100010In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1220100010In.Body body;

   public Core1220100010In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1220100010In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1220100010In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1220100010In)) {
         return false;
      } else {
         Core1220100010In other = (Core1220100010In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1220100010In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "账号/卡号",
         notNull = true,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "真实交易对手账号",
         notNull = false,
         length = "50",
         remark = "真实交易对手账号",
         maxSize = 50
      )
      private String othRealBaseAcctNo;
      @V(
         desc = "真实交易对手名称",
         notNull = false,
         length = "200",
         remark = "真实交易对手名称",
         maxSize = 200
      )
      private String othRealTranName;
      @V(
         desc = "对方账号/卡号",
         notNull = false,
         length = "50",
         remark = "对方账号/卡号",
         maxSize = 50
      )
      private String othBaseAcctNo;
      @V(
         desc = "交易发生额上限",
         notNull = false,
         length = "17",
         remark = "交易发生额上限",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal tranHighestAmt;
      @V(
         desc = "对方账户名称",
         notNull = false,
         length = "200",
         remark = "对方账户名称",
         maxSize = 200
      )
      private String othAcctName;
      @V(
         desc = "交易发生额下限",
         notNull = false,
         length = "17",
         remark = "交易发生额下限",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal lowestTranAmt;
      @V(
         desc = "开始日期",
         notNull = false,
         remark = "开始日期"
      )
      private String startDate;
      @V(
         desc = "结束日期",
         notNull = false,
         remark = "结束日期"
      )
      private String endDate;
      @V(
         desc = "是否已打印凭证",
         notNull = false,
         length = "1",
         inDesc = "0-柜面签约已打凭证 ,1-柜面解约已打凭证,2-网银签约未打凭证,3-网银签约已打凭证 ,4-网银解约未打凭证,5-网银签约已打凭证,7-其他",
         remark = "协定宝签约解约后是否已打印凭证",
         maxSize = 1
      )
      private String printStatus;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getCcy() {
         return this.ccy;
      }

      public String getOthRealBaseAcctNo() {
         return this.othRealBaseAcctNo;
      }

      public String getOthRealTranName() {
         return this.othRealTranName;
      }

      public String getOthBaseAcctNo() {
         return this.othBaseAcctNo;
      }

      public BigDecimal getTranHighestAmt() {
         return this.tranHighestAmt;
      }

      public String getOthAcctName() {
         return this.othAcctName;
      }

      public BigDecimal getLowestTranAmt() {
         return this.lowestTranAmt;
      }

      public String getStartDate() {
         return this.startDate;
      }

      public String getEndDate() {
         return this.endDate;
      }

      public String getPrintStatus() {
         return this.printStatus;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setOthRealBaseAcctNo(String othRealBaseAcctNo) {
         this.othRealBaseAcctNo = othRealBaseAcctNo;
      }

      public void setOthRealTranName(String othRealTranName) {
         this.othRealTranName = othRealTranName;
      }

      public void setOthBaseAcctNo(String othBaseAcctNo) {
         this.othBaseAcctNo = othBaseAcctNo;
      }

      public void setTranHighestAmt(BigDecimal tranHighestAmt) {
         this.tranHighestAmt = tranHighestAmt;
      }

      public void setOthAcctName(String othAcctName) {
         this.othAcctName = othAcctName;
      }

      public void setLowestTranAmt(BigDecimal lowestTranAmt) {
         this.lowestTranAmt = lowestTranAmt;
      }

      public void setStartDate(String startDate) {
         this.startDate = startDate;
      }

      public void setEndDate(String endDate) {
         this.endDate = endDate;
      }

      public void setPrintStatus(String printStatus) {
         this.printStatus = printStatus;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1220100010In.Body)) {
            return false;
         } else {
            Core1220100010In.Body other = (Core1220100010In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label167: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label167;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label167;
                  }

                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               label153: {
                  Object this$acctSeqNo = this.getAcctSeqNo();
                  Object other$acctSeqNo = other.getAcctSeqNo();
                  if (this$acctSeqNo == null) {
                     if (other$acctSeqNo == null) {
                        break label153;
                     }
                  } else if (this$acctSeqNo.equals(other$acctSeqNo)) {
                     break label153;
                  }

                  return false;
               }

               Object this$ccy = this.getCcy();
               Object other$ccy = other.getCcy();
               if (this$ccy == null) {
                  if (other$ccy != null) {
                     return false;
                  }
               } else if (!this$ccy.equals(other$ccy)) {
                  return false;
               }

               label139: {
                  Object this$othRealBaseAcctNo = this.getOthRealBaseAcctNo();
                  Object other$othRealBaseAcctNo = other.getOthRealBaseAcctNo();
                  if (this$othRealBaseAcctNo == null) {
                     if (other$othRealBaseAcctNo == null) {
                        break label139;
                     }
                  } else if (this$othRealBaseAcctNo.equals(other$othRealBaseAcctNo)) {
                     break label139;
                  }

                  return false;
               }

               Object this$othRealTranName = this.getOthRealTranName();
               Object other$othRealTranName = other.getOthRealTranName();
               if (this$othRealTranName == null) {
                  if (other$othRealTranName != null) {
                     return false;
                  }
               } else if (!this$othRealTranName.equals(other$othRealTranName)) {
                  return false;
               }

               label125: {
                  Object this$othBaseAcctNo = this.getOthBaseAcctNo();
                  Object other$othBaseAcctNo = other.getOthBaseAcctNo();
                  if (this$othBaseAcctNo == null) {
                     if (other$othBaseAcctNo == null) {
                        break label125;
                     }
                  } else if (this$othBaseAcctNo.equals(other$othBaseAcctNo)) {
                     break label125;
                  }

                  return false;
               }

               label118: {
                  Object this$tranHighestAmt = this.getTranHighestAmt();
                  Object other$tranHighestAmt = other.getTranHighestAmt();
                  if (this$tranHighestAmt == null) {
                     if (other$tranHighestAmt == null) {
                        break label118;
                     }
                  } else if (this$tranHighestAmt.equals(other$tranHighestAmt)) {
                     break label118;
                  }

                  return false;
               }

               Object this$othAcctName = this.getOthAcctName();
               Object other$othAcctName = other.getOthAcctName();
               if (this$othAcctName == null) {
                  if (other$othAcctName != null) {
                     return false;
                  }
               } else if (!this$othAcctName.equals(other$othAcctName)) {
                  return false;
               }

               label104: {
                  Object this$lowestTranAmt = this.getLowestTranAmt();
                  Object other$lowestTranAmt = other.getLowestTranAmt();
                  if (this$lowestTranAmt == null) {
                     if (other$lowestTranAmt == null) {
                        break label104;
                     }
                  } else if (this$lowestTranAmt.equals(other$lowestTranAmt)) {
                     break label104;
                  }

                  return false;
               }

               label97: {
                  Object this$startDate = this.getStartDate();
                  Object other$startDate = other.getStartDate();
                  if (this$startDate == null) {
                     if (other$startDate == null) {
                        break label97;
                     }
                  } else if (this$startDate.equals(other$startDate)) {
                     break label97;
                  }

                  return false;
               }

               Object this$endDate = this.getEndDate();
               Object other$endDate = other.getEndDate();
               if (this$endDate == null) {
                  if (other$endDate != null) {
                     return false;
                  }
               } else if (!this$endDate.equals(other$endDate)) {
                  return false;
               }

               Object this$printStatus = this.getPrintStatus();
               Object other$printStatus = other.getPrintStatus();
               if (this$printStatus == null) {
                  if (other$printStatus != null) {
                     return false;
                  }
               } else if (!this$printStatus.equals(other$printStatus)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1220100010In.Body;
      }
      public String toString() {
         return "Core1220100010In.Body(baseAcctNo=" + this.getBaseAcctNo() + ", prodType=" + this.getProdType() + ", acctSeqNo=" + this.getAcctSeqNo() + ", ccy=" + this.getCcy() + ", othRealBaseAcctNo=" + this.getOthRealBaseAcctNo() + ", othRealTranName=" + this.getOthRealTranName() + ", othBaseAcctNo=" + this.getOthBaseAcctNo() + ", tranHighestAmt=" + this.getTranHighestAmt() + ", othAcctName=" + this.getOthAcctName() + ", lowestTranAmt=" + this.getLowestTranAmt() + ", startDate=" + this.getStartDate() + ", endDate=" + this.getEndDate() + ", printStatus=" + this.getPrintStatus() + ")";
      }
   }
}
