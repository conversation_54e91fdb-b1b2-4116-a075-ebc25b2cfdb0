package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;

@MessageIn
public class Core1200061074In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1200061074In.Body body;

   public Core1200061074In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1200061074In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1200061074In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200061074In)) {
         return false;
      } else {
         Core1200061074In other = (Core1200061074In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200061074In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "保证金账号",
         notNull = false,
         length = "50",
         remark = "保证金账号",
         maxSize = 50
      )
      private String depBaseAcctNo;
      @V(
         desc = "交易日期",
         notNull = false,
         remark = "交易日期"
      )
      private String tranDate;
      @V(
         desc = "兑换类型",
         notNull = false,
         length = "1",
         inDesc = "B-结汇,S-售汇,E-外币兑换",
         remark = "兑换类型",
         maxSize = 1
      )
      private String exType;
      @V(
         desc = "操作类型",
         notNull = false,
         length = "2",
         inDesc = "D-删除,U-修改,A-新增",
         remark = "操作类型",
         maxSize = 2
      )
      private String options;
      @V(
         desc = "交割方式",
         notNull = false,
         length = "1",
         inDesc = "1-择期交割日,0-固定交割日",
         remark = "远期结售汇交割方式",
         maxSize = 1
      )
      private String deliveryMethod;
      @V(
         desc = "固定交割日",
         notNull = false,
         remark = "固定交割日"
      )
      private String fixDealDate;
      @V(
         desc = "择期交割日",
         notNull = false,
         remark = "择期交割日"
      )
      private String changeDealDate;
      @V(
         desc = "择期交割结束日",
         notNull = false,
         remark = "择期交割结束日"
      )
      private String changeDealDateEnd;
      @V(
         desc = "交易对手",
         notNull = false,
         length = "1",
         inDesc = "1-对客,2-合作行",
         remark = "交易对手",
         maxSize = 1
      )
      private String dealRecAcct;
      @V(
         desc = "业务编号",
         notNull = false,
         length = "50",
         remark = "业务编号",
         maxSize = 50
      )
      private String busiNo;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "客户中文名",
         notNull = false,
         length = "50",
         remark = "客户中文名",
         maxSize = 50
      )
      private String clientNameChn;
      @V(
         desc = "客户英文名称",
         notNull = false,
         length = "50",
         remark = "客户英文名称",
         maxSize = 50
      )
      private String clientNameEn;
      @V(
         desc = "外币币种",
         notNull = false,
         length = "3",
         remark = "外币币种",
         maxSize = 3
      )
      private String foreCcy;
      @V(
         desc = "外币账号",
         notNull = false,
         length = "50",
         remark = "外币账号",
         maxSize = 50
      )
      private String foreBaseAcctNo;
      @V(
         desc = "外币账户序号",
         notNull = false,
         length = "5",
         remark = "外币账户序号",
         maxSize = 5
      )
      private String foreAcctSeqNo;
      @V(
         desc = "人民币账户",
         notNull = false,
         length = "50",
         remark = "人名币账户",
         maxSize = 50
      )
      private String cnyBaseAcctNo;
      @V(
         desc = "人民币账户序号",
         notNull = false,
         length = "5",
         remark = "人名币账户序号",
         maxSize = 5
      )
      private String cnyAcctSeqNo;
      @V(
         desc = "我行对客户报价",
         notNull = false,
         length = "25",
         remark = "我行对客户报价",
         decimalLength = 10,
         precision = 10
      )
      private BigDecimal offerLetter;
      @V(
         desc = "外币金额",
         notNull = false,
         length = "17",
         remark = "外币金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal foreAmt;
      @V(
         desc = "人民币金额",
         notNull = false,
         length = "17",
         remark = "人名币金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal cnyAmt;
      @V(
         desc = "占用额度币种",
         notNull = false,
         length = "3",
         remark = "占用额度币种",
         maxSize = 3
      )
      private String occCcy;
      @V(
         desc = "占用额度金额",
         notNull = false,
         length = "17",
         remark = "占用额度金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal occAmt;
      @V(
         desc = "保证金币种",
         notNull = false,
         length = "3",
         remark = "保证金币种",
         maxSize = 3
      )
      private String depCcy;
      @V(
         desc = "当日存入金额",
         notNull = false,
         length = "17",
         remark = "当日存入金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal depAmt;
      @V(
         desc = "保证金账号序号",
         notNull = false,
         length = "5",
         remark = "保证金账户序号",
         maxSize = 5
      )
      private String depAcctSeqNo;
      @V(
         desc = "合作行名称",
         notNull = false,
         length = "50",
         remark = "合作行名称",
         maxSize = 50
      )
      private String teamBankName;
      @V(
         desc = "合作行报价",
         notNull = false,
         length = "25",
         remark = "合作行报价",
         decimalLength = 10,
         precision = 10
      )
      private BigDecimal teamBankRate;
      @V(
         desc = "序号",
         notNull = false,
         length = "50",
         remark = "序号",
         maxSize = 50
      )
      private String seqNo;
      @V(
         desc = "备注",
         notNull = false,
         length = "100",
         remark = "备注",
         maxSize = 100
      )
      private String remarks;
      @V(
         desc = "客户中文名称",
         notNull = false,
         length = "200",
         remark = "客户中文名称",
         maxSize = 200
      )
      private String chClientName;
      @V(
         desc = "客户英文名称",
         notNull = false,
         length = "200",
         remark = "客户英文名称",
         maxSize = 200
      )
      private String enClientName;

      public String getDepBaseAcctNo() {
         return this.depBaseAcctNo;
      }

      public String getTranDate() {
         return this.tranDate;
      }

      public String getExType() {
         return this.exType;
      }

      public String getOptions() {
         return this.options;
      }

      public String getDeliveryMethod() {
         return this.deliveryMethod;
      }

      public String getFixDealDate() {
         return this.fixDealDate;
      }

      public String getChangeDealDate() {
         return this.changeDealDate;
      }

      public String getChangeDealDateEnd() {
         return this.changeDealDateEnd;
      }

      public String getDealRecAcct() {
         return this.dealRecAcct;
      }

      public String getBusiNo() {
         return this.busiNo;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public String getClientNameChn() {
         return this.clientNameChn;
      }

      public String getClientNameEn() {
         return this.clientNameEn;
      }

      public String getForeCcy() {
         return this.foreCcy;
      }

      public String getForeBaseAcctNo() {
         return this.foreBaseAcctNo;
      }

      public String getForeAcctSeqNo() {
         return this.foreAcctSeqNo;
      }

      public String getCnyBaseAcctNo() {
         return this.cnyBaseAcctNo;
      }

      public String getCnyAcctSeqNo() {
         return this.cnyAcctSeqNo;
      }

      public BigDecimal getOfferLetter() {
         return this.offerLetter;
      }

      public BigDecimal getForeAmt() {
         return this.foreAmt;
      }

      public BigDecimal getCnyAmt() {
         return this.cnyAmt;
      }

      public String getOccCcy() {
         return this.occCcy;
      }

      public BigDecimal getOccAmt() {
         return this.occAmt;
      }

      public String getDepCcy() {
         return this.depCcy;
      }

      public BigDecimal getDepAmt() {
         return this.depAmt;
      }

      public String getDepAcctSeqNo() {
         return this.depAcctSeqNo;
      }

      public String getTeamBankName() {
         return this.teamBankName;
      }

      public BigDecimal getTeamBankRate() {
         return this.teamBankRate;
      }

      public String getSeqNo() {
         return this.seqNo;
      }

      public String getRemarks() {
         return this.remarks;
      }

      public String getChClientName() {
         return this.chClientName;
      }

      public String getEnClientName() {
         return this.enClientName;
      }

      public void setDepBaseAcctNo(String depBaseAcctNo) {
         this.depBaseAcctNo = depBaseAcctNo;
      }

      public void setTranDate(String tranDate) {
         this.tranDate = tranDate;
      }

      public void setExType(String exType) {
         this.exType = exType;
      }

      public void setOptions(String options) {
         this.options = options;
      }

      public void setDeliveryMethod(String deliveryMethod) {
         this.deliveryMethod = deliveryMethod;
      }

      public void setFixDealDate(String fixDealDate) {
         this.fixDealDate = fixDealDate;
      }

      public void setChangeDealDate(String changeDealDate) {
         this.changeDealDate = changeDealDate;
      }

      public void setChangeDealDateEnd(String changeDealDateEnd) {
         this.changeDealDateEnd = changeDealDateEnd;
      }

      public void setDealRecAcct(String dealRecAcct) {
         this.dealRecAcct = dealRecAcct;
      }

      public void setBusiNo(String busiNo) {
         this.busiNo = busiNo;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setClientNameChn(String clientNameChn) {
         this.clientNameChn = clientNameChn;
      }

      public void setClientNameEn(String clientNameEn) {
         this.clientNameEn = clientNameEn;
      }

      public void setForeCcy(String foreCcy) {
         this.foreCcy = foreCcy;
      }

      public void setForeBaseAcctNo(String foreBaseAcctNo) {
         this.foreBaseAcctNo = foreBaseAcctNo;
      }

      public void setForeAcctSeqNo(String foreAcctSeqNo) {
         this.foreAcctSeqNo = foreAcctSeqNo;
      }

      public void setCnyBaseAcctNo(String cnyBaseAcctNo) {
         this.cnyBaseAcctNo = cnyBaseAcctNo;
      }

      public void setCnyAcctSeqNo(String cnyAcctSeqNo) {
         this.cnyAcctSeqNo = cnyAcctSeqNo;
      }

      public void setOfferLetter(BigDecimal offerLetter) {
         this.offerLetter = offerLetter;
      }

      public void setForeAmt(BigDecimal foreAmt) {
         this.foreAmt = foreAmt;
      }

      public void setCnyAmt(BigDecimal cnyAmt) {
         this.cnyAmt = cnyAmt;
      }

      public void setOccCcy(String occCcy) {
         this.occCcy = occCcy;
      }

      public void setOccAmt(BigDecimal occAmt) {
         this.occAmt = occAmt;
      }

      public void setDepCcy(String depCcy) {
         this.depCcy = depCcy;
      }

      public void setDepAmt(BigDecimal depAmt) {
         this.depAmt = depAmt;
      }

      public void setDepAcctSeqNo(String depAcctSeqNo) {
         this.depAcctSeqNo = depAcctSeqNo;
      }

      public void setTeamBankName(String teamBankName) {
         this.teamBankName = teamBankName;
      }

      public void setTeamBankRate(BigDecimal teamBankRate) {
         this.teamBankRate = teamBankRate;
      }

      public void setSeqNo(String seqNo) {
         this.seqNo = seqNo;
      }

      public void setRemarks(String remarks) {
         this.remarks = remarks;
      }

      public void setChClientName(String chClientName) {
         this.chClientName = chClientName;
      }

      public void setEnClientName(String enClientName) {
         this.enClientName = enClientName;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1200061074In.Body)) {
            return false;
         } else {
            Core1200061074In.Body other = (Core1200061074In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$depBaseAcctNo = this.getDepBaseAcctNo();
               Object other$depBaseAcctNo = other.getDepBaseAcctNo();
               if (this$depBaseAcctNo == null) {
                  if (other$depBaseAcctNo != null) {
                     return false;
                  }
               } else if (!this$depBaseAcctNo.equals(other$depBaseAcctNo)) {
                  return false;
               }

               Object this$tranDate = this.getTranDate();
               Object other$tranDate = other.getTranDate();
               if (this$tranDate == null) {
                  if (other$tranDate != null) {
                     return false;
                  }
               } else if (!this$tranDate.equals(other$tranDate)) {
                  return false;
               }

               Object this$exType = this.getExType();
               Object other$exType = other.getExType();
               if (this$exType == null) {
                  if (other$exType != null) {
                     return false;
                  }
               } else if (!this$exType.equals(other$exType)) {
                  return false;
               }

               label398: {
                  Object this$options = this.getOptions();
                  Object other$options = other.getOptions();
                  if (this$options == null) {
                     if (other$options == null) {
                        break label398;
                     }
                  } else if (this$options.equals(other$options)) {
                     break label398;
                  }

                  return false;
               }

               label391: {
                  Object this$deliveryMethod = this.getDeliveryMethod();
                  Object other$deliveryMethod = other.getDeliveryMethod();
                  if (this$deliveryMethod == null) {
                     if (other$deliveryMethod == null) {
                        break label391;
                     }
                  } else if (this$deliveryMethod.equals(other$deliveryMethod)) {
                     break label391;
                  }

                  return false;
               }

               Object this$fixDealDate = this.getFixDealDate();
               Object other$fixDealDate = other.getFixDealDate();
               if (this$fixDealDate == null) {
                  if (other$fixDealDate != null) {
                     return false;
                  }
               } else if (!this$fixDealDate.equals(other$fixDealDate)) {
                  return false;
               }

               label377: {
                  Object this$changeDealDate = this.getChangeDealDate();
                  Object other$changeDealDate = other.getChangeDealDate();
                  if (this$changeDealDate == null) {
                     if (other$changeDealDate == null) {
                        break label377;
                     }
                  } else if (this$changeDealDate.equals(other$changeDealDate)) {
                     break label377;
                  }

                  return false;
               }

               label370: {
                  Object this$changeDealDateEnd = this.getChangeDealDateEnd();
                  Object other$changeDealDateEnd = other.getChangeDealDateEnd();
                  if (this$changeDealDateEnd == null) {
                     if (other$changeDealDateEnd == null) {
                        break label370;
                     }
                  } else if (this$changeDealDateEnd.equals(other$changeDealDateEnd)) {
                     break label370;
                  }

                  return false;
               }

               Object this$dealRecAcct = this.getDealRecAcct();
               Object other$dealRecAcct = other.getDealRecAcct();
               if (this$dealRecAcct == null) {
                  if (other$dealRecAcct != null) {
                     return false;
                  }
               } else if (!this$dealRecAcct.equals(other$dealRecAcct)) {
                  return false;
               }

               Object this$busiNo = this.getBusiNo();
               Object other$busiNo = other.getBusiNo();
               if (this$busiNo == null) {
                  if (other$busiNo != null) {
                     return false;
                  }
               } else if (!this$busiNo.equals(other$busiNo)) {
                  return false;
               }

               label349: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label349;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label349;
                  }

                  return false;
               }

               label342: {
                  Object this$acctSeqNo = this.getAcctSeqNo();
                  Object other$acctSeqNo = other.getAcctSeqNo();
                  if (this$acctSeqNo == null) {
                     if (other$acctSeqNo == null) {
                        break label342;
                     }
                  } else if (this$acctSeqNo.equals(other$acctSeqNo)) {
                     break label342;
                  }

                  return false;
               }

               Object this$clientNo = this.getClientNo();
               Object other$clientNo = other.getClientNo();
               if (this$clientNo == null) {
                  if (other$clientNo != null) {
                     return false;
                  }
               } else if (!this$clientNo.equals(other$clientNo)) {
                  return false;
               }

               label328: {
                  Object this$clientNameChn = this.getClientNameChn();
                  Object other$clientNameChn = other.getClientNameChn();
                  if (this$clientNameChn == null) {
                     if (other$clientNameChn == null) {
                        break label328;
                     }
                  } else if (this$clientNameChn.equals(other$clientNameChn)) {
                     break label328;
                  }

                  return false;
               }

               Object this$clientNameEn = this.getClientNameEn();
               Object other$clientNameEn = other.getClientNameEn();
               if (this$clientNameEn == null) {
                  if (other$clientNameEn != null) {
                     return false;
                  }
               } else if (!this$clientNameEn.equals(other$clientNameEn)) {
                  return false;
               }

               label314: {
                  Object this$foreCcy = this.getForeCcy();
                  Object other$foreCcy = other.getForeCcy();
                  if (this$foreCcy == null) {
                     if (other$foreCcy == null) {
                        break label314;
                     }
                  } else if (this$foreCcy.equals(other$foreCcy)) {
                     break label314;
                  }

                  return false;
               }

               Object this$foreBaseAcctNo = this.getForeBaseAcctNo();
               Object other$foreBaseAcctNo = other.getForeBaseAcctNo();
               if (this$foreBaseAcctNo == null) {
                  if (other$foreBaseAcctNo != null) {
                     return false;
                  }
               } else if (!this$foreBaseAcctNo.equals(other$foreBaseAcctNo)) {
                  return false;
               }

               Object this$foreAcctSeqNo = this.getForeAcctSeqNo();
               Object other$foreAcctSeqNo = other.getForeAcctSeqNo();
               if (this$foreAcctSeqNo == null) {
                  if (other$foreAcctSeqNo != null) {
                     return false;
                  }
               } else if (!this$foreAcctSeqNo.equals(other$foreAcctSeqNo)) {
                  return false;
               }

               Object this$cnyBaseAcctNo = this.getCnyBaseAcctNo();
               Object other$cnyBaseAcctNo = other.getCnyBaseAcctNo();
               if (this$cnyBaseAcctNo == null) {
                  if (other$cnyBaseAcctNo != null) {
                     return false;
                  }
               } else if (!this$cnyBaseAcctNo.equals(other$cnyBaseAcctNo)) {
                  return false;
               }

               label286: {
                  Object this$cnyAcctSeqNo = this.getCnyAcctSeqNo();
                  Object other$cnyAcctSeqNo = other.getCnyAcctSeqNo();
                  if (this$cnyAcctSeqNo == null) {
                     if (other$cnyAcctSeqNo == null) {
                        break label286;
                     }
                  } else if (this$cnyAcctSeqNo.equals(other$cnyAcctSeqNo)) {
                     break label286;
                  }

                  return false;
               }

               label279: {
                  Object this$offerLetter = this.getOfferLetter();
                  Object other$offerLetter = other.getOfferLetter();
                  if (this$offerLetter == null) {
                     if (other$offerLetter == null) {
                        break label279;
                     }
                  } else if (this$offerLetter.equals(other$offerLetter)) {
                     break label279;
                  }

                  return false;
               }

               Object this$foreAmt = this.getForeAmt();
               Object other$foreAmt = other.getForeAmt();
               if (this$foreAmt == null) {
                  if (other$foreAmt != null) {
                     return false;
                  }
               } else if (!this$foreAmt.equals(other$foreAmt)) {
                  return false;
               }

               label265: {
                  Object this$cnyAmt = this.getCnyAmt();
                  Object other$cnyAmt = other.getCnyAmt();
                  if (this$cnyAmt == null) {
                     if (other$cnyAmt == null) {
                        break label265;
                     }
                  } else if (this$cnyAmt.equals(other$cnyAmt)) {
                     break label265;
                  }

                  return false;
               }

               label258: {
                  Object this$occCcy = this.getOccCcy();
                  Object other$occCcy = other.getOccCcy();
                  if (this$occCcy == null) {
                     if (other$occCcy == null) {
                        break label258;
                     }
                  } else if (this$occCcy.equals(other$occCcy)) {
                     break label258;
                  }

                  return false;
               }

               Object this$occAmt = this.getOccAmt();
               Object other$occAmt = other.getOccAmt();
               if (this$occAmt == null) {
                  if (other$occAmt != null) {
                     return false;
                  }
               } else if (!this$occAmt.equals(other$occAmt)) {
                  return false;
               }

               Object this$depCcy = this.getDepCcy();
               Object other$depCcy = other.getDepCcy();
               if (this$depCcy == null) {
                  if (other$depCcy != null) {
                     return false;
                  }
               } else if (!this$depCcy.equals(other$depCcy)) {
                  return false;
               }

               label237: {
                  Object this$depAmt = this.getDepAmt();
                  Object other$depAmt = other.getDepAmt();
                  if (this$depAmt == null) {
                     if (other$depAmt == null) {
                        break label237;
                     }
                  } else if (this$depAmt.equals(other$depAmt)) {
                     break label237;
                  }

                  return false;
               }

               label230: {
                  Object this$depAcctSeqNo = this.getDepAcctSeqNo();
                  Object other$depAcctSeqNo = other.getDepAcctSeqNo();
                  if (this$depAcctSeqNo == null) {
                     if (other$depAcctSeqNo == null) {
                        break label230;
                     }
                  } else if (this$depAcctSeqNo.equals(other$depAcctSeqNo)) {
                     break label230;
                  }

                  return false;
               }

               Object this$teamBankName = this.getTeamBankName();
               Object other$teamBankName = other.getTeamBankName();
               if (this$teamBankName == null) {
                  if (other$teamBankName != null) {
                     return false;
                  }
               } else if (!this$teamBankName.equals(other$teamBankName)) {
                  return false;
               }

               label216: {
                  Object this$teamBankRate = this.getTeamBankRate();
                  Object other$teamBankRate = other.getTeamBankRate();
                  if (this$teamBankRate == null) {
                     if (other$teamBankRate == null) {
                        break label216;
                     }
                  } else if (this$teamBankRate.equals(other$teamBankRate)) {
                     break label216;
                  }

                  return false;
               }

               Object this$seqNo = this.getSeqNo();
               Object other$seqNo = other.getSeqNo();
               if (this$seqNo == null) {
                  if (other$seqNo != null) {
                     return false;
                  }
               } else if (!this$seqNo.equals(other$seqNo)) {
                  return false;
               }

               label202: {
                  Object this$remarks = this.getRemarks();
                  Object other$remarks = other.getRemarks();
                  if (this$remarks == null) {
                     if (other$remarks == null) {
                        break label202;
                     }
                  } else if (this$remarks.equals(other$remarks)) {
                     break label202;
                  }

                  return false;
               }

               Object this$chClientName = this.getChClientName();
               Object other$chClientName = other.getChClientName();
               if (this$chClientName == null) {
                  if (other$chClientName != null) {
                     return false;
                  }
               } else if (!this$chClientName.equals(other$chClientName)) {
                  return false;
               }

               Object this$enClientName = this.getEnClientName();
               Object other$enClientName = other.getEnClientName();
               if (this$enClientName == null) {
                  if (other$enClientName != null) {
                     return false;
                  }
               } else if (!this$enClientName.equals(other$enClientName)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1200061074In.Body;
      }
      public String toString() {
         return "Core1200061074In.Body(depBaseAcctNo=" + this.getDepBaseAcctNo() + ", tranDate=" + this.getTranDate() + ", exType=" + this.getExType() + ", options=" + this.getOptions() + ", deliveryMethod=" + this.getDeliveryMethod() + ", fixDealDate=" + this.getFixDealDate() + ", changeDealDate=" + this.getChangeDealDate() + ", changeDealDateEnd=" + this.getChangeDealDateEnd() + ", dealRecAcct=" + this.getDealRecAcct() + ", busiNo=" + this.getBusiNo() + ", baseAcctNo=" + this.getBaseAcctNo() + ", acctSeqNo=" + this.getAcctSeqNo() + ", clientNo=" + this.getClientNo() + ", clientNameChn=" + this.getClientNameChn() + ", clientNameEn=" + this.getClientNameEn() + ", foreCcy=" + this.getForeCcy() + ", foreBaseAcctNo=" + this.getForeBaseAcctNo() + ", foreAcctSeqNo=" + this.getForeAcctSeqNo() + ", cnyBaseAcctNo=" + this.getCnyBaseAcctNo() + ", cnyAcctSeqNo=" + this.getCnyAcctSeqNo() + ", offerLetter=" + this.getOfferLetter() + ", foreAmt=" + this.getForeAmt() + ", cnyAmt=" + this.getCnyAmt() + ", occCcy=" + this.getOccCcy() + ", occAmt=" + this.getOccAmt() + ", depCcy=" + this.getDepCcy() + ", depAmt=" + this.getDepAmt() + ", depAcctSeqNo=" + this.getDepAcctSeqNo() + ", teamBankName=" + this.getTeamBankName() + ", teamBankRate=" + this.getTeamBankRate() + ", seqNo=" + this.getSeqNo() + ", remarks=" + this.getRemarks() + ", chClientName=" + this.getChClientName() + ", enClientName=" + this.getEnClientName() + ")";
      }
   }
}
