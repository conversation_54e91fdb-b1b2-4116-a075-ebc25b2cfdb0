package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400100218Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "期次总额度",
      notNull = false,
      length = "17",
      remark = "期次总额度",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal stageTotalQuota;
   @V(
      desc = "期次已分配额度",
      notNull = false,
      length = "17",
      remark = "期次已分配额度",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal stageDistributeQuota;
   @V(
      desc = "期次剩余额度",
      notNull = false,
      length = "17",
      remark = "期次剩余额度",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal stageLeaveQuota;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100218Out.ClientListArray> clientListArray;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100218Out.ChannelArray> channelArray;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100218Out.BranchArray> branchArray;

   public BigDecimal getStageTotalQuota() {
      return this.stageTotalQuota;
   }

   public BigDecimal getStageDistributeQuota() {
      return this.stageDistributeQuota;
   }

   public BigDecimal getStageLeaveQuota() {
      return this.stageLeaveQuota;
   }

   public List<Core1400100218Out.ClientListArray> getClientListArray() {
      return this.clientListArray;
   }

   public List<Core1400100218Out.ChannelArray> getChannelArray() {
      return this.channelArray;
   }

   public List<Core1400100218Out.BranchArray> getBranchArray() {
      return this.branchArray;
   }

   public void setStageTotalQuota(BigDecimal stageTotalQuota) {
      this.stageTotalQuota = stageTotalQuota;
   }

   public void setStageDistributeQuota(BigDecimal stageDistributeQuota) {
      this.stageDistributeQuota = stageDistributeQuota;
   }

   public void setStageLeaveQuota(BigDecimal stageLeaveQuota) {
      this.stageLeaveQuota = stageLeaveQuota;
   }

   public void setClientListArray(List<Core1400100218Out.ClientListArray> clientListArray) {
      this.clientListArray = clientListArray;
   }

   public void setChannelArray(List<Core1400100218Out.ChannelArray> channelArray) {
      this.channelArray = channelArray;
   }

   public void setBranchArray(List<Core1400100218Out.BranchArray> branchArray) {
      this.branchArray = branchArray;
   }

   public String toString() {
      return "Core1400100218Out(stageTotalQuota=" + this.getStageTotalQuota() + ", stageDistributeQuota=" + this.getStageDistributeQuota() + ", stageLeaveQuota=" + this.getStageLeaveQuota() + ", clientListArray=" + this.getClientListArray() + ", channelArray=" + this.getChannelArray() + ", branchArray=" + this.getBranchArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100218Out)) {
         return false;
      } else {
         Core1400100218Out other = (Core1400100218Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$stageTotalQuota = this.getStageTotalQuota();
            Object other$stageTotalQuota = other.getStageTotalQuota();
            if (this$stageTotalQuota == null) {
               if (other$stageTotalQuota != null) {
                  return false;
               }
            } else if (!this$stageTotalQuota.equals(other$stageTotalQuota)) {
               return false;
            }

            Object this$stageDistributeQuota = this.getStageDistributeQuota();
            Object other$stageDistributeQuota = other.getStageDistributeQuota();
            if (this$stageDistributeQuota == null) {
               if (other$stageDistributeQuota != null) {
                  return false;
               }
            } else if (!this$stageDistributeQuota.equals(other$stageDistributeQuota)) {
               return false;
            }

            label71: {
               Object this$stageLeaveQuota = this.getStageLeaveQuota();
               Object other$stageLeaveQuota = other.getStageLeaveQuota();
               if (this$stageLeaveQuota == null) {
                  if (other$stageLeaveQuota == null) {
                     break label71;
                  }
               } else if (this$stageLeaveQuota.equals(other$stageLeaveQuota)) {
                  break label71;
               }

               return false;
            }

            label64: {
               Object this$clientListArray = this.getClientListArray();
               Object other$clientListArray = other.getClientListArray();
               if (this$clientListArray == null) {
                  if (other$clientListArray == null) {
                     break label64;
                  }
               } else if (this$clientListArray.equals(other$clientListArray)) {
                  break label64;
               }

               return false;
            }

            Object this$channelArray = this.getChannelArray();
            Object other$channelArray = other.getChannelArray();
            if (this$channelArray == null) {
               if (other$channelArray != null) {
                  return false;
               }
            } else if (!this$channelArray.equals(other$channelArray)) {
               return false;
            }

            Object this$branchArray = this.getBranchArray();
            Object other$branchArray = other.getBranchArray();
            if (this$branchArray == null) {
               if (other$branchArray != null) {
                  return false;
               }
            } else if (!this$branchArray.equals(other$branchArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100218Out;
   }
   public static class BranchArray {
      @V(
         desc = "支行总额度",
         notNull = false,
         length = "17",
         remark = "支行总额度",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal branchTotalQuota;
      @V(
         desc = "支行已分配额度",
         notNull = false,
         length = "17",
         remark = "支行已分配额度",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal branchDistributeQuota;
      @V(
         desc = "支行剩余额度",
         notNull = false,
         length = "17",
         remark = "支行剩余额度",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal branchLeaveQuota;
      @V(
         desc = "所属机构号",
         notNull = false,
         length = "50",
         remark = "所属机构号",
         maxSize = 50
      )
      private String branch;

      public BigDecimal getBranchTotalQuota() {
         return this.branchTotalQuota;
      }

      public BigDecimal getBranchDistributeQuota() {
         return this.branchDistributeQuota;
      }

      public BigDecimal getBranchLeaveQuota() {
         return this.branchLeaveQuota;
      }

      public String getBranch() {
         return this.branch;
      }

      public void setBranchTotalQuota(BigDecimal branchTotalQuota) {
         this.branchTotalQuota = branchTotalQuota;
      }

      public void setBranchDistributeQuota(BigDecimal branchDistributeQuota) {
         this.branchDistributeQuota = branchDistributeQuota;
      }

      public void setBranchLeaveQuota(BigDecimal branchLeaveQuota) {
         this.branchLeaveQuota = branchLeaveQuota;
      }

      public void setBranch(String branch) {
         this.branch = branch;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100218Out.BranchArray)) {
            return false;
         } else {
            Core1400100218Out.BranchArray other = (Core1400100218Out.BranchArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label59: {
                  Object this$branchTotalQuota = this.getBranchTotalQuota();
                  Object other$branchTotalQuota = other.getBranchTotalQuota();
                  if (this$branchTotalQuota == null) {
                     if (other$branchTotalQuota == null) {
                        break label59;
                     }
                  } else if (this$branchTotalQuota.equals(other$branchTotalQuota)) {
                     break label59;
                  }

                  return false;
               }

               Object this$branchDistributeQuota = this.getBranchDistributeQuota();
               Object other$branchDistributeQuota = other.getBranchDistributeQuota();
               if (this$branchDistributeQuota == null) {
                  if (other$branchDistributeQuota != null) {
                     return false;
                  }
               } else if (!this$branchDistributeQuota.equals(other$branchDistributeQuota)) {
                  return false;
               }

               Object this$branchLeaveQuota = this.getBranchLeaveQuota();
               Object other$branchLeaveQuota = other.getBranchLeaveQuota();
               if (this$branchLeaveQuota == null) {
                  if (other$branchLeaveQuota != null) {
                     return false;
                  }
               } else if (!this$branchLeaveQuota.equals(other$branchLeaveQuota)) {
                  return false;
               }

               Object this$branch = this.getBranch();
               Object other$branch = other.getBranch();
               if (this$branch == null) {
                  if (other$branch != null) {
                     return false;
                  }
               } else if (!this$branch.equals(other$branch)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100218Out.BranchArray;
      }
      public String toString() {
         return "Core1400100218Out.BranchArray(branchTotalQuota=" + this.getBranchTotalQuota() + ", branchDistributeQuota=" + this.getBranchDistributeQuota() + ", branchLeaveQuota=" + this.getBranchLeaveQuota() + ", branch=" + this.getBranch() + ")";
      }
   }

   public static class ChannelArray {
      @V(
         desc = "渠道总额度",
         notNull = false,
         length = "17",
         remark = "渠道总额度",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal channelTotalQuota;
      @V(
         desc = "渠道已分配额度",
         notNull = false,
         length = "17",
         remark = "渠道已分配额度",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal channelDistributeQuota;
      @V(
         desc = "渠道剩余额度",
         notNull = false,
         length = "17",
         remark = "渠道剩余额度",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal channelLeaveQuota;
      @V(
         desc = "渠道",
         notNull = false,
         length = "10",
         remark = "渠道细类",
         maxSize = 10
      )
      private String channel;

      public BigDecimal getChannelTotalQuota() {
         return this.channelTotalQuota;
      }

      public BigDecimal getChannelDistributeQuota() {
         return this.channelDistributeQuota;
      }

      public BigDecimal getChannelLeaveQuota() {
         return this.channelLeaveQuota;
      }

      public String getChannel() {
         return this.channel;
      }

      public void setChannelTotalQuota(BigDecimal channelTotalQuota) {
         this.channelTotalQuota = channelTotalQuota;
      }

      public void setChannelDistributeQuota(BigDecimal channelDistributeQuota) {
         this.channelDistributeQuota = channelDistributeQuota;
      }

      public void setChannelLeaveQuota(BigDecimal channelLeaveQuota) {
         this.channelLeaveQuota = channelLeaveQuota;
      }

      public void setChannel(String channel) {
         this.channel = channel;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100218Out.ChannelArray)) {
            return false;
         } else {
            Core1400100218Out.ChannelArray other = (Core1400100218Out.ChannelArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label59: {
                  Object this$channelTotalQuota = this.getChannelTotalQuota();
                  Object other$channelTotalQuota = other.getChannelTotalQuota();
                  if (this$channelTotalQuota == null) {
                     if (other$channelTotalQuota == null) {
                        break label59;
                     }
                  } else if (this$channelTotalQuota.equals(other$channelTotalQuota)) {
                     break label59;
                  }

                  return false;
               }

               Object this$channelDistributeQuota = this.getChannelDistributeQuota();
               Object other$channelDistributeQuota = other.getChannelDistributeQuota();
               if (this$channelDistributeQuota == null) {
                  if (other$channelDistributeQuota != null) {
                     return false;
                  }
               } else if (!this$channelDistributeQuota.equals(other$channelDistributeQuota)) {
                  return false;
               }

               Object this$channelLeaveQuota = this.getChannelLeaveQuota();
               Object other$channelLeaveQuota = other.getChannelLeaveQuota();
               if (this$channelLeaveQuota == null) {
                  if (other$channelLeaveQuota != null) {
                     return false;
                  }
               } else if (!this$channelLeaveQuota.equals(other$channelLeaveQuota)) {
                  return false;
               }

               Object this$channel = this.getChannel();
               Object other$channel = other.getChannel();
               if (this$channel == null) {
                  if (other$channel != null) {
                     return false;
                  }
               } else if (!this$channel.equals(other$channel)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100218Out.ChannelArray;
      }
      public String toString() {
         return "Core1400100218Out.ChannelArray(channelTotalQuota=" + this.getChannelTotalQuota() + ", channelDistributeQuota=" + this.getChannelDistributeQuota() + ", channelLeaveQuota=" + this.getChannelLeaveQuota() + ", channel=" + this.getChannel() + ")";
      }
   }

   public static class ClientListArray {
      @V(
         desc = "白名单总额度",
         notNull = false,
         length = "17",
         remark = "白名单总额度",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal whiteTotalQuota;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "白名单已分配额度",
         notNull = false,
         length = "17",
         remark = "白名单已分配额度",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal whiteDistributeQuota;
      @V(
         desc = "证件类型",
         notNull = false,
         length = "3",
         remark = "证件类型",
         maxSize = 3
      )
      private String documentType;
      @V(
         desc = "证件号码",
         notNull = false,
         length = "50",
         remark = "证件号码",
         maxSize = 50
      )
      private String documentId;
      @V(
         desc = "白名单剩余额度",
         notNull = false,
         length = "17",
         remark = "白名单剩余额度",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal whiteLeaveQuota;

      public BigDecimal getWhiteTotalQuota() {
         return this.whiteTotalQuota;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public BigDecimal getWhiteDistributeQuota() {
         return this.whiteDistributeQuota;
      }

      public String getDocumentType() {
         return this.documentType;
      }

      public String getDocumentId() {
         return this.documentId;
      }

      public BigDecimal getWhiteLeaveQuota() {
         return this.whiteLeaveQuota;
      }

      public void setWhiteTotalQuota(BigDecimal whiteTotalQuota) {
         this.whiteTotalQuota = whiteTotalQuota;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setWhiteDistributeQuota(BigDecimal whiteDistributeQuota) {
         this.whiteDistributeQuota = whiteDistributeQuota;
      }

      public void setDocumentType(String documentType) {
         this.documentType = documentType;
      }

      public void setDocumentId(String documentId) {
         this.documentId = documentId;
      }

      public void setWhiteLeaveQuota(BigDecimal whiteLeaveQuota) {
         this.whiteLeaveQuota = whiteLeaveQuota;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100218Out.ClientListArray)) {
            return false;
         } else {
            Core1400100218Out.ClientListArray other = (Core1400100218Out.ClientListArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$whiteTotalQuota = this.getWhiteTotalQuota();
               Object other$whiteTotalQuota = other.getWhiteTotalQuota();
               if (this$whiteTotalQuota == null) {
                  if (other$whiteTotalQuota != null) {
                     return false;
                  }
               } else if (!this$whiteTotalQuota.equals(other$whiteTotalQuota)) {
                  return false;
               }

               Object this$clientNo = this.getClientNo();
               Object other$clientNo = other.getClientNo();
               if (this$clientNo == null) {
                  if (other$clientNo != null) {
                     return false;
                  }
               } else if (!this$clientNo.equals(other$clientNo)) {
                  return false;
               }

               Object this$whiteDistributeQuota = this.getWhiteDistributeQuota();
               Object other$whiteDistributeQuota = other.getWhiteDistributeQuota();
               if (this$whiteDistributeQuota == null) {
                  if (other$whiteDistributeQuota != null) {
                     return false;
                  }
               } else if (!this$whiteDistributeQuota.equals(other$whiteDistributeQuota)) {
                  return false;
               }

               label62: {
                  Object this$documentType = this.getDocumentType();
                  Object other$documentType = other.getDocumentType();
                  if (this$documentType == null) {
                     if (other$documentType == null) {
                        break label62;
                     }
                  } else if (this$documentType.equals(other$documentType)) {
                     break label62;
                  }

                  return false;
               }

               label55: {
                  Object this$documentId = this.getDocumentId();
                  Object other$documentId = other.getDocumentId();
                  if (this$documentId == null) {
                     if (other$documentId == null) {
                        break label55;
                     }
                  } else if (this$documentId.equals(other$documentId)) {
                     break label55;
                  }

                  return false;
               }

               Object this$whiteLeaveQuota = this.getWhiteLeaveQuota();
               Object other$whiteLeaveQuota = other.getWhiteLeaveQuota();
               if (this$whiteLeaveQuota == null) {
                  if (other$whiteLeaveQuota != null) {
                     return false;
                  }
               } else if (!this$whiteLeaveQuota.equals(other$whiteLeaveQuota)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100218Out.ClientListArray;
      }
      public String toString() {
         return "Core1400100218Out.ClientListArray(whiteTotalQuota=" + this.getWhiteTotalQuota() + ", clientNo=" + this.getClientNo() + ", whiteDistributeQuota=" + this.getWhiteDistributeQuota() + ", documentType=" + this.getDocumentType() + ", documentId=" + this.getDocumentId() + ", whiteLeaveQuota=" + this.getWhiteLeaveQuota() + ")";
      }
   }
}
