package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@MessageIn
public class Core1200100216In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1200100216In.Body body;

   public Core1200100216In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1200100216In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1200100216In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200100216In)) {
         return false;
      } else {
         Core1200100216In other = (Core1200100216In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200100216In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "发行年度",
         notNull = true,
         length = "5",
         remark = "发行年度",
         maxSize = 5
      )
      private String issueYear;
      @V(
         desc = "币种",
         notNull = true,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "审批单号",
         notNull = false,
         length = "50",
         remark = "审批单号",
         maxSize = 50
      )
      private String approvalNo;
      @V(
         desc = "数组",
         notNull = true,
         remark = "数组"
      )
      private List<Core1200100216In.Body.DevLimitArray> devLimitArray;

      public String getProdType() {
         return this.prodType;
      }

      public String getIssueYear() {
         return this.issueYear;
      }

      public String getCcy() {
         return this.ccy;
      }

      public String getApprovalNo() {
         return this.approvalNo;
      }

      public List<Core1200100216In.Body.DevLimitArray> getDevLimitArray() {
         return this.devLimitArray;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setIssueYear(String issueYear) {
         this.issueYear = issueYear;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setApprovalNo(String approvalNo) {
         this.approvalNo = approvalNo;
      }

      public void setDevLimitArray(List<Core1200100216In.Body.DevLimitArray> devLimitArray) {
         this.devLimitArray = devLimitArray;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1200100216In.Body)) {
            return false;
         } else {
            Core1200100216In.Body other = (Core1200100216In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label71: {
                  Object this$prodType = this.getProdType();
                  Object other$prodType = other.getProdType();
                  if (this$prodType == null) {
                     if (other$prodType == null) {
                        break label71;
                     }
                  } else if (this$prodType.equals(other$prodType)) {
                     break label71;
                  }

                  return false;
               }

               Object this$issueYear = this.getIssueYear();
               Object other$issueYear = other.getIssueYear();
               if (this$issueYear == null) {
                  if (other$issueYear != null) {
                     return false;
                  }
               } else if (!this$issueYear.equals(other$issueYear)) {
                  return false;
               }

               label57: {
                  Object this$ccy = this.getCcy();
                  Object other$ccy = other.getCcy();
                  if (this$ccy == null) {
                     if (other$ccy == null) {
                        break label57;
                     }
                  } else if (this$ccy.equals(other$ccy)) {
                     break label57;
                  }

                  return false;
               }

               Object this$approvalNo = this.getApprovalNo();
               Object other$approvalNo = other.getApprovalNo();
               if (this$approvalNo == null) {
                  if (other$approvalNo != null) {
                     return false;
                  }
               } else if (!this$approvalNo.equals(other$approvalNo)) {
                  return false;
               }

               Object this$devLimitArray = this.getDevLimitArray();
               Object other$devLimitArray = other.getDevLimitArray();
               if (this$devLimitArray == null) {
                  if (other$devLimitArray == null) {
                     return true;
                  }
               } else if (this$devLimitArray.equals(other$devLimitArray)) {
                  return true;
               }

               return false;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1200100216In.Body;
      }
      public String toString() {
         return "Core1200100216In.Body(prodType=" + this.getProdType() + ", issueYear=" + this.getIssueYear() + ", ccy=" + this.getCcy() + ", approvalNo=" + this.getApprovalNo() + ", devLimitArray=" + this.getDevLimitArray() + ")";
      }

      public static class DevLimitArray {
         @V(
            desc = "本机构发行额度",
            notNull = true,
            length = "17",
            remark = "本机构发行额度",
            decimalLength = 2,
            precision = 2
         )
         private BigDecimal branchTotalLimit;
         @V(
            desc = "本机构可用额度额度",
            notNull = false,
            length = "17",
            remark = "本机构可用额度额度",
            decimalLength = 2,
            precision = 2
         )
         private BigDecimal branchLeaveLimit;
         @V(
            desc = "所属机构号",
            notNull = true,
            length = "50",
            remark = "机构代码",
            maxSize = 50
         )
         private String branch;
         @V(
            desc = "变更操作方式",
            notNull = false,
            length = "2",
            inDesc = "SC-单笔,BC-批量,00-签发录入,01-复核,02-兑付,03-退回,04-挂失,05-解挂,06-签发修改,07-签发删除,11-复核,12-核对,13-移存,14-退回申请,15-挂失,16-解挂",
            remark = "变更操作方式",
            maxSize = 2
         )
         private String operateType;

         public BigDecimal getBranchTotalLimit() {
            return this.branchTotalLimit;
         }

         public BigDecimal getBranchLeaveLimit() {
            return this.branchLeaveLimit;
         }

         public String getBranch() {
            return this.branch;
         }

         public String getOperateType() {
            return this.operateType;
         }

         public void setBranchTotalLimit(BigDecimal branchTotalLimit) {
            this.branchTotalLimit = branchTotalLimit;
         }

         public void setBranchLeaveLimit(BigDecimal branchLeaveLimit) {
            this.branchLeaveLimit = branchLeaveLimit;
         }

         public void setBranch(String branch) {
            this.branch = branch;
         }

         public void setOperateType(String operateType) {
            this.operateType = operateType;
         }

         public boolean equals(Object o) {
            if (o == this) {
               return true;
            } else if (!(o instanceof Core1200100216In.Body.DevLimitArray)) {
               return false;
            } else {
               Core1200100216In.Body.DevLimitArray other = (Core1200100216In.Body.DevLimitArray)o;
               if (!other.canEqual(this)) {
                  return false;
               } else {
                  label59: {
                     Object this$branchTotalLimit = this.getBranchTotalLimit();
                     Object other$branchTotalLimit = other.getBranchTotalLimit();
                     if (this$branchTotalLimit == null) {
                        if (other$branchTotalLimit == null) {
                           break label59;
                        }
                     } else if (this$branchTotalLimit.equals(other$branchTotalLimit)) {
                        break label59;
                     }

                     return false;
                  }

                  Object this$branchLeaveLimit = this.getBranchLeaveLimit();
                  Object other$branchLeaveLimit = other.getBranchLeaveLimit();
                  if (this$branchLeaveLimit == null) {
                     if (other$branchLeaveLimit != null) {
                        return false;
                     }
                  } else if (!this$branchLeaveLimit.equals(other$branchLeaveLimit)) {
                     return false;
                  }

                  Object this$branch = this.getBranch();
                  Object other$branch = other.getBranch();
                  if (this$branch == null) {
                     if (other$branch != null) {
                        return false;
                     }
                  } else if (!this$branch.equals(other$branch)) {
                     return false;
                  }

                  Object this$operateType = this.getOperateType();
                  Object other$operateType = other.getOperateType();
                  if (this$operateType == null) {
                     if (other$operateType != null) {
                        return false;
                     }
                  } else if (!this$operateType.equals(other$operateType)) {
                     return false;
                  }

                  return true;
               }
            }
         }

         protected boolean canEqual(Object other) {
            return other instanceof Core1200100216In.Body.DevLimitArray;
         }
         public String toString() {
            return "Core1200100216In.Body.DevLimitArray(branchTotalLimit=" + this.getBranchTotalLimit() + ", branchLeaveLimit=" + this.getBranchLeaveLimit() + ", branch=" + this.getBranch() + ", operateType=" + this.getOperateType() + ")";
         }
      }
   }
}
