package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12200112In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12200112Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12200112 {
   String URL = "/rb/file/client/batch/change";


   @ApiRemark("账户客户批量变更")
   @ApiDesc("用于批量处理客户账户的变更")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1220",
      messageCode = "0112"
   )
   @BusinessCategory("1220-文件")
   @FunctionCategory("RB02-账户管理")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core12200112Out runService(Core12200112In var1);
}
