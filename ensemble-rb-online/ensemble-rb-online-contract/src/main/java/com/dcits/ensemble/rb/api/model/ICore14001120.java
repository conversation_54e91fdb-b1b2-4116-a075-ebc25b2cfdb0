package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14001120In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14001120Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14001120 {
   String URL = "/rb/inq/voucher/court";


   @ApiRemark("标准优化")
   @ApiDesc("法院处理查询")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "1120"
   )
   @BusinessCategory("1400")
   @FunctionCategory("RB06-凭证处理")
   @ConsumeSys("CIT/TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core14001120Out runService(Core14001120In var1);
}
