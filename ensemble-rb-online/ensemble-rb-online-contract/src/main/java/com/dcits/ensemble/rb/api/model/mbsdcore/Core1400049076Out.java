package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400049076Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400049076Out.FeePackageArray> feePackageArray;

   public List<Core1400049076Out.FeePackageArray> getFeePackageArray() {
      return this.feePackageArray;
   }

   public void setFeePackageArray(List<Core1400049076Out.FeePackageArray> feePackageArray) {
      this.feePackageArray = feePackageArray;
   }

   public String toString() {
      return "Core1400049076Out(feePackageArray=" + this.getFeePackageArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400049076Out)) {
         return false;
      } else {
         Core1400049076Out other = (Core1400049076Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$feePackageArray = this.getFeePackageArray();
            Object other$feePackageArray = other.getFeePackageArray();
            if (this$feePackageArray == null) {
               if (other$feePackageArray != null) {
                  return false;
               }
            } else if (!this$feePackageArray.equals(other$feePackageArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400049076Out;
   }
   public static class FeePackageArray {
      @V(
         desc = "证件类型",
         notNull = false,
         length = "3",
         remark = "证件类型",
         maxSize = 3
      )
      private String documentType;
      @V(
         desc = "证件号码",
         notNull = false,
         length = "50",
         remark = "证件号码",
         maxSize = 50
      )
      private String documentId;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "可用余额",
         notNull = false,
         length = "17",
         remark = "可用余额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal availableAmt;
      @V(
         desc = "生效日期",
         notNull = false,
         remark = "生效日期"
      )
      private String effectDate;
      @V(
         desc = "结束日期",
         notNull = false,
         remark = "结束日期"
      )
      private String endDate;
      @V(
         desc = "协议编号",
         notNull = false,
         length = "50",
         remark = "协议编号",
         maxSize = 50
      )
      private String agreementId;
      @V(
         desc = "套餐币种取自",
         notNull = false,
         length = "3",
         remark = "套餐币种取自",
         maxSize = 3
      )
      private String packageCcy;
      @V(
         desc = "可抵扣金额",
         notNull = false,
         length = "17",
         remark = "可抵扣金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal packageAmt;
      @V(
         desc = "优惠金额",
         notNull = false,
         length = "17",
         remark = "优惠金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal discountAmount;
      @V(
         desc = "结算金额",
         notNull = false,
         length = "17",
         remark = "结算金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal settleAmt;
      @V(
         desc = "已使用金额",
         notNull = false,
         length = "17",
         remark = "已使用金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal useAmt;
      @V(
         desc = "套餐频率",
         notNull = false,
         length = "5",
         remark = "套餐频率取自",
         maxSize = 5
      )
      private String packagePeriodFreq;
      @V(
         desc = "套餐类型",
         notNull = false,
         length = "3",
         in = "NUM,AMT,ALL",
         remark = "套餐类型",
         maxSize = 3
      )
      private String packageType;
      @V(
         desc = "收费账号",
         notNull = false,
         length = "50",
         remark = "收费账号",
         maxSize = 50
      )
      private String chargeToBaseAcctNo;
      @V(
         desc = "套餐代码",
         notNull = false,
         length = "50",
         remark = "套餐代码",
         maxSize = 50
      )
      private String packageId;
      @V(
         desc = "套餐描述",
         notNull = false,
         length = "50",
         remark = "套餐描述",
         maxSize = 50
      )
      private String packageDesc;
      @V(
         desc = "可抵扣笔数",
         notNull = false,
         length = "5",
         remark = "可抵扣笔数"
      )
      private Integer packageNum;
      @V(
         desc = "协议状态",
         notNull = false,
         length = "2",
         in = "A,E",
         remark = "普通协议使用，可应用于大部分场景，贷款模块用于资产证券化合同状态",
         maxSize = 2
      )
      private String agreementStatus;
      @V(
         desc = "已使用笔数",
         notNull = false,
         length = "5",
         remark = "已使用笔数"
      )
      private Integer tranNum;
      @V(
         desc = "可用抵扣次数",
         notNull = false,
         length = "5",
         remark = "可用抵扣次数"
      )
      private Integer availNum;
      @V(
         desc = "法人",
         notNull = false,
         length = "20",
         remark = "法人",
         maxSize = 20
      )
      private String company;

      public String getDocumentType() {
         return this.documentType;
      }

      public String getDocumentId() {
         return this.documentId;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public BigDecimal getAvailableAmt() {
         return this.availableAmt;
      }

      public String getEffectDate() {
         return this.effectDate;
      }

      public String getEndDate() {
         return this.endDate;
      }

      public String getAgreementId() {
         return this.agreementId;
      }

      public String getPackageCcy() {
         return this.packageCcy;
      }

      public BigDecimal getPackageAmt() {
         return this.packageAmt;
      }

      public BigDecimal getDiscountAmount() {
         return this.discountAmount;
      }

      public BigDecimal getSettleAmt() {
         return this.settleAmt;
      }

      public BigDecimal getUseAmt() {
         return this.useAmt;
      }

      public String getPackagePeriodFreq() {
         return this.packagePeriodFreq;
      }

      public String getPackageType() {
         return this.packageType;
      }

      public String getChargeToBaseAcctNo() {
         return this.chargeToBaseAcctNo;
      }

      public String getPackageId() {
         return this.packageId;
      }

      public String getPackageDesc() {
         return this.packageDesc;
      }

      public Integer getPackageNum() {
         return this.packageNum;
      }

      public String getAgreementStatus() {
         return this.agreementStatus;
      }

      public Integer getTranNum() {
         return this.tranNum;
      }

      public Integer getAvailNum() {
         return this.availNum;
      }

      public String getCompany() {
         return this.company;
      }

      public void setDocumentType(String documentType) {
         this.documentType = documentType;
      }

      public void setDocumentId(String documentId) {
         this.documentId = documentId;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setAvailableAmt(BigDecimal availableAmt) {
         this.availableAmt = availableAmt;
      }

      public void setEffectDate(String effectDate) {
         this.effectDate = effectDate;
      }

      public void setEndDate(String endDate) {
         this.endDate = endDate;
      }

      public void setAgreementId(String agreementId) {
         this.agreementId = agreementId;
      }

      public void setPackageCcy(String packageCcy) {
         this.packageCcy = packageCcy;
      }

      public void setPackageAmt(BigDecimal packageAmt) {
         this.packageAmt = packageAmt;
      }

      public void setDiscountAmount(BigDecimal discountAmount) {
         this.discountAmount = discountAmount;
      }

      public void setSettleAmt(BigDecimal settleAmt) {
         this.settleAmt = settleAmt;
      }

      public void setUseAmt(BigDecimal useAmt) {
         this.useAmt = useAmt;
      }

      public void setPackagePeriodFreq(String packagePeriodFreq) {
         this.packagePeriodFreq = packagePeriodFreq;
      }

      public void setPackageType(String packageType) {
         this.packageType = packageType;
      }

      public void setChargeToBaseAcctNo(String chargeToBaseAcctNo) {
         this.chargeToBaseAcctNo = chargeToBaseAcctNo;
      }

      public void setPackageId(String packageId) {
         this.packageId = packageId;
      }

      public void setPackageDesc(String packageDesc) {
         this.packageDesc = packageDesc;
      }

      public void setPackageNum(Integer packageNum) {
         this.packageNum = packageNum;
      }

      public void setAgreementStatus(String agreementStatus) {
         this.agreementStatus = agreementStatus;
      }

      public void setTranNum(Integer tranNum) {
         this.tranNum = tranNum;
      }

      public void setAvailNum(Integer availNum) {
         this.availNum = availNum;
      }

      public void setCompany(String company) {
         this.company = company;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400049076Out.FeePackageArray)) {
            return false;
         } else {
            Core1400049076Out.FeePackageArray other = (Core1400049076Out.FeePackageArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$documentType = this.getDocumentType();
               Object other$documentType = other.getDocumentType();
               if (this$documentType == null) {
                  if (other$documentType != null) {
                     return false;
                  }
               } else if (!this$documentType.equals(other$documentType)) {
                  return false;
               }

               Object this$documentId = this.getDocumentId();
               Object other$documentId = other.getDocumentId();
               if (this$documentId == null) {
                  if (other$documentId != null) {
                     return false;
                  }
               } else if (!this$documentId.equals(other$documentId)) {
                  return false;
               }

               Object this$clientNo = this.getClientNo();
               Object other$clientNo = other.getClientNo();
               if (this$clientNo == null) {
                  if (other$clientNo != null) {
                     return false;
                  }
               } else if (!this$clientNo.equals(other$clientNo)) {
                  return false;
               }

               label254: {
                  Object this$availableAmt = this.getAvailableAmt();
                  Object other$availableAmt = other.getAvailableAmt();
                  if (this$availableAmt == null) {
                     if (other$availableAmt == null) {
                        break label254;
                     }
                  } else if (this$availableAmt.equals(other$availableAmt)) {
                     break label254;
                  }

                  return false;
               }

               label247: {
                  Object this$effectDate = this.getEffectDate();
                  Object other$effectDate = other.getEffectDate();
                  if (this$effectDate == null) {
                     if (other$effectDate == null) {
                        break label247;
                     }
                  } else if (this$effectDate.equals(other$effectDate)) {
                     break label247;
                  }

                  return false;
               }

               Object this$endDate = this.getEndDate();
               Object other$endDate = other.getEndDate();
               if (this$endDate == null) {
                  if (other$endDate != null) {
                     return false;
                  }
               } else if (!this$endDate.equals(other$endDate)) {
                  return false;
               }

               label233: {
                  Object this$agreementId = this.getAgreementId();
                  Object other$agreementId = other.getAgreementId();
                  if (this$agreementId == null) {
                     if (other$agreementId == null) {
                        break label233;
                     }
                  } else if (this$agreementId.equals(other$agreementId)) {
                     break label233;
                  }

                  return false;
               }

               label226: {
                  Object this$packageCcy = this.getPackageCcy();
                  Object other$packageCcy = other.getPackageCcy();
                  if (this$packageCcy == null) {
                     if (other$packageCcy == null) {
                        break label226;
                     }
                  } else if (this$packageCcy.equals(other$packageCcy)) {
                     break label226;
                  }

                  return false;
               }

               Object this$packageAmt = this.getPackageAmt();
               Object other$packageAmt = other.getPackageAmt();
               if (this$packageAmt == null) {
                  if (other$packageAmt != null) {
                     return false;
                  }
               } else if (!this$packageAmt.equals(other$packageAmt)) {
                  return false;
               }

               Object this$discountAmount = this.getDiscountAmount();
               Object other$discountAmount = other.getDiscountAmount();
               if (this$discountAmount == null) {
                  if (other$discountAmount != null) {
                     return false;
                  }
               } else if (!this$discountAmount.equals(other$discountAmount)) {
                  return false;
               }

               label205: {
                  Object this$settleAmt = this.getSettleAmt();
                  Object other$settleAmt = other.getSettleAmt();
                  if (this$settleAmt == null) {
                     if (other$settleAmt == null) {
                        break label205;
                     }
                  } else if (this$settleAmt.equals(other$settleAmt)) {
                     break label205;
                  }

                  return false;
               }

               label198: {
                  Object this$useAmt = this.getUseAmt();
                  Object other$useAmt = other.getUseAmt();
                  if (this$useAmt == null) {
                     if (other$useAmt == null) {
                        break label198;
                     }
                  } else if (this$useAmt.equals(other$useAmt)) {
                     break label198;
                  }

                  return false;
               }

               Object this$packagePeriodFreq = this.getPackagePeriodFreq();
               Object other$packagePeriodFreq = other.getPackagePeriodFreq();
               if (this$packagePeriodFreq == null) {
                  if (other$packagePeriodFreq != null) {
                     return false;
                  }
               } else if (!this$packagePeriodFreq.equals(other$packagePeriodFreq)) {
                  return false;
               }

               label184: {
                  Object this$packageType = this.getPackageType();
                  Object other$packageType = other.getPackageType();
                  if (this$packageType == null) {
                     if (other$packageType == null) {
                        break label184;
                     }
                  } else if (this$packageType.equals(other$packageType)) {
                     break label184;
                  }

                  return false;
               }

               Object this$chargeToBaseAcctNo = this.getChargeToBaseAcctNo();
               Object other$chargeToBaseAcctNo = other.getChargeToBaseAcctNo();
               if (this$chargeToBaseAcctNo == null) {
                  if (other$chargeToBaseAcctNo != null) {
                     return false;
                  }
               } else if (!this$chargeToBaseAcctNo.equals(other$chargeToBaseAcctNo)) {
                  return false;
               }

               label170: {
                  Object this$packageId = this.getPackageId();
                  Object other$packageId = other.getPackageId();
                  if (this$packageId == null) {
                     if (other$packageId == null) {
                        break label170;
                     }
                  } else if (this$packageId.equals(other$packageId)) {
                     break label170;
                  }

                  return false;
               }

               Object this$packageDesc = this.getPackageDesc();
               Object other$packageDesc = other.getPackageDesc();
               if (this$packageDesc == null) {
                  if (other$packageDesc != null) {
                     return false;
                  }
               } else if (!this$packageDesc.equals(other$packageDesc)) {
                  return false;
               }

               Object this$packageNum = this.getPackageNum();
               Object other$packageNum = other.getPackageNum();
               if (this$packageNum == null) {
                  if (other$packageNum != null) {
                     return false;
                  }
               } else if (!this$packageNum.equals(other$packageNum)) {
                  return false;
               }

               Object this$agreementStatus = this.getAgreementStatus();
               Object other$agreementStatus = other.getAgreementStatus();
               if (this$agreementStatus == null) {
                  if (other$agreementStatus != null) {
                     return false;
                  }
               } else if (!this$agreementStatus.equals(other$agreementStatus)) {
                  return false;
               }

               label142: {
                  Object this$tranNum = this.getTranNum();
                  Object other$tranNum = other.getTranNum();
                  if (this$tranNum == null) {
                     if (other$tranNum == null) {
                        break label142;
                     }
                  } else if (this$tranNum.equals(other$tranNum)) {
                     break label142;
                  }

                  return false;
               }

               label135: {
                  Object this$availNum = this.getAvailNum();
                  Object other$availNum = other.getAvailNum();
                  if (this$availNum == null) {
                     if (other$availNum == null) {
                        break label135;
                     }
                  } else if (this$availNum.equals(other$availNum)) {
                     break label135;
                  }

                  return false;
               }

               Object this$company = this.getCompany();
               Object other$company = other.getCompany();
               if (this$company == null) {
                  if (other$company != null) {
                     return false;
                  }
               } else if (!this$company.equals(other$company)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400049076Out.FeePackageArray;
      }
      public String toString() {
         return "Core1400049076Out.FeePackageArray(documentType=" + this.getDocumentType() + ", documentId=" + this.getDocumentId() + ", clientNo=" + this.getClientNo() + ", availableAmt=" + this.getAvailableAmt() + ", effectDate=" + this.getEffectDate() + ", endDate=" + this.getEndDate() + ", agreementId=" + this.getAgreementId() + ", packageCcy=" + this.getPackageCcy() + ", packageAmt=" + this.getPackageAmt() + ", discountAmount=" + this.getDiscountAmount() + ", settleAmt=" + this.getSettleAmt() + ", useAmt=" + this.getUseAmt() + ", packagePeriodFreq=" + this.getPackagePeriodFreq() + ", packageType=" + this.getPackageType() + ", chargeToBaseAcctNo=" + this.getChargeToBaseAcctNo() + ", packageId=" + this.getPackageId() + ", packageDesc=" + this.getPackageDesc() + ", packageNum=" + this.getPackageNum() + ", agreementStatus=" + this.getAgreementStatus() + ", tranNum=" + this.getTranNum() + ", availNum=" + this.getAvailNum() + ", company=" + this.getCompany() + ")";
      }
   }
}
