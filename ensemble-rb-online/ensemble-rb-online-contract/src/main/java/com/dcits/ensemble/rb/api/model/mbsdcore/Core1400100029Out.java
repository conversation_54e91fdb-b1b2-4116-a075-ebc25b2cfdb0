package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400100029Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100029Out.RunRuleBefore> runRuleBefore;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100029Out.StageCodeInfoArray> stageCodeInfoArray;

   public List<Core1400100029Out.RunRuleBefore> getRunRuleBefore() {
      return this.runRuleBefore;
   }

   public List<Core1400100029Out.StageCodeInfoArray> getStageCodeInfoArray() {
      return this.stageCodeInfoArray;
   }

   public void setRunRuleBefore(List<Core1400100029Out.RunRuleBefore> runRuleBefore) {
      this.runRuleBefore = runRuleBefore;
   }

   public void setStageCodeInfoArray(List<Core1400100029Out.StageCodeInfoArray> stageCodeInfoArray) {
      this.stageCodeInfoArray = stageCodeInfoArray;
   }

   public String toString() {
      return "Core1400100029Out(runRuleBefore=" + this.getRunRuleBefore() + ", stageCodeInfoArray=" + this.getStageCodeInfoArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100029Out)) {
         return false;
      } else {
         Core1400100029Out other = (Core1400100029Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$runRuleBefore = this.getRunRuleBefore();
            Object other$runRuleBefore = other.getRunRuleBefore();
            if (this$runRuleBefore == null) {
               if (other$runRuleBefore != null) {
                  return false;
               }
            } else if (!this$runRuleBefore.equals(other$runRuleBefore)) {
               return false;
            }

            Object this$stageCodeInfoArray = this.getStageCodeInfoArray();
            Object other$stageCodeInfoArray = other.getStageCodeInfoArray();
            if (this$stageCodeInfoArray == null) {
               if (other$stageCodeInfoArray != null) {
                  return false;
               }
            } else if (!this$stageCodeInfoArray.equals(other$stageCodeInfoArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100029Out;
   }
   public static class StageCodeInfoArray {
      @V(
         desc = "基础产品",
         notNull = false,
         length = "20",
         remark = "基础产品",
         maxSize = 20
      )
      private String baseProdType;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "期次代码",
         notNull = false,
         length = "50",
         remark = "期次代码",
         maxSize = 50
      )
      private String stageCode;
      @V(
         desc = "期次描述",
         notNull = false,
         length = "200",
         remark = "期次描述",
         maxSize = 200
      )
      private String stageCodeDesc;
      @V(
         desc = "期次发行金额",
         notNull = false,
         length = "17",
         remark = "期次发行金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal issueAmt;
      @V(
         desc = "可用额度",
         notNull = false,
         length = "17",
         remark = "可用额度",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal availableLimit;
      @V(
         desc = "期次起存金额",
         notNull = false,
         length = "17",
         remark = "期次起存金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal stageMinAmt;
      @V(
         desc = "最小留存金额",
         notNull = false,
         length = "17",
         remark = "最小留存金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal keepMinBal;
      @V(
         desc = "单笔认购最大金额",
         notNull = false,
         length = "17",
         remark = "单笔认购最大金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal sgMaxAmt;
      @V(
         desc = "开始日期",
         notNull = false,
         remark = "开始日期"
      )
      private String startDate;
      @V(
         desc = "起售时间",
         notNull = false,
         length = "26",
         remark = "起售时间",
         maxSize = 26
      )
      private String saleStartTime;
      @V(
         desc = "结束日期",
         notNull = false,
         remark = "结束日期"
      )
      private String endDate;
      @V(
         desc = "止售时间",
         notNull = false,
         length = "26",
         remark = "止售时间",
         maxSize = 26
      )
      private String saleEndTime;
      @V(
         desc = "付息方式",
         notNull = false,
         length = "3",
         remark = "付息方式",
         maxSize = 3
      )
      private String payIntType;
      @V(
         desc = "自动续存资金来源账户付息频率",
         notNull = false,
         length = "5",
         remark = "自动续存资金来源账户付息频率",
         maxSize = 5
      )
      private String aupCycle;
      @V(
         desc = "是否允许提前支取",
         notNull = false,
         length = "1",
         remark = "是否允许提前支取",
         maxSize = 1
      )
      private String preWithdrawFlag;
      @V(
         desc = "提前支取次数",
         notNull = false,
         length = "5",
         remark = "提前支取次数"
      )
      private Integer preWithdrawNum;
      @V(
         desc = "存期期限",
         notNull = false,
         length = "5",
         remark = "期限",
         maxSize = 5
      )
      private String term;
      @V(
         desc = "利率类型",
         notNull = false,
         length = "5",
         remark = "利率类型",
         maxSize = 5
      )
      private String intType;
      @V(
         desc = "浮动利率",
         notNull = false,
         length = "15",
         remark = "浮动利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal floatRate;
      @V(
         desc = "执行利率",
         notNull = false,
         length = "15",
         remark = "执行利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal realRate;
      @V(
         desc = "是否全额转让",
         notNull = false,
         length = "1",
         remark = "定期转让是否全额转让 Y -是 N -否",
         maxSize = 1
      )
      private String isFullTransfer;
      @V(
         desc = "是否可赎回",
         notNull = false,
         length = "1",
         remark = "是否可赎回",
         maxSize = 1
      )
      private String redemptionFlag;
      @V(
         desc = "汇率类型",
         notNull = false,
         length = "10",
         remark = "汇率类型",
         maxSize = 10
      )
      private String rateType;
      @V(
         desc = "赎回利率",
         notNull = false,
         length = "15",
         remark = "赎回利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal tohonorRate;
      @V(
         desc = "到期本金在宽限期是否收息",
         notNull = false,
         length = "1",
         remark = "到期本金在宽限期是否收息",
         maxSize = 1
      )
      private String graceChargeIntFlag;
      @V(
         desc = "是否扣划利息标志",
         notNull = false,
         length = "1",
         remark = "是否扣划利息标志",
         maxSize = 1
      )
      private String intFlag;
      @V(
         desc = "结息日期",
         notNull = false,
         remark = "结息日期"
      )
      private String captDate;
      @V(
         desc = "产品起息日",
         notNull = false,
         length = "10",
         remark = "产品起息日",
         maxSize = 10
      )
      private String prodBeginDate;
      @V(
         desc = "转入手续费",
         notNull = false,
         length = "17",
         remark = "转入手续费",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal inFee;
      @V(
         desc = "转出费用",
         notNull = false,
         length = "17",
         remark = "转出费用",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal trfOutFeeAmt;
      @V(
         desc = "资金来源是否基于内部户",
         notNull = false,
         length = "2",
         remark = "资金来源是否基于内部户",
         maxSize = 2
      )
      private String isCashFromInnerAcct;
      @V(
         desc = "境内境外标志",
         notNull = false,
         length = "1",
         remark = "境内境外标志",
         maxSize = 1
      )
      private String inlandOffshore;
      @V(
         desc = "赎回日期",
         notNull = false,
         remark = "赎回日期"
      )
      private String redeemDate;
      @V(
         desc = "审批单号",
         notNull = false,
         length = "50",
         remark = "审批单号",
         maxSize = 50
      )
      private String approvalNo;
      @V(
         desc = "可售渠道",
         notNull = false,
         length = "100",
         remark = "可售渠道",
         maxSize = 100
      )
      private String onSaleChannel;
      @V(
         desc = "赎回利率类型",
         notNull = false,
         length = "5",
         remark = "赎回利率类型",
         maxSize = 5
      )
      private String redemptionIntType;
      @V(
         desc = "最小变动额",
         notNull = false,
         length = "17",
         remark = "最小变动额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal minChangeBalance;
      @V(
         desc = "可售分行",
         notNull = false,
         length = "20",
         remark = "可售分行",
         maxSize = 20
      )
      private String availSaleSubBranch;

      public String getBaseProdType() {
         return this.baseProdType;
      }

      public String getCcy() {
         return this.ccy;
      }

      public String getStageCode() {
         return this.stageCode;
      }

      public String getStageCodeDesc() {
         return this.stageCodeDesc;
      }

      public BigDecimal getIssueAmt() {
         return this.issueAmt;
      }

      public BigDecimal getAvailableLimit() {
         return this.availableLimit;
      }

      public BigDecimal getStageMinAmt() {
         return this.stageMinAmt;
      }

      public BigDecimal getKeepMinBal() {
         return this.keepMinBal;
      }

      public BigDecimal getSgMaxAmt() {
         return this.sgMaxAmt;
      }

      public String getStartDate() {
         return this.startDate;
      }

      public String getSaleStartTime() {
         return this.saleStartTime;
      }

      public String getEndDate() {
         return this.endDate;
      }

      public String getSaleEndTime() {
         return this.saleEndTime;
      }

      public String getPayIntType() {
         return this.payIntType;
      }

      public String getAupCycle() {
         return this.aupCycle;
      }

      public String getPreWithdrawFlag() {
         return this.preWithdrawFlag;
      }

      public Integer getPreWithdrawNum() {
         return this.preWithdrawNum;
      }

      public String getTerm() {
         return this.term;
      }

      public String getIntType() {
         return this.intType;
      }

      public BigDecimal getFloatRate() {
         return this.floatRate;
      }

      public BigDecimal getRealRate() {
         return this.realRate;
      }

      public String getIsFullTransfer() {
         return this.isFullTransfer;
      }

      public String getRedemptionFlag() {
         return this.redemptionFlag;
      }

      public String getRateType() {
         return this.rateType;
      }

      public BigDecimal getTohonorRate() {
         return this.tohonorRate;
      }

      public String getGraceChargeIntFlag() {
         return this.graceChargeIntFlag;
      }

      public String getIntFlag() {
         return this.intFlag;
      }

      public String getCaptDate() {
         return this.captDate;
      }

      public String getProdBeginDate() {
         return this.prodBeginDate;
      }

      public BigDecimal getInFee() {
         return this.inFee;
      }

      public BigDecimal getTrfOutFeeAmt() {
         return this.trfOutFeeAmt;
      }

      public String getIsCashFromInnerAcct() {
         return this.isCashFromInnerAcct;
      }

      public String getInlandOffshore() {
         return this.inlandOffshore;
      }

      public String getRedeemDate() {
         return this.redeemDate;
      }

      public String getApprovalNo() {
         return this.approvalNo;
      }

      public String getOnSaleChannel() {
         return this.onSaleChannel;
      }

      public String getRedemptionIntType() {
         return this.redemptionIntType;
      }

      public BigDecimal getMinChangeBalance() {
         return this.minChangeBalance;
      }

      public String getAvailSaleSubBranch() {
         return this.availSaleSubBranch;
      }

      public void setBaseProdType(String baseProdType) {
         this.baseProdType = baseProdType;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setStageCode(String stageCode) {
         this.stageCode = stageCode;
      }

      public void setStageCodeDesc(String stageCodeDesc) {
         this.stageCodeDesc = stageCodeDesc;
      }

      public void setIssueAmt(BigDecimal issueAmt) {
         this.issueAmt = issueAmt;
      }

      public void setAvailableLimit(BigDecimal availableLimit) {
         this.availableLimit = availableLimit;
      }

      public void setStageMinAmt(BigDecimal stageMinAmt) {
         this.stageMinAmt = stageMinAmt;
      }

      public void setKeepMinBal(BigDecimal keepMinBal) {
         this.keepMinBal = keepMinBal;
      }

      public void setSgMaxAmt(BigDecimal sgMaxAmt) {
         this.sgMaxAmt = sgMaxAmt;
      }

      public void setStartDate(String startDate) {
         this.startDate = startDate;
      }

      public void setSaleStartTime(String saleStartTime) {
         this.saleStartTime = saleStartTime;
      }

      public void setEndDate(String endDate) {
         this.endDate = endDate;
      }

      public void setSaleEndTime(String saleEndTime) {
         this.saleEndTime = saleEndTime;
      }

      public void setPayIntType(String payIntType) {
         this.payIntType = payIntType;
      }

      public void setAupCycle(String aupCycle) {
         this.aupCycle = aupCycle;
      }

      public void setPreWithdrawFlag(String preWithdrawFlag) {
         this.preWithdrawFlag = preWithdrawFlag;
      }

      public void setPreWithdrawNum(Integer preWithdrawNum) {
         this.preWithdrawNum = preWithdrawNum;
      }

      public void setTerm(String term) {
         this.term = term;
      }

      public void setIntType(String intType) {
         this.intType = intType;
      }

      public void setFloatRate(BigDecimal floatRate) {
         this.floatRate = floatRate;
      }

      public void setRealRate(BigDecimal realRate) {
         this.realRate = realRate;
      }

      public void setIsFullTransfer(String isFullTransfer) {
         this.isFullTransfer = isFullTransfer;
      }

      public void setRedemptionFlag(String redemptionFlag) {
         this.redemptionFlag = redemptionFlag;
      }

      public void setRateType(String rateType) {
         this.rateType = rateType;
      }

      public void setTohonorRate(BigDecimal tohonorRate) {
         this.tohonorRate = tohonorRate;
      }

      public void setGraceChargeIntFlag(String graceChargeIntFlag) {
         this.graceChargeIntFlag = graceChargeIntFlag;
      }

      public void setIntFlag(String intFlag) {
         this.intFlag = intFlag;
      }

      public void setCaptDate(String captDate) {
         this.captDate = captDate;
      }

      public void setProdBeginDate(String prodBeginDate) {
         this.prodBeginDate = prodBeginDate;
      }

      public void setInFee(BigDecimal inFee) {
         this.inFee = inFee;
      }

      public void setTrfOutFeeAmt(BigDecimal trfOutFeeAmt) {
         this.trfOutFeeAmt = trfOutFeeAmt;
      }

      public void setIsCashFromInnerAcct(String isCashFromInnerAcct) {
         this.isCashFromInnerAcct = isCashFromInnerAcct;
      }

      public void setInlandOffshore(String inlandOffshore) {
         this.inlandOffshore = inlandOffshore;
      }

      public void setRedeemDate(String redeemDate) {
         this.redeemDate = redeemDate;
      }

      public void setApprovalNo(String approvalNo) {
         this.approvalNo = approvalNo;
      }

      public void setOnSaleChannel(String onSaleChannel) {
         this.onSaleChannel = onSaleChannel;
      }

      public void setRedemptionIntType(String redemptionIntType) {
         this.redemptionIntType = redemptionIntType;
      }

      public void setMinChangeBalance(BigDecimal minChangeBalance) {
         this.minChangeBalance = minChangeBalance;
      }

      public void setAvailSaleSubBranch(String availSaleSubBranch) {
         this.availSaleSubBranch = availSaleSubBranch;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100029Out.StageCodeInfoArray)) {
            return false;
         } else {
            Core1400100029Out.StageCodeInfoArray other = (Core1400100029Out.StageCodeInfoArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label479: {
                  Object this$baseProdType = this.getBaseProdType();
                  Object other$baseProdType = other.getBaseProdType();
                  if (this$baseProdType == null) {
                     if (other$baseProdType == null) {
                        break label479;
                     }
                  } else if (this$baseProdType.equals(other$baseProdType)) {
                     break label479;
                  }

                  return false;
               }

               Object this$ccy = this.getCcy();
               Object other$ccy = other.getCcy();
               if (this$ccy == null) {
                  if (other$ccy != null) {
                     return false;
                  }
               } else if (!this$ccy.equals(other$ccy)) {
                  return false;
               }

               Object this$stageCode = this.getStageCode();
               Object other$stageCode = other.getStageCode();
               if (this$stageCode == null) {
                  if (other$stageCode != null) {
                     return false;
                  }
               } else if (!this$stageCode.equals(other$stageCode)) {
                  return false;
               }

               label458: {
                  Object this$stageCodeDesc = this.getStageCodeDesc();
                  Object other$stageCodeDesc = other.getStageCodeDesc();
                  if (this$stageCodeDesc == null) {
                     if (other$stageCodeDesc == null) {
                        break label458;
                     }
                  } else if (this$stageCodeDesc.equals(other$stageCodeDesc)) {
                     break label458;
                  }

                  return false;
               }

               label451: {
                  Object this$issueAmt = this.getIssueAmt();
                  Object other$issueAmt = other.getIssueAmt();
                  if (this$issueAmt == null) {
                     if (other$issueAmt == null) {
                        break label451;
                     }
                  } else if (this$issueAmt.equals(other$issueAmt)) {
                     break label451;
                  }

                  return false;
               }

               Object this$availableLimit = this.getAvailableLimit();
               Object other$availableLimit = other.getAvailableLimit();
               if (this$availableLimit == null) {
                  if (other$availableLimit != null) {
                     return false;
                  }
               } else if (!this$availableLimit.equals(other$availableLimit)) {
                  return false;
               }

               Object this$stageMinAmt = this.getStageMinAmt();
               Object other$stageMinAmt = other.getStageMinAmt();
               if (this$stageMinAmt == null) {
                  if (other$stageMinAmt != null) {
                     return false;
                  }
               } else if (!this$stageMinAmt.equals(other$stageMinAmt)) {
                  return false;
               }

               label430: {
                  Object this$keepMinBal = this.getKeepMinBal();
                  Object other$keepMinBal = other.getKeepMinBal();
                  if (this$keepMinBal == null) {
                     if (other$keepMinBal == null) {
                        break label430;
                     }
                  } else if (this$keepMinBal.equals(other$keepMinBal)) {
                     break label430;
                  }

                  return false;
               }

               label423: {
                  Object this$sgMaxAmt = this.getSgMaxAmt();
                  Object other$sgMaxAmt = other.getSgMaxAmt();
                  if (this$sgMaxAmt == null) {
                     if (other$sgMaxAmt == null) {
                        break label423;
                     }
                  } else if (this$sgMaxAmt.equals(other$sgMaxAmt)) {
                     break label423;
                  }

                  return false;
               }

               Object this$startDate = this.getStartDate();
               Object other$startDate = other.getStartDate();
               if (this$startDate == null) {
                  if (other$startDate != null) {
                     return false;
                  }
               } else if (!this$startDate.equals(other$startDate)) {
                  return false;
               }

               label409: {
                  Object this$saleStartTime = this.getSaleStartTime();
                  Object other$saleStartTime = other.getSaleStartTime();
                  if (this$saleStartTime == null) {
                     if (other$saleStartTime == null) {
                        break label409;
                     }
                  } else if (this$saleStartTime.equals(other$saleStartTime)) {
                     break label409;
                  }

                  return false;
               }

               Object this$endDate = this.getEndDate();
               Object other$endDate = other.getEndDate();
               if (this$endDate == null) {
                  if (other$endDate != null) {
                     return false;
                  }
               } else if (!this$endDate.equals(other$endDate)) {
                  return false;
               }

               label395: {
                  Object this$saleEndTime = this.getSaleEndTime();
                  Object other$saleEndTime = other.getSaleEndTime();
                  if (this$saleEndTime == null) {
                     if (other$saleEndTime == null) {
                        break label395;
                     }
                  } else if (this$saleEndTime.equals(other$saleEndTime)) {
                     break label395;
                  }

                  return false;
               }

               Object this$payIntType = this.getPayIntType();
               Object other$payIntType = other.getPayIntType();
               if (this$payIntType == null) {
                  if (other$payIntType != null) {
                     return false;
                  }
               } else if (!this$payIntType.equals(other$payIntType)) {
                  return false;
               }

               Object this$aupCycle = this.getAupCycle();
               Object other$aupCycle = other.getAupCycle();
               if (this$aupCycle == null) {
                  if (other$aupCycle != null) {
                     return false;
                  }
               } else if (!this$aupCycle.equals(other$aupCycle)) {
                  return false;
               }

               label374: {
                  Object this$preWithdrawFlag = this.getPreWithdrawFlag();
                  Object other$preWithdrawFlag = other.getPreWithdrawFlag();
                  if (this$preWithdrawFlag == null) {
                     if (other$preWithdrawFlag == null) {
                        break label374;
                     }
                  } else if (this$preWithdrawFlag.equals(other$preWithdrawFlag)) {
                     break label374;
                  }

                  return false;
               }

               label367: {
                  Object this$preWithdrawNum = this.getPreWithdrawNum();
                  Object other$preWithdrawNum = other.getPreWithdrawNum();
                  if (this$preWithdrawNum == null) {
                     if (other$preWithdrawNum == null) {
                        break label367;
                     }
                  } else if (this$preWithdrawNum.equals(other$preWithdrawNum)) {
                     break label367;
                  }

                  return false;
               }

               Object this$term = this.getTerm();
               Object other$term = other.getTerm();
               if (this$term == null) {
                  if (other$term != null) {
                     return false;
                  }
               } else if (!this$term.equals(other$term)) {
                  return false;
               }

               Object this$intType = this.getIntType();
               Object other$intType = other.getIntType();
               if (this$intType == null) {
                  if (other$intType != null) {
                     return false;
                  }
               } else if (!this$intType.equals(other$intType)) {
                  return false;
               }

               label346: {
                  Object this$floatRate = this.getFloatRate();
                  Object other$floatRate = other.getFloatRate();
                  if (this$floatRate == null) {
                     if (other$floatRate == null) {
                        break label346;
                     }
                  } else if (this$floatRate.equals(other$floatRate)) {
                     break label346;
                  }

                  return false;
               }

               label339: {
                  Object this$realRate = this.getRealRate();
                  Object other$realRate = other.getRealRate();
                  if (this$realRate == null) {
                     if (other$realRate == null) {
                        break label339;
                     }
                  } else if (this$realRate.equals(other$realRate)) {
                     break label339;
                  }

                  return false;
               }

               Object this$isFullTransfer = this.getIsFullTransfer();
               Object other$isFullTransfer = other.getIsFullTransfer();
               if (this$isFullTransfer == null) {
                  if (other$isFullTransfer != null) {
                     return false;
                  }
               } else if (!this$isFullTransfer.equals(other$isFullTransfer)) {
                  return false;
               }

               Object this$redemptionFlag = this.getRedemptionFlag();
               Object other$redemptionFlag = other.getRedemptionFlag();
               if (this$redemptionFlag == null) {
                  if (other$redemptionFlag != null) {
                     return false;
                  }
               } else if (!this$redemptionFlag.equals(other$redemptionFlag)) {
                  return false;
               }

               label318: {
                  Object this$rateType = this.getRateType();
                  Object other$rateType = other.getRateType();
                  if (this$rateType == null) {
                     if (other$rateType == null) {
                        break label318;
                     }
                  } else if (this$rateType.equals(other$rateType)) {
                     break label318;
                  }

                  return false;
               }

               label311: {
                  Object this$tohonorRate = this.getTohonorRate();
                  Object other$tohonorRate = other.getTohonorRate();
                  if (this$tohonorRate == null) {
                     if (other$tohonorRate == null) {
                        break label311;
                     }
                  } else if (this$tohonorRate.equals(other$tohonorRate)) {
                     break label311;
                  }

                  return false;
               }

               Object this$graceChargeIntFlag = this.getGraceChargeIntFlag();
               Object other$graceChargeIntFlag = other.getGraceChargeIntFlag();
               if (this$graceChargeIntFlag == null) {
                  if (other$graceChargeIntFlag != null) {
                     return false;
                  }
               } else if (!this$graceChargeIntFlag.equals(other$graceChargeIntFlag)) {
                  return false;
               }

               label297: {
                  Object this$intFlag = this.getIntFlag();
                  Object other$intFlag = other.getIntFlag();
                  if (this$intFlag == null) {
                     if (other$intFlag == null) {
                        break label297;
                     }
                  } else if (this$intFlag.equals(other$intFlag)) {
                     break label297;
                  }

                  return false;
               }

               Object this$captDate = this.getCaptDate();
               Object other$captDate = other.getCaptDate();
               if (this$captDate == null) {
                  if (other$captDate != null) {
                     return false;
                  }
               } else if (!this$captDate.equals(other$captDate)) {
                  return false;
               }

               label283: {
                  Object this$prodBeginDate = this.getProdBeginDate();
                  Object other$prodBeginDate = other.getProdBeginDate();
                  if (this$prodBeginDate == null) {
                     if (other$prodBeginDate == null) {
                        break label283;
                     }
                  } else if (this$prodBeginDate.equals(other$prodBeginDate)) {
                     break label283;
                  }

                  return false;
               }

               Object this$inFee = this.getInFee();
               Object other$inFee = other.getInFee();
               if (this$inFee == null) {
                  if (other$inFee != null) {
                     return false;
                  }
               } else if (!this$inFee.equals(other$inFee)) {
                  return false;
               }

               Object this$trfOutFeeAmt = this.getTrfOutFeeAmt();
               Object other$trfOutFeeAmt = other.getTrfOutFeeAmt();
               if (this$trfOutFeeAmt == null) {
                  if (other$trfOutFeeAmt != null) {
                     return false;
                  }
               } else if (!this$trfOutFeeAmt.equals(other$trfOutFeeAmt)) {
                  return false;
               }

               label262: {
                  Object this$isCashFromInnerAcct = this.getIsCashFromInnerAcct();
                  Object other$isCashFromInnerAcct = other.getIsCashFromInnerAcct();
                  if (this$isCashFromInnerAcct == null) {
                     if (other$isCashFromInnerAcct == null) {
                        break label262;
                     }
                  } else if (this$isCashFromInnerAcct.equals(other$isCashFromInnerAcct)) {
                     break label262;
                  }

                  return false;
               }

               label255: {
                  Object this$inlandOffshore = this.getInlandOffshore();
                  Object other$inlandOffshore = other.getInlandOffshore();
                  if (this$inlandOffshore == null) {
                     if (other$inlandOffshore == null) {
                        break label255;
                     }
                  } else if (this$inlandOffshore.equals(other$inlandOffshore)) {
                     break label255;
                  }

                  return false;
               }

               Object this$redeemDate = this.getRedeemDate();
               Object other$redeemDate = other.getRedeemDate();
               if (this$redeemDate == null) {
                  if (other$redeemDate != null) {
                     return false;
                  }
               } else if (!this$redeemDate.equals(other$redeemDate)) {
                  return false;
               }

               Object this$approvalNo = this.getApprovalNo();
               Object other$approvalNo = other.getApprovalNo();
               if (this$approvalNo == null) {
                  if (other$approvalNo != null) {
                     return false;
                  }
               } else if (!this$approvalNo.equals(other$approvalNo)) {
                  return false;
               }

               label234: {
                  Object this$onSaleChannel = this.getOnSaleChannel();
                  Object other$onSaleChannel = other.getOnSaleChannel();
                  if (this$onSaleChannel == null) {
                     if (other$onSaleChannel == null) {
                        break label234;
                     }
                  } else if (this$onSaleChannel.equals(other$onSaleChannel)) {
                     break label234;
                  }

                  return false;
               }

               label227: {
                  Object this$redemptionIntType = this.getRedemptionIntType();
                  Object other$redemptionIntType = other.getRedemptionIntType();
                  if (this$redemptionIntType == null) {
                     if (other$redemptionIntType == null) {
                        break label227;
                     }
                  } else if (this$redemptionIntType.equals(other$redemptionIntType)) {
                     break label227;
                  }

                  return false;
               }

               Object this$minChangeBalance = this.getMinChangeBalance();
               Object other$minChangeBalance = other.getMinChangeBalance();
               if (this$minChangeBalance == null) {
                  if (other$minChangeBalance != null) {
                     return false;
                  }
               } else if (!this$minChangeBalance.equals(other$minChangeBalance)) {
                  return false;
               }

               Object this$availSaleSubBranch = this.getAvailSaleSubBranch();
               Object other$availSaleSubBranch = other.getAvailSaleSubBranch();
               if (this$availSaleSubBranch == null) {
                  if (other$availSaleSubBranch != null) {
                     return false;
                  }
               } else if (!this$availSaleSubBranch.equals(other$availSaleSubBranch)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100029Out.StageCodeInfoArray;
      }
      public String toString() {
         return "Core1400100029Out.StageCodeInfoArray(baseProdType=" + this.getBaseProdType() + ", ccy=" + this.getCcy() + ", stageCode=" + this.getStageCode() + ", stageCodeDesc=" + this.getStageCodeDesc() + ", issueAmt=" + this.getIssueAmt() + ", availableLimit=" + this.getAvailableLimit() + ", stageMinAmt=" + this.getStageMinAmt() + ", keepMinBal=" + this.getKeepMinBal() + ", sgMaxAmt=" + this.getSgMaxAmt() + ", startDate=" + this.getStartDate() + ", saleStartTime=" + this.getSaleStartTime() + ", endDate=" + this.getEndDate() + ", saleEndTime=" + this.getSaleEndTime() + ", payIntType=" + this.getPayIntType() + ", aupCycle=" + this.getAupCycle() + ", preWithdrawFlag=" + this.getPreWithdrawFlag() + ", preWithdrawNum=" + this.getPreWithdrawNum() + ", term=" + this.getTerm() + ", intType=" + this.getIntType() + ", floatRate=" + this.getFloatRate() + ", realRate=" + this.getRealRate() + ", isFullTransfer=" + this.getIsFullTransfer() + ", redemptionFlag=" + this.getRedemptionFlag() + ", rateType=" + this.getRateType() + ", tohonorRate=" + this.getTohonorRate() + ", graceChargeIntFlag=" + this.getGraceChargeIntFlag() + ", intFlag=" + this.getIntFlag() + ", captDate=" + this.getCaptDate() + ", prodBeginDate=" + this.getProdBeginDate() + ", inFee=" + this.getInFee() + ", trfOutFeeAmt=" + this.getTrfOutFeeAmt() + ", isCashFromInnerAcct=" + this.getIsCashFromInnerAcct() + ", inlandOffshore=" + this.getInlandOffshore() + ", redeemDate=" + this.getRedeemDate() + ", approvalNo=" + this.getApprovalNo() + ", onSaleChannel=" + this.getOnSaleChannel() + ", redemptionIntType=" + this.getRedemptionIntType() + ", minChangeBalance=" + this.getMinChangeBalance() + ", availSaleSubBranch=" + this.getAvailSaleSubBranch() + ")";
      }
   }

   public static class RunRuleBefore {
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "产品描述",
         notNull = false,
         length = "200",
         remark = "解释产品具体特性",
         maxSize = 200
      )
      private String prodDesc;
      @V(
         desc = "产品启用日期",
         notNull = false,
         remark = "产品启用日期"
      )
      private String prodStartDate;
      @V(
         desc = "产品停用日期",
         notNull = false,
         remark = "产品停用日期"
      )
      private String prodEndDate;
      @V(
         desc = "产品状态",
         notNull = false,
         length = "1",
         remark = "产品状态",
         maxSize = 1
      )
      private String prodStatus;
      @V(
         desc = "存期期限",
         notNull = false,
         length = "5",
         remark = "期限",
         maxSize = 5
      )
      private String term;
      @V(
         desc = "认购起存金额",
         notNull = false,
         length = "17",
         remark = "认购起存金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal initAmt;
      @V(
         desc = "累进金额",
         notNull = false,
         length = "17",
         remark = "累进金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal addAmt;
      @V(
         desc = "数组",
         notNull = false,
         remark = "数组"
      )
      private List<Core1400100029Out.RunRuleBefore.RateArray> rateArray;
      @V(
         desc = "数组",
         notNull = false,
         remark = "数组"
      )
      private List<Core1400100029Out.RunRuleBefore.ListArray> listArray;
      @V(
         desc = "客户类型范围",
         notNull = false,
         length = "5",
         remark = "客户类型范围",
         maxSize = 5
      )
      private String clientTypeRange;

      public String getProdType() {
         return this.prodType;
      }

      public String getProdDesc() {
         return this.prodDesc;
      }

      public String getProdStartDate() {
         return this.prodStartDate;
      }

      public String getProdEndDate() {
         return this.prodEndDate;
      }

      public String getProdStatus() {
         return this.prodStatus;
      }

      public String getTerm() {
         return this.term;
      }

      public BigDecimal getInitAmt() {
         return this.initAmt;
      }

      public BigDecimal getAddAmt() {
         return this.addAmt;
      }

      public List<Core1400100029Out.RunRuleBefore.RateArray> getRateArray() {
         return this.rateArray;
      }

      public List<Core1400100029Out.RunRuleBefore.ListArray> getListArray() {
         return this.listArray;
      }

      public String getClientTypeRange() {
         return this.clientTypeRange;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setProdDesc(String prodDesc) {
         this.prodDesc = prodDesc;
      }

      public void setProdStartDate(String prodStartDate) {
         this.prodStartDate = prodStartDate;
      }

      public void setProdEndDate(String prodEndDate) {
         this.prodEndDate = prodEndDate;
      }

      public void setProdStatus(String prodStatus) {
         this.prodStatus = prodStatus;
      }

      public void setTerm(String term) {
         this.term = term;
      }

      public void setInitAmt(BigDecimal initAmt) {
         this.initAmt = initAmt;
      }

      public void setAddAmt(BigDecimal addAmt) {
         this.addAmt = addAmt;
      }

      public void setRateArray(List<Core1400100029Out.RunRuleBefore.RateArray> rateArray) {
         this.rateArray = rateArray;
      }

      public void setListArray(List<Core1400100029Out.RunRuleBefore.ListArray> listArray) {
         this.listArray = listArray;
      }

      public void setClientTypeRange(String clientTypeRange) {
         this.clientTypeRange = clientTypeRange;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100029Out.RunRuleBefore)) {
            return false;
         } else {
            Core1400100029Out.RunRuleBefore other = (Core1400100029Out.RunRuleBefore)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label143: {
                  Object this$prodType = this.getProdType();
                  Object other$prodType = other.getProdType();
                  if (this$prodType == null) {
                     if (other$prodType == null) {
                        break label143;
                     }
                  } else if (this$prodType.equals(other$prodType)) {
                     break label143;
                  }

                  return false;
               }

               Object this$prodDesc = this.getProdDesc();
               Object other$prodDesc = other.getProdDesc();
               if (this$prodDesc == null) {
                  if (other$prodDesc != null) {
                     return false;
                  }
               } else if (!this$prodDesc.equals(other$prodDesc)) {
                  return false;
               }

               Object this$prodStartDate = this.getProdStartDate();
               Object other$prodStartDate = other.getProdStartDate();
               if (this$prodStartDate == null) {
                  if (other$prodStartDate != null) {
                     return false;
                  }
               } else if (!this$prodStartDate.equals(other$prodStartDate)) {
                  return false;
               }

               label122: {
                  Object this$prodEndDate = this.getProdEndDate();
                  Object other$prodEndDate = other.getProdEndDate();
                  if (this$prodEndDate == null) {
                     if (other$prodEndDate == null) {
                        break label122;
                     }
                  } else if (this$prodEndDate.equals(other$prodEndDate)) {
                     break label122;
                  }

                  return false;
               }

               label115: {
                  Object this$prodStatus = this.getProdStatus();
                  Object other$prodStatus = other.getProdStatus();
                  if (this$prodStatus == null) {
                     if (other$prodStatus == null) {
                        break label115;
                     }
                  } else if (this$prodStatus.equals(other$prodStatus)) {
                     break label115;
                  }

                  return false;
               }

               Object this$term = this.getTerm();
               Object other$term = other.getTerm();
               if (this$term == null) {
                  if (other$term != null) {
                     return false;
                  }
               } else if (!this$term.equals(other$term)) {
                  return false;
               }

               Object this$initAmt = this.getInitAmt();
               Object other$initAmt = other.getInitAmt();
               if (this$initAmt == null) {
                  if (other$initAmt != null) {
                     return false;
                  }
               } else if (!this$initAmt.equals(other$initAmt)) {
                  return false;
               }

               label94: {
                  Object this$addAmt = this.getAddAmt();
                  Object other$addAmt = other.getAddAmt();
                  if (this$addAmt == null) {
                     if (other$addAmt == null) {
                        break label94;
                     }
                  } else if (this$addAmt.equals(other$addAmt)) {
                     break label94;
                  }

                  return false;
               }

               label87: {
                  Object this$rateArray = this.getRateArray();
                  Object other$rateArray = other.getRateArray();
                  if (this$rateArray == null) {
                     if (other$rateArray == null) {
                        break label87;
                     }
                  } else if (this$rateArray.equals(other$rateArray)) {
                     break label87;
                  }

                  return false;
               }

               Object this$listArray = this.getListArray();
               Object other$listArray = other.getListArray();
               if (this$listArray == null) {
                  if (other$listArray != null) {
                     return false;
                  }
               } else if (!this$listArray.equals(other$listArray)) {
                  return false;
               }

               Object this$clientTypeRange = this.getClientTypeRange();
               Object other$clientTypeRange = other.getClientTypeRange();
               if (this$clientTypeRange == null) {
                  if (other$clientTypeRange != null) {
                     return false;
                  }
               } else if (!this$clientTypeRange.equals(other$clientTypeRange)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100029Out.RunRuleBefore;
      }
      public String toString() {
         return "Core1400100029Out.RunRuleBefore(prodType=" + this.getProdType() + ", prodDesc=" + this.getProdDesc() + ", prodStartDate=" + this.getProdStartDate() + ", prodEndDate=" + this.getProdEndDate() + ", prodStatus=" + this.getProdStatus() + ", term=" + this.getTerm() + ", initAmt=" + this.getInitAmt() + ", addAmt=" + this.getAddAmt() + ", rateArray=" + this.getRateArray() + ", listArray=" + this.getListArray() + ", clientTypeRange=" + this.getClientTypeRange() + ")";
      }

      public static class ListArray {
         @V(
            desc = "币种",
            notNull = false,
            length = "3",
            remark = "币种",
            maxSize = 3
         )
         private String ccy;
         @V(
            desc = "最小留存金额",
            notNull = false,
            length = "17",
            remark = "最小留存金额",
            decimalLength = 2,
            precision = 2
         )
         private BigDecimal keepMinBal;

         public String getCcy() {
            return this.ccy;
         }

         public BigDecimal getKeepMinBal() {
            return this.keepMinBal;
         }

         public void setCcy(String ccy) {
            this.ccy = ccy;
         }

         public void setKeepMinBal(BigDecimal keepMinBal) {
            this.keepMinBal = keepMinBal;
         }

         public boolean equals(Object o) {
            if (o == this) {
               return true;
            } else if (!(o instanceof Core1400100029Out.RunRuleBefore.ListArray)) {
               return false;
            } else {
               Core1400100029Out.RunRuleBefore.ListArray other = (Core1400100029Out.RunRuleBefore.ListArray)o;
               if (!other.canEqual(this)) {
                  return false;
               } else {
                  Object this$ccy = this.getCcy();
                  Object other$ccy = other.getCcy();
                  if (this$ccy == null) {
                     if (other$ccy != null) {
                        return false;
                     }
                  } else if (!this$ccy.equals(other$ccy)) {
                     return false;
                  }

                  Object this$keepMinBal = this.getKeepMinBal();
                  Object other$keepMinBal = other.getKeepMinBal();
                  if (this$keepMinBal == null) {
                     if (other$keepMinBal != null) {
                        return false;
                     }
                  } else if (!this$keepMinBal.equals(other$keepMinBal)) {
                     return false;
                  }

                  return true;
               }
            }
         }

         protected boolean canEqual(Object other) {
            return other instanceof Core1400100029Out.RunRuleBefore.ListArray;
         }
         public String toString() {
            return "Core1400100029Out.RunRuleBefore.ListArray(ccy=" + this.getCcy() + ", keepMinBal=" + this.getKeepMinBal() + ")";
         }
      }

      public static class RateArray {
         @V(
            desc = "浮动点差",
            notNull = false,
            length = "15",
            remark = "浮动点差",
            decimalLength = 8,
            precision = 8
         )
         private BigDecimal floatPoint;
         @V(
            desc = "执行利率",
            notNull = false,
            length = "15",
            remark = "执行利率",
            decimalLength = 8,
            precision = 8
         )
         private BigDecimal realRate;

         public BigDecimal getFloatPoint() {
            return this.floatPoint;
         }

         public BigDecimal getRealRate() {
            return this.realRate;
         }

         public void setFloatPoint(BigDecimal floatPoint) {
            this.floatPoint = floatPoint;
         }

         public void setRealRate(BigDecimal realRate) {
            this.realRate = realRate;
         }

         public boolean equals(Object o) {
            if (o == this) {
               return true;
            } else if (!(o instanceof Core1400100029Out.RunRuleBefore.RateArray)) {
               return false;
            } else {
               Core1400100029Out.RunRuleBefore.RateArray other = (Core1400100029Out.RunRuleBefore.RateArray)o;
               if (!other.canEqual(this)) {
                  return false;
               } else {
                  Object this$floatPoint = this.getFloatPoint();
                  Object other$floatPoint = other.getFloatPoint();
                  if (this$floatPoint == null) {
                     if (other$floatPoint != null) {
                        return false;
                     }
                  } else if (!this$floatPoint.equals(other$floatPoint)) {
                     return false;
                  }

                  Object this$realRate = this.getRealRate();
                  Object other$realRate = other.getRealRate();
                  if (this$realRate == null) {
                     if (other$realRate != null) {
                        return false;
                     }
                  } else if (!this$realRate.equals(other$realRate)) {
                     return false;
                  }

                  return true;
               }
            }
         }

         protected boolean canEqual(Object other) {
            return other instanceof Core1400100029Out.RunRuleBefore.RateArray;
         }
         public String toString() {
            return "Core1400100029Out.RunRuleBefore.RateArray(floatPoint=" + this.getFloatPoint() + ", realRate=" + this.getRealRate() + ")";
         }
      }
   }
}
