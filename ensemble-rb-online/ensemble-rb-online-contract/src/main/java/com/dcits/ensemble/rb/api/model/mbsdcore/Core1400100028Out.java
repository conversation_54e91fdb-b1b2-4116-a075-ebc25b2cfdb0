package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.util.List;

@MessageOut
public class Core1400100028Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "暂停非柜面数组",
      notNull = false,
      remark = "暂停非柜面数组"
   )
   private List<Core1400100028Out.UncounterArray> uncounterArray;

   public List<Core1400100028Out.UncounterArray> getUncounterArray() {
      return this.uncounterArray;
   }

   public void setUncounterArray(List<Core1400100028Out.UncounterArray> uncounterArray) {
      this.uncounterArray = uncounterArray;
   }

   public String toString() {
      return "Core1400100028Out(uncounterArray=" + this.getUncounterArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100028Out)) {
         return false;
      } else {
         Core1400100028Out other = (Core1400100028Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$uncounterArray = this.getUncounterArray();
            Object other$uncounterArray = other.getUncounterArray();
            if (this$uncounterArray == null) {
               if (other$uncounterArray != null) {
                  return false;
               }
            } else if (!this$uncounterArray.equals(other$uncounterArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100028Out;
   }
   public static class UncounterArray {
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "暂记非柜面限制类型",
         notNull = false,
         length = "3",
         remark = "暂记非柜面限制类型",
         maxSize = 3
      )
      private String uncounterRestraintType;
      @V(
         desc = "入表原因",
         notNull = false,
         length = "50",
         remark = "入表原因",
         maxSize = 50
      )
      private String uncounterDesc;
      @V(
         desc = "业务类型描述",
         notNull = false,
         length = "200",
         remark = "业务类型描述",
         maxSize = 200
      )
      private String busiDesc;
      @V(
         desc = "所属机构号",
         notNull = false,
         length = "50",
         remark = "所属机构号",
         maxSize = 50
      )
      private String branch;
      @V(
         desc = "状态",
         notNull = false,
         length = "1",
         remark = "状态",
         maxSize = 1
      )
      private String status;
      @V(
         desc = "交易渠道状态",
         notNull = false,
         length = "3",
         remark = "交易渠道状态",
         maxSize = 3
      )
      private String tranChannelStatus;
      @V(
         desc = "涉案标识",
         notNull = false,
         length = "10",
         remark = "用于华兴银行涉案标识处理",
         maxSize = 10
      )
      private String caseInvolvedFlag;
      @V(
         desc = "客户涉案日期",
         notNull = false,
         remark = "客户涉案日期"
      )
      private String caseInvolvedDate;
      @V(
         desc = "操作柜员",
         notNull = false,
         length = "30",
         remark = "操作柜员",
         maxSize = 30
      )
      private String operUserId;
      @V(
         desc = "设置时间",
         notNull = false,
         remark = "设置时间"
      )
      private String setTime;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getUncounterRestraintType() {
         return this.uncounterRestraintType;
      }

      public String getUncounterDesc() {
         return this.uncounterDesc;
      }

      public String getBusiDesc() {
         return this.busiDesc;
      }

      public String getBranch() {
         return this.branch;
      }

      public String getStatus() {
         return this.status;
      }

      public String getTranChannelStatus() {
         return this.tranChannelStatus;
      }

      public String getCaseInvolvedFlag() {
         return this.caseInvolvedFlag;
      }

      public String getCaseInvolvedDate() {
         return this.caseInvolvedDate;
      }

      public String getOperUserId() {
         return this.operUserId;
      }

      public String getSetTime() {
         return this.setTime;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setUncounterRestraintType(String uncounterRestraintType) {
         this.uncounterRestraintType = uncounterRestraintType;
      }

      public void setUncounterDesc(String uncounterDesc) {
         this.uncounterDesc = uncounterDesc;
      }

      public void setBusiDesc(String busiDesc) {
         this.busiDesc = busiDesc;
      }

      public void setBranch(String branch) {
         this.branch = branch;
      }

      public void setStatus(String status) {
         this.status = status;
      }

      public void setTranChannelStatus(String tranChannelStatus) {
         this.tranChannelStatus = tranChannelStatus;
      }

      public void setCaseInvolvedFlag(String caseInvolvedFlag) {
         this.caseInvolvedFlag = caseInvolvedFlag;
      }

      public void setCaseInvolvedDate(String caseInvolvedDate) {
         this.caseInvolvedDate = caseInvolvedDate;
      }

      public void setOperUserId(String operUserId) {
         this.operUserId = operUserId;
      }

      public void setSetTime(String setTime) {
         this.setTime = setTime;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100028Out.UncounterArray)) {
            return false;
         } else {
            Core1400100028Out.UncounterArray other = (Core1400100028Out.UncounterArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo != null) {
                     return false;
                  }
               } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo != null) {
                     return false;
                  }
               } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                  return false;
               }

               label158: {
                  Object this$acctCcy = this.getAcctCcy();
                  Object other$acctCcy = other.getAcctCcy();
                  if (this$acctCcy == null) {
                     if (other$acctCcy == null) {
                        break label158;
                     }
                  } else if (this$acctCcy.equals(other$acctCcy)) {
                     break label158;
                  }

                  return false;
               }

               label151: {
                  Object this$uncounterRestraintType = this.getUncounterRestraintType();
                  Object other$uncounterRestraintType = other.getUncounterRestraintType();
                  if (this$uncounterRestraintType == null) {
                     if (other$uncounterRestraintType == null) {
                        break label151;
                     }
                  } else if (this$uncounterRestraintType.equals(other$uncounterRestraintType)) {
                     break label151;
                  }

                  return false;
               }

               Object this$uncounterDesc = this.getUncounterDesc();
               Object other$uncounterDesc = other.getUncounterDesc();
               if (this$uncounterDesc == null) {
                  if (other$uncounterDesc != null) {
                     return false;
                  }
               } else if (!this$uncounterDesc.equals(other$uncounterDesc)) {
                  return false;
               }

               label137: {
                  Object this$busiDesc = this.getBusiDesc();
                  Object other$busiDesc = other.getBusiDesc();
                  if (this$busiDesc == null) {
                     if (other$busiDesc == null) {
                        break label137;
                     }
                  } else if (this$busiDesc.equals(other$busiDesc)) {
                     break label137;
                  }

                  return false;
               }

               label130: {
                  Object this$branch = this.getBranch();
                  Object other$branch = other.getBranch();
                  if (this$branch == null) {
                     if (other$branch == null) {
                        break label130;
                     }
                  } else if (this$branch.equals(other$branch)) {
                     break label130;
                  }

                  return false;
               }

               Object this$status = this.getStatus();
               Object other$status = other.getStatus();
               if (this$status == null) {
                  if (other$status != null) {
                     return false;
                  }
               } else if (!this$status.equals(other$status)) {
                  return false;
               }

               Object this$tranChannelStatus = this.getTranChannelStatus();
               Object other$tranChannelStatus = other.getTranChannelStatus();
               if (this$tranChannelStatus == null) {
                  if (other$tranChannelStatus != null) {
                     return false;
                  }
               } else if (!this$tranChannelStatus.equals(other$tranChannelStatus)) {
                  return false;
               }

               label109: {
                  Object this$caseInvolvedFlag = this.getCaseInvolvedFlag();
                  Object other$caseInvolvedFlag = other.getCaseInvolvedFlag();
                  if (this$caseInvolvedFlag == null) {
                     if (other$caseInvolvedFlag == null) {
                        break label109;
                     }
                  } else if (this$caseInvolvedFlag.equals(other$caseInvolvedFlag)) {
                     break label109;
                  }

                  return false;
               }

               label102: {
                  Object this$caseInvolvedDate = this.getCaseInvolvedDate();
                  Object other$caseInvolvedDate = other.getCaseInvolvedDate();
                  if (this$caseInvolvedDate == null) {
                     if (other$caseInvolvedDate == null) {
                        break label102;
                     }
                  } else if (this$caseInvolvedDate.equals(other$caseInvolvedDate)) {
                     break label102;
                  }

                  return false;
               }

               Object this$operUserId = this.getOperUserId();
               Object other$operUserId = other.getOperUserId();
               if (this$operUserId == null) {
                  if (other$operUserId != null) {
                     return false;
                  }
               } else if (!this$operUserId.equals(other$operUserId)) {
                  return false;
               }

               Object this$setTime = this.getSetTime();
               Object other$setTime = other.getSetTime();
               if (this$setTime == null) {
                  if (other$setTime != null) {
                     return false;
                  }
               } else if (!this$setTime.equals(other$setTime)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100028Out.UncounterArray;
      }
      public String toString() {
         return "Core1400100028Out.UncounterArray(baseAcctNo=" + this.getBaseAcctNo() + ", prodType=" + this.getProdType() + ", acctSeqNo=" + this.getAcctSeqNo() + ", acctCcy=" + this.getAcctCcy() + ", uncounterRestraintType=" + this.getUncounterRestraintType() + ", uncounterDesc=" + this.getUncounterDesc() + ", busiDesc=" + this.getBusiDesc() + ", branch=" + this.getBranch() + ", status=" + this.getStatus() + ", tranChannelStatus=" + this.getTranChannelStatus() + ", caseInvolvedFlag=" + this.getCaseInvolvedFlag() + ", caseInvolvedDate=" + this.getCaseInvolvedDate() + ", operUserId=" + this.getOperUserId() + ", setTime=" + this.getSetTime() + ")";
      }
   }
}
