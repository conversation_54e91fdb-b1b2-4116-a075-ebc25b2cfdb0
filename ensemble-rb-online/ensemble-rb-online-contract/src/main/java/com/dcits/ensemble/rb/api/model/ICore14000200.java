package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000200In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000200Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000200 {
   String URL = "/rb/nfin/agreementId/inquiry";

   
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0200"
   )
   Core14000200Out runService(Core14000200In var1);
}
