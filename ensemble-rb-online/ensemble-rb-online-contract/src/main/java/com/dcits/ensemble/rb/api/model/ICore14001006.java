package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14001006In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14001006Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14001006 {
   String URL = "/rb/inq/arrt/qrybefperperty";


   @ApiRemark("/rb/inq/arrt/qrybefperperty")
   @ApiDesc("只查询最后一次属性变更之前账户所发生的动账交易历史。最后一次属性变更之后账户所发生的动账交易历史需要通过账户交易历史查询功能查")
   @EnsembleElements(
      serviceCode = "Core",
      messageType = "1400",
      messageCode = "1006"
   )
   @BusinessCategory("1400-查询")
   @FunctionCategory("RB02-账户管理")
   Core14001006Out runService(Core14001006In var1);
}
