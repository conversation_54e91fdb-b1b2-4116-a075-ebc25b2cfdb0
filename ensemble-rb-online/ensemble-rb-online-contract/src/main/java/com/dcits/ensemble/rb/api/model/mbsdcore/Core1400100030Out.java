package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;

@MessageOut
public class Core1400100030Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "协议编号",
      notNull = false,
      length = "50",
      remark = "协议编号",
      maxSize = 50
   )
   private String agreementId;
   @V(
      desc = "协议类型",
      notNull = false,
      length = "10",
      remark = "协议类型",
      maxSize = 10
   )
   private String agreementType;
   @V(
      desc = "协议状态",
      notNull = false,
      length = "2",
      remark = "普通协议使用，可应用于大部分场景",
      maxSize = 2
   )
   private String agreementStatus;
   @V(
      desc = "客户号",
      notNull = false,
      length = "20",
      remark = "客户号",
      maxSize = 20
   )
   private String clientNo;
   @V(
      desc = "账号/卡号",
      notNull = false,
      length = "50",
      remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
      maxSize = 50
   )
   private String baseAcctNo;
   @V(
      desc = "产品类型",
      notNull = false,
      length = "20",
      remark = "产品类型",
      maxSize = 20
   )
   private String prodType;
   @V(
      desc = "账户币种",
      notNull = false,
      length = "3",
      remark = "账户币种 对于AIO账户和一本通账户",
      maxSize = 3
   )
   private String acctCcy;
   @V(
      desc = "账户序号",
      notNull = false,
      length = "5",
      remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
      maxSize = 5
   )
   private String acctSeqNo;
   @V(
      desc = "签约日期",
      notNull = false,
      remark = "签约日期"
   )
   private String signDate;
   @V(
      desc = "签约柜员",
      notNull = false,
      length = "30",
      remark = "签约柜员",
      maxSize = 30
   )
   private String signUserId;
   @V(
      desc = "签约机构",
      notNull = false,
      length = "50",
      remark = "签约机构",
      maxSize = 50
   )
   private String signBranch;
   @V(
      desc = "是否自动转存",
      notNull = false,
      length = "10",
      remark = "定期是否自动转存",
      maxSize = 10
   )
   private String autoRenewRollover;
   @V(
      desc = "划转频率",
      notNull = false,
      length = "5",
      remark = "划转频率",
      maxSize = 5
   )
   private String transferFreq;
   @V(
      desc = "划转频率类型",
      notNull = false,
      length = "5",
      remark = "理财划转频率了行，Y年/M-月/D-日/Q-季/W-周",
      maxSize = 5
   )
   private String transferFreqType;
   @V(
      desc = "转存起始日期",
      notNull = false,
      remark = "理财签约后的转存开始日期"
   )
   private String transferStartDate;
   @V(
      desc = "转存结束日期或终止日期",
      notNull = false,
      remark = "理财协议转存的结束日期"
   )
   private String transferEndDate;
   @V(
      desc = "划转日",
      notNull = false,
      length = "2",
      remark = "划转日",
      maxSize = 2
   )
   private String transferDay;
   @V(
      desc = "累积失败次数",
      notNull = false,
      length = "5",
      remark = "连续失败次数"
   )
   private Integer failureTimes;
   @V(
      desc = "失败总次数",
      notNull = false,
      length = "5",
      remark = "累积失败次数"
   )
   private Integer failureTotalTimes;
   @V(
      desc = "成功总次数",
      notNull = false,
      length = "5",
      remark = "成功总笔数"
   )
   private Integer successTotalTimes;
   @V(
      desc = "累积成功次数",
      notNull = false,
      length = "5",
      remark = "连续成功次数"
   )
   private Integer successTimes;
   @V(
      desc = "理财金额",
      notNull = false,
      length = "17",
      remark = "理财金额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal financialAmount;
   @V(
      desc = "理财固定金额",
      notNull = false,
      length = "17",
      remark = "理财固定金额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal finFixedAmt;
   @V(
      desc = "协议留存金额",
      notNull = false,
      length = "17",
      remark = "协议留存金额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal remainAmt;
   @V(
      desc = "最小起存金额",
      notNull = false,
      length = "17",
      remark = "最小起存金额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal intMinAmt;
   @V(
      desc = "行内利率",
      notNull = false,
      length = "15",
      remark = "在人行基准利率调整后对客发布的行内利率",
      decimalLength = 8,
      precision = 8
   )
   private BigDecimal actualRate;
   @V(
      desc = "金额总和",
      notNull = false,
      length = "17",
      remark = "总金额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal sumAmount;
   @V(
      desc = "失败次数阈值设置",
      notNull = false,
      length = "5",
      remark = "失败次数阈值设置"
   )
   private Integer failureParamTimes;
   @V(
      desc = "结算账户余额",
      notNull = false,
      length = "17",
      remark = "结算账户余额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal settleAcctBalance;
   @V(
      desc = "上次划转日期",
      notNull = false,
      remark = "上次划转日期"
   )
   private String lastTransferDate;
   @V(
      desc = "存期期限",
      notNull = false,
      length = "5",
      remark = "期限",
      maxSize = 5
   )
   private String term;
   @V(
      desc = "下次划转日期",
      notNull = false,
      remark = "下次划转日期"
   )
   private String nextTransferDate;
   @V(
      desc = "期限类型",
      notNull = false,
      length = "1",
      remark = "期限类型",
      maxSize = 1
   )
   private String termType;

   public String getAgreementId() {
      return this.agreementId;
   }

   public String getAgreementType() {
      return this.agreementType;
   }

   public String getAgreementStatus() {
      return this.agreementStatus;
   }

   public String getClientNo() {
      return this.clientNo;
   }

   public String getBaseAcctNo() {
      return this.baseAcctNo;
   }

   public String getProdType() {
      return this.prodType;
   }

   public String getAcctCcy() {
      return this.acctCcy;
   }

   public String getAcctSeqNo() {
      return this.acctSeqNo;
   }

   public String getSignDate() {
      return this.signDate;
   }

   public String getSignUserId() {
      return this.signUserId;
   }

   public String getSignBranch() {
      return this.signBranch;
   }

   public String getAutoRenewRollover() {
      return this.autoRenewRollover;
   }

   public String getTransferFreq() {
      return this.transferFreq;
   }

   public String getTransferFreqType() {
      return this.transferFreqType;
   }

   public String getTransferStartDate() {
      return this.transferStartDate;
   }

   public String getTransferEndDate() {
      return this.transferEndDate;
   }

   public String getTransferDay() {
      return this.transferDay;
   }

   public Integer getFailureTimes() {
      return this.failureTimes;
   }

   public Integer getFailureTotalTimes() {
      return this.failureTotalTimes;
   }

   public Integer getSuccessTotalTimes() {
      return this.successTotalTimes;
   }

   public Integer getSuccessTimes() {
      return this.successTimes;
   }

   public BigDecimal getFinancialAmount() {
      return this.financialAmount;
   }

   public BigDecimal getFinFixedAmt() {
      return this.finFixedAmt;
   }

   public BigDecimal getRemainAmt() {
      return this.remainAmt;
   }

   public BigDecimal getIntMinAmt() {
      return this.intMinAmt;
   }

   public BigDecimal getActualRate() {
      return this.actualRate;
   }

   public BigDecimal getSumAmount() {
      return this.sumAmount;
   }

   public Integer getFailureParamTimes() {
      return this.failureParamTimes;
   }

   public BigDecimal getSettleAcctBalance() {
      return this.settleAcctBalance;
   }

   public String getLastTransferDate() {
      return this.lastTransferDate;
   }

   public String getTerm() {
      return this.term;
   }

   public String getNextTransferDate() {
      return this.nextTransferDate;
   }

   public String getTermType() {
      return this.termType;
   }

   public void setAgreementId(String agreementId) {
      this.agreementId = agreementId;
   }

   public void setAgreementType(String agreementType) {
      this.agreementType = agreementType;
   }

   public void setAgreementStatus(String agreementStatus) {
      this.agreementStatus = agreementStatus;
   }

   public void setClientNo(String clientNo) {
      this.clientNo = clientNo;
   }

   public void setBaseAcctNo(String baseAcctNo) {
      this.baseAcctNo = baseAcctNo;
   }

   public void setProdType(String prodType) {
      this.prodType = prodType;
   }

   public void setAcctCcy(String acctCcy) {
      this.acctCcy = acctCcy;
   }

   public void setAcctSeqNo(String acctSeqNo) {
      this.acctSeqNo = acctSeqNo;
   }

   public void setSignDate(String signDate) {
      this.signDate = signDate;
   }

   public void setSignUserId(String signUserId) {
      this.signUserId = signUserId;
   }

   public void setSignBranch(String signBranch) {
      this.signBranch = signBranch;
   }

   public void setAutoRenewRollover(String autoRenewRollover) {
      this.autoRenewRollover = autoRenewRollover;
   }

   public void setTransferFreq(String transferFreq) {
      this.transferFreq = transferFreq;
   }

   public void setTransferFreqType(String transferFreqType) {
      this.transferFreqType = transferFreqType;
   }

   public void setTransferStartDate(String transferStartDate) {
      this.transferStartDate = transferStartDate;
   }

   public void setTransferEndDate(String transferEndDate) {
      this.transferEndDate = transferEndDate;
   }

   public void setTransferDay(String transferDay) {
      this.transferDay = transferDay;
   }

   public void setFailureTimes(Integer failureTimes) {
      this.failureTimes = failureTimes;
   }

   public void setFailureTotalTimes(Integer failureTotalTimes) {
      this.failureTotalTimes = failureTotalTimes;
   }

   public void setSuccessTotalTimes(Integer successTotalTimes) {
      this.successTotalTimes = successTotalTimes;
   }

   public void setSuccessTimes(Integer successTimes) {
      this.successTimes = successTimes;
   }

   public void setFinancialAmount(BigDecimal financialAmount) {
      this.financialAmount = financialAmount;
   }

   public void setFinFixedAmt(BigDecimal finFixedAmt) {
      this.finFixedAmt = finFixedAmt;
   }

   public void setRemainAmt(BigDecimal remainAmt) {
      this.remainAmt = remainAmt;
   }

   public void setIntMinAmt(BigDecimal intMinAmt) {
      this.intMinAmt = intMinAmt;
   }

   public void setActualRate(BigDecimal actualRate) {
      this.actualRate = actualRate;
   }

   public void setSumAmount(BigDecimal sumAmount) {
      this.sumAmount = sumAmount;
   }

   public void setFailureParamTimes(Integer failureParamTimes) {
      this.failureParamTimes = failureParamTimes;
   }

   public void setSettleAcctBalance(BigDecimal settleAcctBalance) {
      this.settleAcctBalance = settleAcctBalance;
   }

   public void setLastTransferDate(String lastTransferDate) {
      this.lastTransferDate = lastTransferDate;
   }

   public void setTerm(String term) {
      this.term = term;
   }

   public void setNextTransferDate(String nextTransferDate) {
      this.nextTransferDate = nextTransferDate;
   }

   public void setTermType(String termType) {
      this.termType = termType;
   }

   public String toString() {
      return "Core1400100030Out(agreementId=" + this.getAgreementId() + ", agreementType=" + this.getAgreementType() + ", agreementStatus=" + this.getAgreementStatus() + ", clientNo=" + this.getClientNo() + ", baseAcctNo=" + this.getBaseAcctNo() + ", prodType=" + this.getProdType() + ", acctCcy=" + this.getAcctCcy() + ", acctSeqNo=" + this.getAcctSeqNo() + ", signDate=" + this.getSignDate() + ", signUserId=" + this.getSignUserId() + ", signBranch=" + this.getSignBranch() + ", autoRenewRollover=" + this.getAutoRenewRollover() + ", transferFreq=" + this.getTransferFreq() + ", transferFreqType=" + this.getTransferFreqType() + ", transferStartDate=" + this.getTransferStartDate() + ", transferEndDate=" + this.getTransferEndDate() + ", transferDay=" + this.getTransferDay() + ", failureTimes=" + this.getFailureTimes() + ", failureTotalTimes=" + this.getFailureTotalTimes() + ", successTotalTimes=" + this.getSuccessTotalTimes() + ", successTimes=" + this.getSuccessTimes() + ", financialAmount=" + this.getFinancialAmount() + ", finFixedAmt=" + this.getFinFixedAmt() + ", remainAmt=" + this.getRemainAmt() + ", intMinAmt=" + this.getIntMinAmt() + ", actualRate=" + this.getActualRate() + ", sumAmount=" + this.getSumAmount() + ", failureParamTimes=" + this.getFailureParamTimes() + ", settleAcctBalance=" + this.getSettleAcctBalance() + ", lastTransferDate=" + this.getLastTransferDate() + ", term=" + this.getTerm() + ", nextTransferDate=" + this.getNextTransferDate() + ", termType=" + this.getTermType() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100030Out)) {
         return false;
      } else {
         Core1400100030Out other = (Core1400100030Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            label409: {
               Object this$agreementId = this.getAgreementId();
               Object other$agreementId = other.getAgreementId();
               if (this$agreementId == null) {
                  if (other$agreementId == null) {
                     break label409;
                  }
               } else if (this$agreementId.equals(other$agreementId)) {
                  break label409;
               }

               return false;
            }

            Object this$agreementType = this.getAgreementType();
            Object other$agreementType = other.getAgreementType();
            if (this$agreementType == null) {
               if (other$agreementType != null) {
                  return false;
               }
            } else if (!this$agreementType.equals(other$agreementType)) {
               return false;
            }

            label395: {
               Object this$agreementStatus = this.getAgreementStatus();
               Object other$agreementStatus = other.getAgreementStatus();
               if (this$agreementStatus == null) {
                  if (other$agreementStatus == null) {
                     break label395;
                  }
               } else if (this$agreementStatus.equals(other$agreementStatus)) {
                  break label395;
               }

               return false;
            }

            Object this$clientNo = this.getClientNo();
            Object other$clientNo = other.getClientNo();
            if (this$clientNo == null) {
               if (other$clientNo != null) {
                  return false;
               }
            } else if (!this$clientNo.equals(other$clientNo)) {
               return false;
            }

            Object this$baseAcctNo = this.getBaseAcctNo();
            Object other$baseAcctNo = other.getBaseAcctNo();
            if (this$baseAcctNo == null) {
               if (other$baseAcctNo != null) {
                  return false;
               }
            } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
               return false;
            }

            label374: {
               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType == null) {
                     break label374;
                  }
               } else if (this$prodType.equals(other$prodType)) {
                  break label374;
               }

               return false;
            }

            label367: {
               Object this$acctCcy = this.getAcctCcy();
               Object other$acctCcy = other.getAcctCcy();
               if (this$acctCcy == null) {
                  if (other$acctCcy == null) {
                     break label367;
                  }
               } else if (this$acctCcy.equals(other$acctCcy)) {
                  break label367;
               }

               return false;
            }

            Object this$acctSeqNo = this.getAcctSeqNo();
            Object other$acctSeqNo = other.getAcctSeqNo();
            if (this$acctSeqNo == null) {
               if (other$acctSeqNo != null) {
                  return false;
               }
            } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
               return false;
            }

            Object this$signDate = this.getSignDate();
            Object other$signDate = other.getSignDate();
            if (this$signDate == null) {
               if (other$signDate != null) {
                  return false;
               }
            } else if (!this$signDate.equals(other$signDate)) {
               return false;
            }

            label346: {
               Object this$signUserId = this.getSignUserId();
               Object other$signUserId = other.getSignUserId();
               if (this$signUserId == null) {
                  if (other$signUserId == null) {
                     break label346;
                  }
               } else if (this$signUserId.equals(other$signUserId)) {
                  break label346;
               }

               return false;
            }

            label339: {
               Object this$signBranch = this.getSignBranch();
               Object other$signBranch = other.getSignBranch();
               if (this$signBranch == null) {
                  if (other$signBranch == null) {
                     break label339;
                  }
               } else if (this$signBranch.equals(other$signBranch)) {
                  break label339;
               }

               return false;
            }

            Object this$autoRenewRollover = this.getAutoRenewRollover();
            Object other$autoRenewRollover = other.getAutoRenewRollover();
            if (this$autoRenewRollover == null) {
               if (other$autoRenewRollover != null) {
                  return false;
               }
            } else if (!this$autoRenewRollover.equals(other$autoRenewRollover)) {
               return false;
            }

            Object this$transferFreq = this.getTransferFreq();
            Object other$transferFreq = other.getTransferFreq();
            if (this$transferFreq == null) {
               if (other$transferFreq != null) {
                  return false;
               }
            } else if (!this$transferFreq.equals(other$transferFreq)) {
               return false;
            }

            label318: {
               Object this$transferFreqType = this.getTransferFreqType();
               Object other$transferFreqType = other.getTransferFreqType();
               if (this$transferFreqType == null) {
                  if (other$transferFreqType == null) {
                     break label318;
                  }
               } else if (this$transferFreqType.equals(other$transferFreqType)) {
                  break label318;
               }

               return false;
            }

            label311: {
               Object this$transferStartDate = this.getTransferStartDate();
               Object other$transferStartDate = other.getTransferStartDate();
               if (this$transferStartDate == null) {
                  if (other$transferStartDate == null) {
                     break label311;
                  }
               } else if (this$transferStartDate.equals(other$transferStartDate)) {
                  break label311;
               }

               return false;
            }

            Object this$transferEndDate = this.getTransferEndDate();
            Object other$transferEndDate = other.getTransferEndDate();
            if (this$transferEndDate == null) {
               if (other$transferEndDate != null) {
                  return false;
               }
            } else if (!this$transferEndDate.equals(other$transferEndDate)) {
               return false;
            }

            label297: {
               Object this$transferDay = this.getTransferDay();
               Object other$transferDay = other.getTransferDay();
               if (this$transferDay == null) {
                  if (other$transferDay == null) {
                     break label297;
                  }
               } else if (this$transferDay.equals(other$transferDay)) {
                  break label297;
               }

               return false;
            }

            Object this$failureTimes = this.getFailureTimes();
            Object other$failureTimes = other.getFailureTimes();
            if (this$failureTimes == null) {
               if (other$failureTimes != null) {
                  return false;
               }
            } else if (!this$failureTimes.equals(other$failureTimes)) {
               return false;
            }

            label283: {
               Object this$failureTotalTimes = this.getFailureTotalTimes();
               Object other$failureTotalTimes = other.getFailureTotalTimes();
               if (this$failureTotalTimes == null) {
                  if (other$failureTotalTimes == null) {
                     break label283;
                  }
               } else if (this$failureTotalTimes.equals(other$failureTotalTimes)) {
                  break label283;
               }

               return false;
            }

            Object this$successTotalTimes = this.getSuccessTotalTimes();
            Object other$successTotalTimes = other.getSuccessTotalTimes();
            if (this$successTotalTimes == null) {
               if (other$successTotalTimes != null) {
                  return false;
               }
            } else if (!this$successTotalTimes.equals(other$successTotalTimes)) {
               return false;
            }

            Object this$successTimes = this.getSuccessTimes();
            Object other$successTimes = other.getSuccessTimes();
            if (this$successTimes == null) {
               if (other$successTimes != null) {
                  return false;
               }
            } else if (!this$successTimes.equals(other$successTimes)) {
               return false;
            }

            label262: {
               Object this$financialAmount = this.getFinancialAmount();
               Object other$financialAmount = other.getFinancialAmount();
               if (this$financialAmount == null) {
                  if (other$financialAmount == null) {
                     break label262;
                  }
               } else if (this$financialAmount.equals(other$financialAmount)) {
                  break label262;
               }

               return false;
            }

            label255: {
               Object this$finFixedAmt = this.getFinFixedAmt();
               Object other$finFixedAmt = other.getFinFixedAmt();
               if (this$finFixedAmt == null) {
                  if (other$finFixedAmt == null) {
                     break label255;
                  }
               } else if (this$finFixedAmt.equals(other$finFixedAmt)) {
                  break label255;
               }

               return false;
            }

            Object this$remainAmt = this.getRemainAmt();
            Object other$remainAmt = other.getRemainAmt();
            if (this$remainAmt == null) {
               if (other$remainAmt != null) {
                  return false;
               }
            } else if (!this$remainAmt.equals(other$remainAmt)) {
               return false;
            }

            Object this$intMinAmt = this.getIntMinAmt();
            Object other$intMinAmt = other.getIntMinAmt();
            if (this$intMinAmt == null) {
               if (other$intMinAmt != null) {
                  return false;
               }
            } else if (!this$intMinAmt.equals(other$intMinAmt)) {
               return false;
            }

            label234: {
               Object this$actualRate = this.getActualRate();
               Object other$actualRate = other.getActualRate();
               if (this$actualRate == null) {
                  if (other$actualRate == null) {
                     break label234;
                  }
               } else if (this$actualRate.equals(other$actualRate)) {
                  break label234;
               }

               return false;
            }

            label227: {
               Object this$sumAmount = this.getSumAmount();
               Object other$sumAmount = other.getSumAmount();
               if (this$sumAmount == null) {
                  if (other$sumAmount == null) {
                     break label227;
                  }
               } else if (this$sumAmount.equals(other$sumAmount)) {
                  break label227;
               }

               return false;
            }

            Object this$failureParamTimes = this.getFailureParamTimes();
            Object other$failureParamTimes = other.getFailureParamTimes();
            if (this$failureParamTimes == null) {
               if (other$failureParamTimes != null) {
                  return false;
               }
            } else if (!this$failureParamTimes.equals(other$failureParamTimes)) {
               return false;
            }

            Object this$settleAcctBalance = this.getSettleAcctBalance();
            Object other$settleAcctBalance = other.getSettleAcctBalance();
            if (this$settleAcctBalance == null) {
               if (other$settleAcctBalance != null) {
                  return false;
               }
            } else if (!this$settleAcctBalance.equals(other$settleAcctBalance)) {
               return false;
            }

            label206: {
               Object this$lastTransferDate = this.getLastTransferDate();
               Object other$lastTransferDate = other.getLastTransferDate();
               if (this$lastTransferDate == null) {
                  if (other$lastTransferDate == null) {
                     break label206;
                  }
               } else if (this$lastTransferDate.equals(other$lastTransferDate)) {
                  break label206;
               }

               return false;
            }

            label199: {
               Object this$term = this.getTerm();
               Object other$term = other.getTerm();
               if (this$term == null) {
                  if (other$term == null) {
                     break label199;
                  }
               } else if (this$term.equals(other$term)) {
                  break label199;
               }

               return false;
            }

            Object this$nextTransferDate = this.getNextTransferDate();
            Object other$nextTransferDate = other.getNextTransferDate();
            if (this$nextTransferDate == null) {
               if (other$nextTransferDate != null) {
                  return false;
               }
            } else if (!this$nextTransferDate.equals(other$nextTransferDate)) {
               return false;
            }

            Object this$termType = this.getTermType();
            Object other$termType = other.getTermType();
            if (this$termType == null) {
               if (other$termType != null) {
                  return false;
               }
            } else if (!this$termType.equals(other$termType)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100030Out;
   }
}
