package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400057406Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400057406Out.IncomeArray> incomeArray;

   public List<Core1400057406Out.IncomeArray> getIncomeArray() {
      return this.incomeArray;
   }

   public void setIncomeArray(List<Core1400057406Out.IncomeArray> incomeArray) {
      this.incomeArray = incomeArray;
   }

   public String toString() {
      return "Core1400057406Out(incomeArray=" + this.getIncomeArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400057406Out)) {
         return false;
      } else {
         Core1400057406Out other = (Core1400057406Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$incomeArray = this.getIncomeArray();
            Object other$incomeArray = other.getIncomeArray();
            if (this$incomeArray == null) {
               if (other$incomeArray != null) {
                  return false;
               }
            } else if (!this$incomeArray.equals(other$incomeArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400057406Out;
   }
   public static class IncomeArray {
      @V(
         desc = "交易日期",
         notNull = false,
         remark = "交易日期"
      )
      private String tranDate;
      @V(
         desc = "交易金额",
         notNull = false,
         length = "17",
         remark = "交易金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal tranAmt;
      @V(
         desc = "法人",
         notNull = false,
         length = "20",
         remark = "法人",
         maxSize = 20
      )
      private String company;

      public String getTranDate() {
         return this.tranDate;
      }

      public BigDecimal getTranAmt() {
         return this.tranAmt;
      }

      public String getCompany() {
         return this.company;
      }

      public void setTranDate(String tranDate) {
         this.tranDate = tranDate;
      }

      public void setTranAmt(BigDecimal tranAmt) {
         this.tranAmt = tranAmt;
      }

      public void setCompany(String company) {
         this.company = company;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400057406Out.IncomeArray)) {
            return false;
         } else {
            Core1400057406Out.IncomeArray other = (Core1400057406Out.IncomeArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label47: {
                  Object this$tranDate = this.getTranDate();
                  Object other$tranDate = other.getTranDate();
                  if (this$tranDate == null) {
                     if (other$tranDate == null) {
                        break label47;
                     }
                  } else if (this$tranDate.equals(other$tranDate)) {
                     break label47;
                  }

                  return false;
               }

               Object this$tranAmt = this.getTranAmt();
               Object other$tranAmt = other.getTranAmt();
               if (this$tranAmt == null) {
                  if (other$tranAmt != null) {
                     return false;
                  }
               } else if (!this$tranAmt.equals(other$tranAmt)) {
                  return false;
               }

               Object this$company = this.getCompany();
               Object other$company = other.getCompany();
               if (this$company == null) {
                  if (other$company != null) {
                     return false;
                  }
               } else if (!this$company.equals(other$company)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400057406Out.IncomeArray;
      }
      public String toString() {
         return "Core1400057406Out.IncomeArray(tranDate=" + this.getTranDate() + ", tranAmt=" + this.getTranAmt() + ", company=" + this.getCompany() + ")";
      }
   }
}
