package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000050In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000050Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000050 {
   String URL = "/rb/inq/acct/address/by/clientNo";


   @ApiRemark("根据客户号查询所有未关闭账户四要素信息以及地址信息（东亚POC）")
   @ApiDesc("根据客户号查询所有未关闭账户四要素信息以及地址信息（东亚POC）")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0050"
   )
   Core14000050Out runService(Core14000050In var1);
}
