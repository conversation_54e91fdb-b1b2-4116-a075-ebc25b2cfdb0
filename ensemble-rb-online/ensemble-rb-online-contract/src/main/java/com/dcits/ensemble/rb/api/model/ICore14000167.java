package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000167In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000167Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000167 {
   String URL = "/rb/inq/blacklist/info";


   @ApiRemark("黑名单操作查询")
   @ApiDesc("黑名单操作查询")
   @EnsembleElements(
      serviceCode = "Core",
      messageType = "1400",
      messageCode = "0167"
   )
   @BusinessCategory("1400-查询")
   @FunctionCategory("RB01-公共服务")
   Core14000167Out runService(Core14000167In var1);
}
