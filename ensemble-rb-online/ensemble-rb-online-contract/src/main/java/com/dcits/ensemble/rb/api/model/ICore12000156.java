package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000156In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000156Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12000156 {
   String URL = "/rb/nfin/card/update/status";


   @ApiRemark("卡凭证入库后更新")
   @ApiDesc("用于提供给ob模块凭证更新后返回更新给rb模块更新")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0156"
   )
   @BusinessCategory("1200-非金融")
   @FunctionCategory("RB20-借记卡")
   @ConsumeSys("CBS")
   @ApiUseStatus("PRODUCT-产品")
   Core12000156Out runService(Core12000156In var1);
}
