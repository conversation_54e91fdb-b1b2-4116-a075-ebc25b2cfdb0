package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000720In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000720Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000720 {
   String URL = "/rb/nfin/password/query";


   @ApiDesc("本接口用于查询密码错误次数以及当天各渠道密码错误信息")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0720"
   )
   @BusinessCategory("1400-查询")
   @FunctionCategory("RB12-单位结算卡")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core14000720Out runService(Core14000720In var1);
}
