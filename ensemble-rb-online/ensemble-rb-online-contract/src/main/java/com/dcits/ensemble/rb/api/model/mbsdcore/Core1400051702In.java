package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400051702In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400051702In.Body body;

   public Core1400051702In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400051702In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400051702In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400051702In)) {
         return false;
      } else {
         Core1400051702In other = (Core1400051702In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400051702In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "客户号",
         notNull = true,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "交易月",
         notNull = false,
         length = "2",
         remark = "交易月",
         maxSize = 2
      )
      private String tranMonth;
      @V(
         desc = "开始月份",
         notNull = true,
         length = "2",
         remark = "开始月份",
         maxSize = 2
      )
      private String startMonth;
      @V(
         desc = "结束月份",
         notNull = true,
         length = "2",
         remark = "结束月份",
         maxSize = 2
      )
      private String endMonth;

      public String getClientNo() {
         return this.clientNo;
      }

      public String getTranMonth() {
         return this.tranMonth;
      }

      public String getStartMonth() {
         return this.startMonth;
      }

      public String getEndMonth() {
         return this.endMonth;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setTranMonth(String tranMonth) {
         this.tranMonth = tranMonth;
      }

      public void setStartMonth(String startMonth) {
         this.startMonth = startMonth;
      }

      public void setEndMonth(String endMonth) {
         this.endMonth = endMonth;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400051702In.Body)) {
            return false;
         } else {
            Core1400051702In.Body other = (Core1400051702In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label59: {
                  Object this$clientNo = this.getClientNo();
                  Object other$clientNo = other.getClientNo();
                  if (this$clientNo == null) {
                     if (other$clientNo == null) {
                        break label59;
                     }
                  } else if (this$clientNo.equals(other$clientNo)) {
                     break label59;
                  }

                  return false;
               }

               Object this$tranMonth = this.getTranMonth();
               Object other$tranMonth = other.getTranMonth();
               if (this$tranMonth == null) {
                  if (other$tranMonth != null) {
                     return false;
                  }
               } else if (!this$tranMonth.equals(other$tranMonth)) {
                  return false;
               }

               Object this$startMonth = this.getStartMonth();
               Object other$startMonth = other.getStartMonth();
               if (this$startMonth == null) {
                  if (other$startMonth != null) {
                     return false;
                  }
               } else if (!this$startMonth.equals(other$startMonth)) {
                  return false;
               }

               Object this$endMonth = this.getEndMonth();
               Object other$endMonth = other.getEndMonth();
               if (this$endMonth == null) {
                  if (other$endMonth != null) {
                     return false;
                  }
               } else if (!this$endMonth.equals(other$endMonth)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400051702In.Body;
      }
      public String toString() {
         return "Core1400051702In.Body(clientNo=" + this.getClientNo() + ", tranMonth=" + this.getTranMonth() + ", startMonth=" + this.getStartMonth() + ", endMonth=" + this.getEndMonth() + ")";
      }
   }
}
