package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000252In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000252Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000252 {
   String URL = "/rb/inq/exchange/exchangeCodeQuery";


   @ApiRemark("结售汇登记列表查询")
   @ApiDesc("供柜面‘结售汇登记列表查询’交易查询")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0252"
   )
   @BusinessCategory("1400-查询")
   @FunctionCategory("RB47-客户账户查询")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core14000252Out runService(Core14000252In var1);
}
