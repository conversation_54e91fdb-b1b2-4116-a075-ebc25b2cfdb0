package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;

@MessageOut
public class Core1400023403Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "总数量",
      notNull = false,
      length = "5",
      remark = "总数量"
   )
   private Integer totalNum;

   public Integer getTotalNum() {
      return this.totalNum;
   }

   public void setTotalNum(Integer totalNum) {
      this.totalNum = totalNum;
   }

   public String toString() {
      return "Core1400023403Out(totalNum=" + this.getTotalNum() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400023403Out)) {
         return false;
      } else {
         Core1400023403Out other = (Core1400023403Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$totalNum = this.getTotalNum();
            Object other$totalNum = other.getTotalNum();
            if (this$totalNum == null) {
               if (other$totalNum != null) {
                  return false;
               }
            } else if (!this$totalNum.equals(other$totalNum)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400023403Out;
   }
}
