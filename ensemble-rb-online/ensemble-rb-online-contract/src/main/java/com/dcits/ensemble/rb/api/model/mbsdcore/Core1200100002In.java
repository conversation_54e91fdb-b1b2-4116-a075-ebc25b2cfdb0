package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;

@MessageIn
public class Core1200100002In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1200100002In.Body body;

   public Core1200100002In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1200100002In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1200100002In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200100002In)) {
         return false;
      } else {
         Core1200100002In other = (Core1200100002In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200100002In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "费用计提编号",
         notNull = false,
         length = "50",
         remark = "费用计提编号",
         maxSize = 50
      )
      private String feeIntNo;
      @V(
         desc = "费用类型",
         notNull = false,
         length = "20",
         remark = "费用类型",
         maxSize = 20
      )
      private String feeType;
      @V(
         desc = "结束计提日期",
         notNull = false,
         remark = "结束计提日期"
      )
      private String endAccrualDate;
      @V(
         desc = "开始计提日期",
         notNull = false,
         remark = "开始计提日期"
      )
      private String startAccrualDate;
      @V(
         desc = "操作类型",
         notNull = true,
         length = "1",
         inDesc = "A-新增,D-解除,U-更新,C-确认,H-交接,O-出库",
         remark = "操作类型",
         maxSize = 1
      )
      private String operateFlag;
      @V(
         desc = "频率",
         notNull = false,
         length = "5",
         inDesc = "DL-日报表,ML-月报表,QL-季报表,HY-半年报表,TQ-前三季度报表,FY-年报表",
         remark = "频率",
         maxSize = 5
      )
      private String freqType;
      @V(
         desc = "原业务编号",
         notNull = false,
         length = "50",
         remark = "原业务编号,如承兑汇票号码,信用证编号等",
         maxSize = 50
      )
      private String extTradeNo;
      @V(
         desc = "利息金额",
         notNull = false,
         length = "17",
         remark = "利息金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal intAmt;
      @V(
         desc = "摘要",
         notNull = false,
         length = "500",
         remark = "开户时的账号用途，销户时的销户原因",
         maxSize = 500
      )
      private String narrative;
      @V(
         desc = "摘要码",
         notNull = false,
         length = "30",
         remark = "摘要码",
         maxSize = 30
      )
      private String narrativeCode;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;

      public String getFeeIntNo() {
         return this.feeIntNo;
      }

      public String getFeeType() {
         return this.feeType;
      }

      public String getEndAccrualDate() {
         return this.endAccrualDate;
      }

      public String getStartAccrualDate() {
         return this.startAccrualDate;
      }

      public String getOperateFlag() {
         return this.operateFlag;
      }

      public String getFreqType() {
         return this.freqType;
      }

      public String getExtTradeNo() {
         return this.extTradeNo;
      }

      public BigDecimal getIntAmt() {
         return this.intAmt;
      }

      public String getNarrative() {
         return this.narrative;
      }

      public String getNarrativeCode() {
         return this.narrativeCode;
      }

      public String getCcy() {
         return this.ccy;
      }

      public void setFeeIntNo(String feeIntNo) {
         this.feeIntNo = feeIntNo;
      }

      public void setFeeType(String feeType) {
         this.feeType = feeType;
      }

      public void setEndAccrualDate(String endAccrualDate) {
         this.endAccrualDate = endAccrualDate;
      }

      public void setStartAccrualDate(String startAccrualDate) {
         this.startAccrualDate = startAccrualDate;
      }

      public void setOperateFlag(String operateFlag) {
         this.operateFlag = operateFlag;
      }

      public void setFreqType(String freqType) {
         this.freqType = freqType;
      }

      public void setExtTradeNo(String extTradeNo) {
         this.extTradeNo = extTradeNo;
      }

      public void setIntAmt(BigDecimal intAmt) {
         this.intAmt = intAmt;
      }

      public void setNarrative(String narrative) {
         this.narrative = narrative;
      }

      public void setNarrativeCode(String narrativeCode) {
         this.narrativeCode = narrativeCode;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1200100002In.Body)) {
            return false;
         } else {
            Core1200100002In.Body other = (Core1200100002In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label143: {
                  Object this$feeIntNo = this.getFeeIntNo();
                  Object other$feeIntNo = other.getFeeIntNo();
                  if (this$feeIntNo == null) {
                     if (other$feeIntNo == null) {
                        break label143;
                     }
                  } else if (this$feeIntNo.equals(other$feeIntNo)) {
                     break label143;
                  }

                  return false;
               }

               Object this$feeType = this.getFeeType();
               Object other$feeType = other.getFeeType();
               if (this$feeType == null) {
                  if (other$feeType != null) {
                     return false;
                  }
               } else if (!this$feeType.equals(other$feeType)) {
                  return false;
               }

               Object this$endAccrualDate = this.getEndAccrualDate();
               Object other$endAccrualDate = other.getEndAccrualDate();
               if (this$endAccrualDate == null) {
                  if (other$endAccrualDate != null) {
                     return false;
                  }
               } else if (!this$endAccrualDate.equals(other$endAccrualDate)) {
                  return false;
               }

               label122: {
                  Object this$startAccrualDate = this.getStartAccrualDate();
                  Object other$startAccrualDate = other.getStartAccrualDate();
                  if (this$startAccrualDate == null) {
                     if (other$startAccrualDate == null) {
                        break label122;
                     }
                  } else if (this$startAccrualDate.equals(other$startAccrualDate)) {
                     break label122;
                  }

                  return false;
               }

               label115: {
                  Object this$operateFlag = this.getOperateFlag();
                  Object other$operateFlag = other.getOperateFlag();
                  if (this$operateFlag == null) {
                     if (other$operateFlag == null) {
                        break label115;
                     }
                  } else if (this$operateFlag.equals(other$operateFlag)) {
                     break label115;
                  }

                  return false;
               }

               Object this$freqType = this.getFreqType();
               Object other$freqType = other.getFreqType();
               if (this$freqType == null) {
                  if (other$freqType != null) {
                     return false;
                  }
               } else if (!this$freqType.equals(other$freqType)) {
                  return false;
               }

               Object this$extTradeNo = this.getExtTradeNo();
               Object other$extTradeNo = other.getExtTradeNo();
               if (this$extTradeNo == null) {
                  if (other$extTradeNo != null) {
                     return false;
                  }
               } else if (!this$extTradeNo.equals(other$extTradeNo)) {
                  return false;
               }

               label94: {
                  Object this$intAmt = this.getIntAmt();
                  Object other$intAmt = other.getIntAmt();
                  if (this$intAmt == null) {
                     if (other$intAmt == null) {
                        break label94;
                     }
                  } else if (this$intAmt.equals(other$intAmt)) {
                     break label94;
                  }

                  return false;
               }

               label87: {
                  Object this$narrative = this.getNarrative();
                  Object other$narrative = other.getNarrative();
                  if (this$narrative == null) {
                     if (other$narrative == null) {
                        break label87;
                     }
                  } else if (this$narrative.equals(other$narrative)) {
                     break label87;
                  }

                  return false;
               }

               Object this$narrativeCode = this.getNarrativeCode();
               Object other$narrativeCode = other.getNarrativeCode();
               if (this$narrativeCode == null) {
                  if (other$narrativeCode != null) {
                     return false;
                  }
               } else if (!this$narrativeCode.equals(other$narrativeCode)) {
                  return false;
               }

               Object this$ccy = this.getCcy();
               Object other$ccy = other.getCcy();
               if (this$ccy == null) {
                  if (other$ccy != null) {
                     return false;
                  }
               } else if (!this$ccy.equals(other$ccy)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1200100002In.Body;
      }
      public String toString() {
         return "Core1200100002In.Body(feeIntNo=" + this.getFeeIntNo() + ", feeType=" + this.getFeeType() + ", endAccrualDate=" + this.getEndAccrualDate() + ", startAccrualDate=" + this.getStartAccrualDate() + ", operateFlag=" + this.getOperateFlag() + ", freqType=" + this.getFreqType() + ", extTradeNo=" + this.getExtTradeNo() + ", intAmt=" + this.getIntAmt() + ", narrative=" + this.getNarrative() + ", narrativeCode=" + this.getNarrativeCode() + ", ccy=" + this.getCcy() + ")";
      }
   }
}
