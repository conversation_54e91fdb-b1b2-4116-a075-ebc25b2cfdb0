package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;

@MessageIn
public class Core1400100021In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400100021In.Body body;

   public Core1400100021In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400100021In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400100021In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100021In)) {
         return false;
      } else {
         Core1400100021In other = (Core1400100021In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100021In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "账号/卡号",
         notNull = true,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "协议类型",
         notNull = false,
         length = "10",
         in = "CLD,DC,DLS,HQB,JDL,KDT,KYD,PCP,WDL,XDB,XDCK,XDL,YBWL,YCD,YHT,ZHY,ZZB,LOA,ZXY,ODF,FIN,NTE,YD,ES,REC,ID,SL,SWP,ACC,PCD,FEE,PKG,SMS,YDT",
         inDesc = "CLD-存立得,DC-大额存单,DLS-贷利省,HQB-活期宝,JDL-加多利,KDT-卡贷通,KYD-卡易贷,PCP-资金池,WDL-稳得利,XDB-协定宝,XDCK-协定存款产品,XDL-先得利,YBWL-一本万利,YCD-英才贷,YDT-易贷通,YHT-一户通,ZHY-周享赢,ZXY-坐享其盈,ZZB-至尊宝,LOA-贷款,ODF-法人透支协议,FIN-卡理财协议,SMS-短信,PKG-费用套餐,FEE-暂不收费,PCD-周期性强制扣划,ACC-协定存款协议,SWP-账户清扫协议,ID-智能存款协议,SL-金额补足协议,REC-回单签约,ES-电票签约,YD-约定,NTE-活期智能存款,PAS-隐私账户签约,BXD-协定利率（无留存）",
         remark = "协议类型",
         maxSize = 10
      )
      private String agreementType;
      @V(
         desc = "收支类型",
         notNull = false,
         length = "10",
         in = "I,E",
         inDesc = "I-收入,E-支出",
         remark = "收支类型，通过此字段判断收入方，还是支出方",
         maxSize = 10
      )
      private String incExpType;
      @V(
         desc = "起始金额",
         notNull = false,
         length = "17",
         remark = "起始金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal startAmt;
      @V(
         desc = "截止金额",
         notNull = false,
         length = "17",
         remark = "截止金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal endAmt;
      @V(
         desc = "交易起始日期",
         notNull = false,
         remark = "交易起始日期"
      )
      private String tranStartDate;
      @V(
         desc = "交易结束日期",
         notNull = false,
         remark = "交易结束日期"
      )
      private String tranEndDate;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getAgreementType() {
         return this.agreementType;
      }

      public String getIncExpType() {
         return this.incExpType;
      }

      public BigDecimal getStartAmt() {
         return this.startAmt;
      }

      public BigDecimal getEndAmt() {
         return this.endAmt;
      }

      public String getTranStartDate() {
         return this.tranStartDate;
      }

      public String getTranEndDate() {
         return this.tranEndDate;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setAgreementType(String agreementType) {
         this.agreementType = agreementType;
      }

      public void setIncExpType(String incExpType) {
         this.incExpType = incExpType;
      }

      public void setStartAmt(BigDecimal startAmt) {
         this.startAmt = startAmt;
      }

      public void setEndAmt(BigDecimal endAmt) {
         this.endAmt = endAmt;
      }

      public void setTranStartDate(String tranStartDate) {
         this.tranStartDate = tranStartDate;
      }

      public void setTranEndDate(String tranEndDate) {
         this.tranEndDate = tranEndDate;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100021In.Body)) {
            return false;
         } else {
            Core1400100021In.Body other = (Core1400100021In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label119: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label119;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label119;
                  }

                  return false;
               }

               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo != null) {
                     return false;
                  }
               } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                  return false;
               }

               label105: {
                  Object this$agreementType = this.getAgreementType();
                  Object other$agreementType = other.getAgreementType();
                  if (this$agreementType == null) {
                     if (other$agreementType == null) {
                        break label105;
                     }
                  } else if (this$agreementType.equals(other$agreementType)) {
                     break label105;
                  }

                  return false;
               }

               Object this$incExpType = this.getIncExpType();
               Object other$incExpType = other.getIncExpType();
               if (this$incExpType == null) {
                  if (other$incExpType != null) {
                     return false;
                  }
               } else if (!this$incExpType.equals(other$incExpType)) {
                  return false;
               }

               label91: {
                  Object this$startAmt = this.getStartAmt();
                  Object other$startAmt = other.getStartAmt();
                  if (this$startAmt == null) {
                     if (other$startAmt == null) {
                        break label91;
                     }
                  } else if (this$startAmt.equals(other$startAmt)) {
                     break label91;
                  }

                  return false;
               }

               Object this$endAmt = this.getEndAmt();
               Object other$endAmt = other.getEndAmt();
               if (this$endAmt == null) {
                  if (other$endAmt != null) {
                     return false;
                  }
               } else if (!this$endAmt.equals(other$endAmt)) {
                  return false;
               }

               label77: {
                  Object this$tranStartDate = this.getTranStartDate();
                  Object other$tranStartDate = other.getTranStartDate();
                  if (this$tranStartDate == null) {
                     if (other$tranStartDate == null) {
                        break label77;
                     }
                  } else if (this$tranStartDate.equals(other$tranStartDate)) {
                     break label77;
                  }

                  return false;
               }

               label70: {
                  Object this$tranEndDate = this.getTranEndDate();
                  Object other$tranEndDate = other.getTranEndDate();
                  if (this$tranEndDate == null) {
                     if (other$tranEndDate == null) {
                        break label70;
                     }
                  } else if (this$tranEndDate.equals(other$tranEndDate)) {
                     break label70;
                  }

                  return false;
               }

               Object this$clientNo = this.getClientNo();
               Object other$clientNo = other.getClientNo();
               if (this$clientNo == null) {
                  if (other$clientNo != null) {
                     return false;
                  }
               } else if (!this$clientNo.equals(other$clientNo)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100021In.Body;
      }
      public String toString() {
         return "Core1400100021In.Body(baseAcctNo=" + this.getBaseAcctNo() + ", acctSeqNo=" + this.getAcctSeqNo() + ", agreementType=" + this.getAgreementType() + ", incExpType=" + this.getIncExpType() + ", startAmt=" + this.getStartAmt() + ", endAmt=" + this.getEndAmt() + ", tranStartDate=" + this.getTranStartDate() + ", tranEndDate=" + this.getTranEndDate() + ", clientNo=" + this.getClientNo() + ")";
      }
   }
}
