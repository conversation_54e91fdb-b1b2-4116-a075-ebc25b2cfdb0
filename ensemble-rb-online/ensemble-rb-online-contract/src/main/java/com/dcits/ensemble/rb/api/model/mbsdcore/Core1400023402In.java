package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400023402In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400023402In.Body body;

   public Core1400023402In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400023402In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400023402In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400023402In)) {
         return false;
      } else {
         Core1400023402In other = (Core1400023402In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400023402In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "所属机构号",
         notNull = false,
         length = "50",
         remark = "所属机构号",
         maxSize = 50
      )
      private String branch;
      @V(
         desc = "票据类型",
         notNull = false,
         length = "5",
         inDesc = "P-纸质,E-电子,CT00-可转让汇票,CT01-不可转让汇票,CT02-现金汇票",
         remark = "票据类型",
         maxSize = 5
      )
      private String billType;
      @V(
         desc = "凭证类型",
         notNull = false,
         length = "10",
         remark = "凭证类型",
         maxSize = 10
      )
      private String docType;
      @V(
         desc = "凭证种类",
         notNull = false,
         length = "3",
         inDesc = "PBK-存折,CHK-支票,DCT-存单,CRD-卡,CFT-存款证明,BNK-银行票据,COL-托收票据,DFT-银行汇票,TCH-旅行支票,BAT-银行承兑汇票,CAT-商业承兑汇票,CHQ-支票,OTH-其他,SCV-印鉴",
         remark = "凭证种类",
         maxSize = 3
      )
      private String docClass;
      @V(
         desc = "票据号码",
         notNull = false,
         length = "50",
         remark = "票据号码",
         maxSize = 50
      )
      private String billNo;
      @V(
         desc = "交易起始日期",
         notNull = false,
         remark = "交易起始日期"
      )
      private String tranStartDate;
      @V(
         desc = "交易结束日期",
         notNull = false,
         remark = "交易结束日期"
      )
      private String tranEndDate;
      @V(
         desc = "过期标志",
         notNull = false,
         length = "10",
         remark = "过期标志",
         maxSize = 10
      )
      private String expireFlag;
      @V(
         desc = "变更操作方式",
         notNull = false,
         length = "2",
         inDesc = "SC-单笔,BC-批量,00-签发录入,01-复核,02-兑付,03-退回,04-挂失,05-解挂,06-签发修改,07-签发删除,11-复核,12-核对,13-移存,14-退回申请,15-挂失,16-解挂",
         remark = "变更操作方式",
         maxSize = 2
      )
      private String operateType;

      public String getBranch() {
         return this.branch;
      }

      public String getBillType() {
         return this.billType;
      }

      public String getDocType() {
         return this.docType;
      }

      public String getDocClass() {
         return this.docClass;
      }

      public String getBillNo() {
         return this.billNo;
      }

      public String getTranStartDate() {
         return this.tranStartDate;
      }

      public String getTranEndDate() {
         return this.tranEndDate;
      }

      public String getExpireFlag() {
         return this.expireFlag;
      }

      public String getOperateType() {
         return this.operateType;
      }

      public void setBranch(String branch) {
         this.branch = branch;
      }

      public void setBillType(String billType) {
         this.billType = billType;
      }

      public void setDocType(String docType) {
         this.docType = docType;
      }

      public void setDocClass(String docClass) {
         this.docClass = docClass;
      }

      public void setBillNo(String billNo) {
         this.billNo = billNo;
      }

      public void setTranStartDate(String tranStartDate) {
         this.tranStartDate = tranStartDate;
      }

      public void setTranEndDate(String tranEndDate) {
         this.tranEndDate = tranEndDate;
      }

      public void setExpireFlag(String expireFlag) {
         this.expireFlag = expireFlag;
      }

      public void setOperateType(String operateType) {
         this.operateType = operateType;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400023402In.Body)) {
            return false;
         } else {
            Core1400023402In.Body other = (Core1400023402In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label119: {
                  Object this$branch = this.getBranch();
                  Object other$branch = other.getBranch();
                  if (this$branch == null) {
                     if (other$branch == null) {
                        break label119;
                     }
                  } else if (this$branch.equals(other$branch)) {
                     break label119;
                  }

                  return false;
               }

               Object this$billType = this.getBillType();
               Object other$billType = other.getBillType();
               if (this$billType == null) {
                  if (other$billType != null) {
                     return false;
                  }
               } else if (!this$billType.equals(other$billType)) {
                  return false;
               }

               label105: {
                  Object this$docType = this.getDocType();
                  Object other$docType = other.getDocType();
                  if (this$docType == null) {
                     if (other$docType == null) {
                        break label105;
                     }
                  } else if (this$docType.equals(other$docType)) {
                     break label105;
                  }

                  return false;
               }

               Object this$docClass = this.getDocClass();
               Object other$docClass = other.getDocClass();
               if (this$docClass == null) {
                  if (other$docClass != null) {
                     return false;
                  }
               } else if (!this$docClass.equals(other$docClass)) {
                  return false;
               }

               label91: {
                  Object this$billNo = this.getBillNo();
                  Object other$billNo = other.getBillNo();
                  if (this$billNo == null) {
                     if (other$billNo == null) {
                        break label91;
                     }
                  } else if (this$billNo.equals(other$billNo)) {
                     break label91;
                  }

                  return false;
               }

               Object this$tranStartDate = this.getTranStartDate();
               Object other$tranStartDate = other.getTranStartDate();
               if (this$tranStartDate == null) {
                  if (other$tranStartDate != null) {
                     return false;
                  }
               } else if (!this$tranStartDate.equals(other$tranStartDate)) {
                  return false;
               }

               label77: {
                  Object this$tranEndDate = this.getTranEndDate();
                  Object other$tranEndDate = other.getTranEndDate();
                  if (this$tranEndDate == null) {
                     if (other$tranEndDate == null) {
                        break label77;
                     }
                  } else if (this$tranEndDate.equals(other$tranEndDate)) {
                     break label77;
                  }

                  return false;
               }

               label70: {
                  Object this$expireFlag = this.getExpireFlag();
                  Object other$expireFlag = other.getExpireFlag();
                  if (this$expireFlag == null) {
                     if (other$expireFlag == null) {
                        break label70;
                     }
                  } else if (this$expireFlag.equals(other$expireFlag)) {
                     break label70;
                  }

                  return false;
               }

               Object this$operateType = this.getOperateType();
               Object other$operateType = other.getOperateType();
               if (this$operateType == null) {
                  if (other$operateType != null) {
                     return false;
                  }
               } else if (!this$operateType.equals(other$operateType)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400023402In.Body;
      }
      public String toString() {
         return "Core1400023402In.Body(branch=" + this.getBranch() + ", billType=" + this.getBillType() + ", docType=" + this.getDocType() + ", docClass=" + this.getDocClass() + ", billNo=" + this.getBillNo() + ", tranStartDate=" + this.getTranStartDate() + ", tranEndDate=" + this.getTranEndDate() + ", expireFlag=" + this.getExpireFlag() + ", operateType=" + this.getOperateType() + ")";
      }
   }
}
