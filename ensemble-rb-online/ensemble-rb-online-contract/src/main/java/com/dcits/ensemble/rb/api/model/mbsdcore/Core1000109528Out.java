package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1000109528Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1000109528Out.ItemArray> itemArray;

   public List<Core1000109528Out.ItemArray> getItemArray() {
      return this.itemArray;
   }

   public void setItemArray(List<Core1000109528Out.ItemArray> itemArray) {
      this.itemArray = itemArray;
   }

   public String toString() {
      return "Core1000109528Out(itemArray=" + this.getItemArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1000109528Out)) {
         return false;
      } else {
         Core1000109528Out other = (Core1000109528Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$itemArray = this.getItemArray();
            Object other$itemArray = other.getItemArray();
            if (this$itemArray == null) {
               if (other$itemArray != null) {
                  return false;
               }
            } else if (!this$itemArray.equals(other$itemArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1000109528Out;
   }
   public static class ItemArray {
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "归集金额",
         notNull = false,
         length = "17",
         remark = "归集金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal upAmt;
      @V(
         desc = "失败原因",
         notNull = false,
         length = "200",
         remark = "失败原因",
         maxSize = 200
      )
      private String failureReason;
      @V(
         desc = "处理结果",
         notNull = false,
         length = "200",
         remark = "处理结果",
         maxSize = 200
      )
      private String dealResult;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public BigDecimal getUpAmt() {
         return this.upAmt;
      }

      public String getFailureReason() {
         return this.failureReason;
      }

      public String getDealResult() {
         return this.dealResult;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setUpAmt(BigDecimal upAmt) {
         this.upAmt = upAmt;
      }

      public void setFailureReason(String failureReason) {
         this.failureReason = failureReason;
      }

      public void setDealResult(String dealResult) {
         this.dealResult = dealResult;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1000109528Out.ItemArray)) {
            return false;
         } else {
            Core1000109528Out.ItemArray other = (Core1000109528Out.ItemArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label71: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label71;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label71;
                  }

                  return false;
               }

               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo != null) {
                     return false;
                  }
               } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                  return false;
               }

               label57: {
                  Object this$upAmt = this.getUpAmt();
                  Object other$upAmt = other.getUpAmt();
                  if (this$upAmt == null) {
                     if (other$upAmt == null) {
                        break label57;
                     }
                  } else if (this$upAmt.equals(other$upAmt)) {
                     break label57;
                  }

                  return false;
               }

               Object this$failureReason = this.getFailureReason();
               Object other$failureReason = other.getFailureReason();
               if (this$failureReason == null) {
                  if (other$failureReason != null) {
                     return false;
                  }
               } else if (!this$failureReason.equals(other$failureReason)) {
                  return false;
               }

               Object this$dealResult = this.getDealResult();
               Object other$dealResult = other.getDealResult();
               if (this$dealResult == null) {
                  if (other$dealResult == null) {
                     return true;
                  }
               } else if (this$dealResult.equals(other$dealResult)) {
                  return true;
               }

               return false;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1000109528Out.ItemArray;
      }
      public String toString() {
         return "Core1000109528Out.ItemArray(baseAcctNo=" + this.getBaseAcctNo() + ", acctSeqNo=" + this.getAcctSeqNo() + ", upAmt=" + this.getUpAmt() + ", failureReason=" + this.getFailureReason() + ", dealResult=" + this.getDealResult() + ")";
      }
   }
}
