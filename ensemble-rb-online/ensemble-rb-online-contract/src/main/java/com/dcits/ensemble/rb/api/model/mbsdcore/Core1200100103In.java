package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1200100103In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1200100103In.Body body;

   public Core1200100103In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1200100103In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1200100103In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200100103In)) {
         return false;
      } else {
         Core1200100103In other = (Core1200100103In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200100103In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "大额存单账户",
         notNull = true,
         length = "50",
         remark = "大额存单账户",
         maxSize = 50
      )
      private String dcBaseAcctNo;
      @V(
         desc = "大额存单账户序号",
         notNull = true,
         length = "5",
         remark = "大额存单账户序号",
         maxSize = 5
      )
      private String dcAcctSeqNo;
      @V(
         desc = "保证金账户序号",
         notNull = true,
         length = "5",
         remark = "保证金账户序号",
         maxSize = 5
      )
      private String bondAcctSeqNo;
      @V(
         desc = "保证金账号",
         notNull = true,
         length = "50",
         remark = "保证金账号",
         maxSize = 50
      )
      private String bondAcctNo;
      @V(
         desc = "是否为子账户续开标志",
         notNull = true,
         length = "1",
         inDesc = "Y-是,N-否",
         remark = "是否为子账户续开标志",
         maxSize = 1
      )
      private String isSubOpen;
      @V(
         desc = "是否续冻",
         notNull = true,
         length = "1",
         inDesc = "Y-是,N-否",
         remark = "是否续冻",
         maxSize = 1
      )
      private String isFrozen;
      @V(
         desc = "开始日期",
         notNull = false,
         remark = "开始日期"
      )
      private String startDate;
      @V(
         desc = "限制类型",
         notNull = false,
         length = "3",
         remark = "限制类型",
         maxSize = 3
      )
      private String restraintType;
      @V(
         desc = "结束日期",
         notNull = true,
         remark = "结束日期"
      )
      private String endDate;
      @V(
         desc = "限制编号",
         notNull = false,
         length = "50",
         remark = "限制编号",
         maxSize = 50
      )
      private String resSeqNo;

      public String getDcBaseAcctNo() {
         return this.dcBaseAcctNo;
      }

      public String getDcAcctSeqNo() {
         return this.dcAcctSeqNo;
      }

      public String getBondAcctSeqNo() {
         return this.bondAcctSeqNo;
      }

      public String getBondAcctNo() {
         return this.bondAcctNo;
      }

      public String getIsSubOpen() {
         return this.isSubOpen;
      }

      public String getIsFrozen() {
         return this.isFrozen;
      }

      public String getStartDate() {
         return this.startDate;
      }

      public String getRestraintType() {
         return this.restraintType;
      }

      public String getEndDate() {
         return this.endDate;
      }

      public String getResSeqNo() {
         return this.resSeqNo;
      }

      public void setDcBaseAcctNo(String dcBaseAcctNo) {
         this.dcBaseAcctNo = dcBaseAcctNo;
      }

      public void setDcAcctSeqNo(String dcAcctSeqNo) {
         this.dcAcctSeqNo = dcAcctSeqNo;
      }

      public void setBondAcctSeqNo(String bondAcctSeqNo) {
         this.bondAcctSeqNo = bondAcctSeqNo;
      }

      public void setBondAcctNo(String bondAcctNo) {
         this.bondAcctNo = bondAcctNo;
      }

      public void setIsSubOpen(String isSubOpen) {
         this.isSubOpen = isSubOpen;
      }

      public void setIsFrozen(String isFrozen) {
         this.isFrozen = isFrozen;
      }

      public void setStartDate(String startDate) {
         this.startDate = startDate;
      }

      public void setRestraintType(String restraintType) {
         this.restraintType = restraintType;
      }

      public void setEndDate(String endDate) {
         this.endDate = endDate;
      }

      public void setResSeqNo(String resSeqNo) {
         this.resSeqNo = resSeqNo;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1200100103In.Body)) {
            return false;
         } else {
            Core1200100103In.Body other = (Core1200100103In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$dcBaseAcctNo = this.getDcBaseAcctNo();
               Object other$dcBaseAcctNo = other.getDcBaseAcctNo();
               if (this$dcBaseAcctNo == null) {
                  if (other$dcBaseAcctNo != null) {
                     return false;
                  }
               } else if (!this$dcBaseAcctNo.equals(other$dcBaseAcctNo)) {
                  return false;
               }

               Object this$dcAcctSeqNo = this.getDcAcctSeqNo();
               Object other$dcAcctSeqNo = other.getDcAcctSeqNo();
               if (this$dcAcctSeqNo == null) {
                  if (other$dcAcctSeqNo != null) {
                     return false;
                  }
               } else if (!this$dcAcctSeqNo.equals(other$dcAcctSeqNo)) {
                  return false;
               }

               Object this$bondAcctSeqNo = this.getBondAcctSeqNo();
               Object other$bondAcctSeqNo = other.getBondAcctSeqNo();
               if (this$bondAcctSeqNo == null) {
                  if (other$bondAcctSeqNo != null) {
                     return false;
                  }
               } else if (!this$bondAcctSeqNo.equals(other$bondAcctSeqNo)) {
                  return false;
               }

               label110: {
                  Object this$bondAcctNo = this.getBondAcctNo();
                  Object other$bondAcctNo = other.getBondAcctNo();
                  if (this$bondAcctNo == null) {
                     if (other$bondAcctNo == null) {
                        break label110;
                     }
                  } else if (this$bondAcctNo.equals(other$bondAcctNo)) {
                     break label110;
                  }

                  return false;
               }

               label103: {
                  Object this$isSubOpen = this.getIsSubOpen();
                  Object other$isSubOpen = other.getIsSubOpen();
                  if (this$isSubOpen == null) {
                     if (other$isSubOpen == null) {
                        break label103;
                     }
                  } else if (this$isSubOpen.equals(other$isSubOpen)) {
                     break label103;
                  }

                  return false;
               }

               Object this$isFrozen = this.getIsFrozen();
               Object other$isFrozen = other.getIsFrozen();
               if (this$isFrozen == null) {
                  if (other$isFrozen != null) {
                     return false;
                  }
               } else if (!this$isFrozen.equals(other$isFrozen)) {
                  return false;
               }

               label89: {
                  Object this$startDate = this.getStartDate();
                  Object other$startDate = other.getStartDate();
                  if (this$startDate == null) {
                     if (other$startDate == null) {
                        break label89;
                     }
                  } else if (this$startDate.equals(other$startDate)) {
                     break label89;
                  }

                  return false;
               }

               label82: {
                  Object this$restraintType = this.getRestraintType();
                  Object other$restraintType = other.getRestraintType();
                  if (this$restraintType == null) {
                     if (other$restraintType == null) {
                        break label82;
                     }
                  } else if (this$restraintType.equals(other$restraintType)) {
                     break label82;
                  }

                  return false;
               }

               Object this$endDate = this.getEndDate();
               Object other$endDate = other.getEndDate();
               if (this$endDate == null) {
                  if (other$endDate != null) {
                     return false;
                  }
               } else if (!this$endDate.equals(other$endDate)) {
                  return false;
               }

               Object this$resSeqNo = this.getResSeqNo();
               Object other$resSeqNo = other.getResSeqNo();
               if (this$resSeqNo == null) {
                  if (other$resSeqNo != null) {
                     return false;
                  }
               } else if (!this$resSeqNo.equals(other$resSeqNo)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1200100103In.Body;
      }
      public String toString() {
         return "Core1200100103In.Body(dcBaseAcctNo=" + this.getDcBaseAcctNo() + ", dcAcctSeqNo=" + this.getDcAcctSeqNo() + ", bondAcctSeqNo=" + this.getBondAcctSeqNo() + ", bondAcctNo=" + this.getBondAcctNo() + ", isSubOpen=" + this.getIsSubOpen() + ", isFrozen=" + this.getIsFrozen() + ", startDate=" + this.getStartDate() + ", restraintType=" + this.getRestraintType() + ", endDate=" + this.getEndDate() + ", resSeqNo=" + this.getResSeqNo() + ")";
      }
   }
}
