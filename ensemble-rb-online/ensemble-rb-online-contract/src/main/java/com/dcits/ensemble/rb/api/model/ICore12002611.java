package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12002611In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12002611Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12002611 {
   String URL = "/rb/nfin/northbound/unsign";


   @ApiRemark("北向通解约")
   @ApiDesc("北向通解约")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "2611"
   )
   @BusinessCategory("存款")
   @FunctionCategory("RB05-协议管理")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core12002611Out runService(Core12002611In var1);
}
