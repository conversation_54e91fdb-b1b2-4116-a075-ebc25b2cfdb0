package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400032401Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400032401Out.AdvanceDetailArray> advanceDetailArray;

   public List<Core1400032401Out.AdvanceDetailArray> getAdvanceDetailArray() {
      return this.advanceDetailArray;
   }

   public void setAdvanceDetailArray(List<Core1400032401Out.AdvanceDetailArray> advanceDetailArray) {
      this.advanceDetailArray = advanceDetailArray;
   }

   public String toString() {
      return "Core1400032401Out(advanceDetailArray=" + this.getAdvanceDetailArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400032401Out)) {
         return false;
      } else {
         Core1400032401Out other = (Core1400032401Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$advanceDetailArray = this.getAdvanceDetailArray();
            Object other$advanceDetailArray = other.getAdvanceDetailArray();
            if (this$advanceDetailArray == null) {
               if (other$advanceDetailArray != null) {
                  return false;
               }
            } else if (!this$advanceDetailArray.equals(other$advanceDetailArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400032401Out;
   }
   public static class AdvanceDetailArray {
      @V(
         desc = "保证金冻结序号",
         notNull = false,
         length = "50",
         remark = "保证金冻结序号",
         maxSize = 50
      )
      private String bondResSeqNo;
      @V(
         desc = "序号",
         notNull = false,
         length = "50",
         remark = "序号",
         maxSize = 50
      )
      private String seqNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "账户内部键值",
         notNull = false,
         length = "15",
         remark = "账户内部键值"
      )
      private Long internalKey;
      @V(
         desc = "结算账户类别",
         notNull = false,
         length = "1",
         in = "R,I,N:Nostro 往帐账户,V:Vostro 往来帐账户,C:Card Account 卡号",
         remark = "结算账户类别",
         maxSize = 1
      )
      private String settleAcctCategory;
      @V(
         desc = "账户余额",
         notNull = false,
         length = "17",
         remark = "账户余额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal acctBalance;
      @V(
         desc = "交易日期",
         notNull = false,
         remark = "交易日期"
      )
      private String tranDate;
      @V(
         desc = "所属机构号",
         notNull = false,
         length = "50",
         remark = "机构代码",
         maxSize = 50
      )
      private String branch;
      @V(
         desc = "业务流水号",
         notNull = false,
         length = "50",
         remark = "支付流水号",
         maxSize = 50
      )
      private String serialNo;
      @V(
         desc = "银承合同编号",
         notNull = false,
         length = "50",
         remark = "银行承兑汇票协议编号",
         maxSize = 50
      )
      private String acceptContractNo;
      @V(
         desc = "交易参考号",
         notNull = false,
         length = "50",
         remark = "交易参考号",
         maxSize = 50
      )
      private String reference;
      @V(
         desc = "需清算金额",
         notNull = false,
         length = "17",
         remark = "需清算金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal needSettleAmt;
      @V(
         desc = "总金额",
         notNull = false,
         length = "17",
         remark = "总金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal totalAmt;
      @V(
         desc = "已备款金额",
         notNull = false,
         length = "17",
         remark = "已备款金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal realSettleAmt;
      @V(
         desc = "票据类型",
         notNull = false,
         length = "5",
         in = "P,E",
         remark = "票据类型",
         maxSize = 5
      )
      private String billType;
      @V(
         desc = "合同号",
         notNull = false,
         length = "50",
         remark = "合同号",
         maxSize = 50
      )
      private String contractNo;
      @V(
         desc = "借据号",
         notNull = false,
         length = "50",
         remark = "借据号",
         maxSize = 50
      )
      private String cmisloanNo;
      @V(
         desc = "票据号码",
         notNull = false,
         length = "50",
         remark = "票据号码",
         maxSize = 50
      )
      private String billNo;
      @V(
         desc = "备款状态描述",
         notNull = false,
         length = "50",
         remark = "备款状态描述",
         maxSize = 50
      )
      private String settleStatus;
      @V(
         desc = "票据状态",
         notNull = false,
         length = "2",
         in = "00,01,02,03,04,05,06",
         remark = "票据状态",
         maxSize = 2
      )
      private String billStatus;
      @V(
         desc = "票据到期日",
         notNull = false,
         remark = "票据到期日"
      )
      private String billMaturityDate;
      @V(
         desc = "最后出票人名称",
         notNull = false,
         length = "200",
         remark = "最后出票人名称",
         maxSize = 200
      )
      private String lastHolderNeme;
      @V(
         desc = "交易时间戳",
         notNull = false,
         length = "26",
         remark = "交易时间戳",
         maxSize = 26
      )
      private String tranTimestamp;
      @V(
         desc = "法人",
         notNull = false,
         length = "20",
         remark = "法人",
         maxSize = 20
      )
      private String company;

      public String getBondResSeqNo() {
         return this.bondResSeqNo;
      }

      public String getSeqNo() {
         return this.seqNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public Long getInternalKey() {
         return this.internalKey;
      }

      public String getSettleAcctCategory() {
         return this.settleAcctCategory;
      }

      public BigDecimal getAcctBalance() {
         return this.acctBalance;
      }

      public String getTranDate() {
         return this.tranDate;
      }

      public String getBranch() {
         return this.branch;
      }

      public String getSerialNo() {
         return this.serialNo;
      }

      public String getAcceptContractNo() {
         return this.acceptContractNo;
      }

      public String getReference() {
         return this.reference;
      }

      public BigDecimal getNeedSettleAmt() {
         return this.needSettleAmt;
      }

      public BigDecimal getTotalAmt() {
         return this.totalAmt;
      }

      public BigDecimal getRealSettleAmt() {
         return this.realSettleAmt;
      }

      public String getBillType() {
         return this.billType;
      }

      public String getContractNo() {
         return this.contractNo;
      }

      public String getCmisloanNo() {
         return this.cmisloanNo;
      }

      public String getBillNo() {
         return this.billNo;
      }

      public String getSettleStatus() {
         return this.settleStatus;
      }

      public String getBillStatus() {
         return this.billStatus;
      }

      public String getBillMaturityDate() {
         return this.billMaturityDate;
      }

      public String getLastHolderNeme() {
         return this.lastHolderNeme;
      }

      public String getTranTimestamp() {
         return this.tranTimestamp;
      }

      public String getCompany() {
         return this.company;
      }

      public void setBondResSeqNo(String bondResSeqNo) {
         this.bondResSeqNo = bondResSeqNo;
      }

      public void setSeqNo(String seqNo) {
         this.seqNo = seqNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setInternalKey(Long internalKey) {
         this.internalKey = internalKey;
      }

      public void setSettleAcctCategory(String settleAcctCategory) {
         this.settleAcctCategory = settleAcctCategory;
      }

      public void setAcctBalance(BigDecimal acctBalance) {
         this.acctBalance = acctBalance;
      }

      public void setTranDate(String tranDate) {
         this.tranDate = tranDate;
      }

      public void setBranch(String branch) {
         this.branch = branch;
      }

      public void setSerialNo(String serialNo) {
         this.serialNo = serialNo;
      }

      public void setAcceptContractNo(String acceptContractNo) {
         this.acceptContractNo = acceptContractNo;
      }

      public void setReference(String reference) {
         this.reference = reference;
      }

      public void setNeedSettleAmt(BigDecimal needSettleAmt) {
         this.needSettleAmt = needSettleAmt;
      }

      public void setTotalAmt(BigDecimal totalAmt) {
         this.totalAmt = totalAmt;
      }

      public void setRealSettleAmt(BigDecimal realSettleAmt) {
         this.realSettleAmt = realSettleAmt;
      }

      public void setBillType(String billType) {
         this.billType = billType;
      }

      public void setContractNo(String contractNo) {
         this.contractNo = contractNo;
      }

      public void setCmisloanNo(String cmisloanNo) {
         this.cmisloanNo = cmisloanNo;
      }

      public void setBillNo(String billNo) {
         this.billNo = billNo;
      }

      public void setSettleStatus(String settleStatus) {
         this.settleStatus = settleStatus;
      }

      public void setBillStatus(String billStatus) {
         this.billStatus = billStatus;
      }

      public void setBillMaturityDate(String billMaturityDate) {
         this.billMaturityDate = billMaturityDate;
      }

      public void setLastHolderNeme(String lastHolderNeme) {
         this.lastHolderNeme = lastHolderNeme;
      }

      public void setTranTimestamp(String tranTimestamp) {
         this.tranTimestamp = tranTimestamp;
      }

      public void setCompany(String company) {
         this.company = company;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400032401Out.AdvanceDetailArray)) {
            return false;
         } else {
            Core1400032401Out.AdvanceDetailArray other = (Core1400032401Out.AdvanceDetailArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label335: {
                  Object this$bondResSeqNo = this.getBondResSeqNo();
                  Object other$bondResSeqNo = other.getBondResSeqNo();
                  if (this$bondResSeqNo == null) {
                     if (other$bondResSeqNo == null) {
                        break label335;
                     }
                  } else if (this$bondResSeqNo.equals(other$bondResSeqNo)) {
                     break label335;
                  }

                  return false;
               }

               Object this$seqNo = this.getSeqNo();
               Object other$seqNo = other.getSeqNo();
               if (this$seqNo == null) {
                  if (other$seqNo != null) {
                     return false;
                  }
               } else if (!this$seqNo.equals(other$seqNo)) {
                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               label314: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label314;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label314;
                  }

                  return false;
               }

               label307: {
                  Object this$acctCcy = this.getAcctCcy();
                  Object other$acctCcy = other.getAcctCcy();
                  if (this$acctCcy == null) {
                     if (other$acctCcy == null) {
                        break label307;
                     }
                  } else if (this$acctCcy.equals(other$acctCcy)) {
                     break label307;
                  }

                  return false;
               }

               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo != null) {
                     return false;
                  }
               } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                  return false;
               }

               Object this$internalKey = this.getInternalKey();
               Object other$internalKey = other.getInternalKey();
               if (this$internalKey == null) {
                  if (other$internalKey != null) {
                     return false;
                  }
               } else if (!this$internalKey.equals(other$internalKey)) {
                  return false;
               }

               label286: {
                  Object this$settleAcctCategory = this.getSettleAcctCategory();
                  Object other$settleAcctCategory = other.getSettleAcctCategory();
                  if (this$settleAcctCategory == null) {
                     if (other$settleAcctCategory == null) {
                        break label286;
                     }
                  } else if (this$settleAcctCategory.equals(other$settleAcctCategory)) {
                     break label286;
                  }

                  return false;
               }

               label279: {
                  Object this$acctBalance = this.getAcctBalance();
                  Object other$acctBalance = other.getAcctBalance();
                  if (this$acctBalance == null) {
                     if (other$acctBalance == null) {
                        break label279;
                     }
                  } else if (this$acctBalance.equals(other$acctBalance)) {
                     break label279;
                  }

                  return false;
               }

               Object this$tranDate = this.getTranDate();
               Object other$tranDate = other.getTranDate();
               if (this$tranDate == null) {
                  if (other$tranDate != null) {
                     return false;
                  }
               } else if (!this$tranDate.equals(other$tranDate)) {
                  return false;
               }

               label265: {
                  Object this$branch = this.getBranch();
                  Object other$branch = other.getBranch();
                  if (this$branch == null) {
                     if (other$branch == null) {
                        break label265;
                     }
                  } else if (this$branch.equals(other$branch)) {
                     break label265;
                  }

                  return false;
               }

               Object this$serialNo = this.getSerialNo();
               Object other$serialNo = other.getSerialNo();
               if (this$serialNo == null) {
                  if (other$serialNo != null) {
                     return false;
                  }
               } else if (!this$serialNo.equals(other$serialNo)) {
                  return false;
               }

               label251: {
                  Object this$acceptContractNo = this.getAcceptContractNo();
                  Object other$acceptContractNo = other.getAcceptContractNo();
                  if (this$acceptContractNo == null) {
                     if (other$acceptContractNo == null) {
                        break label251;
                     }
                  } else if (this$acceptContractNo.equals(other$acceptContractNo)) {
                     break label251;
                  }

                  return false;
               }

               Object this$reference = this.getReference();
               Object other$reference = other.getReference();
               if (this$reference == null) {
                  if (other$reference != null) {
                     return false;
                  }
               } else if (!this$reference.equals(other$reference)) {
                  return false;
               }

               Object this$needSettleAmt = this.getNeedSettleAmt();
               Object other$needSettleAmt = other.getNeedSettleAmt();
               if (this$needSettleAmt == null) {
                  if (other$needSettleAmt != null) {
                     return false;
                  }
               } else if (!this$needSettleAmt.equals(other$needSettleAmt)) {
                  return false;
               }

               label230: {
                  Object this$totalAmt = this.getTotalAmt();
                  Object other$totalAmt = other.getTotalAmt();
                  if (this$totalAmt == null) {
                     if (other$totalAmt == null) {
                        break label230;
                     }
                  } else if (this$totalAmt.equals(other$totalAmt)) {
                     break label230;
                  }

                  return false;
               }

               label223: {
                  Object this$realSettleAmt = this.getRealSettleAmt();
                  Object other$realSettleAmt = other.getRealSettleAmt();
                  if (this$realSettleAmt == null) {
                     if (other$realSettleAmt == null) {
                        break label223;
                     }
                  } else if (this$realSettleAmt.equals(other$realSettleAmt)) {
                     break label223;
                  }

                  return false;
               }

               Object this$billType = this.getBillType();
               Object other$billType = other.getBillType();
               if (this$billType == null) {
                  if (other$billType != null) {
                     return false;
                  }
               } else if (!this$billType.equals(other$billType)) {
                  return false;
               }

               Object this$contractNo = this.getContractNo();
               Object other$contractNo = other.getContractNo();
               if (this$contractNo == null) {
                  if (other$contractNo != null) {
                     return false;
                  }
               } else if (!this$contractNo.equals(other$contractNo)) {
                  return false;
               }

               label202: {
                  Object this$cmisloanNo = this.getCmisloanNo();
                  Object other$cmisloanNo = other.getCmisloanNo();
                  if (this$cmisloanNo == null) {
                     if (other$cmisloanNo == null) {
                        break label202;
                     }
                  } else if (this$cmisloanNo.equals(other$cmisloanNo)) {
                     break label202;
                  }

                  return false;
               }

               label195: {
                  Object this$billNo = this.getBillNo();
                  Object other$billNo = other.getBillNo();
                  if (this$billNo == null) {
                     if (other$billNo == null) {
                        break label195;
                     }
                  } else if (this$billNo.equals(other$billNo)) {
                     break label195;
                  }

                  return false;
               }

               Object this$settleStatus = this.getSettleStatus();
               Object other$settleStatus = other.getSettleStatus();
               if (this$settleStatus == null) {
                  if (other$settleStatus != null) {
                     return false;
                  }
               } else if (!this$settleStatus.equals(other$settleStatus)) {
                  return false;
               }

               Object this$billStatus = this.getBillStatus();
               Object other$billStatus = other.getBillStatus();
               if (this$billStatus == null) {
                  if (other$billStatus != null) {
                     return false;
                  }
               } else if (!this$billStatus.equals(other$billStatus)) {
                  return false;
               }

               label174: {
                  Object this$billMaturityDate = this.getBillMaturityDate();
                  Object other$billMaturityDate = other.getBillMaturityDate();
                  if (this$billMaturityDate == null) {
                     if (other$billMaturityDate == null) {
                        break label174;
                     }
                  } else if (this$billMaturityDate.equals(other$billMaturityDate)) {
                     break label174;
                  }

                  return false;
               }

               label167: {
                  Object this$lastHolderNeme = this.getLastHolderNeme();
                  Object other$lastHolderNeme = other.getLastHolderNeme();
                  if (this$lastHolderNeme == null) {
                     if (other$lastHolderNeme == null) {
                        break label167;
                     }
                  } else if (this$lastHolderNeme.equals(other$lastHolderNeme)) {
                     break label167;
                  }

                  return false;
               }

               Object this$tranTimestamp = this.getTranTimestamp();
               Object other$tranTimestamp = other.getTranTimestamp();
               if (this$tranTimestamp == null) {
                  if (other$tranTimestamp != null) {
                     return false;
                  }
               } else if (!this$tranTimestamp.equals(other$tranTimestamp)) {
                  return false;
               }

               Object this$company = this.getCompany();
               Object other$company = other.getCompany();
               if (this$company == null) {
                  if (other$company != null) {
                     return false;
                  }
               } else if (!this$company.equals(other$company)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400032401Out.AdvanceDetailArray;
      }
      public String toString() {
         return "Core1400032401Out.AdvanceDetailArray(bondResSeqNo=" + this.getBondResSeqNo() + ", seqNo=" + this.getSeqNo() + ", prodType=" + this.getProdType() + ", baseAcctNo=" + this.getBaseAcctNo() + ", acctCcy=" + this.getAcctCcy() + ", acctSeqNo=" + this.getAcctSeqNo() + ", internalKey=" + this.getInternalKey() + ", settleAcctCategory=" + this.getSettleAcctCategory() + ", acctBalance=" + this.getAcctBalance() + ", tranDate=" + this.getTranDate() + ", branch=" + this.getBranch() + ", serialNo=" + this.getSerialNo() + ", acceptContractNo=" + this.getAcceptContractNo() + ", reference=" + this.getReference() + ", needSettleAmt=" + this.getNeedSettleAmt() + ", totalAmt=" + this.getTotalAmt() + ", realSettleAmt=" + this.getRealSettleAmt() + ", billType=" + this.getBillType() + ", contractNo=" + this.getContractNo() + ", cmisloanNo=" + this.getCmisloanNo() + ", billNo=" + this.getBillNo() + ", settleStatus=" + this.getSettleStatus() + ", billStatus=" + this.getBillStatus() + ", billMaturityDate=" + this.getBillMaturityDate() + ", lastHolderNeme=" + this.getLastHolderNeme() + ", tranTimestamp=" + this.getTranTimestamp() + ", company=" + this.getCompany() + ")";
      }
   }
}
