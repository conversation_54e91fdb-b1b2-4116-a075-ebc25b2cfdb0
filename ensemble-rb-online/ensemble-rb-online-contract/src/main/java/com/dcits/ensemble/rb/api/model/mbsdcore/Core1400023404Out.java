package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400023404Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400023404Out.Array> array;

   public List<Core1400023404Out.Array> getArray() {
      return this.array;
   }

   public void setArray(List<Core1400023404Out.Array> array) {
      this.array = array;
   }

   public String toString() {
      return "Core1400023404Out(array=" + this.getArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400023404Out)) {
         return false;
      } else {
         Core1400023404Out other = (Core1400023404Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$array = this.getArray();
            Object other$array = other.getArray();
            if (this$array == null) {
               if (other$array != null) {
                  return false;
               }
            } else if (!this$array.equals(other$array)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400023404Out;
   }
   public static class Array {
      @V(
         desc = "挂失类型",
         notNull = false,
         length = "3",
         remark = "挂失类型",
         maxSize = 3
      )
      private String lostType;
      @V(
         desc = "票据签发机构",
         notNull = false,
         length = "50",
         remark = "签发机构",
         maxSize = 50
      )
      private String billSignBranch;
      @V(
         desc = "本票类型",
         notNull = false,
         length = "1",
         remark = "本票类型",
         maxSize = 1
      )
      private String billCategory;
      @V(
         desc = "票据类型",
         notNull = false,
         length = "5",
         remark = "票据类型",
         maxSize = 5
      )
      private String billType;
      @V(
         desc = "票据号码",
         notNull = false,
         length = "50",
         remark = "票据号码",
         maxSize = 50
      )
      private String billNo;
      @V(
         desc = "出票金额",
         notNull = false,
         length = "17",
         remark = "出票金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal billTranAmt;
      @V(
         desc = "交易日期",
         notNull = false,
         remark = "交易日期"
      )
      private String tranDate;
      @V(
         desc = "通知书编号",
         notNull = false,
         length = "50",
         remark = "通知书编号",
         maxSize = 50
      )
      private String adviceNoteNo;
      @V(
         desc = "执法人1证件号码",
         notNull = false,
         length = "50",
         remark = "执法人1证件号码",
         maxSize = 50
      )
      private String judiciaryDocumentId;
      @V(
         desc = "执法人1证件号码2",
         notNull = false,
         length = "50",
         remark = "执法人1证件号码2",
         maxSize = 50
      )
      private String judiciaryDocumentId2;
      @V(
         desc = "执法人1证件类型",
         notNull = false,
         length = "3",
         remark = "执法人1证件类型",
         maxSize = 3
      )
      private String judiciaryDocumentType;
      @V(
         desc = "执法人1证件类型2",
         notNull = false,
         length = "3",
         remark = "执法人1证件类型2",
         maxSize = 3
      )
      private String judiciaryDocumentType2;
      @V(
         desc = "执法人1姓名",
         notNull = false,
         length = "200",
         remark = "执法人1姓名",
         maxSize = 200
      )
      private String judiciaryOfficerName;
      @V(
         desc = "执法人2证件号码",
         notNull = false,
         length = "50",
         remark = "执法人2证件号码",
         maxSize = 50
      )
      private String judiciaryOthDocumentId;
      @V(
         desc = "执法人2证件号码2",
         notNull = false,
         length = "50",
         remark = "执法人2证件号码2",
         maxSize = 50
      )
      private String judiciaryOthDocumentId2;
      @V(
         desc = "执法人2证件类型",
         notNull = false,
         length = "3",
         remark = "执法人2证件类型",
         maxSize = 3
      )
      private String judiciaryOthDocumentType;
      @V(
         desc = "执法人2证件类型2",
         notNull = false,
         length = "3",
         remark = "执法人2证件类型2",
         maxSize = 3
      )
      private String judiciaryOthDocumentType2;
      @V(
         desc = "执法人2姓名",
         notNull = false,
         length = "200",
         remark = "执法人2姓名",
         maxSize = 200
      )
      private String judiciaryOthOfficerName;
      @V(
         desc = "挂失起始日期",
         notNull = false,
         remark = "挂失起始日期"
      )
      private String lostBeginDate;
      @V(
         desc = "挂失原因",
         notNull = false,
         length = "200",
         remark = "挂失原因",
         maxSize = 200
      )
      private String lostReason;
      @V(
         desc = "解挂原因",
         notNull = false,
         length = "200",
         remark = "解挂原因",
         maxSize = 200
      )
      private String unlostReason;
      @V(
         desc = "解挂时间",
         notNull = false,
         length = "26",
         remark = "解挂时间",
         maxSize = 26
      )
      private String unlostTime;
      @V(
         desc = "解挂经办人",
         notNull = false,
         length = "200",
         remark = "解挂经办人",
         maxSize = 200
      )
      private String unlostCommissionName;
      @V(
         desc = "挂失申请书编号",
         notNull = false,
         length = "50",
         remark = "挂失申请书编号",
         maxSize = 50
      )
      private String lossNo;
      @V(
         desc = "申请人名称",
         notNull = false,
         length = "200",
         remark = "申请人名称",
         maxSize = 200
      )
      private String applyerAcctName;
      @V(
         desc = "备注",
         notNull = false,
         length = "200",
         remark = "备注",
         maxSize = 200
      )
      private String remark;
      @V(
         desc = "挂失柜员",
         notNull = false,
         length = "30",
         remark = "挂失柜员",
         maxSize = 30
      )
      private String lostUserId;
      @V(
         desc = "解挂柜员",
         notNull = false,
         length = "30",
         remark = "解挂柜员",
         maxSize = 30
      )
      private String unlostUserId;
      @V(
         desc = "解挂机构",
         notNull = false,
         length = "50",
         remark = "解挂机构",
         maxSize = 50
      )
      private String unchainBranch;
      @V(
         desc = "挂失截至日期",
         notNull = false,
         remark = "挂失截至日期"
      )
      private String lostEndDate;
      @V(
         desc = "挂失状态",
         notNull = false,
         length = "1",
         remark = "挂失状态",
         maxSize = 1
      )
      private String lostStatus;

      public String getLostType() {
         return this.lostType;
      }

      public String getBillSignBranch() {
         return this.billSignBranch;
      }

      public String getBillCategory() {
         return this.billCategory;
      }

      public String getBillType() {
         return this.billType;
      }

      public String getBillNo() {
         return this.billNo;
      }

      public BigDecimal getBillTranAmt() {
         return this.billTranAmt;
      }

      public String getTranDate() {
         return this.tranDate;
      }

      public String getAdviceNoteNo() {
         return this.adviceNoteNo;
      }

      public String getJudiciaryDocumentId() {
         return this.judiciaryDocumentId;
      }

      public String getJudiciaryDocumentId2() {
         return this.judiciaryDocumentId2;
      }

      public String getJudiciaryDocumentType() {
         return this.judiciaryDocumentType;
      }

      public String getJudiciaryDocumentType2() {
         return this.judiciaryDocumentType2;
      }

      public String getJudiciaryOfficerName() {
         return this.judiciaryOfficerName;
      }

      public String getJudiciaryOthDocumentId() {
         return this.judiciaryOthDocumentId;
      }

      public String getJudiciaryOthDocumentId2() {
         return this.judiciaryOthDocumentId2;
      }

      public String getJudiciaryOthDocumentType() {
         return this.judiciaryOthDocumentType;
      }

      public String getJudiciaryOthDocumentType2() {
         return this.judiciaryOthDocumentType2;
      }

      public String getJudiciaryOthOfficerName() {
         return this.judiciaryOthOfficerName;
      }

      public String getLostBeginDate() {
         return this.lostBeginDate;
      }

      public String getLostReason() {
         return this.lostReason;
      }

      public String getUnlostReason() {
         return this.unlostReason;
      }

      public String getUnlostTime() {
         return this.unlostTime;
      }

      public String getUnlostCommissionName() {
         return this.unlostCommissionName;
      }

      public String getLossNo() {
         return this.lossNo;
      }

      public String getApplyerAcctName() {
         return this.applyerAcctName;
      }

      public String getRemark() {
         return this.remark;
      }

      public String getLostUserId() {
         return this.lostUserId;
      }

      public String getUnlostUserId() {
         return this.unlostUserId;
      }

      public String getUnchainBranch() {
         return this.unchainBranch;
      }

      public String getLostEndDate() {
         return this.lostEndDate;
      }

      public String getLostStatus() {
         return this.lostStatus;
      }

      public void setLostType(String lostType) {
         this.lostType = lostType;
      }

      public void setBillSignBranch(String billSignBranch) {
         this.billSignBranch = billSignBranch;
      }

      public void setBillCategory(String billCategory) {
         this.billCategory = billCategory;
      }

      public void setBillType(String billType) {
         this.billType = billType;
      }

      public void setBillNo(String billNo) {
         this.billNo = billNo;
      }

      public void setBillTranAmt(BigDecimal billTranAmt) {
         this.billTranAmt = billTranAmt;
      }

      public void setTranDate(String tranDate) {
         this.tranDate = tranDate;
      }

      public void setAdviceNoteNo(String adviceNoteNo) {
         this.adviceNoteNo = adviceNoteNo;
      }

      public void setJudiciaryDocumentId(String judiciaryDocumentId) {
         this.judiciaryDocumentId = judiciaryDocumentId;
      }

      public void setJudiciaryDocumentId2(String judiciaryDocumentId2) {
         this.judiciaryDocumentId2 = judiciaryDocumentId2;
      }

      public void setJudiciaryDocumentType(String judiciaryDocumentType) {
         this.judiciaryDocumentType = judiciaryDocumentType;
      }

      public void setJudiciaryDocumentType2(String judiciaryDocumentType2) {
         this.judiciaryDocumentType2 = judiciaryDocumentType2;
      }

      public void setJudiciaryOfficerName(String judiciaryOfficerName) {
         this.judiciaryOfficerName = judiciaryOfficerName;
      }

      public void setJudiciaryOthDocumentId(String judiciaryOthDocumentId) {
         this.judiciaryOthDocumentId = judiciaryOthDocumentId;
      }

      public void setJudiciaryOthDocumentId2(String judiciaryOthDocumentId2) {
         this.judiciaryOthDocumentId2 = judiciaryOthDocumentId2;
      }

      public void setJudiciaryOthDocumentType(String judiciaryOthDocumentType) {
         this.judiciaryOthDocumentType = judiciaryOthDocumentType;
      }

      public void setJudiciaryOthDocumentType2(String judiciaryOthDocumentType2) {
         this.judiciaryOthDocumentType2 = judiciaryOthDocumentType2;
      }

      public void setJudiciaryOthOfficerName(String judiciaryOthOfficerName) {
         this.judiciaryOthOfficerName = judiciaryOthOfficerName;
      }

      public void setLostBeginDate(String lostBeginDate) {
         this.lostBeginDate = lostBeginDate;
      }

      public void setLostReason(String lostReason) {
         this.lostReason = lostReason;
      }

      public void setUnlostReason(String unlostReason) {
         this.unlostReason = unlostReason;
      }

      public void setUnlostTime(String unlostTime) {
         this.unlostTime = unlostTime;
      }

      public void setUnlostCommissionName(String unlostCommissionName) {
         this.unlostCommissionName = unlostCommissionName;
      }

      public void setLossNo(String lossNo) {
         this.lossNo = lossNo;
      }

      public void setApplyerAcctName(String applyerAcctName) {
         this.applyerAcctName = applyerAcctName;
      }

      public void setRemark(String remark) {
         this.remark = remark;
      }

      public void setLostUserId(String lostUserId) {
         this.lostUserId = lostUserId;
      }

      public void setUnlostUserId(String unlostUserId) {
         this.unlostUserId = unlostUserId;
      }

      public void setUnchainBranch(String unchainBranch) {
         this.unchainBranch = unchainBranch;
      }

      public void setLostEndDate(String lostEndDate) {
         this.lostEndDate = lostEndDate;
      }

      public void setLostStatus(String lostStatus) {
         this.lostStatus = lostStatus;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400023404Out.Array)) {
            return false;
         } else {
            Core1400023404Out.Array other = (Core1400023404Out.Array)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label383: {
                  Object this$lostType = this.getLostType();
                  Object other$lostType = other.getLostType();
                  if (this$lostType == null) {
                     if (other$lostType == null) {
                        break label383;
                     }
                  } else if (this$lostType.equals(other$lostType)) {
                     break label383;
                  }

                  return false;
               }

               Object this$billSignBranch = this.getBillSignBranch();
               Object other$billSignBranch = other.getBillSignBranch();
               if (this$billSignBranch == null) {
                  if (other$billSignBranch != null) {
                     return false;
                  }
               } else if (!this$billSignBranch.equals(other$billSignBranch)) {
                  return false;
               }

               Object this$billCategory = this.getBillCategory();
               Object other$billCategory = other.getBillCategory();
               if (this$billCategory == null) {
                  if (other$billCategory != null) {
                     return false;
                  }
               } else if (!this$billCategory.equals(other$billCategory)) {
                  return false;
               }

               label362: {
                  Object this$billType = this.getBillType();
                  Object other$billType = other.getBillType();
                  if (this$billType == null) {
                     if (other$billType == null) {
                        break label362;
                     }
                  } else if (this$billType.equals(other$billType)) {
                     break label362;
                  }

                  return false;
               }

               label355: {
                  Object this$billNo = this.getBillNo();
                  Object other$billNo = other.getBillNo();
                  if (this$billNo == null) {
                     if (other$billNo == null) {
                        break label355;
                     }
                  } else if (this$billNo.equals(other$billNo)) {
                     break label355;
                  }

                  return false;
               }

               Object this$billTranAmt = this.getBillTranAmt();
               Object other$billTranAmt = other.getBillTranAmt();
               if (this$billTranAmt == null) {
                  if (other$billTranAmt != null) {
                     return false;
                  }
               } else if (!this$billTranAmt.equals(other$billTranAmt)) {
                  return false;
               }

               Object this$tranDate = this.getTranDate();
               Object other$tranDate = other.getTranDate();
               if (this$tranDate == null) {
                  if (other$tranDate != null) {
                     return false;
                  }
               } else if (!this$tranDate.equals(other$tranDate)) {
                  return false;
               }

               label334: {
                  Object this$adviceNoteNo = this.getAdviceNoteNo();
                  Object other$adviceNoteNo = other.getAdviceNoteNo();
                  if (this$adviceNoteNo == null) {
                     if (other$adviceNoteNo == null) {
                        break label334;
                     }
                  } else if (this$adviceNoteNo.equals(other$adviceNoteNo)) {
                     break label334;
                  }

                  return false;
               }

               label327: {
                  Object this$judiciaryDocumentId = this.getJudiciaryDocumentId();
                  Object other$judiciaryDocumentId = other.getJudiciaryDocumentId();
                  if (this$judiciaryDocumentId == null) {
                     if (other$judiciaryDocumentId == null) {
                        break label327;
                     }
                  } else if (this$judiciaryDocumentId.equals(other$judiciaryDocumentId)) {
                     break label327;
                  }

                  return false;
               }

               Object this$judiciaryDocumentId2 = this.getJudiciaryDocumentId2();
               Object other$judiciaryDocumentId2 = other.getJudiciaryDocumentId2();
               if (this$judiciaryDocumentId2 == null) {
                  if (other$judiciaryDocumentId2 != null) {
                     return false;
                  }
               } else if (!this$judiciaryDocumentId2.equals(other$judiciaryDocumentId2)) {
                  return false;
               }

               label313: {
                  Object this$judiciaryDocumentType = this.getJudiciaryDocumentType();
                  Object other$judiciaryDocumentType = other.getJudiciaryDocumentType();
                  if (this$judiciaryDocumentType == null) {
                     if (other$judiciaryDocumentType == null) {
                        break label313;
                     }
                  } else if (this$judiciaryDocumentType.equals(other$judiciaryDocumentType)) {
                     break label313;
                  }

                  return false;
               }

               Object this$judiciaryDocumentType2 = this.getJudiciaryDocumentType2();
               Object other$judiciaryDocumentType2 = other.getJudiciaryDocumentType2();
               if (this$judiciaryDocumentType2 == null) {
                  if (other$judiciaryDocumentType2 != null) {
                     return false;
                  }
               } else if (!this$judiciaryDocumentType2.equals(other$judiciaryDocumentType2)) {
                  return false;
               }

               label299: {
                  Object this$judiciaryOfficerName = this.getJudiciaryOfficerName();
                  Object other$judiciaryOfficerName = other.getJudiciaryOfficerName();
                  if (this$judiciaryOfficerName == null) {
                     if (other$judiciaryOfficerName == null) {
                        break label299;
                     }
                  } else if (this$judiciaryOfficerName.equals(other$judiciaryOfficerName)) {
                     break label299;
                  }

                  return false;
               }

               Object this$judiciaryOthDocumentId = this.getJudiciaryOthDocumentId();
               Object other$judiciaryOthDocumentId = other.getJudiciaryOthDocumentId();
               if (this$judiciaryOthDocumentId == null) {
                  if (other$judiciaryOthDocumentId != null) {
                     return false;
                  }
               } else if (!this$judiciaryOthDocumentId.equals(other$judiciaryOthDocumentId)) {
                  return false;
               }

               Object this$judiciaryOthDocumentId2 = this.getJudiciaryOthDocumentId2();
               Object other$judiciaryOthDocumentId2 = other.getJudiciaryOthDocumentId2();
               if (this$judiciaryOthDocumentId2 == null) {
                  if (other$judiciaryOthDocumentId2 != null) {
                     return false;
                  }
               } else if (!this$judiciaryOthDocumentId2.equals(other$judiciaryOthDocumentId2)) {
                  return false;
               }

               label278: {
                  Object this$judiciaryOthDocumentType = this.getJudiciaryOthDocumentType();
                  Object other$judiciaryOthDocumentType = other.getJudiciaryOthDocumentType();
                  if (this$judiciaryOthDocumentType == null) {
                     if (other$judiciaryOthDocumentType == null) {
                        break label278;
                     }
                  } else if (this$judiciaryOthDocumentType.equals(other$judiciaryOthDocumentType)) {
                     break label278;
                  }

                  return false;
               }

               label271: {
                  Object this$judiciaryOthDocumentType2 = this.getJudiciaryOthDocumentType2();
                  Object other$judiciaryOthDocumentType2 = other.getJudiciaryOthDocumentType2();
                  if (this$judiciaryOthDocumentType2 == null) {
                     if (other$judiciaryOthDocumentType2 == null) {
                        break label271;
                     }
                  } else if (this$judiciaryOthDocumentType2.equals(other$judiciaryOthDocumentType2)) {
                     break label271;
                  }

                  return false;
               }

               Object this$judiciaryOthOfficerName = this.getJudiciaryOthOfficerName();
               Object other$judiciaryOthOfficerName = other.getJudiciaryOthOfficerName();
               if (this$judiciaryOthOfficerName == null) {
                  if (other$judiciaryOthOfficerName != null) {
                     return false;
                  }
               } else if (!this$judiciaryOthOfficerName.equals(other$judiciaryOthOfficerName)) {
                  return false;
               }

               Object this$lostBeginDate = this.getLostBeginDate();
               Object other$lostBeginDate = other.getLostBeginDate();
               if (this$lostBeginDate == null) {
                  if (other$lostBeginDate != null) {
                     return false;
                  }
               } else if (!this$lostBeginDate.equals(other$lostBeginDate)) {
                  return false;
               }

               label250: {
                  Object this$lostReason = this.getLostReason();
                  Object other$lostReason = other.getLostReason();
                  if (this$lostReason == null) {
                     if (other$lostReason == null) {
                        break label250;
                     }
                  } else if (this$lostReason.equals(other$lostReason)) {
                     break label250;
                  }

                  return false;
               }

               label243: {
                  Object this$unlostReason = this.getUnlostReason();
                  Object other$unlostReason = other.getUnlostReason();
                  if (this$unlostReason == null) {
                     if (other$unlostReason == null) {
                        break label243;
                     }
                  } else if (this$unlostReason.equals(other$unlostReason)) {
                     break label243;
                  }

                  return false;
               }

               Object this$unlostTime = this.getUnlostTime();
               Object other$unlostTime = other.getUnlostTime();
               if (this$unlostTime == null) {
                  if (other$unlostTime != null) {
                     return false;
                  }
               } else if (!this$unlostTime.equals(other$unlostTime)) {
                  return false;
               }

               Object this$unlostCommissionName = this.getUnlostCommissionName();
               Object other$unlostCommissionName = other.getUnlostCommissionName();
               if (this$unlostCommissionName == null) {
                  if (other$unlostCommissionName != null) {
                     return false;
                  }
               } else if (!this$unlostCommissionName.equals(other$unlostCommissionName)) {
                  return false;
               }

               label222: {
                  Object this$lossNo = this.getLossNo();
                  Object other$lossNo = other.getLossNo();
                  if (this$lossNo == null) {
                     if (other$lossNo == null) {
                        break label222;
                     }
                  } else if (this$lossNo.equals(other$lossNo)) {
                     break label222;
                  }

                  return false;
               }

               label215: {
                  Object this$applyerAcctName = this.getApplyerAcctName();
                  Object other$applyerAcctName = other.getApplyerAcctName();
                  if (this$applyerAcctName == null) {
                     if (other$applyerAcctName == null) {
                        break label215;
                     }
                  } else if (this$applyerAcctName.equals(other$applyerAcctName)) {
                     break label215;
                  }

                  return false;
               }

               Object this$remark = this.getRemark();
               Object other$remark = other.getRemark();
               if (this$remark == null) {
                  if (other$remark != null) {
                     return false;
                  }
               } else if (!this$remark.equals(other$remark)) {
                  return false;
               }

               label201: {
                  Object this$lostUserId = this.getLostUserId();
                  Object other$lostUserId = other.getLostUserId();
                  if (this$lostUserId == null) {
                     if (other$lostUserId == null) {
                        break label201;
                     }
                  } else if (this$lostUserId.equals(other$lostUserId)) {
                     break label201;
                  }

                  return false;
               }

               Object this$unlostUserId = this.getUnlostUserId();
               Object other$unlostUserId = other.getUnlostUserId();
               if (this$unlostUserId == null) {
                  if (other$unlostUserId != null) {
                     return false;
                  }
               } else if (!this$unlostUserId.equals(other$unlostUserId)) {
                  return false;
               }

               label187: {
                  Object this$unchainBranch = this.getUnchainBranch();
                  Object other$unchainBranch = other.getUnchainBranch();
                  if (this$unchainBranch == null) {
                     if (other$unchainBranch == null) {
                        break label187;
                     }
                  } else if (this$unchainBranch.equals(other$unchainBranch)) {
                     break label187;
                  }

                  return false;
               }

               Object this$lostEndDate = this.getLostEndDate();
               Object other$lostEndDate = other.getLostEndDate();
               if (this$lostEndDate == null) {
                  if (other$lostEndDate != null) {
                     return false;
                  }
               } else if (!this$lostEndDate.equals(other$lostEndDate)) {
                  return false;
               }

               Object this$lostStatus = this.getLostStatus();
               Object other$lostStatus = other.getLostStatus();
               if (this$lostStatus == null) {
                  if (other$lostStatus != null) {
                     return false;
                  }
               } else if (!this$lostStatus.equals(other$lostStatus)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400023404Out.Array;
      }
      public String toString() {
         return "Core1400023404Out.Array(lostType=" + this.getLostType() + ", billSignBranch=" + this.getBillSignBranch() + ", billCategory=" + this.getBillCategory() + ", billType=" + this.getBillType() + ", billNo=" + this.getBillNo() + ", billTranAmt=" + this.getBillTranAmt() + ", tranDate=" + this.getTranDate() + ", adviceNoteNo=" + this.getAdviceNoteNo() + ", judiciaryDocumentId=" + this.getJudiciaryDocumentId() + ", judiciaryDocumentId2=" + this.getJudiciaryDocumentId2() + ", judiciaryDocumentType=" + this.getJudiciaryDocumentType() + ", judiciaryDocumentType2=" + this.getJudiciaryDocumentType2() + ", judiciaryOfficerName=" + this.getJudiciaryOfficerName() + ", judiciaryOthDocumentId=" + this.getJudiciaryOthDocumentId() + ", judiciaryOthDocumentId2=" + this.getJudiciaryOthDocumentId2() + ", judiciaryOthDocumentType=" + this.getJudiciaryOthDocumentType() + ", judiciaryOthDocumentType2=" + this.getJudiciaryOthDocumentType2() + ", judiciaryOthOfficerName=" + this.getJudiciaryOthOfficerName() + ", lostBeginDate=" + this.getLostBeginDate() + ", lostReason=" + this.getLostReason() + ", unlostReason=" + this.getUnlostReason() + ", unlostTime=" + this.getUnlostTime() + ", unlostCommissionName=" + this.getUnlostCommissionName() + ", lossNo=" + this.getLossNo() + ", applyerAcctName=" + this.getApplyerAcctName() + ", remark=" + this.getRemark() + ", lostUserId=" + this.getLostUserId() + ", unlostUserId=" + this.getUnlostUserId() + ", unchainBranch=" + this.getUnchainBranch() + ", lostEndDate=" + this.getLostEndDate() + ", lostStatus=" + this.getLostStatus() + ")";
      }
   }
}
