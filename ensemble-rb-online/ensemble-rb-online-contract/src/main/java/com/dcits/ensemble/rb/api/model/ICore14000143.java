package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000143In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000143Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000143 {
   String URL = "/rb/inq/tran/updatelist";


   @ApiRemark("交易历史查询")
   @ApiDesc("交易历史查询")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0143"
   )
   @BusinessCategory("交易历史查询")
   @FunctionCategory("RB48-登记簿查询")
   @ConsumeSys("137")
   @ApiUseStatus("PRODUCT-产品")
   Core14000143Out runService(Core14000143In var1);
}
