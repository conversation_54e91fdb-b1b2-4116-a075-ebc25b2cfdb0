package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000210In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000210Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12000210 {
   String URL = "/rb/nfin/term/agre/modify";


   @ApiDesc("本接口用于协议存款签约之后，对协议利率进行维护")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0210"
   )
   @BusinessCategory("1200-非金融")
   @FunctionCategory("RB02-账户管理")
   @ConsumeSys("EOS")
   @ApiUseStatus("PRODUCT-产品")
   Core12000210Out runService(Core12000210In var1);
}
