package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12209415In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12209415Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12209415 {
   String URL = "/rb/inq/highRisk/excel";


   @ApiRemark("高风险历史交易文件（报表）")
   @ApiDesc("东亚POC功能")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1220",
      messageCode = "9415"
   )
   @BusinessCategory("1220-文件")
   @FunctionCategory("RB08-特殊业务")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core12209415Out runService(Core12209415In var1);
}
