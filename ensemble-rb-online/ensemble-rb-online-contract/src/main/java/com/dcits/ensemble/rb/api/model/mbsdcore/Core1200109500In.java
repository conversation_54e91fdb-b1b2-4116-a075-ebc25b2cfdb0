package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@MessageIn
public class Core1200109500In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1200109500In.Body body;

   public Core1200109500In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1200109500In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1200109500In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200109500In)) {
         return false;
      } else {
         Core1200109500In other = (Core1200109500In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200109500In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "账号/卡号",
         notNull = true,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "账户序号",
         notNull = true,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "资金来源账号",
         notNull = true,
         length = "30",
         remark = "登记资金来源账号",
         maxSize = 30
      )
      private String fundFromAcctNo;
      @V(
         desc = "交易金额",
         notNull = true,
         length = "17",
         remark = "交易金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal tranAmt;
      @V(
         desc = "数组",
         notNull = false,
         remark = "数组"
      )
      private List<Core1200109500In.Body.HangArray> hangArray;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getFundFromAcctNo() {
         return this.fundFromAcctNo;
      }

      public BigDecimal getTranAmt() {
         return this.tranAmt;
      }

      public List<Core1200109500In.Body.HangArray> getHangArray() {
         return this.hangArray;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setFundFromAcctNo(String fundFromAcctNo) {
         this.fundFromAcctNo = fundFromAcctNo;
      }

      public void setTranAmt(BigDecimal tranAmt) {
         this.tranAmt = tranAmt;
      }

      public void setHangArray(List<Core1200109500In.Body.HangArray> hangArray) {
         this.hangArray = hangArray;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1200109500In.Body)) {
            return false;
         } else {
            Core1200109500In.Body other = (Core1200109500In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label95: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label95;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label95;
                  }

                  return false;
               }

               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo != null) {
                     return false;
                  }
               } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                  return false;
               }

               Object this$fundFromAcctNo = this.getFundFromAcctNo();
               Object other$fundFromAcctNo = other.getFundFromAcctNo();
               if (this$fundFromAcctNo == null) {
                  if (other$fundFromAcctNo != null) {
                     return false;
                  }
               } else if (!this$fundFromAcctNo.equals(other$fundFromAcctNo)) {
                  return false;
               }

               label74: {
                  Object this$tranAmt = this.getTranAmt();
                  Object other$tranAmt = other.getTranAmt();
                  if (this$tranAmt == null) {
                     if (other$tranAmt == null) {
                        break label74;
                     }
                  } else if (this$tranAmt.equals(other$tranAmt)) {
                     break label74;
                  }

                  return false;
               }

               label67: {
                  Object this$hangArray = this.getHangArray();
                  Object other$hangArray = other.getHangArray();
                  if (this$hangArray == null) {
                     if (other$hangArray == null) {
                        break label67;
                     }
                  } else if (this$hangArray.equals(other$hangArray)) {
                     break label67;
                  }

                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               Object this$acctCcy = this.getAcctCcy();
               Object other$acctCcy = other.getAcctCcy();
               if (this$acctCcy == null) {
                  if (other$acctCcy != null) {
                     return false;
                  }
               } else if (!this$acctCcy.equals(other$acctCcy)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1200109500In.Body;
      }
      public String toString() {
         return "Core1200109500In.Body(baseAcctNo=" + this.getBaseAcctNo() + ", acctSeqNo=" + this.getAcctSeqNo() + ", fundFromAcctNo=" + this.getFundFromAcctNo() + ", tranAmt=" + this.getTranAmt() + ", hangArray=" + this.getHangArray() + ", prodType=" + this.getProdType() + ", acctCcy=" + this.getAcctCcy() + ")";
      }

      public static class HangArray {
         @V(
            desc = "追加挂账子序号",
            notNull = false,
            length = "50",
            remark = "追加挂账子序号",
            maxSize = 50
         )
         private String subHangSeqNo;
         @V(
            desc = "挂账序列号",
            notNull = false,
            length = "50",
            remark = "挂账账户序列号",
            maxSize = 50
         )
         private String hangSeqNo;
         @V(
            desc = "交易金额",
            notNull = false,
            length = "17",
            remark = "交易金额",
            decimalLength = 2,
            precision = 2
         )
         private BigDecimal tranAmt;

         public String getSubHangSeqNo() {
            return this.subHangSeqNo;
         }

         public String getHangSeqNo() {
            return this.hangSeqNo;
         }

         public BigDecimal getTranAmt() {
            return this.tranAmt;
         }

         public void setSubHangSeqNo(String subHangSeqNo) {
            this.subHangSeqNo = subHangSeqNo;
         }

         public void setHangSeqNo(String hangSeqNo) {
            this.hangSeqNo = hangSeqNo;
         }

         public void setTranAmt(BigDecimal tranAmt) {
            this.tranAmt = tranAmt;
         }

         public boolean equals(Object o) {
            if (o == this) {
               return true;
            } else if (!(o instanceof Core1200109500In.Body.HangArray)) {
               return false;
            } else {
               Core1200109500In.Body.HangArray other = (Core1200109500In.Body.HangArray)o;
               if (!other.canEqual(this)) {
                  return false;
               } else {
                  label47: {
                     Object this$subHangSeqNo = this.getSubHangSeqNo();
                     Object other$subHangSeqNo = other.getSubHangSeqNo();
                     if (this$subHangSeqNo == null) {
                        if (other$subHangSeqNo == null) {
                           break label47;
                        }
                     } else if (this$subHangSeqNo.equals(other$subHangSeqNo)) {
                        break label47;
                     }

                     return false;
                  }

                  Object this$hangSeqNo = this.getHangSeqNo();
                  Object other$hangSeqNo = other.getHangSeqNo();
                  if (this$hangSeqNo == null) {
                     if (other$hangSeqNo != null) {
                        return false;
                     }
                  } else if (!this$hangSeqNo.equals(other$hangSeqNo)) {
                     return false;
                  }

                  Object this$tranAmt = this.getTranAmt();
                  Object other$tranAmt = other.getTranAmt();
                  if (this$tranAmt == null) {
                     if (other$tranAmt != null) {
                        return false;
                     }
                  } else if (!this$tranAmt.equals(other$tranAmt)) {
                     return false;
                  }

                  return true;
               }
            }
         }

         protected boolean canEqual(Object other) {
            return other instanceof Core1200109500In.Body.HangArray;
         }
         public String toString() {
            return "Core1200109500In.Body.HangArray(subHangSeqNo=" + this.getSubHangSeqNo() + ", hangSeqNo=" + this.getHangSeqNo() + ", tranAmt=" + this.getTranAmt() + ")";
         }
      }
   }
}
