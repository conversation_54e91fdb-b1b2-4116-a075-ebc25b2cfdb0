package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class MdsdCore12006699In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private MdsdCore12006699In.Body body;

   public MdsdCore12006699In.Body getBody() {
      return this.body;
   }

   public void setBody(MdsdCore12006699In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "MdsdCore12006699In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof MdsdCore12006699In)) {
         return false;
      } else {
         MdsdCore12006699In other = (MdsdCore12006699In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof MdsdCore12006699In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "客户名称",
         notNull = false,
         length = "200",
         remark = "客户名称",
         maxSize = 200
      )
      private String clientName;
      @V(
         desc = "原客户名称",
         notNull = false,
         length = "200",
         remark = "原客户名称",
         maxSize = 200
      )
      private String oldClientName;

      public String getClientNo() {
         return this.clientNo;
      }

      public String getClientName() {
         return this.clientName;
      }

      public String getOldClientName() {
         return this.oldClientName;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setClientName(String clientName) {
         this.clientName = clientName;
      }

      public void setOldClientName(String oldClientName) {
         this.oldClientName = oldClientName;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof MdsdCore12006699In.Body)) {
            return false;
         } else {
            MdsdCore12006699In.Body other = (MdsdCore12006699In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label47: {
                  Object this$clientNo = this.getClientNo();
                  Object other$clientNo = other.getClientNo();
                  if (this$clientNo == null) {
                     if (other$clientNo == null) {
                        break label47;
                     }
                  } else if (this$clientNo.equals(other$clientNo)) {
                     break label47;
                  }

                  return false;
               }

               Object this$clientName = this.getClientName();
               Object other$clientName = other.getClientName();
               if (this$clientName == null) {
                  if (other$clientName != null) {
                     return false;
                  }
               } else if (!this$clientName.equals(other$clientName)) {
                  return false;
               }

               Object this$oldClientName = this.getOldClientName();
               Object other$oldClientName = other.getOldClientName();
               if (this$oldClientName == null) {
                  if (other$oldClientName != null) {
                     return false;
                  }
               } else if (!this$oldClientName.equals(other$oldClientName)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof MdsdCore12006699In.Body;
      }
      public String toString() {
         return "MdsdCore12006699In.Body(clientNo=" + this.getClientNo() + ", clientName=" + this.getClientName() + ", oldClientName=" + this.getOldClientName() + ")";
      }
   }
}
