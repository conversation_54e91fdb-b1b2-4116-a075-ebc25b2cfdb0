package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10000325In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10000325Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore10000325 {
   String URL = "/rb/fin/bab/compensate";


   @ApiRemark("保函保证金备款记账")
   @ApiDesc("用于保函保证金备款记账，按顺序扣划出保证金冻结金额，不足则扣结算账户，仍不足则进行垫款")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "0325"
   )
   @BusinessCategory("1000-金融")
   @FunctionCategory("RB03-金融交易")
   @ConsumeSys("CMS")
   @ApiUseStatus("PRODUCT-产品")
   Core10000325Out runService(Core10000325In var1);
}
