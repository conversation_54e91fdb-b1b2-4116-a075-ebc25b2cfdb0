package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12009402In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12009402Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12009402 {
   String URL = "/rb/nfin/inner/term/base/open";


   @ApiRemark("华兴需求")
   @ApiDesc("用于开立存放同业定期一本通主账户")
   @BusinessCategory("1200-非金融")
   @FunctionCategory("RB15-内部账")
   @ConsumeSys("TLE/137")
   @ApiUseStatus("PRODUCT-产品")
   Core12009402Out runService(Core12009402In var1);
}
