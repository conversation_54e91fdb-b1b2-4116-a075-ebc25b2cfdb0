package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14006010In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14006010Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14006010 {
   String URL = "/rb/inq/card/single/self";

   
   @ApiRemark("标准优化")
   @ApiDesc("自编卡号信息查询")
   @EnsembleElements(
      serviceCode = "Core",
      messageType = "1400",
      messageCode = "6010"
   )
   @FunctionCategory("RB20-借记卡")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core14006010Out runService(Core14006010In var1);
}
