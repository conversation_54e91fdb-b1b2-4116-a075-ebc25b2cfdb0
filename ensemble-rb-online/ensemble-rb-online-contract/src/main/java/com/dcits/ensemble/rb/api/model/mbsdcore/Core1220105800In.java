package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;

@MessageIn
public class Core1220105800In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1220105800In.Body body;

   public Core1220105800In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1220105800In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1220105800In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1220105800In)) {
         return false;
      } else {
         Core1220105800In other = (Core1220105800In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1220105800In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "开始日期",
         notNull = true,
         remark = "开始日期"
      )
      private String startDate;
      @V(
         desc = "结束日期",
         notNull = true,
         remark = "结束日期"
      )
      private String endDate;
      @V(
         desc = "最小金额",
         notNull = false,
         length = "17",
         remark = "最小金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal minAmt;
      @V(
         desc = "最大金额",
         notNull = false,
         length = "17",
         remark = "最大金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal maxAmt;
      @V(
         desc = "对方账号/卡号",
         notNull = false,
         length = "50",
         remark = "对方账号/卡号",
         maxSize = 50
      )
      private String othBaseAcctNo;
      @V(
         desc = "对方账户名称",
         notNull = false,
         length = "200",
         remark = "对方账户名称",
         maxSize = 200
      )
      private String othAcctName;
      @V(
         desc = "是否打印",
         notNull = false,
         length = "1",
         remark = "是否打印",
         maxSize = 1
      )
      private String isPrint;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getStartDate() {
         return this.startDate;
      }

      public String getEndDate() {
         return this.endDate;
      }

      public BigDecimal getMinAmt() {
         return this.minAmt;
      }

      public BigDecimal getMaxAmt() {
         return this.maxAmt;
      }

      public String getOthBaseAcctNo() {
         return this.othBaseAcctNo;
      }

      public String getOthAcctName() {
         return this.othAcctName;
      }

      public String getIsPrint() {
         return this.isPrint;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setStartDate(String startDate) {
         this.startDate = startDate;
      }

      public void setEndDate(String endDate) {
         this.endDate = endDate;
      }

      public void setMinAmt(BigDecimal minAmt) {
         this.minAmt = minAmt;
      }

      public void setMaxAmt(BigDecimal maxAmt) {
         this.maxAmt = maxAmt;
      }

      public void setOthBaseAcctNo(String othBaseAcctNo) {
         this.othBaseAcctNo = othBaseAcctNo;
      }

      public void setOthAcctName(String othAcctName) {
         this.othAcctName = othAcctName;
      }

      public void setIsPrint(String isPrint) {
         this.isPrint = isPrint;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1220105800In.Body)) {
            return false;
         } else {
            Core1220105800In.Body other = (Core1220105800In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label107: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label107;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label107;
                  }

                  return false;
               }

               Object this$startDate = this.getStartDate();
               Object other$startDate = other.getStartDate();
               if (this$startDate == null) {
                  if (other$startDate != null) {
                     return false;
                  }
               } else if (!this$startDate.equals(other$startDate)) {
                  return false;
               }

               Object this$endDate = this.getEndDate();
               Object other$endDate = other.getEndDate();
               if (this$endDate == null) {
                  if (other$endDate != null) {
                     return false;
                  }
               } else if (!this$endDate.equals(other$endDate)) {
                  return false;
               }

               label86: {
                  Object this$minAmt = this.getMinAmt();
                  Object other$minAmt = other.getMinAmt();
                  if (this$minAmt == null) {
                     if (other$minAmt == null) {
                        break label86;
                     }
                  } else if (this$minAmt.equals(other$minAmt)) {
                     break label86;
                  }

                  return false;
               }

               label79: {
                  Object this$maxAmt = this.getMaxAmt();
                  Object other$maxAmt = other.getMaxAmt();
                  if (this$maxAmt == null) {
                     if (other$maxAmt == null) {
                        break label79;
                     }
                  } else if (this$maxAmt.equals(other$maxAmt)) {
                     break label79;
                  }

                  return false;
               }

               label72: {
                  Object this$othBaseAcctNo = this.getOthBaseAcctNo();
                  Object other$othBaseAcctNo = other.getOthBaseAcctNo();
                  if (this$othBaseAcctNo == null) {
                     if (other$othBaseAcctNo == null) {
                        break label72;
                     }
                  } else if (this$othBaseAcctNo.equals(other$othBaseAcctNo)) {
                     break label72;
                  }

                  return false;
               }

               Object this$othAcctName = this.getOthAcctName();
               Object other$othAcctName = other.getOthAcctName();
               if (this$othAcctName == null) {
                  if (other$othAcctName != null) {
                     return false;
                  }
               } else if (!this$othAcctName.equals(other$othAcctName)) {
                  return false;
               }

               Object this$isPrint = this.getIsPrint();
               Object other$isPrint = other.getIsPrint();
               if (this$isPrint == null) {
                  if (other$isPrint != null) {
                     return false;
                  }
               } else if (!this$isPrint.equals(other$isPrint)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1220105800In.Body;
      }
      public String toString() {
         return "Core1220105800In.Body(baseAcctNo=" + this.getBaseAcctNo() + ", startDate=" + this.getStartDate() + ", endDate=" + this.getEndDate() + ", minAmt=" + this.getMinAmt() + ", maxAmt=" + this.getMaxAmt() + ", othBaseAcctNo=" + this.getOthBaseAcctNo() + ", othAcctName=" + this.getOthAcctName() + ", isPrint=" + this.getIsPrint() + ")";
      }
   }
}
