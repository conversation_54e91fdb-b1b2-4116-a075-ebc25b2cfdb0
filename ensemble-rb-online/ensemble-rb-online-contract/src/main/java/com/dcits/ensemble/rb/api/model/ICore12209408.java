package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12209408In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12209408Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12209408 {
   String URL = "/rb/inq/acct/hangofflist";


   @ApiRemark("标准优化")
   @ApiDesc("根据上送的条件查询账户挂销账信息")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1220",
      messageCode = "9408"
   )
   @BusinessCategory("1220-文件")
   @FunctionCategory("RB15-内部账")
   @ConsumeSys("TLE/EOS/BCS")
   @ApiUseStatus("PRODUCT-产品")
   Core12209408Out runService(Core12209408In var1);
}
