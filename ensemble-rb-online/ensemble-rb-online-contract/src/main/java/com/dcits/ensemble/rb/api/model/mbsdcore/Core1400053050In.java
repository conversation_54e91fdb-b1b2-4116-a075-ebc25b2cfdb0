package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;

@MessageIn
public class Core1400053050In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400053050In.Body body;

   public Core1400053050In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400053050In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400053050In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400053050In)) {
         return false;
      } else {
         Core1400053050In other = (Core1400053050In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400053050In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "交易金额",
         notNull = false,
         length = "17",
         remark = "交易金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal tranAmt;
      @V(
         desc = "交易类型",
         notNull = false,
         length = "10",
         remark = "交易类型",
         maxSize = 10
      )
      private String tranType;
      @V(
         desc = "对方账号/卡号",
         notNull = false,
         length = "50",
         remark = "对方账号/卡号",
         maxSize = 50
      )
      private String othBaseAcctNo;
      @V(
         desc = "借贷方向",
         notNull = false,
         length = "1",
         inDesc = "D-借,C-贷",
         remark = "借贷方向",
         maxSize = 1
      )
      private String drCrFlag;
      @V(
         desc = "账户名称",
         notNull = false,
         length = "200",
         remark = "账户名称，一般指中文账户名称",
         maxSize = 200
      )
      private String acctName;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getCcy() {
         return this.ccy;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public BigDecimal getTranAmt() {
         return this.tranAmt;
      }

      public String getTranType() {
         return this.tranType;
      }

      public String getOthBaseAcctNo() {
         return this.othBaseAcctNo;
      }

      public String getDrCrFlag() {
         return this.drCrFlag;
      }

      public String getAcctName() {
         return this.acctName;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setTranAmt(BigDecimal tranAmt) {
         this.tranAmt = tranAmt;
      }

      public void setTranType(String tranType) {
         this.tranType = tranType;
      }

      public void setOthBaseAcctNo(String othBaseAcctNo) {
         this.othBaseAcctNo = othBaseAcctNo;
      }

      public void setDrCrFlag(String drCrFlag) {
         this.drCrFlag = drCrFlag;
      }

      public void setAcctName(String acctName) {
         this.acctName = acctName;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400053050In.Body)) {
            return false;
         } else {
            Core1400053050In.Body other = (Core1400053050In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label107: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label107;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label107;
                  }

                  return false;
               }

               Object this$ccy = this.getCcy();
               Object other$ccy = other.getCcy();
               if (this$ccy == null) {
                  if (other$ccy != null) {
                     return false;
                  }
               } else if (!this$ccy.equals(other$ccy)) {
                  return false;
               }

               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo != null) {
                     return false;
                  }
               } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                  return false;
               }

               label86: {
                  Object this$tranAmt = this.getTranAmt();
                  Object other$tranAmt = other.getTranAmt();
                  if (this$tranAmt == null) {
                     if (other$tranAmt == null) {
                        break label86;
                     }
                  } else if (this$tranAmt.equals(other$tranAmt)) {
                     break label86;
                  }

                  return false;
               }

               label79: {
                  Object this$tranType = this.getTranType();
                  Object other$tranType = other.getTranType();
                  if (this$tranType == null) {
                     if (other$tranType == null) {
                        break label79;
                     }
                  } else if (this$tranType.equals(other$tranType)) {
                     break label79;
                  }

                  return false;
               }

               label72: {
                  Object this$othBaseAcctNo = this.getOthBaseAcctNo();
                  Object other$othBaseAcctNo = other.getOthBaseAcctNo();
                  if (this$othBaseAcctNo == null) {
                     if (other$othBaseAcctNo == null) {
                        break label72;
                     }
                  } else if (this$othBaseAcctNo.equals(other$othBaseAcctNo)) {
                     break label72;
                  }

                  return false;
               }

               Object this$drCrFlag = this.getDrCrFlag();
               Object other$drCrFlag = other.getDrCrFlag();
               if (this$drCrFlag == null) {
                  if (other$drCrFlag != null) {
                     return false;
                  }
               } else if (!this$drCrFlag.equals(other$drCrFlag)) {
                  return false;
               }

               Object this$acctName = this.getAcctName();
               Object other$acctName = other.getAcctName();
               if (this$acctName == null) {
                  if (other$acctName != null) {
                     return false;
                  }
               } else if (!this$acctName.equals(other$acctName)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400053050In.Body;
      }
      public String toString() {
         return "Core1400053050In.Body(baseAcctNo=" + this.getBaseAcctNo() + ", ccy=" + this.getCcy() + ", acctSeqNo=" + this.getAcctSeqNo() + ", tranAmt=" + this.getTranAmt() + ", tranType=" + this.getTranType() + ", othBaseAcctNo=" + this.getOthBaseAcctNo() + ", drCrFlag=" + this.getDrCrFlag() + ", acctName=" + this.getAcctName() + ")";
      }
   }
}
