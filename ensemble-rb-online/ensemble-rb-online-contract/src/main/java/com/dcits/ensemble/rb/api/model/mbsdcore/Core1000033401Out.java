package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;

@MessageOut
public class Core1000033401Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "业务流水号",
      notNull = false,
      length = "50",
      remark = "支付流水号",
      maxSize = 50
   )
   private String serialNo;
   @V(
      desc = "交易参考号",
      notNull = false,
      length = "50",
      remark = "交易参考号",
      maxSize = 50
   )
   private String reference;
   @V(
      desc = "票据密押",
      notNull = false,
      length = "20",
      remark = "票据密押",
      maxSize = 20
   )
   private String billPswd;

   public String getSerialNo() {
      return this.serialNo;
   }

   public String getReference() {
      return this.reference;
   }

   public String getBillPswd() {
      return this.billPswd;
   }

   public void setSerialNo(String serialNo) {
      this.serialNo = serialNo;
   }

   public void setReference(String reference) {
      this.reference = reference;
   }

   public void setBillPswd(String billPswd) {
      this.billPswd = billPswd;
   }

   public String toString() {
      return "Core1000033401Out(serialNo=" + this.getSerialNo() + ", reference=" + this.getReference() + ", billPswd=" + this.getBillPswd() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1000033401Out)) {
         return false;
      } else {
         Core1000033401Out other = (Core1000033401Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            label49: {
               Object this$serialNo = this.getSerialNo();
               Object other$serialNo = other.getSerialNo();
               if (this$serialNo == null) {
                  if (other$serialNo == null) {
                     break label49;
                  }
               } else if (this$serialNo.equals(other$serialNo)) {
                  break label49;
               }

               return false;
            }

            Object this$reference = this.getReference();
            Object other$reference = other.getReference();
            if (this$reference == null) {
               if (other$reference != null) {
                  return false;
               }
            } else if (!this$reference.equals(other$reference)) {
               return false;
            }

            Object this$billPswd = this.getBillPswd();
            Object other$billPswd = other.getBillPswd();
            if (this$billPswd == null) {
               if (other$billPswd != null) {
                  return false;
               }
            } else if (!this$billPswd.equals(other$billPswd)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1000033401Out;
   }
}
