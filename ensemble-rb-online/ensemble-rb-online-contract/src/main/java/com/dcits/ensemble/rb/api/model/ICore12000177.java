package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000177In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000177Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12000177 {
   String URL = "/rb/nfin/risk/limit/maint";


   @ApiDesc("用于个人或对公活期账户风险等级限额临时修改，修改后账户的限额以分户级限额为准")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0177"
   )
   @BusinessCategory("1200")
   @FunctionCategory("RB08-特殊业务")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core12000177Out runService(Core12000177In var1);
}
