package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000153In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000153Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12000153 {
   String URL = "/rb/nfin/client/acct/merge";


   @ApiRemark("标准优化")
   @ApiDesc("该接口用于合并客户下部分或全部账户至另一客户下，主要更新账户主表及账户客户关联信息表，并登记账户持有人变更登记簿，变更前需进行账户检查")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0153"
   )
   @BusinessCategory("1200-非金融")
   @FunctionCategory("RB08-特殊业务")
   @ConsumeSys("TLE/COS/PR")
   @ApiUseStatus("PRODUCT-产品")
   Core12000153Out runService(Core12000153In var1);
}
