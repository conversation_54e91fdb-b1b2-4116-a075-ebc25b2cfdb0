package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400033402In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400033402In.Body body;

   public Core1400033402In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400033402In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400033402In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400033402In)) {
         return false;
      } else {
         Core1400033402In other = (Core1400033402In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400033402In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "开始日期",
         notNull = false,
         remark = "开始日期"
      )
      private String startDate;
      @V(
         desc = "结束日期",
         notNull = false,
         remark = "结束日期"
      )
      private String endDate;
      @V(
         desc = "所属机构号",
         notNull = false,
         length = "50",
         remark = "机构代码",
         maxSize = 50
      )
      private String branch;
      @V(
         desc = "业务流水号",
         notNull = false,
         length = "50",
         remark = "支付流水号",
         maxSize = 50
      )
      private String serialNo;
      @V(
         desc = "原业务流水号",
         notNull = false,
         length = "50",
         remark = "原签发流水号",
         maxSize = 50
      )
      private String origSerialNo;
      @V(
         desc = "限额维护类型",
         notNull = false,
         length = "1",
         in = "A,U,D",
         inDesc = "A-新增 ,U-修改 ,D-删除",
         remark = "限额维护类型",
         maxSize = 1
      )
      private String operType;
      @V(
         desc = "票据号码",
         notNull = false,
         length = "50",
         remark = "票据号码",
         maxSize = 50
      )
      private String billNo;
      @V(
         desc = "票据状态",
         notNull = false,
         length = "2",
         in = "00,01,02,03,04,05,06",
         inDesc = "00-录入,01-签发,02-兑付,03-退回,04-挂失,05-解挂,06-删除,08-挂失止付,09-公示催告,11-未复核,12-已复核,13-已打印,14-已签章核对,15-已签发冲正,16-已移存",
         remark = "票据状态",
         maxSize = 2
      )
      private String billStatus;

      public String getStartDate() {
         return this.startDate;
      }

      public String getEndDate() {
         return this.endDate;
      }

      public String getBranch() {
         return this.branch;
      }

      public String getSerialNo() {
         return this.serialNo;
      }

      public String getOrigSerialNo() {
         return this.origSerialNo;
      }

      public String getOperType() {
         return this.operType;
      }

      public String getBillNo() {
         return this.billNo;
      }

      public String getBillStatus() {
         return this.billStatus;
      }

      public void setStartDate(String startDate) {
         this.startDate = startDate;
      }

      public void setEndDate(String endDate) {
         this.endDate = endDate;
      }

      public void setBranch(String branch) {
         this.branch = branch;
      }

      public void setSerialNo(String serialNo) {
         this.serialNo = serialNo;
      }

      public void setOrigSerialNo(String origSerialNo) {
         this.origSerialNo = origSerialNo;
      }

      public void setOperType(String operType) {
         this.operType = operType;
      }

      public void setBillNo(String billNo) {
         this.billNo = billNo;
      }

      public void setBillStatus(String billStatus) {
         this.billStatus = billStatus;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400033402In.Body)) {
            return false;
         } else {
            Core1400033402In.Body other = (Core1400033402In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label107: {
                  Object this$startDate = this.getStartDate();
                  Object other$startDate = other.getStartDate();
                  if (this$startDate == null) {
                     if (other$startDate == null) {
                        break label107;
                     }
                  } else if (this$startDate.equals(other$startDate)) {
                     break label107;
                  }

                  return false;
               }

               Object this$endDate = this.getEndDate();
               Object other$endDate = other.getEndDate();
               if (this$endDate == null) {
                  if (other$endDate != null) {
                     return false;
                  }
               } else if (!this$endDate.equals(other$endDate)) {
                  return false;
               }

               Object this$branch = this.getBranch();
               Object other$branch = other.getBranch();
               if (this$branch == null) {
                  if (other$branch != null) {
                     return false;
                  }
               } else if (!this$branch.equals(other$branch)) {
                  return false;
               }

               label86: {
                  Object this$serialNo = this.getSerialNo();
                  Object other$serialNo = other.getSerialNo();
                  if (this$serialNo == null) {
                     if (other$serialNo == null) {
                        break label86;
                     }
                  } else if (this$serialNo.equals(other$serialNo)) {
                     break label86;
                  }

                  return false;
               }

               label79: {
                  Object this$origSerialNo = this.getOrigSerialNo();
                  Object other$origSerialNo = other.getOrigSerialNo();
                  if (this$origSerialNo == null) {
                     if (other$origSerialNo == null) {
                        break label79;
                     }
                  } else if (this$origSerialNo.equals(other$origSerialNo)) {
                     break label79;
                  }

                  return false;
               }

               label72: {
                  Object this$operType = this.getOperType();
                  Object other$operType = other.getOperType();
                  if (this$operType == null) {
                     if (other$operType == null) {
                        break label72;
                     }
                  } else if (this$operType.equals(other$operType)) {
                     break label72;
                  }

                  return false;
               }

               Object this$billNo = this.getBillNo();
               Object other$billNo = other.getBillNo();
               if (this$billNo == null) {
                  if (other$billNo != null) {
                     return false;
                  }
               } else if (!this$billNo.equals(other$billNo)) {
                  return false;
               }

               Object this$billStatus = this.getBillStatus();
               Object other$billStatus = other.getBillStatus();
               if (this$billStatus == null) {
                  if (other$billStatus != null) {
                     return false;
                  }
               } else if (!this$billStatus.equals(other$billStatus)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400033402In.Body;
      }
      public String toString() {
         return "Core1400033402In.Body(startDate=" + this.getStartDate() + ", endDate=" + this.getEndDate() + ", branch=" + this.getBranch() + ", serialNo=" + this.getSerialNo() + ", origSerialNo=" + this.getOrigSerialNo() + ", operType=" + this.getOperType() + ", billNo=" + this.getBillNo() + ", billStatus=" + this.getBillStatus() + ")";
      }
   }
}
