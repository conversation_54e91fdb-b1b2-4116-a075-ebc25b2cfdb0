package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.util.List;

@MessageOut
public class Core1400106013Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400106013Out.ListArray> listArray;

   public List<Core1400106013Out.ListArray> getListArray() {
      return this.listArray;
   }

   public void setListArray(List<Core1400106013Out.ListArray> listArray) {
      this.listArray = listArray;
   }

   public String toString() {
      return "Core1400106013Out(listArray=" + this.getListArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400106013Out)) {
         return false;
      } else {
         Core1400106013Out other = (Core1400106013Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$listArray = this.getListArray();
            Object other$listArray = other.getListArray();
            if (this$listArray == null) {
               if (other$listArray != null) {
                  return false;
               }
            } else if (!this$listArray.equals(other$listArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400106013Out;
   }
   public static class ListArray {
      @V(
         desc = "申请编号",
         notNull = false,
         length = "50",
         remark = "申请编号",
         maxSize = 50
      )
      private String applyNo;
      @V(
         desc = "制卡类型",
         notNull = false,
         length = "1",
         remark = "制卡类型",
         maxSize = 1
      )
      private String makeCardType;
      @V(
         desc = "凭证类型",
         notNull = false,
         length = "10",
         remark = "凭证类型",
         maxSize = 10
      )
      private String docType;
      @V(
         desc = "凭证起始号码",
         notNull = false,
         length = "50",
         remark = "凭证起始号码",
         maxSize = 50
      )
      private String voucherStartNo;
      @V(
         desc = "凭证终止号码",
         notNull = false,
         length = "50",
         remark = "凭证终止号码",
         maxSize = 50
      )
      private String voucherEndNo;

      public String getApplyNo() {
         return this.applyNo;
      }

      public String getMakeCardType() {
         return this.makeCardType;
      }

      public String getDocType() {
         return this.docType;
      }

      public String getVoucherStartNo() {
         return this.voucherStartNo;
      }

      public String getVoucherEndNo() {
         return this.voucherEndNo;
      }

      public void setApplyNo(String applyNo) {
         this.applyNo = applyNo;
      }

      public void setMakeCardType(String makeCardType) {
         this.makeCardType = makeCardType;
      }

      public void setDocType(String docType) {
         this.docType = docType;
      }

      public void setVoucherStartNo(String voucherStartNo) {
         this.voucherStartNo = voucherStartNo;
      }

      public void setVoucherEndNo(String voucherEndNo) {
         this.voucherEndNo = voucherEndNo;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400106013Out.ListArray)) {
            return false;
         } else {
            Core1400106013Out.ListArray other = (Core1400106013Out.ListArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label71: {
                  Object this$applyNo = this.getApplyNo();
                  Object other$applyNo = other.getApplyNo();
                  if (this$applyNo == null) {
                     if (other$applyNo == null) {
                        break label71;
                     }
                  } else if (this$applyNo.equals(other$applyNo)) {
                     break label71;
                  }

                  return false;
               }

               Object this$makeCardType = this.getMakeCardType();
               Object other$makeCardType = other.getMakeCardType();
               if (this$makeCardType == null) {
                  if (other$makeCardType != null) {
                     return false;
                  }
               } else if (!this$makeCardType.equals(other$makeCardType)) {
                  return false;
               }

               label57: {
                  Object this$docType = this.getDocType();
                  Object other$docType = other.getDocType();
                  if (this$docType == null) {
                     if (other$docType == null) {
                        break label57;
                     }
                  } else if (this$docType.equals(other$docType)) {
                     break label57;
                  }

                  return false;
               }

               Object this$voucherStartNo = this.getVoucherStartNo();
               Object other$voucherStartNo = other.getVoucherStartNo();
               if (this$voucherStartNo == null) {
                  if (other$voucherStartNo != null) {
                     return false;
                  }
               } else if (!this$voucherStartNo.equals(other$voucherStartNo)) {
                  return false;
               }

               Object this$voucherEndNo = this.getVoucherEndNo();
               Object other$voucherEndNo = other.getVoucherEndNo();
               if (this$voucherEndNo == null) {
                  if (other$voucherEndNo == null) {
                     return true;
                  }
               } else if (this$voucherEndNo.equals(other$voucherEndNo)) {
                  return true;
               }

               return false;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400106013Out.ListArray;
      }
      public String toString() {
         return "Core1400106013Out.ListArray(applyNo=" + this.getApplyNo() + ", makeCardType=" + this.getMakeCardType() + ", docType=" + this.getDocType() + ", voucherStartNo=" + this.getVoucherStartNo() + ", voucherEndNo=" + this.getVoucherEndNo() + ")";
      }
   }
}
