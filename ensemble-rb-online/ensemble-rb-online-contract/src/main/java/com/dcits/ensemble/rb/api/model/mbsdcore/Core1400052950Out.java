package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.util.List;

@MessageOut
public class Core1400052950Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400052950Out.DocArray> docArray;

   public List<Core1400052950Out.DocArray> getDocArray() {
      return this.docArray;
   }

   public void setDocArray(List<Core1400052950Out.DocArray> docArray) {
      this.docArray = docArray;
   }

   public String toString() {
      return "Core1400052950Out(docArray=" + this.getDocArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400052950Out)) {
         return false;
      } else {
         Core1400052950Out other = (Core1400052950Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$docArray = this.getDocArray();
            Object other$docArray = other.getDocArray();
            if (this$docArray == null) {
               if (other$docArray != null) {
                  return false;
               }
            } else if (!this$docArray.equals(other$docArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400052950Out;
   }
   public static class DocArray {
      @V(
         desc = "证件类型",
         notNull = false,
         length = "3",
         remark = "证件类型",
         maxSize = 3
      )
      private String documentType;
      @V(
         desc = "证件号码",
         notNull = false,
         length = "50",
         remark = "证件号码",
         maxSize = 50
      )
      private String documentId;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "客户名称",
         notNull = false,
         length = "200",
         remark = "客户名称",
         maxSize = 200
      )
      private String clientName;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "生效日期",
         notNull = false,
         remark = "生效日期"
      )
      private String effectDate;
      @V(
         desc = "结束日期",
         notNull = false,
         remark = "结束日期"
      )
      private String endDate;
      @V(
         desc = "暂记非柜面限制类型",
         notNull = false,
         length = "3",
         remark = "暂记非柜面限制类型",
         maxSize = 3
      )
      private String uncounterRestraintType;
      @V(
         desc = "暂停非柜面标记",
         notNull = false,
         length = "1",
         in = "1,2,3,4",
         remark = "暂停非柜面标记",
         maxSize = 1
      )
      private String uncounterRestraintStatus;
      @V(
         desc = "法人",
         notNull = false,
         length = "20",
         remark = "法人",
         maxSize = 20
      )
      private String company;

      public String getDocumentType() {
         return this.documentType;
      }

      public String getDocumentId() {
         return this.documentId;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public String getClientName() {
         return this.clientName;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getEffectDate() {
         return this.effectDate;
      }

      public String getEndDate() {
         return this.endDate;
      }

      public String getUncounterRestraintType() {
         return this.uncounterRestraintType;
      }

      public String getUncounterRestraintStatus() {
         return this.uncounterRestraintStatus;
      }

      public String getCompany() {
         return this.company;
      }

      public void setDocumentType(String documentType) {
         this.documentType = documentType;
      }

      public void setDocumentId(String documentId) {
         this.documentId = documentId;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setClientName(String clientName) {
         this.clientName = clientName;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setEffectDate(String effectDate) {
         this.effectDate = effectDate;
      }

      public void setEndDate(String endDate) {
         this.endDate = endDate;
      }

      public void setUncounterRestraintType(String uncounterRestraintType) {
         this.uncounterRestraintType = uncounterRestraintType;
      }

      public void setUncounterRestraintStatus(String uncounterRestraintStatus) {
         this.uncounterRestraintStatus = uncounterRestraintStatus;
      }

      public void setCompany(String company) {
         this.company = company;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400052950Out.DocArray)) {
            return false;
         } else {
            Core1400052950Out.DocArray other = (Core1400052950Out.DocArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$documentType = this.getDocumentType();
               Object other$documentType = other.getDocumentType();
               if (this$documentType == null) {
                  if (other$documentType != null) {
                     return false;
                  }
               } else if (!this$documentType.equals(other$documentType)) {
                  return false;
               }

               Object this$documentId = this.getDocumentId();
               Object other$documentId = other.getDocumentId();
               if (this$documentId == null) {
                  if (other$documentId != null) {
                     return false;
                  }
               } else if (!this$documentId.equals(other$documentId)) {
                  return false;
               }

               Object this$clientNo = this.getClientNo();
               Object other$clientNo = other.getClientNo();
               if (this$clientNo == null) {
                  if (other$clientNo != null) {
                     return false;
                  }
               } else if (!this$clientNo.equals(other$clientNo)) {
                  return false;
               }

               label110: {
                  Object this$clientName = this.getClientName();
                  Object other$clientName = other.getClientName();
                  if (this$clientName == null) {
                     if (other$clientName == null) {
                        break label110;
                     }
                  } else if (this$clientName.equals(other$clientName)) {
                     break label110;
                  }

                  return false;
               }

               label103: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label103;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label103;
                  }

                  return false;
               }

               Object this$effectDate = this.getEffectDate();
               Object other$effectDate = other.getEffectDate();
               if (this$effectDate == null) {
                  if (other$effectDate != null) {
                     return false;
                  }
               } else if (!this$effectDate.equals(other$effectDate)) {
                  return false;
               }

               label89: {
                  Object this$endDate = this.getEndDate();
                  Object other$endDate = other.getEndDate();
                  if (this$endDate == null) {
                     if (other$endDate == null) {
                        break label89;
                     }
                  } else if (this$endDate.equals(other$endDate)) {
                     break label89;
                  }

                  return false;
               }

               label82: {
                  Object this$uncounterRestraintType = this.getUncounterRestraintType();
                  Object other$uncounterRestraintType = other.getUncounterRestraintType();
                  if (this$uncounterRestraintType == null) {
                     if (other$uncounterRestraintType == null) {
                        break label82;
                     }
                  } else if (this$uncounterRestraintType.equals(other$uncounterRestraintType)) {
                     break label82;
                  }

                  return false;
               }

               Object this$uncounterRestraintStatus = this.getUncounterRestraintStatus();
               Object other$uncounterRestraintStatus = other.getUncounterRestraintStatus();
               if (this$uncounterRestraintStatus == null) {
                  if (other$uncounterRestraintStatus != null) {
                     return false;
                  }
               } else if (!this$uncounterRestraintStatus.equals(other$uncounterRestraintStatus)) {
                  return false;
               }

               Object this$company = this.getCompany();
               Object other$company = other.getCompany();
               if (this$company == null) {
                  if (other$company != null) {
                     return false;
                  }
               } else if (!this$company.equals(other$company)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400052950Out.DocArray;
      }
      public String toString() {
         return "Core1400052950Out.DocArray(documentType=" + this.getDocumentType() + ", documentId=" + this.getDocumentId() + ", clientNo=" + this.getClientNo() + ", clientName=" + this.getClientName() + ", baseAcctNo=" + this.getBaseAcctNo() + ", effectDate=" + this.getEffectDate() + ", endDate=" + this.getEndDate() + ", uncounterRestraintType=" + this.getUncounterRestraintType() + ", uncounterRestraintStatus=" + this.getUncounterRestraintStatus() + ", company=" + this.getCompany() + ")";
      }
   }
}
