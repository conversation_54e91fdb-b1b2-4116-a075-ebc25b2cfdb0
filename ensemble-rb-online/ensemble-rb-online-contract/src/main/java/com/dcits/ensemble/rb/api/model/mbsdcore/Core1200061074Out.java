package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;

@MessageOut
public class Core1200061074Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "交易参考号",
      notNull = false,
      length = "50",
      remark = "交易参考号",
      maxSize = 50
   )
   private String reference;
   @V(
      desc = "冻结编号",
      notNull = false,
      length = "50",
      remark = "冻结编号",
      maxSize = 50
   )
   private String restraintSeqNo;

   public String getReference() {
      return this.reference;
   }

   public String getRestraintSeqNo() {
      return this.restraintSeqNo;
   }

   public void setReference(String reference) {
      this.reference = reference;
   }

   public void setRestraintSeqNo(String restraintSeqNo) {
      this.restraintSeqNo = restraintSeqNo;
   }

   public String toString() {
      return "Core1200061074Out(reference=" + this.getReference() + ", restraintSeqNo=" + this.getRestraintSeqNo() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200061074Out)) {
         return false;
      } else {
         Core1200061074Out other = (Core1200061074Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$reference = this.getReference();
            Object other$reference = other.getReference();
            if (this$reference == null) {
               if (other$reference != null) {
                  return false;
               }
            } else if (!this$reference.equals(other$reference)) {
               return false;
            }

            Object this$restraintSeqNo = this.getRestraintSeqNo();
            Object other$restraintSeqNo = other.getRestraintSeqNo();
            if (this$restraintSeqNo == null) {
               if (other$restraintSeqNo != null) {
                  return false;
               }
            } else if (!this$restraintSeqNo.equals(other$restraintSeqNo)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200061074Out;
   }
}
