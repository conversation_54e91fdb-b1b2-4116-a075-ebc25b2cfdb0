package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000407In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000407Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000407 {
   String URL = "/rb/inq/verify/hist";


   @ApiDesc("本功能用于查询账户核实的历史记录，给柜面提供接口。")
   @EnsembleElements(
      serviceCode = "Core",
      messageType = "1400",
      messageCode = "0407"
   )
   @FunctionCategory("RB48-登记簿查询")
   @ConsumeSys("CBS")
   @ApiUseStatus("PRODUCT-产品")
   Core14000407Out runService(Core14000407In var1);
}
