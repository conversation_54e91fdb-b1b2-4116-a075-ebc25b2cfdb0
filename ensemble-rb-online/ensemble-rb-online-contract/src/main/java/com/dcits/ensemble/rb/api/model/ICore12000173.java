package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000173In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000173Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12000173 {
   String URL = "/rb/nfin/book/print/maint";


   @ApiRemark("标准优化")
   @ApiDesc("交易主要用于卡配对账簿打印位置的调整功能")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0173"
   )
   @BusinessCategory("1200-非金融")
   @FunctionCategory("RB06-凭证处理")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core12000173Out runService(Core12000173In var1);
}
