package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.util.List;

@MessageOut
public class Core1400100013Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100013Out.AcctArray> acctArray;

   public List<Core1400100013Out.AcctArray> getAcctArray() {
      return this.acctArray;
   }

   public void setAcctArray(List<Core1400100013Out.AcctArray> acctArray) {
      this.acctArray = acctArray;
   }

   public String toString() {
      return "Core1400100013Out(acctArray=" + this.getAcctArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100013Out)) {
         return false;
      } else {
         Core1400100013Out other = (Core1400100013Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$acctArray = this.getAcctArray();
            Object other$acctArray = other.getAcctArray();
            if (this$acctArray == null) {
               if (other$acctArray != null) {
                  return false;
               }
            } else if (!this$acctArray.equals(other$acctArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100013Out;
   }
   public static class AcctArray {
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "序号",
         notNull = false,
         length = "50",
         remark = "序号",
         maxSize = 50
      )
      private String seqNo;
      @V(
         desc = "产品描述",
         notNull = false,
         length = "200",
         remark = "解释产品具体特性",
         maxSize = 200
      )
      private String prodDesc;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getCcy() {
         return this.ccy;
      }

      public String getSeqNo() {
         return this.seqNo;
      }

      public String getProdDesc() {
         return this.prodDesc;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setSeqNo(String seqNo) {
         this.seqNo = seqNo;
      }

      public void setProdDesc(String prodDesc) {
         this.prodDesc = prodDesc;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100013Out.AcctArray)) {
            return false;
         } else {
            Core1400100013Out.AcctArray other = (Core1400100013Out.AcctArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label71: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label71;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label71;
                  }

                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               label57: {
                  Object this$ccy = this.getCcy();
                  Object other$ccy = other.getCcy();
                  if (this$ccy == null) {
                     if (other$ccy == null) {
                        break label57;
                     }
                  } else if (this$ccy.equals(other$ccy)) {
                     break label57;
                  }

                  return false;
               }

               Object this$seqNo = this.getSeqNo();
               Object other$seqNo = other.getSeqNo();
               if (this$seqNo == null) {
                  if (other$seqNo != null) {
                     return false;
                  }
               } else if (!this$seqNo.equals(other$seqNo)) {
                  return false;
               }

               Object this$prodDesc = this.getProdDesc();
               Object other$prodDesc = other.getProdDesc();
               if (this$prodDesc == null) {
                  if (other$prodDesc == null) {
                     return true;
                  }
               } else if (this$prodDesc.equals(other$prodDesc)) {
                  return true;
               }

               return false;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100013Out.AcctArray;
      }
      public String toString() {
         return "Core1400100013Out.AcctArray(baseAcctNo=" + this.getBaseAcctNo() + ", prodType=" + this.getProdType() + ", ccy=" + this.getCcy() + ", seqNo=" + this.getSeqNo() + ", prodDesc=" + this.getProdDesc() + ")";
      }
   }
}
