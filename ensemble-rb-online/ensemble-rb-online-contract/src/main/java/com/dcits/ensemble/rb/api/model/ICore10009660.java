package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10009660In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10009660Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore10009660 {
   String URL = "/rb/fin/channel/common/gjaccount";

   
   @ApiRemark("深度优化")
   @ApiDesc("该功能仅提供给国结系统，用于国结系统的入账。")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "9660"
   )
   @BusinessCategory("1000-金融")
   @FunctionCategory("RB50-通用记账")
   @ConsumeSys("ISS")
   @ApiUseStatus("PRODUCT-产品")
   Core10009660Out runService(Core10009660In var1);
}
