package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400100221In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400100221In.Body body;

   public Core1400100221In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400100221In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400100221In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100221In)) {
         return false;
      } else {
         Core1400100221In other = (Core1400100221In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100221In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "期次产品分类",
         notNull = false,
         length = "5",
         inDesc = "DC-大额存单,ST-结构性存款",
         remark = "期次产品分类,XXC-新兴存，LSDQ-零售定期",
         maxSize = 5
      )
      private String stageProdClass;

      public String getStageProdClass() {
         return this.stageProdClass;
      }

      public void setStageProdClass(String stageProdClass) {
         this.stageProdClass = stageProdClass;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100221In.Body)) {
            return false;
         } else {
            Core1400100221In.Body other = (Core1400100221In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$stageProdClass = this.getStageProdClass();
               Object other$stageProdClass = other.getStageProdClass();
               if (this$stageProdClass == null) {
                  if (other$stageProdClass != null) {
                     return false;
                  }
               } else if (!this$stageProdClass.equals(other$stageProdClass)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100221In.Body;
      }
      public String toString() {
         return "Core1400100221In.Body(stageProdClass=" + this.getStageProdClass() + ")";
      }
   }
}
