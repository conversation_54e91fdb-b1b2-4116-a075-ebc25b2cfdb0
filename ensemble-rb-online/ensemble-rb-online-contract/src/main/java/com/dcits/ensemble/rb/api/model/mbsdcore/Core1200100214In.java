package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;

@MessageIn
public class Core1200100214In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1200100214In.Body body;

   public Core1200100214In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1200100214In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1200100214In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200100214In)) {
         return false;
      } else {
         Core1200100214In other = (Core1200100214In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200100214In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "发行年度",
         notNull = true,
         length = "5",
         remark = "发行年度",
         maxSize = 5
      )
      private String issueYear;
      @V(
         desc = "币种",
         notNull = true,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "备案额度",
         notNull = true,
         length = "17",
         remark = "调整后备案额度",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal recordLimit;
      @V(
         desc = "调整额度",
         notNull = false,
         length = "17",
         remark = "调整额度",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal adjustLimit;
      @V(
         desc = "零售额度",
         notNull = true,
         length = "17",
         remark = "零售额度",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal personalLimit;
      @V(
         desc = "单位额度",
         notNull = true,
         length = "17",
         remark = "单位额度",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal coprLimit;
      @V(
         desc = "机构额度",
         notNull = true,
         length = "17",
         remark = "机构额度",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal institutionLimit;

      public String getIssueYear() {
         return this.issueYear;
      }

      public String getCcy() {
         return this.ccy;
      }

      public BigDecimal getRecordLimit() {
         return this.recordLimit;
      }

      public BigDecimal getAdjustLimit() {
         return this.adjustLimit;
      }

      public BigDecimal getPersonalLimit() {
         return this.personalLimit;
      }

      public BigDecimal getCoprLimit() {
         return this.coprLimit;
      }

      public BigDecimal getInstitutionLimit() {
         return this.institutionLimit;
      }

      public void setIssueYear(String issueYear) {
         this.issueYear = issueYear;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setRecordLimit(BigDecimal recordLimit) {
         this.recordLimit = recordLimit;
      }

      public void setAdjustLimit(BigDecimal adjustLimit) {
         this.adjustLimit = adjustLimit;
      }

      public void setPersonalLimit(BigDecimal personalLimit) {
         this.personalLimit = personalLimit;
      }

      public void setCoprLimit(BigDecimal coprLimit) {
         this.coprLimit = coprLimit;
      }

      public void setInstitutionLimit(BigDecimal institutionLimit) {
         this.institutionLimit = institutionLimit;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1200100214In.Body)) {
            return false;
         } else {
            Core1200100214In.Body other = (Core1200100214In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label95: {
                  Object this$issueYear = this.getIssueYear();
                  Object other$issueYear = other.getIssueYear();
                  if (this$issueYear == null) {
                     if (other$issueYear == null) {
                        break label95;
                     }
                  } else if (this$issueYear.equals(other$issueYear)) {
                     break label95;
                  }

                  return false;
               }

               Object this$ccy = this.getCcy();
               Object other$ccy = other.getCcy();
               if (this$ccy == null) {
                  if (other$ccy != null) {
                     return false;
                  }
               } else if (!this$ccy.equals(other$ccy)) {
                  return false;
               }

               Object this$recordLimit = this.getRecordLimit();
               Object other$recordLimit = other.getRecordLimit();
               if (this$recordLimit == null) {
                  if (other$recordLimit != null) {
                     return false;
                  }
               } else if (!this$recordLimit.equals(other$recordLimit)) {
                  return false;
               }

               label74: {
                  Object this$adjustLimit = this.getAdjustLimit();
                  Object other$adjustLimit = other.getAdjustLimit();
                  if (this$adjustLimit == null) {
                     if (other$adjustLimit == null) {
                        break label74;
                     }
                  } else if (this$adjustLimit.equals(other$adjustLimit)) {
                     break label74;
                  }

                  return false;
               }

               label67: {
                  Object this$personalLimit = this.getPersonalLimit();
                  Object other$personalLimit = other.getPersonalLimit();
                  if (this$personalLimit == null) {
                     if (other$personalLimit == null) {
                        break label67;
                     }
                  } else if (this$personalLimit.equals(other$personalLimit)) {
                     break label67;
                  }

                  return false;
               }

               Object this$coprLimit = this.getCoprLimit();
               Object other$coprLimit = other.getCoprLimit();
               if (this$coprLimit == null) {
                  if (other$coprLimit != null) {
                     return false;
                  }
               } else if (!this$coprLimit.equals(other$coprLimit)) {
                  return false;
               }

               Object this$institutionLimit = this.getInstitutionLimit();
               Object other$institutionLimit = other.getInstitutionLimit();
               if (this$institutionLimit == null) {
                  if (other$institutionLimit != null) {
                     return false;
                  }
               } else if (!this$institutionLimit.equals(other$institutionLimit)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1200100214In.Body;
      }
      public String toString() {
         return "Core1200100214In.Body(issueYear=" + this.getIssueYear() + ", ccy=" + this.getCcy() + ", recordLimit=" + this.getRecordLimit() + ", adjustLimit=" + this.getAdjustLimit() + ", personalLimit=" + this.getPersonalLimit() + ", coprLimit=" + this.getCoprLimit() + ", institutionLimit=" + this.getInstitutionLimit() + ")";
      }
   }
}
