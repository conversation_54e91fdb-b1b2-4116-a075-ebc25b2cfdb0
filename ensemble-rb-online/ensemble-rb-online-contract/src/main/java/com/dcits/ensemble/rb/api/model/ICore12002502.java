package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12002502In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12002502Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12002502 {
   String URL = "/rb/nfin/asyn/updateloaninfo";


   @ApiRemark("更新贷款合同可透支额度")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "2502"
   )
   @FunctionCategory("RB02-账户管理")
   Core12002502Out runService(Core12002502In var1);
}
