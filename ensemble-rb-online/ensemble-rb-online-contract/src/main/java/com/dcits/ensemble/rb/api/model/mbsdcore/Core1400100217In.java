package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;

@MessageIn
public class Core1400100217In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400100217In.Body body;

   public Core1400100217In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400100217In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400100217In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100217In)) {
         return false;
      } else {
         Core1400100217In other = (Core1400100217In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100217In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "预约总金额",
         notNull = false,
         length = "17",
         remark = "预约总金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal applyAmt;
      @V(
         desc = "发行年度",
         notNull = false,
         length = "5",
         remark = "发行年度",
         maxSize = 5
      )
      private String issueYear;
      @V(
         desc = "审批单号",
         notNull = false,
         length = "50",
         remark = "审批单号",
         maxSize = 50
      )
      private String approvalNo;
      @V(
         desc = "申请机构",
         notNull = false,
         length = "50",
         remark = "申请机构",
         maxSize = 50
      )
      private String applyBranch;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;

      public BigDecimal getApplyAmt() {
         return this.applyAmt;
      }

      public String getIssueYear() {
         return this.issueYear;
      }

      public String getApprovalNo() {
         return this.approvalNo;
      }

      public String getApplyBranch() {
         return this.applyBranch;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public void setApplyAmt(BigDecimal applyAmt) {
         this.applyAmt = applyAmt;
      }

      public void setIssueYear(String issueYear) {
         this.issueYear = issueYear;
      }

      public void setApprovalNo(String approvalNo) {
         this.approvalNo = approvalNo;
      }

      public void setApplyBranch(String applyBranch) {
         this.applyBranch = applyBranch;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100217In.Body)) {
            return false;
         } else {
            Core1400100217In.Body other = (Core1400100217In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label71: {
                  Object this$applyAmt = this.getApplyAmt();
                  Object other$applyAmt = other.getApplyAmt();
                  if (this$applyAmt == null) {
                     if (other$applyAmt == null) {
                        break label71;
                     }
                  } else if (this$applyAmt.equals(other$applyAmt)) {
                     break label71;
                  }

                  return false;
               }

               Object this$issueYear = this.getIssueYear();
               Object other$issueYear = other.getIssueYear();
               if (this$issueYear == null) {
                  if (other$issueYear != null) {
                     return false;
                  }
               } else if (!this$issueYear.equals(other$issueYear)) {
                  return false;
               }

               label57: {
                  Object this$approvalNo = this.getApprovalNo();
                  Object other$approvalNo = other.getApprovalNo();
                  if (this$approvalNo == null) {
                     if (other$approvalNo == null) {
                        break label57;
                     }
                  } else if (this$approvalNo.equals(other$approvalNo)) {
                     break label57;
                  }

                  return false;
               }

               Object this$applyBranch = this.getApplyBranch();
               Object other$applyBranch = other.getApplyBranch();
               if (this$applyBranch == null) {
                  if (other$applyBranch != null) {
                     return false;
                  }
               } else if (!this$applyBranch.equals(other$applyBranch)) {
                  return false;
               }

               Object this$clientNo = this.getClientNo();
               Object other$clientNo = other.getClientNo();
               if (this$clientNo == null) {
                  if (other$clientNo == null) {
                     return true;
                  }
               } else if (this$clientNo.equals(other$clientNo)) {
                  return true;
               }

               return false;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100217In.Body;
      }
      public String toString() {
         return "Core1400100217In.Body(applyAmt=" + this.getApplyAmt() + ", issueYear=" + this.getIssueYear() + ", approvalNo=" + this.getApprovalNo() + ", applyBranch=" + this.getApplyBranch() + ", clientNo=" + this.getClientNo() + ")";
      }
   }
}
