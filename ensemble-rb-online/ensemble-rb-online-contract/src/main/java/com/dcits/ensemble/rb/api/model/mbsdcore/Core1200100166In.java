package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;

@MessageIn
public class Core1200100166In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1200100166In.Body body;

   public Core1200100166In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1200100166In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1200100166In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200100166In)) {
         return false;
      } else {
         Core1200100166In other = (Core1200100166In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200100166In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "变更操作类型",
         notNull = true,
         length = "2",
         inDesc = "01-新增,02-修改,03-删除",
         remark = "变更操作类型",
         maxSize = 2
      )
      private String amendOperateType;
      @V(
         desc = "交易流水识别号",
         notNull = true,
         length = "50",
         remark = "交易流水识别号",
         maxSize = 50
      )
      private String tradeNo;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "账户名称",
         notNull = false,
         length = "200",
         remark = "账户名称，一般指中文账户名称",
         maxSize = 200
      )
      private String acctName;
      @V(
         desc = "总额度",
         notNull = false,
         length = "17",
         remark = "总额度",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal totalLimit;
      @V(
         desc = "已结汇人民币金额",
         notNull = false,
         length = "17",
         remark = "已结汇人民币金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal settlementEdAmt;
      @V(
         desc = "已结汇币种",
         notNull = false,
         length = "3",
         remark = "已结汇币种",
         maxSize = 3
      )
      private String settlementEdCcy;
      @V(
         desc = "已结汇金额",
         notNull = false,
         length = "17",
         remark = "已结汇金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal settlementEdOthAmt;
      @V(
         desc = "支出金额",
         notNull = false,
         length = "17",
         remark = "支出金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal expenses;
      @V(
         desc = "待支付金额",
         notNull = false,
         length = "17",
         remark = "待支付金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal dueAmount;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "备注",
         notNull = false,
         length = "200",
         remark = "备注",
         maxSize = 200
      )
      private String remark;

      public String getAmendOperateType() {
         return this.amendOperateType;
      }

      public String getTradeNo() {
         return this.tradeNo;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getAcctName() {
         return this.acctName;
      }

      public BigDecimal getTotalLimit() {
         return this.totalLimit;
      }

      public BigDecimal getSettlementEdAmt() {
         return this.settlementEdAmt;
      }

      public String getSettlementEdCcy() {
         return this.settlementEdCcy;
      }

      public BigDecimal getSettlementEdOthAmt() {
         return this.settlementEdOthAmt;
      }

      public BigDecimal getExpenses() {
         return this.expenses;
      }

      public BigDecimal getDueAmount() {
         return this.dueAmount;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public String getRemark() {
         return this.remark;
      }

      public void setAmendOperateType(String amendOperateType) {
         this.amendOperateType = amendOperateType;
      }

      public void setTradeNo(String tradeNo) {
         this.tradeNo = tradeNo;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setAcctName(String acctName) {
         this.acctName = acctName;
      }

      public void setTotalLimit(BigDecimal totalLimit) {
         this.totalLimit = totalLimit;
      }

      public void setSettlementEdAmt(BigDecimal settlementEdAmt) {
         this.settlementEdAmt = settlementEdAmt;
      }

      public void setSettlementEdCcy(String settlementEdCcy) {
         this.settlementEdCcy = settlementEdCcy;
      }

      public void setSettlementEdOthAmt(BigDecimal settlementEdOthAmt) {
         this.settlementEdOthAmt = settlementEdOthAmt;
      }

      public void setExpenses(BigDecimal expenses) {
         this.expenses = expenses;
      }

      public void setDueAmount(BigDecimal dueAmount) {
         this.dueAmount = dueAmount;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setRemark(String remark) {
         this.remark = remark;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1200100166In.Body)) {
            return false;
         } else {
            Core1200100166In.Body other = (Core1200100166In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label155: {
                  Object this$amendOperateType = this.getAmendOperateType();
                  Object other$amendOperateType = other.getAmendOperateType();
                  if (this$amendOperateType == null) {
                     if (other$amendOperateType == null) {
                        break label155;
                     }
                  } else if (this$amendOperateType.equals(other$amendOperateType)) {
                     break label155;
                  }

                  return false;
               }

               Object this$tradeNo = this.getTradeNo();
               Object other$tradeNo = other.getTradeNo();
               if (this$tradeNo == null) {
                  if (other$tradeNo != null) {
                     return false;
                  }
               } else if (!this$tradeNo.equals(other$tradeNo)) {
                  return false;
               }

               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo != null) {
                     return false;
                  }
               } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
                  return false;
               }

               label134: {
                  Object this$acctName = this.getAcctName();
                  Object other$acctName = other.getAcctName();
                  if (this$acctName == null) {
                     if (other$acctName == null) {
                        break label134;
                     }
                  } else if (this$acctName.equals(other$acctName)) {
                     break label134;
                  }

                  return false;
               }

               label127: {
                  Object this$totalLimit = this.getTotalLimit();
                  Object other$totalLimit = other.getTotalLimit();
                  if (this$totalLimit == null) {
                     if (other$totalLimit == null) {
                        break label127;
                     }
                  } else if (this$totalLimit.equals(other$totalLimit)) {
                     break label127;
                  }

                  return false;
               }

               label120: {
                  Object this$settlementEdAmt = this.getSettlementEdAmt();
                  Object other$settlementEdAmt = other.getSettlementEdAmt();
                  if (this$settlementEdAmt == null) {
                     if (other$settlementEdAmt == null) {
                        break label120;
                     }
                  } else if (this$settlementEdAmt.equals(other$settlementEdAmt)) {
                     break label120;
                  }

                  return false;
               }

               Object this$settlementEdCcy = this.getSettlementEdCcy();
               Object other$settlementEdCcy = other.getSettlementEdCcy();
               if (this$settlementEdCcy == null) {
                  if (other$settlementEdCcy != null) {
                     return false;
                  }
               } else if (!this$settlementEdCcy.equals(other$settlementEdCcy)) {
                  return false;
               }

               label106: {
                  Object this$settlementEdOthAmt = this.getSettlementEdOthAmt();
                  Object other$settlementEdOthAmt = other.getSettlementEdOthAmt();
                  if (this$settlementEdOthAmt == null) {
                     if (other$settlementEdOthAmt == null) {
                        break label106;
                     }
                  } else if (this$settlementEdOthAmt.equals(other$settlementEdOthAmt)) {
                     break label106;
                  }

                  return false;
               }

               Object this$expenses = this.getExpenses();
               Object other$expenses = other.getExpenses();
               if (this$expenses == null) {
                  if (other$expenses != null) {
                     return false;
                  }
               } else if (!this$expenses.equals(other$expenses)) {
                  return false;
               }

               label92: {
                  Object this$dueAmount = this.getDueAmount();
                  Object other$dueAmount = other.getDueAmount();
                  if (this$dueAmount == null) {
                     if (other$dueAmount == null) {
                        break label92;
                     }
                  } else if (this$dueAmount.equals(other$dueAmount)) {
                     break label92;
                  }

                  return false;
               }

               Object this$clientNo = this.getClientNo();
               Object other$clientNo = other.getClientNo();
               if (this$clientNo == null) {
                  if (other$clientNo != null) {
                     return false;
                  }
               } else if (!this$clientNo.equals(other$clientNo)) {
                  return false;
               }

               Object this$remark = this.getRemark();
               Object other$remark = other.getRemark();
               if (this$remark == null) {
                  if (other$remark != null) {
                     return false;
                  }
               } else if (!this$remark.equals(other$remark)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1200100166In.Body;
      }
      public String toString() {
         return "Core1200100166In.Body(amendOperateType=" + this.getAmendOperateType() + ", tradeNo=" + this.getTradeNo() + ", baseAcctNo=" + this.getBaseAcctNo() + ", acctName=" + this.getAcctName() + ", totalLimit=" + this.getTotalLimit() + ", settlementEdAmt=" + this.getSettlementEdAmt() + ", settlementEdCcy=" + this.getSettlementEdCcy() + ", settlementEdOthAmt=" + this.getSettlementEdOthAmt() + ", expenses=" + this.getExpenses() + ", dueAmount=" + this.getDueAmount() + ", clientNo=" + this.getClientNo() + ", remark=" + this.getRemark() + ")";
      }
   }
}
