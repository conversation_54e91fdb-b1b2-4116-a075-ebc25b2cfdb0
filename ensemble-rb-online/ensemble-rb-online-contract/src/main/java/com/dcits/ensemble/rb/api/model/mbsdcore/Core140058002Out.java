package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;

@MessageOut
public class Core140058002Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "协议编号",
      notNull = false,
      length = "50",
      remark = "协议编号",
      maxSize = 50
   )
   private String agreementId;
   @V(
      desc = "签约机构",
      notNull = false,
      length = "50",
      remark = "签约机构",
      maxSize = 50
   )
   private String signBranch;
   @V(
      desc = "协议类型",
      notNull = false,
      length = "10",
      remark = "协议类型",
      maxSize = 10
   )
   private String agreementType;
   @V(
      desc = "签约渠道",
      notNull = false,
      length = "20",
      remark = "签约渠道",
      maxSize = 20
   )
   private String signChannel;
   @V(
      desc = "签约日期",
      notNull = false,
      remark = "签约日期"
   )
   private String signDate;
   @V(
      desc = "签约柜员",
      notNull = false,
      length = "30",
      remark = "签约柜员",
      maxSize = 30
   )
   private String signUserId;
   @V(
      desc = "开始日期",
      notNull = false,
      remark = "开始日期"
   )
   private String startDate;
   @V(
      desc = "结束日期",
      notNull = false,
      remark = "结束日期"
   )
   private String endDate;
   @V(
      desc = "协议状态",
      notNull = false,
      length = "2",
      remark = "普通协议使用，可应用于大部分场景",
      maxSize = 2
   )
   private String agreementStatus;
   @V(
      desc = "客户号",
      notNull = false,
      length = "20",
      remark = "客户号",
      maxSize = 20
   )
   private String clientNo;
   @V(
      desc = "客户简称",
      notNull = false,
      length = "200",
      remark = "客户简称",
      maxSize = 200
   )
   private String clientShort;
   @V(
      desc = "法人",
      notNull = false,
      length = "20",
      remark = "法人",
      maxSize = 20
   )
   private String company;
   @V(
      desc = "推送标志",
      notNull = false,
      length = "1",
      remark = "推送标志",
      maxSize = 1
   )
   private String pushFlag;
   @V(
      desc = "电子邮件",
      notNull = false,
      length = "200",
      remark = "电子邮件",
      maxSize = 200
   )
   private String email;
   @V(
      desc = "账单周期",
      notNull = false,
      length = "2",
      remark = "账单周期",
      maxSize = 2
   )
   private String billingCycle;
   @V(
      desc = "上一账单日",
      notNull = false,
      remark = "上一账单日"
   )
   private String lastBillingDate;
   @V(
      desc = "下一账单日",
      notNull = false,
      remark = "下一账单日"
   )
   private String nextBillingDate;

   public String getAgreementId() {
      return this.agreementId;
   }

   public String getSignBranch() {
      return this.signBranch;
   }

   public String getAgreementType() {
      return this.agreementType;
   }

   public String getSignChannel() {
      return this.signChannel;
   }

   public String getSignDate() {
      return this.signDate;
   }

   public String getSignUserId() {
      return this.signUserId;
   }

   public String getStartDate() {
      return this.startDate;
   }

   public String getEndDate() {
      return this.endDate;
   }

   public String getAgreementStatus() {
      return this.agreementStatus;
   }

   public String getClientNo() {
      return this.clientNo;
   }

   public String getClientShort() {
      return this.clientShort;
   }

   public String getCompany() {
      return this.company;
   }

   public String getPushFlag() {
      return this.pushFlag;
   }

   public String getEmail() {
      return this.email;
   }

   public String getBillingCycle() {
      return this.billingCycle;
   }

   public String getLastBillingDate() {
      return this.lastBillingDate;
   }

   public String getNextBillingDate() {
      return this.nextBillingDate;
   }

   public void setAgreementId(String agreementId) {
      this.agreementId = agreementId;
   }

   public void setSignBranch(String signBranch) {
      this.signBranch = signBranch;
   }

   public void setAgreementType(String agreementType) {
      this.agreementType = agreementType;
   }

   public void setSignChannel(String signChannel) {
      this.signChannel = signChannel;
   }

   public void setSignDate(String signDate) {
      this.signDate = signDate;
   }

   public void setSignUserId(String signUserId) {
      this.signUserId = signUserId;
   }

   public void setStartDate(String startDate) {
      this.startDate = startDate;
   }

   public void setEndDate(String endDate) {
      this.endDate = endDate;
   }

   public void setAgreementStatus(String agreementStatus) {
      this.agreementStatus = agreementStatus;
   }

   public void setClientNo(String clientNo) {
      this.clientNo = clientNo;
   }

   public void setClientShort(String clientShort) {
      this.clientShort = clientShort;
   }

   public void setCompany(String company) {
      this.company = company;
   }

   public void setPushFlag(String pushFlag) {
      this.pushFlag = pushFlag;
   }

   public void setEmail(String email) {
      this.email = email;
   }

   public void setBillingCycle(String billingCycle) {
      this.billingCycle = billingCycle;
   }

   public void setLastBillingDate(String lastBillingDate) {
      this.lastBillingDate = lastBillingDate;
   }

   public void setNextBillingDate(String nextBillingDate) {
      this.nextBillingDate = nextBillingDate;
   }

   public String toString() {
      return "Core140058002Out(agreementId=" + this.getAgreementId() + ", signBranch=" + this.getSignBranch() + ", agreementType=" + this.getAgreementType() + ", signChannel=" + this.getSignChannel() + ", signDate=" + this.getSignDate() + ", signUserId=" + this.getSignUserId() + ", startDate=" + this.getStartDate() + ", endDate=" + this.getEndDate() + ", agreementStatus=" + this.getAgreementStatus() + ", clientNo=" + this.getClientNo() + ", clientShort=" + this.getClientShort() + ", company=" + this.getCompany() + ", pushFlag=" + this.getPushFlag() + ", email=" + this.getEmail() + ", billingCycle=" + this.getBillingCycle() + ", lastBillingDate=" + this.getLastBillingDate() + ", nextBillingDate=" + this.getNextBillingDate() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core140058002Out)) {
         return false;
      } else {
         Core140058002Out other = (Core140058002Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            label217: {
               Object this$agreementId = this.getAgreementId();
               Object other$agreementId = other.getAgreementId();
               if (this$agreementId == null) {
                  if (other$agreementId == null) {
                     break label217;
                  }
               } else if (this$agreementId.equals(other$agreementId)) {
                  break label217;
               }

               return false;
            }

            Object this$signBranch = this.getSignBranch();
            Object other$signBranch = other.getSignBranch();
            if (this$signBranch == null) {
               if (other$signBranch != null) {
                  return false;
               }
            } else if (!this$signBranch.equals(other$signBranch)) {
               return false;
            }

            label203: {
               Object this$agreementType = this.getAgreementType();
               Object other$agreementType = other.getAgreementType();
               if (this$agreementType == null) {
                  if (other$agreementType == null) {
                     break label203;
                  }
               } else if (this$agreementType.equals(other$agreementType)) {
                  break label203;
               }

               return false;
            }

            Object this$signChannel = this.getSignChannel();
            Object other$signChannel = other.getSignChannel();
            if (this$signChannel == null) {
               if (other$signChannel != null) {
                  return false;
               }
            } else if (!this$signChannel.equals(other$signChannel)) {
               return false;
            }

            Object this$signDate = this.getSignDate();
            Object other$signDate = other.getSignDate();
            if (this$signDate == null) {
               if (other$signDate != null) {
                  return false;
               }
            } else if (!this$signDate.equals(other$signDate)) {
               return false;
            }

            label182: {
               Object this$signUserId = this.getSignUserId();
               Object other$signUserId = other.getSignUserId();
               if (this$signUserId == null) {
                  if (other$signUserId == null) {
                     break label182;
                  }
               } else if (this$signUserId.equals(other$signUserId)) {
                  break label182;
               }

               return false;
            }

            label175: {
               Object this$startDate = this.getStartDate();
               Object other$startDate = other.getStartDate();
               if (this$startDate == null) {
                  if (other$startDate == null) {
                     break label175;
                  }
               } else if (this$startDate.equals(other$startDate)) {
                  break label175;
               }

               return false;
            }

            Object this$endDate = this.getEndDate();
            Object other$endDate = other.getEndDate();
            if (this$endDate == null) {
               if (other$endDate != null) {
                  return false;
               }
            } else if (!this$endDate.equals(other$endDate)) {
               return false;
            }

            Object this$agreementStatus = this.getAgreementStatus();
            Object other$agreementStatus = other.getAgreementStatus();
            if (this$agreementStatus == null) {
               if (other$agreementStatus != null) {
                  return false;
               }
            } else if (!this$agreementStatus.equals(other$agreementStatus)) {
               return false;
            }

            label154: {
               Object this$clientNo = this.getClientNo();
               Object other$clientNo = other.getClientNo();
               if (this$clientNo == null) {
                  if (other$clientNo == null) {
                     break label154;
                  }
               } else if (this$clientNo.equals(other$clientNo)) {
                  break label154;
               }

               return false;
            }

            label147: {
               Object this$clientShort = this.getClientShort();
               Object other$clientShort = other.getClientShort();
               if (this$clientShort == null) {
                  if (other$clientShort == null) {
                     break label147;
                  }
               } else if (this$clientShort.equals(other$clientShort)) {
                  break label147;
               }

               return false;
            }

            Object this$company = this.getCompany();
            Object other$company = other.getCompany();
            if (this$company == null) {
               if (other$company != null) {
                  return false;
               }
            } else if (!this$company.equals(other$company)) {
               return false;
            }

            Object this$pushFlag = this.getPushFlag();
            Object other$pushFlag = other.getPushFlag();
            if (this$pushFlag == null) {
               if (other$pushFlag != null) {
                  return false;
               }
            } else if (!this$pushFlag.equals(other$pushFlag)) {
               return false;
            }

            label126: {
               Object this$email = this.getEmail();
               Object other$email = other.getEmail();
               if (this$email == null) {
                  if (other$email == null) {
                     break label126;
                  }
               } else if (this$email.equals(other$email)) {
                  break label126;
               }

               return false;
            }

            label119: {
               Object this$billingCycle = this.getBillingCycle();
               Object other$billingCycle = other.getBillingCycle();
               if (this$billingCycle == null) {
                  if (other$billingCycle == null) {
                     break label119;
                  }
               } else if (this$billingCycle.equals(other$billingCycle)) {
                  break label119;
               }

               return false;
            }

            Object this$lastBillingDate = this.getLastBillingDate();
            Object other$lastBillingDate = other.getLastBillingDate();
            if (this$lastBillingDate == null) {
               if (other$lastBillingDate != null) {
                  return false;
               }
            } else if (!this$lastBillingDate.equals(other$lastBillingDate)) {
               return false;
            }

            Object this$nextBillingDate = this.getNextBillingDate();
            Object other$nextBillingDate = other.getNextBillingDate();
            if (this$nextBillingDate == null) {
               if (other$nextBillingDate != null) {
                  return false;
               }
            } else if (!this$nextBillingDate.equals(other$nextBillingDate)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core140058002Out;
   }
}
