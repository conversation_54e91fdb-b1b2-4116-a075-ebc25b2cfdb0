package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000171In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000171Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12000171 {
   String URL = "/rb/nfin/printbook/change";


   @ApiRemark("标准优化")
   @ApiDesc("用于卡配对账簿的更换")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0171"
   )
   @BusinessCategory("1200-非金融")
   @FunctionCategory("RB20-借记卡")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core12000171Out runService(Core12000171In var1);
}
