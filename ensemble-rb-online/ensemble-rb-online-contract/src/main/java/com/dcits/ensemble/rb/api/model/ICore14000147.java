package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000147In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000147Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000147 {
   String URL = "/rb/nfin/agreement/csl/query";

   
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0147"
   )
   Core14000147Out runService(Core14000147In var1);
}
