package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000176In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000176Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000176 {
   String URL = "/rb/inq/acctriskupdate/query";


   @ApiRemark("账户风险等级变更查询")
   @ApiDesc("账户缝隙等级变更查询")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0176"
   )
   @BusinessCategory("1400-查询")
   @FunctionCategory("RB02-账户管理")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core14000176Out runService(Core14000176In var1);
}
