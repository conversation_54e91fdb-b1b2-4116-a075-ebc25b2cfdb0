package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400061086Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "携带证开立维护登机簿",
      notNull = false,
      remark = "携带证开立维护登机簿"
   )
   private List<Core1400061086Out.RbExchangeTakedocuRegList> rbExchangeTakedocuRegList;

   public List<Core1400061086Out.RbExchangeTakedocuRegList> getRbExchangeTakedocuRegList() {
      return this.rbExchangeTakedocuRegList;
   }

   public void setRbExchangeTakedocuRegList(List<Core1400061086Out.RbExchangeTakedocuRegList> rbExchangeTakedocuRegList) {
      this.rbExchangeTakedocuRegList = rbExchangeTakedocuRegList;
   }

   public String toString() {
      return "Core1400061086Out(rbExchangeTakedocuRegList=" + this.getRbExchangeTakedocuRegList() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400061086Out)) {
         return false;
      } else {
         Core1400061086Out other = (Core1400061086Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$rbExchangeTakedocuRegList = this.getRbExchangeTakedocuRegList();
            Object other$rbExchangeTakedocuRegList = other.getRbExchangeTakedocuRegList();
            if (this$rbExchangeTakedocuRegList == null) {
               if (other$rbExchangeTakedocuRegList != null) {
                  return false;
               }
            } else if (!this$rbExchangeTakedocuRegList.equals(other$rbExchangeTakedocuRegList)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400061086Out;
   }
   public static class RbExchangeTakedocuRegList {
      @V(
         desc = "所属机构号",
         notNull = false,
         length = "50",
         remark = "所属机构号",
         maxSize = 50
      )
      private String branch;
      @V(
         desc = "证件类型",
         notNull = false,
         length = "3",
         remark = "证件类型",
         maxSize = 3
      )
      private String documentType;
      @V(
         desc = "证件号码",
         notNull = false,
         length = "2000",
         remark = "证件号码",
         maxSize = 2000
      )
      private String documentNo;
      @V(
         desc = "客户名称",
         notNull = false,
         length = "200",
         remark = "客户名称",
         maxSize = 200
      )
      private String clientName;
      @V(
         desc = "国籍",
         notNull = false,
         length = "10",
         remark = "国籍",
         maxSize = 10
      )
      private String national;
      @V(
         desc = "目的地",
         notNull = false,
         length = "100",
         remark = "目的地",
         maxSize = 100
      )
      private String bourn;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "金额",
         notNull = false,
         length = "17",
         remark = "金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal amt;
      @V(
         desc = "生效日期",
         notNull = false,
         remark = "生效日期"
      )
      private String effectDate;
      @V(
         desc = "凭证号码",
         notNull = false,
         length = "50",
         remark = "凭证号码",
         maxSize = 50
      )
      private String voucherNo;
      @V(
         desc = "凭证类型",
         notNull = false,
         length = "10",
         remark = "凭证类型",
         maxSize = 10
      )
      private String docType;
      @V(
         desc = "前缀",
         notNull = false,
         length = "10",
         remark = "前缀",
         maxSize = 10
      )
      private String prefix;
      @V(
         desc = "旧凭证类型",
         notNull = false,
         length = "10",
         remark = "旧凭证类型",
         maxSize = 10
      )
      private String oldDocType;
      @V(
         desc = "旧凭证前缀",
         notNull = false,
         length = "10",
         remark = "旧凭证前缀",
         maxSize = 10
      )
      private String oldPrefix;
      @V(
         desc = "旧凭证号码",
         notNull = false,
         length = "50",
         remark = "旧凭证号码",
         maxSize = 50
      )
      private String oldVoucherNo;
      @V(
         desc = "凭证状态",
         notNull = false,
         length = "3",
         remark = "凭证状态",
         maxSize = 3
      )
      private String voucherStatus;
      @V(
         desc = "凭证原状态",
         notNull = false,
         length = "3",
         remark = "凭证原状态",
         maxSize = 3
      )
      private String oldStatus;
      @V(
         desc = "交易柜员",
         notNull = false,
         length = "30",
         remark = "交易柜员",
         maxSize = 30
      )
      private String userId;

      public String getBranch() {
         return this.branch;
      }

      public String getDocumentType() {
         return this.documentType;
      }

      public String getDocumentNo() {
         return this.documentNo;
      }

      public String getClientName() {
         return this.clientName;
      }

      public String getNational() {
         return this.national;
      }

      public String getBourn() {
         return this.bourn;
      }

      public String getCcy() {
         return this.ccy;
      }

      public BigDecimal getAmt() {
         return this.amt;
      }

      public String getEffectDate() {
         return this.effectDate;
      }

      public String getVoucherNo() {
         return this.voucherNo;
      }

      public String getDocType() {
         return this.docType;
      }

      public String getPrefix() {
         return this.prefix;
      }

      public String getOldDocType() {
         return this.oldDocType;
      }

      public String getOldPrefix() {
         return this.oldPrefix;
      }

      public String getOldVoucherNo() {
         return this.oldVoucherNo;
      }

      public String getVoucherStatus() {
         return this.voucherStatus;
      }

      public String getOldStatus() {
         return this.oldStatus;
      }

      public String getUserId() {
         return this.userId;
      }

      public void setBranch(String branch) {
         this.branch = branch;
      }

      public void setDocumentType(String documentType) {
         this.documentType = documentType;
      }

      public void setDocumentNo(String documentNo) {
         this.documentNo = documentNo;
      }

      public void setClientName(String clientName) {
         this.clientName = clientName;
      }

      public void setNational(String national) {
         this.national = national;
      }

      public void setBourn(String bourn) {
         this.bourn = bourn;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setAmt(BigDecimal amt) {
         this.amt = amt;
      }

      public void setEffectDate(String effectDate) {
         this.effectDate = effectDate;
      }

      public void setVoucherNo(String voucherNo) {
         this.voucherNo = voucherNo;
      }

      public void setDocType(String docType) {
         this.docType = docType;
      }

      public void setPrefix(String prefix) {
         this.prefix = prefix;
      }

      public void setOldDocType(String oldDocType) {
         this.oldDocType = oldDocType;
      }

      public void setOldPrefix(String oldPrefix) {
         this.oldPrefix = oldPrefix;
      }

      public void setOldVoucherNo(String oldVoucherNo) {
         this.oldVoucherNo = oldVoucherNo;
      }

      public void setVoucherStatus(String voucherStatus) {
         this.voucherStatus = voucherStatus;
      }

      public void setOldStatus(String oldStatus) {
         this.oldStatus = oldStatus;
      }

      public void setUserId(String userId) {
         this.userId = userId;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400061086Out.RbExchangeTakedocuRegList)) {
            return false;
         } else {
            Core1400061086Out.RbExchangeTakedocuRegList other = (Core1400061086Out.RbExchangeTakedocuRegList)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$branch = this.getBranch();
               Object other$branch = other.getBranch();
               if (this$branch == null) {
                  if (other$branch != null) {
                     return false;
                  }
               } else if (!this$branch.equals(other$branch)) {
                  return false;
               }

               Object this$documentType = this.getDocumentType();
               Object other$documentType = other.getDocumentType();
               if (this$documentType == null) {
                  if (other$documentType != null) {
                     return false;
                  }
               } else if (!this$documentType.equals(other$documentType)) {
                  return false;
               }

               Object this$documentNo = this.getDocumentNo();
               Object other$documentNo = other.getDocumentNo();
               if (this$documentNo == null) {
                  if (other$documentNo != null) {
                     return false;
                  }
               } else if (!this$documentNo.equals(other$documentNo)) {
                  return false;
               }

               label206: {
                  Object this$clientName = this.getClientName();
                  Object other$clientName = other.getClientName();
                  if (this$clientName == null) {
                     if (other$clientName == null) {
                        break label206;
                     }
                  } else if (this$clientName.equals(other$clientName)) {
                     break label206;
                  }

                  return false;
               }

               label199: {
                  Object this$national = this.getNational();
                  Object other$national = other.getNational();
                  if (this$national == null) {
                     if (other$national == null) {
                        break label199;
                     }
                  } else if (this$national.equals(other$national)) {
                     break label199;
                  }

                  return false;
               }

               Object this$bourn = this.getBourn();
               Object other$bourn = other.getBourn();
               if (this$bourn == null) {
                  if (other$bourn != null) {
                     return false;
                  }
               } else if (!this$bourn.equals(other$bourn)) {
                  return false;
               }

               label185: {
                  Object this$ccy = this.getCcy();
                  Object other$ccy = other.getCcy();
                  if (this$ccy == null) {
                     if (other$ccy == null) {
                        break label185;
                     }
                  } else if (this$ccy.equals(other$ccy)) {
                     break label185;
                  }

                  return false;
               }

               label178: {
                  Object this$amt = this.getAmt();
                  Object other$amt = other.getAmt();
                  if (this$amt == null) {
                     if (other$amt == null) {
                        break label178;
                     }
                  } else if (this$amt.equals(other$amt)) {
                     break label178;
                  }

                  return false;
               }

               Object this$effectDate = this.getEffectDate();
               Object other$effectDate = other.getEffectDate();
               if (this$effectDate == null) {
                  if (other$effectDate != null) {
                     return false;
                  }
               } else if (!this$effectDate.equals(other$effectDate)) {
                  return false;
               }

               Object this$voucherNo = this.getVoucherNo();
               Object other$voucherNo = other.getVoucherNo();
               if (this$voucherNo == null) {
                  if (other$voucherNo != null) {
                     return false;
                  }
               } else if (!this$voucherNo.equals(other$voucherNo)) {
                  return false;
               }

               label157: {
                  Object this$docType = this.getDocType();
                  Object other$docType = other.getDocType();
                  if (this$docType == null) {
                     if (other$docType == null) {
                        break label157;
                     }
                  } else if (this$docType.equals(other$docType)) {
                     break label157;
                  }

                  return false;
               }

               label150: {
                  Object this$prefix = this.getPrefix();
                  Object other$prefix = other.getPrefix();
                  if (this$prefix == null) {
                     if (other$prefix == null) {
                        break label150;
                     }
                  } else if (this$prefix.equals(other$prefix)) {
                     break label150;
                  }

                  return false;
               }

               Object this$oldDocType = this.getOldDocType();
               Object other$oldDocType = other.getOldDocType();
               if (this$oldDocType == null) {
                  if (other$oldDocType != null) {
                     return false;
                  }
               } else if (!this$oldDocType.equals(other$oldDocType)) {
                  return false;
               }

               label136: {
                  Object this$oldPrefix = this.getOldPrefix();
                  Object other$oldPrefix = other.getOldPrefix();
                  if (this$oldPrefix == null) {
                     if (other$oldPrefix == null) {
                        break label136;
                     }
                  } else if (this$oldPrefix.equals(other$oldPrefix)) {
                     break label136;
                  }

                  return false;
               }

               Object this$oldVoucherNo = this.getOldVoucherNo();
               Object other$oldVoucherNo = other.getOldVoucherNo();
               if (this$oldVoucherNo == null) {
                  if (other$oldVoucherNo != null) {
                     return false;
                  }
               } else if (!this$oldVoucherNo.equals(other$oldVoucherNo)) {
                  return false;
               }

               label122: {
                  Object this$voucherStatus = this.getVoucherStatus();
                  Object other$voucherStatus = other.getVoucherStatus();
                  if (this$voucherStatus == null) {
                     if (other$voucherStatus == null) {
                        break label122;
                     }
                  } else if (this$voucherStatus.equals(other$voucherStatus)) {
                     break label122;
                  }

                  return false;
               }

               Object this$oldStatus = this.getOldStatus();
               Object other$oldStatus = other.getOldStatus();
               if (this$oldStatus == null) {
                  if (other$oldStatus != null) {
                     return false;
                  }
               } else if (!this$oldStatus.equals(other$oldStatus)) {
                  return false;
               }

               Object this$userId = this.getUserId();
               Object other$userId = other.getUserId();
               if (this$userId == null) {
                  if (other$userId != null) {
                     return false;
                  }
               } else if (!this$userId.equals(other$userId)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400061086Out.RbExchangeTakedocuRegList;
      }
      public String toString() {
         return "Core1400061086Out.RbExchangeTakedocuRegList(branch=" + this.getBranch() + ", documentType=" + this.getDocumentType() + ", documentNo=" + this.getDocumentNo() + ", clientName=" + this.getClientName() + ", national=" + this.getNational() + ", bourn=" + this.getBourn() + ", ccy=" + this.getCcy() + ", amt=" + this.getAmt() + ", effectDate=" + this.getEffectDate() + ", voucherNo=" + this.getVoucherNo() + ", docType=" + this.getDocType() + ", prefix=" + this.getPrefix() + ", oldDocType=" + this.getOldDocType() + ", oldPrefix=" + this.getOldPrefix() + ", oldVoucherNo=" + this.getOldVoucherNo() + ", voucherStatus=" + this.getVoucherStatus() + ", oldStatus=" + this.getOldStatus() + ", userId=" + this.getUserId() + ")";
      }
   }
}
