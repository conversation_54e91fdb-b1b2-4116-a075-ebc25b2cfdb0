package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000124In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000124Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000124 {
   String URL = "/rb/inq/prod/totalcheck";

   
   @ApiDesc("产品余额总分核对结果查询")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0124"
   )
   @FunctionCategory("RB08-特殊业务")
   Core14000124Out runService(Core14000124In var1);
}
