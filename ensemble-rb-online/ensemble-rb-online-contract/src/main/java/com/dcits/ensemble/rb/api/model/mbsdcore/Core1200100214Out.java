package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;

@MessageOut
public class Core1200100214Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "备案额度",
      notNull = false,
      length = "17",
      remark = "调整后备案额度",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal recordLimit;
   @V(
      desc = "剩余额度",
      notNull = false,
      length = "17",
      remark = "剩余额度",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal leaveLimit;

   public BigDecimal getRecordLimit() {
      return this.recordLimit;
   }

   public BigDecimal getLeaveLimit() {
      return this.leaveLimit;
   }

   public void setRecordLimit(BigDecimal recordLimit) {
      this.recordLimit = recordLimit;
   }

   public void setLeaveLimit(BigDecimal leaveLimit) {
      this.leaveLimit = leaveLimit;
   }

   public String toString() {
      return "Core1200100214Out(recordLimit=" + this.getRecordLimit() + ", leaveLimit=" + this.getLeaveLimit() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200100214Out)) {
         return false;
      } else {
         Core1200100214Out other = (Core1200100214Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$recordLimit = this.getRecordLimit();
            Object other$recordLimit = other.getRecordLimit();
            if (this$recordLimit == null) {
               if (other$recordLimit != null) {
                  return false;
               }
            } else if (!this$recordLimit.equals(other$recordLimit)) {
               return false;
            }

            Object this$leaveLimit = this.getLeaveLimit();
            Object other$leaveLimit = other.getLeaveLimit();
            if (this$leaveLimit == null) {
               if (other$leaveLimit != null) {
                  return false;
               }
            } else if (!this$leaveLimit.equals(other$leaveLimit)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200100214Out;
   }
}
