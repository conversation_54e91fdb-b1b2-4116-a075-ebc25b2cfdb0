package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core13000003In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core13000003Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore13000003 {
   String URL = "/rb/rev/inner/tran/common";


   @ApiRemark("内部账统一冲正")
   @ApiDesc("内部账统一冲正,设计场景内部账挂销账冲正，手工结息冲正，定期一本通开立冲正等")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1300",
      messageCode = "0003"
   )
   @BusinessCategory("1300")
   @FunctionCategory("RB08-特殊业务")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core13000003Out runService(Core13000003In var1);
}
