package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400100010Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100010Out.TranHistArray> tranHistArray;
   @V(
      desc = "借方金额",
      notNull = false,
      length = "38",
      remark = "借方金额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal drTotalAmt;
   @V(
      desc = "贷方总金额",
      notNull = false,
      length = "38",
      remark = "贷方总金额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal crTotalAmt;
   @V(
      desc = "借方交易总笔数",
      notNull = false,
      length = "10",
      remark = "借方交易总笔数"
   )
   private Long drTotalCnt;
   @V(
      desc = "贷方交易总笔数",
      notNull = false,
      length = "10",
      remark = "贷方交易总笔数"
   )
   private Long crTotalCnt;

   public List<Core1400100010Out.TranHistArray> getTranHistArray() {
      return this.tranHistArray;
   }

   public BigDecimal getDrTotalAmt() {
      return this.drTotalAmt;
   }

   public BigDecimal getCrTotalAmt() {
      return this.crTotalAmt;
   }

   public Long getDrTotalCnt() {
      return this.drTotalCnt;
   }

   public Long getCrTotalCnt() {
      return this.crTotalCnt;
   }

   public void setTranHistArray(List<Core1400100010Out.TranHistArray> tranHistArray) {
      this.tranHistArray = tranHistArray;
   }

   public void setDrTotalAmt(BigDecimal drTotalAmt) {
      this.drTotalAmt = drTotalAmt;
   }

   public void setCrTotalAmt(BigDecimal crTotalAmt) {
      this.crTotalAmt = crTotalAmt;
   }

   public void setDrTotalCnt(Long drTotalCnt) {
      this.drTotalCnt = drTotalCnt;
   }

   public void setCrTotalCnt(Long crTotalCnt) {
      this.crTotalCnt = crTotalCnt;
   }

   public String toString() {
      return "Core1400100010Out(tranHistArray=" + this.getTranHistArray() + ", drTotalAmt=" + this.getDrTotalAmt() + ", crTotalAmt=" + this.getCrTotalAmt() + ", drTotalCnt=" + this.getDrTotalCnt() + ", crTotalCnt=" + this.getCrTotalCnt() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100010Out)) {
         return false;
      } else {
         Core1400100010Out other = (Core1400100010Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            label73: {
               Object this$tranHistArray = this.getTranHistArray();
               Object other$tranHistArray = other.getTranHistArray();
               if (this$tranHistArray == null) {
                  if (other$tranHistArray == null) {
                     break label73;
                  }
               } else if (this$tranHistArray.equals(other$tranHistArray)) {
                  break label73;
               }

               return false;
            }

            Object this$drTotalAmt = this.getDrTotalAmt();
            Object other$drTotalAmt = other.getDrTotalAmt();
            if (this$drTotalAmt == null) {
               if (other$drTotalAmt != null) {
                  return false;
               }
            } else if (!this$drTotalAmt.equals(other$drTotalAmt)) {
               return false;
            }

            label59: {
               Object this$crTotalAmt = this.getCrTotalAmt();
               Object other$crTotalAmt = other.getCrTotalAmt();
               if (this$crTotalAmt == null) {
                  if (other$crTotalAmt == null) {
                     break label59;
                  }
               } else if (this$crTotalAmt.equals(other$crTotalAmt)) {
                  break label59;
               }

               return false;
            }

            Object this$drTotalCnt = this.getDrTotalCnt();
            Object other$drTotalCnt = other.getDrTotalCnt();
            if (this$drTotalCnt == null) {
               if (other$drTotalCnt != null) {
                  return false;
               }
            } else if (!this$drTotalCnt.equals(other$drTotalCnt)) {
               return false;
            }

            Object this$crTotalCnt = this.getCrTotalCnt();
            Object other$crTotalCnt = other.getCrTotalCnt();
            if (this$crTotalCnt == null) {
               if (other$crTotalCnt != null) {
                  return false;
               }
            } else if (!this$crTotalCnt.equals(other$crTotalCnt)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100010Out;
   }
   public static class TranHistArray {
      @V(
         desc = "证件类型",
         notNull = false,
         length = "3",
         remark = "证件类型",
         maxSize = 3
      )
      private String documentType;
      @V(
         desc = "证件号码",
         notNull = false,
         length = "50",
         remark = "证件号码",
         maxSize = 50
      )
      private String documentId;
      @V(
         desc = "客户中文名称",
         notNull = false,
         length = "200",
         remark = "客户中文名称",
         maxSize = 200
      )
      private String chClientName;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "客户类型",
         notNull = false,
         length = "3",
         remark = "客户大类，目前一般分为个人，公司，金融机构和内部客户。取之于CIF_CLIENT_TYPE.CLIENT_TYPE",
         maxSize = 3
      )
      private String clientType;
      @V(
         desc = "客户名称",
         notNull = false,
         length = "200",
         remark = "客户名称",
         maxSize = 200
      )
      private String clientName;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "账户开户行",
         notNull = false,
         length = "50",
         remark = "账户实际开户机构，柜面为实际网点机构，线上渠道一般为对应主账户的实际开户机构",
         maxSize = 50
      )
      private String acctBranch;
      @V(
         desc = "金额类型",
         notNull = false,
         length = "10",
         remark = "金额类型",
         maxSize = 10
      )
      private String amtType;
      @V(
         desc = "交易前余额",
         notNull = false,
         length = "17",
         remark = "交易前余额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal previousBalAmt;
      @V(
         desc = "实际余额",
         notNull = false,
         length = "17",
         remark = "实际余额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal actualBal;
      @V(
         desc = "账户描述",
         notNull = false,
         length = "200",
         remark = "账户描述,目前同账户名称",
         maxSize = 200
      )
      private String acctDesc;
      @V(
         desc = "交易金额",
         notNull = false,
         length = "17",
         remark = "交易金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal tranAmt;
      @V(
         desc = "渠道流水号",
         notNull = false,
         length = "50",
         remark = "渠道流水号",
         maxSize = 50
      )
      private String channelSeqNo;
      @V(
         desc = "交易类型",
         notNull = false,
         length = "10",
         remark = "交易类型",
         maxSize = 10
      )
      private String tranType;
      @V(
         desc = "转出交易类型",
         notNull = false,
         length = "10",
         remark = "转出交易类型",
         maxSize = 10
      )
      private String drTranType;
      @V(
         desc = "交易对手身份证件/证明文件类型",
         notNull = false,
         length = "3",
         remark = "交易对手身份证件/证明文件类型",
         maxSize = 3
      )
      private String othDocumentType;
      @V(
         desc = "交易对手身份证件/证明文件号码",
         notNull = false,
         length = "50",
         remark = "交易对手身份证件/证明文件号码",
         maxSize = 50
      )
      private String othDocumentId;
      @V(
         desc = "对方账号/卡号",
         notNull = false,
         length = "50",
         remark = "对方账号/卡号",
         maxSize = 50
      )
      private String othBaseAcctNo;
      @V(
         desc = "对方账户产品类型",
         notNull = false,
         length = "20",
         remark = "对方账户产品类型",
         maxSize = 20
      )
      private String othProdType;
      @V(
         desc = "对方账户币种",
         notNull = false,
         length = "3",
         remark = "对方账户币种",
         maxSize = 3
      )
      private String othAcctCcy;
      @V(
         desc = "对方账户序列号",
         notNull = false,
         length = "5",
         remark = "对方账户序列号",
         maxSize = 5
      )
      private String othAcctSeqNo;
      @V(
         desc = "对方账户描述",
         notNull = false,
         length = "200",
         remark = "对方账户描述",
         maxSize = 200
      )
      private String othAcctDesc;
      @V(
         desc = "对方账户开户机构",
         notNull = false,
         length = "50",
         remark = "对方账户开户机构",
         maxSize = 50
      )
      private String othBranch;
      @V(
         desc = "收款行行名",
         notNull = false,
         length = "200",
         remark = "收款行行名",
         maxSize = 200
      )
      private String othBranchName;
      @V(
         desc = "对方银行名称",
         notNull = false,
         length = "50",
         remark = "对方银行名称",
         maxSize = 50
      )
      private String othBankName;
      @V(
         desc = "对方银行代码",
         notNull = false,
         length = "20",
         remark = "对方银行代码",
         maxSize = 20
      )
      private String othBankCode;
      @V(
         desc = "对方交易流水号",
         notNull = false,
         length = "50",
         remark = "对方交易流水号",
         maxSize = 50
      )
      private String othSeqNo;
      @V(
         desc = "对方交易参考号",
         notNull = false,
         length = "50",
         remark = "对方交易参考号",
         maxSize = 50
      )
      private String othReference;
      @V(
         desc = "收款方账号",
         notNull = false,
         length = "50",
         remark = "收款方账号",
         maxSize = 50
      )
      private String toAcctNo;
      @V(
         desc = "转出户名",
         notNull = false,
         length = "200",
         remark = "转出户名",
         maxSize = 200
      )
      private String toAcctName;
      @V(
         desc = "付款方账号",
         notNull = false,
         length = "50",
         remark = "付款方账号",
         maxSize = 50
      )
      private String sourceAcctNo;
      @V(
         desc = "付款方名称",
         notNull = false,
         length = "200",
         remark = "付款方名称",
         maxSize = 200
      )
      private String sourceAcctName;
      @V(
         desc = "凭证类型",
         notNull = false,
         length = "10",
         remark = "凭证类型",
         maxSize = 10
      )
      private String docType;
      @V(
         desc = "前缀",
         notNull = false,
         length = "10",
         remark = "前缀",
         maxSize = 10
      )
      private String prefix;
      @V(
         desc = "凭证号码",
         notNull = false,
         length = "50",
         remark = "凭证号码",
         maxSize = 50
      )
      private String voucherNo;
      @V(
         desc = "支取方式",
         notNull = false,
         length = "1",
         remark = "支取方式",
         maxSize = 1
      )
      private String withdrawalType;
      @V(
         desc = "账户用途",
         notNull = false,
         length = "10",
         remark = "账户用途",
         maxSize = 10
      )
      private String reasonCode;
      @V(
         desc = "交易柜员",
         notNull = false,
         length = "30",
         remark = "交易柜员",
         maxSize = 30
      )
      private String userId;
      @V(
         desc = "银行交易序号",
         notNull = false,
         length = "50",
         remark = "银行交易序号,单一机构下发生交易序号，按顺序递增 格式为 \"机构_序号",
         maxSize = 50
      )
      private String bankSeqNo;
      @V(
         desc = "授权柜员",
         notNull = false,
         length = "30",
         remark = "授权柜员",
         maxSize = 30
      )
      private String authUserId;
      @V(
         desc = "复核柜员",
         notNull = false,
         length = "30",
         remark = "复核柜员",
         maxSize = 30
      )
      private String apprUserId;
      @V(
         desc = "银行名称",
         notNull = false,
         length = "50",
         remark = "银行名称",
         maxSize = 50
      )
      private String bankName;
      @V(
         desc = "银行代码",
         notNull = false,
         length = "20",
         remark = "银行代码",
         maxSize = 20
      )
      private String bankCode;
      @V(
         desc = "所属机构号",
         notNull = false,
         length = "50",
         remark = "所属机构号",
         maxSize = 50
      )
      private String branch;
      @V(
         desc = "交易参考号",
         notNull = false,
         length = "50",
         remark = "交易参考号",
         maxSize = 50
      )
      private String reference;
      @V(
         desc = "交易种类",
         notNull = false,
         length = "5",
         remark = "交易种类",
         maxSize = 5
      )
      private String tranCategory;
      @V(
         desc = "生效日期",
         notNull = false,
         remark = "生效日期"
      )
      private String effectDate;
      @V(
         desc = "冲正交易类型",
         notNull = false,
         length = "10",
         remark = "冲正交易类型",
         maxSize = 10
      )
      private String reversalTranType;
      @V(
         desc = "冲正日期",
         notNull = false,
         remark = "冲正日期"
      )
      private String reversalDate;
      @V(
         desc = "序号",
         notNull = false,
         length = "50",
         remark = "序号",
         maxSize = 50
      )
      private String seqNo;
      @V(
         desc = "交易描述",
         notNull = false,
         length = "200",
         remark = "交易描述",
         maxSize = 200
      )
      private String tranDesc;
      @V(
         desc = "交易附言",
         notNull = false,
         length = "2000",
         remark = "交易附言",
         maxSize = 2000
      )
      private String tranNote;
      @V(
         desc = "主交易序号",
         notNull = false,
         length = "50",
         remark = "主交易序号",
         maxSize = 50
      )
      private String primaryTranSeqNo;
      @V(
         desc = "资金冻结流水号",
         notNull = false,
         length = "50",
         remark = "记录冻结编号信息",
         maxSize = 50
      )
      private String fhSeqNo;
      @V(
         desc = "清算日期",
         notNull = false,
         remark = "清算日期"
      )
      private String settlementDate;
      @V(
         desc = "他行等值金额",
         notNull = false,
         length = "17",
         remark = "他行等值金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal contraEquivAmt;
      @V(
         desc = "基础等值金额",
         notNull = false,
         length = "17",
         remark = "基础等值金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal baseEquivAmt;
      @V(
         desc = "交叉汇率",
         notNull = false,
         length = "15",
         remark = "交叉汇率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal crossRate;
      @V(
         desc = "起始币种",
         notNull = false,
         length = "3",
         remark = "源币种",
         maxSize = 3
      )
      private String fromCcy;
      @V(
         desc = "移出金额",
         notNull = false,
         length = "17",
         remark = "移出金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal fromAmount;
      @V(
         desc = "买方交易汇率标志",
         notNull = false,
         length = "1",
         remark = "买方交易汇率标志",
         maxSize = 1
      )
      private String fromRateFlag;
      @V(
         desc = "买方汇率值",
         notNull = false,
         length = "15",
         remark = "买方汇率值",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal fromXrate;
      @V(
         desc = "目的币种",
         notNull = false,
         length = "3",
         remark = "目标币种",
         maxSize = 3
      )
      private String toCcy;
      @V(
         desc = "移入金额",
         notNull = false,
         length = "17",
         remark = "移入金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal toAmount;
      @V(
         desc = "卖方交易汇率标志",
         notNull = false,
         length = "1",
         remark = "卖方交易汇率标志",
         maxSize = 1
      )
      private String toRateFlag;
      @V(
         desc = "卖方汇率值",
         notNull = false,
         length = "15",
         remark = "卖方汇率值",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal toXrate;
      @V(
         desc = "根据实际交易时修改交叉汇率计算的金额",
         notNull = false,
         length = "17",
         remark = "根据实际交易时修改交叉汇率计算的金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal ovToAmount;
      @V(
         desc = "汇率标志",
         notNull = false,
         length = "1",
         remark = "汇率标志",
         maxSize = 1
      )
      private String rateFlag;
      @V(
         desc = "牌价类型",
         notNull = false,
         length = "1",
         remark = "牌价类型",
         maxSize = 1
      )
      private String quoteType;
      @V(
         desc = "终端号",
         notNull = false,
         length = "50",
         remark = "终端号",
         maxSize = 50
      )
      private String terminalId;
      @V(
         desc = "跟踪ID",
         notNull = false,
         length = "200",
         remark = "跟踪ID",
         maxSize = 200
      )
      private String traceId;
      @V(
         desc = "摘要",
         notNull = false,
         length = "500",
         remark = "开户时的账号用途，销户时的销户原因",
         maxSize = 500
      )
      private String narrative;
      @V(
         desc = "现金项目",
         notNull = false,
         length = "10",
         remark = "现金项目",
         maxSize = 10
      )
      private String cashItem;
      @V(
         desc = "业务处理状态",
         notNull = false,
         length = "1",
         remark = "业务处理状态",
         maxSize = 1
      )
      private String tranStatus;
      @V(
         desc = "利润中心",
         notNull = false,
         length = "20",
         remark = "利润中心",
         maxSize = 20
      )
      private String profitCenter;
      @V(
         desc = "账套",
         notNull = false,
         length = "10",
         remark = "账套",
         maxSize = 10
      )
      private String businessUnit;
      @V(
         desc = "源模块",
         notNull = false,
         length = "3",
         remark = "源模块",
         maxSize = 3
      )
      private String sourceModule;
      @V(
         desc = "事件类型",
         notNull = false,
         length = "20",
         remark = "事件类型",
         maxSize = 20
      )
      private String eventType;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "借贷标识",
         notNull = false,
         length = "1",
         remark = "借贷标识",
         maxSize = 1
      )
      private String crDrMaintInd;
      @V(
         desc = "余额类型",
         notNull = false,
         length = "2",
         remark = "余额类型",
         maxSize = 2
      )
      private String balType;
      @V(
         desc = "打印次数",
         notNull = false,
         length = "5",
         remark = "银行承兑汇票登记簿凭证打印次数"
      )
      private Integer printCnt;
      @V(
         desc = "批次号",
         notNull = false,
         length = "50",
         remark = "批次号",
         maxSize = 50
      )
      private String batchNo;
      @V(
         desc = "中间业务类型",
         notNull = false,
         length = "10",
         remark = "中间业务类型",
         maxSize = 10
      )
      private String bizType;
      @V(
         desc = "是否补登存",
         notNull = false,
         length = "1",
         remark = "是否补登存",
         maxSize = 1
      )
      private String pbkUpdFlag;
      @V(
         desc = "渠道类型",
         notNull = false,
         length = "10",
         remark = "渠道类型",
         maxSize = 10
      )
      private String sourceType;
      @V(
         desc = "是否冲正标志",
         notNull = false,
         length = "1",
         remark = "是否冲正标志",
         maxSize = 1
      )
      private String reversal;
      @V(
         desc = "交款单位",
         notNull = false,
         length = "200",
         remark = "交款单位",
         maxSize = 200
      )
      private String payUnit;
      @V(
         desc = "服务费标识",
         notNull = false,
         length = "1",
         remark = "服务费标识",
         maxSize = 1
      )
      private String servCharge;
      @V(
         desc = "法人",
         notNull = false,
         length = "20",
         remark = "法人",
         maxSize = 20
      )
      private String company;
      @V(
         desc = "账户开户日期",
         notNull = false,
         remark = "账户开户日期"
      )
      private String acctOpenDate;
      @V(
         desc = "存期期限",
         notNull = false,
         length = "5",
         remark = "期限",
         maxSize = 5
      )
      private String term;
      @V(
         desc = "期限类型",
         notNull = false,
         length = "1",
         remark = "期限类型",
         maxSize = 1
      )
      private String termType;
      @V(
         desc = "对方账户名称",
         notNull = false,
         length = "200",
         remark = "对方账户名称",
         maxSize = 200
      )
      private String othAcctName;
      @V(
         desc = "执行利率",
         notNull = false,
         length = "15",
         remark = "执行利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal realRate;
      @V(
         desc = "行内利率",
         notNull = false,
         length = "15",
         remark = "在人行基准利率调整后对客发布的行内利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal actualRate;
      @V(
         desc = "真实交易对手账号",
         notNull = false,
         length = "50",
         remark = "真实交易对手账号",
         maxSize = 50
      )
      private String othRealBaseAcctNo;
      @V(
         desc = "真实交易对手名称",
         notNull = false,
         length = "200",
         remark = "真实交易对手名称",
         maxSize = 200
      )
      private String othRealTranName;
      @V(
         desc = "机构名称",
         notNull = false,
         length = "200",
         remark = "机构名称",
         maxSize = 200
      )
      private String branchName;
      @V(
         desc = "卖方牌价类型",
         notNull = false,
         length = "1",
         remark = "牌价类型",
         maxSize = 1
      )
      private String toId;
      @V(
         desc = "本金金额",
         notNull = false,
         length = "17",
         remark = "本金金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal priAmt;
      @V(
         desc = "小额免密标志",
         notNull = false,
         length = "1",
         remark = "小额免密标志",
         maxSize = 1
      )
      private String piFlag;
      @V(
         desc = "交易日期",
         notNull = false,
         remark = "交易日期"
      )
      private String tranDate;
      @V(
         desc = "北向通签约状态",
         notNull = false,
         length = "10",
         remark = "北向通签约状态",
         maxSize = 10
      )
      private String northboundStatus;
      @V(
         desc = "摘要码",
         notNull = false,
         length = "30",
         remark = "摘要码",
         maxSize = 30
      )
      private String narrativeCode;
      @V(
         desc = "交易时间戳",
         notNull = false,
         length = "26",
         remark = "交易时间戳",
         maxSize = 26
      )
      private String tranTimestamp;
      @V(
         desc = "他行交易日期",
         notNull = false,
         length = "30",
         remark = "他行交易日期",
         maxSize = 30
      )
      private String contraTranDate;
      @V(
         desc = "子流水号",
         notNull = false,
         length = "100",
         remark = "子流水号",
         maxSize = 100
      )
      private String subSeqNo;
      @V(
         desc = "真实对方金融机构名称",
         notNull = false,
         length = "50",
         remark = "真实对方金融机构名称",
         maxSize = 50
      )
      private String othRealBankName;
      @V(
         desc = "摘要码描述",
         notNull = false,
         length = "100",
         remark = "摘要码描述",
         maxSize = 100
      )
      private String narrativeCodeDesc;
      @V(
         desc = "原始交易时间戳",
         notNull = false,
         length = "26",
         remark = "原始交易时间戳",
         maxSize = 26
      )
      private String origTranTimestamp;
      @V(
         desc = "真实金融交易机构",
         notNull = false,
         length = "100",
         remark = "真实金融交易机构",
         maxSize = 100
      )
      private String realTranBranchName;
      @V(
         desc = "透支金额",
         notNull = false,
         length = "17",
         remark = "透支金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal odAmount;

      public String getDocumentType() {
         return this.documentType;
      }

      public String getDocumentId() {
         return this.documentId;
      }

      public String getChClientName() {
         return this.chClientName;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public String getClientType() {
         return this.clientType;
      }

      public String getClientName() {
         return this.clientName;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getAcctBranch() {
         return this.acctBranch;
      }

      public String getAmtType() {
         return this.amtType;
      }

      public BigDecimal getPreviousBalAmt() {
         return this.previousBalAmt;
      }

      public BigDecimal getActualBal() {
         return this.actualBal;
      }

      public String getAcctDesc() {
         return this.acctDesc;
      }

      public BigDecimal getTranAmt() {
         return this.tranAmt;
      }

      public String getChannelSeqNo() {
         return this.channelSeqNo;
      }

      public String getTranType() {
         return this.tranType;
      }

      public String getDrTranType() {
         return this.drTranType;
      }

      public String getOthDocumentType() {
         return this.othDocumentType;
      }

      public String getOthDocumentId() {
         return this.othDocumentId;
      }

      public String getOthBaseAcctNo() {
         return this.othBaseAcctNo;
      }

      public String getOthProdType() {
         return this.othProdType;
      }

      public String getOthAcctCcy() {
         return this.othAcctCcy;
      }

      public String getOthAcctSeqNo() {
         return this.othAcctSeqNo;
      }

      public String getOthAcctDesc() {
         return this.othAcctDesc;
      }

      public String getOthBranch() {
         return this.othBranch;
      }

      public String getOthBranchName() {
         return this.othBranchName;
      }

      public String getOthBankName() {
         return this.othBankName;
      }

      public String getOthBankCode() {
         return this.othBankCode;
      }

      public String getOthSeqNo() {
         return this.othSeqNo;
      }

      public String getOthReference() {
         return this.othReference;
      }

      public String getToAcctNo() {
         return this.toAcctNo;
      }

      public String getToAcctName() {
         return this.toAcctName;
      }

      public String getSourceAcctNo() {
         return this.sourceAcctNo;
      }

      public String getSourceAcctName() {
         return this.sourceAcctName;
      }

      public String getDocType() {
         return this.docType;
      }

      public String getPrefix() {
         return this.prefix;
      }

      public String getVoucherNo() {
         return this.voucherNo;
      }

      public String getWithdrawalType() {
         return this.withdrawalType;
      }

      public String getReasonCode() {
         return this.reasonCode;
      }

      public String getUserId() {
         return this.userId;
      }

      public String getBankSeqNo() {
         return this.bankSeqNo;
      }

      public String getAuthUserId() {
         return this.authUserId;
      }

      public String getApprUserId() {
         return this.apprUserId;
      }

      public String getBankName() {
         return this.bankName;
      }

      public String getBankCode() {
         return this.bankCode;
      }

      public String getBranch() {
         return this.branch;
      }

      public String getReference() {
         return this.reference;
      }

      public String getTranCategory() {
         return this.tranCategory;
      }

      public String getEffectDate() {
         return this.effectDate;
      }

      public String getReversalTranType() {
         return this.reversalTranType;
      }

      public String getReversalDate() {
         return this.reversalDate;
      }

      public String getSeqNo() {
         return this.seqNo;
      }

      public String getTranDesc() {
         return this.tranDesc;
      }

      public String getTranNote() {
         return this.tranNote;
      }

      public String getPrimaryTranSeqNo() {
         return this.primaryTranSeqNo;
      }

      public String getFhSeqNo() {
         return this.fhSeqNo;
      }

      public String getSettlementDate() {
         return this.settlementDate;
      }

      public BigDecimal getContraEquivAmt() {
         return this.contraEquivAmt;
      }

      public BigDecimal getBaseEquivAmt() {
         return this.baseEquivAmt;
      }

      public BigDecimal getCrossRate() {
         return this.crossRate;
      }

      public String getFromCcy() {
         return this.fromCcy;
      }

      public BigDecimal getFromAmount() {
         return this.fromAmount;
      }

      public String getFromRateFlag() {
         return this.fromRateFlag;
      }

      public BigDecimal getFromXrate() {
         return this.fromXrate;
      }

      public String getToCcy() {
         return this.toCcy;
      }

      public BigDecimal getToAmount() {
         return this.toAmount;
      }

      public String getToRateFlag() {
         return this.toRateFlag;
      }

      public BigDecimal getToXrate() {
         return this.toXrate;
      }

      public BigDecimal getOvToAmount() {
         return this.ovToAmount;
      }

      public String getRateFlag() {
         return this.rateFlag;
      }

      public String getQuoteType() {
         return this.quoteType;
      }

      public String getTerminalId() {
         return this.terminalId;
      }

      public String getTraceId() {
         return this.traceId;
      }

      public String getNarrative() {
         return this.narrative;
      }

      public String getCashItem() {
         return this.cashItem;
      }

      public String getTranStatus() {
         return this.tranStatus;
      }

      public String getProfitCenter() {
         return this.profitCenter;
      }

      public String getBusinessUnit() {
         return this.businessUnit;
      }

      public String getSourceModule() {
         return this.sourceModule;
      }

      public String getEventType() {
         return this.eventType;
      }

      public String getCcy() {
         return this.ccy;
      }

      public String getCrDrMaintInd() {
         return this.crDrMaintInd;
      }

      public String getBalType() {
         return this.balType;
      }

      public Integer getPrintCnt() {
         return this.printCnt;
      }

      public String getBatchNo() {
         return this.batchNo;
      }

      public String getBizType() {
         return this.bizType;
      }

      public String getPbkUpdFlag() {
         return this.pbkUpdFlag;
      }

      public String getSourceType() {
         return this.sourceType;
      }

      public String getReversal() {
         return this.reversal;
      }

      public String getPayUnit() {
         return this.payUnit;
      }

      public String getServCharge() {
         return this.servCharge;
      }

      public String getCompany() {
         return this.company;
      }

      public String getAcctOpenDate() {
         return this.acctOpenDate;
      }

      public String getTerm() {
         return this.term;
      }

      public String getTermType() {
         return this.termType;
      }

      public String getOthAcctName() {
         return this.othAcctName;
      }

      public BigDecimal getRealRate() {
         return this.realRate;
      }

      public BigDecimal getActualRate() {
         return this.actualRate;
      }

      public String getOthRealBaseAcctNo() {
         return this.othRealBaseAcctNo;
      }

      public String getOthRealTranName() {
         return this.othRealTranName;
      }

      public String getBranchName() {
         return this.branchName;
      }

      public String getToId() {
         return this.toId;
      }

      public BigDecimal getPriAmt() {
         return this.priAmt;
      }

      public String getPiFlag() {
         return this.piFlag;
      }

      public String getTranDate() {
         return this.tranDate;
      }

      public String getNorthboundStatus() {
         return this.northboundStatus;
      }

      public String getNarrativeCode() {
         return this.narrativeCode;
      }

      public String getTranTimestamp() {
         return this.tranTimestamp;
      }

      public String getContraTranDate() {
         return this.contraTranDate;
      }

      public String getSubSeqNo() {
         return this.subSeqNo;
      }

      public String getOthRealBankName() {
         return this.othRealBankName;
      }

      public String getNarrativeCodeDesc() {
         return this.narrativeCodeDesc;
      }

      public String getOrigTranTimestamp() {
         return this.origTranTimestamp;
      }

      public String getRealTranBranchName() {
         return this.realTranBranchName;
      }

      public BigDecimal getOdAmount() {
         return this.odAmount;
      }

      public void setDocumentType(String documentType) {
         this.documentType = documentType;
      }

      public void setDocumentId(String documentId) {
         this.documentId = documentId;
      }

      public void setChClientName(String chClientName) {
         this.chClientName = chClientName;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setClientType(String clientType) {
         this.clientType = clientType;
      }

      public void setClientName(String clientName) {
         this.clientName = clientName;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setAcctBranch(String acctBranch) {
         this.acctBranch = acctBranch;
      }

      public void setAmtType(String amtType) {
         this.amtType = amtType;
      }

      public void setPreviousBalAmt(BigDecimal previousBalAmt) {
         this.previousBalAmt = previousBalAmt;
      }

      public void setActualBal(BigDecimal actualBal) {
         this.actualBal = actualBal;
      }

      public void setAcctDesc(String acctDesc) {
         this.acctDesc = acctDesc;
      }

      public void setTranAmt(BigDecimal tranAmt) {
         this.tranAmt = tranAmt;
      }

      public void setChannelSeqNo(String channelSeqNo) {
         this.channelSeqNo = channelSeqNo;
      }

      public void setTranType(String tranType) {
         this.tranType = tranType;
      }

      public void setDrTranType(String drTranType) {
         this.drTranType = drTranType;
      }

      public void setOthDocumentType(String othDocumentType) {
         this.othDocumentType = othDocumentType;
      }

      public void setOthDocumentId(String othDocumentId) {
         this.othDocumentId = othDocumentId;
      }

      public void setOthBaseAcctNo(String othBaseAcctNo) {
         this.othBaseAcctNo = othBaseAcctNo;
      }

      public void setOthProdType(String othProdType) {
         this.othProdType = othProdType;
      }

      public void setOthAcctCcy(String othAcctCcy) {
         this.othAcctCcy = othAcctCcy;
      }

      public void setOthAcctSeqNo(String othAcctSeqNo) {
         this.othAcctSeqNo = othAcctSeqNo;
      }

      public void setOthAcctDesc(String othAcctDesc) {
         this.othAcctDesc = othAcctDesc;
      }

      public void setOthBranch(String othBranch) {
         this.othBranch = othBranch;
      }

      public void setOthBranchName(String othBranchName) {
         this.othBranchName = othBranchName;
      }

      public void setOthBankName(String othBankName) {
         this.othBankName = othBankName;
      }

      public void setOthBankCode(String othBankCode) {
         this.othBankCode = othBankCode;
      }

      public void setOthSeqNo(String othSeqNo) {
         this.othSeqNo = othSeqNo;
      }

      public void setOthReference(String othReference) {
         this.othReference = othReference;
      }

      public void setToAcctNo(String toAcctNo) {
         this.toAcctNo = toAcctNo;
      }

      public void setToAcctName(String toAcctName) {
         this.toAcctName = toAcctName;
      }

      public void setSourceAcctNo(String sourceAcctNo) {
         this.sourceAcctNo = sourceAcctNo;
      }

      public void setSourceAcctName(String sourceAcctName) {
         this.sourceAcctName = sourceAcctName;
      }

      public void setDocType(String docType) {
         this.docType = docType;
      }

      public void setPrefix(String prefix) {
         this.prefix = prefix;
      }

      public void setVoucherNo(String voucherNo) {
         this.voucherNo = voucherNo;
      }

      public void setWithdrawalType(String withdrawalType) {
         this.withdrawalType = withdrawalType;
      }

      public void setReasonCode(String reasonCode) {
         this.reasonCode = reasonCode;
      }

      public void setUserId(String userId) {
         this.userId = userId;
      }

      public void setBankSeqNo(String bankSeqNo) {
         this.bankSeqNo = bankSeqNo;
      }

      public void setAuthUserId(String authUserId) {
         this.authUserId = authUserId;
      }

      public void setApprUserId(String apprUserId) {
         this.apprUserId = apprUserId;
      }

      public void setBankName(String bankName) {
         this.bankName = bankName;
      }

      public void setBankCode(String bankCode) {
         this.bankCode = bankCode;
      }

      public void setBranch(String branch) {
         this.branch = branch;
      }

      public void setReference(String reference) {
         this.reference = reference;
      }

      public void setTranCategory(String tranCategory) {
         this.tranCategory = tranCategory;
      }

      public void setEffectDate(String effectDate) {
         this.effectDate = effectDate;
      }

      public void setReversalTranType(String reversalTranType) {
         this.reversalTranType = reversalTranType;
      }

      public void setReversalDate(String reversalDate) {
         this.reversalDate = reversalDate;
      }

      public void setSeqNo(String seqNo) {
         this.seqNo = seqNo;
      }

      public void setTranDesc(String tranDesc) {
         this.tranDesc = tranDesc;
      }

      public void setTranNote(String tranNote) {
         this.tranNote = tranNote;
      }

      public void setPrimaryTranSeqNo(String primaryTranSeqNo) {
         this.primaryTranSeqNo = primaryTranSeqNo;
      }

      public void setFhSeqNo(String fhSeqNo) {
         this.fhSeqNo = fhSeqNo;
      }

      public void setSettlementDate(String settlementDate) {
         this.settlementDate = settlementDate;
      }

      public void setContraEquivAmt(BigDecimal contraEquivAmt) {
         this.contraEquivAmt = contraEquivAmt;
      }

      public void setBaseEquivAmt(BigDecimal baseEquivAmt) {
         this.baseEquivAmt = baseEquivAmt;
      }

      public void setCrossRate(BigDecimal crossRate) {
         this.crossRate = crossRate;
      }

      public void setFromCcy(String fromCcy) {
         this.fromCcy = fromCcy;
      }

      public void setFromAmount(BigDecimal fromAmount) {
         this.fromAmount = fromAmount;
      }

      public void setFromRateFlag(String fromRateFlag) {
         this.fromRateFlag = fromRateFlag;
      }

      public void setFromXrate(BigDecimal fromXrate) {
         this.fromXrate = fromXrate;
      }

      public void setToCcy(String toCcy) {
         this.toCcy = toCcy;
      }

      public void setToAmount(BigDecimal toAmount) {
         this.toAmount = toAmount;
      }

      public void setToRateFlag(String toRateFlag) {
         this.toRateFlag = toRateFlag;
      }

      public void setToXrate(BigDecimal toXrate) {
         this.toXrate = toXrate;
      }

      public void setOvToAmount(BigDecimal ovToAmount) {
         this.ovToAmount = ovToAmount;
      }

      public void setRateFlag(String rateFlag) {
         this.rateFlag = rateFlag;
      }

      public void setQuoteType(String quoteType) {
         this.quoteType = quoteType;
      }

      public void setTerminalId(String terminalId) {
         this.terminalId = terminalId;
      }

      public void setTraceId(String traceId) {
         this.traceId = traceId;
      }

      public void setNarrative(String narrative) {
         this.narrative = narrative;
      }

      public void setCashItem(String cashItem) {
         this.cashItem = cashItem;
      }

      public void setTranStatus(String tranStatus) {
         this.tranStatus = tranStatus;
      }

      public void setProfitCenter(String profitCenter) {
         this.profitCenter = profitCenter;
      }

      public void setBusinessUnit(String businessUnit) {
         this.businessUnit = businessUnit;
      }

      public void setSourceModule(String sourceModule) {
         this.sourceModule = sourceModule;
      }

      public void setEventType(String eventType) {
         this.eventType = eventType;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setCrDrMaintInd(String crDrMaintInd) {
         this.crDrMaintInd = crDrMaintInd;
      }

      public void setBalType(String balType) {
         this.balType = balType;
      }

      public void setPrintCnt(Integer printCnt) {
         this.printCnt = printCnt;
      }

      public void setBatchNo(String batchNo) {
         this.batchNo = batchNo;
      }

      public void setBizType(String bizType) {
         this.bizType = bizType;
      }

      public void setPbkUpdFlag(String pbkUpdFlag) {
         this.pbkUpdFlag = pbkUpdFlag;
      }

      public void setSourceType(String sourceType) {
         this.sourceType = sourceType;
      }

      public void setReversal(String reversal) {
         this.reversal = reversal;
      }

      public void setPayUnit(String payUnit) {
         this.payUnit = payUnit;
      }

      public void setServCharge(String servCharge) {
         this.servCharge = servCharge;
      }

      public void setCompany(String company) {
         this.company = company;
      }

      public void setAcctOpenDate(String acctOpenDate) {
         this.acctOpenDate = acctOpenDate;
      }

      public void setTerm(String term) {
         this.term = term;
      }

      public void setTermType(String termType) {
         this.termType = termType;
      }

      public void setOthAcctName(String othAcctName) {
         this.othAcctName = othAcctName;
      }

      public void setRealRate(BigDecimal realRate) {
         this.realRate = realRate;
      }

      public void setActualRate(BigDecimal actualRate) {
         this.actualRate = actualRate;
      }

      public void setOthRealBaseAcctNo(String othRealBaseAcctNo) {
         this.othRealBaseAcctNo = othRealBaseAcctNo;
      }

      public void setOthRealTranName(String othRealTranName) {
         this.othRealTranName = othRealTranName;
      }

      public void setBranchName(String branchName) {
         this.branchName = branchName;
      }

      public void setToId(String toId) {
         this.toId = toId;
      }

      public void setPriAmt(BigDecimal priAmt) {
         this.priAmt = priAmt;
      }

      public void setPiFlag(String piFlag) {
         this.piFlag = piFlag;
      }

      public void setTranDate(String tranDate) {
         this.tranDate = tranDate;
      }

      public void setNorthboundStatus(String northboundStatus) {
         this.northboundStatus = northboundStatus;
      }

      public void setNarrativeCode(String narrativeCode) {
         this.narrativeCode = narrativeCode;
      }

      public void setTranTimestamp(String tranTimestamp) {
         this.tranTimestamp = tranTimestamp;
      }

      public void setContraTranDate(String contraTranDate) {
         this.contraTranDate = contraTranDate;
      }

      public void setSubSeqNo(String subSeqNo) {
         this.subSeqNo = subSeqNo;
      }

      public void setOthRealBankName(String othRealBankName) {
         this.othRealBankName = othRealBankName;
      }

      public void setNarrativeCodeDesc(String narrativeCodeDesc) {
         this.narrativeCodeDesc = narrativeCodeDesc;
      }

      public void setOrigTranTimestamp(String origTranTimestamp) {
         this.origTranTimestamp = origTranTimestamp;
      }

      public void setRealTranBranchName(String realTranBranchName) {
         this.realTranBranchName = realTranBranchName;
      }

      public void setOdAmount(BigDecimal odAmount) {
         this.odAmount = odAmount;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100010Out.TranHistArray)) {
            return false;
         } else {
            Core1400100010Out.TranHistArray other = (Core1400100010Out.TranHistArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label1415: {
                  Object this$documentType = this.getDocumentType();
                  Object other$documentType = other.getDocumentType();
                  if (this$documentType == null) {
                     if (other$documentType == null) {
                        break label1415;
                     }
                  } else if (this$documentType.equals(other$documentType)) {
                     break label1415;
                  }

                  return false;
               }

               Object this$documentId = this.getDocumentId();
               Object other$documentId = other.getDocumentId();
               if (this$documentId == null) {
                  if (other$documentId != null) {
                     return false;
                  }
               } else if (!this$documentId.equals(other$documentId)) {
                  return false;
               }

               label1401: {
                  Object this$chClientName = this.getChClientName();
                  Object other$chClientName = other.getChClientName();
                  if (this$chClientName == null) {
                     if (other$chClientName == null) {
                        break label1401;
                     }
                  } else if (this$chClientName.equals(other$chClientName)) {
                     break label1401;
                  }

                  return false;
               }

               Object this$clientNo = this.getClientNo();
               Object other$clientNo = other.getClientNo();
               if (this$clientNo == null) {
                  if (other$clientNo != null) {
                     return false;
                  }
               } else if (!this$clientNo.equals(other$clientNo)) {
                  return false;
               }

               label1387: {
                  Object this$clientType = this.getClientType();
                  Object other$clientType = other.getClientType();
                  if (this$clientType == null) {
                     if (other$clientType == null) {
                        break label1387;
                     }
                  } else if (this$clientType.equals(other$clientType)) {
                     break label1387;
                  }

                  return false;
               }

               Object this$clientName = this.getClientName();
               Object other$clientName = other.getClientName();
               if (this$clientName == null) {
                  if (other$clientName != null) {
                     return false;
                  }
               } else if (!this$clientName.equals(other$clientName)) {
                  return false;
               }

               label1373: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label1373;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label1373;
                  }

                  return false;
               }

               label1366: {
                  Object this$prodType = this.getProdType();
                  Object other$prodType = other.getProdType();
                  if (this$prodType == null) {
                     if (other$prodType == null) {
                        break label1366;
                     }
                  } else if (this$prodType.equals(other$prodType)) {
                     break label1366;
                  }

                  return false;
               }

               Object this$acctCcy = this.getAcctCcy();
               Object other$acctCcy = other.getAcctCcy();
               if (this$acctCcy == null) {
                  if (other$acctCcy != null) {
                     return false;
                  }
               } else if (!this$acctCcy.equals(other$acctCcy)) {
                  return false;
               }

               label1352: {
                  Object this$acctSeqNo = this.getAcctSeqNo();
                  Object other$acctSeqNo = other.getAcctSeqNo();
                  if (this$acctSeqNo == null) {
                     if (other$acctSeqNo == null) {
                        break label1352;
                     }
                  } else if (this$acctSeqNo.equals(other$acctSeqNo)) {
                     break label1352;
                  }

                  return false;
               }

               label1345: {
                  Object this$acctBranch = this.getAcctBranch();
                  Object other$acctBranch = other.getAcctBranch();
                  if (this$acctBranch == null) {
                     if (other$acctBranch == null) {
                        break label1345;
                     }
                  } else if (this$acctBranch.equals(other$acctBranch)) {
                     break label1345;
                  }

                  return false;
               }

               Object this$amtType = this.getAmtType();
               Object other$amtType = other.getAmtType();
               if (this$amtType == null) {
                  if (other$amtType != null) {
                     return false;
                  }
               } else if (!this$amtType.equals(other$amtType)) {
                  return false;
               }

               Object this$previousBalAmt = this.getPreviousBalAmt();
               Object other$previousBalAmt = other.getPreviousBalAmt();
               if (this$previousBalAmt == null) {
                  if (other$previousBalAmt != null) {
                     return false;
                  }
               } else if (!this$previousBalAmt.equals(other$previousBalAmt)) {
                  return false;
               }

               label1324: {
                  Object this$actualBal = this.getActualBal();
                  Object other$actualBal = other.getActualBal();
                  if (this$actualBal == null) {
                     if (other$actualBal == null) {
                        break label1324;
                     }
                  } else if (this$actualBal.equals(other$actualBal)) {
                     break label1324;
                  }

                  return false;
               }

               Object this$acctDesc = this.getAcctDesc();
               Object other$acctDesc = other.getAcctDesc();
               if (this$acctDesc == null) {
                  if (other$acctDesc != null) {
                     return false;
                  }
               } else if (!this$acctDesc.equals(other$acctDesc)) {
                  return false;
               }

               Object this$tranAmt = this.getTranAmt();
               Object other$tranAmt = other.getTranAmt();
               if (this$tranAmt == null) {
                  if (other$tranAmt != null) {
                     return false;
                  }
               } else if (!this$tranAmt.equals(other$tranAmt)) {
                  return false;
               }

               label1303: {
                  Object this$channelSeqNo = this.getChannelSeqNo();
                  Object other$channelSeqNo = other.getChannelSeqNo();
                  if (this$channelSeqNo == null) {
                     if (other$channelSeqNo == null) {
                        break label1303;
                     }
                  } else if (this$channelSeqNo.equals(other$channelSeqNo)) {
                     break label1303;
                  }

                  return false;
               }

               Object this$tranType = this.getTranType();
               Object other$tranType = other.getTranType();
               if (this$tranType == null) {
                  if (other$tranType != null) {
                     return false;
                  }
               } else if (!this$tranType.equals(other$tranType)) {
                  return false;
               }

               label1289: {
                  Object this$drTranType = this.getDrTranType();
                  Object other$drTranType = other.getDrTranType();
                  if (this$drTranType == null) {
                     if (other$drTranType == null) {
                        break label1289;
                     }
                  } else if (this$drTranType.equals(other$drTranType)) {
                     break label1289;
                  }

                  return false;
               }

               Object this$othDocumentType = this.getOthDocumentType();
               Object other$othDocumentType = other.getOthDocumentType();
               if (this$othDocumentType == null) {
                  if (other$othDocumentType != null) {
                     return false;
                  }
               } else if (!this$othDocumentType.equals(other$othDocumentType)) {
                  return false;
               }

               label1275: {
                  Object this$othDocumentId = this.getOthDocumentId();
                  Object other$othDocumentId = other.getOthDocumentId();
                  if (this$othDocumentId == null) {
                     if (other$othDocumentId == null) {
                        break label1275;
                     }
                  } else if (this$othDocumentId.equals(other$othDocumentId)) {
                     break label1275;
                  }

                  return false;
               }

               Object this$othBaseAcctNo = this.getOthBaseAcctNo();
               Object other$othBaseAcctNo = other.getOthBaseAcctNo();
               if (this$othBaseAcctNo == null) {
                  if (other$othBaseAcctNo != null) {
                     return false;
                  }
               } else if (!this$othBaseAcctNo.equals(other$othBaseAcctNo)) {
                  return false;
               }

               label1261: {
                  Object this$othProdType = this.getOthProdType();
                  Object other$othProdType = other.getOthProdType();
                  if (this$othProdType == null) {
                     if (other$othProdType == null) {
                        break label1261;
                     }
                  } else if (this$othProdType.equals(other$othProdType)) {
                     break label1261;
                  }

                  return false;
               }

               label1254: {
                  Object this$othAcctCcy = this.getOthAcctCcy();
                  Object other$othAcctCcy = other.getOthAcctCcy();
                  if (this$othAcctCcy == null) {
                     if (other$othAcctCcy == null) {
                        break label1254;
                     }
                  } else if (this$othAcctCcy.equals(other$othAcctCcy)) {
                     break label1254;
                  }

                  return false;
               }

               Object this$othAcctSeqNo = this.getOthAcctSeqNo();
               Object other$othAcctSeqNo = other.getOthAcctSeqNo();
               if (this$othAcctSeqNo == null) {
                  if (other$othAcctSeqNo != null) {
                     return false;
                  }
               } else if (!this$othAcctSeqNo.equals(other$othAcctSeqNo)) {
                  return false;
               }

               label1240: {
                  Object this$othAcctDesc = this.getOthAcctDesc();
                  Object other$othAcctDesc = other.getOthAcctDesc();
                  if (this$othAcctDesc == null) {
                     if (other$othAcctDesc == null) {
                        break label1240;
                     }
                  } else if (this$othAcctDesc.equals(other$othAcctDesc)) {
                     break label1240;
                  }

                  return false;
               }

               label1233: {
                  Object this$othBranch = this.getOthBranch();
                  Object other$othBranch = other.getOthBranch();
                  if (this$othBranch == null) {
                     if (other$othBranch == null) {
                        break label1233;
                     }
                  } else if (this$othBranch.equals(other$othBranch)) {
                     break label1233;
                  }

                  return false;
               }

               Object this$othBranchName = this.getOthBranchName();
               Object other$othBranchName = other.getOthBranchName();
               if (this$othBranchName == null) {
                  if (other$othBranchName != null) {
                     return false;
                  }
               } else if (!this$othBranchName.equals(other$othBranchName)) {
                  return false;
               }

               Object this$othBankName = this.getOthBankName();
               Object other$othBankName = other.getOthBankName();
               if (this$othBankName == null) {
                  if (other$othBankName != null) {
                     return false;
                  }
               } else if (!this$othBankName.equals(other$othBankName)) {
                  return false;
               }

               label1212: {
                  Object this$othBankCode = this.getOthBankCode();
                  Object other$othBankCode = other.getOthBankCode();
                  if (this$othBankCode == null) {
                     if (other$othBankCode == null) {
                        break label1212;
                     }
                  } else if (this$othBankCode.equals(other$othBankCode)) {
                     break label1212;
                  }

                  return false;
               }

               Object this$othSeqNo = this.getOthSeqNo();
               Object other$othSeqNo = other.getOthSeqNo();
               if (this$othSeqNo == null) {
                  if (other$othSeqNo != null) {
                     return false;
                  }
               } else if (!this$othSeqNo.equals(other$othSeqNo)) {
                  return false;
               }

               Object this$othReference = this.getOthReference();
               Object other$othReference = other.getOthReference();
               if (this$othReference == null) {
                  if (other$othReference != null) {
                     return false;
                  }
               } else if (!this$othReference.equals(other$othReference)) {
                  return false;
               }

               label1191: {
                  Object this$toAcctNo = this.getToAcctNo();
                  Object other$toAcctNo = other.getToAcctNo();
                  if (this$toAcctNo == null) {
                     if (other$toAcctNo == null) {
                        break label1191;
                     }
                  } else if (this$toAcctNo.equals(other$toAcctNo)) {
                     break label1191;
                  }

                  return false;
               }

               Object this$toAcctName = this.getToAcctName();
               Object other$toAcctName = other.getToAcctName();
               if (this$toAcctName == null) {
                  if (other$toAcctName != null) {
                     return false;
                  }
               } else if (!this$toAcctName.equals(other$toAcctName)) {
                  return false;
               }

               label1177: {
                  Object this$sourceAcctNo = this.getSourceAcctNo();
                  Object other$sourceAcctNo = other.getSourceAcctNo();
                  if (this$sourceAcctNo == null) {
                     if (other$sourceAcctNo == null) {
                        break label1177;
                     }
                  } else if (this$sourceAcctNo.equals(other$sourceAcctNo)) {
                     break label1177;
                  }

                  return false;
               }

               Object this$sourceAcctName = this.getSourceAcctName();
               Object other$sourceAcctName = other.getSourceAcctName();
               if (this$sourceAcctName == null) {
                  if (other$sourceAcctName != null) {
                     return false;
                  }
               } else if (!this$sourceAcctName.equals(other$sourceAcctName)) {
                  return false;
               }

               label1163: {
                  Object this$docType = this.getDocType();
                  Object other$docType = other.getDocType();
                  if (this$docType == null) {
                     if (other$docType == null) {
                        break label1163;
                     }
                  } else if (this$docType.equals(other$docType)) {
                     break label1163;
                  }

                  return false;
               }

               Object this$prefix = this.getPrefix();
               Object other$prefix = other.getPrefix();
               if (this$prefix == null) {
                  if (other$prefix != null) {
                     return false;
                  }
               } else if (!this$prefix.equals(other$prefix)) {
                  return false;
               }

               label1149: {
                  Object this$voucherNo = this.getVoucherNo();
                  Object other$voucherNo = other.getVoucherNo();
                  if (this$voucherNo == null) {
                     if (other$voucherNo == null) {
                        break label1149;
                     }
                  } else if (this$voucherNo.equals(other$voucherNo)) {
                     break label1149;
                  }

                  return false;
               }

               label1142: {
                  Object this$withdrawalType = this.getWithdrawalType();
                  Object other$withdrawalType = other.getWithdrawalType();
                  if (this$withdrawalType == null) {
                     if (other$withdrawalType == null) {
                        break label1142;
                     }
                  } else if (this$withdrawalType.equals(other$withdrawalType)) {
                     break label1142;
                  }

                  return false;
               }

               Object this$reasonCode = this.getReasonCode();
               Object other$reasonCode = other.getReasonCode();
               if (this$reasonCode == null) {
                  if (other$reasonCode != null) {
                     return false;
                  }
               } else if (!this$reasonCode.equals(other$reasonCode)) {
                  return false;
               }

               label1128: {
                  Object this$userId = this.getUserId();
                  Object other$userId = other.getUserId();
                  if (this$userId == null) {
                     if (other$userId == null) {
                        break label1128;
                     }
                  } else if (this$userId.equals(other$userId)) {
                     break label1128;
                  }

                  return false;
               }

               label1121: {
                  Object this$bankSeqNo = this.getBankSeqNo();
                  Object other$bankSeqNo = other.getBankSeqNo();
                  if (this$bankSeqNo == null) {
                     if (other$bankSeqNo == null) {
                        break label1121;
                     }
                  } else if (this$bankSeqNo.equals(other$bankSeqNo)) {
                     break label1121;
                  }

                  return false;
               }

               Object this$authUserId = this.getAuthUserId();
               Object other$authUserId = other.getAuthUserId();
               if (this$authUserId == null) {
                  if (other$authUserId != null) {
                     return false;
                  }
               } else if (!this$authUserId.equals(other$authUserId)) {
                  return false;
               }

               Object this$apprUserId = this.getApprUserId();
               Object other$apprUserId = other.getApprUserId();
               if (this$apprUserId == null) {
                  if (other$apprUserId != null) {
                     return false;
                  }
               } else if (!this$apprUserId.equals(other$apprUserId)) {
                  return false;
               }

               label1100: {
                  Object this$bankName = this.getBankName();
                  Object other$bankName = other.getBankName();
                  if (this$bankName == null) {
                     if (other$bankName == null) {
                        break label1100;
                     }
                  } else if (this$bankName.equals(other$bankName)) {
                     break label1100;
                  }

                  return false;
               }

               Object this$bankCode = this.getBankCode();
               Object other$bankCode = other.getBankCode();
               if (this$bankCode == null) {
                  if (other$bankCode != null) {
                     return false;
                  }
               } else if (!this$bankCode.equals(other$bankCode)) {
                  return false;
               }

               Object this$branch = this.getBranch();
               Object other$branch = other.getBranch();
               if (this$branch == null) {
                  if (other$branch != null) {
                     return false;
                  }
               } else if (!this$branch.equals(other$branch)) {
                  return false;
               }

               label1079: {
                  Object this$reference = this.getReference();
                  Object other$reference = other.getReference();
                  if (this$reference == null) {
                     if (other$reference == null) {
                        break label1079;
                     }
                  } else if (this$reference.equals(other$reference)) {
                     break label1079;
                  }

                  return false;
               }

               Object this$tranCategory = this.getTranCategory();
               Object other$tranCategory = other.getTranCategory();
               if (this$tranCategory == null) {
                  if (other$tranCategory != null) {
                     return false;
                  }
               } else if (!this$tranCategory.equals(other$tranCategory)) {
                  return false;
               }

               label1065: {
                  Object this$effectDate = this.getEffectDate();
                  Object other$effectDate = other.getEffectDate();
                  if (this$effectDate == null) {
                     if (other$effectDate == null) {
                        break label1065;
                     }
                  } else if (this$effectDate.equals(other$effectDate)) {
                     break label1065;
                  }

                  return false;
               }

               Object this$reversalTranType = this.getReversalTranType();
               Object other$reversalTranType = other.getReversalTranType();
               if (this$reversalTranType == null) {
                  if (other$reversalTranType != null) {
                     return false;
                  }
               } else if (!this$reversalTranType.equals(other$reversalTranType)) {
                  return false;
               }

               label1051: {
                  Object this$reversalDate = this.getReversalDate();
                  Object other$reversalDate = other.getReversalDate();
                  if (this$reversalDate == null) {
                     if (other$reversalDate == null) {
                        break label1051;
                     }
                  } else if (this$reversalDate.equals(other$reversalDate)) {
                     break label1051;
                  }

                  return false;
               }

               Object this$seqNo = this.getSeqNo();
               Object other$seqNo = other.getSeqNo();
               if (this$seqNo == null) {
                  if (other$seqNo != null) {
                     return false;
                  }
               } else if (!this$seqNo.equals(other$seqNo)) {
                  return false;
               }

               label1037: {
                  Object this$tranDesc = this.getTranDesc();
                  Object other$tranDesc = other.getTranDesc();
                  if (this$tranDesc == null) {
                     if (other$tranDesc == null) {
                        break label1037;
                     }
                  } else if (this$tranDesc.equals(other$tranDesc)) {
                     break label1037;
                  }

                  return false;
               }

               label1030: {
                  Object this$tranNote = this.getTranNote();
                  Object other$tranNote = other.getTranNote();
                  if (this$tranNote == null) {
                     if (other$tranNote == null) {
                        break label1030;
                     }
                  } else if (this$tranNote.equals(other$tranNote)) {
                     break label1030;
                  }

                  return false;
               }

               Object this$primaryTranSeqNo = this.getPrimaryTranSeqNo();
               Object other$primaryTranSeqNo = other.getPrimaryTranSeqNo();
               if (this$primaryTranSeqNo == null) {
                  if (other$primaryTranSeqNo != null) {
                     return false;
                  }
               } else if (!this$primaryTranSeqNo.equals(other$primaryTranSeqNo)) {
                  return false;
               }

               label1016: {
                  Object this$fhSeqNo = this.getFhSeqNo();
                  Object other$fhSeqNo = other.getFhSeqNo();
                  if (this$fhSeqNo == null) {
                     if (other$fhSeqNo == null) {
                        break label1016;
                     }
                  } else if (this$fhSeqNo.equals(other$fhSeqNo)) {
                     break label1016;
                  }

                  return false;
               }

               label1009: {
                  Object this$settlementDate = this.getSettlementDate();
                  Object other$settlementDate = other.getSettlementDate();
                  if (this$settlementDate == null) {
                     if (other$settlementDate == null) {
                        break label1009;
                     }
                  } else if (this$settlementDate.equals(other$settlementDate)) {
                     break label1009;
                  }

                  return false;
               }

               Object this$contraEquivAmt = this.getContraEquivAmt();
               Object other$contraEquivAmt = other.getContraEquivAmt();
               if (this$contraEquivAmt == null) {
                  if (other$contraEquivAmt != null) {
                     return false;
                  }
               } else if (!this$contraEquivAmt.equals(other$contraEquivAmt)) {
                  return false;
               }

               Object this$baseEquivAmt = this.getBaseEquivAmt();
               Object other$baseEquivAmt = other.getBaseEquivAmt();
               if (this$baseEquivAmt == null) {
                  if (other$baseEquivAmt != null) {
                     return false;
                  }
               } else if (!this$baseEquivAmt.equals(other$baseEquivAmt)) {
                  return false;
               }

               label988: {
                  Object this$crossRate = this.getCrossRate();
                  Object other$crossRate = other.getCrossRate();
                  if (this$crossRate == null) {
                     if (other$crossRate == null) {
                        break label988;
                     }
                  } else if (this$crossRate.equals(other$crossRate)) {
                     break label988;
                  }

                  return false;
               }

               Object this$fromCcy = this.getFromCcy();
               Object other$fromCcy = other.getFromCcy();
               if (this$fromCcy == null) {
                  if (other$fromCcy != null) {
                     return false;
                  }
               } else if (!this$fromCcy.equals(other$fromCcy)) {
                  return false;
               }

               Object this$fromAmount = this.getFromAmount();
               Object other$fromAmount = other.getFromAmount();
               if (this$fromAmount == null) {
                  if (other$fromAmount != null) {
                     return false;
                  }
               } else if (!this$fromAmount.equals(other$fromAmount)) {
                  return false;
               }

               label967: {
                  Object this$fromRateFlag = this.getFromRateFlag();
                  Object other$fromRateFlag = other.getFromRateFlag();
                  if (this$fromRateFlag == null) {
                     if (other$fromRateFlag == null) {
                        break label967;
                     }
                  } else if (this$fromRateFlag.equals(other$fromRateFlag)) {
                     break label967;
                  }

                  return false;
               }

               Object this$fromXrate = this.getFromXrate();
               Object other$fromXrate = other.getFromXrate();
               if (this$fromXrate == null) {
                  if (other$fromXrate != null) {
                     return false;
                  }
               } else if (!this$fromXrate.equals(other$fromXrate)) {
                  return false;
               }

               label953: {
                  Object this$toCcy = this.getToCcy();
                  Object other$toCcy = other.getToCcy();
                  if (this$toCcy == null) {
                     if (other$toCcy == null) {
                        break label953;
                     }
                  } else if (this$toCcy.equals(other$toCcy)) {
                     break label953;
                  }

                  return false;
               }

               Object this$toAmount = this.getToAmount();
               Object other$toAmount = other.getToAmount();
               if (this$toAmount == null) {
                  if (other$toAmount != null) {
                     return false;
                  }
               } else if (!this$toAmount.equals(other$toAmount)) {
                  return false;
               }

               label939: {
                  Object this$toRateFlag = this.getToRateFlag();
                  Object other$toRateFlag = other.getToRateFlag();
                  if (this$toRateFlag == null) {
                     if (other$toRateFlag == null) {
                        break label939;
                     }
                  } else if (this$toRateFlag.equals(other$toRateFlag)) {
                     break label939;
                  }

                  return false;
               }

               Object this$toXrate = this.getToXrate();
               Object other$toXrate = other.getToXrate();
               if (this$toXrate == null) {
                  if (other$toXrate != null) {
                     return false;
                  }
               } else if (!this$toXrate.equals(other$toXrate)) {
                  return false;
               }

               label925: {
                  Object this$ovToAmount = this.getOvToAmount();
                  Object other$ovToAmount = other.getOvToAmount();
                  if (this$ovToAmount == null) {
                     if (other$ovToAmount == null) {
                        break label925;
                     }
                  } else if (this$ovToAmount.equals(other$ovToAmount)) {
                     break label925;
                  }

                  return false;
               }

               label918: {
                  Object this$rateFlag = this.getRateFlag();
                  Object other$rateFlag = other.getRateFlag();
                  if (this$rateFlag == null) {
                     if (other$rateFlag == null) {
                        break label918;
                     }
                  } else if (this$rateFlag.equals(other$rateFlag)) {
                     break label918;
                  }

                  return false;
               }

               Object this$quoteType = this.getQuoteType();
               Object other$quoteType = other.getQuoteType();
               if (this$quoteType == null) {
                  if (other$quoteType != null) {
                     return false;
                  }
               } else if (!this$quoteType.equals(other$quoteType)) {
                  return false;
               }

               label904: {
                  Object this$terminalId = this.getTerminalId();
                  Object other$terminalId = other.getTerminalId();
                  if (this$terminalId == null) {
                     if (other$terminalId == null) {
                        break label904;
                     }
                  } else if (this$terminalId.equals(other$terminalId)) {
                     break label904;
                  }

                  return false;
               }

               label897: {
                  Object this$traceId = this.getTraceId();
                  Object other$traceId = other.getTraceId();
                  if (this$traceId == null) {
                     if (other$traceId == null) {
                        break label897;
                     }
                  } else if (this$traceId.equals(other$traceId)) {
                     break label897;
                  }

                  return false;
               }

               Object this$narrative = this.getNarrative();
               Object other$narrative = other.getNarrative();
               if (this$narrative == null) {
                  if (other$narrative != null) {
                     return false;
                  }
               } else if (!this$narrative.equals(other$narrative)) {
                  return false;
               }

               Object this$cashItem = this.getCashItem();
               Object other$cashItem = other.getCashItem();
               if (this$cashItem == null) {
                  if (other$cashItem != null) {
                     return false;
                  }
               } else if (!this$cashItem.equals(other$cashItem)) {
                  return false;
               }

               label876: {
                  Object this$tranStatus = this.getTranStatus();
                  Object other$tranStatus = other.getTranStatus();
                  if (this$tranStatus == null) {
                     if (other$tranStatus == null) {
                        break label876;
                     }
                  } else if (this$tranStatus.equals(other$tranStatus)) {
                     break label876;
                  }

                  return false;
               }

               Object this$profitCenter = this.getProfitCenter();
               Object other$profitCenter = other.getProfitCenter();
               if (this$profitCenter == null) {
                  if (other$profitCenter != null) {
                     return false;
                  }
               } else if (!this$profitCenter.equals(other$profitCenter)) {
                  return false;
               }

               Object this$businessUnit = this.getBusinessUnit();
               Object other$businessUnit = other.getBusinessUnit();
               if (this$businessUnit == null) {
                  if (other$businessUnit != null) {
                     return false;
                  }
               } else if (!this$businessUnit.equals(other$businessUnit)) {
                  return false;
               }

               label855: {
                  Object this$sourceModule = this.getSourceModule();
                  Object other$sourceModule = other.getSourceModule();
                  if (this$sourceModule == null) {
                     if (other$sourceModule == null) {
                        break label855;
                     }
                  } else if (this$sourceModule.equals(other$sourceModule)) {
                     break label855;
                  }

                  return false;
               }

               Object this$eventType = this.getEventType();
               Object other$eventType = other.getEventType();
               if (this$eventType == null) {
                  if (other$eventType != null) {
                     return false;
                  }
               } else if (!this$eventType.equals(other$eventType)) {
                  return false;
               }

               label841: {
                  Object this$ccy = this.getCcy();
                  Object other$ccy = other.getCcy();
                  if (this$ccy == null) {
                     if (other$ccy == null) {
                        break label841;
                     }
                  } else if (this$ccy.equals(other$ccy)) {
                     break label841;
                  }

                  return false;
               }

               Object this$crDrMaintInd = this.getCrDrMaintInd();
               Object other$crDrMaintInd = other.getCrDrMaintInd();
               if (this$crDrMaintInd == null) {
                  if (other$crDrMaintInd != null) {
                     return false;
                  }
               } else if (!this$crDrMaintInd.equals(other$crDrMaintInd)) {
                  return false;
               }

               label827: {
                  Object this$balType = this.getBalType();
                  Object other$balType = other.getBalType();
                  if (this$balType == null) {
                     if (other$balType == null) {
                        break label827;
                     }
                  } else if (this$balType.equals(other$balType)) {
                     break label827;
                  }

                  return false;
               }

               Object this$printCnt = this.getPrintCnt();
               Object other$printCnt = other.getPrintCnt();
               if (this$printCnt == null) {
                  if (other$printCnt != null) {
                     return false;
                  }
               } else if (!this$printCnt.equals(other$printCnt)) {
                  return false;
               }

               label813: {
                  Object this$batchNo = this.getBatchNo();
                  Object other$batchNo = other.getBatchNo();
                  if (this$batchNo == null) {
                     if (other$batchNo == null) {
                        break label813;
                     }
                  } else if (this$batchNo.equals(other$batchNo)) {
                     break label813;
                  }

                  return false;
               }

               label806: {
                  Object this$bizType = this.getBizType();
                  Object other$bizType = other.getBizType();
                  if (this$bizType == null) {
                     if (other$bizType == null) {
                        break label806;
                     }
                  } else if (this$bizType.equals(other$bizType)) {
                     break label806;
                  }

                  return false;
               }

               Object this$pbkUpdFlag = this.getPbkUpdFlag();
               Object other$pbkUpdFlag = other.getPbkUpdFlag();
               if (this$pbkUpdFlag == null) {
                  if (other$pbkUpdFlag != null) {
                     return false;
                  }
               } else if (!this$pbkUpdFlag.equals(other$pbkUpdFlag)) {
                  return false;
               }

               label792: {
                  Object this$sourceType = this.getSourceType();
                  Object other$sourceType = other.getSourceType();
                  if (this$sourceType == null) {
                     if (other$sourceType == null) {
                        break label792;
                     }
                  } else if (this$sourceType.equals(other$sourceType)) {
                     break label792;
                  }

                  return false;
               }

               label785: {
                  Object this$reversal = this.getReversal();
                  Object other$reversal = other.getReversal();
                  if (this$reversal == null) {
                     if (other$reversal == null) {
                        break label785;
                     }
                  } else if (this$reversal.equals(other$reversal)) {
                     break label785;
                  }

                  return false;
               }

               Object this$payUnit = this.getPayUnit();
               Object other$payUnit = other.getPayUnit();
               if (this$payUnit == null) {
                  if (other$payUnit != null) {
                     return false;
                  }
               } else if (!this$payUnit.equals(other$payUnit)) {
                  return false;
               }

               Object this$servCharge = this.getServCharge();
               Object other$servCharge = other.getServCharge();
               if (this$servCharge == null) {
                  if (other$servCharge != null) {
                     return false;
                  }
               } else if (!this$servCharge.equals(other$servCharge)) {
                  return false;
               }

               label764: {
                  Object this$company = this.getCompany();
                  Object other$company = other.getCompany();
                  if (this$company == null) {
                     if (other$company == null) {
                        break label764;
                     }
                  } else if (this$company.equals(other$company)) {
                     break label764;
                  }

                  return false;
               }

               Object this$acctOpenDate = this.getAcctOpenDate();
               Object other$acctOpenDate = other.getAcctOpenDate();
               if (this$acctOpenDate == null) {
                  if (other$acctOpenDate != null) {
                     return false;
                  }
               } else if (!this$acctOpenDate.equals(other$acctOpenDate)) {
                  return false;
               }

               Object this$term = this.getTerm();
               Object other$term = other.getTerm();
               if (this$term == null) {
                  if (other$term != null) {
                     return false;
                  }
               } else if (!this$term.equals(other$term)) {
                  return false;
               }

               label743: {
                  Object this$termType = this.getTermType();
                  Object other$termType = other.getTermType();
                  if (this$termType == null) {
                     if (other$termType == null) {
                        break label743;
                     }
                  } else if (this$termType.equals(other$termType)) {
                     break label743;
                  }

                  return false;
               }

               Object this$othAcctName = this.getOthAcctName();
               Object other$othAcctName = other.getOthAcctName();
               if (this$othAcctName == null) {
                  if (other$othAcctName != null) {
                     return false;
                  }
               } else if (!this$othAcctName.equals(other$othAcctName)) {
                  return false;
               }

               label729: {
                  Object this$realRate = this.getRealRate();
                  Object other$realRate = other.getRealRate();
                  if (this$realRate == null) {
                     if (other$realRate == null) {
                        break label729;
                     }
                  } else if (this$realRate.equals(other$realRate)) {
                     break label729;
                  }

                  return false;
               }

               Object this$actualRate = this.getActualRate();
               Object other$actualRate = other.getActualRate();
               if (this$actualRate == null) {
                  if (other$actualRate != null) {
                     return false;
                  }
               } else if (!this$actualRate.equals(other$actualRate)) {
                  return false;
               }

               label715: {
                  Object this$othRealBaseAcctNo = this.getOthRealBaseAcctNo();
                  Object other$othRealBaseAcctNo = other.getOthRealBaseAcctNo();
                  if (this$othRealBaseAcctNo == null) {
                     if (other$othRealBaseAcctNo == null) {
                        break label715;
                     }
                  } else if (this$othRealBaseAcctNo.equals(other$othRealBaseAcctNo)) {
                     break label715;
                  }

                  return false;
               }

               Object this$othRealTranName = this.getOthRealTranName();
               Object other$othRealTranName = other.getOthRealTranName();
               if (this$othRealTranName == null) {
                  if (other$othRealTranName != null) {
                     return false;
                  }
               } else if (!this$othRealTranName.equals(other$othRealTranName)) {
                  return false;
               }

               label701: {
                  Object this$branchName = this.getBranchName();
                  Object other$branchName = other.getBranchName();
                  if (this$branchName == null) {
                     if (other$branchName == null) {
                        break label701;
                     }
                  } else if (this$branchName.equals(other$branchName)) {
                     break label701;
                  }

                  return false;
               }

               label694: {
                  Object this$toId = this.getToId();
                  Object other$toId = other.getToId();
                  if (this$toId == null) {
                     if (other$toId == null) {
                        break label694;
                     }
                  } else if (this$toId.equals(other$toId)) {
                     break label694;
                  }

                  return false;
               }

               Object this$priAmt = this.getPriAmt();
               Object other$priAmt = other.getPriAmt();
               if (this$priAmt == null) {
                  if (other$priAmt != null) {
                     return false;
                  }
               } else if (!this$priAmt.equals(other$priAmt)) {
                  return false;
               }

               label680: {
                  Object this$piFlag = this.getPiFlag();
                  Object other$piFlag = other.getPiFlag();
                  if (this$piFlag == null) {
                     if (other$piFlag == null) {
                        break label680;
                     }
                  } else if (this$piFlag.equals(other$piFlag)) {
                     break label680;
                  }

                  return false;
               }

               label673: {
                  Object this$tranDate = this.getTranDate();
                  Object other$tranDate = other.getTranDate();
                  if (this$tranDate == null) {
                     if (other$tranDate == null) {
                        break label673;
                     }
                  } else if (this$tranDate.equals(other$tranDate)) {
                     break label673;
                  }

                  return false;
               }

               Object this$northboundStatus = this.getNorthboundStatus();
               Object other$northboundStatus = other.getNorthboundStatus();
               if (this$northboundStatus == null) {
                  if (other$northboundStatus != null) {
                     return false;
                  }
               } else if (!this$northboundStatus.equals(other$northboundStatus)) {
                  return false;
               }

               Object this$narrativeCode = this.getNarrativeCode();
               Object other$narrativeCode = other.getNarrativeCode();
               if (this$narrativeCode == null) {
                  if (other$narrativeCode != null) {
                     return false;
                  }
               } else if (!this$narrativeCode.equals(other$narrativeCode)) {
                  return false;
               }

               label652: {
                  Object this$tranTimestamp = this.getTranTimestamp();
                  Object other$tranTimestamp = other.getTranTimestamp();
                  if (this$tranTimestamp == null) {
                     if (other$tranTimestamp == null) {
                        break label652;
                     }
                  } else if (this$tranTimestamp.equals(other$tranTimestamp)) {
                     break label652;
                  }

                  return false;
               }

               Object this$contraTranDate = this.getContraTranDate();
               Object other$contraTranDate = other.getContraTranDate();
               if (this$contraTranDate == null) {
                  if (other$contraTranDate != null) {
                     return false;
                  }
               } else if (!this$contraTranDate.equals(other$contraTranDate)) {
                  return false;
               }

               Object this$subSeqNo = this.getSubSeqNo();
               Object other$subSeqNo = other.getSubSeqNo();
               if (this$subSeqNo == null) {
                  if (other$subSeqNo != null) {
                     return false;
                  }
               } else if (!this$subSeqNo.equals(other$subSeqNo)) {
                  return false;
               }

               label631: {
                  Object this$othRealBankName = this.getOthRealBankName();
                  Object other$othRealBankName = other.getOthRealBankName();
                  if (this$othRealBankName == null) {
                     if (other$othRealBankName == null) {
                        break label631;
                     }
                  } else if (this$othRealBankName.equals(other$othRealBankName)) {
                     break label631;
                  }

                  return false;
               }

               Object this$narrativeCodeDesc = this.getNarrativeCodeDesc();
               Object other$narrativeCodeDesc = other.getNarrativeCodeDesc();
               if (this$narrativeCodeDesc == null) {
                  if (other$narrativeCodeDesc != null) {
                     return false;
                  }
               } else if (!this$narrativeCodeDesc.equals(other$narrativeCodeDesc)) {
                  return false;
               }

               label617: {
                  Object this$origTranTimestamp = this.getOrigTranTimestamp();
                  Object other$origTranTimestamp = other.getOrigTranTimestamp();
                  if (this$origTranTimestamp == null) {
                     if (other$origTranTimestamp == null) {
                        break label617;
                     }
                  } else if (this$origTranTimestamp.equals(other$origTranTimestamp)) {
                     break label617;
                  }

                  return false;
               }

               Object this$realTranBranchName = this.getRealTranBranchName();
               Object other$realTranBranchName = other.getRealTranBranchName();
               if (this$realTranBranchName == null) {
                  if (other$realTranBranchName != null) {
                     return false;
                  }
               } else if (!this$realTranBranchName.equals(other$realTranBranchName)) {
                  return false;
               }

               Object this$odAmount = this.getOdAmount();
               Object other$odAmount = other.getOdAmount();
               if (this$odAmount == null) {
                  if (other$odAmount == null) {
                     return true;
                  }
               } else if (this$odAmount.equals(other$odAmount)) {
                  return true;
               }

               return false;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100010Out.TranHistArray;
      }
      public String toString() {
         return "Core1400100010Out.TranHistArray(documentType=" + this.getDocumentType() + ", documentId=" + this.getDocumentId() + ", chClientName=" + this.getChClientName() + ", clientNo=" + this.getClientNo() + ", clientType=" + this.getClientType() + ", clientName=" + this.getClientName() + ", baseAcctNo=" + this.getBaseAcctNo() + ", prodType=" + this.getProdType() + ", acctCcy=" + this.getAcctCcy() + ", acctSeqNo=" + this.getAcctSeqNo() + ", acctBranch=" + this.getAcctBranch() + ", amtType=" + this.getAmtType() + ", previousBalAmt=" + this.getPreviousBalAmt() + ", actualBal=" + this.getActualBal() + ", acctDesc=" + this.getAcctDesc() + ", tranAmt=" + this.getTranAmt() + ", channelSeqNo=" + this.getChannelSeqNo() + ", tranType=" + this.getTranType() + ", drTranType=" + this.getDrTranType() + ", othDocumentType=" + this.getOthDocumentType() + ", othDocumentId=" + this.getOthDocumentId() + ", othBaseAcctNo=" + this.getOthBaseAcctNo() + ", othProdType=" + this.getOthProdType() + ", othAcctCcy=" + this.getOthAcctCcy() + ", othAcctSeqNo=" + this.getOthAcctSeqNo() + ", othAcctDesc=" + this.getOthAcctDesc() + ", othBranch=" + this.getOthBranch() + ", othBranchName=" + this.getOthBranchName() + ", othBankName=" + this.getOthBankName() + ", othBankCode=" + this.getOthBankCode() + ", othSeqNo=" + this.getOthSeqNo() + ", othReference=" + this.getOthReference() + ", toAcctNo=" + this.getToAcctNo() + ", toAcctName=" + this.getToAcctName() + ", sourceAcctNo=" + this.getSourceAcctNo() + ", sourceAcctName=" + this.getSourceAcctName() + ", docType=" + this.getDocType() + ", prefix=" + this.getPrefix() + ", voucherNo=" + this.getVoucherNo() + ", withdrawalType=" + this.getWithdrawalType() + ", reasonCode=" + this.getReasonCode() + ", userId=" + this.getUserId() + ", bankSeqNo=" + this.getBankSeqNo() + ", authUserId=" + this.getAuthUserId() + ", apprUserId=" + this.getApprUserId() + ", bankName=" + this.getBankName() + ", bankCode=" + this.getBankCode() + ", branch=" + this.getBranch() + ", reference=" + this.getReference() + ", tranCategory=" + this.getTranCategory() + ", effectDate=" + this.getEffectDate() + ", reversalTranType=" + this.getReversalTranType() + ", reversalDate=" + this.getReversalDate() + ", seqNo=" + this.getSeqNo() + ", tranDesc=" + this.getTranDesc() + ", tranNote=" + this.getTranNote() + ", primaryTranSeqNo=" + this.getPrimaryTranSeqNo() + ", fhSeqNo=" + this.getFhSeqNo() + ", settlementDate=" + this.getSettlementDate() + ", contraEquivAmt=" + this.getContraEquivAmt() + ", baseEquivAmt=" + this.getBaseEquivAmt() + ", crossRate=" + this.getCrossRate() + ", fromCcy=" + this.getFromCcy() + ", fromAmount=" + this.getFromAmount() + ", fromRateFlag=" + this.getFromRateFlag() + ", fromXrate=" + this.getFromXrate() + ", toCcy=" + this.getToCcy() + ", toAmount=" + this.getToAmount() + ", toRateFlag=" + this.getToRateFlag() + ", toXrate=" + this.getToXrate() + ", ovToAmount=" + this.getOvToAmount() + ", rateFlag=" + this.getRateFlag() + ", quoteType=" + this.getQuoteType() + ", terminalId=" + this.getTerminalId() + ", traceId=" + this.getTraceId() + ", narrative=" + this.getNarrative() + ", cashItem=" + this.getCashItem() + ", tranStatus=" + this.getTranStatus() + ", profitCenter=" + this.getProfitCenter() + ", businessUnit=" + this.getBusinessUnit() + ", sourceModule=" + this.getSourceModule() + ", eventType=" + this.getEventType() + ", ccy=" + this.getCcy() + ", crDrMaintInd=" + this.getCrDrMaintInd() + ", balType=" + this.getBalType() + ", printCnt=" + this.getPrintCnt() + ", batchNo=" + this.getBatchNo() + ", bizType=" + this.getBizType() + ", pbkUpdFlag=" + this.getPbkUpdFlag() + ", sourceType=" + this.getSourceType() + ", reversal=" + this.getReversal() + ", payUnit=" + this.getPayUnit() + ", servCharge=" + this.getServCharge() + ", company=" + this.getCompany() + ", acctOpenDate=" + this.getAcctOpenDate() + ", term=" + this.getTerm() + ", termType=" + this.getTermType() + ", othAcctName=" + this.getOthAcctName() + ", realRate=" + this.getRealRate() + ", actualRate=" + this.getActualRate() + ", othRealBaseAcctNo=" + this.getOthRealBaseAcctNo() + ", othRealTranName=" + this.getOthRealTranName() + ", branchName=" + this.getBranchName() + ", toId=" + this.getToId() + ", priAmt=" + this.getPriAmt() + ", piFlag=" + this.getPiFlag() + ", tranDate=" + this.getTranDate() + ", northboundStatus=" + this.getNorthboundStatus() + ", narrativeCode=" + this.getNarrativeCode() + ", tranTimestamp=" + this.getTranTimestamp() + ", contraTranDate=" + this.getContraTranDate() + ", subSeqNo=" + this.getSubSeqNo() + ", othRealBankName=" + this.getOthRealBankName() + ", narrativeCodeDesc=" + this.getNarrativeCodeDesc() + ", origTranTimestamp=" + this.getOrigTranTimestamp() + ", realTranBranchName=" + this.getRealTranBranchName() + ", odAmount=" + this.getOdAmount() + ")";
      }
   }
}
