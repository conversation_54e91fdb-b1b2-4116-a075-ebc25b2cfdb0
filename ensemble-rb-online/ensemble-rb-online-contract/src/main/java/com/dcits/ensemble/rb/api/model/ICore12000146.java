package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000146In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000146Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12000146 {
   String URL = "/rb/nfin/voucher/status/cardunionmaint";


   @ApiDesc("支持卡折一体户的折凭证作废功能")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0146"
   )
   @BusinessCategory("1200-非金融")
   @FunctionCategory("RB06-凭证处理")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core12000146Out runService(Core12000146In var1);
}
