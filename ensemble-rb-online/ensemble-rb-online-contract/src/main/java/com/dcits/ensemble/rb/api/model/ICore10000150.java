package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10000150In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10000150Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore10000150 {
   String URL = "/rb/nfin/trusted/pay/operate";


   @ApiRemark("受托支付维护")
   @ApiDesc("处理受托支付业务通过支付系统汇款之后进行退回的情况，如果原有的限制编号状态为生效，那么对原账户的该限制编号进行止付金额的修改；如果原有的限制编号状态为失效（即受托支付业务已经全额划款），那么恢复限制编号的状态（改为生效），同时对账户进行金额的止付")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "0150"
   )
   @BusinessCategory("1000-金融交易")
   @FunctionCategory("RB03-金融交易")
   @ConsumeSys("PS2")
   @ApiUseStatus("PRODUCT-产品")
   Core10000150Out runService(Core10000150In var1);
}
