package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.util.List;

@MessageIn
public class MbsdCore12009987In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private MbsdCore12009987In.Body body;

   public MbsdCore12009987In.Body getBody() {
      return this.body;
   }

   public void setBody(MbsdCore12009987In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "MbsdCore12009987In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof MbsdCore12009987In)) {
         return false;
      } else {
         MbsdCore12009987In other = (MbsdCore12009987In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof MbsdCore12009987In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "业务区分标记",
         notNull = true,
         length = "2",
         inDesc = "A-新增,U-修改,D-删除,C-复核,01-录入,02-修改,03-删除,I-新增,R-启用,M-修改,E-复核,O-出库,B-退回,L-丢失,W-代销毁,S-手工销号,R-拒绝,N-不操作",
         remark = "业务区分标记",
         maxSize = 2
      )
      private String optionKw;
      @V(
         desc = "账户内部键值",
         notNull = true,
         length = "15",
         remark = "账户内部键值"
      )
      private Long internalKey;
      @V(
         desc = "账号/卡号",
         notNull = true,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "账户名称",
         notNull = false,
         length = "200",
         remark = "账户名称，一般指中文账户名称",
         maxSize = 200
      )
      private String acctName;
      @V(
         desc = "交易柜员",
         notNull = false,
         length = "30",
         remark = "交易柜员",
         maxSize = 30
      )
      private String userId;
      @V(
         desc = "交易日期",
         notNull = false,
         remark = "交易日期"
      )
      private String tranDate;
      @V(
         desc = "备注",
         notNull = false,
         length = "200",
         remark = "备注",
         maxSize = 200
      )
      private String remark;
      @V(
         desc = "数组",
         notNull = false,
         remark = "数组"
      )
      private List<MbsdCore12009987In.Body.AcctArray> acctArray;
      @V(
         desc = "是否黑名单客户",
         notNull = false,
         length = "1",
         inDesc = "Y-是,N-否",
         remark = "是否黑名单客户",
         maxSize = 1
      )
      private String blacklistIndFlag;

      public String getOptionKw() {
         return this.optionKw;
      }

      public Long getInternalKey() {
         return this.internalKey;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getCcy() {
         return this.ccy;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getAcctName() {
         return this.acctName;
      }

      public String getUserId() {
         return this.userId;
      }

      public String getTranDate() {
         return this.tranDate;
      }

      public String getRemark() {
         return this.remark;
      }

      public List<MbsdCore12009987In.Body.AcctArray> getAcctArray() {
         return this.acctArray;
      }

      public String getBlacklistIndFlag() {
         return this.blacklistIndFlag;
      }

      public void setOptionKw(String optionKw) {
         this.optionKw = optionKw;
      }

      public void setInternalKey(Long internalKey) {
         this.internalKey = internalKey;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setAcctName(String acctName) {
         this.acctName = acctName;
      }

      public void setUserId(String userId) {
         this.userId = userId;
      }

      public void setTranDate(String tranDate) {
         this.tranDate = tranDate;
      }

      public void setRemark(String remark) {
         this.remark = remark;
      }

      public void setAcctArray(List<MbsdCore12009987In.Body.AcctArray> acctArray) {
         this.acctArray = acctArray;
      }

      public void setBlacklistIndFlag(String blacklistIndFlag) {
         this.blacklistIndFlag = blacklistIndFlag;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof MbsdCore12009987In.Body)) {
            return false;
         } else {
            MbsdCore12009987In.Body other = (MbsdCore12009987In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label155: {
                  Object this$optionKw = this.getOptionKw();
                  Object other$optionKw = other.getOptionKw();
                  if (this$optionKw == null) {
                     if (other$optionKw == null) {
                        break label155;
                     }
                  } else if (this$optionKw.equals(other$optionKw)) {
                     break label155;
                  }

                  return false;
               }

               Object this$internalKey = this.getInternalKey();
               Object other$internalKey = other.getInternalKey();
               if (this$internalKey == null) {
                  if (other$internalKey != null) {
                     return false;
                  }
               } else if (!this$internalKey.equals(other$internalKey)) {
                  return false;
               }

               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo != null) {
                     return false;
                  }
               } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
                  return false;
               }

               label134: {
                  Object this$prodType = this.getProdType();
                  Object other$prodType = other.getProdType();
                  if (this$prodType == null) {
                     if (other$prodType == null) {
                        break label134;
                     }
                  } else if (this$prodType.equals(other$prodType)) {
                     break label134;
                  }

                  return false;
               }

               label127: {
                  Object this$ccy = this.getCcy();
                  Object other$ccy = other.getCcy();
                  if (this$ccy == null) {
                     if (other$ccy == null) {
                        break label127;
                     }
                  } else if (this$ccy.equals(other$ccy)) {
                     break label127;
                  }

                  return false;
               }

               label120: {
                  Object this$acctSeqNo = this.getAcctSeqNo();
                  Object other$acctSeqNo = other.getAcctSeqNo();
                  if (this$acctSeqNo == null) {
                     if (other$acctSeqNo == null) {
                        break label120;
                     }
                  } else if (this$acctSeqNo.equals(other$acctSeqNo)) {
                     break label120;
                  }

                  return false;
               }

               Object this$acctName = this.getAcctName();
               Object other$acctName = other.getAcctName();
               if (this$acctName == null) {
                  if (other$acctName != null) {
                     return false;
                  }
               } else if (!this$acctName.equals(other$acctName)) {
                  return false;
               }

               label106: {
                  Object this$userId = this.getUserId();
                  Object other$userId = other.getUserId();
                  if (this$userId == null) {
                     if (other$userId == null) {
                        break label106;
                     }
                  } else if (this$userId.equals(other$userId)) {
                     break label106;
                  }

                  return false;
               }

               Object this$tranDate = this.getTranDate();
               Object other$tranDate = other.getTranDate();
               if (this$tranDate == null) {
                  if (other$tranDate != null) {
                     return false;
                  }
               } else if (!this$tranDate.equals(other$tranDate)) {
                  return false;
               }

               label92: {
                  Object this$remark = this.getRemark();
                  Object other$remark = other.getRemark();
                  if (this$remark == null) {
                     if (other$remark == null) {
                        break label92;
                     }
                  } else if (this$remark.equals(other$remark)) {
                     break label92;
                  }

                  return false;
               }

               Object this$acctArray = this.getAcctArray();
               Object other$acctArray = other.getAcctArray();
               if (this$acctArray == null) {
                  if (other$acctArray != null) {
                     return false;
                  }
               } else if (!this$acctArray.equals(other$acctArray)) {
                  return false;
               }

               Object this$blacklistIndFlag = this.getBlacklistIndFlag();
               Object other$blacklistIndFlag = other.getBlacklistIndFlag();
               if (this$blacklistIndFlag == null) {
                  if (other$blacklistIndFlag != null) {
                     return false;
                  }
               } else if (!this$blacklistIndFlag.equals(other$blacklistIndFlag)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof MbsdCore12009987In.Body;
      }
      public String toString() {
         return "MbsdCore12009987In.Body(optionKw=" + this.getOptionKw() + ", internalKey=" + this.getInternalKey() + ", baseAcctNo=" + this.getBaseAcctNo() + ", prodType=" + this.getProdType() + ", ccy=" + this.getCcy() + ", acctSeqNo=" + this.getAcctSeqNo() + ", acctName=" + this.getAcctName() + ", userId=" + this.getUserId() + ", tranDate=" + this.getTranDate() + ", remark=" + this.getRemark() + ", acctArray=" + this.getAcctArray() + ", blacklistIndFlag=" + this.getBlacklistIndFlag() + ")";
      }

      public static class AcctArray {
         @V(
            desc = "对手账户内部键",
            notNull = true,
            length = "15",
            remark = "对手账户内部键"
         )
         private Long othInternalKey;
         @V(
            desc = "对方账号/卡号",
            notNull = true,
            length = "50",
            remark = "对方账号卡号",
            maxSize = 50
         )
         private String othBaseAcctNo;
         @V(
            desc = "对方账户产品类型",
            notNull = false,
            length = "20",
            remark = "对方账户产品类型",
            maxSize = 20
         )
         private String othProdType;
         @V(
            desc = "对方账户序列号",
            notNull = false,
            length = "5",
            remark = "对方账户序列号",
            maxSize = 5
         )
         private String othAcctSeqNo;
         @V(
            desc = "对手账户币种",
            notNull = false,
            length = "3",
            remark = "对手账户币种",
            maxSize = 3
         )
         private String othCcy;
         @V(
            desc = "对方账户名称",
            notNull = false,
            length = "200",
            remark = "对方账户名称",
            maxSize = 200
         )
         private String othAcctName;
         @V(
            desc = "渠道",
            notNull = false,
            length = "10",
            inDesc = "JZ-集中作业,MT-柜面,ZD-智能网点自助交易平台,VT-远程智能自助银行,QT-XBankT10,QE-快窗智能高柜,CP-手机银行,EB-个人网上银行,CB-企业网上银行,CM-现金管理,WB-微信银行A ,PT-支付",
            remark = "渠道细类",
            maxSize = 10
         )
         private String channel;

         public Long getOthInternalKey() {
            return this.othInternalKey;
         }

         public String getOthBaseAcctNo() {
            return this.othBaseAcctNo;
         }

         public String getOthProdType() {
            return this.othProdType;
         }

         public String getOthAcctSeqNo() {
            return this.othAcctSeqNo;
         }

         public String getOthCcy() {
            return this.othCcy;
         }

         public String getOthAcctName() {
            return this.othAcctName;
         }

         public String getChannel() {
            return this.channel;
         }

         public void setOthInternalKey(Long othInternalKey) {
            this.othInternalKey = othInternalKey;
         }

         public void setOthBaseAcctNo(String othBaseAcctNo) {
            this.othBaseAcctNo = othBaseAcctNo;
         }

         public void setOthProdType(String othProdType) {
            this.othProdType = othProdType;
         }

         public void setOthAcctSeqNo(String othAcctSeqNo) {
            this.othAcctSeqNo = othAcctSeqNo;
         }

         public void setOthCcy(String othCcy) {
            this.othCcy = othCcy;
         }

         public void setOthAcctName(String othAcctName) {
            this.othAcctName = othAcctName;
         }

         public void setChannel(String channel) {
            this.channel = channel;
         }

         public boolean equals(Object o) {
            if (o == this) {
               return true;
            } else if (!(o instanceof MbsdCore12009987In.Body.AcctArray)) {
               return false;
            } else {
               MbsdCore12009987In.Body.AcctArray other = (MbsdCore12009987In.Body.AcctArray)o;
               if (!other.canEqual(this)) {
                  return false;
               } else {
                  label95: {
                     Object this$othInternalKey = this.getOthInternalKey();
                     Object other$othInternalKey = other.getOthInternalKey();
                     if (this$othInternalKey == null) {
                        if (other$othInternalKey == null) {
                           break label95;
                        }
                     } else if (this$othInternalKey.equals(other$othInternalKey)) {
                        break label95;
                     }

                     return false;
                  }

                  Object this$othBaseAcctNo = this.getOthBaseAcctNo();
                  Object other$othBaseAcctNo = other.getOthBaseAcctNo();
                  if (this$othBaseAcctNo == null) {
                     if (other$othBaseAcctNo != null) {
                        return false;
                     }
                  } else if (!this$othBaseAcctNo.equals(other$othBaseAcctNo)) {
                     return false;
                  }

                  Object this$othProdType = this.getOthProdType();
                  Object other$othProdType = other.getOthProdType();
                  if (this$othProdType == null) {
                     if (other$othProdType != null) {
                        return false;
                     }
                  } else if (!this$othProdType.equals(other$othProdType)) {
                     return false;
                  }

                  label74: {
                     Object this$othAcctSeqNo = this.getOthAcctSeqNo();
                     Object other$othAcctSeqNo = other.getOthAcctSeqNo();
                     if (this$othAcctSeqNo == null) {
                        if (other$othAcctSeqNo == null) {
                           break label74;
                        }
                     } else if (this$othAcctSeqNo.equals(other$othAcctSeqNo)) {
                        break label74;
                     }

                     return false;
                  }

                  label67: {
                     Object this$othCcy = this.getOthCcy();
                     Object other$othCcy = other.getOthCcy();
                     if (this$othCcy == null) {
                        if (other$othCcy == null) {
                           break label67;
                        }
                     } else if (this$othCcy.equals(other$othCcy)) {
                        break label67;
                     }

                     return false;
                  }

                  Object this$othAcctName = this.getOthAcctName();
                  Object other$othAcctName = other.getOthAcctName();
                  if (this$othAcctName == null) {
                     if (other$othAcctName != null) {
                        return false;
                     }
                  } else if (!this$othAcctName.equals(other$othAcctName)) {
                     return false;
                  }

                  Object this$channel = this.getChannel();
                  Object other$channel = other.getChannel();
                  if (this$channel == null) {
                     if (other$channel != null) {
                        return false;
                     }
                  } else if (!this$channel.equals(other$channel)) {
                     return false;
                  }

                  return true;
               }
            }
         }

         protected boolean canEqual(Object other) {
            return other instanceof MbsdCore12009987In.Body.AcctArray;
         }
         public String toString() {
            return "MbsdCore12009987In.Body.AcctArray(othInternalKey=" + this.getOthInternalKey() + ", othBaseAcctNo=" + this.getOthBaseAcctNo() + ", othProdType=" + this.getOthProdType() + ", othAcctSeqNo=" + this.getOthAcctSeqNo() + ", othCcy=" + this.getOthCcy() + ", othAcctName=" + this.getOthAcctName() + ", channel=" + this.getChannel() + ")";
         }
      }
   }
}
