package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400100025Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100025Out.SubProdArray> subProdArray;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100025Out.SubArray> subArray;

   public List<Core1400100025Out.SubProdArray> getSubProdArray() {
      return this.subProdArray;
   }

   public List<Core1400100025Out.SubArray> getSubArray() {
      return this.subArray;
   }

   public void setSubProdArray(List<Core1400100025Out.SubProdArray> subProdArray) {
      this.subProdArray = subProdArray;
   }

   public void setSubArray(List<Core1400100025Out.SubArray> subArray) {
      this.subArray = subArray;
   }

   public String toString() {
      return "Core1400100025Out(subProdArray=" + this.getSubProdArray() + ", subArray=" + this.getSubArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100025Out)) {
         return false;
      } else {
         Core1400100025Out other = (Core1400100025Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$subProdArray = this.getSubProdArray();
            Object other$subProdArray = other.getSubProdArray();
            if (this$subProdArray == null) {
               if (other$subProdArray != null) {
                  return false;
               }
            } else if (!this$subProdArray.equals(other$subProdArray)) {
               return false;
            }

            Object this$subArray = this.getSubArray();
            Object other$subArray = other.getSubArray();
            if (this$subArray == null) {
               if (other$subArray != null) {
                  return false;
               }
            } else if (!this$subArray.equals(other$subArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100025Out;
   }
   public static class SubArray {
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "余额",
         notNull = false,
         length = "17",
         remark = "余额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal balance;
      @V(
         desc = "基础等值金额",
         notNull = false,
         length = "17",
         remark = "基础等值金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal baseEquivAmt;
      @V(
         desc = "执行汇率",
         notNull = false,
         length = "15",
         remark = "执行汇率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal exchRate;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public BigDecimal getBalance() {
         return this.balance;
      }

      public BigDecimal getBaseEquivAmt() {
         return this.baseEquivAmt;
      }

      public BigDecimal getExchRate() {
         return this.exchRate;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setBalance(BigDecimal balance) {
         this.balance = balance;
      }

      public void setBaseEquivAmt(BigDecimal baseEquivAmt) {
         this.baseEquivAmt = baseEquivAmt;
      }

      public void setExchRate(BigDecimal exchRate) {
         this.exchRate = exchRate;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100025Out.SubArray)) {
            return false;
         } else {
            Core1400100025Out.SubArray other = (Core1400100025Out.SubArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo != null) {
                     return false;
                  }
               } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               Object this$acctCcy = this.getAcctCcy();
               Object other$acctCcy = other.getAcctCcy();
               if (this$acctCcy == null) {
                  if (other$acctCcy != null) {
                     return false;
                  }
               } else if (!this$acctCcy.equals(other$acctCcy)) {
                  return false;
               }

               label62: {
                  Object this$balance = this.getBalance();
                  Object other$balance = other.getBalance();
                  if (this$balance == null) {
                     if (other$balance == null) {
                        break label62;
                     }
                  } else if (this$balance.equals(other$balance)) {
                     break label62;
                  }

                  return false;
               }

               label55: {
                  Object this$baseEquivAmt = this.getBaseEquivAmt();
                  Object other$baseEquivAmt = other.getBaseEquivAmt();
                  if (this$baseEquivAmt == null) {
                     if (other$baseEquivAmt == null) {
                        break label55;
                     }
                  } else if (this$baseEquivAmt.equals(other$baseEquivAmt)) {
                     break label55;
                  }

                  return false;
               }

               Object this$exchRate = this.getExchRate();
               Object other$exchRate = other.getExchRate();
               if (this$exchRate == null) {
                  if (other$exchRate != null) {
                     return false;
                  }
               } else if (!this$exchRate.equals(other$exchRate)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100025Out.SubArray;
      }
      public String toString() {
         return "Core1400100025Out.SubArray(baseAcctNo=" + this.getBaseAcctNo() + ", prodType=" + this.getProdType() + ", acctCcy=" + this.getAcctCcy() + ", balance=" + this.getBalance() + ", baseEquivAmt=" + this.getBaseEquivAmt() + ", exchRate=" + this.getExchRate() + ")";
      }
   }

   public static class SubProdArray {
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "余额",
         notNull = false,
         length = "17",
         remark = "余额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal balance;

      public String getProdType() {
         return this.prodType;
      }

      public String getCcy() {
         return this.ccy;
      }

      public BigDecimal getBalance() {
         return this.balance;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setBalance(BigDecimal balance) {
         this.balance = balance;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100025Out.SubProdArray)) {
            return false;
         } else {
            Core1400100025Out.SubProdArray other = (Core1400100025Out.SubProdArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label47: {
                  Object this$prodType = this.getProdType();
                  Object other$prodType = other.getProdType();
                  if (this$prodType == null) {
                     if (other$prodType == null) {
                        break label47;
                     }
                  } else if (this$prodType.equals(other$prodType)) {
                     break label47;
                  }

                  return false;
               }

               Object this$ccy = this.getCcy();
               Object other$ccy = other.getCcy();
               if (this$ccy == null) {
                  if (other$ccy != null) {
                     return false;
                  }
               } else if (!this$ccy.equals(other$ccy)) {
                  return false;
               }

               Object this$balance = this.getBalance();
               Object other$balance = other.getBalance();
               if (this$balance == null) {
                  if (other$balance != null) {
                     return false;
                  }
               } else if (!this$balance.equals(other$balance)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100025Out.SubProdArray;
      }
      public String toString() {
         return "Core1400100025Out.SubProdArray(prodType=" + this.getProdType() + ", ccy=" + this.getCcy() + ", balance=" + this.getBalance() + ")";
      }
   }
}
