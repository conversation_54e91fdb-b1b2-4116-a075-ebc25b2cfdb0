package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400061087Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "存续期公允价值重估登记簿数组",
      notNull = false,
      remark = "存续期公允价值重估登记簿数组"
   )
   private List<Core1400061087Out.ExchangeAgainestRegArray> exchangeAgainestRegArray;

   public List<Core1400061087Out.ExchangeAgainestRegArray> getExchangeAgainestRegArray() {
      return this.exchangeAgainestRegArray;
   }

   public void setExchangeAgainestRegArray(List<Core1400061087Out.ExchangeAgainestRegArray> exchangeAgainestRegArray) {
      this.exchangeAgainestRegArray = exchangeAgainestRegArray;
   }

   public String toString() {
      return "Core1400061087Out(exchangeAgainestRegArray=" + this.getExchangeAgainestRegArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400061087Out)) {
         return false;
      } else {
         Core1400061087Out other = (Core1400061087Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$exchangeAgainestRegArray = this.getExchangeAgainestRegArray();
            Object other$exchangeAgainestRegArray = other.getExchangeAgainestRegArray();
            if (this$exchangeAgainestRegArray == null) {
               if (other$exchangeAgainestRegArray != null) {
                  return false;
               }
            } else if (!this$exchangeAgainestRegArray.equals(other$exchangeAgainestRegArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400061087Out;
   }
   public static class ExchangeAgainestRegArray {
      @V(
         desc = "登记日期",
         notNull = false,
         remark = "登记日期"
      )
      private String regTranDate;
      @V(
         desc = "业务编号",
         notNull = false,
         length = "50",
         remark = "业务编号",
         maxSize = 50
      )
      private String busiNo;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "客户中文名称",
         notNull = false,
         length = "200",
         remark = "客户中文名称",
         maxSize = 200
      )
      private String chClientName;
      @V(
         desc = "客户英文名称",
         notNull = false,
         length = "200",
         remark = "客户英文名称",
         maxSize = 200
      )
      private String enClientName;
      @V(
         desc = "外币币种",
         notNull = false,
         length = "3",
         remark = "外币币种",
         maxSize = 3
      )
      private String foreCcy;
      @V(
         desc = "我行对客户报价（原）",
         notNull = false,
         length = "17",
         remark = "我行对客户报价（原）",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal offerLetterOld;
      @V(
         desc = "合作银行报价（原）",
         notNull = false,
         length = "17",
         remark = "合作银行报价（原）",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal teamBankRateOld;
      @V(
         desc = "我行对客户报价（新）",
         notNull = false,
         length = "17",
         remark = "我行对客户报价（新）",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal offerLetterNew;
      @V(
         desc = "合作银行报价（新）",
         notNull = false,
         length = "17",
         remark = "合作银行报价（新）",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal teamBankRateNew;
      @V(
         desc = "外币金额（原）",
         notNull = false,
         length = "17",
         remark = "外币金额（原）",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal foreAmtOld;
      @V(
         desc = "人民币金额（原）",
         notNull = false,
         length = "17",
         remark = "人民币金额（原）",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal cnyAmtOld;
      @V(
         desc = "外币金额（新）",
         notNull = false,
         length = "17",
         remark = "外币金额（新）",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal foreAmtNew;
      @V(
         desc = "人民币金额（新）",
         notNull = false,
         length = "17",
         remark = "人民币金额（新）",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal cnyAmtNew;
      @V(
         desc = "损益标识",
         notNull = false,
         length = "1",
         remark = "损益标识",
         maxSize = 1
      )
      private String lossIncomeFlag;
      @V(
         desc = "重估损益金额",
         notNull = false,
         length = "17",
         remark = "重估损益金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal lossIncomeAmt;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "序号",
         notNull = false,
         length = "50",
         remark = "序号",
         maxSize = 50
      )
      private String seqNo;
      @V(
         desc = "损失外币金额",
         notNull = false,
         length = "17",
         remark = "损失外币金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal lossForeAmt;
      @V(
         desc = "损失人民币金额",
         notNull = false,
         length = "17",
         remark = "损失人民币金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal lossCnyAmt;
      @V(
         desc = "当日存入金额",
         notNull = false,
         length = "17",
         remark = "当日存入金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal depAmt;
      @V(
         desc = "冻结编号",
         notNull = false,
         length = "50",
         remark = "冻结编号",
         maxSize = 50
      )
      private String restraintSeqNo;
      @V(
         desc = "初次预警",
         notNull = false,
         length = "1",
         remark = "初次预警",
         maxSize = 1
      )
      private String waringFlag;
      @V(
         desc = "再次预警",
         notNull = false,
         length = "1",
         remark = "再次预警 0-预警 1 -不预警",
         maxSize = 1
      )
      private String thenWaringFlag;
      @V(
         desc = "兑换类型",
         notNull = false,
         length = "1",
         remark = "兑换类型",
         maxSize = 1
      )
      private String exType;
      @V(
         desc = "上一保证金额",
         notNull = false,
         length = "17",
         remark = "上一保证金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal lastBailAmount;

      public String getRegTranDate() {
         return this.regTranDate;
      }

      public String getBusiNo() {
         return this.busiNo;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public String getChClientName() {
         return this.chClientName;
      }

      public String getEnClientName() {
         return this.enClientName;
      }

      public String getForeCcy() {
         return this.foreCcy;
      }

      public BigDecimal getOfferLetterOld() {
         return this.offerLetterOld;
      }

      public BigDecimal getTeamBankRateOld() {
         return this.teamBankRateOld;
      }

      public BigDecimal getOfferLetterNew() {
         return this.offerLetterNew;
      }

      public BigDecimal getTeamBankRateNew() {
         return this.teamBankRateNew;
      }

      public BigDecimal getForeAmtOld() {
         return this.foreAmtOld;
      }

      public BigDecimal getCnyAmtOld() {
         return this.cnyAmtOld;
      }

      public BigDecimal getForeAmtNew() {
         return this.foreAmtNew;
      }

      public BigDecimal getCnyAmtNew() {
         return this.cnyAmtNew;
      }

      public String getLossIncomeFlag() {
         return this.lossIncomeFlag;
      }

      public BigDecimal getLossIncomeAmt() {
         return this.lossIncomeAmt;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getSeqNo() {
         return this.seqNo;
      }

      public BigDecimal getLossForeAmt() {
         return this.lossForeAmt;
      }

      public BigDecimal getLossCnyAmt() {
         return this.lossCnyAmt;
      }

      public BigDecimal getDepAmt() {
         return this.depAmt;
      }

      public String getRestraintSeqNo() {
         return this.restraintSeqNo;
      }

      public String getWaringFlag() {
         return this.waringFlag;
      }

      public String getThenWaringFlag() {
         return this.thenWaringFlag;
      }

      public String getExType() {
         return this.exType;
      }

      public BigDecimal getLastBailAmount() {
         return this.lastBailAmount;
      }

      public void setRegTranDate(String regTranDate) {
         this.regTranDate = regTranDate;
      }

      public void setBusiNo(String busiNo) {
         this.busiNo = busiNo;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setChClientName(String chClientName) {
         this.chClientName = chClientName;
      }

      public void setEnClientName(String enClientName) {
         this.enClientName = enClientName;
      }

      public void setForeCcy(String foreCcy) {
         this.foreCcy = foreCcy;
      }

      public void setOfferLetterOld(BigDecimal offerLetterOld) {
         this.offerLetterOld = offerLetterOld;
      }

      public void setTeamBankRateOld(BigDecimal teamBankRateOld) {
         this.teamBankRateOld = teamBankRateOld;
      }

      public void setOfferLetterNew(BigDecimal offerLetterNew) {
         this.offerLetterNew = offerLetterNew;
      }

      public void setTeamBankRateNew(BigDecimal teamBankRateNew) {
         this.teamBankRateNew = teamBankRateNew;
      }

      public void setForeAmtOld(BigDecimal foreAmtOld) {
         this.foreAmtOld = foreAmtOld;
      }

      public void setCnyAmtOld(BigDecimal cnyAmtOld) {
         this.cnyAmtOld = cnyAmtOld;
      }

      public void setForeAmtNew(BigDecimal foreAmtNew) {
         this.foreAmtNew = foreAmtNew;
      }

      public void setCnyAmtNew(BigDecimal cnyAmtNew) {
         this.cnyAmtNew = cnyAmtNew;
      }

      public void setLossIncomeFlag(String lossIncomeFlag) {
         this.lossIncomeFlag = lossIncomeFlag;
      }

      public void setLossIncomeAmt(BigDecimal lossIncomeAmt) {
         this.lossIncomeAmt = lossIncomeAmt;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setSeqNo(String seqNo) {
         this.seqNo = seqNo;
      }

      public void setLossForeAmt(BigDecimal lossForeAmt) {
         this.lossForeAmt = lossForeAmt;
      }

      public void setLossCnyAmt(BigDecimal lossCnyAmt) {
         this.lossCnyAmt = lossCnyAmt;
      }

      public void setDepAmt(BigDecimal depAmt) {
         this.depAmt = depAmt;
      }

      public void setRestraintSeqNo(String restraintSeqNo) {
         this.restraintSeqNo = restraintSeqNo;
      }

      public void setWaringFlag(String waringFlag) {
         this.waringFlag = waringFlag;
      }

      public void setThenWaringFlag(String thenWaringFlag) {
         this.thenWaringFlag = thenWaringFlag;
      }

      public void setExType(String exType) {
         this.exType = exType;
      }

      public void setLastBailAmount(BigDecimal lastBailAmount) {
         this.lastBailAmount = lastBailAmount;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400061087Out.ExchangeAgainestRegArray)) {
            return false;
         } else {
            Core1400061087Out.ExchangeAgainestRegArray other = (Core1400061087Out.ExchangeAgainestRegArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$regTranDate = this.getRegTranDate();
               Object other$regTranDate = other.getRegTranDate();
               if (this$regTranDate == null) {
                  if (other$regTranDate != null) {
                     return false;
                  }
               } else if (!this$regTranDate.equals(other$regTranDate)) {
                  return false;
               }

               Object this$busiNo = this.getBusiNo();
               Object other$busiNo = other.getBusiNo();
               if (this$busiNo == null) {
                  if (other$busiNo != null) {
                     return false;
                  }
               } else if (!this$busiNo.equals(other$busiNo)) {
                  return false;
               }

               Object this$clientNo = this.getClientNo();
               Object other$clientNo = other.getClientNo();
               if (this$clientNo == null) {
                  if (other$clientNo != null) {
                     return false;
                  }
               } else if (!this$clientNo.equals(other$clientNo)) {
                  return false;
               }

               label302: {
                  Object this$chClientName = this.getChClientName();
                  Object other$chClientName = other.getChClientName();
                  if (this$chClientName == null) {
                     if (other$chClientName == null) {
                        break label302;
                     }
                  } else if (this$chClientName.equals(other$chClientName)) {
                     break label302;
                  }

                  return false;
               }

               label295: {
                  Object this$enClientName = this.getEnClientName();
                  Object other$enClientName = other.getEnClientName();
                  if (this$enClientName == null) {
                     if (other$enClientName == null) {
                        break label295;
                     }
                  } else if (this$enClientName.equals(other$enClientName)) {
                     break label295;
                  }

                  return false;
               }

               Object this$foreCcy = this.getForeCcy();
               Object other$foreCcy = other.getForeCcy();
               if (this$foreCcy == null) {
                  if (other$foreCcy != null) {
                     return false;
                  }
               } else if (!this$foreCcy.equals(other$foreCcy)) {
                  return false;
               }

               label281: {
                  Object this$offerLetterOld = this.getOfferLetterOld();
                  Object other$offerLetterOld = other.getOfferLetterOld();
                  if (this$offerLetterOld == null) {
                     if (other$offerLetterOld == null) {
                        break label281;
                     }
                  } else if (this$offerLetterOld.equals(other$offerLetterOld)) {
                     break label281;
                  }

                  return false;
               }

               label274: {
                  Object this$teamBankRateOld = this.getTeamBankRateOld();
                  Object other$teamBankRateOld = other.getTeamBankRateOld();
                  if (this$teamBankRateOld == null) {
                     if (other$teamBankRateOld == null) {
                        break label274;
                     }
                  } else if (this$teamBankRateOld.equals(other$teamBankRateOld)) {
                     break label274;
                  }

                  return false;
               }

               Object this$offerLetterNew = this.getOfferLetterNew();
               Object other$offerLetterNew = other.getOfferLetterNew();
               if (this$offerLetterNew == null) {
                  if (other$offerLetterNew != null) {
                     return false;
                  }
               } else if (!this$offerLetterNew.equals(other$offerLetterNew)) {
                  return false;
               }

               Object this$teamBankRateNew = this.getTeamBankRateNew();
               Object other$teamBankRateNew = other.getTeamBankRateNew();
               if (this$teamBankRateNew == null) {
                  if (other$teamBankRateNew != null) {
                     return false;
                  }
               } else if (!this$teamBankRateNew.equals(other$teamBankRateNew)) {
                  return false;
               }

               label253: {
                  Object this$foreAmtOld = this.getForeAmtOld();
                  Object other$foreAmtOld = other.getForeAmtOld();
                  if (this$foreAmtOld == null) {
                     if (other$foreAmtOld == null) {
                        break label253;
                     }
                  } else if (this$foreAmtOld.equals(other$foreAmtOld)) {
                     break label253;
                  }

                  return false;
               }

               label246: {
                  Object this$cnyAmtOld = this.getCnyAmtOld();
                  Object other$cnyAmtOld = other.getCnyAmtOld();
                  if (this$cnyAmtOld == null) {
                     if (other$cnyAmtOld == null) {
                        break label246;
                     }
                  } else if (this$cnyAmtOld.equals(other$cnyAmtOld)) {
                     break label246;
                  }

                  return false;
               }

               Object this$foreAmtNew = this.getForeAmtNew();
               Object other$foreAmtNew = other.getForeAmtNew();
               if (this$foreAmtNew == null) {
                  if (other$foreAmtNew != null) {
                     return false;
                  }
               } else if (!this$foreAmtNew.equals(other$foreAmtNew)) {
                  return false;
               }

               label232: {
                  Object this$cnyAmtNew = this.getCnyAmtNew();
                  Object other$cnyAmtNew = other.getCnyAmtNew();
                  if (this$cnyAmtNew == null) {
                     if (other$cnyAmtNew == null) {
                        break label232;
                     }
                  } else if (this$cnyAmtNew.equals(other$cnyAmtNew)) {
                     break label232;
                  }

                  return false;
               }

               Object this$lossIncomeFlag = this.getLossIncomeFlag();
               Object other$lossIncomeFlag = other.getLossIncomeFlag();
               if (this$lossIncomeFlag == null) {
                  if (other$lossIncomeFlag != null) {
                     return false;
                  }
               } else if (!this$lossIncomeFlag.equals(other$lossIncomeFlag)) {
                  return false;
               }

               label218: {
                  Object this$lossIncomeAmt = this.getLossIncomeAmt();
                  Object other$lossIncomeAmt = other.getLossIncomeAmt();
                  if (this$lossIncomeAmt == null) {
                     if (other$lossIncomeAmt == null) {
                        break label218;
                     }
                  } else if (this$lossIncomeAmt.equals(other$lossIncomeAmt)) {
                     break label218;
                  }

                  return false;
               }

               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo != null) {
                     return false;
                  }
               } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
                  return false;
               }

               Object this$seqNo = this.getSeqNo();
               Object other$seqNo = other.getSeqNo();
               if (this$seqNo == null) {
                  if (other$seqNo != null) {
                     return false;
                  }
               } else if (!this$seqNo.equals(other$seqNo)) {
                  return false;
               }

               Object this$lossForeAmt = this.getLossForeAmt();
               Object other$lossForeAmt = other.getLossForeAmt();
               if (this$lossForeAmt == null) {
                  if (other$lossForeAmt != null) {
                     return false;
                  }
               } else if (!this$lossForeAmt.equals(other$lossForeAmt)) {
                  return false;
               }

               label190: {
                  Object this$lossCnyAmt = this.getLossCnyAmt();
                  Object other$lossCnyAmt = other.getLossCnyAmt();
                  if (this$lossCnyAmt == null) {
                     if (other$lossCnyAmt == null) {
                        break label190;
                     }
                  } else if (this$lossCnyAmt.equals(other$lossCnyAmt)) {
                     break label190;
                  }

                  return false;
               }

               label183: {
                  Object this$depAmt = this.getDepAmt();
                  Object other$depAmt = other.getDepAmt();
                  if (this$depAmt == null) {
                     if (other$depAmt == null) {
                        break label183;
                     }
                  } else if (this$depAmt.equals(other$depAmt)) {
                     break label183;
                  }

                  return false;
               }

               Object this$restraintSeqNo = this.getRestraintSeqNo();
               Object other$restraintSeqNo = other.getRestraintSeqNo();
               if (this$restraintSeqNo == null) {
                  if (other$restraintSeqNo != null) {
                     return false;
                  }
               } else if (!this$restraintSeqNo.equals(other$restraintSeqNo)) {
                  return false;
               }

               label169: {
                  Object this$waringFlag = this.getWaringFlag();
                  Object other$waringFlag = other.getWaringFlag();
                  if (this$waringFlag == null) {
                     if (other$waringFlag == null) {
                        break label169;
                     }
                  } else if (this$waringFlag.equals(other$waringFlag)) {
                     break label169;
                  }

                  return false;
               }

               label162: {
                  Object this$thenWaringFlag = this.getThenWaringFlag();
                  Object other$thenWaringFlag = other.getThenWaringFlag();
                  if (this$thenWaringFlag == null) {
                     if (other$thenWaringFlag == null) {
                        break label162;
                     }
                  } else if (this$thenWaringFlag.equals(other$thenWaringFlag)) {
                     break label162;
                  }

                  return false;
               }

               Object this$exType = this.getExType();
               Object other$exType = other.getExType();
               if (this$exType == null) {
                  if (other$exType != null) {
                     return false;
                  }
               } else if (!this$exType.equals(other$exType)) {
                  return false;
               }

               Object this$lastBailAmount = this.getLastBailAmount();
               Object other$lastBailAmount = other.getLastBailAmount();
               if (this$lastBailAmount == null) {
                  if (other$lastBailAmount != null) {
                     return false;
                  }
               } else if (!this$lastBailAmount.equals(other$lastBailAmount)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400061087Out.ExchangeAgainestRegArray;
      }
      public String toString() {
         return "Core1400061087Out.ExchangeAgainestRegArray(regTranDate=" + this.getRegTranDate() + ", busiNo=" + this.getBusiNo() + ", clientNo=" + this.getClientNo() + ", chClientName=" + this.getChClientName() + ", enClientName=" + this.getEnClientName() + ", foreCcy=" + this.getForeCcy() + ", offerLetterOld=" + this.getOfferLetterOld() + ", teamBankRateOld=" + this.getTeamBankRateOld() + ", offerLetterNew=" + this.getOfferLetterNew() + ", teamBankRateNew=" + this.getTeamBankRateNew() + ", foreAmtOld=" + this.getForeAmtOld() + ", cnyAmtOld=" + this.getCnyAmtOld() + ", foreAmtNew=" + this.getForeAmtNew() + ", cnyAmtNew=" + this.getCnyAmtNew() + ", lossIncomeFlag=" + this.getLossIncomeFlag() + ", lossIncomeAmt=" + this.getLossIncomeAmt() + ", baseAcctNo=" + this.getBaseAcctNo() + ", seqNo=" + this.getSeqNo() + ", lossForeAmt=" + this.getLossForeAmt() + ", lossCnyAmt=" + this.getLossCnyAmt() + ", depAmt=" + this.getDepAmt() + ", restraintSeqNo=" + this.getRestraintSeqNo() + ", waringFlag=" + this.getWaringFlag() + ", thenWaringFlag=" + this.getThenWaringFlag() + ", exType=" + this.getExType() + ", lastBailAmount=" + this.getLastBailAmount() + ")";
      }
   }
}
