package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400100222In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400100222In.Body body;

   public Core1400100222In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400100222In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400100222In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100222In)) {
         return false;
      } else {
         Core1400100222In other = (Core1400100222In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100222In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "账户内部键值",
         notNull = false,
         length = "15",
         remark = "账户内部键值"
      )
      private Long internalKey;
      @V(
         desc = "预约起始日期",
         notNull = false,
         remark = "预约起始日期"
      )
      private String precontractStartDate;
      @V(
         desc = "预约终止日期",
         notNull = false,
         remark = "预约终止日期"
      )
      private String precontractEndDate;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public Long getInternalKey() {
         return this.internalKey;
      }

      public String getPrecontractStartDate() {
         return this.precontractStartDate;
      }

      public String getPrecontractEndDate() {
         return this.precontractEndDate;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setInternalKey(Long internalKey) {
         this.internalKey = internalKey;
      }

      public void setPrecontractStartDate(String precontractStartDate) {
         this.precontractStartDate = precontractStartDate;
      }

      public void setPrecontractEndDate(String precontractEndDate) {
         this.precontractEndDate = precontractEndDate;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100222In.Body)) {
            return false;
         } else {
            Core1400100222In.Body other = (Core1400100222In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label59: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label59;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label59;
                  }

                  return false;
               }

               Object this$internalKey = this.getInternalKey();
               Object other$internalKey = other.getInternalKey();
               if (this$internalKey == null) {
                  if (other$internalKey != null) {
                     return false;
                  }
               } else if (!this$internalKey.equals(other$internalKey)) {
                  return false;
               }

               Object this$precontractStartDate = this.getPrecontractStartDate();
               Object other$precontractStartDate = other.getPrecontractStartDate();
               if (this$precontractStartDate == null) {
                  if (other$precontractStartDate != null) {
                     return false;
                  }
               } else if (!this$precontractStartDate.equals(other$precontractStartDate)) {
                  return false;
               }

               Object this$precontractEndDate = this.getPrecontractEndDate();
               Object other$precontractEndDate = other.getPrecontractEndDate();
               if (this$precontractEndDate == null) {
                  if (other$precontractEndDate != null) {
                     return false;
                  }
               } else if (!this$precontractEndDate.equals(other$precontractEndDate)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100222In.Body;
      }
      public String toString() {
         return "Core1400100222In.Body(baseAcctNo=" + this.getBaseAcctNo() + ", internalKey=" + this.getInternalKey() + ", precontractStartDate=" + this.getPrecontractStartDate() + ", precontractEndDate=" + this.getPrecontractEndDate() + ")";
      }
   }
}
