package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1200090001In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1200090001In.Body body;

   public Core1200090001In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1200090001In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1200090001In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200090001In)) {
         return false;
      } else {
         Core1200090001In other = (Core1200090001In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200090001In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "现金管理签约",
         notNull = false,
         length = "2",
         inDesc = "01-新增,02-修改,03-删除",
         remark = "现金管理签约",
         maxSize = 2
      )
      private String cashControlOperateType;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "账户组子账户账号",
         notNull = false,
         length = "50",
         remark = "账户组子账户账号",
         maxSize = 50
      )
      private String subBaseAcctNo;
      @V(
         desc = "交易日期",
         notNull = false,
         remark = "交易日期"
      )
      private String tranDate;
      @V(
         desc = "状态",
         notNull = false,
         length = "1",
         in = "A,F",
         inDesc = "A-有效,F-无效,O-未过账,P-已过账,N-新增,U-修改,D-删除,C-非活动状态",
         remark = "状态",
         maxSize = 1
      )
      private String status;
      @V(
         desc = "渠道流水号",
         notNull = false,
         length = "50",
         remark = "渠道流水号",
         maxSize = 50
      )
      private String channelSeqNo;
      @V(
         desc = "子流水号",
         notNull = false,
         length = "100",
         remark = "子流水号",
         maxSize = 100
      )
      private String subSeqNo;

      public String getCashControlOperateType() {
         return this.cashControlOperateType;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getSubBaseAcctNo() {
         return this.subBaseAcctNo;
      }

      public String getTranDate() {
         return this.tranDate;
      }

      public String getStatus() {
         return this.status;
      }

      public String getChannelSeqNo() {
         return this.channelSeqNo;
      }

      public String getSubSeqNo() {
         return this.subSeqNo;
      }

      public void setCashControlOperateType(String cashControlOperateType) {
         this.cashControlOperateType = cashControlOperateType;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setSubBaseAcctNo(String subBaseAcctNo) {
         this.subBaseAcctNo = subBaseAcctNo;
      }

      public void setTranDate(String tranDate) {
         this.tranDate = tranDate;
      }

      public void setStatus(String status) {
         this.status = status;
      }

      public void setChannelSeqNo(String channelSeqNo) {
         this.channelSeqNo = channelSeqNo;
      }

      public void setSubSeqNo(String subSeqNo) {
         this.subSeqNo = subSeqNo;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1200090001In.Body)) {
            return false;
         } else {
            Core1200090001In.Body other = (Core1200090001In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label95: {
                  Object this$cashControlOperateType = this.getCashControlOperateType();
                  Object other$cashControlOperateType = other.getCashControlOperateType();
                  if (this$cashControlOperateType == null) {
                     if (other$cashControlOperateType == null) {
                        break label95;
                     }
                  } else if (this$cashControlOperateType.equals(other$cashControlOperateType)) {
                     break label95;
                  }

                  return false;
               }

               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo != null) {
                     return false;
                  }
               } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
                  return false;
               }

               Object this$subBaseAcctNo = this.getSubBaseAcctNo();
               Object other$subBaseAcctNo = other.getSubBaseAcctNo();
               if (this$subBaseAcctNo == null) {
                  if (other$subBaseAcctNo != null) {
                     return false;
                  }
               } else if (!this$subBaseAcctNo.equals(other$subBaseAcctNo)) {
                  return false;
               }

               label74: {
                  Object this$tranDate = this.getTranDate();
                  Object other$tranDate = other.getTranDate();
                  if (this$tranDate == null) {
                     if (other$tranDate == null) {
                        break label74;
                     }
                  } else if (this$tranDate.equals(other$tranDate)) {
                     break label74;
                  }

                  return false;
               }

               label67: {
                  Object this$status = this.getStatus();
                  Object other$status = other.getStatus();
                  if (this$status == null) {
                     if (other$status == null) {
                        break label67;
                     }
                  } else if (this$status.equals(other$status)) {
                     break label67;
                  }

                  return false;
               }

               Object this$channelSeqNo = this.getChannelSeqNo();
               Object other$channelSeqNo = other.getChannelSeqNo();
               if (this$channelSeqNo == null) {
                  if (other$channelSeqNo != null) {
                     return false;
                  }
               } else if (!this$channelSeqNo.equals(other$channelSeqNo)) {
                  return false;
               }

               Object this$subSeqNo = this.getSubSeqNo();
               Object other$subSeqNo = other.getSubSeqNo();
               if (this$subSeqNo == null) {
                  if (other$subSeqNo != null) {
                     return false;
                  }
               } else if (!this$subSeqNo.equals(other$subSeqNo)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1200090001In.Body;
      }
      public String toString() {
         return "Core1200090001In.Body(cashControlOperateType=" + this.getCashControlOperateType() + ", baseAcctNo=" + this.getBaseAcctNo() + ", subBaseAcctNo=" + this.getSubBaseAcctNo() + ", tranDate=" + this.getTranDate() + ", status=" + this.getStatus() + ", channelSeqNo=" + this.getChannelSeqNo() + ", subSeqNo=" + this.getSubSeqNo() + ")";
      }
   }
}
