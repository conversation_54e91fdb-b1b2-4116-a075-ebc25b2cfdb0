package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;

@MessageIn
public class Core1200100221In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1200100221In.Body body;

   public Core1200100221In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1200100221In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1200100221In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200100221In)) {
         return false;
      } else {
         Core1200100221In other = (Core1200100221In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200100221In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "期次代码",
         notNull = false,
         length = "50",
         remark = "期次代码",
         maxSize = 50
      )
      private String stageCode;
      @V(
         desc = "OA申请额度",
         notNull = false,
         length = "17",
         remark = "OA申请额度",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal oaAmt;

      public String getStageCode() {
         return this.stageCode;
      }

      public BigDecimal getOaAmt() {
         return this.oaAmt;
      }

      public void setStageCode(String stageCode) {
         this.stageCode = stageCode;
      }

      public void setOaAmt(BigDecimal oaAmt) {
         this.oaAmt = oaAmt;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1200100221In.Body)) {
            return false;
         } else {
            Core1200100221In.Body other = (Core1200100221In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$stageCode = this.getStageCode();
               Object other$stageCode = other.getStageCode();
               if (this$stageCode == null) {
                  if (other$stageCode != null) {
                     return false;
                  }
               } else if (!this$stageCode.equals(other$stageCode)) {
                  return false;
               }

               Object this$oaAmt = this.getOaAmt();
               Object other$oaAmt = other.getOaAmt();
               if (this$oaAmt == null) {
                  if (other$oaAmt != null) {
                     return false;
                  }
               } else if (!this$oaAmt.equals(other$oaAmt)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1200100221In.Body;
      }
      public String toString() {
         return "Core1200100221In.Body(stageCode=" + this.getStageCode() + ", oaAmt=" + this.getOaAmt() + ")";
      }
   }
}
