package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1220050132In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1220050132In.Body body;

   public Core1220050132In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1220050132In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1220050132In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1220050132In)) {
         return false;
      } else {
         Core1220050132In other = (Core1220050132In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1220050132In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "交易日期",
         notNull = false,
         remark = "交易日期"
      )
      private String tranDate;
      @V(
         desc = "文件路径",
         notNull = false,
         length = "200",
         remark = "文件路径",
         maxSize = 200
      )
      private String filePath;

      public String getTranDate() {
         return this.tranDate;
      }

      public String getFilePath() {
         return this.filePath;
      }

      public void setTranDate(String tranDate) {
         this.tranDate = tranDate;
      }

      public void setFilePath(String filePath) {
         this.filePath = filePath;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1220050132In.Body)) {
            return false;
         } else {
            Core1220050132In.Body other = (Core1220050132In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$tranDate = this.getTranDate();
               Object other$tranDate = other.getTranDate();
               if (this$tranDate == null) {
                  if (other$tranDate != null) {
                     return false;
                  }
               } else if (!this$tranDate.equals(other$tranDate)) {
                  return false;
               }

               Object this$filePath = this.getFilePath();
               Object other$filePath = other.getFilePath();
               if (this$filePath == null) {
                  if (other$filePath != null) {
                     return false;
                  }
               } else if (!this$filePath.equals(other$filePath)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1220050132In.Body;
      }
      public String toString() {
         return "Core1220050132In.Body(tranDate=" + this.getTranDate() + ", filePath=" + this.getFilePath() + ")";
      }
   }
}
