package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000141In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000141Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000141 {
   String URL = "/rb/inq/interest/aggdjinfo";


   @ApiRemark("用于查询账户的负积数调整记录及冲正交易引起的负积数调整")
   @ApiDesc("用于查询账户的负积数调整记录及冲正交易引起的负积数调整,为华兴项目增加")
   @EnsembleElements(
      serviceCode = "Core",
      messageType = "1400",
      messageCode = "0141"
   )
   @BusinessCategory("用于查询账户的负积数调整记录及冲正交易引")
   @FunctionCategory("RB04-计结息")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core14000141Out runService(Core14000141In var1);
}
