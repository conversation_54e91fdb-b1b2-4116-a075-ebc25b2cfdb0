package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000148In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000148Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12000148 {
   String URL = "/rb/nfin/overdraft/card/quota/update";


   @ApiRemark("消费透支卡额度变更接口")
   @ApiDesc("由信贷系统调入，通过上送消费透支卡信息、密码进行校验，密码通过后进行额度变更")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0148"
   )
   Core12000148Out runService(Core12000148In var1);
}
