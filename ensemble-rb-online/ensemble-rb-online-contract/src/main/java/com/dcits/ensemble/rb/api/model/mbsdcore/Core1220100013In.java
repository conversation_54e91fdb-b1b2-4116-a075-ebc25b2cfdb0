package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1220100013In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1220100013In.Body body;

   public Core1220100013In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1220100013In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1220100013In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1220100013In)) {
         return false;
      } else {
         Core1220100013In other = (Core1220100013In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1220100013In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "批次号",
         notNull = false,
         length = "50",
         remark = "批次号",
         maxSize = 50
      )
      private String batchNo;
      @V(
         desc = "账户条数",
         notNull = false,
         length = "5",
         remark = "账户条数"
      )
      private Integer acctCount;
      @V(
         desc = "文件种类",
         notNull = true,
         length = "5",
         remark = "文件种类，1-转久悬，2-转不动",
         maxSize = 5
      )
      private String fileClass;

      public String getBatchNo() {
         return this.batchNo;
      }

      public Integer getAcctCount() {
         return this.acctCount;
      }

      public String getFileClass() {
         return this.fileClass;
      }

      public void setBatchNo(String batchNo) {
         this.batchNo = batchNo;
      }

      public void setAcctCount(Integer acctCount) {
         this.acctCount = acctCount;
      }

      public void setFileClass(String fileClass) {
         this.fileClass = fileClass;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1220100013In.Body)) {
            return false;
         } else {
            Core1220100013In.Body other = (Core1220100013In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label47: {
                  Object this$batchNo = this.getBatchNo();
                  Object other$batchNo = other.getBatchNo();
                  if (this$batchNo == null) {
                     if (other$batchNo == null) {
                        break label47;
                     }
                  } else if (this$batchNo.equals(other$batchNo)) {
                     break label47;
                  }

                  return false;
               }

               Object this$acctCount = this.getAcctCount();
               Object other$acctCount = other.getAcctCount();
               if (this$acctCount == null) {
                  if (other$acctCount != null) {
                     return false;
                  }
               } else if (!this$acctCount.equals(other$acctCount)) {
                  return false;
               }

               Object this$fileClass = this.getFileClass();
               Object other$fileClass = other.getFileClass();
               if (this$fileClass == null) {
                  if (other$fileClass != null) {
                     return false;
                  }
               } else if (!this$fileClass.equals(other$fileClass)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1220100013In.Body;
      }
      public String toString() {
         return "Core1220100013In.Body(batchNo=" + this.getBatchNo() + ", acctCount=" + this.getAcctCount() + ", fileClass=" + this.getFileClass() + ")";
      }
   }
}
