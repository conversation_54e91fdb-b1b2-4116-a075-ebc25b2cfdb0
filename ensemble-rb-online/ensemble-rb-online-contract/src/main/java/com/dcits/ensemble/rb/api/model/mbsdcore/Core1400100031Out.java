package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400100031Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "成功总笔数",
      notNull = false,
      length = "5",
      remark = "成功总笔数"
   )
   private Integer succCount;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100031Out.AcctArray> acctArray;

   public Integer getSuccCount() {
      return this.succCount;
   }

   public List<Core1400100031Out.AcctArray> getAcctArray() {
      return this.acctArray;
   }

   public void setSuccCount(Integer succCount) {
      this.succCount = succCount;
   }

   public void setAcctArray(List<Core1400100031Out.AcctArray> acctArray) {
      this.acctArray = acctArray;
   }

   public String toString() {
      return "Core1400100031Out(succCount=" + this.getSuccCount() + ", acctArray=" + this.getAcctArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100031Out)) {
         return false;
      } else {
         Core1400100031Out other = (Core1400100031Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$succCount = this.getSuccCount();
            Object other$succCount = other.getSuccCount();
            if (this$succCount == null) {
               if (other$succCount != null) {
                  return false;
               }
            } else if (!this$succCount.equals(other$succCount)) {
               return false;
            }

            Object this$acctArray = this.getAcctArray();
            Object other$acctArray = other.getAcctArray();
            if (this$acctArray == null) {
               if (other$acctArray != null) {
                  return false;
               }
            } else if (!this$acctArray.equals(other$acctArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100031Out;
   }
   public static class AcctArray {
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "账户类别",
         notNull = false,
         length = "1",
         remark = "账户类别，用于区分账户类别（一二三类户），满足人行对于电子账户的管理办法",
         maxSize = 1
      )
      private String acctClass;
      @V(
         desc = "账户开户日期",
         notNull = false,
         remark = "账户开户日期"
      )
      private String acctOpenDate;
      @V(
         desc = "签约金额",
         notNull = false,
         length = "17",
         remark = "签约金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal signAmt;
      @V(
         desc = "账户余额",
         notNull = false,
         length = "17",
         remark = "账户余额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal acctBalance;
      @V(
         desc = "累积违约支取金额合计",
         notNull = false,
         length = "17",
         remark = "累积违约支取金额合计。约定转存类账户协议查询使用",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal totalPastFadAmt;
      @V(
         desc = "开户渠道",
         notNull = false,
         length = "2",
         remark = "账户开户渠道  账户上报信息",
         maxSize = 2
      )
      private String accountOpenChannel;
      @V(
         desc = "行内利率",
         notNull = false,
         length = "15",
         remark = "在人行基准利率调整后对客发布的行内利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal actualRate;
      @V(
         desc = "所属机构号",
         notNull = false,
         length = "50",
         remark = "所属机构号",
         maxSize = 50
      )
      private String branch;
      @V(
         desc = "销户日期",
         notNull = false,
         remark = "账户销户日期"
      )
      private String acctCloseDate;
      @V(
         desc = "执行利率",
         notNull = false,
         length = "15",
         remark = "执行利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal realRate;
      @V(
         desc = "账户利息",
         notNull = false,
         length = "17",
         remark = "账户利息",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal acctInt;
      @V(
         desc = "产品起息日",
         notNull = false,
         length = "10",
         remark = "产品起息日",
         maxSize = 10
      )
      private String prodBeginDate;
      @V(
         desc = "结息日期",
         notNull = false,
         remark = "结息日期"
      )
      private String captDate;
      @V(
         desc = "到期日",
         notNull = false,
         remark = "到期日"
      )
      private String matureDate;
      @V(
         desc = "存期期限",
         notNull = false,
         length = "5",
         remark = "期限",
         maxSize = 5
      )
      private String term;
      @V(
         desc = "比例",
         notNull = false,
         length = "5",
         remark = "比例",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal percent;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "负债账户",
         notNull = false,
         length = "30",
         remark = "负债账户",
         maxSize = 30
      )
      private String liaAcctNo;
      @V(
         desc = "止付到期日",
         notNull = false,
         length = "30",
         remark = "止付到期日",
         maxSize = 30
      )
      private String resDueDate;
      @V(
         desc = "协议状态",
         notNull = false,
         length = "2",
         remark = "普通协议使用，可应用于大部分场景",
         maxSize = 2
      )
      private String agreementStatus;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getAcctClass() {
         return this.acctClass;
      }

      public String getAcctOpenDate() {
         return this.acctOpenDate;
      }

      public BigDecimal getSignAmt() {
         return this.signAmt;
      }

      public BigDecimal getAcctBalance() {
         return this.acctBalance;
      }

      public BigDecimal getTotalPastFadAmt() {
         return this.totalPastFadAmt;
      }

      public String getAccountOpenChannel() {
         return this.accountOpenChannel;
      }

      public BigDecimal getActualRate() {
         return this.actualRate;
      }

      public String getBranch() {
         return this.branch;
      }

      public String getAcctCloseDate() {
         return this.acctCloseDate;
      }

      public BigDecimal getRealRate() {
         return this.realRate;
      }

      public BigDecimal getAcctInt() {
         return this.acctInt;
      }

      public String getProdBeginDate() {
         return this.prodBeginDate;
      }

      public String getCaptDate() {
         return this.captDate;
      }

      public String getMatureDate() {
         return this.matureDate;
      }

      public String getTerm() {
         return this.term;
      }

      public BigDecimal getPercent() {
         return this.percent;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public String getLiaAcctNo() {
         return this.liaAcctNo;
      }

      public String getResDueDate() {
         return this.resDueDate;
      }

      public String getAgreementStatus() {
         return this.agreementStatus;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setAcctClass(String acctClass) {
         this.acctClass = acctClass;
      }

      public void setAcctOpenDate(String acctOpenDate) {
         this.acctOpenDate = acctOpenDate;
      }

      public void setSignAmt(BigDecimal signAmt) {
         this.signAmt = signAmt;
      }

      public void setAcctBalance(BigDecimal acctBalance) {
         this.acctBalance = acctBalance;
      }

      public void setTotalPastFadAmt(BigDecimal totalPastFadAmt) {
         this.totalPastFadAmt = totalPastFadAmt;
      }

      public void setAccountOpenChannel(String accountOpenChannel) {
         this.accountOpenChannel = accountOpenChannel;
      }

      public void setActualRate(BigDecimal actualRate) {
         this.actualRate = actualRate;
      }

      public void setBranch(String branch) {
         this.branch = branch;
      }

      public void setAcctCloseDate(String acctCloseDate) {
         this.acctCloseDate = acctCloseDate;
      }

      public void setRealRate(BigDecimal realRate) {
         this.realRate = realRate;
      }

      public void setAcctInt(BigDecimal acctInt) {
         this.acctInt = acctInt;
      }

      public void setProdBeginDate(String prodBeginDate) {
         this.prodBeginDate = prodBeginDate;
      }

      public void setCaptDate(String captDate) {
         this.captDate = captDate;
      }

      public void setMatureDate(String matureDate) {
         this.matureDate = matureDate;
      }

      public void setTerm(String term) {
         this.term = term;
      }

      public void setPercent(BigDecimal percent) {
         this.percent = percent;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setLiaAcctNo(String liaAcctNo) {
         this.liaAcctNo = liaAcctNo;
      }

      public void setResDueDate(String resDueDate) {
         this.resDueDate = resDueDate;
      }

      public void setAgreementStatus(String agreementStatus) {
         this.agreementStatus = agreementStatus;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100031Out.AcctArray)) {
            return false;
         } else {
            Core1400100031Out.AcctArray other = (Core1400100031Out.AcctArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label299: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label299;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label299;
                  }

                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               Object this$acctCcy = this.getAcctCcy();
               Object other$acctCcy = other.getAcctCcy();
               if (this$acctCcy == null) {
                  if (other$acctCcy != null) {
                     return false;
                  }
               } else if (!this$acctCcy.equals(other$acctCcy)) {
                  return false;
               }

               label278: {
                  Object this$acctSeqNo = this.getAcctSeqNo();
                  Object other$acctSeqNo = other.getAcctSeqNo();
                  if (this$acctSeqNo == null) {
                     if (other$acctSeqNo == null) {
                        break label278;
                     }
                  } else if (this$acctSeqNo.equals(other$acctSeqNo)) {
                     break label278;
                  }

                  return false;
               }

               label271: {
                  Object this$acctClass = this.getAcctClass();
                  Object other$acctClass = other.getAcctClass();
                  if (this$acctClass == null) {
                     if (other$acctClass == null) {
                        break label271;
                     }
                  } else if (this$acctClass.equals(other$acctClass)) {
                     break label271;
                  }

                  return false;
               }

               label264: {
                  Object this$acctOpenDate = this.getAcctOpenDate();
                  Object other$acctOpenDate = other.getAcctOpenDate();
                  if (this$acctOpenDate == null) {
                     if (other$acctOpenDate == null) {
                        break label264;
                     }
                  } else if (this$acctOpenDate.equals(other$acctOpenDate)) {
                     break label264;
                  }

                  return false;
               }

               Object this$signAmt = this.getSignAmt();
               Object other$signAmt = other.getSignAmt();
               if (this$signAmt == null) {
                  if (other$signAmt != null) {
                     return false;
                  }
               } else if (!this$signAmt.equals(other$signAmt)) {
                  return false;
               }

               label250: {
                  Object this$acctBalance = this.getAcctBalance();
                  Object other$acctBalance = other.getAcctBalance();
                  if (this$acctBalance == null) {
                     if (other$acctBalance == null) {
                        break label250;
                     }
                  } else if (this$acctBalance.equals(other$acctBalance)) {
                     break label250;
                  }

                  return false;
               }

               Object this$totalPastFadAmt = this.getTotalPastFadAmt();
               Object other$totalPastFadAmt = other.getTotalPastFadAmt();
               if (this$totalPastFadAmt == null) {
                  if (other$totalPastFadAmt != null) {
                     return false;
                  }
               } else if (!this$totalPastFadAmt.equals(other$totalPastFadAmt)) {
                  return false;
               }

               label236: {
                  Object this$accountOpenChannel = this.getAccountOpenChannel();
                  Object other$accountOpenChannel = other.getAccountOpenChannel();
                  if (this$accountOpenChannel == null) {
                     if (other$accountOpenChannel == null) {
                        break label236;
                     }
                  } else if (this$accountOpenChannel.equals(other$accountOpenChannel)) {
                     break label236;
                  }

                  return false;
               }

               Object this$actualRate = this.getActualRate();
               Object other$actualRate = other.getActualRate();
               if (this$actualRate == null) {
                  if (other$actualRate != null) {
                     return false;
                  }
               } else if (!this$actualRate.equals(other$actualRate)) {
                  return false;
               }

               Object this$branch = this.getBranch();
               Object other$branch = other.getBranch();
               if (this$branch == null) {
                  if (other$branch != null) {
                     return false;
                  }
               } else if (!this$branch.equals(other$branch)) {
                  return false;
               }

               label215: {
                  Object this$acctCloseDate = this.getAcctCloseDate();
                  Object other$acctCloseDate = other.getAcctCloseDate();
                  if (this$acctCloseDate == null) {
                     if (other$acctCloseDate == null) {
                        break label215;
                     }
                  } else if (this$acctCloseDate.equals(other$acctCloseDate)) {
                     break label215;
                  }

                  return false;
               }

               label208: {
                  Object this$realRate = this.getRealRate();
                  Object other$realRate = other.getRealRate();
                  if (this$realRate == null) {
                     if (other$realRate == null) {
                        break label208;
                     }
                  } else if (this$realRate.equals(other$realRate)) {
                     break label208;
                  }

                  return false;
               }

               Object this$acctInt = this.getAcctInt();
               Object other$acctInt = other.getAcctInt();
               if (this$acctInt == null) {
                  if (other$acctInt != null) {
                     return false;
                  }
               } else if (!this$acctInt.equals(other$acctInt)) {
                  return false;
               }

               Object this$prodBeginDate = this.getProdBeginDate();
               Object other$prodBeginDate = other.getProdBeginDate();
               if (this$prodBeginDate == null) {
                  if (other$prodBeginDate != null) {
                     return false;
                  }
               } else if (!this$prodBeginDate.equals(other$prodBeginDate)) {
                  return false;
               }

               label187: {
                  Object this$captDate = this.getCaptDate();
                  Object other$captDate = other.getCaptDate();
                  if (this$captDate == null) {
                     if (other$captDate == null) {
                        break label187;
                     }
                  } else if (this$captDate.equals(other$captDate)) {
                     break label187;
                  }

                  return false;
               }

               Object this$matureDate = this.getMatureDate();
               Object other$matureDate = other.getMatureDate();
               if (this$matureDate == null) {
                  if (other$matureDate != null) {
                     return false;
                  }
               } else if (!this$matureDate.equals(other$matureDate)) {
                  return false;
               }

               Object this$term = this.getTerm();
               Object other$term = other.getTerm();
               if (this$term == null) {
                  if (other$term != null) {
                     return false;
                  }
               } else if (!this$term.equals(other$term)) {
                  return false;
               }

               label166: {
                  Object this$percent = this.getPercent();
                  Object other$percent = other.getPercent();
                  if (this$percent == null) {
                     if (other$percent == null) {
                        break label166;
                     }
                  } else if (this$percent.equals(other$percent)) {
                     break label166;
                  }

                  return false;
               }

               label159: {
                  Object this$clientNo = this.getClientNo();
                  Object other$clientNo = other.getClientNo();
                  if (this$clientNo == null) {
                     if (other$clientNo == null) {
                        break label159;
                     }
                  } else if (this$clientNo.equals(other$clientNo)) {
                     break label159;
                  }

                  return false;
               }

               label152: {
                  Object this$liaAcctNo = this.getLiaAcctNo();
                  Object other$liaAcctNo = other.getLiaAcctNo();
                  if (this$liaAcctNo == null) {
                     if (other$liaAcctNo == null) {
                        break label152;
                     }
                  } else if (this$liaAcctNo.equals(other$liaAcctNo)) {
                     break label152;
                  }

                  return false;
               }

               Object this$resDueDate = this.getResDueDate();
               Object other$resDueDate = other.getResDueDate();
               if (this$resDueDate == null) {
                  if (other$resDueDate != null) {
                     return false;
                  }
               } else if (!this$resDueDate.equals(other$resDueDate)) {
                  return false;
               }

               Object this$agreementStatus = this.getAgreementStatus();
               Object other$agreementStatus = other.getAgreementStatus();
               if (this$agreementStatus == null) {
                  if (other$agreementStatus != null) {
                     return false;
                  }
               } else if (!this$agreementStatus.equals(other$agreementStatus)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100031Out.AcctArray;
      }
      public String toString() {
         return "Core1400100031Out.AcctArray(baseAcctNo=" + this.getBaseAcctNo() + ", prodType=" + this.getProdType() + ", acctCcy=" + this.getAcctCcy() + ", acctSeqNo=" + this.getAcctSeqNo() + ", acctClass=" + this.getAcctClass() + ", acctOpenDate=" + this.getAcctOpenDate() + ", signAmt=" + this.getSignAmt() + ", acctBalance=" + this.getAcctBalance() + ", totalPastFadAmt=" + this.getTotalPastFadAmt() + ", accountOpenChannel=" + this.getAccountOpenChannel() + ", actualRate=" + this.getActualRate() + ", branch=" + this.getBranch() + ", acctCloseDate=" + this.getAcctCloseDate() + ", realRate=" + this.getRealRate() + ", acctInt=" + this.getAcctInt() + ", prodBeginDate=" + this.getProdBeginDate() + ", captDate=" + this.getCaptDate() + ", matureDate=" + this.getMatureDate() + ", term=" + this.getTerm() + ", percent=" + this.getPercent() + ", clientNo=" + this.getClientNo() + ", liaAcctNo=" + this.getLiaAcctNo() + ", resDueDate=" + this.getResDueDate() + ", agreementStatus=" + this.getAgreementStatus() + ")";
      }
   }
}
