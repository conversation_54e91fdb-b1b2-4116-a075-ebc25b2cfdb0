package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10000114In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10000114Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore10000114 {
   String URL = "/rb/fin/interest/adjcycledint";


   @ApiRemark("该功能用于对账户往期的积数进行调整")
   @EnsembleElements(
      serviceCode = "Core",
      messageType = "1000",
      messageCode = "0114"
   )
   @BusinessCategory("用于对账户往期的积数进行调整")
   @FunctionCategory("RB04-计结息")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core10000114Out runService(Core10000114In var1);
}
