package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000206In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000206Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12000206 {
   String URL = "/rb/nfin/card/lock";


   @ApiRemark("标准优化")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0206"
   )
   @BusinessCategory("1200-非金融")
   @FunctionCategory("RB05-协议管理")
   @ApiUseStatus("PRODUCT-产品")
   Core12000206Out runService(Core12000206In var1);
}
