package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12006210In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12006210Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12006210 {
   String URL = "/rb/fin/acct/doss/notice";


   @ApiRemark("久悬未取款通知登记维护")
   @ApiDesc("该接口实现久悬未取款通知登记表进行修改维护，除账户信息外，通知日期、通知方式、联系人、联系电话、联系地址、久悬通知状态处理、备注均由前端上送。")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "6210"
   )
   @FunctionCategory("RB02-账户管理")
   @ApiUseStatus("PRODUCT-产品")
   Core12006210Out runService(Core12006210In var1);
}
