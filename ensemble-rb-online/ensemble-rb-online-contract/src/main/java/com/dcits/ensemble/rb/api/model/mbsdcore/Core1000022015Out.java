package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;

@MessageOut
public class Core1000022015Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "交易参考号",
      notNull = false,
      length = "50",
      remark = "交易参考号",
      maxSize = 50
   )
   private String reference;
   @V(
      desc = "交易金额",
      notNull = false,
      length = "17",
      remark = "交易金额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal tranAmt;

   public String getReference() {
      return this.reference;
   }

   public BigDecimal getTranAmt() {
      return this.tranAmt;
   }

   public void setReference(String reference) {
      this.reference = reference;
   }

   public void setTranAmt(BigDecimal tranAmt) {
      this.tranAmt = tranAmt;
   }

   public String toString() {
      return "Core1000022015Out(reference=" + this.getReference() + ", tranAmt=" + this.getTranAmt() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1000022015Out)) {
         return false;
      } else {
         Core1000022015Out other = (Core1000022015Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$reference = this.getReference();
            Object other$reference = other.getReference();
            if (this$reference == null) {
               if (other$reference != null) {
                  return false;
               }
            } else if (!this$reference.equals(other$reference)) {
               return false;
            }

            Object this$tranAmt = this.getTranAmt();
            Object other$tranAmt = other.getTranAmt();
            if (this$tranAmt == null) {
               if (other$tranAmt != null) {
                  return false;
               }
            } else if (!this$tranAmt.equals(other$tranAmt)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1000022015Out;
   }
}
