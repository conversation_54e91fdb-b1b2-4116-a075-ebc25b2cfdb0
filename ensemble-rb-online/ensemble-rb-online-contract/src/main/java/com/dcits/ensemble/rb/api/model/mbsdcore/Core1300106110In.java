package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1300106110In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1300106110In.Body body;

   public Core1300106110In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1300106110In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1300106110In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1300106110In)) {
         return false;
      } else {
         Core1300106110In other = (Core1300106110In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1300106110In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "卡产品代码",
         notNull = false,
         length = "20",
         remark = "卡产品代码",
         maxSize = 20
      )
      private String mainProdType;
      @V(
         desc = "序列号",
         notNull = false,
         length = "5",
         remark = "序列号",
         maxSize = 5
      )
      private String mainAcctSeqNo;
      @V(
         desc = "主币种",
         notNull = false,
         length = "3",
         remark = "主币种",
         maxSize = 3
      )
      private String mainCcy;
      @V(
         desc = "销户方式",
         notNull = false,
         length = "1",
         inDesc = "0-正常销户,1-损坏销户,2-挂失销户,5-冒名销户",
         remark = "销户方式",
         maxSize = 1
      )
      private String closeType;
      @V(
         desc = "注销原因",
         notNull = false,
         length = "200",
         remark = "注销原因:客户注销时，需要登记客户注销原因",
         maxSize = 200
      )
      private String closeReason;
      @V(
         desc = "审批单号",
         notNull = false,
         length = "50",
         remark = "审批单号",
         maxSize = 50
      )
      private String approvalNo;
      @V(
         desc = "摘要",
         notNull = false,
         length = "500",
         remark = "开户时的账号用途，销户时的销户原因",
         maxSize = 500
      )
      private String narrative;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getMainProdType() {
         return this.mainProdType;
      }

      public String getMainAcctSeqNo() {
         return this.mainAcctSeqNo;
      }

      public String getMainCcy() {
         return this.mainCcy;
      }

      public String getCloseType() {
         return this.closeType;
      }

      public String getCloseReason() {
         return this.closeReason;
      }

      public String getApprovalNo() {
         return this.approvalNo;
      }

      public String getNarrative() {
         return this.narrative;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setMainProdType(String mainProdType) {
         this.mainProdType = mainProdType;
      }

      public void setMainAcctSeqNo(String mainAcctSeqNo) {
         this.mainAcctSeqNo = mainAcctSeqNo;
      }

      public void setMainCcy(String mainCcy) {
         this.mainCcy = mainCcy;
      }

      public void setCloseType(String closeType) {
         this.closeType = closeType;
      }

      public void setCloseReason(String closeReason) {
         this.closeReason = closeReason;
      }

      public void setApprovalNo(String approvalNo) {
         this.approvalNo = approvalNo;
      }

      public void setNarrative(String narrative) {
         this.narrative = narrative;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1300106110In.Body)) {
            return false;
         } else {
            Core1300106110In.Body other = (Core1300106110In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label107: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label107;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label107;
                  }

                  return false;
               }

               Object this$mainProdType = this.getMainProdType();
               Object other$mainProdType = other.getMainProdType();
               if (this$mainProdType == null) {
                  if (other$mainProdType != null) {
                     return false;
                  }
               } else if (!this$mainProdType.equals(other$mainProdType)) {
                  return false;
               }

               Object this$mainAcctSeqNo = this.getMainAcctSeqNo();
               Object other$mainAcctSeqNo = other.getMainAcctSeqNo();
               if (this$mainAcctSeqNo == null) {
                  if (other$mainAcctSeqNo != null) {
                     return false;
                  }
               } else if (!this$mainAcctSeqNo.equals(other$mainAcctSeqNo)) {
                  return false;
               }

               label86: {
                  Object this$mainCcy = this.getMainCcy();
                  Object other$mainCcy = other.getMainCcy();
                  if (this$mainCcy == null) {
                     if (other$mainCcy == null) {
                        break label86;
                     }
                  } else if (this$mainCcy.equals(other$mainCcy)) {
                     break label86;
                  }

                  return false;
               }

               label79: {
                  Object this$closeType = this.getCloseType();
                  Object other$closeType = other.getCloseType();
                  if (this$closeType == null) {
                     if (other$closeType == null) {
                        break label79;
                     }
                  } else if (this$closeType.equals(other$closeType)) {
                     break label79;
                  }

                  return false;
               }

               label72: {
                  Object this$closeReason = this.getCloseReason();
                  Object other$closeReason = other.getCloseReason();
                  if (this$closeReason == null) {
                     if (other$closeReason == null) {
                        break label72;
                     }
                  } else if (this$closeReason.equals(other$closeReason)) {
                     break label72;
                  }

                  return false;
               }

               Object this$approvalNo = this.getApprovalNo();
               Object other$approvalNo = other.getApprovalNo();
               if (this$approvalNo == null) {
                  if (other$approvalNo != null) {
                     return false;
                  }
               } else if (!this$approvalNo.equals(other$approvalNo)) {
                  return false;
               }

               Object this$narrative = this.getNarrative();
               Object other$narrative = other.getNarrative();
               if (this$narrative == null) {
                  if (other$narrative != null) {
                     return false;
                  }
               } else if (!this$narrative.equals(other$narrative)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1300106110In.Body;
      }
      public String toString() {
         return "Core1300106110In.Body(baseAcctNo=" + this.getBaseAcctNo() + ", mainProdType=" + this.getMainProdType() + ", mainAcctSeqNo=" + this.getMainAcctSeqNo() + ", mainCcy=" + this.getMainCcy() + ", closeType=" + this.getCloseType() + ", closeReason=" + this.getCloseReason() + ", approvalNo=" + this.getApprovalNo() + ", narrative=" + this.getNarrative() + ")";
      }
   }
}
