package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000140In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000140Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000140 {
   String URL = "/rb/inq/cent/deal";


   @ApiRemark("分位金额处理查询")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0140"
   )
   @BusinessCategory("分位金额处理查询")
   @FunctionCategory("RB08-特殊业务")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core14000140Out runService(Core14000140In var1);
}
