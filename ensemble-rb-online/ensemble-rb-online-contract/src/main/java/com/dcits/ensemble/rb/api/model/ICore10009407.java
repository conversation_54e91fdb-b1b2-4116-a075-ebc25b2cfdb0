package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10009407In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10009407Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore10009407 {
   String URL = "/rb/fin/inner/amend";


   @ApiRemark("华兴项目新增转账时转出方或转入方发生错误的更正处理")
   @ApiDesc("华兴项目内部账户转出方或转入方发生串户错误的更正处理")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "9407"
   )
   @BusinessCategory("转账时转出方或转入方发生错误")
   @FunctionCategory("RB15-内部账")
   @ConsumeSys("TLE/137")
   Core10009407Out runService(Core10009407In var1);
}
