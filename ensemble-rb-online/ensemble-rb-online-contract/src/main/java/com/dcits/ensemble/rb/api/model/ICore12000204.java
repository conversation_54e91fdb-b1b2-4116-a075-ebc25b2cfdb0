package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000204In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000204Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12000204 {
   String URL = "/rb/nfin/term/close/open";


   @ApiDesc("整存整取定期产品到期后，支持直接使用原账户资金销户并新开，新开的定期产品本金可大于或小于原定期本息和，通过同一客户下的结算账户作为资金差额的转入/转出账户。新开的整存整取定期产品支持自动转存")
   @EnsembleElements(
      serviceCode = "Core",
      messageType = "1200",
      messageCode = "0204"
   )
   @BusinessCategory("1200")
   @FunctionCategory("RB02-账户管理")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core12000204Out runService(Core12000204In var1);
}
