package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;

@MessageOut
public class Core1400033404Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "办理人姓名",
      notNull = false,
      length = "200",
      remark = "办理人姓名",
      maxSize = 200
   )
   private String agentName;
   @V(
      desc = "发行日期",
      notNull = false,
      remark = "发行日期"
   )
   private String issueDate;
   @V(
      desc = "所属机构号",
      notNull = false,
      length = "50",
      remark = "机构代码",
      maxSize = 50
   )
   private String branch;
   @V(
      desc = "签发行行号",
      notNull = false,
      length = "20",
      remark = "签发行行号",
      maxSize = 20
   )
   private String issueBankNo;
   @V(
      desc = "签发行行名",
      notNull = false,
      length = "50",
      remark = "签发行行名",
      maxSize = 50
   )
   private String issueBankName;
   @V(
      desc = "挂失编号",
      notNull = false,
      length = "50",
      remark = "挂失编号",
      maxSize = 50
   )
   private String lostNo;
   @V(
      desc = "挂失申请书编号",
      notNull = false,
      length = "50",
      remark = "挂失申请书编号",
      maxSize = 50
   )
   private String lossNo;
   @V(
      desc = "原业务流水号",
      notNull = false,
      length = "50",
      remark = "原签发流水号",
      maxSize = 50
   )
   private String origSerialNo;
   @V(
      desc = "票面金额",
      notNull = false,
      length = "17",
      remark = "票面金额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal billAmt;
   @V(
      desc = "挂失人证件类型",
      notNull = false,
      length = "3",
      remark = "挂失人证件类型",
      maxSize = 3
   )
   private String lostDocumentType;
   @V(
      desc = "解挂人证件类型",
      notNull = false,
      length = "3",
      remark = "解挂人证件类型",
      maxSize = 3
   )
   private String unlostDocumentType;
   @V(
      desc = "申请人产品类型",
      notNull = false,
      length = "20",
      remark = "申请人产品类型",
      maxSize = 20
   )
   private String applyerProdeType;
   @V(
      desc = "代理标志",
      notNull = false,
      length = "1",
      in = "Y,N",
      remark = "代理标志",
      maxSize = 1
   )
   private String agentFlag;
   @V(
      desc = "生效标识",
      notNull = false,
      length = "1",
      in = "Y,N",
      remark = "生效标识",
      maxSize = 1
   )
   private String tranValidFlag;
   @V(
      desc = "票据类型",
      notNull = false,
      length = "5",
      in = "P,E",
      remark = "票据类型",
      maxSize = 5
   )
   private String billType;
   @V(
      desc = "签发标识",
      notNull = false,
      length = "1",
      in = "0,1",
      remark = "签发标识",
      maxSize = 1
   )
   private String issueFlag;
   @V(
      desc = "收款人账户产品类型",
      notNull = false,
      length = "20",
      remark = "收款人账户产品类型",
      maxSize = 20
   )
   private String payeeProdType;
   @V(
      desc = "申请人账号",
      notNull = false,
      length = "50",
      remark = "申请人账号",
      maxSize = 50
   )
   private String applyerBaseAcctNo;
   @V(
      desc = "收款人账户",
      notNull = false,
      length = "50",
      remark = "收款人账户",
      maxSize = 50
   )
   private String payeeAcctNo;
   @V(
      desc = "申请人账户币种",
      notNull = false,
      length = "3",
      remark = "申请人账户币种",
      maxSize = 3
   )
   private String applyerAcctCcy;
   @V(
      desc = "收款人账户币种",
      notNull = false,
      length = "3",
      remark = "收款人账户币种",
      maxSize = 3
   )
   private String payeeAcctCcy;
   @V(
      desc = "申请人账户序列号",
      notNull = false,
      length = "5",
      remark = "申请人账户序列号",
      maxSize = 5
   )
   private String applyerAcctSeqNo;
   @V(
      desc = "收款人账户序列号",
      notNull = false,
      length = "5",
      remark = "收款人账户序列号",
      maxSize = 5
   )
   private String payeeAcctSeqNo;
   @V(
      desc = "申请人名称",
      notNull = false,
      length = "200",
      remark = "申请人名称",
      maxSize = 200
   )
   private String applyerAcctName;
   @V(
      desc = "收款人名称",
      notNull = false,
      length = "200",
      remark = "收款人名称",
      maxSize = 200
   )
   private String payeeAcctName;
   @V(
      desc = "挂失人证件号码",
      notNull = false,
      length = "50",
      remark = "挂失人证件号码",
      maxSize = 50
   )
   private String lostDocumentId;
   @V(
      desc = "解挂人姓名",
      notNull = false,
      length = "200",
      remark = "解挂人姓名",
      maxSize = 200
   )
   private String unlostName;
   @V(
      desc = "解挂人证件号码",
      notNull = false,
      length = "50",
      remark = "解挂人证件号码",
      maxSize = 50
   )
   private String unlostDocumentId;
   @V(
      desc = "挂失人名称",
      notNull = false,
      length = "200",
      remark = "挂失人名称",
      maxSize = 200
   )
   private String lostName;
   @V(
      desc = "挂失人联系电话",
      notNull = false,
      length = "20",
      remark = "挂失人联系电话",
      maxSize = 20
   )
   private String lostTelNo;
   @V(
      desc = "本票丧失地点",
      notNull = false,
      length = "500",
      remark = "本票丧失地点",
      maxSize = 500
   )
   private String billLostAddr;
   @V(
      desc = "营业场所住所",
      notNull = false,
      length = "500",
      remark = "营业场所住所",
      maxSize = 500
   )
   private String busiPlace;
   @V(
      desc = "挂失原因",
      notNull = false,
      length = "200",
      remark = "挂失原因",
      maxSize = 200
   )
   private String lostReason;
   @V(
      desc = "挂失状态",
      notNull = false,
      length = "1",
      in = "0,1,2",
      remark = "挂失状态",
      maxSize = 1
   )
   private String lostStatus;
   @V(
      desc = "解挂人联系电话",
      notNull = false,
      length = "20",
      remark = "解挂人联系电话",
      maxSize = 20
   )
   private String unlostTelNo;
   @V(
      desc = "解挂原因",
      notNull = false,
      length = "200",
      remark = "解挂原因",
      maxSize = 200
   )
   private String unlostReason;
   @V(
      desc = "票据号码",
      notNull = false,
      length = "50",
      remark = "票据号码",
      maxSize = 50
   )
   private String billNo;
   @V(
      desc = "票据密押",
      notNull = false,
      length = "20",
      remark = "票据密押",
      maxSize = 20
   )
   private String billPswd;
   @V(
      desc = "挂失柜员",
      notNull = false,
      length = "30",
      remark = "挂失柜员",
      maxSize = 30
   )
   private String lostUserId;
   @V(
      desc = "解挂柜员",
      notNull = false,
      length = "30",
      remark = "解挂柜员",
      maxSize = 30
   )
   private String unlostUserId;
   @V(
      desc = "本票丧失时间",
      notNull = false,
      length = "26",
      remark = "本票丧失时间",
      maxSize = 26
   )
   private String billLostTime;
   @V(
      desc = "挂失时间",
      notNull = false,
      length = "26",
      remark = "挂失时间",
      maxSize = 26
   )
   private String lostTime;
   @V(
      desc = "解挂时间",
      notNull = false,
      length = "26",
      remark = "解挂时间",
      maxSize = 26
   )
   private String unlostTime;
   @V(
      desc = "办理人证件类型",
      notNull = false,
      length = "3",
      remark = "办理人证件类型",
      maxSize = 3
   )
   private String agentDocumentType;
   @V(
      desc = "办理人证件号码",
      notNull = false,
      length = "50",
      remark = "办理人证件号码",
      maxSize = 50
   )
   private String agentDocumentId;
   @V(
      desc = "法人",
      notNull = false,
      length = "20",
      remark = "法人",
      maxSize = 20
   )
   private String company;

   public String getAgentName() {
      return this.agentName;
   }

   public String getIssueDate() {
      return this.issueDate;
   }

   public String getBranch() {
      return this.branch;
   }

   public String getIssueBankNo() {
      return this.issueBankNo;
   }

   public String getIssueBankName() {
      return this.issueBankName;
   }

   public String getLostNo() {
      return this.lostNo;
   }

   public String getLossNo() {
      return this.lossNo;
   }

   public String getOrigSerialNo() {
      return this.origSerialNo;
   }

   public BigDecimal getBillAmt() {
      return this.billAmt;
   }

   public String getLostDocumentType() {
      return this.lostDocumentType;
   }

   public String getUnlostDocumentType() {
      return this.unlostDocumentType;
   }

   public String getApplyerProdeType() {
      return this.applyerProdeType;
   }

   public String getAgentFlag() {
      return this.agentFlag;
   }

   public String getTranValidFlag() {
      return this.tranValidFlag;
   }

   public String getBillType() {
      return this.billType;
   }

   public String getIssueFlag() {
      return this.issueFlag;
   }

   public String getPayeeProdType() {
      return this.payeeProdType;
   }

   public String getApplyerBaseAcctNo() {
      return this.applyerBaseAcctNo;
   }

   public String getPayeeAcctNo() {
      return this.payeeAcctNo;
   }

   public String getApplyerAcctCcy() {
      return this.applyerAcctCcy;
   }

   public String getPayeeAcctCcy() {
      return this.payeeAcctCcy;
   }

   public String getApplyerAcctSeqNo() {
      return this.applyerAcctSeqNo;
   }

   public String getPayeeAcctSeqNo() {
      return this.payeeAcctSeqNo;
   }

   public String getApplyerAcctName() {
      return this.applyerAcctName;
   }

   public String getPayeeAcctName() {
      return this.payeeAcctName;
   }

   public String getLostDocumentId() {
      return this.lostDocumentId;
   }

   public String getUnlostName() {
      return this.unlostName;
   }

   public String getUnlostDocumentId() {
      return this.unlostDocumentId;
   }

   public String getLostName() {
      return this.lostName;
   }

   public String getLostTelNo() {
      return this.lostTelNo;
   }

   public String getBillLostAddr() {
      return this.billLostAddr;
   }

   public String getBusiPlace() {
      return this.busiPlace;
   }

   public String getLostReason() {
      return this.lostReason;
   }

   public String getLostStatus() {
      return this.lostStatus;
   }

   public String getUnlostTelNo() {
      return this.unlostTelNo;
   }

   public String getUnlostReason() {
      return this.unlostReason;
   }

   public String getBillNo() {
      return this.billNo;
   }

   public String getBillPswd() {
      return this.billPswd;
   }

   public String getLostUserId() {
      return this.lostUserId;
   }

   public String getUnlostUserId() {
      return this.unlostUserId;
   }

   public String getBillLostTime() {
      return this.billLostTime;
   }

   public String getLostTime() {
      return this.lostTime;
   }

   public String getUnlostTime() {
      return this.unlostTime;
   }

   public String getAgentDocumentType() {
      return this.agentDocumentType;
   }

   public String getAgentDocumentId() {
      return this.agentDocumentId;
   }

   public String getCompany() {
      return this.company;
   }

   public void setAgentName(String agentName) {
      this.agentName = agentName;
   }

   public void setIssueDate(String issueDate) {
      this.issueDate = issueDate;
   }

   public void setBranch(String branch) {
      this.branch = branch;
   }

   public void setIssueBankNo(String issueBankNo) {
      this.issueBankNo = issueBankNo;
   }

   public void setIssueBankName(String issueBankName) {
      this.issueBankName = issueBankName;
   }

   public void setLostNo(String lostNo) {
      this.lostNo = lostNo;
   }

   public void setLossNo(String lossNo) {
      this.lossNo = lossNo;
   }

   public void setOrigSerialNo(String origSerialNo) {
      this.origSerialNo = origSerialNo;
   }

   public void setBillAmt(BigDecimal billAmt) {
      this.billAmt = billAmt;
   }

   public void setLostDocumentType(String lostDocumentType) {
      this.lostDocumentType = lostDocumentType;
   }

   public void setUnlostDocumentType(String unlostDocumentType) {
      this.unlostDocumentType = unlostDocumentType;
   }

   public void setApplyerProdeType(String applyerProdeType) {
      this.applyerProdeType = applyerProdeType;
   }

   public void setAgentFlag(String agentFlag) {
      this.agentFlag = agentFlag;
   }

   public void setTranValidFlag(String tranValidFlag) {
      this.tranValidFlag = tranValidFlag;
   }

   public void setBillType(String billType) {
      this.billType = billType;
   }

   public void setIssueFlag(String issueFlag) {
      this.issueFlag = issueFlag;
   }

   public void setPayeeProdType(String payeeProdType) {
      this.payeeProdType = payeeProdType;
   }

   public void setApplyerBaseAcctNo(String applyerBaseAcctNo) {
      this.applyerBaseAcctNo = applyerBaseAcctNo;
   }

   public void setPayeeAcctNo(String payeeAcctNo) {
      this.payeeAcctNo = payeeAcctNo;
   }

   public void setApplyerAcctCcy(String applyerAcctCcy) {
      this.applyerAcctCcy = applyerAcctCcy;
   }

   public void setPayeeAcctCcy(String payeeAcctCcy) {
      this.payeeAcctCcy = payeeAcctCcy;
   }

   public void setApplyerAcctSeqNo(String applyerAcctSeqNo) {
      this.applyerAcctSeqNo = applyerAcctSeqNo;
   }

   public void setPayeeAcctSeqNo(String payeeAcctSeqNo) {
      this.payeeAcctSeqNo = payeeAcctSeqNo;
   }

   public void setApplyerAcctName(String applyerAcctName) {
      this.applyerAcctName = applyerAcctName;
   }

   public void setPayeeAcctName(String payeeAcctName) {
      this.payeeAcctName = payeeAcctName;
   }

   public void setLostDocumentId(String lostDocumentId) {
      this.lostDocumentId = lostDocumentId;
   }

   public void setUnlostName(String unlostName) {
      this.unlostName = unlostName;
   }

   public void setUnlostDocumentId(String unlostDocumentId) {
      this.unlostDocumentId = unlostDocumentId;
   }

   public void setLostName(String lostName) {
      this.lostName = lostName;
   }

   public void setLostTelNo(String lostTelNo) {
      this.lostTelNo = lostTelNo;
   }

   public void setBillLostAddr(String billLostAddr) {
      this.billLostAddr = billLostAddr;
   }

   public void setBusiPlace(String busiPlace) {
      this.busiPlace = busiPlace;
   }

   public void setLostReason(String lostReason) {
      this.lostReason = lostReason;
   }

   public void setLostStatus(String lostStatus) {
      this.lostStatus = lostStatus;
   }

   public void setUnlostTelNo(String unlostTelNo) {
      this.unlostTelNo = unlostTelNo;
   }

   public void setUnlostReason(String unlostReason) {
      this.unlostReason = unlostReason;
   }

   public void setBillNo(String billNo) {
      this.billNo = billNo;
   }

   public void setBillPswd(String billPswd) {
      this.billPswd = billPswd;
   }

   public void setLostUserId(String lostUserId) {
      this.lostUserId = lostUserId;
   }

   public void setUnlostUserId(String unlostUserId) {
      this.unlostUserId = unlostUserId;
   }

   public void setBillLostTime(String billLostTime) {
      this.billLostTime = billLostTime;
   }

   public void setLostTime(String lostTime) {
      this.lostTime = lostTime;
   }

   public void setUnlostTime(String unlostTime) {
      this.unlostTime = unlostTime;
   }

   public void setAgentDocumentType(String agentDocumentType) {
      this.agentDocumentType = agentDocumentType;
   }

   public void setAgentDocumentId(String agentDocumentId) {
      this.agentDocumentId = agentDocumentId;
   }

   public void setCompany(String company) {
      this.company = company;
   }

   public String toString() {
      return "Core1400033404Out(agentName=" + this.getAgentName() + ", issueDate=" + this.getIssueDate() + ", branch=" + this.getBranch() + ", issueBankNo=" + this.getIssueBankNo() + ", issueBankName=" + this.getIssueBankName() + ", lostNo=" + this.getLostNo() + ", lossNo=" + this.getLossNo() + ", origSerialNo=" + this.getOrigSerialNo() + ", billAmt=" + this.getBillAmt() + ", lostDocumentType=" + this.getLostDocumentType() + ", unlostDocumentType=" + this.getUnlostDocumentType() + ", applyerProdeType=" + this.getApplyerProdeType() + ", agentFlag=" + this.getAgentFlag() + ", tranValidFlag=" + this.getTranValidFlag() + ", billType=" + this.getBillType() + ", issueFlag=" + this.getIssueFlag() + ", payeeProdType=" + this.getPayeeProdType() + ", applyerBaseAcctNo=" + this.getApplyerBaseAcctNo() + ", payeeAcctNo=" + this.getPayeeAcctNo() + ", applyerAcctCcy=" + this.getApplyerAcctCcy() + ", payeeAcctCcy=" + this.getPayeeAcctCcy() + ", applyerAcctSeqNo=" + this.getApplyerAcctSeqNo() + ", payeeAcctSeqNo=" + this.getPayeeAcctSeqNo() + ", applyerAcctName=" + this.getApplyerAcctName() + ", payeeAcctName=" + this.getPayeeAcctName() + ", lostDocumentId=" + this.getLostDocumentId() + ", unlostName=" + this.getUnlostName() + ", unlostDocumentId=" + this.getUnlostDocumentId() + ", lostName=" + this.getLostName() + ", lostTelNo=" + this.getLostTelNo() + ", billLostAddr=" + this.getBillLostAddr() + ", busiPlace=" + this.getBusiPlace() + ", lostReason=" + this.getLostReason() + ", lostStatus=" + this.getLostStatus() + ", unlostTelNo=" + this.getUnlostTelNo() + ", unlostReason=" + this.getUnlostReason() + ", billNo=" + this.getBillNo() + ", billPswd=" + this.getBillPswd() + ", lostUserId=" + this.getLostUserId() + ", unlostUserId=" + this.getUnlostUserId() + ", billLostTime=" + this.getBillLostTime() + ", lostTime=" + this.getLostTime() + ", unlostTime=" + this.getUnlostTime() + ", agentDocumentType=" + this.getAgentDocumentType() + ", agentDocumentId=" + this.getAgentDocumentId() + ", company=" + this.getCompany() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400033404Out)) {
         return false;
      } else {
         Core1400033404Out other = (Core1400033404Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$agentName = this.getAgentName();
            Object other$agentName = other.getAgentName();
            if (this$agentName == null) {
               if (other$agentName != null) {
                  return false;
               }
            } else if (!this$agentName.equals(other$agentName)) {
               return false;
            }

            Object this$issueDate = this.getIssueDate();
            Object other$issueDate = other.getIssueDate();
            if (this$issueDate == null) {
               if (other$issueDate != null) {
                  return false;
               }
            } else if (!this$issueDate.equals(other$issueDate)) {
               return false;
            }

            label551: {
               Object this$branch = this.getBranch();
               Object other$branch = other.getBranch();
               if (this$branch == null) {
                  if (other$branch == null) {
                     break label551;
                  }
               } else if (this$branch.equals(other$branch)) {
                  break label551;
               }

               return false;
            }

            label544: {
               Object this$issueBankNo = this.getIssueBankNo();
               Object other$issueBankNo = other.getIssueBankNo();
               if (this$issueBankNo == null) {
                  if (other$issueBankNo == null) {
                     break label544;
                  }
               } else if (this$issueBankNo.equals(other$issueBankNo)) {
                  break label544;
               }

               return false;
            }

            Object this$issueBankName = this.getIssueBankName();
            Object other$issueBankName = other.getIssueBankName();
            if (this$issueBankName == null) {
               if (other$issueBankName != null) {
                  return false;
               }
            } else if (!this$issueBankName.equals(other$issueBankName)) {
               return false;
            }

            Object this$lostNo = this.getLostNo();
            Object other$lostNo = other.getLostNo();
            if (this$lostNo == null) {
               if (other$lostNo != null) {
                  return false;
               }
            } else if (!this$lostNo.equals(other$lostNo)) {
               return false;
            }

            label523: {
               Object this$lossNo = this.getLossNo();
               Object other$lossNo = other.getLossNo();
               if (this$lossNo == null) {
                  if (other$lossNo == null) {
                     break label523;
                  }
               } else if (this$lossNo.equals(other$lossNo)) {
                  break label523;
               }

               return false;
            }

            Object this$origSerialNo = this.getOrigSerialNo();
            Object other$origSerialNo = other.getOrigSerialNo();
            if (this$origSerialNo == null) {
               if (other$origSerialNo != null) {
                  return false;
               }
            } else if (!this$origSerialNo.equals(other$origSerialNo)) {
               return false;
            }

            Object this$billAmt = this.getBillAmt();
            Object other$billAmt = other.getBillAmt();
            if (this$billAmt == null) {
               if (other$billAmt != null) {
                  return false;
               }
            } else if (!this$billAmt.equals(other$billAmt)) {
               return false;
            }

            label502: {
               Object this$lostDocumentType = this.getLostDocumentType();
               Object other$lostDocumentType = other.getLostDocumentType();
               if (this$lostDocumentType == null) {
                  if (other$lostDocumentType == null) {
                     break label502;
                  }
               } else if (this$lostDocumentType.equals(other$lostDocumentType)) {
                  break label502;
               }

               return false;
            }

            label495: {
               Object this$unlostDocumentType = this.getUnlostDocumentType();
               Object other$unlostDocumentType = other.getUnlostDocumentType();
               if (this$unlostDocumentType == null) {
                  if (other$unlostDocumentType == null) {
                     break label495;
                  }
               } else if (this$unlostDocumentType.equals(other$unlostDocumentType)) {
                  break label495;
               }

               return false;
            }

            label488: {
               Object this$applyerProdeType = this.getApplyerProdeType();
               Object other$applyerProdeType = other.getApplyerProdeType();
               if (this$applyerProdeType == null) {
                  if (other$applyerProdeType == null) {
                     break label488;
                  }
               } else if (this$applyerProdeType.equals(other$applyerProdeType)) {
                  break label488;
               }

               return false;
            }

            Object this$agentFlag = this.getAgentFlag();
            Object other$agentFlag = other.getAgentFlag();
            if (this$agentFlag == null) {
               if (other$agentFlag != null) {
                  return false;
               }
            } else if (!this$agentFlag.equals(other$agentFlag)) {
               return false;
            }

            label474: {
               Object this$tranValidFlag = this.getTranValidFlag();
               Object other$tranValidFlag = other.getTranValidFlag();
               if (this$tranValidFlag == null) {
                  if (other$tranValidFlag == null) {
                     break label474;
                  }
               } else if (this$tranValidFlag.equals(other$tranValidFlag)) {
                  break label474;
               }

               return false;
            }

            Object this$billType = this.getBillType();
            Object other$billType = other.getBillType();
            if (this$billType == null) {
               if (other$billType != null) {
                  return false;
               }
            } else if (!this$billType.equals(other$billType)) {
               return false;
            }

            label460: {
               Object this$issueFlag = this.getIssueFlag();
               Object other$issueFlag = other.getIssueFlag();
               if (this$issueFlag == null) {
                  if (other$issueFlag == null) {
                     break label460;
                  }
               } else if (this$issueFlag.equals(other$issueFlag)) {
                  break label460;
               }

               return false;
            }

            Object this$payeeProdType = this.getPayeeProdType();
            Object other$payeeProdType = other.getPayeeProdType();
            if (this$payeeProdType == null) {
               if (other$payeeProdType != null) {
                  return false;
               }
            } else if (!this$payeeProdType.equals(other$payeeProdType)) {
               return false;
            }

            Object this$applyerBaseAcctNo = this.getApplyerBaseAcctNo();
            Object other$applyerBaseAcctNo = other.getApplyerBaseAcctNo();
            if (this$applyerBaseAcctNo == null) {
               if (other$applyerBaseAcctNo != null) {
                  return false;
               }
            } else if (!this$applyerBaseAcctNo.equals(other$applyerBaseAcctNo)) {
               return false;
            }

            label439: {
               Object this$payeeAcctNo = this.getPayeeAcctNo();
               Object other$payeeAcctNo = other.getPayeeAcctNo();
               if (this$payeeAcctNo == null) {
                  if (other$payeeAcctNo == null) {
                     break label439;
                  }
               } else if (this$payeeAcctNo.equals(other$payeeAcctNo)) {
                  break label439;
               }

               return false;
            }

            label432: {
               Object this$applyerAcctCcy = this.getApplyerAcctCcy();
               Object other$applyerAcctCcy = other.getApplyerAcctCcy();
               if (this$applyerAcctCcy == null) {
                  if (other$applyerAcctCcy == null) {
                     break label432;
                  }
               } else if (this$applyerAcctCcy.equals(other$applyerAcctCcy)) {
                  break label432;
               }

               return false;
            }

            Object this$payeeAcctCcy = this.getPayeeAcctCcy();
            Object other$payeeAcctCcy = other.getPayeeAcctCcy();
            if (this$payeeAcctCcy == null) {
               if (other$payeeAcctCcy != null) {
                  return false;
               }
            } else if (!this$payeeAcctCcy.equals(other$payeeAcctCcy)) {
               return false;
            }

            Object this$applyerAcctSeqNo = this.getApplyerAcctSeqNo();
            Object other$applyerAcctSeqNo = other.getApplyerAcctSeqNo();
            if (this$applyerAcctSeqNo == null) {
               if (other$applyerAcctSeqNo != null) {
                  return false;
               }
            } else if (!this$applyerAcctSeqNo.equals(other$applyerAcctSeqNo)) {
               return false;
            }

            label411: {
               Object this$payeeAcctSeqNo = this.getPayeeAcctSeqNo();
               Object other$payeeAcctSeqNo = other.getPayeeAcctSeqNo();
               if (this$payeeAcctSeqNo == null) {
                  if (other$payeeAcctSeqNo == null) {
                     break label411;
                  }
               } else if (this$payeeAcctSeqNo.equals(other$payeeAcctSeqNo)) {
                  break label411;
               }

               return false;
            }

            Object this$applyerAcctName = this.getApplyerAcctName();
            Object other$applyerAcctName = other.getApplyerAcctName();
            if (this$applyerAcctName == null) {
               if (other$applyerAcctName != null) {
                  return false;
               }
            } else if (!this$applyerAcctName.equals(other$applyerAcctName)) {
               return false;
            }

            Object this$payeeAcctName = this.getPayeeAcctName();
            Object other$payeeAcctName = other.getPayeeAcctName();
            if (this$payeeAcctName == null) {
               if (other$payeeAcctName != null) {
                  return false;
               }
            } else if (!this$payeeAcctName.equals(other$payeeAcctName)) {
               return false;
            }

            label390: {
               Object this$lostDocumentId = this.getLostDocumentId();
               Object other$lostDocumentId = other.getLostDocumentId();
               if (this$lostDocumentId == null) {
                  if (other$lostDocumentId == null) {
                     break label390;
                  }
               } else if (this$lostDocumentId.equals(other$lostDocumentId)) {
                  break label390;
               }

               return false;
            }

            label383: {
               Object this$unlostName = this.getUnlostName();
               Object other$unlostName = other.getUnlostName();
               if (this$unlostName == null) {
                  if (other$unlostName == null) {
                     break label383;
                  }
               } else if (this$unlostName.equals(other$unlostName)) {
                  break label383;
               }

               return false;
            }

            label376: {
               Object this$unlostDocumentId = this.getUnlostDocumentId();
               Object other$unlostDocumentId = other.getUnlostDocumentId();
               if (this$unlostDocumentId == null) {
                  if (other$unlostDocumentId == null) {
                     break label376;
                  }
               } else if (this$unlostDocumentId.equals(other$unlostDocumentId)) {
                  break label376;
               }

               return false;
            }

            Object this$lostName = this.getLostName();
            Object other$lostName = other.getLostName();
            if (this$lostName == null) {
               if (other$lostName != null) {
                  return false;
               }
            } else if (!this$lostName.equals(other$lostName)) {
               return false;
            }

            label362: {
               Object this$lostTelNo = this.getLostTelNo();
               Object other$lostTelNo = other.getLostTelNo();
               if (this$lostTelNo == null) {
                  if (other$lostTelNo == null) {
                     break label362;
                  }
               } else if (this$lostTelNo.equals(other$lostTelNo)) {
                  break label362;
               }

               return false;
            }

            Object this$billLostAddr = this.getBillLostAddr();
            Object other$billLostAddr = other.getBillLostAddr();
            if (this$billLostAddr == null) {
               if (other$billLostAddr != null) {
                  return false;
               }
            } else if (!this$billLostAddr.equals(other$billLostAddr)) {
               return false;
            }

            label348: {
               Object this$busiPlace = this.getBusiPlace();
               Object other$busiPlace = other.getBusiPlace();
               if (this$busiPlace == null) {
                  if (other$busiPlace == null) {
                     break label348;
                  }
               } else if (this$busiPlace.equals(other$busiPlace)) {
                  break label348;
               }

               return false;
            }

            Object this$lostReason = this.getLostReason();
            Object other$lostReason = other.getLostReason();
            if (this$lostReason == null) {
               if (other$lostReason != null) {
                  return false;
               }
            } else if (!this$lostReason.equals(other$lostReason)) {
               return false;
            }

            Object this$lostStatus = this.getLostStatus();
            Object other$lostStatus = other.getLostStatus();
            if (this$lostStatus == null) {
               if (other$lostStatus != null) {
                  return false;
               }
            } else if (!this$lostStatus.equals(other$lostStatus)) {
               return false;
            }

            label327: {
               Object this$unlostTelNo = this.getUnlostTelNo();
               Object other$unlostTelNo = other.getUnlostTelNo();
               if (this$unlostTelNo == null) {
                  if (other$unlostTelNo == null) {
                     break label327;
                  }
               } else if (this$unlostTelNo.equals(other$unlostTelNo)) {
                  break label327;
               }

               return false;
            }

            label320: {
               Object this$unlostReason = this.getUnlostReason();
               Object other$unlostReason = other.getUnlostReason();
               if (this$unlostReason == null) {
                  if (other$unlostReason == null) {
                     break label320;
                  }
               } else if (this$unlostReason.equals(other$unlostReason)) {
                  break label320;
               }

               return false;
            }

            Object this$billNo = this.getBillNo();
            Object other$billNo = other.getBillNo();
            if (this$billNo == null) {
               if (other$billNo != null) {
                  return false;
               }
            } else if (!this$billNo.equals(other$billNo)) {
               return false;
            }

            Object this$billPswd = this.getBillPswd();
            Object other$billPswd = other.getBillPswd();
            if (this$billPswd == null) {
               if (other$billPswd != null) {
                  return false;
               }
            } else if (!this$billPswd.equals(other$billPswd)) {
               return false;
            }

            label299: {
               Object this$lostUserId = this.getLostUserId();
               Object other$lostUserId = other.getLostUserId();
               if (this$lostUserId == null) {
                  if (other$lostUserId == null) {
                     break label299;
                  }
               } else if (this$lostUserId.equals(other$lostUserId)) {
                  break label299;
               }

               return false;
            }

            Object this$unlostUserId = this.getUnlostUserId();
            Object other$unlostUserId = other.getUnlostUserId();
            if (this$unlostUserId == null) {
               if (other$unlostUserId != null) {
                  return false;
               }
            } else if (!this$unlostUserId.equals(other$unlostUserId)) {
               return false;
            }

            Object this$billLostTime = this.getBillLostTime();
            Object other$billLostTime = other.getBillLostTime();
            if (this$billLostTime == null) {
               if (other$billLostTime != null) {
                  return false;
               }
            } else if (!this$billLostTime.equals(other$billLostTime)) {
               return false;
            }

            label278: {
               Object this$lostTime = this.getLostTime();
               Object other$lostTime = other.getLostTime();
               if (this$lostTime == null) {
                  if (other$lostTime == null) {
                     break label278;
                  }
               } else if (this$lostTime.equals(other$lostTime)) {
                  break label278;
               }

               return false;
            }

            label271: {
               Object this$unlostTime = this.getUnlostTime();
               Object other$unlostTime = other.getUnlostTime();
               if (this$unlostTime == null) {
                  if (other$unlostTime == null) {
                     break label271;
                  }
               } else if (this$unlostTime.equals(other$unlostTime)) {
                  break label271;
               }

               return false;
            }

            label264: {
               Object this$agentDocumentType = this.getAgentDocumentType();
               Object other$agentDocumentType = other.getAgentDocumentType();
               if (this$agentDocumentType == null) {
                  if (other$agentDocumentType == null) {
                     break label264;
                  }
               } else if (this$agentDocumentType.equals(other$agentDocumentType)) {
                  break label264;
               }

               return false;
            }

            Object this$agentDocumentId = this.getAgentDocumentId();
            Object other$agentDocumentId = other.getAgentDocumentId();
            if (this$agentDocumentId == null) {
               if (other$agentDocumentId != null) {
                  return false;
               }
            } else if (!this$agentDocumentId.equals(other$agentDocumentId)) {
               return false;
            }

            Object this$company = this.getCompany();
            Object other$company = other.getCompany();
            if (this$company == null) {
               if (other$company != null) {
                  return false;
               }
            } else if (!this$company.equals(other$company)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400033404Out;
   }
}
