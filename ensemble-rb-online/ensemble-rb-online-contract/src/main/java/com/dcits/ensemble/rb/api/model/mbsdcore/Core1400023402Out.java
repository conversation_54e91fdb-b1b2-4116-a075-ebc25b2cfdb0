package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400023402Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400023402Out.PnBillDetailArray> pnBillDetailArray;
   @V(
      desc = "签约类型",
      notNull = false,
      length = "20",
      remark = "签约类型",
      maxSize = 20
   )
   private String signType;
   @V(
      desc = "打印方式",
      notNull = false,
      length = "3",
      remark = "打印方式",
      maxSize = 3
   )
   private String printType;

   public List<Core1400023402Out.PnBillDetailArray> getPnBillDetailArray() {
      return this.pnBillDetailArray;
   }

   public String getSignType() {
      return this.signType;
   }

   public String getPrintType() {
      return this.printType;
   }

   public void setPnBillDetailArray(List<Core1400023402Out.PnBillDetailArray> pnBillDetailArray) {
      this.pnBillDetailArray = pnBillDetailArray;
   }

   public void setSignType(String signType) {
      this.signType = signType;
   }

   public void setPrintType(String printType) {
      this.printType = printType;
   }

   public String toString() {
      return "Core1400023402Out(pnBillDetailArray=" + this.getPnBillDetailArray() + ", signType=" + this.getSignType() + ", printType=" + this.getPrintType() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400023402Out)) {
         return false;
      } else {
         Core1400023402Out other = (Core1400023402Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            label49: {
               Object this$pnBillDetailArray = this.getPnBillDetailArray();
               Object other$pnBillDetailArray = other.getPnBillDetailArray();
               if (this$pnBillDetailArray == null) {
                  if (other$pnBillDetailArray == null) {
                     break label49;
                  }
               } else if (this$pnBillDetailArray.equals(other$pnBillDetailArray)) {
                  break label49;
               }

               return false;
            }

            Object this$signType = this.getSignType();
            Object other$signType = other.getSignType();
            if (this$signType == null) {
               if (other$signType != null) {
                  return false;
               }
            } else if (!this$signType.equals(other$signType)) {
               return false;
            }

            Object this$printType = this.getPrintType();
            Object other$printType = other.getPrintType();
            if (this$printType == null) {
               if (other$printType != null) {
                  return false;
               }
            } else if (!this$printType.equals(other$printType)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400023402Out;
   }
   public static class PnBillDetailArray {
      @V(
         desc = "业务流水号",
         notNull = false,
         length = "50",
         remark = "支付流水号",
         maxSize = 50
      )
      private String serialNo;
      @V(
         desc = "所属机构号",
         notNull = false,
         length = "50",
         remark = "所属机构号",
         maxSize = 50
      )
      private String branch;
      @V(
         desc = "票据类型",
         notNull = false,
         length = "5",
         remark = "票据类型",
         maxSize = 5
      )
      private String billType;
      @V(
         desc = "票据号码",
         notNull = false,
         length = "50",
         remark = "票据号码",
         maxSize = 50
      )
      private String billNo;
      @V(
         desc = "票面金额",
         notNull = false,
         length = "17",
         remark = "票面金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal billAmt;
      @V(
         desc = "交易日期",
         notNull = false,
         remark = "交易日期"
      )
      private String tranDate;
      @V(
         desc = "挂账序列号",
         notNull = false,
         length = "50",
         remark = "挂账账户序列号",
         maxSize = 50
      )
      private String hangSeqNo;
      @V(
         desc = "签发现金项目代码",
         notNull = false,
         length = "3",
         remark = "签发现金项目代码",
         maxSize = 3
      )
      private String signCashItem;
      @V(
         desc = "付款人账号",
         notNull = false,
         length = "50",
         remark = "付款人账号",
         maxSize = 50
      )
      private String payerBaseAcctNo;
      @V(
         desc = "付款人名称",
         notNull = false,
         length = "200",
         remark = "付款人名称",
         maxSize = 200
      )
      private String payerName;
      @V(
         desc = "本票申请书号码",
         notNull = false,
         length = "50",
         remark = "本票申请书号码",
         maxSize = 50
      )
      private String billApplyNo;
      @V(
         desc = "票据登记日期",
         notNull = false,
         remark = "票据登记日期"
      )
      private String billSignDate;
      @V(
         desc = "兑付方式",
         notNull = false,
         length = "10",
         remark = "兑付方式",
         maxSize = 10
      )
      private String paymentType;
      @V(
         desc = "退回方式1-现金2-转账3-销挂账",
         notNull = false,
         length = "1",
         remark = "退回方式1-现金2-转账3-销挂账",
         maxSize = 1
      )
      private String returnType;
      @V(
         desc = "兑付现金项目代码",
         notNull = false,
         length = "3",
         remark = "兑付现金项目代码",
         maxSize = 3
      )
      private String paymentCashItem;
      @V(
         desc = "收款人账户",
         notNull = false,
         length = "50",
         remark = "收款人账户",
         maxSize = 50
      )
      private String payeeAcctNo;
      @V(
         desc = "收款人名称",
         notNull = false,
         length = "200",
         remark = "收款人名称",
         maxSize = 200
      )
      private String payeeName;
      @V(
         desc = "兑付日期",
         notNull = false,
         remark = "兑付日期"
      )
      private String paymentDate;
      @V(
         desc = "原票据号码",
         notNull = false,
         length = "50",
         remark = "原票据号码",
         maxSize = 50
      )
      private String orgBillNo;
      @V(
         desc = "打印方式",
         notNull = false,
         length = "3",
         remark = "打印方式",
         maxSize = 3
      )
      private String printType;
      @V(
         desc = "交易柜员",
         notNull = false,
         length = "30",
         remark = "交易柜员",
         maxSize = 30
      )
      private String userId;
      @V(
         desc = "状态",
         notNull = false,
         length = "1",
         remark = "状态",
         maxSize = 1
      )
      private String status;
      @V(
         desc = "渠道流水号",
         notNull = false,
         length = "50",
         remark = "渠道流水号",
         maxSize = 50
      )
      private String channelSeqNo;
      @V(
         desc = "过期标志",
         notNull = false,
         length = "10",
         remark = "过期标志",
         maxSize = 10
      )
      private String expireFlag;
      @V(
         desc = "票据密押",
         notNull = false,
         length = "200",
         remark = "票据密押",
         maxSize = 200
      )
      private String encrypKey;
      @V(
         desc = "限额维护类型",
         notNull = false,
         length = "1",
         remark = "限额维护类型",
         maxSize = 1
      )
      private String operType;
      @V(
         desc = "签约类型",
         notNull = false,
         length = "20",
         remark = "签约类型",
         maxSize = 20
      )
      private String signType;

      public String getSerialNo() {
         return this.serialNo;
      }

      public String getBranch() {
         return this.branch;
      }

      public String getBillType() {
         return this.billType;
      }

      public String getBillNo() {
         return this.billNo;
      }

      public BigDecimal getBillAmt() {
         return this.billAmt;
      }

      public String getTranDate() {
         return this.tranDate;
      }

      public String getHangSeqNo() {
         return this.hangSeqNo;
      }

      public String getSignCashItem() {
         return this.signCashItem;
      }

      public String getPayerBaseAcctNo() {
         return this.payerBaseAcctNo;
      }

      public String getPayerName() {
         return this.payerName;
      }

      public String getBillApplyNo() {
         return this.billApplyNo;
      }

      public String getBillSignDate() {
         return this.billSignDate;
      }

      public String getPaymentType() {
         return this.paymentType;
      }

      public String getReturnType() {
         return this.returnType;
      }

      public String getPaymentCashItem() {
         return this.paymentCashItem;
      }

      public String getPayeeAcctNo() {
         return this.payeeAcctNo;
      }

      public String getPayeeName() {
         return this.payeeName;
      }

      public String getPaymentDate() {
         return this.paymentDate;
      }

      public String getOrgBillNo() {
         return this.orgBillNo;
      }

      public String getPrintType() {
         return this.printType;
      }

      public String getUserId() {
         return this.userId;
      }

      public String getStatus() {
         return this.status;
      }

      public String getChannelSeqNo() {
         return this.channelSeqNo;
      }

      public String getExpireFlag() {
         return this.expireFlag;
      }

      public String getEncrypKey() {
         return this.encrypKey;
      }

      public String getOperType() {
         return this.operType;
      }

      public String getSignType() {
         return this.signType;
      }

      public void setSerialNo(String serialNo) {
         this.serialNo = serialNo;
      }

      public void setBranch(String branch) {
         this.branch = branch;
      }

      public void setBillType(String billType) {
         this.billType = billType;
      }

      public void setBillNo(String billNo) {
         this.billNo = billNo;
      }

      public void setBillAmt(BigDecimal billAmt) {
         this.billAmt = billAmt;
      }

      public void setTranDate(String tranDate) {
         this.tranDate = tranDate;
      }

      public void setHangSeqNo(String hangSeqNo) {
         this.hangSeqNo = hangSeqNo;
      }

      public void setSignCashItem(String signCashItem) {
         this.signCashItem = signCashItem;
      }

      public void setPayerBaseAcctNo(String payerBaseAcctNo) {
         this.payerBaseAcctNo = payerBaseAcctNo;
      }

      public void setPayerName(String payerName) {
         this.payerName = payerName;
      }

      public void setBillApplyNo(String billApplyNo) {
         this.billApplyNo = billApplyNo;
      }

      public void setBillSignDate(String billSignDate) {
         this.billSignDate = billSignDate;
      }

      public void setPaymentType(String paymentType) {
         this.paymentType = paymentType;
      }

      public void setReturnType(String returnType) {
         this.returnType = returnType;
      }

      public void setPaymentCashItem(String paymentCashItem) {
         this.paymentCashItem = paymentCashItem;
      }

      public void setPayeeAcctNo(String payeeAcctNo) {
         this.payeeAcctNo = payeeAcctNo;
      }

      public void setPayeeName(String payeeName) {
         this.payeeName = payeeName;
      }

      public void setPaymentDate(String paymentDate) {
         this.paymentDate = paymentDate;
      }

      public void setOrgBillNo(String orgBillNo) {
         this.orgBillNo = orgBillNo;
      }

      public void setPrintType(String printType) {
         this.printType = printType;
      }

      public void setUserId(String userId) {
         this.userId = userId;
      }

      public void setStatus(String status) {
         this.status = status;
      }

      public void setChannelSeqNo(String channelSeqNo) {
         this.channelSeqNo = channelSeqNo;
      }

      public void setExpireFlag(String expireFlag) {
         this.expireFlag = expireFlag;
      }

      public void setEncrypKey(String encrypKey) {
         this.encrypKey = encrypKey;
      }

      public void setOperType(String operType) {
         this.operType = operType;
      }

      public void setSignType(String signType) {
         this.signType = signType;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400023402Out.PnBillDetailArray)) {
            return false;
         } else {
            Core1400023402Out.PnBillDetailArray other = (Core1400023402Out.PnBillDetailArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label335: {
                  Object this$serialNo = this.getSerialNo();
                  Object other$serialNo = other.getSerialNo();
                  if (this$serialNo == null) {
                     if (other$serialNo == null) {
                        break label335;
                     }
                  } else if (this$serialNo.equals(other$serialNo)) {
                     break label335;
                  }

                  return false;
               }

               Object this$branch = this.getBranch();
               Object other$branch = other.getBranch();
               if (this$branch == null) {
                  if (other$branch != null) {
                     return false;
                  }
               } else if (!this$branch.equals(other$branch)) {
                  return false;
               }

               Object this$billType = this.getBillType();
               Object other$billType = other.getBillType();
               if (this$billType == null) {
                  if (other$billType != null) {
                     return false;
                  }
               } else if (!this$billType.equals(other$billType)) {
                  return false;
               }

               label314: {
                  Object this$billNo = this.getBillNo();
                  Object other$billNo = other.getBillNo();
                  if (this$billNo == null) {
                     if (other$billNo == null) {
                        break label314;
                     }
                  } else if (this$billNo.equals(other$billNo)) {
                     break label314;
                  }

                  return false;
               }

               label307: {
                  Object this$billAmt = this.getBillAmt();
                  Object other$billAmt = other.getBillAmt();
                  if (this$billAmt == null) {
                     if (other$billAmt == null) {
                        break label307;
                     }
                  } else if (this$billAmt.equals(other$billAmt)) {
                     break label307;
                  }

                  return false;
               }

               Object this$tranDate = this.getTranDate();
               Object other$tranDate = other.getTranDate();
               if (this$tranDate == null) {
                  if (other$tranDate != null) {
                     return false;
                  }
               } else if (!this$tranDate.equals(other$tranDate)) {
                  return false;
               }

               Object this$hangSeqNo = this.getHangSeqNo();
               Object other$hangSeqNo = other.getHangSeqNo();
               if (this$hangSeqNo == null) {
                  if (other$hangSeqNo != null) {
                     return false;
                  }
               } else if (!this$hangSeqNo.equals(other$hangSeqNo)) {
                  return false;
               }

               label286: {
                  Object this$signCashItem = this.getSignCashItem();
                  Object other$signCashItem = other.getSignCashItem();
                  if (this$signCashItem == null) {
                     if (other$signCashItem == null) {
                        break label286;
                     }
                  } else if (this$signCashItem.equals(other$signCashItem)) {
                     break label286;
                  }

                  return false;
               }

               label279: {
                  Object this$payerBaseAcctNo = this.getPayerBaseAcctNo();
                  Object other$payerBaseAcctNo = other.getPayerBaseAcctNo();
                  if (this$payerBaseAcctNo == null) {
                     if (other$payerBaseAcctNo == null) {
                        break label279;
                     }
                  } else if (this$payerBaseAcctNo.equals(other$payerBaseAcctNo)) {
                     break label279;
                  }

                  return false;
               }

               Object this$payerName = this.getPayerName();
               Object other$payerName = other.getPayerName();
               if (this$payerName == null) {
                  if (other$payerName != null) {
                     return false;
                  }
               } else if (!this$payerName.equals(other$payerName)) {
                  return false;
               }

               label265: {
                  Object this$billApplyNo = this.getBillApplyNo();
                  Object other$billApplyNo = other.getBillApplyNo();
                  if (this$billApplyNo == null) {
                     if (other$billApplyNo == null) {
                        break label265;
                     }
                  } else if (this$billApplyNo.equals(other$billApplyNo)) {
                     break label265;
                  }

                  return false;
               }

               Object this$billSignDate = this.getBillSignDate();
               Object other$billSignDate = other.getBillSignDate();
               if (this$billSignDate == null) {
                  if (other$billSignDate != null) {
                     return false;
                  }
               } else if (!this$billSignDate.equals(other$billSignDate)) {
                  return false;
               }

               label251: {
                  Object this$paymentType = this.getPaymentType();
                  Object other$paymentType = other.getPaymentType();
                  if (this$paymentType == null) {
                     if (other$paymentType == null) {
                        break label251;
                     }
                  } else if (this$paymentType.equals(other$paymentType)) {
                     break label251;
                  }

                  return false;
               }

               Object this$returnType = this.getReturnType();
               Object other$returnType = other.getReturnType();
               if (this$returnType == null) {
                  if (other$returnType != null) {
                     return false;
                  }
               } else if (!this$returnType.equals(other$returnType)) {
                  return false;
               }

               Object this$paymentCashItem = this.getPaymentCashItem();
               Object other$paymentCashItem = other.getPaymentCashItem();
               if (this$paymentCashItem == null) {
                  if (other$paymentCashItem != null) {
                     return false;
                  }
               } else if (!this$paymentCashItem.equals(other$paymentCashItem)) {
                  return false;
               }

               label230: {
                  Object this$payeeAcctNo = this.getPayeeAcctNo();
                  Object other$payeeAcctNo = other.getPayeeAcctNo();
                  if (this$payeeAcctNo == null) {
                     if (other$payeeAcctNo == null) {
                        break label230;
                     }
                  } else if (this$payeeAcctNo.equals(other$payeeAcctNo)) {
                     break label230;
                  }

                  return false;
               }

               label223: {
                  Object this$payeeName = this.getPayeeName();
                  Object other$payeeName = other.getPayeeName();
                  if (this$payeeName == null) {
                     if (other$payeeName == null) {
                        break label223;
                     }
                  } else if (this$payeeName.equals(other$payeeName)) {
                     break label223;
                  }

                  return false;
               }

               Object this$paymentDate = this.getPaymentDate();
               Object other$paymentDate = other.getPaymentDate();
               if (this$paymentDate == null) {
                  if (other$paymentDate != null) {
                     return false;
                  }
               } else if (!this$paymentDate.equals(other$paymentDate)) {
                  return false;
               }

               Object this$orgBillNo = this.getOrgBillNo();
               Object other$orgBillNo = other.getOrgBillNo();
               if (this$orgBillNo == null) {
                  if (other$orgBillNo != null) {
                     return false;
                  }
               } else if (!this$orgBillNo.equals(other$orgBillNo)) {
                  return false;
               }

               label202: {
                  Object this$printType = this.getPrintType();
                  Object other$printType = other.getPrintType();
                  if (this$printType == null) {
                     if (other$printType == null) {
                        break label202;
                     }
                  } else if (this$printType.equals(other$printType)) {
                     break label202;
                  }

                  return false;
               }

               label195: {
                  Object this$userId = this.getUserId();
                  Object other$userId = other.getUserId();
                  if (this$userId == null) {
                     if (other$userId == null) {
                        break label195;
                     }
                  } else if (this$userId.equals(other$userId)) {
                     break label195;
                  }

                  return false;
               }

               Object this$status = this.getStatus();
               Object other$status = other.getStatus();
               if (this$status == null) {
                  if (other$status != null) {
                     return false;
                  }
               } else if (!this$status.equals(other$status)) {
                  return false;
               }

               Object this$channelSeqNo = this.getChannelSeqNo();
               Object other$channelSeqNo = other.getChannelSeqNo();
               if (this$channelSeqNo == null) {
                  if (other$channelSeqNo != null) {
                     return false;
                  }
               } else if (!this$channelSeqNo.equals(other$channelSeqNo)) {
                  return false;
               }

               label174: {
                  Object this$expireFlag = this.getExpireFlag();
                  Object other$expireFlag = other.getExpireFlag();
                  if (this$expireFlag == null) {
                     if (other$expireFlag == null) {
                        break label174;
                     }
                  } else if (this$expireFlag.equals(other$expireFlag)) {
                     break label174;
                  }

                  return false;
               }

               label167: {
                  Object this$encrypKey = this.getEncrypKey();
                  Object other$encrypKey = other.getEncrypKey();
                  if (this$encrypKey == null) {
                     if (other$encrypKey == null) {
                        break label167;
                     }
                  } else if (this$encrypKey.equals(other$encrypKey)) {
                     break label167;
                  }

                  return false;
               }

               Object this$operType = this.getOperType();
               Object other$operType = other.getOperType();
               if (this$operType == null) {
                  if (other$operType != null) {
                     return false;
                  }
               } else if (!this$operType.equals(other$operType)) {
                  return false;
               }

               Object this$signType = this.getSignType();
               Object other$signType = other.getSignType();
               if (this$signType == null) {
                  if (other$signType != null) {
                     return false;
                  }
               } else if (!this$signType.equals(other$signType)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400023402Out.PnBillDetailArray;
      }
      public String toString() {
         return "Core1400023402Out.PnBillDetailArray(serialNo=" + this.getSerialNo() + ", branch=" + this.getBranch() + ", billType=" + this.getBillType() + ", billNo=" + this.getBillNo() + ", billAmt=" + this.getBillAmt() + ", tranDate=" + this.getTranDate() + ", hangSeqNo=" + this.getHangSeqNo() + ", signCashItem=" + this.getSignCashItem() + ", payerBaseAcctNo=" + this.getPayerBaseAcctNo() + ", payerName=" + this.getPayerName() + ", billApplyNo=" + this.getBillApplyNo() + ", billSignDate=" + this.getBillSignDate() + ", paymentType=" + this.getPaymentType() + ", returnType=" + this.getReturnType() + ", paymentCashItem=" + this.getPaymentCashItem() + ", payeeAcctNo=" + this.getPayeeAcctNo() + ", payeeName=" + this.getPayeeName() + ", paymentDate=" + this.getPaymentDate() + ", orgBillNo=" + this.getOrgBillNo() + ", printType=" + this.getPrintType() + ", userId=" + this.getUserId() + ", status=" + this.getStatus() + ", channelSeqNo=" + this.getChannelSeqNo() + ", expireFlag=" + this.getExpireFlag() + ", encrypKey=" + this.getEncrypKey() + ", operType=" + this.getOperType() + ", signType=" + this.getSignType() + ")";
      }
   }
}
