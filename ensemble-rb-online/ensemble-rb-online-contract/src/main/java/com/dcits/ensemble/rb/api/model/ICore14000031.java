package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000031In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000031Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000031 {
   String URL = "/rb/inq/base/acct";


   @ApiRemark("标准优化")
   @ApiDesc("根据账户名称查询账户基本信息")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0031"
   )
   @BusinessCategory("1400-查询")
   @FunctionCategory("RB47-客户账户查询")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core14000031Out runService(Core14000031In var1);
}
