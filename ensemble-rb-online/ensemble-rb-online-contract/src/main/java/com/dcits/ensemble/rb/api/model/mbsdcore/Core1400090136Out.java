package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400090136Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "协定存款数组",
      notNull = false,
      remark = "协定存款数组"
   )
   private List<Core1400090136Out.AgreeDepositArray> agreeDepositArray;

   public List<Core1400090136Out.AgreeDepositArray> getAgreeDepositArray() {
      return this.agreeDepositArray;
   }

   public void setAgreeDepositArray(List<Core1400090136Out.AgreeDepositArray> agreeDepositArray) {
      this.agreeDepositArray = agreeDepositArray;
   }

   public String toString() {
      return "Core1400090136Out(agreeDepositArray=" + this.getAgreeDepositArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400090136Out)) {
         return false;
      } else {
         Core1400090136Out other = (Core1400090136Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$agreeDepositArray = this.getAgreeDepositArray();
            Object other$agreeDepositArray = other.getAgreeDepositArray();
            if (this$agreeDepositArray == null) {
               if (other$agreeDepositArray != null) {
                  return false;
               }
            } else if (!this$agreeDepositArray.equals(other$agreeDepositArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400090136Out;
   }
   public static class AgreeDepositArray {
      @V(
         desc = "期限类型",
         notNull = false,
         length = "1",
         in = "D,W,M,Y",
         remark = "期限类型",
         maxSize = 1
      )
      private String termType;
      @V(
         desc = "开始日期",
         notNull = false,
         remark = "开始日期"
      )
      private String startDate;
      @V(
         desc = "结束日期",
         notNull = false,
         remark = "结束日期"
      )
      private String endDate;
      @V(
         desc = "定期阶段",
         notNull = false,
         length = "5",
         remark = "定期阶段",
         maxSize = 5
      )
      private String termPeriod;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户名称",
         notNull = false,
         length = "200",
         remark = "账户名称，一般指中文账户名称",
         maxSize = 200
      )
      private String acctName;
      @V(
         desc = "数组",
         notNull = false,
         remark = "数组"
      )
      private List<Core1400090136Out.AgreeDepositArray.ConRateArray> conRateArray;
      @V(
         desc = "法人",
         notNull = false,
         length = "20",
         remark = "法人",
         maxSize = 20
      )
      private String company;

      public String getTermType() {
         return this.termType;
      }

      public String getStartDate() {
         return this.startDate;
      }

      public String getEndDate() {
         return this.endDate;
      }

      public String getTermPeriod() {
         return this.termPeriod;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctName() {
         return this.acctName;
      }

      public List<Core1400090136Out.AgreeDepositArray.ConRateArray> getConRateArray() {
         return this.conRateArray;
      }

      public String getCompany() {
         return this.company;
      }

      public void setTermType(String termType) {
         this.termType = termType;
      }

      public void setStartDate(String startDate) {
         this.startDate = startDate;
      }

      public void setEndDate(String endDate) {
         this.endDate = endDate;
      }

      public void setTermPeriod(String termPeriod) {
         this.termPeriod = termPeriod;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctName(String acctName) {
         this.acctName = acctName;
      }

      public void setConRateArray(List<Core1400090136Out.AgreeDepositArray.ConRateArray> conRateArray) {
         this.conRateArray = conRateArray;
      }

      public void setCompany(String company) {
         this.company = company;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400090136Out.AgreeDepositArray)) {
            return false;
         } else {
            Core1400090136Out.AgreeDepositArray other = (Core1400090136Out.AgreeDepositArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$termType = this.getTermType();
               Object other$termType = other.getTermType();
               if (this$termType == null) {
                  if (other$termType != null) {
                     return false;
                  }
               } else if (!this$termType.equals(other$termType)) {
                  return false;
               }

               Object this$startDate = this.getStartDate();
               Object other$startDate = other.getStartDate();
               if (this$startDate == null) {
                  if (other$startDate != null) {
                     return false;
                  }
               } else if (!this$startDate.equals(other$startDate)) {
                  return false;
               }

               Object this$endDate = this.getEndDate();
               Object other$endDate = other.getEndDate();
               if (this$endDate == null) {
                  if (other$endDate != null) {
                     return false;
                  }
               } else if (!this$endDate.equals(other$endDate)) {
                  return false;
               }

               label110: {
                  Object this$termPeriod = this.getTermPeriod();
                  Object other$termPeriod = other.getTermPeriod();
                  if (this$termPeriod == null) {
                     if (other$termPeriod == null) {
                        break label110;
                     }
                  } else if (this$termPeriod.equals(other$termPeriod)) {
                     break label110;
                  }

                  return false;
               }

               label103: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label103;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label103;
                  }

                  return false;
               }

               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo != null) {
                     return false;
                  }
               } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                  return false;
               }

               label89: {
                  Object this$prodType = this.getProdType();
                  Object other$prodType = other.getProdType();
                  if (this$prodType == null) {
                     if (other$prodType == null) {
                        break label89;
                     }
                  } else if (this$prodType.equals(other$prodType)) {
                     break label89;
                  }

                  return false;
               }

               label82: {
                  Object this$acctName = this.getAcctName();
                  Object other$acctName = other.getAcctName();
                  if (this$acctName == null) {
                     if (other$acctName == null) {
                        break label82;
                     }
                  } else if (this$acctName.equals(other$acctName)) {
                     break label82;
                  }

                  return false;
               }

               Object this$conRateArray = this.getConRateArray();
               Object other$conRateArray = other.getConRateArray();
               if (this$conRateArray == null) {
                  if (other$conRateArray != null) {
                     return false;
                  }
               } else if (!this$conRateArray.equals(other$conRateArray)) {
                  return false;
               }

               Object this$company = this.getCompany();
               Object other$company = other.getCompany();
               if (this$company == null) {
                  if (other$company != null) {
                     return false;
                  }
               } else if (!this$company.equals(other$company)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400090136Out.AgreeDepositArray;
      }
      public String toString() {
         return "Core1400090136Out.AgreeDepositArray(termType=" + this.getTermType() + ", startDate=" + this.getStartDate() + ", endDate=" + this.getEndDate() + ", termPeriod=" + this.getTermPeriod() + ", baseAcctNo=" + this.getBaseAcctNo() + ", acctSeqNo=" + this.getAcctSeqNo() + ", prodType=" + this.getProdType() + ", acctName=" + this.getAcctName() + ", conRateArray=" + this.getConRateArray() + ", company=" + this.getCompany() + ")";
      }

      public static class ConRateArray {
         @V(
            desc = "分户级利率浮动百分点",
            notNull = false,
            length = "15",
            remark = "分户固定利率，一般与客户协商后的利率上浮百分点，只在客户正常计提和支取的时候生效",
            decimalLength = 8,
            precision = 8
         )
         private BigDecimal acctSpreadRate;
         @V(
            desc = "分户级利率浮动百分比",
            notNull = false,
            length = "5",
            remark = "分户级利率浮动百分比",
            decimalLength = 2,
            precision = 2
         )
         private BigDecimal acctPercentRate;
         @V(
            desc = "分户级固定利率",
            notNull = false,
            length = "15",
            remark = "分户固定利率，一般与客户协商后的固定利率，优先级最高，不受行内基准利率调整的影响",
            decimalLength = 8,
            precision = 8
         )
         private BigDecimal acctFixedRate;
         @V(
            desc = "靠档金额",
            notNull = false,
            length = "17",
            remark = "靠档金额",
            decimalLength = 2,
            precision = 2
         )
         private BigDecimal nearAmt;
         @V(
            desc = "利息分类",
            notNull = false,
            length = "5",
            in = "INT,ODI,PDUE",
            remark = "利息分类",
            maxSize = 5
         )
         private String intClass;
         @V(
            desc = "行内利率",
            notNull = false,
            length = "15",
            remark = "在人行基准利率调整后对客发布的行内利率",
            decimalLength = 8,
            precision = 8
         )
         private BigDecimal actualRate;
         @V(
            desc = "浮动利率",
            notNull = false,
            length = "15",
            remark = "浮动利率",
            decimalLength = 8,
            precision = 8
         )
         private BigDecimal floatRate;
         @V(
            desc = "执行利率",
            notNull = false,
            length = "15",
            remark = "执行利率",
            decimalLength = 8,
            precision = 8
         )
         private BigDecimal realRate;
         @V(
            desc = "利率类型",
            notNull = false,
            length = "5",
            remark = "利率类型",
            maxSize = 5
         )
         private String intType;
         @V(
            desc = "年基准天数",
            notNull = false,
            length = "3",
            remark = "年基准天数",
            maxSize = 3
         )
         private String yearBasis;

         public BigDecimal getAcctSpreadRate() {
            return this.acctSpreadRate;
         }

         public BigDecimal getAcctPercentRate() {
            return this.acctPercentRate;
         }

         public BigDecimal getAcctFixedRate() {
            return this.acctFixedRate;
         }

         public BigDecimal getNearAmt() {
            return this.nearAmt;
         }

         public String getIntClass() {
            return this.intClass;
         }

         public BigDecimal getActualRate() {
            return this.actualRate;
         }

         public BigDecimal getFloatRate() {
            return this.floatRate;
         }

         public BigDecimal getRealRate() {
            return this.realRate;
         }

         public String getIntType() {
            return this.intType;
         }

         public String getYearBasis() {
            return this.yearBasis;
         }

         public void setAcctSpreadRate(BigDecimal acctSpreadRate) {
            this.acctSpreadRate = acctSpreadRate;
         }

         public void setAcctPercentRate(BigDecimal acctPercentRate) {
            this.acctPercentRate = acctPercentRate;
         }

         public void setAcctFixedRate(BigDecimal acctFixedRate) {
            this.acctFixedRate = acctFixedRate;
         }

         public void setNearAmt(BigDecimal nearAmt) {
            this.nearAmt = nearAmt;
         }

         public void setIntClass(String intClass) {
            this.intClass = intClass;
         }

         public void setActualRate(BigDecimal actualRate) {
            this.actualRate = actualRate;
         }

         public void setFloatRate(BigDecimal floatRate) {
            this.floatRate = floatRate;
         }

         public void setRealRate(BigDecimal realRate) {
            this.realRate = realRate;
         }

         public void setIntType(String intType) {
            this.intType = intType;
         }

         public void setYearBasis(String yearBasis) {
            this.yearBasis = yearBasis;
         }

         public boolean equals(Object o) {
            if (o == this) {
               return true;
            } else if (!(o instanceof Core1400090136Out.AgreeDepositArray.ConRateArray)) {
               return false;
            } else {
               Core1400090136Out.AgreeDepositArray.ConRateArray other = (Core1400090136Out.AgreeDepositArray.ConRateArray)o;
               if (!other.canEqual(this)) {
                  return false;
               } else {
                  Object this$acctSpreadRate = this.getAcctSpreadRate();
                  Object other$acctSpreadRate = other.getAcctSpreadRate();
                  if (this$acctSpreadRate == null) {
                     if (other$acctSpreadRate != null) {
                        return false;
                     }
                  } else if (!this$acctSpreadRate.equals(other$acctSpreadRate)) {
                     return false;
                  }

                  Object this$acctPercentRate = this.getAcctPercentRate();
                  Object other$acctPercentRate = other.getAcctPercentRate();
                  if (this$acctPercentRate == null) {
                     if (other$acctPercentRate != null) {
                        return false;
                     }
                  } else if (!this$acctPercentRate.equals(other$acctPercentRate)) {
                     return false;
                  }

                  Object this$acctFixedRate = this.getAcctFixedRate();
                  Object other$acctFixedRate = other.getAcctFixedRate();
                  if (this$acctFixedRate == null) {
                     if (other$acctFixedRate != null) {
                        return false;
                     }
                  } else if (!this$acctFixedRate.equals(other$acctFixedRate)) {
                     return false;
                  }

                  label110: {
                     Object this$nearAmt = this.getNearAmt();
                     Object other$nearAmt = other.getNearAmt();
                     if (this$nearAmt == null) {
                        if (other$nearAmt == null) {
                           break label110;
                        }
                     } else if (this$nearAmt.equals(other$nearAmt)) {
                        break label110;
                     }

                     return false;
                  }

                  label103: {
                     Object this$intClass = this.getIntClass();
                     Object other$intClass = other.getIntClass();
                     if (this$intClass == null) {
                        if (other$intClass == null) {
                           break label103;
                        }
                     } else if (this$intClass.equals(other$intClass)) {
                        break label103;
                     }

                     return false;
                  }

                  Object this$actualRate = this.getActualRate();
                  Object other$actualRate = other.getActualRate();
                  if (this$actualRate == null) {
                     if (other$actualRate != null) {
                        return false;
                     }
                  } else if (!this$actualRate.equals(other$actualRate)) {
                     return false;
                  }

                  label89: {
                     Object this$floatRate = this.getFloatRate();
                     Object other$floatRate = other.getFloatRate();
                     if (this$floatRate == null) {
                        if (other$floatRate == null) {
                           break label89;
                        }
                     } else if (this$floatRate.equals(other$floatRate)) {
                        break label89;
                     }

                     return false;
                  }

                  label82: {
                     Object this$realRate = this.getRealRate();
                     Object other$realRate = other.getRealRate();
                     if (this$realRate == null) {
                        if (other$realRate == null) {
                           break label82;
                        }
                     } else if (this$realRate.equals(other$realRate)) {
                        break label82;
                     }

                     return false;
                  }

                  Object this$intType = this.getIntType();
                  Object other$intType = other.getIntType();
                  if (this$intType == null) {
                     if (other$intType != null) {
                        return false;
                     }
                  } else if (!this$intType.equals(other$intType)) {
                     return false;
                  }

                  Object this$yearBasis = this.getYearBasis();
                  Object other$yearBasis = other.getYearBasis();
                  if (this$yearBasis == null) {
                     if (other$yearBasis != null) {
                        return false;
                     }
                  } else if (!this$yearBasis.equals(other$yearBasis)) {
                     return false;
                  }

                  return true;
               }
            }
         }

         protected boolean canEqual(Object other) {
            return other instanceof Core1400090136Out.AgreeDepositArray.ConRateArray;
         }
         public String toString() {
            return "Core1400090136Out.AgreeDepositArray.ConRateArray(acctSpreadRate=" + this.getAcctSpreadRate() + ", acctPercentRate=" + this.getAcctPercentRate() + ", acctFixedRate=" + this.getAcctFixedRate() + ", nearAmt=" + this.getNearAmt() + ", intClass=" + this.getIntClass() + ", actualRate=" + this.getActualRate() + ", floatRate=" + this.getFloatRate() + ", realRate=" + this.getRealRate() + ", intType=" + this.getIntType() + ", yearBasis=" + this.getYearBasis() + ")";
         }
      }
   }
}
