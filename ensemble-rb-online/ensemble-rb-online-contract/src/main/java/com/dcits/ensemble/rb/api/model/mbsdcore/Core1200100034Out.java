package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;

@MessageOut
public class Core1200100034Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "账户余额",
      notNull = false,
      length = "17",
      remark = "账户余额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal acctBalance;
   @V(
      desc = "支取金额",
      notNull = false,
      length = "17",
      remark = "支取金额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal debtAmt;
   @V(
      desc = "账户利息",
      notNull = false,
      length = "17",
      remark = "账户利息",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal acctInt;
   @V(
      desc = "转让本金",
      notNull = false,
      length = "17",
      remark = "转让本金",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal tranferPri;
   @V(
      desc = "亏损标识",
      notNull = false,
      length = "1",
      remark = "亏损标识，Y-是,N-否",
      maxSize = 1
   )
   private String deficitFlag;

   public BigDecimal getAcctBalance() {
      return this.acctBalance;
   }

   public BigDecimal getDebtAmt() {
      return this.debtAmt;
   }

   public BigDecimal getAcctInt() {
      return this.acctInt;
   }

   public BigDecimal getTranferPri() {
      return this.tranferPri;
   }

   public String getDeficitFlag() {
      return this.deficitFlag;
   }

   public void setAcctBalance(BigDecimal acctBalance) {
      this.acctBalance = acctBalance;
   }

   public void setDebtAmt(BigDecimal debtAmt) {
      this.debtAmt = debtAmt;
   }

   public void setAcctInt(BigDecimal acctInt) {
      this.acctInt = acctInt;
   }

   public void setTranferPri(BigDecimal tranferPri) {
      this.tranferPri = tranferPri;
   }

   public void setDeficitFlag(String deficitFlag) {
      this.deficitFlag = deficitFlag;
   }

   public String toString() {
      return "Core1200100034Out(acctBalance=" + this.getAcctBalance() + ", debtAmt=" + this.getDebtAmt() + ", acctInt=" + this.getAcctInt() + ", tranferPri=" + this.getTranferPri() + ", deficitFlag=" + this.getDeficitFlag() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200100034Out)) {
         return false;
      } else {
         Core1200100034Out other = (Core1200100034Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            label73: {
               Object this$acctBalance = this.getAcctBalance();
               Object other$acctBalance = other.getAcctBalance();
               if (this$acctBalance == null) {
                  if (other$acctBalance == null) {
                     break label73;
                  }
               } else if (this$acctBalance.equals(other$acctBalance)) {
                  break label73;
               }

               return false;
            }

            Object this$debtAmt = this.getDebtAmt();
            Object other$debtAmt = other.getDebtAmt();
            if (this$debtAmt == null) {
               if (other$debtAmt != null) {
                  return false;
               }
            } else if (!this$debtAmt.equals(other$debtAmt)) {
               return false;
            }

            label59: {
               Object this$acctInt = this.getAcctInt();
               Object other$acctInt = other.getAcctInt();
               if (this$acctInt == null) {
                  if (other$acctInt == null) {
                     break label59;
                  }
               } else if (this$acctInt.equals(other$acctInt)) {
                  break label59;
               }

               return false;
            }

            Object this$tranferPri = this.getTranferPri();
            Object other$tranferPri = other.getTranferPri();
            if (this$tranferPri == null) {
               if (other$tranferPri != null) {
                  return false;
               }
            } else if (!this$tranferPri.equals(other$tranferPri)) {
               return false;
            }

            Object this$deficitFlag = this.getDeficitFlag();
            Object other$deficitFlag = other.getDeficitFlag();
            if (this$deficitFlag == null) {
               if (other$deficitFlag != null) {
                  return false;
               }
            } else if (!this$deficitFlag.equals(other$deficitFlag)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200100034Out;
   }
}
