package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000140In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000140Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12000140 {
   String URL = "/rb/nfin/pbk/change/card";


   @ApiRemark("深度优化")
   @ApiDesc("支持个人普通存折、个人定活一本通存折更换为卡")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0140"
   )
   @BusinessCategory("1200-非金融")
   @FunctionCategory("OB67-凭证")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core12000140Out runService(Core12000140In var1);
}
