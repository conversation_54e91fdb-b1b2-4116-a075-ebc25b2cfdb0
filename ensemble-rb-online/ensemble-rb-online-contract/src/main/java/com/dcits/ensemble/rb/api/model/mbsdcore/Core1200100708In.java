package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.util.List;

@MessageIn
public class Core1200100708In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1200100708In.Body body;

   public Core1200100708In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1200100708In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1200100708In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200100708In)) {
         return false;
      } else {
         Core1200100708In other = (Core1200100708In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200100708In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "外围系统协议编号",
         notNull = false,
         length = "50",
         remark = "外围系统协议编号",
         maxSize = 50
      )
      private String signId;
      @V(
         desc = "协议操作类型",
         notNull = true,
         length = "2",
         inDesc = "01-创建,02-修改,03-删除",
         remark = "协议操作类型",
         maxSize = 2
      )
      private String agreementOperateType;
      @V(
         desc = "资金池产品类型",
         notNull = true,
         length = "20",
         remark = "资金池产品类型",
         maxSize = 20
      )
      private String pcpProdType;
      @V(
         desc = "备案通知书编写",
         notNull = false,
         length = "50",
         remark = "备案通知书编写",
         maxSize = 50
      )
      private String recordNoticeNo;
      @V(
         desc = "数组",
         notNull = false,
         remark = "数组"
      )
      private List<Core1200100708In.Body.SubAcctArray> subAcctArray;

      public String getClientNo() {
         return this.clientNo;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getSignId() {
         return this.signId;
      }

      public String getAgreementOperateType() {
         return this.agreementOperateType;
      }

      public String getPcpProdType() {
         return this.pcpProdType;
      }

      public String getRecordNoticeNo() {
         return this.recordNoticeNo;
      }

      public List<Core1200100708In.Body.SubAcctArray> getSubAcctArray() {
         return this.subAcctArray;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setSignId(String signId) {
         this.signId = signId;
      }

      public void setAgreementOperateType(String agreementOperateType) {
         this.agreementOperateType = agreementOperateType;
      }

      public void setPcpProdType(String pcpProdType) {
         this.pcpProdType = pcpProdType;
      }

      public void setRecordNoticeNo(String recordNoticeNo) {
         this.recordNoticeNo = recordNoticeNo;
      }

      public void setSubAcctArray(List<Core1200100708In.Body.SubAcctArray> subAcctArray) {
         this.subAcctArray = subAcctArray;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1200100708In.Body)) {
            return false;
         } else {
            Core1200100708In.Body other = (Core1200100708In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$clientNo = this.getClientNo();
               Object other$clientNo = other.getClientNo();
               if (this$clientNo == null) {
                  if (other$clientNo != null) {
                     return false;
                  }
               } else if (!this$clientNo.equals(other$clientNo)) {
                  return false;
               }

               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo != null) {
                     return false;
                  }
               } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               label110: {
                  Object this$acctCcy = this.getAcctCcy();
                  Object other$acctCcy = other.getAcctCcy();
                  if (this$acctCcy == null) {
                     if (other$acctCcy == null) {
                        break label110;
                     }
                  } else if (this$acctCcy.equals(other$acctCcy)) {
                     break label110;
                  }

                  return false;
               }

               label103: {
                  Object this$acctSeqNo = this.getAcctSeqNo();
                  Object other$acctSeqNo = other.getAcctSeqNo();
                  if (this$acctSeqNo == null) {
                     if (other$acctSeqNo == null) {
                        break label103;
                     }
                  } else if (this$acctSeqNo.equals(other$acctSeqNo)) {
                     break label103;
                  }

                  return false;
               }

               Object this$signId = this.getSignId();
               Object other$signId = other.getSignId();
               if (this$signId == null) {
                  if (other$signId != null) {
                     return false;
                  }
               } else if (!this$signId.equals(other$signId)) {
                  return false;
               }

               label89: {
                  Object this$agreementOperateType = this.getAgreementOperateType();
                  Object other$agreementOperateType = other.getAgreementOperateType();
                  if (this$agreementOperateType == null) {
                     if (other$agreementOperateType == null) {
                        break label89;
                     }
                  } else if (this$agreementOperateType.equals(other$agreementOperateType)) {
                     break label89;
                  }

                  return false;
               }

               label82: {
                  Object this$pcpProdType = this.getPcpProdType();
                  Object other$pcpProdType = other.getPcpProdType();
                  if (this$pcpProdType == null) {
                     if (other$pcpProdType == null) {
                        break label82;
                     }
                  } else if (this$pcpProdType.equals(other$pcpProdType)) {
                     break label82;
                  }

                  return false;
               }

               Object this$recordNoticeNo = this.getRecordNoticeNo();
               Object other$recordNoticeNo = other.getRecordNoticeNo();
               if (this$recordNoticeNo == null) {
                  if (other$recordNoticeNo != null) {
                     return false;
                  }
               } else if (!this$recordNoticeNo.equals(other$recordNoticeNo)) {
                  return false;
               }

               Object this$subAcctArray = this.getSubAcctArray();
               Object other$subAcctArray = other.getSubAcctArray();
               if (this$subAcctArray == null) {
                  if (other$subAcctArray != null) {
                     return false;
                  }
               } else if (!this$subAcctArray.equals(other$subAcctArray)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1200100708In.Body;
      }
      public String toString() {
         return "Core1200100708In.Body(clientNo=" + this.getClientNo() + ", baseAcctNo=" + this.getBaseAcctNo() + ", prodType=" + this.getProdType() + ", acctCcy=" + this.getAcctCcy() + ", acctSeqNo=" + this.getAcctSeqNo() + ", signId=" + this.getSignId() + ", agreementOperateType=" + this.getAgreementOperateType() + ", pcpProdType=" + this.getPcpProdType() + ", recordNoticeNo=" + this.getRecordNoticeNo() + ", subAcctArray=" + this.getSubAcctArray() + ")";
      }

      public static class SubAcctArray {
         @V(
            desc = "产品类型",
            notNull = false,
            length = "20",
            remark = "产品类型",
            maxSize = 20
         )
         private String prodType;
         @V(
            desc = "账号/卡号",
            notNull = true,
            length = "50",
            remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
            maxSize = 50
         )
         private String baseAcctNo;
         @V(
            desc = "账户序号",
            notNull = false,
            length = "5",
            remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
            maxSize = 5
         )
         private String acctSeqNo;
         @V(
            desc = "账户币种",
            notNull = false,
            length = "3",
            remark = "账户币种 对于AIO账户和一本通账户",
            maxSize = 3
         )
         private String acctCcy;
         @V(
            desc = "账户名称",
            notNull = false,
            length = "200",
            remark = "账户名称，一般指中文账户名称",
            maxSize = 200
         )
         private String acctName;
         @V(
            desc = "是否行内行外",
            notNull = true,
            length = "1",
            inDesc = "I-行内,O-行外",
            remark = "是否行内行外",
            maxSize = 1
         )
         private String bankInOut;
         @V(
            desc = "境内境外标志",
            notNull = false,
            length = "1",
            inDesc = "I-境内,O-境外",
            remark = "境内境外标志",
            maxSize = 1
         )
         private String inlandOffshore;
         @V(
            desc = "银行行号",
            notNull = false,
            length = "20",
            remark = "银行行号",
            maxSize = 20
         )
         private String bankNo;
         @V(
            desc = "银行名称",
            notNull = false,
            length = "50",
            remark = "银行名称",
            maxSize = 50
         )
         private String bankName;
         @V(
            desc = "外汇项目",
            notNull = false,
            length = "50",
            remark = "跨境资金池登记子账户外汇项目",
            maxSize = 50
         )
         private String fxProject;

         public String getProdType() {
            return this.prodType;
         }

         public String getBaseAcctNo() {
            return this.baseAcctNo;
         }

         public String getAcctSeqNo() {
            return this.acctSeqNo;
         }

         public String getAcctCcy() {
            return this.acctCcy;
         }

         public String getAcctName() {
            return this.acctName;
         }

         public String getBankInOut() {
            return this.bankInOut;
         }

         public String getInlandOffshore() {
            return this.inlandOffshore;
         }

         public String getBankNo() {
            return this.bankNo;
         }

         public String getBankName() {
            return this.bankName;
         }

         public String getFxProject() {
            return this.fxProject;
         }

         public void setProdType(String prodType) {
            this.prodType = prodType;
         }

         public void setBaseAcctNo(String baseAcctNo) {
            this.baseAcctNo = baseAcctNo;
         }

         public void setAcctSeqNo(String acctSeqNo) {
            this.acctSeqNo = acctSeqNo;
         }

         public void setAcctCcy(String acctCcy) {
            this.acctCcy = acctCcy;
         }

         public void setAcctName(String acctName) {
            this.acctName = acctName;
         }

         public void setBankInOut(String bankInOut) {
            this.bankInOut = bankInOut;
         }

         public void setInlandOffshore(String inlandOffshore) {
            this.inlandOffshore = inlandOffshore;
         }

         public void setBankNo(String bankNo) {
            this.bankNo = bankNo;
         }

         public void setBankName(String bankName) {
            this.bankName = bankName;
         }

         public void setFxProject(String fxProject) {
            this.fxProject = fxProject;
         }

         public boolean equals(Object o) {
            if (o == this) {
               return true;
            } else if (!(o instanceof Core1200100708In.Body.SubAcctArray)) {
               return false;
            } else {
               Core1200100708In.Body.SubAcctArray other = (Core1200100708In.Body.SubAcctArray)o;
               if (!other.canEqual(this)) {
                  return false;
               } else {
                  Object this$prodType = this.getProdType();
                  Object other$prodType = other.getProdType();
                  if (this$prodType == null) {
                     if (other$prodType != null) {
                        return false;
                     }
                  } else if (!this$prodType.equals(other$prodType)) {
                     return false;
                  }

                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo != null) {
                        return false;
                     }
                  } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
                     return false;
                  }

                  Object this$acctSeqNo = this.getAcctSeqNo();
                  Object other$acctSeqNo = other.getAcctSeqNo();
                  if (this$acctSeqNo == null) {
                     if (other$acctSeqNo != null) {
                        return false;
                     }
                  } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                     return false;
                  }

                  label110: {
                     Object this$acctCcy = this.getAcctCcy();
                     Object other$acctCcy = other.getAcctCcy();
                     if (this$acctCcy == null) {
                        if (other$acctCcy == null) {
                           break label110;
                        }
                     } else if (this$acctCcy.equals(other$acctCcy)) {
                        break label110;
                     }

                     return false;
                  }

                  label103: {
                     Object this$acctName = this.getAcctName();
                     Object other$acctName = other.getAcctName();
                     if (this$acctName == null) {
                        if (other$acctName == null) {
                           break label103;
                        }
                     } else if (this$acctName.equals(other$acctName)) {
                        break label103;
                     }

                     return false;
                  }

                  Object this$bankInOut = this.getBankInOut();
                  Object other$bankInOut = other.getBankInOut();
                  if (this$bankInOut == null) {
                     if (other$bankInOut != null) {
                        return false;
                     }
                  } else if (!this$bankInOut.equals(other$bankInOut)) {
                     return false;
                  }

                  label89: {
                     Object this$inlandOffshore = this.getInlandOffshore();
                     Object other$inlandOffshore = other.getInlandOffshore();
                     if (this$inlandOffshore == null) {
                        if (other$inlandOffshore == null) {
                           break label89;
                        }
                     } else if (this$inlandOffshore.equals(other$inlandOffshore)) {
                        break label89;
                     }

                     return false;
                  }

                  label82: {
                     Object this$bankNo = this.getBankNo();
                     Object other$bankNo = other.getBankNo();
                     if (this$bankNo == null) {
                        if (other$bankNo == null) {
                           break label82;
                        }
                     } else if (this$bankNo.equals(other$bankNo)) {
                        break label82;
                     }

                     return false;
                  }

                  Object this$bankName = this.getBankName();
                  Object other$bankName = other.getBankName();
                  if (this$bankName == null) {
                     if (other$bankName != null) {
                        return false;
                     }
                  } else if (!this$bankName.equals(other$bankName)) {
                     return false;
                  }

                  Object this$fxProject = this.getFxProject();
                  Object other$fxProject = other.getFxProject();
                  if (this$fxProject == null) {
                     if (other$fxProject != null) {
                        return false;
                     }
                  } else if (!this$fxProject.equals(other$fxProject)) {
                     return false;
                  }

                  return true;
               }
            }
         }

         protected boolean canEqual(Object other) {
            return other instanceof Core1200100708In.Body.SubAcctArray;
         }
         public String toString() {
            return "Core1200100708In.Body.SubAcctArray(prodType=" + this.getProdType() + ", baseAcctNo=" + this.getBaseAcctNo() + ", acctSeqNo=" + this.getAcctSeqNo() + ", acctCcy=" + this.getAcctCcy() + ", acctName=" + this.getAcctName() + ", bankInOut=" + this.getBankInOut() + ", inlandOffshore=" + this.getInlandOffshore() + ", bankNo=" + this.getBankNo() + ", bankName=" + this.getBankName() + ", fxProject=" + this.getFxProject() + ")";
         }
      }
   }
}
