package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10000113In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10000113Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore10000113 {
   String URL = "/rb/fin/trans/amend";


   @ApiRemark("华兴项目新增转账时转出方或转入方发生错误的更正处理")
   @ApiDesc("华兴项目新增转账时转出方或转入方发生错误的更正处理")
   @EnsembleElements(
      serviceCode = "Core",
      messageType = "1000",
      messageCode = "0113"
   )
   @BusinessCategory("转账时转出方或转入方发生错误")
   @FunctionCategory("RB58-异常处理")
   @ConsumeSys("TLE")
   Core10000113Out runService(Core10000113In var1);
}
