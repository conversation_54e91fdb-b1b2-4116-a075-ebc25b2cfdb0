package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;

@MessageOut
public class Core1200023402Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "票据密押",
      notNull = false,
      length = "200",
      remark = "票据密押",
      maxSize = 200
   )
   private String encrypKey;

   public String getEncrypKey() {
      return this.encrypKey;
   }

   public void setEncrypKey(String encrypKey) {
      this.encrypKey = encrypKey;
   }

   public String toString() {
      return "Core1200023402Out(encrypKey=" + this.getEncrypKey() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200023402Out)) {
         return false;
      } else {
         Core1200023402Out other = (Core1200023402Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$encrypKey = this.getEncrypKey();
            Object other$encrypKey = other.getEncrypKey();
            if (this$encrypKey == null) {
               if (other$encrypKey != null) {
                  return false;
               }
            } else if (!this$encrypKey.equals(other$encrypKey)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200023402Out;
   }
}
