package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;

@MessageOut
public class Core1200100712Out extends EnsResponse {
   private static final long serialVersionUID = 1L;

   public String toString() {
      return "Core1200100712Out()";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200100712Out)) {
         return false;
      } else {
         Core1200100712Out other = (Core1200100712Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else {
            return super.equals(o);
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200100712Out;
   }
}
