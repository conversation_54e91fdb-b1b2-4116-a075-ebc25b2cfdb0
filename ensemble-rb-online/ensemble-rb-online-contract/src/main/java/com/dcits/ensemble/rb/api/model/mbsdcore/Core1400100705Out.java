package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400100705Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "资金池账户组ID",
      notNull = false,
      length = "30",
      remark = "资金池账户组ID",
      maxSize = 30
   )
   private String pcpGroupId;
   @V(
      desc = "结算账号",
      notNull = false,
      length = "50",
      remark = "结算账号",
      maxSize = 50
   )
   private String settleBaseAcctNo;
   @V(
      desc = "结算账户序号",
      notNull = false,
      length = "5",
      remark = "结算账户序号",
      maxSize = 5
   )
   private String settleAcctSeqNo;
   @V(
      desc = "结算账户产品类型",
      notNull = false,
      length = "20",
      remark = "结算账户产品类型",
      maxSize = 20
   )
   private String settleProdType;
   @V(
      desc = "结算账户币种",
      notNull = false,
      length = "3",
      remark = "结算账户币种",
      maxSize = 3
   )
   private String settleAcctCcy;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100705Out.AcctArray> acctArray;
   @V(
      desc = "账号/卡号",
      notNull = false,
      length = "50",
      remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
      maxSize = 50
   )
   private String baseAcctNo;
   @V(
      desc = "客户名称",
      notNull = false,
      length = "200",
      remark = "客户名称",
      maxSize = 200
   )
   private String clientName;
   @V(
      desc = "账户序号",
      notNull = false,
      length = "5",
      remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
      maxSize = 5
   )
   private String acctSeqNo;
   @V(
      desc = "产品类型",
      notNull = false,
      length = "20",
      remark = "产品类型",
      maxSize = 20
   )
   private String prodType;
   @V(
      desc = "账户币种",
      notNull = false,
      length = "3",
      remark = "账户币种 对于AIO账户和一本通账户",
      maxSize = 3
   )
   private String acctCcy;
   @V(
      desc = "执行利率",
      notNull = false,
      length = "15",
      remark = "执行利率",
      decimalLength = 8,
      precision = 8
   )
   private BigDecimal realRate;
   @V(
      desc = "审批状态",
      notNull = false,
      length = "1",
      remark = "审批状态",
      maxSize = 1
   )
   private String approveStatus;
   @V(
      desc = "审批状态",
      notNull = false,
      length = "1",
      remark = "久悬户处理审批状态",
      maxSize = 1
   )
   private String approvalStatus;
   @V(
      desc = "协议利率",
      notNull = false,
      length = "15",
      remark = "协议利率",
      decimalLength = 8,
      precision = 8
   )
   private BigDecimal agreeIntRate;

   public String getPcpGroupId() {
      return this.pcpGroupId;
   }

   public String getSettleBaseAcctNo() {
      return this.settleBaseAcctNo;
   }

   public String getSettleAcctSeqNo() {
      return this.settleAcctSeqNo;
   }

   public String getSettleProdType() {
      return this.settleProdType;
   }

   public String getSettleAcctCcy() {
      return this.settleAcctCcy;
   }

   public List<Core1400100705Out.AcctArray> getAcctArray() {
      return this.acctArray;
   }

   public String getBaseAcctNo() {
      return this.baseAcctNo;
   }

   public String getClientName() {
      return this.clientName;
   }

   public String getAcctSeqNo() {
      return this.acctSeqNo;
   }

   public String getProdType() {
      return this.prodType;
   }

   public String getAcctCcy() {
      return this.acctCcy;
   }

   public BigDecimal getRealRate() {
      return this.realRate;
   }

   public String getApproveStatus() {
      return this.approveStatus;
   }

   public String getApprovalStatus() {
      return this.approvalStatus;
   }

   public BigDecimal getAgreeIntRate() {
      return this.agreeIntRate;
   }

   public void setPcpGroupId(String pcpGroupId) {
      this.pcpGroupId = pcpGroupId;
   }

   public void setSettleBaseAcctNo(String settleBaseAcctNo) {
      this.settleBaseAcctNo = settleBaseAcctNo;
   }

   public void setSettleAcctSeqNo(String settleAcctSeqNo) {
      this.settleAcctSeqNo = settleAcctSeqNo;
   }

   public void setSettleProdType(String settleProdType) {
      this.settleProdType = settleProdType;
   }

   public void setSettleAcctCcy(String settleAcctCcy) {
      this.settleAcctCcy = settleAcctCcy;
   }

   public void setAcctArray(List<Core1400100705Out.AcctArray> acctArray) {
      this.acctArray = acctArray;
   }

   public void setBaseAcctNo(String baseAcctNo) {
      this.baseAcctNo = baseAcctNo;
   }

   public void setClientName(String clientName) {
      this.clientName = clientName;
   }

   public void setAcctSeqNo(String acctSeqNo) {
      this.acctSeqNo = acctSeqNo;
   }

   public void setProdType(String prodType) {
      this.prodType = prodType;
   }

   public void setAcctCcy(String acctCcy) {
      this.acctCcy = acctCcy;
   }

   public void setRealRate(BigDecimal realRate) {
      this.realRate = realRate;
   }

   public void setApproveStatus(String approveStatus) {
      this.approveStatus = approveStatus;
   }

   public void setApprovalStatus(String approvalStatus) {
      this.approvalStatus = approvalStatus;
   }

   public void setAgreeIntRate(BigDecimal agreeIntRate) {
      this.agreeIntRate = agreeIntRate;
   }

   public String toString() {
      return "Core1400100705Out(pcpGroupId=" + this.getPcpGroupId() + ", settleBaseAcctNo=" + this.getSettleBaseAcctNo() + ", settleAcctSeqNo=" + this.getSettleAcctSeqNo() + ", settleProdType=" + this.getSettleProdType() + ", settleAcctCcy=" + this.getSettleAcctCcy() + ", acctArray=" + this.getAcctArray() + ", baseAcctNo=" + this.getBaseAcctNo() + ", clientName=" + this.getClientName() + ", acctSeqNo=" + this.getAcctSeqNo() + ", prodType=" + this.getProdType() + ", acctCcy=" + this.getAcctCcy() + ", realRate=" + this.getRealRate() + ", approveStatus=" + this.getApproveStatus() + ", approvalStatus=" + this.getApprovalStatus() + ", agreeIntRate=" + this.getAgreeIntRate() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100705Out)) {
         return false;
      } else {
         Core1400100705Out other = (Core1400100705Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            label193: {
               Object this$pcpGroupId = this.getPcpGroupId();
               Object other$pcpGroupId = other.getPcpGroupId();
               if (this$pcpGroupId == null) {
                  if (other$pcpGroupId == null) {
                     break label193;
                  }
               } else if (this$pcpGroupId.equals(other$pcpGroupId)) {
                  break label193;
               }

               return false;
            }

            Object this$settleBaseAcctNo = this.getSettleBaseAcctNo();
            Object other$settleBaseAcctNo = other.getSettleBaseAcctNo();
            if (this$settleBaseAcctNo == null) {
               if (other$settleBaseAcctNo != null) {
                  return false;
               }
            } else if (!this$settleBaseAcctNo.equals(other$settleBaseAcctNo)) {
               return false;
            }

            Object this$settleAcctSeqNo = this.getSettleAcctSeqNo();
            Object other$settleAcctSeqNo = other.getSettleAcctSeqNo();
            if (this$settleAcctSeqNo == null) {
               if (other$settleAcctSeqNo != null) {
                  return false;
               }
            } else if (!this$settleAcctSeqNo.equals(other$settleAcctSeqNo)) {
               return false;
            }

            label172: {
               Object this$settleProdType = this.getSettleProdType();
               Object other$settleProdType = other.getSettleProdType();
               if (this$settleProdType == null) {
                  if (other$settleProdType == null) {
                     break label172;
                  }
               } else if (this$settleProdType.equals(other$settleProdType)) {
                  break label172;
               }

               return false;
            }

            Object this$settleAcctCcy = this.getSettleAcctCcy();
            Object other$settleAcctCcy = other.getSettleAcctCcy();
            if (this$settleAcctCcy == null) {
               if (other$settleAcctCcy != null) {
                  return false;
               }
            } else if (!this$settleAcctCcy.equals(other$settleAcctCcy)) {
               return false;
            }

            Object this$acctArray = this.getAcctArray();
            Object other$acctArray = other.getAcctArray();
            if (this$acctArray == null) {
               if (other$acctArray != null) {
                  return false;
               }
            } else if (!this$acctArray.equals(other$acctArray)) {
               return false;
            }

            label151: {
               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo == null) {
                     break label151;
                  }
               } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                  break label151;
               }

               return false;
            }

            Object this$clientName = this.getClientName();
            Object other$clientName = other.getClientName();
            if (this$clientName == null) {
               if (other$clientName != null) {
                  return false;
               }
            } else if (!this$clientName.equals(other$clientName)) {
               return false;
            }

            label137: {
               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo == null) {
                     break label137;
                  }
               } else if (this$acctSeqNo.equals(other$acctSeqNo)) {
                  break label137;
               }

               return false;
            }

            Object this$prodType = this.getProdType();
            Object other$prodType = other.getProdType();
            if (this$prodType == null) {
               if (other$prodType != null) {
                  return false;
               }
            } else if (!this$prodType.equals(other$prodType)) {
               return false;
            }

            label123: {
               Object this$acctCcy = this.getAcctCcy();
               Object other$acctCcy = other.getAcctCcy();
               if (this$acctCcy == null) {
                  if (other$acctCcy == null) {
                     break label123;
                  }
               } else if (this$acctCcy.equals(other$acctCcy)) {
                  break label123;
               }

               return false;
            }

            Object this$realRate = this.getRealRate();
            Object other$realRate = other.getRealRate();
            if (this$realRate == null) {
               if (other$realRate != null) {
                  return false;
               }
            } else if (!this$realRate.equals(other$realRate)) {
               return false;
            }

            label109: {
               Object this$approveStatus = this.getApproveStatus();
               Object other$approveStatus = other.getApproveStatus();
               if (this$approveStatus == null) {
                  if (other$approveStatus == null) {
                     break label109;
                  }
               } else if (this$approveStatus.equals(other$approveStatus)) {
                  break label109;
               }

               return false;
            }

            label102: {
               Object this$approvalStatus = this.getApprovalStatus();
               Object other$approvalStatus = other.getApprovalStatus();
               if (this$approvalStatus == null) {
                  if (other$approvalStatus == null) {
                     break label102;
                  }
               } else if (this$approvalStatus.equals(other$approvalStatus)) {
                  break label102;
               }

               return false;
            }

            Object this$agreeIntRate = this.getAgreeIntRate();
            Object other$agreeIntRate = other.getAgreeIntRate();
            if (this$agreeIntRate == null) {
               if (other$agreeIntRate != null) {
                  return false;
               }
            } else if (!this$agreeIntRate.equals(other$agreeIntRate)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100705Out;
   }
   public static class AcctArray {
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "上级账户（组主账户）",
         notNull = false,
         length = "50",
         remark = "上级账户（组主账户）",
         maxSize = 50
      )
      private String upperBaseAcctNo;
      @V(
         desc = "执行利率",
         notNull = false,
         length = "15",
         remark = "执行利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal realRate;
      @V(
         desc = "客户名称",
         notNull = false,
         length = "200",
         remark = "客户名称",
         maxSize = 200
      )
      private String clientName;
      @V(
         desc = "数组",
         notNull = false,
         remark = "数组"
      )
      private List<Core1400100705Out.AcctArray.ConRateArray> conRateArray;
      @V(
         desc = "审批状态",
         notNull = false,
         length = "1",
         remark = "审批状态",
         maxSize = 1
      )
      private String approveStatus;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getUpperBaseAcctNo() {
         return this.upperBaseAcctNo;
      }

      public BigDecimal getRealRate() {
         return this.realRate;
      }

      public String getClientName() {
         return this.clientName;
      }

      public List<Core1400100705Out.AcctArray.ConRateArray> getConRateArray() {
         return this.conRateArray;
      }

      public String getApproveStatus() {
         return this.approveStatus;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setUpperBaseAcctNo(String upperBaseAcctNo) {
         this.upperBaseAcctNo = upperBaseAcctNo;
      }

      public void setRealRate(BigDecimal realRate) {
         this.realRate = realRate;
      }

      public void setClientName(String clientName) {
         this.clientName = clientName;
      }

      public void setConRateArray(List<Core1400100705Out.AcctArray.ConRateArray> conRateArray) {
         this.conRateArray = conRateArray;
      }

      public void setApproveStatus(String approveStatus) {
         this.approveStatus = approveStatus;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100705Out.AcctArray)) {
            return false;
         } else {
            Core1400100705Out.AcctArray other = (Core1400100705Out.AcctArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label119: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label119;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label119;
                  }

                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               label105: {
                  Object this$acctCcy = this.getAcctCcy();
                  Object other$acctCcy = other.getAcctCcy();
                  if (this$acctCcy == null) {
                     if (other$acctCcy == null) {
                        break label105;
                     }
                  } else if (this$acctCcy.equals(other$acctCcy)) {
                     break label105;
                  }

                  return false;
               }

               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo != null) {
                     return false;
                  }
               } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                  return false;
               }

               label91: {
                  Object this$upperBaseAcctNo = this.getUpperBaseAcctNo();
                  Object other$upperBaseAcctNo = other.getUpperBaseAcctNo();
                  if (this$upperBaseAcctNo == null) {
                     if (other$upperBaseAcctNo == null) {
                        break label91;
                     }
                  } else if (this$upperBaseAcctNo.equals(other$upperBaseAcctNo)) {
                     break label91;
                  }

                  return false;
               }

               Object this$realRate = this.getRealRate();
               Object other$realRate = other.getRealRate();
               if (this$realRate == null) {
                  if (other$realRate != null) {
                     return false;
                  }
               } else if (!this$realRate.equals(other$realRate)) {
                  return false;
               }

               label77: {
                  Object this$clientName = this.getClientName();
                  Object other$clientName = other.getClientName();
                  if (this$clientName == null) {
                     if (other$clientName == null) {
                        break label77;
                     }
                  } else if (this$clientName.equals(other$clientName)) {
                     break label77;
                  }

                  return false;
               }

               label70: {
                  Object this$conRateArray = this.getConRateArray();
                  Object other$conRateArray = other.getConRateArray();
                  if (this$conRateArray == null) {
                     if (other$conRateArray == null) {
                        break label70;
                     }
                  } else if (this$conRateArray.equals(other$conRateArray)) {
                     break label70;
                  }

                  return false;
               }

               Object this$approveStatus = this.getApproveStatus();
               Object other$approveStatus = other.getApproveStatus();
               if (this$approveStatus == null) {
                  if (other$approveStatus != null) {
                     return false;
                  }
               } else if (!this$approveStatus.equals(other$approveStatus)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100705Out.AcctArray;
      }
      public String toString() {
         return "Core1400100705Out.AcctArray(baseAcctNo=" + this.getBaseAcctNo() + ", prodType=" + this.getProdType() + ", acctCcy=" + this.getAcctCcy() + ", acctSeqNo=" + this.getAcctSeqNo() + ", upperBaseAcctNo=" + this.getUpperBaseAcctNo() + ", realRate=" + this.getRealRate() + ", clientName=" + this.getClientName() + ", conRateArray=" + this.getConRateArray() + ", approveStatus=" + this.getApproveStatus() + ")";
      }

      public static class ConRateArray {
         @V(
            desc = "执行利率",
            notNull = false,
            length = "15",
            remark = "执行利率",
            decimalLength = 8,
            precision = 8
         )
         private BigDecimal realRate;
         @V(
            desc = "靠档金额",
            notNull = false,
            length = "17",
            remark = "靠档金额",
            decimalLength = 2,
            precision = 2
         )
         private BigDecimal nearAmt;

         public BigDecimal getRealRate() {
            return this.realRate;
         }

         public BigDecimal getNearAmt() {
            return this.nearAmt;
         }

         public void setRealRate(BigDecimal realRate) {
            this.realRate = realRate;
         }

         public void setNearAmt(BigDecimal nearAmt) {
            this.nearAmt = nearAmt;
         }

         public boolean equals(Object o) {
            if (o == this) {
               return true;
            } else if (!(o instanceof Core1400100705Out.AcctArray.ConRateArray)) {
               return false;
            } else {
               Core1400100705Out.AcctArray.ConRateArray other = (Core1400100705Out.AcctArray.ConRateArray)o;
               if (!other.canEqual(this)) {
                  return false;
               } else {
                  Object this$realRate = this.getRealRate();
                  Object other$realRate = other.getRealRate();
                  if (this$realRate == null) {
                     if (other$realRate != null) {
                        return false;
                     }
                  } else if (!this$realRate.equals(other$realRate)) {
                     return false;
                  }

                  Object this$nearAmt = this.getNearAmt();
                  Object other$nearAmt = other.getNearAmt();
                  if (this$nearAmt == null) {
                     if (other$nearAmt != null) {
                        return false;
                     }
                  } else if (!this$nearAmt.equals(other$nearAmt)) {
                     return false;
                  }

                  return true;
               }
            }
         }

         protected boolean canEqual(Object other) {
            return other instanceof Core1400100705Out.AcctArray.ConRateArray;
         }
         public String toString() {
            return "Core1400100705Out.AcctArray.ConRateArray(realRate=" + this.getRealRate() + ", nearAmt=" + this.getNearAmt() + ")";
         }
      }
   }
}
