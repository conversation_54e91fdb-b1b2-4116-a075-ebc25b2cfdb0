package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400033404In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400033404In.Body body;

   public Core1400033404In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400033404In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400033404In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400033404In)) {
         return false;
      } else {
         Core1400033404In other = (Core1400033404In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400033404In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "挂失申请书编号",
         notNull = false,
         length = "50",
         remark = "挂失申请书编号",
         maxSize = 50
      )
      private String lossNo;
      @V(
         desc = "本票挂失查询类型",
         notNull = false,
         length = "2",
         in = "01,02",
         inDesc = "01-根据表面编号查询02-根据挂失编号查询",
         remark = "操作类型",
         maxSize = 2
      )
      private String pnUnlostQueryType;
      @V(
         desc = "票据号码",
         notNull = false,
         length = "50",
         remark = "票据号码",
         maxSize = 50
      )
      private String billNo;

      public String getLossNo() {
         return this.lossNo;
      }

      public String getPnUnlostQueryType() {
         return this.pnUnlostQueryType;
      }

      public String getBillNo() {
         return this.billNo;
      }

      public void setLossNo(String lossNo) {
         this.lossNo = lossNo;
      }

      public void setPnUnlostQueryType(String pnUnlostQueryType) {
         this.pnUnlostQueryType = pnUnlostQueryType;
      }

      public void setBillNo(String billNo) {
         this.billNo = billNo;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400033404In.Body)) {
            return false;
         } else {
            Core1400033404In.Body other = (Core1400033404In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label47: {
                  Object this$lossNo = this.getLossNo();
                  Object other$lossNo = other.getLossNo();
                  if (this$lossNo == null) {
                     if (other$lossNo == null) {
                        break label47;
                     }
                  } else if (this$lossNo.equals(other$lossNo)) {
                     break label47;
                  }

                  return false;
               }

               Object this$pnUnlostQueryType = this.getPnUnlostQueryType();
               Object other$pnUnlostQueryType = other.getPnUnlostQueryType();
               if (this$pnUnlostQueryType == null) {
                  if (other$pnUnlostQueryType != null) {
                     return false;
                  }
               } else if (!this$pnUnlostQueryType.equals(other$pnUnlostQueryType)) {
                  return false;
               }

               Object this$billNo = this.getBillNo();
               Object other$billNo = other.getBillNo();
               if (this$billNo == null) {
                  if (other$billNo != null) {
                     return false;
                  }
               } else if (!this$billNo.equals(other$billNo)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400033404In.Body;
      }
      public String toString() {
         return "Core1400033404In.Body(lossNo=" + this.getLossNo() + ", pnUnlostQueryType=" + this.getPnUnlostQueryType() + ", billNo=" + this.getBillNo() + ")";
      }
   }
}
