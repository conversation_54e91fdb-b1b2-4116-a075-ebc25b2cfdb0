package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;

@MessageIn
public class Core1200100220In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1200100220In.Body body;

   public Core1200100220In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1200100220In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1200100220In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200100220In)) {
         return false;
      } else {
         Core1200100220In other = (Core1200100220In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200100220In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "申请机构",
         notNull = true,
         length = "50",
         remark = "申请机构",
         maxSize = 50
      )
      private String applyBranch;
      @V(
         desc = "预约总金额",
         notNull = true,
         length = "17",
         remark = "预约总金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal applyAmt;
      @V(
         desc = "发行年度",
         notNull = true,
         length = "5",
         remark = "发行年度",
         maxSize = 5
      )
      private String issueYear;
      @V(
         desc = "审批单号",
         notNull = true,
         length = "50",
         remark = "审批单号",
         maxSize = 50
      )
      private String approvalNo;
      @V(
         desc = "操作类型",
         notNull = true,
         length = "1",
         inDesc = "A-新增,D-解除,U-更新,C-确认,H-交接,O-出库",
         remark = "操作类型",
         maxSize = 1
      )
      private String operateFlag;
      @V(
         desc = "期次代码",
         notNull = false,
         length = "50",
         remark = "期次代码",
         maxSize = 50
      )
      private String stageCode;
      @V(
         desc = "所属机构号",
         notNull = false,
         length = "50",
         remark = "所属机构号",
         maxSize = 50
      )
      private String branch;
      @V(
         desc = "利率审批单单号",
         notNull = false,
         length = "50",
         remark = "利率审批单单号",
         maxSize = 50
      )
      private String intRateFormNo;
      @V(
         desc = "限额",
         notNull = false,
         length = "17",
         remark = "限额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal limit;
      @V(
         desc = "额度类别",
         notNull = false,
         length = "20",
         inDesc = "PRE-预约额度,ELE-电子渠道额度",
         remark = "额度类别",
         maxSize = 20
      )
      private String limitClass;

      public String getApplyBranch() {
         return this.applyBranch;
      }

      public BigDecimal getApplyAmt() {
         return this.applyAmt;
      }

      public String getIssueYear() {
         return this.issueYear;
      }

      public String getApprovalNo() {
         return this.approvalNo;
      }

      public String getOperateFlag() {
         return this.operateFlag;
      }

      public String getStageCode() {
         return this.stageCode;
      }

      public String getBranch() {
         return this.branch;
      }

      public String getIntRateFormNo() {
         return this.intRateFormNo;
      }

      public BigDecimal getLimit() {
         return this.limit;
      }

      public String getLimitClass() {
         return this.limitClass;
      }

      public void setApplyBranch(String applyBranch) {
         this.applyBranch = applyBranch;
      }

      public void setApplyAmt(BigDecimal applyAmt) {
         this.applyAmt = applyAmt;
      }

      public void setIssueYear(String issueYear) {
         this.issueYear = issueYear;
      }

      public void setApprovalNo(String approvalNo) {
         this.approvalNo = approvalNo;
      }

      public void setOperateFlag(String operateFlag) {
         this.operateFlag = operateFlag;
      }

      public void setStageCode(String stageCode) {
         this.stageCode = stageCode;
      }

      public void setBranch(String branch) {
         this.branch = branch;
      }

      public void setIntRateFormNo(String intRateFormNo) {
         this.intRateFormNo = intRateFormNo;
      }

      public void setLimit(BigDecimal limit) {
         this.limit = limit;
      }

      public void setLimitClass(String limitClass) {
         this.limitClass = limitClass;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1200100220In.Body)) {
            return false;
         } else {
            Core1200100220In.Body other = (Core1200100220In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$applyBranch = this.getApplyBranch();
               Object other$applyBranch = other.getApplyBranch();
               if (this$applyBranch == null) {
                  if (other$applyBranch != null) {
                     return false;
                  }
               } else if (!this$applyBranch.equals(other$applyBranch)) {
                  return false;
               }

               Object this$applyAmt = this.getApplyAmt();
               Object other$applyAmt = other.getApplyAmt();
               if (this$applyAmt == null) {
                  if (other$applyAmt != null) {
                     return false;
                  }
               } else if (!this$applyAmt.equals(other$applyAmt)) {
                  return false;
               }

               Object this$issueYear = this.getIssueYear();
               Object other$issueYear = other.getIssueYear();
               if (this$issueYear == null) {
                  if (other$issueYear != null) {
                     return false;
                  }
               } else if (!this$issueYear.equals(other$issueYear)) {
                  return false;
               }

               label110: {
                  Object this$approvalNo = this.getApprovalNo();
                  Object other$approvalNo = other.getApprovalNo();
                  if (this$approvalNo == null) {
                     if (other$approvalNo == null) {
                        break label110;
                     }
                  } else if (this$approvalNo.equals(other$approvalNo)) {
                     break label110;
                  }

                  return false;
               }

               label103: {
                  Object this$operateFlag = this.getOperateFlag();
                  Object other$operateFlag = other.getOperateFlag();
                  if (this$operateFlag == null) {
                     if (other$operateFlag == null) {
                        break label103;
                     }
                  } else if (this$operateFlag.equals(other$operateFlag)) {
                     break label103;
                  }

                  return false;
               }

               Object this$stageCode = this.getStageCode();
               Object other$stageCode = other.getStageCode();
               if (this$stageCode == null) {
                  if (other$stageCode != null) {
                     return false;
                  }
               } else if (!this$stageCode.equals(other$stageCode)) {
                  return false;
               }

               label89: {
                  Object this$branch = this.getBranch();
                  Object other$branch = other.getBranch();
                  if (this$branch == null) {
                     if (other$branch == null) {
                        break label89;
                     }
                  } else if (this$branch.equals(other$branch)) {
                     break label89;
                  }

                  return false;
               }

               label82: {
                  Object this$intRateFormNo = this.getIntRateFormNo();
                  Object other$intRateFormNo = other.getIntRateFormNo();
                  if (this$intRateFormNo == null) {
                     if (other$intRateFormNo == null) {
                        break label82;
                     }
                  } else if (this$intRateFormNo.equals(other$intRateFormNo)) {
                     break label82;
                  }

                  return false;
               }

               Object this$limit = this.getLimit();
               Object other$limit = other.getLimit();
               if (this$limit == null) {
                  if (other$limit != null) {
                     return false;
                  }
               } else if (!this$limit.equals(other$limit)) {
                  return false;
               }

               Object this$limitClass = this.getLimitClass();
               Object other$limitClass = other.getLimitClass();
               if (this$limitClass == null) {
                  if (other$limitClass != null) {
                     return false;
                  }
               } else if (!this$limitClass.equals(other$limitClass)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1200100220In.Body;
      }
      public String toString() {
         return "Core1200100220In.Body(applyBranch=" + this.getApplyBranch() + ", applyAmt=" + this.getApplyAmt() + ", issueYear=" + this.getIssueYear() + ", approvalNo=" + this.getApprovalNo() + ", operateFlag=" + this.getOperateFlag() + ", stageCode=" + this.getStageCode() + ", branch=" + this.getBranch() + ", intRateFormNo=" + this.getIntRateFormNo() + ", limit=" + this.getLimit() + ", limitClass=" + this.getLimitClass() + ")";
      }
   }
}
