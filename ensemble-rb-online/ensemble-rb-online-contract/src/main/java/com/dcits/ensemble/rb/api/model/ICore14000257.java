package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000257In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000257Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000257 {
   String URL = "/rb/inq/dc/cost";

   
   @ApiDesc("大额存单购买成本查询接口")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0257"
   )
   @FunctionCategory("RB10-大额存单")
   @ConsumeSys("TLE")
   Core14000257Out runService(Core14000257In var1);
}
