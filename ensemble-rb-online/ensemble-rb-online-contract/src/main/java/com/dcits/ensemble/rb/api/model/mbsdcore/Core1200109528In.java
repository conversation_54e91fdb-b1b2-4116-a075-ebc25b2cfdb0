package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1200109528In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1200109528In.Body body;

   public Core1200109528In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1200109528In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1200109528In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200109528In)) {
         return false;
      } else {
         Core1200109528In other = (Core1200109528In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200109528In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "账号/卡号",
         notNull = true,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "账户序号",
         notNull = true,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "对方账户序列号",
         notNull = false,
         length = "5",
         remark = "对方账户序列号",
         maxSize = 5
      )
      private String othAcctSeqNo;
      @V(
         desc = "备付金账户类型",
         notNull = false,
         length = "1",
         remark = "0-资金归集账户1-零余额账户",
         maxSize = 1
      )
      private String fundAcctType;
      @V(
         desc = "对方账号/卡号",
         notNull = false,
         length = "50",
         remark = "对方账号/卡号",
         maxSize = 50
      )
      private String othBaseAcctNo;
      @V(
         desc = "备付金操作类型",
         notNull = true,
         length = "1",
         inDesc = "0-新增,1-维护,2-删除",
         remark = "备付金操作类型",
         maxSize = 1
      )
      private String fundOperationType;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getOthAcctSeqNo() {
         return this.othAcctSeqNo;
      }

      public String getFundAcctType() {
         return this.fundAcctType;
      }

      public String getOthBaseAcctNo() {
         return this.othBaseAcctNo;
      }

      public String getFundOperationType() {
         return this.fundOperationType;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setOthAcctSeqNo(String othAcctSeqNo) {
         this.othAcctSeqNo = othAcctSeqNo;
      }

      public void setFundAcctType(String fundAcctType) {
         this.fundAcctType = fundAcctType;
      }

      public void setOthBaseAcctNo(String othBaseAcctNo) {
         this.othBaseAcctNo = othBaseAcctNo;
      }

      public void setFundOperationType(String fundOperationType) {
         this.fundOperationType = fundOperationType;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1200109528In.Body)) {
            return false;
         } else {
            Core1200109528In.Body other = (Core1200109528In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo != null) {
                     return false;
                  }
               } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
                  return false;
               }

               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo != null) {
                     return false;
                  }
               } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                  return false;
               }

               Object this$othAcctSeqNo = this.getOthAcctSeqNo();
               Object other$othAcctSeqNo = other.getOthAcctSeqNo();
               if (this$othAcctSeqNo == null) {
                  if (other$othAcctSeqNo != null) {
                     return false;
                  }
               } else if (!this$othAcctSeqNo.equals(other$othAcctSeqNo)) {
                  return false;
               }

               label62: {
                  Object this$fundAcctType = this.getFundAcctType();
                  Object other$fundAcctType = other.getFundAcctType();
                  if (this$fundAcctType == null) {
                     if (other$fundAcctType == null) {
                        break label62;
                     }
                  } else if (this$fundAcctType.equals(other$fundAcctType)) {
                     break label62;
                  }

                  return false;
               }

               label55: {
                  Object this$othBaseAcctNo = this.getOthBaseAcctNo();
                  Object other$othBaseAcctNo = other.getOthBaseAcctNo();
                  if (this$othBaseAcctNo == null) {
                     if (other$othBaseAcctNo == null) {
                        break label55;
                     }
                  } else if (this$othBaseAcctNo.equals(other$othBaseAcctNo)) {
                     break label55;
                  }

                  return false;
               }

               Object this$fundOperationType = this.getFundOperationType();
               Object other$fundOperationType = other.getFundOperationType();
               if (this$fundOperationType == null) {
                  if (other$fundOperationType != null) {
                     return false;
                  }
               } else if (!this$fundOperationType.equals(other$fundOperationType)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1200109528In.Body;
      }
      public String toString() {
         return "Core1200109528In.Body(baseAcctNo=" + this.getBaseAcctNo() + ", acctSeqNo=" + this.getAcctSeqNo() + ", othAcctSeqNo=" + this.getOthAcctSeqNo() + ", fundAcctType=" + this.getFundAcctType() + ", othBaseAcctNo=" + this.getOthBaseAcctNo() + ", fundOperationType=" + this.getFundOperationType() + ")";
      }
   }
}
