package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1220100702In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1220100702In.Body body;

   public Core1220100702In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1220100702In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1220100702In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1220100702In)) {
         return false;
      } else {
         Core1220100702In other = (Core1220100702In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1220100702In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "账号/卡号",
         notNull = true,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "账户序号",
         notNull = true,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "产品类型",
         notNull = true,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户币种",
         notNull = true,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "资金池账户组ID",
         notNull = true,
         length = "30",
         remark = "资金池账户组ID",
         maxSize = 30
      )
      private String pcpGroupId;
      @V(
         desc = "资金池产品类型",
         notNull = true,
         length = "20",
         remark = "资金池产品类型",
         maxSize = 20
      )
      private String pcpProdType;
      @V(
         desc = "文件路径",
         notNull = false,
         length = "200",
         remark = "文件路径",
         maxSize = 200
      )
      private String filePath;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getPcpGroupId() {
         return this.pcpGroupId;
      }

      public String getPcpProdType() {
         return this.pcpProdType;
      }

      public String getFilePath() {
         return this.filePath;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setPcpGroupId(String pcpGroupId) {
         this.pcpGroupId = pcpGroupId;
      }

      public void setPcpProdType(String pcpProdType) {
         this.pcpProdType = pcpProdType;
      }

      public void setFilePath(String filePath) {
         this.filePath = filePath;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1220100702In.Body)) {
            return false;
         } else {
            Core1220100702In.Body other = (Core1220100702In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label95: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label95;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label95;
                  }

                  return false;
               }

               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo != null) {
                     return false;
                  }
               } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               label74: {
                  Object this$acctCcy = this.getAcctCcy();
                  Object other$acctCcy = other.getAcctCcy();
                  if (this$acctCcy == null) {
                     if (other$acctCcy == null) {
                        break label74;
                     }
                  } else if (this$acctCcy.equals(other$acctCcy)) {
                     break label74;
                  }

                  return false;
               }

               label67: {
                  Object this$pcpGroupId = this.getPcpGroupId();
                  Object other$pcpGroupId = other.getPcpGroupId();
                  if (this$pcpGroupId == null) {
                     if (other$pcpGroupId == null) {
                        break label67;
                     }
                  } else if (this$pcpGroupId.equals(other$pcpGroupId)) {
                     break label67;
                  }

                  return false;
               }

               Object this$pcpProdType = this.getPcpProdType();
               Object other$pcpProdType = other.getPcpProdType();
               if (this$pcpProdType == null) {
                  if (other$pcpProdType != null) {
                     return false;
                  }
               } else if (!this$pcpProdType.equals(other$pcpProdType)) {
                  return false;
               }

               Object this$filePath = this.getFilePath();
               Object other$filePath = other.getFilePath();
               if (this$filePath == null) {
                  if (other$filePath != null) {
                     return false;
                  }
               } else if (!this$filePath.equals(other$filePath)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1220100702In.Body;
      }
      public String toString() {
         return "Core1220100702In.Body(baseAcctNo=" + this.getBaseAcctNo() + ", acctSeqNo=" + this.getAcctSeqNo() + ", prodType=" + this.getProdType() + ", acctCcy=" + this.getAcctCcy() + ", pcpGroupId=" + this.getPcpGroupId() + ", pcpProdType=" + this.getPcpProdType() + ", filePath=" + this.getFilePath() + ")";
      }
   }
}
