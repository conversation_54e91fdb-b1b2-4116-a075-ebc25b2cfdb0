package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;

@MessageOut
public class MdsdCore12006699Out extends EnsResponse {
   private static final long serialVersionUID = 1L;

   public String toString() {
      return "MdsdCore12006699Out()";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof MdsdCore12006699Out)) {
         return false;
      } else {
         MdsdCore12006699Out other = (MdsdCore12006699Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else {
            return super.equals(o);
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof MdsdCore12006699Out;
   }
}
