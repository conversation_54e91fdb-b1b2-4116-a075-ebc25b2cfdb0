package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;

@MessageIn
public class Core1400023401In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400023401In.Body body;

   public Core1400023401In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400023401In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400023401In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400023401In)) {
         return false;
      } else {
         Core1400023401In other = (Core1400023401In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400023401In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "票据签发机构",
         notNull = false,
         length = "50",
         remark = "签发机构",
         maxSize = 50
      )
      private String billSignBranch;
      @V(
         desc = "签发柜员",
         notNull = false,
         length = "30",
         remark = "签发柜员",
         maxSize = 30
      )
      private String billSignUserId;
      @V(
         desc = "票据状态",
         notNull = false,
         length = "2",
         in = "00,01,02,03,04,05,06,08,09,11,12,13,14,15,16",
         inDesc = "00-录入,01-签发,02-兑付,03-退回,04-挂失,05-解挂,06-删除,08-挂失止付,09-公示催告,11-未复核,12-已复核,13-已打印,14-已签章核对,15-已签发冲正,16-已移存",
         remark = "票据状态00-录入,01-签发,02-兑付,03-退回,04-挂失,05-解挂,06-删除,08-挂失止付,09-公示催告",
         maxSize = 2
      )
      private String billStatus;
      @V(
         desc = "票据号码",
         notNull = false,
         length = "50",
         remark = "票据号码",
         maxSize = 50
      )
      private String billNo;
      @V(
         desc = "业务流水号",
         notNull = false,
         length = "50",
         remark = "支付流水号",
         maxSize = 50
      )
      private String serialNo;
      @V(
         desc = "票据类型",
         notNull = false,
         length = "5",
         inDesc = "P-纸质,E-电子,CT00-可转让汇票,CT01-不可转让汇票,CT02-现金汇票",
         remark = "票据类型0-可转让本票 1-不可转让本票 2-现金本票",
         maxSize = 5
      )
      private String billType;
      @V(
         desc = "付款人账号",
         notNull = false,
         length = "50",
         remark = "付款人账号",
         maxSize = 50
      )
      private String payerBaseAcctNo;
      @V(
         desc = "凭证类型",
         notNull = false,
         length = "10",
         remark = "凭证类型",
         maxSize = 10
      )
      private String docType;
      @V(
         desc = "凭证种类",
         notNull = false,
         length = "3",
         inDesc = "PBK-存折,CHK-支票,DCT-存单,CRD-卡,CFT-存款证明,BNK-银行票据,COL-托收票据,DFT-银行汇票,TCH-旅行支票,BAT-银行承兑汇票,CAT-商业承兑汇票,CHQ-支票,OTH-其他,SCV-印鉴",
         remark = "凭证种类",
         maxSize = 3
      )
      private String docClass;
      @V(
         desc = "出票金额",
         notNull = false,
         length = "17",
         remark = "出票金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal billTranAmt;
      @V(
         desc = "代理标志",
         notNull = false,
         length = "1",
         inDesc = "Y-是,N-否",
         remark = "代理标志",
         maxSize = 1
      )
      private String agentFlag;
      @V(
         desc = "过期标志",
         notNull = false,
         length = "10",
         remark = "过期标志",
         maxSize = 10
      )
      private String expireFlag;
      @V(
         desc = "最小金额",
         notNull = false,
         length = "17",
         remark = "最小金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal minAmt;
      @V(
         desc = "最大金额",
         notNull = false,
         length = "17",
         remark = "最大金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal maxAmt;
      @V(
         desc = "兑付日期",
         notNull = false,
         remark = "兑付日期"
      )
      private String paymentDate;
      @V(
         desc = "开始日期",
         notNull = false,
         remark = "开始日期"
      )
      private String startDate;
      @V(
         desc = "交易起始日期",
         notNull = false,
         remark = "交易起始日期"
      )
      private String tranStartDate;
      @V(
         desc = "交易结束日期",
         notNull = false,
         remark = "交易结束日期"
      )
      private String tranEndDate;
      @V(
         desc = "限额维护类型",
         notNull = false,
         length = "1",
         inDesc = "A-新增 ,U-修改 ,D-删除",
         remark = "限额维护类型",
         maxSize = 1
      )
      private String operType;
      @V(
         desc = "文件类型",
         notNull = false,
         length = "50",
         remark = "文件类型",
         maxSize = 50
      )
      private String fileType;
      @V(
         desc = "多余金额/未兑付金额",
         notNull = false,
         length = "17",
         remark = "多余金额/未兑付金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal excessAmt;
      @V(
         desc = "到期截至日期",
         notNull = false,
         remark = "到期截至日期"
      )
      private String maturityEndDate;
      @V(
         desc = "到期起始日期",
         notNull = false,
         remark = "到期起始日期"
      )
      private String maturityBeginDate;

      public String getBillSignBranch() {
         return this.billSignBranch;
      }

      public String getBillSignUserId() {
         return this.billSignUserId;
      }

      public String getBillStatus() {
         return this.billStatus;
      }

      public String getBillNo() {
         return this.billNo;
      }

      public String getSerialNo() {
         return this.serialNo;
      }

      public String getBillType() {
         return this.billType;
      }

      public String getPayerBaseAcctNo() {
         return this.payerBaseAcctNo;
      }

      public String getDocType() {
         return this.docType;
      }

      public String getDocClass() {
         return this.docClass;
      }

      public BigDecimal getBillTranAmt() {
         return this.billTranAmt;
      }

      public String getAgentFlag() {
         return this.agentFlag;
      }

      public String getExpireFlag() {
         return this.expireFlag;
      }

      public BigDecimal getMinAmt() {
         return this.minAmt;
      }

      public BigDecimal getMaxAmt() {
         return this.maxAmt;
      }

      public String getPaymentDate() {
         return this.paymentDate;
      }

      public String getStartDate() {
         return this.startDate;
      }

      public String getTranStartDate() {
         return this.tranStartDate;
      }

      public String getTranEndDate() {
         return this.tranEndDate;
      }

      public String getOperType() {
         return this.operType;
      }

      public String getFileType() {
         return this.fileType;
      }

      public BigDecimal getExcessAmt() {
         return this.excessAmt;
      }

      public String getMaturityEndDate() {
         return this.maturityEndDate;
      }

      public String getMaturityBeginDate() {
         return this.maturityBeginDate;
      }

      public void setBillSignBranch(String billSignBranch) {
         this.billSignBranch = billSignBranch;
      }

      public void setBillSignUserId(String billSignUserId) {
         this.billSignUserId = billSignUserId;
      }

      public void setBillStatus(String billStatus) {
         this.billStatus = billStatus;
      }

      public void setBillNo(String billNo) {
         this.billNo = billNo;
      }

      public void setSerialNo(String serialNo) {
         this.serialNo = serialNo;
      }

      public void setBillType(String billType) {
         this.billType = billType;
      }

      public void setPayerBaseAcctNo(String payerBaseAcctNo) {
         this.payerBaseAcctNo = payerBaseAcctNo;
      }

      public void setDocType(String docType) {
         this.docType = docType;
      }

      public void setDocClass(String docClass) {
         this.docClass = docClass;
      }

      public void setBillTranAmt(BigDecimal billTranAmt) {
         this.billTranAmt = billTranAmt;
      }

      public void setAgentFlag(String agentFlag) {
         this.agentFlag = agentFlag;
      }

      public void setExpireFlag(String expireFlag) {
         this.expireFlag = expireFlag;
      }

      public void setMinAmt(BigDecimal minAmt) {
         this.minAmt = minAmt;
      }

      public void setMaxAmt(BigDecimal maxAmt) {
         this.maxAmt = maxAmt;
      }

      public void setPaymentDate(String paymentDate) {
         this.paymentDate = paymentDate;
      }

      public void setStartDate(String startDate) {
         this.startDate = startDate;
      }

      public void setTranStartDate(String tranStartDate) {
         this.tranStartDate = tranStartDate;
      }

      public void setTranEndDate(String tranEndDate) {
         this.tranEndDate = tranEndDate;
      }

      public void setOperType(String operType) {
         this.operType = operType;
      }

      public void setFileType(String fileType) {
         this.fileType = fileType;
      }

      public void setExcessAmt(BigDecimal excessAmt) {
         this.excessAmt = excessAmt;
      }

      public void setMaturityEndDate(String maturityEndDate) {
         this.maturityEndDate = maturityEndDate;
      }

      public void setMaturityBeginDate(String maturityBeginDate) {
         this.maturityBeginDate = maturityBeginDate;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400023401In.Body)) {
            return false;
         } else {
            Core1400023401In.Body other = (Core1400023401In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label287: {
                  Object this$billSignBranch = this.getBillSignBranch();
                  Object other$billSignBranch = other.getBillSignBranch();
                  if (this$billSignBranch == null) {
                     if (other$billSignBranch == null) {
                        break label287;
                     }
                  } else if (this$billSignBranch.equals(other$billSignBranch)) {
                     break label287;
                  }

                  return false;
               }

               Object this$billSignUserId = this.getBillSignUserId();
               Object other$billSignUserId = other.getBillSignUserId();
               if (this$billSignUserId == null) {
                  if (other$billSignUserId != null) {
                     return false;
                  }
               } else if (!this$billSignUserId.equals(other$billSignUserId)) {
                  return false;
               }

               Object this$billStatus = this.getBillStatus();
               Object other$billStatus = other.getBillStatus();
               if (this$billStatus == null) {
                  if (other$billStatus != null) {
                     return false;
                  }
               } else if (!this$billStatus.equals(other$billStatus)) {
                  return false;
               }

               label266: {
                  Object this$billNo = this.getBillNo();
                  Object other$billNo = other.getBillNo();
                  if (this$billNo == null) {
                     if (other$billNo == null) {
                        break label266;
                     }
                  } else if (this$billNo.equals(other$billNo)) {
                     break label266;
                  }

                  return false;
               }

               label259: {
                  Object this$serialNo = this.getSerialNo();
                  Object other$serialNo = other.getSerialNo();
                  if (this$serialNo == null) {
                     if (other$serialNo == null) {
                        break label259;
                     }
                  } else if (this$serialNo.equals(other$serialNo)) {
                     break label259;
                  }

                  return false;
               }

               Object this$billType = this.getBillType();
               Object other$billType = other.getBillType();
               if (this$billType == null) {
                  if (other$billType != null) {
                     return false;
                  }
               } else if (!this$billType.equals(other$billType)) {
                  return false;
               }

               Object this$payerBaseAcctNo = this.getPayerBaseAcctNo();
               Object other$payerBaseAcctNo = other.getPayerBaseAcctNo();
               if (this$payerBaseAcctNo == null) {
                  if (other$payerBaseAcctNo != null) {
                     return false;
                  }
               } else if (!this$payerBaseAcctNo.equals(other$payerBaseAcctNo)) {
                  return false;
               }

               label238: {
                  Object this$docType = this.getDocType();
                  Object other$docType = other.getDocType();
                  if (this$docType == null) {
                     if (other$docType == null) {
                        break label238;
                     }
                  } else if (this$docType.equals(other$docType)) {
                     break label238;
                  }

                  return false;
               }

               label231: {
                  Object this$docClass = this.getDocClass();
                  Object other$docClass = other.getDocClass();
                  if (this$docClass == null) {
                     if (other$docClass == null) {
                        break label231;
                     }
                  } else if (this$docClass.equals(other$docClass)) {
                     break label231;
                  }

                  return false;
               }

               Object this$billTranAmt = this.getBillTranAmt();
               Object other$billTranAmt = other.getBillTranAmt();
               if (this$billTranAmt == null) {
                  if (other$billTranAmt != null) {
                     return false;
                  }
               } else if (!this$billTranAmt.equals(other$billTranAmt)) {
                  return false;
               }

               label217: {
                  Object this$agentFlag = this.getAgentFlag();
                  Object other$agentFlag = other.getAgentFlag();
                  if (this$agentFlag == null) {
                     if (other$agentFlag == null) {
                        break label217;
                     }
                  } else if (this$agentFlag.equals(other$agentFlag)) {
                     break label217;
                  }

                  return false;
               }

               Object this$expireFlag = this.getExpireFlag();
               Object other$expireFlag = other.getExpireFlag();
               if (this$expireFlag == null) {
                  if (other$expireFlag != null) {
                     return false;
                  }
               } else if (!this$expireFlag.equals(other$expireFlag)) {
                  return false;
               }

               label203: {
                  Object this$minAmt = this.getMinAmt();
                  Object other$minAmt = other.getMinAmt();
                  if (this$minAmt == null) {
                     if (other$minAmt == null) {
                        break label203;
                     }
                  } else if (this$minAmt.equals(other$minAmt)) {
                     break label203;
                  }

                  return false;
               }

               Object this$maxAmt = this.getMaxAmt();
               Object other$maxAmt = other.getMaxAmt();
               if (this$maxAmt == null) {
                  if (other$maxAmt != null) {
                     return false;
                  }
               } else if (!this$maxAmt.equals(other$maxAmt)) {
                  return false;
               }

               Object this$paymentDate = this.getPaymentDate();
               Object other$paymentDate = other.getPaymentDate();
               if (this$paymentDate == null) {
                  if (other$paymentDate != null) {
                     return false;
                  }
               } else if (!this$paymentDate.equals(other$paymentDate)) {
                  return false;
               }

               label182: {
                  Object this$startDate = this.getStartDate();
                  Object other$startDate = other.getStartDate();
                  if (this$startDate == null) {
                     if (other$startDate == null) {
                        break label182;
                     }
                  } else if (this$startDate.equals(other$startDate)) {
                     break label182;
                  }

                  return false;
               }

               label175: {
                  Object this$tranStartDate = this.getTranStartDate();
                  Object other$tranStartDate = other.getTranStartDate();
                  if (this$tranStartDate == null) {
                     if (other$tranStartDate == null) {
                        break label175;
                     }
                  } else if (this$tranStartDate.equals(other$tranStartDate)) {
                     break label175;
                  }

                  return false;
               }

               Object this$tranEndDate = this.getTranEndDate();
               Object other$tranEndDate = other.getTranEndDate();
               if (this$tranEndDate == null) {
                  if (other$tranEndDate != null) {
                     return false;
                  }
               } else if (!this$tranEndDate.equals(other$tranEndDate)) {
                  return false;
               }

               Object this$operType = this.getOperType();
               Object other$operType = other.getOperType();
               if (this$operType == null) {
                  if (other$operType != null) {
                     return false;
                  }
               } else if (!this$operType.equals(other$operType)) {
                  return false;
               }

               label154: {
                  Object this$fileType = this.getFileType();
                  Object other$fileType = other.getFileType();
                  if (this$fileType == null) {
                     if (other$fileType == null) {
                        break label154;
                     }
                  } else if (this$fileType.equals(other$fileType)) {
                     break label154;
                  }

                  return false;
               }

               label147: {
                  Object this$excessAmt = this.getExcessAmt();
                  Object other$excessAmt = other.getExcessAmt();
                  if (this$excessAmt == null) {
                     if (other$excessAmt == null) {
                        break label147;
                     }
                  } else if (this$excessAmt.equals(other$excessAmt)) {
                     break label147;
                  }

                  return false;
               }

               Object this$maturityEndDate = this.getMaturityEndDate();
               Object other$maturityEndDate = other.getMaturityEndDate();
               if (this$maturityEndDate == null) {
                  if (other$maturityEndDate != null) {
                     return false;
                  }
               } else if (!this$maturityEndDate.equals(other$maturityEndDate)) {
                  return false;
               }

               Object this$maturityBeginDate = this.getMaturityBeginDate();
               Object other$maturityBeginDate = other.getMaturityBeginDate();
               if (this$maturityBeginDate == null) {
                  if (other$maturityBeginDate != null) {
                     return false;
                  }
               } else if (!this$maturityBeginDate.equals(other$maturityBeginDate)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400023401In.Body;
      }
      public String toString() {
         return "Core1400023401In.Body(billSignBranch=" + this.getBillSignBranch() + ", billSignUserId=" + this.getBillSignUserId() + ", billStatus=" + this.getBillStatus() + ", billNo=" + this.getBillNo() + ", serialNo=" + this.getSerialNo() + ", billType=" + this.getBillType() + ", payerBaseAcctNo=" + this.getPayerBaseAcctNo() + ", docType=" + this.getDocType() + ", docClass=" + this.getDocClass() + ", billTranAmt=" + this.getBillTranAmt() + ", agentFlag=" + this.getAgentFlag() + ", expireFlag=" + this.getExpireFlag() + ", minAmt=" + this.getMinAmt() + ", maxAmt=" + this.getMaxAmt() + ", paymentDate=" + this.getPaymentDate() + ", startDate=" + this.getStartDate() + ", tranStartDate=" + this.getTranStartDate() + ", tranEndDate=" + this.getTranEndDate() + ", operType=" + this.getOperType() + ", fileType=" + this.getFileType() + ", excessAmt=" + this.getExcessAmt() + ", maturityEndDate=" + this.getMaturityEndDate() + ", maturityBeginDate=" + this.getMaturityBeginDate() + ")";
      }
   }
}
