package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400100127Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100127Out.AcctArray> acctArray;

   public List<Core1400100127Out.AcctArray> getAcctArray() {
      return this.acctArray;
   }

   public void setAcctArray(List<Core1400100127Out.AcctArray> acctArray) {
      this.acctArray = acctArray;
   }

   public String toString() {
      return "Core1400100127Out(acctArray=" + this.getAcctArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100127Out)) {
         return false;
      } else {
         Core1400100127Out other = (Core1400100127Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$acctArray = this.getAcctArray();
            Object other$acctArray = other.getAcctArray();
            if (this$acctArray == null) {
               if (other$acctArray != null) {
                  return false;
               }
            } else if (!this$acctArray.equals(other$acctArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100127Out;
   }
   public static class AcctArray {
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "账户名称",
         notNull = false,
         length = "200",
         remark = "账户名称，一般指中文账户名称",
         maxSize = 200
      )
      private String acctName;
      @V(
         desc = "挂销账标志",
         notNull = false,
         length = "1",
         remark = "挂销账标志",
         maxSize = 1
      )
      private String hangWriteOffFlag;
      @V(
         desc = "挂账期限",
         notNull = false,
         length = "5",
         remark = "挂账期限",
         maxSize = 5
      )
      private String hangTerm;
      @V(
         desc = "是否可透支",
         notNull = false,
         length = "1",
         remark = "是否可透支",
         maxSize = 1
      )
      private String odFacility;
      @V(
         desc = "是否允许柜面跨行支取许可标识",
         notNull = false,
         length = "1",
         remark = "是否允许柜面跨行支取许可标识",
         maxSize = 1
      )
      private String counterDebtFlag;
      @V(
         desc = "是否允许柜面跨行存入许可标识",
         notNull = false,
         length = "1",
         remark = "是否允许柜面跨行存入许可标识",
         maxSize = 1
      )
      private String counterDepFlag;
      @V(
         desc = "账户性质",
         notNull = false,
         length = "10",
         remark = "目前实际使用为账户属性",
         maxSize = 10
      )
      private String acctProperty;
      @V(
         desc = "账户性质2",
         notNull = false,
         length = "10",
         remark = "账户性质2- 内部户开户使用 J-结算类,T-投融资类",
         maxSize = 10
      )
      private String acctProperty2;
      @V(
         desc = "对方账号",
         notNull = false,
         length = "50",
         remark = "对方账号",
         maxSize = 50
      )
      private String othAcctNo;
      @V(
         desc = "对方账户名称",
         notNull = false,
         length = "200",
         remark = "对方账户名称",
         maxSize = 200
      )
      private String othAcctName;
      @V(
         desc = "余额",
         notNull = false,
         length = "17",
         remark = "余额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal balance;
      @V(
         desc = "计提金额",
         notNull = false,
         length = "17",
         remark = "计提金额，全额算息时为上日余额或积数，差额算息时为金额分层金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal accrAmt;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户类型",
         notNull = false,
         length = "1",
         remark = "账户类型",
         maxSize = 1
      )
      private String acctType;
      @V(
         desc = "定期账户细类",
         notNull = false,
         length = "1",
         remark = "定期账户细类",
         maxSize = 1
      )
      private String fixedCall;
      @V(
         desc = "账户状态",
         notNull = false,
         length = "1",
         remark = "描述账户生命周期不同阶段的划分",
         maxSize = 1
      )
      private String acctStatus;
      @V(
         desc = "账户属性",
         notNull = false,
         length = "10",
         remark = "账户属性",
         maxSize = 10
      )
      private String acctNature;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getCcy() {
         return this.ccy;
      }

      public String getAcctName() {
         return this.acctName;
      }

      public String getHangWriteOffFlag() {
         return this.hangWriteOffFlag;
      }

      public String getHangTerm() {
         return this.hangTerm;
      }

      public String getOdFacility() {
         return this.odFacility;
      }

      public String getCounterDebtFlag() {
         return this.counterDebtFlag;
      }

      public String getCounterDepFlag() {
         return this.counterDepFlag;
      }

      public String getAcctProperty() {
         return this.acctProperty;
      }

      public String getAcctProperty2() {
         return this.acctProperty2;
      }

      public String getOthAcctNo() {
         return this.othAcctNo;
      }

      public String getOthAcctName() {
         return this.othAcctName;
      }

      public BigDecimal getBalance() {
         return this.balance;
      }

      public BigDecimal getAccrAmt() {
         return this.accrAmt;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctType() {
         return this.acctType;
      }

      public String getFixedCall() {
         return this.fixedCall;
      }

      public String getAcctStatus() {
         return this.acctStatus;
      }

      public String getAcctNature() {
         return this.acctNature;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setAcctName(String acctName) {
         this.acctName = acctName;
      }

      public void setHangWriteOffFlag(String hangWriteOffFlag) {
         this.hangWriteOffFlag = hangWriteOffFlag;
      }

      public void setHangTerm(String hangTerm) {
         this.hangTerm = hangTerm;
      }

      public void setOdFacility(String odFacility) {
         this.odFacility = odFacility;
      }

      public void setCounterDebtFlag(String counterDebtFlag) {
         this.counterDebtFlag = counterDebtFlag;
      }

      public void setCounterDepFlag(String counterDepFlag) {
         this.counterDepFlag = counterDepFlag;
      }

      public void setAcctProperty(String acctProperty) {
         this.acctProperty = acctProperty;
      }

      public void setAcctProperty2(String acctProperty2) {
         this.acctProperty2 = acctProperty2;
      }

      public void setOthAcctNo(String othAcctNo) {
         this.othAcctNo = othAcctNo;
      }

      public void setOthAcctName(String othAcctName) {
         this.othAcctName = othAcctName;
      }

      public void setBalance(BigDecimal balance) {
         this.balance = balance;
      }

      public void setAccrAmt(BigDecimal accrAmt) {
         this.accrAmt = accrAmt;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctType(String acctType) {
         this.acctType = acctType;
      }

      public void setFixedCall(String fixedCall) {
         this.fixedCall = fixedCall;
      }

      public void setAcctStatus(String acctStatus) {
         this.acctStatus = acctStatus;
      }

      public void setAcctNature(String acctNature) {
         this.acctNature = acctNature;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100127Out.AcctArray)) {
            return false;
         } else {
            Core1400100127Out.AcctArray other = (Core1400100127Out.AcctArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label251: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label251;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label251;
                  }

                  return false;
               }

               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo != null) {
                     return false;
                  }
               } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                  return false;
               }

               Object this$ccy = this.getCcy();
               Object other$ccy = other.getCcy();
               if (this$ccy == null) {
                  if (other$ccy != null) {
                     return false;
                  }
               } else if (!this$ccy.equals(other$ccy)) {
                  return false;
               }

               label230: {
                  Object this$acctName = this.getAcctName();
                  Object other$acctName = other.getAcctName();
                  if (this$acctName == null) {
                     if (other$acctName == null) {
                        break label230;
                     }
                  } else if (this$acctName.equals(other$acctName)) {
                     break label230;
                  }

                  return false;
               }

               label223: {
                  Object this$hangWriteOffFlag = this.getHangWriteOffFlag();
                  Object other$hangWriteOffFlag = other.getHangWriteOffFlag();
                  if (this$hangWriteOffFlag == null) {
                     if (other$hangWriteOffFlag == null) {
                        break label223;
                     }
                  } else if (this$hangWriteOffFlag.equals(other$hangWriteOffFlag)) {
                     break label223;
                  }

                  return false;
               }

               label216: {
                  Object this$hangTerm = this.getHangTerm();
                  Object other$hangTerm = other.getHangTerm();
                  if (this$hangTerm == null) {
                     if (other$hangTerm == null) {
                        break label216;
                     }
                  } else if (this$hangTerm.equals(other$hangTerm)) {
                     break label216;
                  }

                  return false;
               }

               Object this$odFacility = this.getOdFacility();
               Object other$odFacility = other.getOdFacility();
               if (this$odFacility == null) {
                  if (other$odFacility != null) {
                     return false;
                  }
               } else if (!this$odFacility.equals(other$odFacility)) {
                  return false;
               }

               label202: {
                  Object this$counterDebtFlag = this.getCounterDebtFlag();
                  Object other$counterDebtFlag = other.getCounterDebtFlag();
                  if (this$counterDebtFlag == null) {
                     if (other$counterDebtFlag == null) {
                        break label202;
                     }
                  } else if (this$counterDebtFlag.equals(other$counterDebtFlag)) {
                     break label202;
                  }

                  return false;
               }

               Object this$counterDepFlag = this.getCounterDepFlag();
               Object other$counterDepFlag = other.getCounterDepFlag();
               if (this$counterDepFlag == null) {
                  if (other$counterDepFlag != null) {
                     return false;
                  }
               } else if (!this$counterDepFlag.equals(other$counterDepFlag)) {
                  return false;
               }

               label188: {
                  Object this$acctProperty = this.getAcctProperty();
                  Object other$acctProperty = other.getAcctProperty();
                  if (this$acctProperty == null) {
                     if (other$acctProperty == null) {
                        break label188;
                     }
                  } else if (this$acctProperty.equals(other$acctProperty)) {
                     break label188;
                  }

                  return false;
               }

               Object this$acctProperty2 = this.getAcctProperty2();
               Object other$acctProperty2 = other.getAcctProperty2();
               if (this$acctProperty2 == null) {
                  if (other$acctProperty2 != null) {
                     return false;
                  }
               } else if (!this$acctProperty2.equals(other$acctProperty2)) {
                  return false;
               }

               Object this$othAcctNo = this.getOthAcctNo();
               Object other$othAcctNo = other.getOthAcctNo();
               if (this$othAcctNo == null) {
                  if (other$othAcctNo != null) {
                     return false;
                  }
               } else if (!this$othAcctNo.equals(other$othAcctNo)) {
                  return false;
               }

               label167: {
                  Object this$othAcctName = this.getOthAcctName();
                  Object other$othAcctName = other.getOthAcctName();
                  if (this$othAcctName == null) {
                     if (other$othAcctName == null) {
                        break label167;
                     }
                  } else if (this$othAcctName.equals(other$othAcctName)) {
                     break label167;
                  }

                  return false;
               }

               label160: {
                  Object this$balance = this.getBalance();
                  Object other$balance = other.getBalance();
                  if (this$balance == null) {
                     if (other$balance == null) {
                        break label160;
                     }
                  } else if (this$balance.equals(other$balance)) {
                     break label160;
                  }

                  return false;
               }

               Object this$accrAmt = this.getAccrAmt();
               Object other$accrAmt = other.getAccrAmt();
               if (this$accrAmt == null) {
                  if (other$accrAmt != null) {
                     return false;
                  }
               } else if (!this$accrAmt.equals(other$accrAmt)) {
                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               label139: {
                  Object this$acctType = this.getAcctType();
                  Object other$acctType = other.getAcctType();
                  if (this$acctType == null) {
                     if (other$acctType == null) {
                        break label139;
                     }
                  } else if (this$acctType.equals(other$acctType)) {
                     break label139;
                  }

                  return false;
               }

               Object this$fixedCall = this.getFixedCall();
               Object other$fixedCall = other.getFixedCall();
               if (this$fixedCall == null) {
                  if (other$fixedCall != null) {
                     return false;
                  }
               } else if (!this$fixedCall.equals(other$fixedCall)) {
                  return false;
               }

               Object this$acctStatus = this.getAcctStatus();
               Object other$acctStatus = other.getAcctStatus();
               if (this$acctStatus == null) {
                  if (other$acctStatus != null) {
                     return false;
                  }
               } else if (!this$acctStatus.equals(other$acctStatus)) {
                  return false;
               }

               Object this$acctNature = this.getAcctNature();
               Object other$acctNature = other.getAcctNature();
               if (this$acctNature == null) {
                  if (other$acctNature != null) {
                     return false;
                  }
               } else if (!this$acctNature.equals(other$acctNature)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100127Out.AcctArray;
      }
      public String toString() {
         return "Core1400100127Out.AcctArray(baseAcctNo=" + this.getBaseAcctNo() + ", acctSeqNo=" + this.getAcctSeqNo() + ", ccy=" + this.getCcy() + ", acctName=" + this.getAcctName() + ", hangWriteOffFlag=" + this.getHangWriteOffFlag() + ", hangTerm=" + this.getHangTerm() + ", odFacility=" + this.getOdFacility() + ", counterDebtFlag=" + this.getCounterDebtFlag() + ", counterDepFlag=" + this.getCounterDepFlag() + ", acctProperty=" + this.getAcctProperty() + ", acctProperty2=" + this.getAcctProperty2() + ", othAcctNo=" + this.getOthAcctNo() + ", othAcctName=" + this.getOthAcctName() + ", balance=" + this.getBalance() + ", accrAmt=" + this.getAccrAmt() + ", prodType=" + this.getProdType() + ", acctType=" + this.getAcctType() + ", fixedCall=" + this.getFixedCall() + ", acctStatus=" + this.getAcctStatus() + ", acctNature=" + this.getAcctNature() + ")";
      }
   }
}
