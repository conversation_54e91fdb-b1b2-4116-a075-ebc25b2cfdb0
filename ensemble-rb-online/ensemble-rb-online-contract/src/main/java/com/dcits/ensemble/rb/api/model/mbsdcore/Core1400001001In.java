package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;

@MessageIn
public class Core1400001001In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400001001In.Body body;

   public Core1400001001In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400001001In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400001001In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400001001In)) {
         return false;
      } else {
         Core1400001001In other = (Core1400001001In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400001001In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "账号/卡号",
         notNull = true,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "排序标志",
         notNull = false,
         length = "2",
         inDesc = "01-正序,02-倒序",
         remark = "排序标志",
         maxSize = 2
      )
      private String sortFlag;
      @V(
         desc = "周期值",
         notNull = false,
         length = "3",
         remark = "周期值",
         maxSize = 3
      )
      private String periodValue;
      @V(
         desc = "敞口期限类型",
         notNull = false,
         length = "1",
         inDesc = "Y-年,Q-季,M-月,W-周,D-日",
         remark = "敞口期限类型",
         maxSize = 1
      )
      private String periodType;
      @V(
         desc = "开始日期",
         notNull = false,
         remark = "开始日期"
      )
      private String startDate;
      @V(
         desc = "结束日期",
         notNull = false,
         remark = "结束日期"
      )
      private String endDate;
      @V(
         desc = "客户号",
         notNull = true,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "交易描述",
         notNull = false,
         length = "200",
         remark = "交易描述",
         maxSize = 200
      )
      private String tranDesc;
      @V(
         desc = "排序方式字段",
         notNull = false,
         length = "10",
         inDesc = "01-根据tranDate排序,02-根据tranAmt排序",
         remark = "排序方式字段",
         maxSize = 10
      )
      private String sortField;
      @V(
         desc = "借贷标志",
         notNull = false,
         length = "1",
         inDesc = "C-贷 ,D-借",
         remark = "借贷标志",
         maxSize = 1
      )
      private String crDrInd;
      @V(
         desc = "最大交易金额",
         notNull = false,
         length = "17",
         remark = "最大交易金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal maxTranAmt;
      @V(
         desc = "最小交易金额",
         notNull = false,
         length = "17",
         remark = "最小交易金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal minTranAmt;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getSortFlag() {
         return this.sortFlag;
      }

      public String getPeriodValue() {
         return this.periodValue;
      }

      public String getPeriodType() {
         return this.periodType;
      }

      public String getStartDate() {
         return this.startDate;
      }

      public String getEndDate() {
         return this.endDate;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public String getTranDesc() {
         return this.tranDesc;
      }

      public String getSortField() {
         return this.sortField;
      }

      public String getCrDrInd() {
         return this.crDrInd;
      }

      public BigDecimal getMaxTranAmt() {
         return this.maxTranAmt;
      }

      public BigDecimal getMinTranAmt() {
         return this.minTranAmt;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setSortFlag(String sortFlag) {
         this.sortFlag = sortFlag;
      }

      public void setPeriodValue(String periodValue) {
         this.periodValue = periodValue;
      }

      public void setPeriodType(String periodType) {
         this.periodType = periodType;
      }

      public void setStartDate(String startDate) {
         this.startDate = startDate;
      }

      public void setEndDate(String endDate) {
         this.endDate = endDate;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setTranDesc(String tranDesc) {
         this.tranDesc = tranDesc;
      }

      public void setSortField(String sortField) {
         this.sortField = sortField;
      }

      public void setCrDrInd(String crDrInd) {
         this.crDrInd = crDrInd;
      }

      public void setMaxTranAmt(BigDecimal maxTranAmt) {
         this.maxTranAmt = maxTranAmt;
      }

      public void setMinTranAmt(BigDecimal minTranAmt) {
         this.minTranAmt = minTranAmt;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400001001In.Body)) {
            return false;
         } else {
            Core1400001001In.Body other = (Core1400001001In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo != null) {
                     return false;
                  }
               } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
                  return false;
               }

               Object this$acctCcy = this.getAcctCcy();
               Object other$acctCcy = other.getAcctCcy();
               if (this$acctCcy == null) {
                  if (other$acctCcy != null) {
                     return false;
                  }
               } else if (!this$acctCcy.equals(other$acctCcy)) {
                  return false;
               }

               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo != null) {
                     return false;
                  }
               } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                  return false;
               }

               label158: {
                  Object this$sortFlag = this.getSortFlag();
                  Object other$sortFlag = other.getSortFlag();
                  if (this$sortFlag == null) {
                     if (other$sortFlag == null) {
                        break label158;
                     }
                  } else if (this$sortFlag.equals(other$sortFlag)) {
                     break label158;
                  }

                  return false;
               }

               label151: {
                  Object this$periodValue = this.getPeriodValue();
                  Object other$periodValue = other.getPeriodValue();
                  if (this$periodValue == null) {
                     if (other$periodValue == null) {
                        break label151;
                     }
                  } else if (this$periodValue.equals(other$periodValue)) {
                     break label151;
                  }

                  return false;
               }

               Object this$periodType = this.getPeriodType();
               Object other$periodType = other.getPeriodType();
               if (this$periodType == null) {
                  if (other$periodType != null) {
                     return false;
                  }
               } else if (!this$periodType.equals(other$periodType)) {
                  return false;
               }

               label137: {
                  Object this$startDate = this.getStartDate();
                  Object other$startDate = other.getStartDate();
                  if (this$startDate == null) {
                     if (other$startDate == null) {
                        break label137;
                     }
                  } else if (this$startDate.equals(other$startDate)) {
                     break label137;
                  }

                  return false;
               }

               label130: {
                  Object this$endDate = this.getEndDate();
                  Object other$endDate = other.getEndDate();
                  if (this$endDate == null) {
                     if (other$endDate == null) {
                        break label130;
                     }
                  } else if (this$endDate.equals(other$endDate)) {
                     break label130;
                  }

                  return false;
               }

               Object this$clientNo = this.getClientNo();
               Object other$clientNo = other.getClientNo();
               if (this$clientNo == null) {
                  if (other$clientNo != null) {
                     return false;
                  }
               } else if (!this$clientNo.equals(other$clientNo)) {
                  return false;
               }

               Object this$tranDesc = this.getTranDesc();
               Object other$tranDesc = other.getTranDesc();
               if (this$tranDesc == null) {
                  if (other$tranDesc != null) {
                     return false;
                  }
               } else if (!this$tranDesc.equals(other$tranDesc)) {
                  return false;
               }

               label109: {
                  Object this$sortField = this.getSortField();
                  Object other$sortField = other.getSortField();
                  if (this$sortField == null) {
                     if (other$sortField == null) {
                        break label109;
                     }
                  } else if (this$sortField.equals(other$sortField)) {
                     break label109;
                  }

                  return false;
               }

               label102: {
                  Object this$crDrInd = this.getCrDrInd();
                  Object other$crDrInd = other.getCrDrInd();
                  if (this$crDrInd == null) {
                     if (other$crDrInd == null) {
                        break label102;
                     }
                  } else if (this$crDrInd.equals(other$crDrInd)) {
                     break label102;
                  }

                  return false;
               }

               Object this$maxTranAmt = this.getMaxTranAmt();
               Object other$maxTranAmt = other.getMaxTranAmt();
               if (this$maxTranAmt == null) {
                  if (other$maxTranAmt != null) {
                     return false;
                  }
               } else if (!this$maxTranAmt.equals(other$maxTranAmt)) {
                  return false;
               }

               Object this$minTranAmt = this.getMinTranAmt();
               Object other$minTranAmt = other.getMinTranAmt();
               if (this$minTranAmt == null) {
                  if (other$minTranAmt != null) {
                     return false;
                  }
               } else if (!this$minTranAmt.equals(other$minTranAmt)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400001001In.Body;
      }
      public String toString() {
         return "Core1400001001In.Body(baseAcctNo=" + this.getBaseAcctNo() + ", acctCcy=" + this.getAcctCcy() + ", acctSeqNo=" + this.getAcctSeqNo() + ", sortFlag=" + this.getSortFlag() + ", periodValue=" + this.getPeriodValue() + ", periodType=" + this.getPeriodType() + ", startDate=" + this.getStartDate() + ", endDate=" + this.getEndDate() + ", clientNo=" + this.getClientNo() + ", tranDesc=" + this.getTranDesc() + ", sortField=" + this.getSortField() + ", crDrInd=" + this.getCrDrInd() + ", maxTranAmt=" + this.getMaxTranAmt() + ", minTranAmt=" + this.getMinTranAmt() + ")";
      }
   }
}
