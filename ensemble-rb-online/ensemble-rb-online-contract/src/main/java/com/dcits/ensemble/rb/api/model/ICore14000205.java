package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000205In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000205Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000205 {
   String URL = "/rb/inq/kyd/intAgreement";


   @ApiRemark("查询利率审批单,基于利率审批功能使用")
   @ApiDesc("查询利率审批单")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0205"
   )
   @BusinessCategory("1400-查询")
   @FunctionCategory("PF93-定价利率")
   @ConsumeSys("COS/TLE/ATM/MBP/STM")
   @ApiUseStatus("PROJECT-项目")
   Core14000205Out runService(Core14000205In var1);
}
