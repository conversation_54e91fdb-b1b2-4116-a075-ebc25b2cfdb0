package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14005800In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14005800Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14005800 {
   String URL = "/rb/nfin/channel/receipt/print";


   @ApiRemark("标准优化")
   @ApiDesc("回单打印查询")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "5800"
   )
   @BusinessCategory("1400-查询")
   @FunctionCategory("RB01-公共服务")
   @ConsumeSys("TLE")
   @ApiUseStatus("PROJECT-项目")
   Core14005800Out runService(Core14005800In var1);
}
