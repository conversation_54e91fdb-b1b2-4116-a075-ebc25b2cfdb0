package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400033402Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400033402Out.PnBillDetailArray> pnBillDetailArray;

   public List<Core1400033402Out.PnBillDetailArray> getPnBillDetailArray() {
      return this.pnBillDetailArray;
   }

   public void setPnBillDetailArray(List<Core1400033402Out.PnBillDetailArray> pnBillDetailArray) {
      this.pnBillDetailArray = pnBillDetailArray;
   }

   public String toString() {
      return "Core1400033402Out(pnBillDetailArray=" + this.getPnBillDetailArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400033402Out)) {
         return false;
      } else {
         Core1400033402Out other = (Core1400033402Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$pnBillDetailArray = this.getPnBillDetailArray();
            Object other$pnBillDetailArray = other.getPnBillDetailArray();
            if (this$pnBillDetailArray == null) {
               if (other$pnBillDetailArray != null) {
                  return false;
               }
            } else if (!this$pnBillDetailArray.equals(other$pnBillDetailArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400033402Out;
   }
   public static class PnBillDetailArray {
      @V(
         desc = "交易日期",
         notNull = false,
         remark = "交易日期"
      )
      private String tranDate;
      @V(
         desc = "签约日期",
         notNull = false,
         remark = "签约日期"
      )
      private String signDate;
      @V(
         desc = "签发行行号",
         notNull = false,
         length = "20",
         remark = "签发行行号",
         maxSize = 20
      )
      private String issueBankNo;
      @V(
         desc = "签发行行名",
         notNull = false,
         length = "50",
         remark = "签发行行名",
         maxSize = 50
      )
      private String issueBankName;
      @V(
         desc = "交易机构",
         notNull = false,
         length = "50",
         remark = "交易机构",
         maxSize = 50
      )
      private String tranBranch;
      @V(
         desc = "业务流水号",
         notNull = false,
         length = "50",
         remark = "支付流水号",
         maxSize = 50
      )
      private String serialNo;
      @V(
         desc = "原业务流水号",
         notNull = false,
         length = "50",
         remark = "原签发流水号",
         maxSize = 50
      )
      private String origSerialNo;
      @V(
         desc = "挂失申请书编号",
         notNull = false,
         length = "50",
         remark = "挂失申请书编号",
         maxSize = 50
      )
      private String lossNo;
      @V(
         desc = "交易参考号",
         notNull = false,
         length = "50",
         remark = "交易参考号",
         maxSize = 50
      )
      private String reference;
      @V(
         desc = "交易类型",
         notNull = false,
         length = "10",
         remark = "交易类型",
         maxSize = 10
      )
      private String tranType;
      @V(
         desc = "票面金额",
         notNull = false,
         length = "17",
         remark = "票面金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal billAmt;
      @V(
         desc = "应收手续费金额",
         notNull = false,
         length = "17",
         remark = "应收手续费金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal feeOsdAmt;
      @V(
         desc = "实际收取金额",
         notNull = false,
         length = "17",
         remark = "实际收取金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal feeRealAmt;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "现转标识",
         notNull = false,
         length = "1",
         in = "0,1",
         remark = "现转标识",
         maxSize = 1
      )
      private String tranferCashFlag;
      @V(
         desc = "费用收取方式",
         notNull = false,
         length = "1",
         in = "T,C,F",
         remark = "费用收取方式",
         maxSize = 1
      )
      private String feeChargeType;
      @V(
         desc = "事件类型",
         notNull = false,
         length = "20",
         remark = "事件类型",
         maxSize = 20
      )
      private String eventType;
      @V(
         desc = "限额维护类型",
         notNull = false,
         length = "1",
         in = "A,U,D",
         remark = "限额维护类型",
         maxSize = 1
      )
      private String operType;
      @V(
         desc = "票据类型",
         notNull = false,
         length = "5",
         in = "P,E",
         remark = "票据类型",
         maxSize = 5
      )
      private String billType;
      @V(
         desc = "收款人账号",
         notNull = false,
         length = "50",
         remark = "收款人账号",
         maxSize = 50
      )
      private String payeeBaseAcctNo;
      @V(
         desc = "收款人账户",
         notNull = false,
         length = "50",
         remark = "收款人账户",
         maxSize = 50
      )
      private String payeeAcctNo;
      @V(
         desc = "退回账号",
         notNull = false,
         length = "50",
         remark = "退回账号",
         maxSize = 50
      )
      private String returnBaseAcctNo;
      @V(
         desc = "付款人账户名称",
         notNull = false,
         length = "200",
         remark = "付款人账户名称",
         maxSize = 200
      )
      private String payerAcctName;
      @V(
         desc = "收款人名称",
         notNull = false,
         length = "200",
         remark = "收款人名称",
         maxSize = 200
      )
      private String payeeAcctName;
      @V(
         desc = "退回账户名称",
         notNull = false,
         length = "200",
         remark = "退回账户名称",
         maxSize = 200
      )
      private String returnAcctName;
      @V(
         desc = "本票申请书号码",
         notNull = false,
         length = "50",
         remark = "本票申请书号码",
         maxSize = 50
      )
      private String billApplyNo;
      @V(
         desc = "处理结果",
         notNull = false,
         length = "200",
         remark = "处理结果",
         maxSize = 200
      )
      private String dealResult;
      @V(
         desc = "票据号码",
         notNull = false,
         length = "50",
         remark = "票据号码",
         maxSize = 50
      )
      private String billNo;
      @V(
         desc = "票据密押",
         notNull = false,
         length = "20",
         remark = "票据密押",
         maxSize = 20
      )
      private String billPswd;
      @V(
         desc = "票据状态",
         notNull = false,
         length = "2",
         in = "00,01,02,03,04,05,06",
         remark = "票据状态",
         maxSize = 2
      )
      private String billStatus;
      @V(
         desc = "交易柜员",
         notNull = false,
         length = "30",
         remark = "交易柜员",
         maxSize = 30
      )
      private String userId;
      @V(
         desc = "复核柜员",
         notNull = false,
         length = "30",
         remark = "复核柜员",
         maxSize = 30
      )
      private String apprUserId;
      @V(
         desc = "授权柜员",
         notNull = false,
         length = "30",
         remark = "授权柜员",
         maxSize = 30
      )
      private String authUserId;
      @V(
         desc = "法人",
         notNull = false,
         length = "20",
         remark = "法人",
         maxSize = 20
      )
      private String company;

      public String getTranDate() {
         return this.tranDate;
      }

      public String getSignDate() {
         return this.signDate;
      }

      public String getIssueBankNo() {
         return this.issueBankNo;
      }

      public String getIssueBankName() {
         return this.issueBankName;
      }

      public String getTranBranch() {
         return this.tranBranch;
      }

      public String getSerialNo() {
         return this.serialNo;
      }

      public String getOrigSerialNo() {
         return this.origSerialNo;
      }

      public String getLossNo() {
         return this.lossNo;
      }

      public String getReference() {
         return this.reference;
      }

      public String getTranType() {
         return this.tranType;
      }

      public BigDecimal getBillAmt() {
         return this.billAmt;
      }

      public BigDecimal getFeeOsdAmt() {
         return this.feeOsdAmt;
      }

      public BigDecimal getFeeRealAmt() {
         return this.feeRealAmt;
      }

      public String getCcy() {
         return this.ccy;
      }

      public String getTranferCashFlag() {
         return this.tranferCashFlag;
      }

      public String getFeeChargeType() {
         return this.feeChargeType;
      }

      public String getEventType() {
         return this.eventType;
      }

      public String getOperType() {
         return this.operType;
      }

      public String getBillType() {
         return this.billType;
      }

      public String getPayeeBaseAcctNo() {
         return this.payeeBaseAcctNo;
      }

      public String getPayeeAcctNo() {
         return this.payeeAcctNo;
      }

      public String getReturnBaseAcctNo() {
         return this.returnBaseAcctNo;
      }

      public String getPayerAcctName() {
         return this.payerAcctName;
      }

      public String getPayeeAcctName() {
         return this.payeeAcctName;
      }

      public String getReturnAcctName() {
         return this.returnAcctName;
      }

      public String getBillApplyNo() {
         return this.billApplyNo;
      }

      public String getDealResult() {
         return this.dealResult;
      }

      public String getBillNo() {
         return this.billNo;
      }

      public String getBillPswd() {
         return this.billPswd;
      }

      public String getBillStatus() {
         return this.billStatus;
      }

      public String getUserId() {
         return this.userId;
      }

      public String getApprUserId() {
         return this.apprUserId;
      }

      public String getAuthUserId() {
         return this.authUserId;
      }

      public String getCompany() {
         return this.company;
      }

      public void setTranDate(String tranDate) {
         this.tranDate = tranDate;
      }

      public void setSignDate(String signDate) {
         this.signDate = signDate;
      }

      public void setIssueBankNo(String issueBankNo) {
         this.issueBankNo = issueBankNo;
      }

      public void setIssueBankName(String issueBankName) {
         this.issueBankName = issueBankName;
      }

      public void setTranBranch(String tranBranch) {
         this.tranBranch = tranBranch;
      }

      public void setSerialNo(String serialNo) {
         this.serialNo = serialNo;
      }

      public void setOrigSerialNo(String origSerialNo) {
         this.origSerialNo = origSerialNo;
      }

      public void setLossNo(String lossNo) {
         this.lossNo = lossNo;
      }

      public void setReference(String reference) {
         this.reference = reference;
      }

      public void setTranType(String tranType) {
         this.tranType = tranType;
      }

      public void setBillAmt(BigDecimal billAmt) {
         this.billAmt = billAmt;
      }

      public void setFeeOsdAmt(BigDecimal feeOsdAmt) {
         this.feeOsdAmt = feeOsdAmt;
      }

      public void setFeeRealAmt(BigDecimal feeRealAmt) {
         this.feeRealAmt = feeRealAmt;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setTranferCashFlag(String tranferCashFlag) {
         this.tranferCashFlag = tranferCashFlag;
      }

      public void setFeeChargeType(String feeChargeType) {
         this.feeChargeType = feeChargeType;
      }

      public void setEventType(String eventType) {
         this.eventType = eventType;
      }

      public void setOperType(String operType) {
         this.operType = operType;
      }

      public void setBillType(String billType) {
         this.billType = billType;
      }

      public void setPayeeBaseAcctNo(String payeeBaseAcctNo) {
         this.payeeBaseAcctNo = payeeBaseAcctNo;
      }

      public void setPayeeAcctNo(String payeeAcctNo) {
         this.payeeAcctNo = payeeAcctNo;
      }

      public void setReturnBaseAcctNo(String returnBaseAcctNo) {
         this.returnBaseAcctNo = returnBaseAcctNo;
      }

      public void setPayerAcctName(String payerAcctName) {
         this.payerAcctName = payerAcctName;
      }

      public void setPayeeAcctName(String payeeAcctName) {
         this.payeeAcctName = payeeAcctName;
      }

      public void setReturnAcctName(String returnAcctName) {
         this.returnAcctName = returnAcctName;
      }

      public void setBillApplyNo(String billApplyNo) {
         this.billApplyNo = billApplyNo;
      }

      public void setDealResult(String dealResult) {
         this.dealResult = dealResult;
      }

      public void setBillNo(String billNo) {
         this.billNo = billNo;
      }

      public void setBillPswd(String billPswd) {
         this.billPswd = billPswd;
      }

      public void setBillStatus(String billStatus) {
         this.billStatus = billStatus;
      }

      public void setUserId(String userId) {
         this.userId = userId;
      }

      public void setApprUserId(String apprUserId) {
         this.apprUserId = apprUserId;
      }

      public void setAuthUserId(String authUserId) {
         this.authUserId = authUserId;
      }

      public void setCompany(String company) {
         this.company = company;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400033402Out.PnBillDetailArray)) {
            return false;
         } else {
            Core1400033402Out.PnBillDetailArray other = (Core1400033402Out.PnBillDetailArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$tranDate = this.getTranDate();
               Object other$tranDate = other.getTranDate();
               if (this$tranDate == null) {
                  if (other$tranDate != null) {
                     return false;
                  }
               } else if (!this$tranDate.equals(other$tranDate)) {
                  return false;
               }

               Object this$signDate = this.getSignDate();
               Object other$signDate = other.getSignDate();
               if (this$signDate == null) {
                  if (other$signDate != null) {
                     return false;
                  }
               } else if (!this$signDate.equals(other$signDate)) {
                  return false;
               }

               Object this$issueBankNo = this.getIssueBankNo();
               Object other$issueBankNo = other.getIssueBankNo();
               if (this$issueBankNo == null) {
                  if (other$issueBankNo != null) {
                     return false;
                  }
               } else if (!this$issueBankNo.equals(other$issueBankNo)) {
                  return false;
               }

               label398: {
                  Object this$issueBankName = this.getIssueBankName();
                  Object other$issueBankName = other.getIssueBankName();
                  if (this$issueBankName == null) {
                     if (other$issueBankName == null) {
                        break label398;
                     }
                  } else if (this$issueBankName.equals(other$issueBankName)) {
                     break label398;
                  }

                  return false;
               }

               label391: {
                  Object this$tranBranch = this.getTranBranch();
                  Object other$tranBranch = other.getTranBranch();
                  if (this$tranBranch == null) {
                     if (other$tranBranch == null) {
                        break label391;
                     }
                  } else if (this$tranBranch.equals(other$tranBranch)) {
                     break label391;
                  }

                  return false;
               }

               Object this$serialNo = this.getSerialNo();
               Object other$serialNo = other.getSerialNo();
               if (this$serialNo == null) {
                  if (other$serialNo != null) {
                     return false;
                  }
               } else if (!this$serialNo.equals(other$serialNo)) {
                  return false;
               }

               label377: {
                  Object this$origSerialNo = this.getOrigSerialNo();
                  Object other$origSerialNo = other.getOrigSerialNo();
                  if (this$origSerialNo == null) {
                     if (other$origSerialNo == null) {
                        break label377;
                     }
                  } else if (this$origSerialNo.equals(other$origSerialNo)) {
                     break label377;
                  }

                  return false;
               }

               label370: {
                  Object this$lossNo = this.getLossNo();
                  Object other$lossNo = other.getLossNo();
                  if (this$lossNo == null) {
                     if (other$lossNo == null) {
                        break label370;
                     }
                  } else if (this$lossNo.equals(other$lossNo)) {
                     break label370;
                  }

                  return false;
               }

               Object this$reference = this.getReference();
               Object other$reference = other.getReference();
               if (this$reference == null) {
                  if (other$reference != null) {
                     return false;
                  }
               } else if (!this$reference.equals(other$reference)) {
                  return false;
               }

               Object this$tranType = this.getTranType();
               Object other$tranType = other.getTranType();
               if (this$tranType == null) {
                  if (other$tranType != null) {
                     return false;
                  }
               } else if (!this$tranType.equals(other$tranType)) {
                  return false;
               }

               label349: {
                  Object this$billAmt = this.getBillAmt();
                  Object other$billAmt = other.getBillAmt();
                  if (this$billAmt == null) {
                     if (other$billAmt == null) {
                        break label349;
                     }
                  } else if (this$billAmt.equals(other$billAmt)) {
                     break label349;
                  }

                  return false;
               }

               label342: {
                  Object this$feeOsdAmt = this.getFeeOsdAmt();
                  Object other$feeOsdAmt = other.getFeeOsdAmt();
                  if (this$feeOsdAmt == null) {
                     if (other$feeOsdAmt == null) {
                        break label342;
                     }
                  } else if (this$feeOsdAmt.equals(other$feeOsdAmt)) {
                     break label342;
                  }

                  return false;
               }

               Object this$feeRealAmt = this.getFeeRealAmt();
               Object other$feeRealAmt = other.getFeeRealAmt();
               if (this$feeRealAmt == null) {
                  if (other$feeRealAmt != null) {
                     return false;
                  }
               } else if (!this$feeRealAmt.equals(other$feeRealAmt)) {
                  return false;
               }

               label328: {
                  Object this$ccy = this.getCcy();
                  Object other$ccy = other.getCcy();
                  if (this$ccy == null) {
                     if (other$ccy == null) {
                        break label328;
                     }
                  } else if (this$ccy.equals(other$ccy)) {
                     break label328;
                  }

                  return false;
               }

               Object this$tranferCashFlag = this.getTranferCashFlag();
               Object other$tranferCashFlag = other.getTranferCashFlag();
               if (this$tranferCashFlag == null) {
                  if (other$tranferCashFlag != null) {
                     return false;
                  }
               } else if (!this$tranferCashFlag.equals(other$tranferCashFlag)) {
                  return false;
               }

               label314: {
                  Object this$feeChargeType = this.getFeeChargeType();
                  Object other$feeChargeType = other.getFeeChargeType();
                  if (this$feeChargeType == null) {
                     if (other$feeChargeType == null) {
                        break label314;
                     }
                  } else if (this$feeChargeType.equals(other$feeChargeType)) {
                     break label314;
                  }

                  return false;
               }

               Object this$eventType = this.getEventType();
               Object other$eventType = other.getEventType();
               if (this$eventType == null) {
                  if (other$eventType != null) {
                     return false;
                  }
               } else if (!this$eventType.equals(other$eventType)) {
                  return false;
               }

               Object this$operType = this.getOperType();
               Object other$operType = other.getOperType();
               if (this$operType == null) {
                  if (other$operType != null) {
                     return false;
                  }
               } else if (!this$operType.equals(other$operType)) {
                  return false;
               }

               Object this$billType = this.getBillType();
               Object other$billType = other.getBillType();
               if (this$billType == null) {
                  if (other$billType != null) {
                     return false;
                  }
               } else if (!this$billType.equals(other$billType)) {
                  return false;
               }

               label286: {
                  Object this$payeeBaseAcctNo = this.getPayeeBaseAcctNo();
                  Object other$payeeBaseAcctNo = other.getPayeeBaseAcctNo();
                  if (this$payeeBaseAcctNo == null) {
                     if (other$payeeBaseAcctNo == null) {
                        break label286;
                     }
                  } else if (this$payeeBaseAcctNo.equals(other$payeeBaseAcctNo)) {
                     break label286;
                  }

                  return false;
               }

               label279: {
                  Object this$payeeAcctNo = this.getPayeeAcctNo();
                  Object other$payeeAcctNo = other.getPayeeAcctNo();
                  if (this$payeeAcctNo == null) {
                     if (other$payeeAcctNo == null) {
                        break label279;
                     }
                  } else if (this$payeeAcctNo.equals(other$payeeAcctNo)) {
                     break label279;
                  }

                  return false;
               }

               Object this$returnBaseAcctNo = this.getReturnBaseAcctNo();
               Object other$returnBaseAcctNo = other.getReturnBaseAcctNo();
               if (this$returnBaseAcctNo == null) {
                  if (other$returnBaseAcctNo != null) {
                     return false;
                  }
               } else if (!this$returnBaseAcctNo.equals(other$returnBaseAcctNo)) {
                  return false;
               }

               label265: {
                  Object this$payerAcctName = this.getPayerAcctName();
                  Object other$payerAcctName = other.getPayerAcctName();
                  if (this$payerAcctName == null) {
                     if (other$payerAcctName == null) {
                        break label265;
                     }
                  } else if (this$payerAcctName.equals(other$payerAcctName)) {
                     break label265;
                  }

                  return false;
               }

               label258: {
                  Object this$payeeAcctName = this.getPayeeAcctName();
                  Object other$payeeAcctName = other.getPayeeAcctName();
                  if (this$payeeAcctName == null) {
                     if (other$payeeAcctName == null) {
                        break label258;
                     }
                  } else if (this$payeeAcctName.equals(other$payeeAcctName)) {
                     break label258;
                  }

                  return false;
               }

               Object this$returnAcctName = this.getReturnAcctName();
               Object other$returnAcctName = other.getReturnAcctName();
               if (this$returnAcctName == null) {
                  if (other$returnAcctName != null) {
                     return false;
                  }
               } else if (!this$returnAcctName.equals(other$returnAcctName)) {
                  return false;
               }

               Object this$billApplyNo = this.getBillApplyNo();
               Object other$billApplyNo = other.getBillApplyNo();
               if (this$billApplyNo == null) {
                  if (other$billApplyNo != null) {
                     return false;
                  }
               } else if (!this$billApplyNo.equals(other$billApplyNo)) {
                  return false;
               }

               label237: {
                  Object this$dealResult = this.getDealResult();
                  Object other$dealResult = other.getDealResult();
                  if (this$dealResult == null) {
                     if (other$dealResult == null) {
                        break label237;
                     }
                  } else if (this$dealResult.equals(other$dealResult)) {
                     break label237;
                  }

                  return false;
               }

               label230: {
                  Object this$billNo = this.getBillNo();
                  Object other$billNo = other.getBillNo();
                  if (this$billNo == null) {
                     if (other$billNo == null) {
                        break label230;
                     }
                  } else if (this$billNo.equals(other$billNo)) {
                     break label230;
                  }

                  return false;
               }

               Object this$billPswd = this.getBillPswd();
               Object other$billPswd = other.getBillPswd();
               if (this$billPswd == null) {
                  if (other$billPswd != null) {
                     return false;
                  }
               } else if (!this$billPswd.equals(other$billPswd)) {
                  return false;
               }

               label216: {
                  Object this$billStatus = this.getBillStatus();
                  Object other$billStatus = other.getBillStatus();
                  if (this$billStatus == null) {
                     if (other$billStatus == null) {
                        break label216;
                     }
                  } else if (this$billStatus.equals(other$billStatus)) {
                     break label216;
                  }

                  return false;
               }

               Object this$userId = this.getUserId();
               Object other$userId = other.getUserId();
               if (this$userId == null) {
                  if (other$userId != null) {
                     return false;
                  }
               } else if (!this$userId.equals(other$userId)) {
                  return false;
               }

               label202: {
                  Object this$apprUserId = this.getApprUserId();
                  Object other$apprUserId = other.getApprUserId();
                  if (this$apprUserId == null) {
                     if (other$apprUserId == null) {
                        break label202;
                     }
                  } else if (this$apprUserId.equals(other$apprUserId)) {
                     break label202;
                  }

                  return false;
               }

               Object this$authUserId = this.getAuthUserId();
               Object other$authUserId = other.getAuthUserId();
               if (this$authUserId == null) {
                  if (other$authUserId != null) {
                     return false;
                  }
               } else if (!this$authUserId.equals(other$authUserId)) {
                  return false;
               }

               Object this$company = this.getCompany();
               Object other$company = other.getCompany();
               if (this$company == null) {
                  if (other$company != null) {
                     return false;
                  }
               } else if (!this$company.equals(other$company)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400033402Out.PnBillDetailArray;
      }
      public String toString() {
         return "Core1400033402Out.PnBillDetailArray(tranDate=" + this.getTranDate() + ", signDate=" + this.getSignDate() + ", issueBankNo=" + this.getIssueBankNo() + ", issueBankName=" + this.getIssueBankName() + ", tranBranch=" + this.getTranBranch() + ", serialNo=" + this.getSerialNo() + ", origSerialNo=" + this.getOrigSerialNo() + ", lossNo=" + this.getLossNo() + ", reference=" + this.getReference() + ", tranType=" + this.getTranType() + ", billAmt=" + this.getBillAmt() + ", feeOsdAmt=" + this.getFeeOsdAmt() + ", feeRealAmt=" + this.getFeeRealAmt() + ", ccy=" + this.getCcy() + ", tranferCashFlag=" + this.getTranferCashFlag() + ", feeChargeType=" + this.getFeeChargeType() + ", eventType=" + this.getEventType() + ", operType=" + this.getOperType() + ", billType=" + this.getBillType() + ", payeeBaseAcctNo=" + this.getPayeeBaseAcctNo() + ", payeeAcctNo=" + this.getPayeeAcctNo() + ", returnBaseAcctNo=" + this.getReturnBaseAcctNo() + ", payerAcctName=" + this.getPayerAcctName() + ", payeeAcctName=" + this.getPayeeAcctName() + ", returnAcctName=" + this.getReturnAcctName() + ", billApplyNo=" + this.getBillApplyNo() + ", dealResult=" + this.getDealResult() + ", billNo=" + this.getBillNo() + ", billPswd=" + this.getBillPswd() + ", billStatus=" + this.getBillStatus() + ", userId=" + this.getUserId() + ", apprUserId=" + this.getApprUserId() + ", authUserId=" + this.getAuthUserId() + ", company=" + this.getCompany() + ")";
      }
   }
}
