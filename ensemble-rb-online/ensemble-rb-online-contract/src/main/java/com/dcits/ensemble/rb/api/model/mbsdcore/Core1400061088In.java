package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400061088In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400061088In.Body body;

   public Core1400061088In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400061088In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400061088In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400061088In)) {
         return false;
      } else {
         Core1400061088In other = (Core1400061088In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400061088In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "交易参考号",
         notNull = false,
         length = "50",
         remark = "交易参考号",
         maxSize = 50
      )
      private String reference;
      @V(
         desc = "结售汇标识",
         notNull = false,
         length = "1",
         inDesc = "0-结汇,1-售汇",
         remark = "结售汇标识",
         maxSize = 1
      )
      private String exChangeFlag;
      @V(
         desc = "所属机构号",
         notNull = false,
         length = "50",
         remark = "所属机构号",
         maxSize = 50
      )
      private String branch;
      @V(
         desc = "结售汇类型",
         notNull = false,
         length = "10",
         remark = "结售汇类型",
         maxSize = 10
      )
      private String exchangeType;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "客户名称",
         notNull = false,
         length = "200",
         remark = "客户名称",
         maxSize = 200
      )
      private String clientName;
      @V(
         desc = "开始日期",
         notNull = false,
         remark = "开始日期"
      )
      private String startDate;
      @V(
         desc = "结束日期",
         notNull = false,
         remark = "结束日期"
      )
      private String endDate;
      @V(
         desc = "买入币种",
         notNull = false,
         length = "3",
         remark = "买入币种",
         maxSize = 3
      )
      private String buyCcy;
      @V(
         desc = "卖出币种",
         notNull = false,
         length = "3",
         remark = "卖出币种",
         maxSize = 3
      )
      private String sellCcy;
      @V(
         desc = "渠道类型",
         notNull = false,
         length = "10",
         remark = "渠道类型",
         maxSize = 10
      )
      private String sourceType;
      @V(
         desc = "证件类型",
         notNull = false,
         length = "3",
         remark = "证件类型",
         maxSize = 3
      )
      private String documentType;
      @V(
         desc = "证件号码",
         notNull = false,
         length = "2000",
         remark = "证件号码",
         maxSize = 2000
      )
      private String documentNo;
      @V(
         desc = "个体客户标志",
         notNull = false,
         length = "1",
         inDesc = "Y-是,N-否",
         remark = "是否个体客户",
         maxSize = 1
      )
      private String isIndividual;

      public String getReference() {
         return this.reference;
      }

      public String getExChangeFlag() {
         return this.exChangeFlag;
      }

      public String getBranch() {
         return this.branch;
      }

      public String getExchangeType() {
         return this.exchangeType;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public String getClientName() {
         return this.clientName;
      }

      public String getStartDate() {
         return this.startDate;
      }

      public String getEndDate() {
         return this.endDate;
      }

      public String getBuyCcy() {
         return this.buyCcy;
      }

      public String getSellCcy() {
         return this.sellCcy;
      }

      public String getSourceType() {
         return this.sourceType;
      }

      public String getDocumentType() {
         return this.documentType;
      }

      public String getDocumentNo() {
         return this.documentNo;
      }

      public String getIsIndividual() {
         return this.isIndividual;
      }

      public void setReference(String reference) {
         this.reference = reference;
      }

      public void setExChangeFlag(String exChangeFlag) {
         this.exChangeFlag = exChangeFlag;
      }

      public void setBranch(String branch) {
         this.branch = branch;
      }

      public void setExchangeType(String exchangeType) {
         this.exchangeType = exchangeType;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setClientName(String clientName) {
         this.clientName = clientName;
      }

      public void setStartDate(String startDate) {
         this.startDate = startDate;
      }

      public void setEndDate(String endDate) {
         this.endDate = endDate;
      }

      public void setBuyCcy(String buyCcy) {
         this.buyCcy = buyCcy;
      }

      public void setSellCcy(String sellCcy) {
         this.sellCcy = sellCcy;
      }

      public void setSourceType(String sourceType) {
         this.sourceType = sourceType;
      }

      public void setDocumentType(String documentType) {
         this.documentType = documentType;
      }

      public void setDocumentNo(String documentNo) {
         this.documentNo = documentNo;
      }

      public void setIsIndividual(String isIndividual) {
         this.isIndividual = isIndividual;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400061088In.Body)) {
            return false;
         } else {
            Core1400061088In.Body other = (Core1400061088In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$reference = this.getReference();
               Object other$reference = other.getReference();
               if (this$reference == null) {
                  if (other$reference != null) {
                     return false;
                  }
               } else if (!this$reference.equals(other$reference)) {
                  return false;
               }

               Object this$exChangeFlag = this.getExChangeFlag();
               Object other$exChangeFlag = other.getExChangeFlag();
               if (this$exChangeFlag == null) {
                  if (other$exChangeFlag != null) {
                     return false;
                  }
               } else if (!this$exChangeFlag.equals(other$exChangeFlag)) {
                  return false;
               }

               Object this$branch = this.getBranch();
               Object other$branch = other.getBranch();
               if (this$branch == null) {
                  if (other$branch != null) {
                     return false;
                  }
               } else if (!this$branch.equals(other$branch)) {
                  return false;
               }

               label158: {
                  Object this$exchangeType = this.getExchangeType();
                  Object other$exchangeType = other.getExchangeType();
                  if (this$exchangeType == null) {
                     if (other$exchangeType == null) {
                        break label158;
                     }
                  } else if (this$exchangeType.equals(other$exchangeType)) {
                     break label158;
                  }

                  return false;
               }

               label151: {
                  Object this$clientNo = this.getClientNo();
                  Object other$clientNo = other.getClientNo();
                  if (this$clientNo == null) {
                     if (other$clientNo == null) {
                        break label151;
                     }
                  } else if (this$clientNo.equals(other$clientNo)) {
                     break label151;
                  }

                  return false;
               }

               Object this$clientName = this.getClientName();
               Object other$clientName = other.getClientName();
               if (this$clientName == null) {
                  if (other$clientName != null) {
                     return false;
                  }
               } else if (!this$clientName.equals(other$clientName)) {
                  return false;
               }

               label137: {
                  Object this$startDate = this.getStartDate();
                  Object other$startDate = other.getStartDate();
                  if (this$startDate == null) {
                     if (other$startDate == null) {
                        break label137;
                     }
                  } else if (this$startDate.equals(other$startDate)) {
                     break label137;
                  }

                  return false;
               }

               label130: {
                  Object this$endDate = this.getEndDate();
                  Object other$endDate = other.getEndDate();
                  if (this$endDate == null) {
                     if (other$endDate == null) {
                        break label130;
                     }
                  } else if (this$endDate.equals(other$endDate)) {
                     break label130;
                  }

                  return false;
               }

               Object this$buyCcy = this.getBuyCcy();
               Object other$buyCcy = other.getBuyCcy();
               if (this$buyCcy == null) {
                  if (other$buyCcy != null) {
                     return false;
                  }
               } else if (!this$buyCcy.equals(other$buyCcy)) {
                  return false;
               }

               Object this$sellCcy = this.getSellCcy();
               Object other$sellCcy = other.getSellCcy();
               if (this$sellCcy == null) {
                  if (other$sellCcy != null) {
                     return false;
                  }
               } else if (!this$sellCcy.equals(other$sellCcy)) {
                  return false;
               }

               label109: {
                  Object this$sourceType = this.getSourceType();
                  Object other$sourceType = other.getSourceType();
                  if (this$sourceType == null) {
                     if (other$sourceType == null) {
                        break label109;
                     }
                  } else if (this$sourceType.equals(other$sourceType)) {
                     break label109;
                  }

                  return false;
               }

               label102: {
                  Object this$documentType = this.getDocumentType();
                  Object other$documentType = other.getDocumentType();
                  if (this$documentType == null) {
                     if (other$documentType == null) {
                        break label102;
                     }
                  } else if (this$documentType.equals(other$documentType)) {
                     break label102;
                  }

                  return false;
               }

               Object this$documentNo = this.getDocumentNo();
               Object other$documentNo = other.getDocumentNo();
               if (this$documentNo == null) {
                  if (other$documentNo != null) {
                     return false;
                  }
               } else if (!this$documentNo.equals(other$documentNo)) {
                  return false;
               }

               Object this$isIndividual = this.getIsIndividual();
               Object other$isIndividual = other.getIsIndividual();
               if (this$isIndividual == null) {
                  if (other$isIndividual != null) {
                     return false;
                  }
               } else if (!this$isIndividual.equals(other$isIndividual)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400061088In.Body;
      }
      public String toString() {
         return "Core1400061088In.Body(reference=" + this.getReference() + ", exChangeFlag=" + this.getExChangeFlag() + ", branch=" + this.getBranch() + ", exchangeType=" + this.getExchangeType() + ", clientNo=" + this.getClientNo() + ", clientName=" + this.getClientName() + ", startDate=" + this.getStartDate() + ", endDate=" + this.getEndDate() + ", buyCcy=" + this.getBuyCcy() + ", sellCcy=" + this.getSellCcy() + ", sourceType=" + this.getSourceType() + ", documentType=" + this.getDocumentType() + ", documentNo=" + this.getDocumentNo() + ", isIndividual=" + this.getIsIndividual() + ")";
      }
   }
}
