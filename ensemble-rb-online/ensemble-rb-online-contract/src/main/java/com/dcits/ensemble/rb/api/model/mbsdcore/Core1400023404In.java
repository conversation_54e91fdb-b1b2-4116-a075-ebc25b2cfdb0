package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;

@MessageIn
public class Core1400023404In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400023404In.Body body;

   public Core1400023404In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400023404In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400023404In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400023404In)) {
         return false;
      } else {
         Core1400023404In other = (Core1400023404In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400023404In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "交易机构",
         notNull = false,
         length = "50",
         remark = "交易机构",
         maxSize = 50
      )
      private String tranBranch;
      @V(
         desc = "交易起始日期",
         notNull = false,
         remark = "交易起始日期"
      )
      private String tranStartDate;
      @V(
         desc = "交易结束日期",
         notNull = false,
         remark = "交易结束日期"
      )
      private String tranEndDate;
      @V(
         desc = "挂失状态",
         notNull = false,
         length = "1",
         inDesc = "0-正常,1-书面挂失,2-口头挂失 ,3-已挂失,4-已解挂",
         remark = "挂失状态",
         maxSize = 1
      )
      private String lostStatus;
      @V(
         desc = "凭证类型",
         notNull = false,
         length = "10",
         remark = "凭证类型",
         maxSize = 10
      )
      private String docType;
      @V(
         desc = "凭证种类",
         notNull = false,
         length = "3",
         inDesc = "PBK-存折,CHK-支票,DCT-存单,CRD-卡,CFT-存款证明,BNK-银行票据,COL-托收票据,DFT-银行汇票,TCH-旅行支票,BAT-银行承兑汇票,CAT-商业承兑汇票,CHQ-支票,OTH-其他,SCV-印鉴",
         remark = "凭证种类",
         maxSize = 3
      )
      private String docClass;
      @V(
         desc = "票据状态",
         notNull = false,
         length = "2",
         inDesc = "00-录入,01-签发,02-兑付,03-退回,04-挂失,05-解挂,06-删除,08-挂失止付,09-公示催告,11-未复核,12-已复核,13-已打印,14-已签章核对,15-已签发冲正,16-已移存",
         remark = "票据状态",
         maxSize = 2
      )
      private String billStatus;
      @V(
         desc = "票据类型",
         notNull = false,
         length = "5",
         inDesc = "P-纸质,E-电子,CT00-可转让汇票,CT01-不可转让汇票,CT02-现金汇票",
         remark = "票据类型",
         maxSize = 5
      )
      private String billType;
      @V(
         desc = "票据号码",
         notNull = false,
         length = "50",
         remark = "票据号码",
         maxSize = 50
      )
      private String billNo;
      @V(
         desc = "票面金额",
         notNull = false,
         length = "17",
         remark = "票面金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal billAmt;
      @V(
         desc = "挂失申请书编号",
         notNull = false,
         length = "50",
         remark = "挂失申请书编号",
         maxSize = 50
      )
      private String lossNo;
      @V(
         desc = "最小金额",
         notNull = false,
         length = "17",
         remark = "最小金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal minAmt;
      @V(
         desc = "最大金额",
         notNull = false,
         length = "17",
         remark = "最大金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal maxAmt;
      @V(
         desc = "限额维护类型",
         notNull = false,
         length = "1",
         inDesc = "A-新增 ,U-修改 ,D-删除",
         remark = "限额维护类型",
         maxSize = 1
      )
      private String operType;
      @V(
         desc = "查询方式",
         notNull = false,
         length = "2",
         inDesc = "QV-根据票号查询,QF-根据参考号查询,QB-登记簿查询",
         remark = "城商行票据查询方式",
         maxSize = 2
      )
      private String queryOperateType;

      public String getTranBranch() {
         return this.tranBranch;
      }

      public String getTranStartDate() {
         return this.tranStartDate;
      }

      public String getTranEndDate() {
         return this.tranEndDate;
      }

      public String getLostStatus() {
         return this.lostStatus;
      }

      public String getDocType() {
         return this.docType;
      }

      public String getDocClass() {
         return this.docClass;
      }

      public String getBillStatus() {
         return this.billStatus;
      }

      public String getBillType() {
         return this.billType;
      }

      public String getBillNo() {
         return this.billNo;
      }

      public BigDecimal getBillAmt() {
         return this.billAmt;
      }

      public String getLossNo() {
         return this.lossNo;
      }

      public BigDecimal getMinAmt() {
         return this.minAmt;
      }

      public BigDecimal getMaxAmt() {
         return this.maxAmt;
      }

      public String getOperType() {
         return this.operType;
      }

      public String getQueryOperateType() {
         return this.queryOperateType;
      }

      public void setTranBranch(String tranBranch) {
         this.tranBranch = tranBranch;
      }

      public void setTranStartDate(String tranStartDate) {
         this.tranStartDate = tranStartDate;
      }

      public void setTranEndDate(String tranEndDate) {
         this.tranEndDate = tranEndDate;
      }

      public void setLostStatus(String lostStatus) {
         this.lostStatus = lostStatus;
      }

      public void setDocType(String docType) {
         this.docType = docType;
      }

      public void setDocClass(String docClass) {
         this.docClass = docClass;
      }

      public void setBillStatus(String billStatus) {
         this.billStatus = billStatus;
      }

      public void setBillType(String billType) {
         this.billType = billType;
      }

      public void setBillNo(String billNo) {
         this.billNo = billNo;
      }

      public void setBillAmt(BigDecimal billAmt) {
         this.billAmt = billAmt;
      }

      public void setLossNo(String lossNo) {
         this.lossNo = lossNo;
      }

      public void setMinAmt(BigDecimal minAmt) {
         this.minAmt = minAmt;
      }

      public void setMaxAmt(BigDecimal maxAmt) {
         this.maxAmt = maxAmt;
      }

      public void setOperType(String operType) {
         this.operType = operType;
      }

      public void setQueryOperateType(String queryOperateType) {
         this.queryOperateType = queryOperateType;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400023404In.Body)) {
            return false;
         } else {
            Core1400023404In.Body other = (Core1400023404In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label191: {
                  Object this$tranBranch = this.getTranBranch();
                  Object other$tranBranch = other.getTranBranch();
                  if (this$tranBranch == null) {
                     if (other$tranBranch == null) {
                        break label191;
                     }
                  } else if (this$tranBranch.equals(other$tranBranch)) {
                     break label191;
                  }

                  return false;
               }

               Object this$tranStartDate = this.getTranStartDate();
               Object other$tranStartDate = other.getTranStartDate();
               if (this$tranStartDate == null) {
                  if (other$tranStartDate != null) {
                     return false;
                  }
               } else if (!this$tranStartDate.equals(other$tranStartDate)) {
                  return false;
               }

               Object this$tranEndDate = this.getTranEndDate();
               Object other$tranEndDate = other.getTranEndDate();
               if (this$tranEndDate == null) {
                  if (other$tranEndDate != null) {
                     return false;
                  }
               } else if (!this$tranEndDate.equals(other$tranEndDate)) {
                  return false;
               }

               label170: {
                  Object this$lostStatus = this.getLostStatus();
                  Object other$lostStatus = other.getLostStatus();
                  if (this$lostStatus == null) {
                     if (other$lostStatus == null) {
                        break label170;
                     }
                  } else if (this$lostStatus.equals(other$lostStatus)) {
                     break label170;
                  }

                  return false;
               }

               label163: {
                  Object this$docType = this.getDocType();
                  Object other$docType = other.getDocType();
                  if (this$docType == null) {
                     if (other$docType == null) {
                        break label163;
                     }
                  } else if (this$docType.equals(other$docType)) {
                     break label163;
                  }

                  return false;
               }

               Object this$docClass = this.getDocClass();
               Object other$docClass = other.getDocClass();
               if (this$docClass == null) {
                  if (other$docClass != null) {
                     return false;
                  }
               } else if (!this$docClass.equals(other$docClass)) {
                  return false;
               }

               Object this$billStatus = this.getBillStatus();
               Object other$billStatus = other.getBillStatus();
               if (this$billStatus == null) {
                  if (other$billStatus != null) {
                     return false;
                  }
               } else if (!this$billStatus.equals(other$billStatus)) {
                  return false;
               }

               label142: {
                  Object this$billType = this.getBillType();
                  Object other$billType = other.getBillType();
                  if (this$billType == null) {
                     if (other$billType == null) {
                        break label142;
                     }
                  } else if (this$billType.equals(other$billType)) {
                     break label142;
                  }

                  return false;
               }

               label135: {
                  Object this$billNo = this.getBillNo();
                  Object other$billNo = other.getBillNo();
                  if (this$billNo == null) {
                     if (other$billNo == null) {
                        break label135;
                     }
                  } else if (this$billNo.equals(other$billNo)) {
                     break label135;
                  }

                  return false;
               }

               Object this$billAmt = this.getBillAmt();
               Object other$billAmt = other.getBillAmt();
               if (this$billAmt == null) {
                  if (other$billAmt != null) {
                     return false;
                  }
               } else if (!this$billAmt.equals(other$billAmt)) {
                  return false;
               }

               label121: {
                  Object this$lossNo = this.getLossNo();
                  Object other$lossNo = other.getLossNo();
                  if (this$lossNo == null) {
                     if (other$lossNo == null) {
                        break label121;
                     }
                  } else if (this$lossNo.equals(other$lossNo)) {
                     break label121;
                  }

                  return false;
               }

               Object this$minAmt = this.getMinAmt();
               Object other$minAmt = other.getMinAmt();
               if (this$minAmt == null) {
                  if (other$minAmt != null) {
                     return false;
                  }
               } else if (!this$minAmt.equals(other$minAmt)) {
                  return false;
               }

               label107: {
                  Object this$maxAmt = this.getMaxAmt();
                  Object other$maxAmt = other.getMaxAmt();
                  if (this$maxAmt == null) {
                     if (other$maxAmt == null) {
                        break label107;
                     }
                  } else if (this$maxAmt.equals(other$maxAmt)) {
                     break label107;
                  }

                  return false;
               }

               Object this$operType = this.getOperType();
               Object other$operType = other.getOperType();
               if (this$operType == null) {
                  if (other$operType != null) {
                     return false;
                  }
               } else if (!this$operType.equals(other$operType)) {
                  return false;
               }

               Object this$queryOperateType = this.getQueryOperateType();
               Object other$queryOperateType = other.getQueryOperateType();
               if (this$queryOperateType == null) {
                  if (other$queryOperateType != null) {
                     return false;
                  }
               } else if (!this$queryOperateType.equals(other$queryOperateType)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400023404In.Body;
      }
      public String toString() {
         return "Core1400023404In.Body(tranBranch=" + this.getTranBranch() + ", tranStartDate=" + this.getTranStartDate() + ", tranEndDate=" + this.getTranEndDate() + ", lostStatus=" + this.getLostStatus() + ", docType=" + this.getDocType() + ", docClass=" + this.getDocClass() + ", billStatus=" + this.getBillStatus() + ", billType=" + this.getBillType() + ", billNo=" + this.getBillNo() + ", billAmt=" + this.getBillAmt() + ", lossNo=" + this.getLossNo() + ", minAmt=" + this.getMinAmt() + ", maxAmt=" + this.getMaxAmt() + ", operType=" + this.getOperType() + ", queryOperateType=" + this.getQueryOperateType() + ")";
      }
   }
}
