package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14001005In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14001005Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14001005 {
   String URL = "/rb/inq/attr/qryproperty";


   @ApiRemark("1.属性变更后，开销户登记簿先登记一条原账户的销户记录，销户日期为维护日期；再登记一条账户的开户记录，开户日期为维护日期，账户账号不变。核心登记属性变更的历史记录，以及操作机构、操作柜员、变更日期，以供查询使用")
   @EnsembleElements(
      serviceCode = "Core",
      messageType = "1400",
      messageCode = "1005"
   )
   @BusinessCategory("1400-查询")
   @FunctionCategory("RB02-账户管理")
   @ApiUseStatus("PRODUCT-产品")
   Core14001005Out runService(Core14001005In var1);
}
