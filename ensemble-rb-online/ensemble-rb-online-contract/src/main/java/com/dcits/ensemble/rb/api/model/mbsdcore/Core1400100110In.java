package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;

@MessageIn
public class Core1400100110In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400100110In.Body body;

   public Core1400100110In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400100110In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400100110In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100110In)) {
         return false;
      } else {
         Core1400100110In other = (Core1400100110In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100110In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "开始日期",
         notNull = false,
         remark = "开始日期"
      )
      private String startDate;
      @V(
         desc = "结束日期",
         notNull = false,
         remark = "结束日期"
      )
      private String endDate;
      @V(
         desc = "开始计息日期开始区间",
         notNull = false,
         remark = "开始计息日期开始区间"
      )
      private String beginIntStartDate;
      @V(
         desc = "开始计息日期结束区间",
         notNull = false,
         remark = "开始计息日期结束区间"
      )
      private String beginIntEndDate;
      @V(
         desc = "结束计息日期开始区间",
         notNull = false,
         remark = "结束计息日期开始区间"
      )
      private String overIntStartDate;
      @V(
         desc = "结束计息日期结束区间",
         notNull = false,
         remark = "结束计息日期结束区间"
      )
      private String overIntEndDate;
      @V(
         desc = "最低利息",
         notNull = false,
         length = "17",
         remark = "最低利息",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal lowestInt;
      @V(
         desc = "最高利息",
         notNull = false,
         length = "17",
         remark = "最高利息",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal highestInt;
      @V(
         desc = "最低利率",
         notNull = false,
         length = "15",
         remark = "最低利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal lowestRate;
      @V(
         desc = "最高利率",
         notNull = false,
         length = "15",
         remark = "最高利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal highestRate;
      @V(
         desc = "协议类型",
         notNull = false,
         length = "10",
         inDesc = "CLD-存立得,DC-大额存单,DLS-贷利省,HQB-活期宝,JDL-加多利,KDT-卡贷通,KYD-卡易贷,PCP-资金池,WDL-稳得利,XDB-协定宝,XDCK-协定存款产品,XDL-先得利,YBWL-一本万利,YCD-英才贷,YDT-易贷通,YHT-一户通,ZHY-周享赢,ZXY-坐享其盈,ZZB-至尊宝,LOA-贷款,ODF-法人透支协议,FIN-卡理财协议,SMS-短信,PKG-费用套餐,FEE-暂不收费,PCD-周期性强制扣划,ACC-协定存款协议,SWP-账户清扫协议,ID-智能存款协议,SL-金额补足协议,REC-回单签约,ES-电票签约,YD-约定,NTE-活期智能存款,PAS-隐私账户签约,BXD-协定利率（无留存）",
         remark = "协议类型",
         maxSize = 10
      )
      private String agreementType;
      @V(
         desc = "交易日期排序方式",
         notNull = false,
         length = "3",
         remark = "交易日期排序方式",
         maxSize = 3
      )
      private String tranDateOrder;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getStartDate() {
         return this.startDate;
      }

      public String getEndDate() {
         return this.endDate;
      }

      public String getBeginIntStartDate() {
         return this.beginIntStartDate;
      }

      public String getBeginIntEndDate() {
         return this.beginIntEndDate;
      }

      public String getOverIntStartDate() {
         return this.overIntStartDate;
      }

      public String getOverIntEndDate() {
         return this.overIntEndDate;
      }

      public BigDecimal getLowestInt() {
         return this.lowestInt;
      }

      public BigDecimal getHighestInt() {
         return this.highestInt;
      }

      public BigDecimal getLowestRate() {
         return this.lowestRate;
      }

      public BigDecimal getHighestRate() {
         return this.highestRate;
      }

      public String getAgreementType() {
         return this.agreementType;
      }

      public String getTranDateOrder() {
         return this.tranDateOrder;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setStartDate(String startDate) {
         this.startDate = startDate;
      }

      public void setEndDate(String endDate) {
         this.endDate = endDate;
      }

      public void setBeginIntStartDate(String beginIntStartDate) {
         this.beginIntStartDate = beginIntStartDate;
      }

      public void setBeginIntEndDate(String beginIntEndDate) {
         this.beginIntEndDate = beginIntEndDate;
      }

      public void setOverIntStartDate(String overIntStartDate) {
         this.overIntStartDate = overIntStartDate;
      }

      public void setOverIntEndDate(String overIntEndDate) {
         this.overIntEndDate = overIntEndDate;
      }

      public void setLowestInt(BigDecimal lowestInt) {
         this.lowestInt = lowestInt;
      }

      public void setHighestInt(BigDecimal highestInt) {
         this.highestInt = highestInt;
      }

      public void setLowestRate(BigDecimal lowestRate) {
         this.lowestRate = lowestRate;
      }

      public void setHighestRate(BigDecimal highestRate) {
         this.highestRate = highestRate;
      }

      public void setAgreementType(String agreementType) {
         this.agreementType = agreementType;
      }

      public void setTranDateOrder(String tranDateOrder) {
         this.tranDateOrder = tranDateOrder;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100110In.Body)) {
            return false;
         } else {
            Core1400100110In.Body other = (Core1400100110In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label167: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label167;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label167;
                  }

                  return false;
               }

               Object this$startDate = this.getStartDate();
               Object other$startDate = other.getStartDate();
               if (this$startDate == null) {
                  if (other$startDate != null) {
                     return false;
                  }
               } else if (!this$startDate.equals(other$startDate)) {
                  return false;
               }

               label153: {
                  Object this$endDate = this.getEndDate();
                  Object other$endDate = other.getEndDate();
                  if (this$endDate == null) {
                     if (other$endDate == null) {
                        break label153;
                     }
                  } else if (this$endDate.equals(other$endDate)) {
                     break label153;
                  }

                  return false;
               }

               Object this$beginIntStartDate = this.getBeginIntStartDate();
               Object other$beginIntStartDate = other.getBeginIntStartDate();
               if (this$beginIntStartDate == null) {
                  if (other$beginIntStartDate != null) {
                     return false;
                  }
               } else if (!this$beginIntStartDate.equals(other$beginIntStartDate)) {
                  return false;
               }

               label139: {
                  Object this$beginIntEndDate = this.getBeginIntEndDate();
                  Object other$beginIntEndDate = other.getBeginIntEndDate();
                  if (this$beginIntEndDate == null) {
                     if (other$beginIntEndDate == null) {
                        break label139;
                     }
                  } else if (this$beginIntEndDate.equals(other$beginIntEndDate)) {
                     break label139;
                  }

                  return false;
               }

               Object this$overIntStartDate = this.getOverIntStartDate();
               Object other$overIntStartDate = other.getOverIntStartDate();
               if (this$overIntStartDate == null) {
                  if (other$overIntStartDate != null) {
                     return false;
                  }
               } else if (!this$overIntStartDate.equals(other$overIntStartDate)) {
                  return false;
               }

               label125: {
                  Object this$overIntEndDate = this.getOverIntEndDate();
                  Object other$overIntEndDate = other.getOverIntEndDate();
                  if (this$overIntEndDate == null) {
                     if (other$overIntEndDate == null) {
                        break label125;
                     }
                  } else if (this$overIntEndDate.equals(other$overIntEndDate)) {
                     break label125;
                  }

                  return false;
               }

               label118: {
                  Object this$lowestInt = this.getLowestInt();
                  Object other$lowestInt = other.getLowestInt();
                  if (this$lowestInt == null) {
                     if (other$lowestInt == null) {
                        break label118;
                     }
                  } else if (this$lowestInt.equals(other$lowestInt)) {
                     break label118;
                  }

                  return false;
               }

               Object this$highestInt = this.getHighestInt();
               Object other$highestInt = other.getHighestInt();
               if (this$highestInt == null) {
                  if (other$highestInt != null) {
                     return false;
                  }
               } else if (!this$highestInt.equals(other$highestInt)) {
                  return false;
               }

               label104: {
                  Object this$lowestRate = this.getLowestRate();
                  Object other$lowestRate = other.getLowestRate();
                  if (this$lowestRate == null) {
                     if (other$lowestRate == null) {
                        break label104;
                     }
                  } else if (this$lowestRate.equals(other$lowestRate)) {
                     break label104;
                  }

                  return false;
               }

               label97: {
                  Object this$highestRate = this.getHighestRate();
                  Object other$highestRate = other.getHighestRate();
                  if (this$highestRate == null) {
                     if (other$highestRate == null) {
                        break label97;
                     }
                  } else if (this$highestRate.equals(other$highestRate)) {
                     break label97;
                  }

                  return false;
               }

               Object this$agreementType = this.getAgreementType();
               Object other$agreementType = other.getAgreementType();
               if (this$agreementType == null) {
                  if (other$agreementType != null) {
                     return false;
                  }
               } else if (!this$agreementType.equals(other$agreementType)) {
                  return false;
               }

               Object this$tranDateOrder = this.getTranDateOrder();
               Object other$tranDateOrder = other.getTranDateOrder();
               if (this$tranDateOrder == null) {
                  if (other$tranDateOrder != null) {
                     return false;
                  }
               } else if (!this$tranDateOrder.equals(other$tranDateOrder)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100110In.Body;
      }
      public String toString() {
         return "Core1400100110In.Body(baseAcctNo=" + this.getBaseAcctNo() + ", startDate=" + this.getStartDate() + ", endDate=" + this.getEndDate() + ", beginIntStartDate=" + this.getBeginIntStartDate() + ", beginIntEndDate=" + this.getBeginIntEndDate() + ", overIntStartDate=" + this.getOverIntStartDate() + ", overIntEndDate=" + this.getOverIntEndDate() + ", lowestInt=" + this.getLowestInt() + ", highestInt=" + this.getHighestInt() + ", lowestRate=" + this.getLowestRate() + ", highestRate=" + this.getHighestRate() + ", agreementType=" + this.getAgreementType() + ", tranDateOrder=" + this.getTranDateOrder() + ")";
      }
   }
}
