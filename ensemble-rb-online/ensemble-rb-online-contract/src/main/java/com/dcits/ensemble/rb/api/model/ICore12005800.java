package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12005800In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12005800Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12005800 {
   String URL = "/rb/nfin/channel/receipt/print/update";


   @ApiRemark("标准优化")
   @ApiDesc("回单打印")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "5800"
   )
   @BusinessCategory("1200-非金融")
   @FunctionCategory("RB01-公共服务")
   @ConsumeSys("TLE")
   @ApiUseStatus("PROJECT-项目")
   Core12005800Out runService(Core12005800In var1);
}
