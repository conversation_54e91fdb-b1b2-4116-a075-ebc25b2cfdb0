package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000997In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000997Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12000997 {
   String URL = "/rb/unify/openclose";


   @ApiRemark("标准优化")
   @ApiDesc("个人定期批开批销,支持个人定期的批量开户和销户;支持多资金来源和多资金去向;支持现金转账挂销账")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0997"
   )
   @BusinessCategory("1200-非金融")
   @FunctionCategory("RB02-账户管理")
   @ApiUseStatus("PRODUCT-产品")
   Core12000997Out runService(Core12000997In var1);
}
