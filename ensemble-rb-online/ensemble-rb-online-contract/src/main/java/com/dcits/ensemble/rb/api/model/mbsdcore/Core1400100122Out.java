package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400100122Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100122Out.TranHistArray> tranHistArray;

   public List<Core1400100122Out.TranHistArray> getTranHistArray() {
      return this.tranHistArray;
   }

   public void setTranHistArray(List<Core1400100122Out.TranHistArray> tranHistArray) {
      this.tranHistArray = tranHistArray;
   }

   public String toString() {
      return "Core1400100122Out(tranHistArray=" + this.getTranHistArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100122Out)) {
         return false;
      } else {
         Core1400100122Out other = (Core1400100122Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$tranHistArray = this.getTranHistArray();
            Object other$tranHistArray = other.getTranHistArray();
            if (this$tranHistArray == null) {
               if (other$tranHistArray != null) {
                  return false;
               }
            } else if (!this$tranHistArray.equals(other$tranHistArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100122Out;
   }
   public static class TranHistArray {
      @V(
         desc = "序号",
         notNull = false,
         length = "50",
         remark = "序号",
         maxSize = 50
      )
      private String seqNo;
      @V(
         desc = "主交易序号",
         notNull = false,
         length = "50",
         remark = "主交易序号",
         maxSize = 50
      )
      private String primaryTranSeqNo;
      @V(
         desc = "资金冻结流水号",
         notNull = false,
         length = "50",
         remark = "记录冻结编号信息",
         maxSize = 50
      )
      private String fhSeqNo;
      @V(
         desc = "渠道流水号",
         notNull = false,
         length = "50",
         remark = "渠道流水号",
         maxSize = 50
      )
      private String channelSeqNo;
      @V(
         desc = "证件号码",
         notNull = false,
         length = "50",
         remark = "证件号码",
         maxSize = 50
      )
      private String documentId;
      @V(
         desc = "证件类型",
         notNull = false,
         length = "3",
         remark = "证件类型",
         maxSize = 3
      )
      private String documentType;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "客户中文名称",
         notNull = false,
         length = "200",
         remark = "客户中文名称",
         maxSize = 200
      )
      private String chClientName;
      @V(
         desc = "客户类型",
         notNull = false,
         length = "3",
         remark = "客户大类，目前一般分为个人，公司，金融机构和内部客户。取之于CIF_CLIENT_TYPE.CLIENT_TYPE",
         maxSize = 3
      )
      private String clientType;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "账户状态",
         notNull = false,
         length = "1",
         remark = "描述账户生命周期不同阶段的划分",
         maxSize = 1
      )
      private String acctStatus;
      @V(
         desc = "账户内部键值",
         notNull = false,
         length = "15",
         remark = "账户内部键值"
      )
      private Long internalKey;
      @V(
         desc = "账户开户行",
         notNull = false,
         length = "50",
         remark = "账户实际开户机构，柜面为实际网点机构，线上渠道一般为对应主账户的实际开户机构",
         maxSize = 50
      )
      private String acctBranch;
      @V(
         desc = "账户交易标志",
         notNull = false,
         length = "1",
         remark = "目前使用M代表现金交易",
         maxSize = 1
      )
      private String acctTranFlag;
      @V(
         desc = "实际余额",
         notNull = false,
         length = "17",
         remark = "实际余额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal actualBal;
      @V(
         desc = "账户描述",
         notNull = false,
         length = "200",
         remark = "账户描述,目前同账户名称",
         maxSize = 200
      )
      private String acctDesc;
      @V(
         desc = "对手账户内部键",
         notNull = false,
         length = "15",
         remark = "对手账户内部键"
      )
      private Long othInternalKey;
      @V(
         desc = "对方账户描述",
         notNull = false,
         length = "200",
         remark = "对方账户描述",
         maxSize = 200
      )
      private String othAcctDesc;
      @V(
         desc = "账户用途",
         notNull = false,
         length = "10",
         remark = "账户用途",
         maxSize = 10
      )
      private String reasonCode;
      @V(
         desc = "生效日期",
         notNull = false,
         remark = "生效日期"
      )
      private String effectDate;
      @V(
         desc = "冲正日期",
         notNull = false,
         remark = "冲正日期"
      )
      private String reversalDate;
      @V(
         desc = "交易日期",
         notNull = false,
         remark = "交易日期"
      )
      private String tranDate;
      @V(
         desc = "交易前余额",
         notNull = false,
         length = "17",
         remark = "交易前余额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal previousBalAmt;
      @V(
         desc = "他行等值金额",
         notNull = false,
         length = "17",
         remark = "他行等值金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal contraEquivAmt;
      @V(
         desc = "基础等值金额",
         notNull = false,
         length = "17",
         remark = "基础等值金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal baseEquivAmt;
      @V(
         desc = "交易金额",
         notNull = false,
         length = "17",
         remark = "交易金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal tranAmt;
      @V(
         desc = "移出金额",
         notNull = false,
         length = "17",
         remark = "移出金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal fromAmount;
      @V(
         desc = "移入金额",
         notNull = false,
         length = "17",
         remark = "移入金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal toAmount;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "交叉汇率",
         notNull = false,
         length = "15",
         remark = "交叉汇率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal crossRate;
      @V(
         desc = "买方交易汇率标志",
         notNull = false,
         length = "1",
         remark = "买方交易汇率标志",
         maxSize = 1
      )
      private String fromRateFlag;
      @V(
         desc = "买方汇率值",
         notNull = false,
         length = "15",
         remark = "买方汇率值",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal fromXrate;
      @V(
         desc = "卖方交易汇率标志",
         notNull = false,
         length = "1",
         remark = "卖方交易汇率标志",
         maxSize = 1
      )
      private String toRateFlag;
      @V(
         desc = "卖方汇率值",
         notNull = false,
         length = "15",
         remark = "卖方汇率值",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal toXrate;
      @V(
         desc = "实际交易时修改交叉汇率",
         notNull = false,
         length = "15",
         remark = "实际交易时修改交叉汇率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal ovCrossRate;
      @V(
         desc = "交易对手身份证件/证明文件类型",
         notNull = false,
         length = "3",
         remark = "交易对手身份证件/证明文件类型",
         maxSize = 3
      )
      private String othDocumentType;
      @V(
         desc = "支取方式",
         notNull = false,
         length = "1",
         remark = "支取方式",
         maxSize = 1
      )
      private String withdrawalType;
      @V(
         desc = "事件类型",
         notNull = false,
         length = "20",
         remark = "事件类型",
         maxSize = 20
      )
      private String eventType;
      @V(
         desc = "汇率类型",
         notNull = false,
         length = "10",
         remark = "汇率类型",
         maxSize = 10
      )
      private String rateType;
      @V(
         desc = "余额类型",
         notNull = false,
         length = "2",
         remark = "余额类型",
         maxSize = 2
      )
      private String balType;
      @V(
         desc = "中间业务类型",
         notNull = false,
         length = "10",
         remark = "中间业务类型",
         maxSize = 10
      )
      private String bizType;
      @V(
         desc = "汇率标志",
         notNull = false,
         length = "1",
         remark = "汇率标志",
         maxSize = 1
      )
      private String rateFlag;
      @V(
         desc = "牌价类型",
         notNull = false,
         length = "1",
         remark = "牌价类型",
         maxSize = 1
      )
      private String quoteType;
      @V(
         desc = "是否补登存",
         notNull = false,
         length = "1",
         remark = "是否补登存",
         maxSize = 1
      )
      private String pbkUpdFlag;
      @V(
         desc = "金额类型",
         notNull = false,
         length = "10",
         remark = "金额类型",
         maxSize = 10
      )
      private String amtType;
      @V(
         desc = "渠道类型",
         notNull = false,
         length = "10",
         remark = "渠道类型",
         maxSize = 10
      )
      private String sourceType;
      @V(
         desc = "交易种类",
         notNull = false,
         length = "5",
         remark = "交易种类",
         maxSize = 5
      )
      private String tranCategory;
      @V(
         desc = "交易描述",
         notNull = false,
         length = "200",
         remark = "交易描述",
         maxSize = 200
      )
      private String tranDesc;
      @V(
         desc = "交易附言",
         notNull = false,
         length = "2000",
         remark = "交易附言",
         maxSize = 2000
      )
      private String tranNote;
      @V(
         desc = "对方交易流水号",
         notNull = false,
         length = "50",
         remark = "对方交易流水号",
         maxSize = 50
      )
      private String othSeqNo;
      @V(
         desc = "对方账户产品类型",
         notNull = false,
         length = "20",
         remark = "对方账户产品类型",
         maxSize = 20
      )
      private String othProdType;
      @V(
         desc = "对方账号/卡号",
         notNull = false,
         length = "50",
         remark = "对方账号/卡号",
         maxSize = 50
      )
      private String othBaseAcctNo;
      @V(
         desc = "起始币种",
         notNull = false,
         length = "3",
         remark = "源币种",
         maxSize = 3
      )
      private String fromCcy;
      @V(
         desc = "目的币种",
         notNull = false,
         length = "3",
         remark = "目标币种",
         maxSize = 3
      )
      private String toCcy;
      @V(
         desc = "对方账户币种",
         notNull = false,
         length = "3",
         remark = "对方账户币种",
         maxSize = 3
      )
      private String othAcctCcy;
      @V(
         desc = "他行币种",
         notNull = false,
         length = "3",
         remark = "他行币种",
         maxSize = 3
      )
      private String contraAcctCcy;
      @V(
         desc = "对方账户序列号",
         notNull = false,
         length = "5",
         remark = "对方账户序列号",
         maxSize = 5
      )
      private String othAcctSeqNo;
      @V(
         desc = "交易对手身份证件/证明文件号码",
         notNull = false,
         length = "50",
         remark = "交易对手身份证件/证明文件号码",
         maxSize = 50
      )
      private String othDocumentId;
      @V(
         desc = "银行交易序号",
         notNull = false,
         length = "50",
         remark = "银行交易序号,单一机构下发生交易序号，按顺序递增 格式为 \"机构_序号",
         maxSize = 50
      )
      private String bankSeqNo;
      @V(
         desc = "终端号",
         notNull = false,
         length = "50",
         remark = "终端号",
         maxSize = 50
      )
      private String terminalId;
      @V(
         desc = "地区码",
         notNull = false,
         length = "5",
         remark = "地区码",
         maxSize = 5
      )
      private String areaCode;
      @V(
         desc = "打印页",
         notNull = false,
         length = "5",
         remark = "打印页"
      )
      private Integer printPageNo;
      @V(
         desc = "打印行",
         notNull = false,
         length = "5",
         remark = "打印行"
      )
      private Integer printLineNo;
      @V(
         desc = "跟踪ID",
         notNull = false,
         length = "200",
         remark = "跟踪ID",
         maxSize = 200
      )
      private String traceId;
      @V(
         desc = "摘要",
         notNull = false,
         length = "500",
         remark = "开户时的账号用途，销户时的销户原因",
         maxSize = 500
      )
      private String narrative;
      @V(
         desc = "现金项目",
         notNull = false,
         length = "10",
         remark = "现金项目",
         maxSize = 10
      )
      private String cashItem;
      @V(
         desc = "业务处理状态",
         notNull = false,
         length = "1",
         remark = "业务处理状态",
         maxSize = 1
      )
      private String tranStatus;
      @V(
         desc = "利润中心",
         notNull = false,
         length = "20",
         remark = "利润中心",
         maxSize = 20
      )
      private String profitCenter;
      @V(
         desc = "账套",
         notNull = false,
         length = "10",
         remark = "账套",
         maxSize = 10
      )
      private String businessUnit;
      @V(
         desc = "源模块",
         notNull = false,
         length = "3",
         remark = "源模块",
         maxSize = 3
      )
      private String sourceModule;
      @V(
         desc = "回收号",
         notNull = false,
         length = "50",
         remark = "回收号",
         maxSize = 50
      )
      private String receiptNo;
      @V(
         desc = "短信发送优先级",
         notNull = false,
         length = "1",
         remark = "短信定义表中，定义各类型短信发送优先级",
         maxSize = 1
      )
      private String sendPriority;
      @V(
         desc = "借贷标识",
         notNull = false,
         length = "1",
         remark = "借贷标识",
         maxSize = 1
      )
      private String crDrMaintInd;
      @V(
         desc = "打印次数",
         notNull = false,
         length = "5",
         remark = "银行承兑汇票登记簿凭证打印次数"
      )
      private Integer printCnt;
      @V(
         desc = "批次号",
         notNull = false,
         length = "50",
         remark = "批次号",
         maxSize = 50
      )
      private String batchNo;
      @V(
         desc = "卖方牌价类型",
         notNull = false,
         length = "1",
         remark = "牌价类型",
         maxSize = 1
      )
      private String toId;
      @V(
         desc = "贷款人",
         notNull = false,
         length = "100",
         remark = "贷款人",
         maxSize = 100
      )
      private String lender;
      @V(
         desc = "核算状态",
         notNull = false,
         length = "3",
         remark = "核算状态，为贷款核算状态类型，会计部门根据借款凭证针对借款合同进行审核的贷款核算分级审批制度",
         maxSize = 3
      )
      private String accountingStatus;
      @V(
         desc = "是否冲正标志",
         notNull = false,
         length = "1",
         remark = "是否冲正标志",
         maxSize = 1
      )
      private String reversal;
      @V(
         desc = "交款单位",
         notNull = false,
         length = "200",
         remark = "交款单位",
         maxSize = 200
      )
      private String payUnit;
      @V(
         desc = "代办/代理人电话",
         notNull = false,
         length = "20",
         remark = "代办/代理人电话",
         maxSize = 20
      )
      private String commissionClientTel;
      @V(
         desc = "交易柜员",
         notNull = false,
         length = "30",
         remark = "交易柜员",
         maxSize = 30
      )
      private String userId;
      @V(
         desc = "授权柜员",
         notNull = false,
         length = "30",
         remark = "授权柜员",
         maxSize = 30
      )
      private String authUserId;
      @V(
         desc = "复核柜员",
         notNull = false,
         length = "30",
         remark = "复核柜员",
         maxSize = 30
      )
      private String apprUserId;
      @V(
         desc = "交易时间戳",
         notNull = false,
         length = "26",
         remark = "交易时间戳",
         maxSize = 26
      )
      private String tranTimestamp;
      @V(
         desc = "代办人名称",
         notNull = false,
         length = "200",
         remark = "代办人名称",
         maxSize = 200
      )
      private String commissionClientName;
      @V(
         desc = "服务费标识",
         notNull = false,
         length = "1",
         remark = "服务费标识",
         maxSize = 1
      )
      private String servCharge;
      @V(
         desc = "法人",
         notNull = false,
         length = "20",
         remark = "法人",
         maxSize = 20
      )
      private String company;

      public String getSeqNo() {
         return this.seqNo;
      }

      public String getPrimaryTranSeqNo() {
         return this.primaryTranSeqNo;
      }

      public String getFhSeqNo() {
         return this.fhSeqNo;
      }

      public String getChannelSeqNo() {
         return this.channelSeqNo;
      }

      public String getDocumentId() {
         return this.documentId;
      }

      public String getDocumentType() {
         return this.documentType;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public String getChClientName() {
         return this.chClientName;
      }

      public String getClientType() {
         return this.clientType;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getAcctStatus() {
         return this.acctStatus;
      }

      public Long getInternalKey() {
         return this.internalKey;
      }

      public String getAcctBranch() {
         return this.acctBranch;
      }

      public String getAcctTranFlag() {
         return this.acctTranFlag;
      }

      public BigDecimal getActualBal() {
         return this.actualBal;
      }

      public String getAcctDesc() {
         return this.acctDesc;
      }

      public Long getOthInternalKey() {
         return this.othInternalKey;
      }

      public String getOthAcctDesc() {
         return this.othAcctDesc;
      }

      public String getReasonCode() {
         return this.reasonCode;
      }

      public String getEffectDate() {
         return this.effectDate;
      }

      public String getReversalDate() {
         return this.reversalDate;
      }

      public String getTranDate() {
         return this.tranDate;
      }

      public BigDecimal getPreviousBalAmt() {
         return this.previousBalAmt;
      }

      public BigDecimal getContraEquivAmt() {
         return this.contraEquivAmt;
      }

      public BigDecimal getBaseEquivAmt() {
         return this.baseEquivAmt;
      }

      public BigDecimal getTranAmt() {
         return this.tranAmt;
      }

      public BigDecimal getFromAmount() {
         return this.fromAmount;
      }

      public BigDecimal getToAmount() {
         return this.toAmount;
      }

      public String getCcy() {
         return this.ccy;
      }

      public BigDecimal getCrossRate() {
         return this.crossRate;
      }

      public String getFromRateFlag() {
         return this.fromRateFlag;
      }

      public BigDecimal getFromXrate() {
         return this.fromXrate;
      }

      public String getToRateFlag() {
         return this.toRateFlag;
      }

      public BigDecimal getToXrate() {
         return this.toXrate;
      }

      public BigDecimal getOvCrossRate() {
         return this.ovCrossRate;
      }

      public String getOthDocumentType() {
         return this.othDocumentType;
      }

      public String getWithdrawalType() {
         return this.withdrawalType;
      }

      public String getEventType() {
         return this.eventType;
      }

      public String getRateType() {
         return this.rateType;
      }

      public String getBalType() {
         return this.balType;
      }

      public String getBizType() {
         return this.bizType;
      }

      public String getRateFlag() {
         return this.rateFlag;
      }

      public String getQuoteType() {
         return this.quoteType;
      }

      public String getPbkUpdFlag() {
         return this.pbkUpdFlag;
      }

      public String getAmtType() {
         return this.amtType;
      }

      public String getSourceType() {
         return this.sourceType;
      }

      public String getTranCategory() {
         return this.tranCategory;
      }

      public String getTranDesc() {
         return this.tranDesc;
      }

      public String getTranNote() {
         return this.tranNote;
      }

      public String getOthSeqNo() {
         return this.othSeqNo;
      }

      public String getOthProdType() {
         return this.othProdType;
      }

      public String getOthBaseAcctNo() {
         return this.othBaseAcctNo;
      }

      public String getFromCcy() {
         return this.fromCcy;
      }

      public String getToCcy() {
         return this.toCcy;
      }

      public String getOthAcctCcy() {
         return this.othAcctCcy;
      }

      public String getContraAcctCcy() {
         return this.contraAcctCcy;
      }

      public String getOthAcctSeqNo() {
         return this.othAcctSeqNo;
      }

      public String getOthDocumentId() {
         return this.othDocumentId;
      }

      public String getBankSeqNo() {
         return this.bankSeqNo;
      }

      public String getTerminalId() {
         return this.terminalId;
      }

      public String getAreaCode() {
         return this.areaCode;
      }

      public Integer getPrintPageNo() {
         return this.printPageNo;
      }

      public Integer getPrintLineNo() {
         return this.printLineNo;
      }

      public String getTraceId() {
         return this.traceId;
      }

      public String getNarrative() {
         return this.narrative;
      }

      public String getCashItem() {
         return this.cashItem;
      }

      public String getTranStatus() {
         return this.tranStatus;
      }

      public String getProfitCenter() {
         return this.profitCenter;
      }

      public String getBusinessUnit() {
         return this.businessUnit;
      }

      public String getSourceModule() {
         return this.sourceModule;
      }

      public String getReceiptNo() {
         return this.receiptNo;
      }

      public String getSendPriority() {
         return this.sendPriority;
      }

      public String getCrDrMaintInd() {
         return this.crDrMaintInd;
      }

      public Integer getPrintCnt() {
         return this.printCnt;
      }

      public String getBatchNo() {
         return this.batchNo;
      }

      public String getToId() {
         return this.toId;
      }

      public String getLender() {
         return this.lender;
      }

      public String getAccountingStatus() {
         return this.accountingStatus;
      }

      public String getReversal() {
         return this.reversal;
      }

      public String getPayUnit() {
         return this.payUnit;
      }

      public String getCommissionClientTel() {
         return this.commissionClientTel;
      }

      public String getUserId() {
         return this.userId;
      }

      public String getAuthUserId() {
         return this.authUserId;
      }

      public String getApprUserId() {
         return this.apprUserId;
      }

      public String getTranTimestamp() {
         return this.tranTimestamp;
      }

      public String getCommissionClientName() {
         return this.commissionClientName;
      }

      public String getServCharge() {
         return this.servCharge;
      }

      public String getCompany() {
         return this.company;
      }

      public void setSeqNo(String seqNo) {
         this.seqNo = seqNo;
      }

      public void setPrimaryTranSeqNo(String primaryTranSeqNo) {
         this.primaryTranSeqNo = primaryTranSeqNo;
      }

      public void setFhSeqNo(String fhSeqNo) {
         this.fhSeqNo = fhSeqNo;
      }

      public void setChannelSeqNo(String channelSeqNo) {
         this.channelSeqNo = channelSeqNo;
      }

      public void setDocumentId(String documentId) {
         this.documentId = documentId;
      }

      public void setDocumentType(String documentType) {
         this.documentType = documentType;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setChClientName(String chClientName) {
         this.chClientName = chClientName;
      }

      public void setClientType(String clientType) {
         this.clientType = clientType;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setAcctStatus(String acctStatus) {
         this.acctStatus = acctStatus;
      }

      public void setInternalKey(Long internalKey) {
         this.internalKey = internalKey;
      }

      public void setAcctBranch(String acctBranch) {
         this.acctBranch = acctBranch;
      }

      public void setAcctTranFlag(String acctTranFlag) {
         this.acctTranFlag = acctTranFlag;
      }

      public void setActualBal(BigDecimal actualBal) {
         this.actualBal = actualBal;
      }

      public void setAcctDesc(String acctDesc) {
         this.acctDesc = acctDesc;
      }

      public void setOthInternalKey(Long othInternalKey) {
         this.othInternalKey = othInternalKey;
      }

      public void setOthAcctDesc(String othAcctDesc) {
         this.othAcctDesc = othAcctDesc;
      }

      public void setReasonCode(String reasonCode) {
         this.reasonCode = reasonCode;
      }

      public void setEffectDate(String effectDate) {
         this.effectDate = effectDate;
      }

      public void setReversalDate(String reversalDate) {
         this.reversalDate = reversalDate;
      }

      public void setTranDate(String tranDate) {
         this.tranDate = tranDate;
      }

      public void setPreviousBalAmt(BigDecimal previousBalAmt) {
         this.previousBalAmt = previousBalAmt;
      }

      public void setContraEquivAmt(BigDecimal contraEquivAmt) {
         this.contraEquivAmt = contraEquivAmt;
      }

      public void setBaseEquivAmt(BigDecimal baseEquivAmt) {
         this.baseEquivAmt = baseEquivAmt;
      }

      public void setTranAmt(BigDecimal tranAmt) {
         this.tranAmt = tranAmt;
      }

      public void setFromAmount(BigDecimal fromAmount) {
         this.fromAmount = fromAmount;
      }

      public void setToAmount(BigDecimal toAmount) {
         this.toAmount = toAmount;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setCrossRate(BigDecimal crossRate) {
         this.crossRate = crossRate;
      }

      public void setFromRateFlag(String fromRateFlag) {
         this.fromRateFlag = fromRateFlag;
      }

      public void setFromXrate(BigDecimal fromXrate) {
         this.fromXrate = fromXrate;
      }

      public void setToRateFlag(String toRateFlag) {
         this.toRateFlag = toRateFlag;
      }

      public void setToXrate(BigDecimal toXrate) {
         this.toXrate = toXrate;
      }

      public void setOvCrossRate(BigDecimal ovCrossRate) {
         this.ovCrossRate = ovCrossRate;
      }

      public void setOthDocumentType(String othDocumentType) {
         this.othDocumentType = othDocumentType;
      }

      public void setWithdrawalType(String withdrawalType) {
         this.withdrawalType = withdrawalType;
      }

      public void setEventType(String eventType) {
         this.eventType = eventType;
      }

      public void setRateType(String rateType) {
         this.rateType = rateType;
      }

      public void setBalType(String balType) {
         this.balType = balType;
      }

      public void setBizType(String bizType) {
         this.bizType = bizType;
      }

      public void setRateFlag(String rateFlag) {
         this.rateFlag = rateFlag;
      }

      public void setQuoteType(String quoteType) {
         this.quoteType = quoteType;
      }

      public void setPbkUpdFlag(String pbkUpdFlag) {
         this.pbkUpdFlag = pbkUpdFlag;
      }

      public void setAmtType(String amtType) {
         this.amtType = amtType;
      }

      public void setSourceType(String sourceType) {
         this.sourceType = sourceType;
      }

      public void setTranCategory(String tranCategory) {
         this.tranCategory = tranCategory;
      }

      public void setTranDesc(String tranDesc) {
         this.tranDesc = tranDesc;
      }

      public void setTranNote(String tranNote) {
         this.tranNote = tranNote;
      }

      public void setOthSeqNo(String othSeqNo) {
         this.othSeqNo = othSeqNo;
      }

      public void setOthProdType(String othProdType) {
         this.othProdType = othProdType;
      }

      public void setOthBaseAcctNo(String othBaseAcctNo) {
         this.othBaseAcctNo = othBaseAcctNo;
      }

      public void setFromCcy(String fromCcy) {
         this.fromCcy = fromCcy;
      }

      public void setToCcy(String toCcy) {
         this.toCcy = toCcy;
      }

      public void setOthAcctCcy(String othAcctCcy) {
         this.othAcctCcy = othAcctCcy;
      }

      public void setContraAcctCcy(String contraAcctCcy) {
         this.contraAcctCcy = contraAcctCcy;
      }

      public void setOthAcctSeqNo(String othAcctSeqNo) {
         this.othAcctSeqNo = othAcctSeqNo;
      }

      public void setOthDocumentId(String othDocumentId) {
         this.othDocumentId = othDocumentId;
      }

      public void setBankSeqNo(String bankSeqNo) {
         this.bankSeqNo = bankSeqNo;
      }

      public void setTerminalId(String terminalId) {
         this.terminalId = terminalId;
      }

      public void setAreaCode(String areaCode) {
         this.areaCode = areaCode;
      }

      public void setPrintPageNo(Integer printPageNo) {
         this.printPageNo = printPageNo;
      }

      public void setPrintLineNo(Integer printLineNo) {
         this.printLineNo = printLineNo;
      }

      public void setTraceId(String traceId) {
         this.traceId = traceId;
      }

      public void setNarrative(String narrative) {
         this.narrative = narrative;
      }

      public void setCashItem(String cashItem) {
         this.cashItem = cashItem;
      }

      public void setTranStatus(String tranStatus) {
         this.tranStatus = tranStatus;
      }

      public void setProfitCenter(String profitCenter) {
         this.profitCenter = profitCenter;
      }

      public void setBusinessUnit(String businessUnit) {
         this.businessUnit = businessUnit;
      }

      public void setSourceModule(String sourceModule) {
         this.sourceModule = sourceModule;
      }

      public void setReceiptNo(String receiptNo) {
         this.receiptNo = receiptNo;
      }

      public void setSendPriority(String sendPriority) {
         this.sendPriority = sendPriority;
      }

      public void setCrDrMaintInd(String crDrMaintInd) {
         this.crDrMaintInd = crDrMaintInd;
      }

      public void setPrintCnt(Integer printCnt) {
         this.printCnt = printCnt;
      }

      public void setBatchNo(String batchNo) {
         this.batchNo = batchNo;
      }

      public void setToId(String toId) {
         this.toId = toId;
      }

      public void setLender(String lender) {
         this.lender = lender;
      }

      public void setAccountingStatus(String accountingStatus) {
         this.accountingStatus = accountingStatus;
      }

      public void setReversal(String reversal) {
         this.reversal = reversal;
      }

      public void setPayUnit(String payUnit) {
         this.payUnit = payUnit;
      }

      public void setCommissionClientTel(String commissionClientTel) {
         this.commissionClientTel = commissionClientTel;
      }

      public void setUserId(String userId) {
         this.userId = userId;
      }

      public void setAuthUserId(String authUserId) {
         this.authUserId = authUserId;
      }

      public void setApprUserId(String apprUserId) {
         this.apprUserId = apprUserId;
      }

      public void setTranTimestamp(String tranTimestamp) {
         this.tranTimestamp = tranTimestamp;
      }

      public void setCommissionClientName(String commissionClientName) {
         this.commissionClientName = commissionClientName;
      }

      public void setServCharge(String servCharge) {
         this.servCharge = servCharge;
      }

      public void setCompany(String company) {
         this.company = company;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100122Out.TranHistArray)) {
            return false;
         } else {
            Core1400100122Out.TranHistArray other = (Core1400100122Out.TranHistArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label1103: {
                  Object this$seqNo = this.getSeqNo();
                  Object other$seqNo = other.getSeqNo();
                  if (this$seqNo == null) {
                     if (other$seqNo == null) {
                        break label1103;
                     }
                  } else if (this$seqNo.equals(other$seqNo)) {
                     break label1103;
                  }

                  return false;
               }

               Object this$primaryTranSeqNo = this.getPrimaryTranSeqNo();
               Object other$primaryTranSeqNo = other.getPrimaryTranSeqNo();
               if (this$primaryTranSeqNo == null) {
                  if (other$primaryTranSeqNo != null) {
                     return false;
                  }
               } else if (!this$primaryTranSeqNo.equals(other$primaryTranSeqNo)) {
                  return false;
               }

               Object this$fhSeqNo = this.getFhSeqNo();
               Object other$fhSeqNo = other.getFhSeqNo();
               if (this$fhSeqNo == null) {
                  if (other$fhSeqNo != null) {
                     return false;
                  }
               } else if (!this$fhSeqNo.equals(other$fhSeqNo)) {
                  return false;
               }

               label1082: {
                  Object this$channelSeqNo = this.getChannelSeqNo();
                  Object other$channelSeqNo = other.getChannelSeqNo();
                  if (this$channelSeqNo == null) {
                     if (other$channelSeqNo == null) {
                        break label1082;
                     }
                  } else if (this$channelSeqNo.equals(other$channelSeqNo)) {
                     break label1082;
                  }

                  return false;
               }

               label1075: {
                  Object this$documentId = this.getDocumentId();
                  Object other$documentId = other.getDocumentId();
                  if (this$documentId == null) {
                     if (other$documentId == null) {
                        break label1075;
                     }
                  } else if (this$documentId.equals(other$documentId)) {
                     break label1075;
                  }

                  return false;
               }

               Object this$documentType = this.getDocumentType();
               Object other$documentType = other.getDocumentType();
               if (this$documentType == null) {
                  if (other$documentType != null) {
                     return false;
                  }
               } else if (!this$documentType.equals(other$documentType)) {
                  return false;
               }

               Object this$clientNo = this.getClientNo();
               Object other$clientNo = other.getClientNo();
               if (this$clientNo == null) {
                  if (other$clientNo != null) {
                     return false;
                  }
               } else if (!this$clientNo.equals(other$clientNo)) {
                  return false;
               }

               label1054: {
                  Object this$chClientName = this.getChClientName();
                  Object other$chClientName = other.getChClientName();
                  if (this$chClientName == null) {
                     if (other$chClientName == null) {
                        break label1054;
                     }
                  } else if (this$chClientName.equals(other$chClientName)) {
                     break label1054;
                  }

                  return false;
               }

               label1047: {
                  Object this$clientType = this.getClientType();
                  Object other$clientType = other.getClientType();
                  if (this$clientType == null) {
                     if (other$clientType == null) {
                        break label1047;
                     }
                  } else if (this$clientType.equals(other$clientType)) {
                     break label1047;
                  }

                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               label1033: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label1033;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label1033;
                  }

                  return false;
               }

               Object this$acctCcy = this.getAcctCcy();
               Object other$acctCcy = other.getAcctCcy();
               if (this$acctCcy == null) {
                  if (other$acctCcy != null) {
                     return false;
                  }
               } else if (!this$acctCcy.equals(other$acctCcy)) {
                  return false;
               }

               label1019: {
                  Object this$acctSeqNo = this.getAcctSeqNo();
                  Object other$acctSeqNo = other.getAcctSeqNo();
                  if (this$acctSeqNo == null) {
                     if (other$acctSeqNo == null) {
                        break label1019;
                     }
                  } else if (this$acctSeqNo.equals(other$acctSeqNo)) {
                     break label1019;
                  }

                  return false;
               }

               Object this$acctStatus = this.getAcctStatus();
               Object other$acctStatus = other.getAcctStatus();
               if (this$acctStatus == null) {
                  if (other$acctStatus != null) {
                     return false;
                  }
               } else if (!this$acctStatus.equals(other$acctStatus)) {
                  return false;
               }

               Object this$internalKey = this.getInternalKey();
               Object other$internalKey = other.getInternalKey();
               if (this$internalKey == null) {
                  if (other$internalKey != null) {
                     return false;
                  }
               } else if (!this$internalKey.equals(other$internalKey)) {
                  return false;
               }

               label998: {
                  Object this$acctBranch = this.getAcctBranch();
                  Object other$acctBranch = other.getAcctBranch();
                  if (this$acctBranch == null) {
                     if (other$acctBranch == null) {
                        break label998;
                     }
                  } else if (this$acctBranch.equals(other$acctBranch)) {
                     break label998;
                  }

                  return false;
               }

               label991: {
                  Object this$acctTranFlag = this.getAcctTranFlag();
                  Object other$acctTranFlag = other.getAcctTranFlag();
                  if (this$acctTranFlag == null) {
                     if (other$acctTranFlag == null) {
                        break label991;
                     }
                  } else if (this$acctTranFlag.equals(other$acctTranFlag)) {
                     break label991;
                  }

                  return false;
               }

               Object this$actualBal = this.getActualBal();
               Object other$actualBal = other.getActualBal();
               if (this$actualBal == null) {
                  if (other$actualBal != null) {
                     return false;
                  }
               } else if (!this$actualBal.equals(other$actualBal)) {
                  return false;
               }

               Object this$acctDesc = this.getAcctDesc();
               Object other$acctDesc = other.getAcctDesc();
               if (this$acctDesc == null) {
                  if (other$acctDesc != null) {
                     return false;
                  }
               } else if (!this$acctDesc.equals(other$acctDesc)) {
                  return false;
               }

               label970: {
                  Object this$othInternalKey = this.getOthInternalKey();
                  Object other$othInternalKey = other.getOthInternalKey();
                  if (this$othInternalKey == null) {
                     if (other$othInternalKey == null) {
                        break label970;
                     }
                  } else if (this$othInternalKey.equals(other$othInternalKey)) {
                     break label970;
                  }

                  return false;
               }

               label963: {
                  Object this$othAcctDesc = this.getOthAcctDesc();
                  Object other$othAcctDesc = other.getOthAcctDesc();
                  if (this$othAcctDesc == null) {
                     if (other$othAcctDesc == null) {
                        break label963;
                     }
                  } else if (this$othAcctDesc.equals(other$othAcctDesc)) {
                     break label963;
                  }

                  return false;
               }

               Object this$reasonCode = this.getReasonCode();
               Object other$reasonCode = other.getReasonCode();
               if (this$reasonCode == null) {
                  if (other$reasonCode != null) {
                     return false;
                  }
               } else if (!this$reasonCode.equals(other$reasonCode)) {
                  return false;
               }

               Object this$effectDate = this.getEffectDate();
               Object other$effectDate = other.getEffectDate();
               if (this$effectDate == null) {
                  if (other$effectDate != null) {
                     return false;
                  }
               } else if (!this$effectDate.equals(other$effectDate)) {
                  return false;
               }

               label942: {
                  Object this$reversalDate = this.getReversalDate();
                  Object other$reversalDate = other.getReversalDate();
                  if (this$reversalDate == null) {
                     if (other$reversalDate == null) {
                        break label942;
                     }
                  } else if (this$reversalDate.equals(other$reversalDate)) {
                     break label942;
                  }

                  return false;
               }

               label935: {
                  Object this$tranDate = this.getTranDate();
                  Object other$tranDate = other.getTranDate();
                  if (this$tranDate == null) {
                     if (other$tranDate == null) {
                        break label935;
                     }
                  } else if (this$tranDate.equals(other$tranDate)) {
                     break label935;
                  }

                  return false;
               }

               Object this$previousBalAmt = this.getPreviousBalAmt();
               Object other$previousBalAmt = other.getPreviousBalAmt();
               if (this$previousBalAmt == null) {
                  if (other$previousBalAmt != null) {
                     return false;
                  }
               } else if (!this$previousBalAmt.equals(other$previousBalAmt)) {
                  return false;
               }

               label921: {
                  Object this$contraEquivAmt = this.getContraEquivAmt();
                  Object other$contraEquivAmt = other.getContraEquivAmt();
                  if (this$contraEquivAmt == null) {
                     if (other$contraEquivAmt == null) {
                        break label921;
                     }
                  } else if (this$contraEquivAmt.equals(other$contraEquivAmt)) {
                     break label921;
                  }

                  return false;
               }

               Object this$baseEquivAmt = this.getBaseEquivAmt();
               Object other$baseEquivAmt = other.getBaseEquivAmt();
               if (this$baseEquivAmt == null) {
                  if (other$baseEquivAmt != null) {
                     return false;
                  }
               } else if (!this$baseEquivAmt.equals(other$baseEquivAmt)) {
                  return false;
               }

               label907: {
                  Object this$tranAmt = this.getTranAmt();
                  Object other$tranAmt = other.getTranAmt();
                  if (this$tranAmt == null) {
                     if (other$tranAmt == null) {
                        break label907;
                     }
                  } else if (this$tranAmt.equals(other$tranAmt)) {
                     break label907;
                  }

                  return false;
               }

               Object this$fromAmount = this.getFromAmount();
               Object other$fromAmount = other.getFromAmount();
               if (this$fromAmount == null) {
                  if (other$fromAmount != null) {
                     return false;
                  }
               } else if (!this$fromAmount.equals(other$fromAmount)) {
                  return false;
               }

               Object this$toAmount = this.getToAmount();
               Object other$toAmount = other.getToAmount();
               if (this$toAmount == null) {
                  if (other$toAmount != null) {
                     return false;
                  }
               } else if (!this$toAmount.equals(other$toAmount)) {
                  return false;
               }

               label886: {
                  Object this$ccy = this.getCcy();
                  Object other$ccy = other.getCcy();
                  if (this$ccy == null) {
                     if (other$ccy == null) {
                        break label886;
                     }
                  } else if (this$ccy.equals(other$ccy)) {
                     break label886;
                  }

                  return false;
               }

               label879: {
                  Object this$crossRate = this.getCrossRate();
                  Object other$crossRate = other.getCrossRate();
                  if (this$crossRate == null) {
                     if (other$crossRate == null) {
                        break label879;
                     }
                  } else if (this$crossRate.equals(other$crossRate)) {
                     break label879;
                  }

                  return false;
               }

               Object this$fromRateFlag = this.getFromRateFlag();
               Object other$fromRateFlag = other.getFromRateFlag();
               if (this$fromRateFlag == null) {
                  if (other$fromRateFlag != null) {
                     return false;
                  }
               } else if (!this$fromRateFlag.equals(other$fromRateFlag)) {
                  return false;
               }

               Object this$fromXrate = this.getFromXrate();
               Object other$fromXrate = other.getFromXrate();
               if (this$fromXrate == null) {
                  if (other$fromXrate != null) {
                     return false;
                  }
               } else if (!this$fromXrate.equals(other$fromXrate)) {
                  return false;
               }

               label858: {
                  Object this$toRateFlag = this.getToRateFlag();
                  Object other$toRateFlag = other.getToRateFlag();
                  if (this$toRateFlag == null) {
                     if (other$toRateFlag == null) {
                        break label858;
                     }
                  } else if (this$toRateFlag.equals(other$toRateFlag)) {
                     break label858;
                  }

                  return false;
               }

               label851: {
                  Object this$toXrate = this.getToXrate();
                  Object other$toXrate = other.getToXrate();
                  if (this$toXrate == null) {
                     if (other$toXrate == null) {
                        break label851;
                     }
                  } else if (this$toXrate.equals(other$toXrate)) {
                     break label851;
                  }

                  return false;
               }

               Object this$ovCrossRate = this.getOvCrossRate();
               Object other$ovCrossRate = other.getOvCrossRate();
               if (this$ovCrossRate == null) {
                  if (other$ovCrossRate != null) {
                     return false;
                  }
               } else if (!this$ovCrossRate.equals(other$ovCrossRate)) {
                  return false;
               }

               Object this$othDocumentType = this.getOthDocumentType();
               Object other$othDocumentType = other.getOthDocumentType();
               if (this$othDocumentType == null) {
                  if (other$othDocumentType != null) {
                     return false;
                  }
               } else if (!this$othDocumentType.equals(other$othDocumentType)) {
                  return false;
               }

               label830: {
                  Object this$withdrawalType = this.getWithdrawalType();
                  Object other$withdrawalType = other.getWithdrawalType();
                  if (this$withdrawalType == null) {
                     if (other$withdrawalType == null) {
                        break label830;
                     }
                  } else if (this$withdrawalType.equals(other$withdrawalType)) {
                     break label830;
                  }

                  return false;
               }

               label823: {
                  Object this$eventType = this.getEventType();
                  Object other$eventType = other.getEventType();
                  if (this$eventType == null) {
                     if (other$eventType == null) {
                        break label823;
                     }
                  } else if (this$eventType.equals(other$eventType)) {
                     break label823;
                  }

                  return false;
               }

               Object this$rateType = this.getRateType();
               Object other$rateType = other.getRateType();
               if (this$rateType == null) {
                  if (other$rateType != null) {
                     return false;
                  }
               } else if (!this$rateType.equals(other$rateType)) {
                  return false;
               }

               label809: {
                  Object this$balType = this.getBalType();
                  Object other$balType = other.getBalType();
                  if (this$balType == null) {
                     if (other$balType == null) {
                        break label809;
                     }
                  } else if (this$balType.equals(other$balType)) {
                     break label809;
                  }

                  return false;
               }

               Object this$bizType = this.getBizType();
               Object other$bizType = other.getBizType();
               if (this$bizType == null) {
                  if (other$bizType != null) {
                     return false;
                  }
               } else if (!this$bizType.equals(other$bizType)) {
                  return false;
               }

               label795: {
                  Object this$rateFlag = this.getRateFlag();
                  Object other$rateFlag = other.getRateFlag();
                  if (this$rateFlag == null) {
                     if (other$rateFlag == null) {
                        break label795;
                     }
                  } else if (this$rateFlag.equals(other$rateFlag)) {
                     break label795;
                  }

                  return false;
               }

               Object this$quoteType = this.getQuoteType();
               Object other$quoteType = other.getQuoteType();
               if (this$quoteType == null) {
                  if (other$quoteType != null) {
                     return false;
                  }
               } else if (!this$quoteType.equals(other$quoteType)) {
                  return false;
               }

               Object this$pbkUpdFlag = this.getPbkUpdFlag();
               Object other$pbkUpdFlag = other.getPbkUpdFlag();
               if (this$pbkUpdFlag == null) {
                  if (other$pbkUpdFlag != null) {
                     return false;
                  }
               } else if (!this$pbkUpdFlag.equals(other$pbkUpdFlag)) {
                  return false;
               }

               label774: {
                  Object this$amtType = this.getAmtType();
                  Object other$amtType = other.getAmtType();
                  if (this$amtType == null) {
                     if (other$amtType == null) {
                        break label774;
                     }
                  } else if (this$amtType.equals(other$amtType)) {
                     break label774;
                  }

                  return false;
               }

               label767: {
                  Object this$sourceType = this.getSourceType();
                  Object other$sourceType = other.getSourceType();
                  if (this$sourceType == null) {
                     if (other$sourceType == null) {
                        break label767;
                     }
                  } else if (this$sourceType.equals(other$sourceType)) {
                     break label767;
                  }

                  return false;
               }

               Object this$tranCategory = this.getTranCategory();
               Object other$tranCategory = other.getTranCategory();
               if (this$tranCategory == null) {
                  if (other$tranCategory != null) {
                     return false;
                  }
               } else if (!this$tranCategory.equals(other$tranCategory)) {
                  return false;
               }

               Object this$tranDesc = this.getTranDesc();
               Object other$tranDesc = other.getTranDesc();
               if (this$tranDesc == null) {
                  if (other$tranDesc != null) {
                     return false;
                  }
               } else if (!this$tranDesc.equals(other$tranDesc)) {
                  return false;
               }

               label746: {
                  Object this$tranNote = this.getTranNote();
                  Object other$tranNote = other.getTranNote();
                  if (this$tranNote == null) {
                     if (other$tranNote == null) {
                        break label746;
                     }
                  } else if (this$tranNote.equals(other$tranNote)) {
                     break label746;
                  }

                  return false;
               }

               label739: {
                  Object this$othSeqNo = this.getOthSeqNo();
                  Object other$othSeqNo = other.getOthSeqNo();
                  if (this$othSeqNo == null) {
                     if (other$othSeqNo == null) {
                        break label739;
                     }
                  } else if (this$othSeqNo.equals(other$othSeqNo)) {
                     break label739;
                  }

                  return false;
               }

               Object this$othProdType = this.getOthProdType();
               Object other$othProdType = other.getOthProdType();
               if (this$othProdType == null) {
                  if (other$othProdType != null) {
                     return false;
                  }
               } else if (!this$othProdType.equals(other$othProdType)) {
                  return false;
               }

               Object this$othBaseAcctNo = this.getOthBaseAcctNo();
               Object other$othBaseAcctNo = other.getOthBaseAcctNo();
               if (this$othBaseAcctNo == null) {
                  if (other$othBaseAcctNo != null) {
                     return false;
                  }
               } else if (!this$othBaseAcctNo.equals(other$othBaseAcctNo)) {
                  return false;
               }

               label718: {
                  Object this$fromCcy = this.getFromCcy();
                  Object other$fromCcy = other.getFromCcy();
                  if (this$fromCcy == null) {
                     if (other$fromCcy == null) {
                        break label718;
                     }
                  } else if (this$fromCcy.equals(other$fromCcy)) {
                     break label718;
                  }

                  return false;
               }

               label711: {
                  Object this$toCcy = this.getToCcy();
                  Object other$toCcy = other.getToCcy();
                  if (this$toCcy == null) {
                     if (other$toCcy == null) {
                        break label711;
                     }
                  } else if (this$toCcy.equals(other$toCcy)) {
                     break label711;
                  }

                  return false;
               }

               Object this$othAcctCcy = this.getOthAcctCcy();
               Object other$othAcctCcy = other.getOthAcctCcy();
               if (this$othAcctCcy == null) {
                  if (other$othAcctCcy != null) {
                     return false;
                  }
               } else if (!this$othAcctCcy.equals(other$othAcctCcy)) {
                  return false;
               }

               label697: {
                  Object this$contraAcctCcy = this.getContraAcctCcy();
                  Object other$contraAcctCcy = other.getContraAcctCcy();
                  if (this$contraAcctCcy == null) {
                     if (other$contraAcctCcy == null) {
                        break label697;
                     }
                  } else if (this$contraAcctCcy.equals(other$contraAcctCcy)) {
                     break label697;
                  }

                  return false;
               }

               Object this$othAcctSeqNo = this.getOthAcctSeqNo();
               Object other$othAcctSeqNo = other.getOthAcctSeqNo();
               if (this$othAcctSeqNo == null) {
                  if (other$othAcctSeqNo != null) {
                     return false;
                  }
               } else if (!this$othAcctSeqNo.equals(other$othAcctSeqNo)) {
                  return false;
               }

               label683: {
                  Object this$othDocumentId = this.getOthDocumentId();
                  Object other$othDocumentId = other.getOthDocumentId();
                  if (this$othDocumentId == null) {
                     if (other$othDocumentId == null) {
                        break label683;
                     }
                  } else if (this$othDocumentId.equals(other$othDocumentId)) {
                     break label683;
                  }

                  return false;
               }

               Object this$bankSeqNo = this.getBankSeqNo();
               Object other$bankSeqNo = other.getBankSeqNo();
               if (this$bankSeqNo == null) {
                  if (other$bankSeqNo != null) {
                     return false;
                  }
               } else if (!this$bankSeqNo.equals(other$bankSeqNo)) {
                  return false;
               }

               Object this$terminalId = this.getTerminalId();
               Object other$terminalId = other.getTerminalId();
               if (this$terminalId == null) {
                  if (other$terminalId != null) {
                     return false;
                  }
               } else if (!this$terminalId.equals(other$terminalId)) {
                  return false;
               }

               label662: {
                  Object this$areaCode = this.getAreaCode();
                  Object other$areaCode = other.getAreaCode();
                  if (this$areaCode == null) {
                     if (other$areaCode == null) {
                        break label662;
                     }
                  } else if (this$areaCode.equals(other$areaCode)) {
                     break label662;
                  }

                  return false;
               }

               label655: {
                  Object this$printPageNo = this.getPrintPageNo();
                  Object other$printPageNo = other.getPrintPageNo();
                  if (this$printPageNo == null) {
                     if (other$printPageNo == null) {
                        break label655;
                     }
                  } else if (this$printPageNo.equals(other$printPageNo)) {
                     break label655;
                  }

                  return false;
               }

               Object this$printLineNo = this.getPrintLineNo();
               Object other$printLineNo = other.getPrintLineNo();
               if (this$printLineNo == null) {
                  if (other$printLineNo != null) {
                     return false;
                  }
               } else if (!this$printLineNo.equals(other$printLineNo)) {
                  return false;
               }

               Object this$traceId = this.getTraceId();
               Object other$traceId = other.getTraceId();
               if (this$traceId == null) {
                  if (other$traceId != null) {
                     return false;
                  }
               } else if (!this$traceId.equals(other$traceId)) {
                  return false;
               }

               label634: {
                  Object this$narrative = this.getNarrative();
                  Object other$narrative = other.getNarrative();
                  if (this$narrative == null) {
                     if (other$narrative == null) {
                        break label634;
                     }
                  } else if (this$narrative.equals(other$narrative)) {
                     break label634;
                  }

                  return false;
               }

               label627: {
                  Object this$cashItem = this.getCashItem();
                  Object other$cashItem = other.getCashItem();
                  if (this$cashItem == null) {
                     if (other$cashItem == null) {
                        break label627;
                     }
                  } else if (this$cashItem.equals(other$cashItem)) {
                     break label627;
                  }

                  return false;
               }

               Object this$tranStatus = this.getTranStatus();
               Object other$tranStatus = other.getTranStatus();
               if (this$tranStatus == null) {
                  if (other$tranStatus != null) {
                     return false;
                  }
               } else if (!this$tranStatus.equals(other$tranStatus)) {
                  return false;
               }

               Object this$profitCenter = this.getProfitCenter();
               Object other$profitCenter = other.getProfitCenter();
               if (this$profitCenter == null) {
                  if (other$profitCenter != null) {
                     return false;
                  }
               } else if (!this$profitCenter.equals(other$profitCenter)) {
                  return false;
               }

               label606: {
                  Object this$businessUnit = this.getBusinessUnit();
                  Object other$businessUnit = other.getBusinessUnit();
                  if (this$businessUnit == null) {
                     if (other$businessUnit == null) {
                        break label606;
                     }
                  } else if (this$businessUnit.equals(other$businessUnit)) {
                     break label606;
                  }

                  return false;
               }

               label599: {
                  Object this$sourceModule = this.getSourceModule();
                  Object other$sourceModule = other.getSourceModule();
                  if (this$sourceModule == null) {
                     if (other$sourceModule == null) {
                        break label599;
                     }
                  } else if (this$sourceModule.equals(other$sourceModule)) {
                     break label599;
                  }

                  return false;
               }

               Object this$receiptNo = this.getReceiptNo();
               Object other$receiptNo = other.getReceiptNo();
               if (this$receiptNo == null) {
                  if (other$receiptNo != null) {
                     return false;
                  }
               } else if (!this$receiptNo.equals(other$receiptNo)) {
                  return false;
               }

               label585: {
                  Object this$sendPriority = this.getSendPriority();
                  Object other$sendPriority = other.getSendPriority();
                  if (this$sendPriority == null) {
                     if (other$sendPriority == null) {
                        break label585;
                     }
                  } else if (this$sendPriority.equals(other$sendPriority)) {
                     break label585;
                  }

                  return false;
               }

               Object this$crDrMaintInd = this.getCrDrMaintInd();
               Object other$crDrMaintInd = other.getCrDrMaintInd();
               if (this$crDrMaintInd == null) {
                  if (other$crDrMaintInd != null) {
                     return false;
                  }
               } else if (!this$crDrMaintInd.equals(other$crDrMaintInd)) {
                  return false;
               }

               label571: {
                  Object this$printCnt = this.getPrintCnt();
                  Object other$printCnt = other.getPrintCnt();
                  if (this$printCnt == null) {
                     if (other$printCnt == null) {
                        break label571;
                     }
                  } else if (this$printCnt.equals(other$printCnt)) {
                     break label571;
                  }

                  return false;
               }

               Object this$batchNo = this.getBatchNo();
               Object other$batchNo = other.getBatchNo();
               if (this$batchNo == null) {
                  if (other$batchNo != null) {
                     return false;
                  }
               } else if (!this$batchNo.equals(other$batchNo)) {
                  return false;
               }

               Object this$toId = this.getToId();
               Object other$toId = other.getToId();
               if (this$toId == null) {
                  if (other$toId != null) {
                     return false;
                  }
               } else if (!this$toId.equals(other$toId)) {
                  return false;
               }

               label550: {
                  Object this$lender = this.getLender();
                  Object other$lender = other.getLender();
                  if (this$lender == null) {
                     if (other$lender == null) {
                        break label550;
                     }
                  } else if (this$lender.equals(other$lender)) {
                     break label550;
                  }

                  return false;
               }

               label543: {
                  Object this$accountingStatus = this.getAccountingStatus();
                  Object other$accountingStatus = other.getAccountingStatus();
                  if (this$accountingStatus == null) {
                     if (other$accountingStatus == null) {
                        break label543;
                     }
                  } else if (this$accountingStatus.equals(other$accountingStatus)) {
                     break label543;
                  }

                  return false;
               }

               Object this$reversal = this.getReversal();
               Object other$reversal = other.getReversal();
               if (this$reversal == null) {
                  if (other$reversal != null) {
                     return false;
                  }
               } else if (!this$reversal.equals(other$reversal)) {
                  return false;
               }

               Object this$payUnit = this.getPayUnit();
               Object other$payUnit = other.getPayUnit();
               if (this$payUnit == null) {
                  if (other$payUnit != null) {
                     return false;
                  }
               } else if (!this$payUnit.equals(other$payUnit)) {
                  return false;
               }

               label522: {
                  Object this$commissionClientTel = this.getCommissionClientTel();
                  Object other$commissionClientTel = other.getCommissionClientTel();
                  if (this$commissionClientTel == null) {
                     if (other$commissionClientTel == null) {
                        break label522;
                     }
                  } else if (this$commissionClientTel.equals(other$commissionClientTel)) {
                     break label522;
                  }

                  return false;
               }

               label515: {
                  Object this$userId = this.getUserId();
                  Object other$userId = other.getUserId();
                  if (this$userId == null) {
                     if (other$userId == null) {
                        break label515;
                     }
                  } else if (this$userId.equals(other$userId)) {
                     break label515;
                  }

                  return false;
               }

               Object this$authUserId = this.getAuthUserId();
               Object other$authUserId = other.getAuthUserId();
               if (this$authUserId == null) {
                  if (other$authUserId != null) {
                     return false;
                  }
               } else if (!this$authUserId.equals(other$authUserId)) {
                  return false;
               }

               Object this$apprUserId = this.getApprUserId();
               Object other$apprUserId = other.getApprUserId();
               if (this$apprUserId == null) {
                  if (other$apprUserId != null) {
                     return false;
                  }
               } else if (!this$apprUserId.equals(other$apprUserId)) {
                  return false;
               }

               label494: {
                  Object this$tranTimestamp = this.getTranTimestamp();
                  Object other$tranTimestamp = other.getTranTimestamp();
                  if (this$tranTimestamp == null) {
                     if (other$tranTimestamp == null) {
                        break label494;
                     }
                  } else if (this$tranTimestamp.equals(other$tranTimestamp)) {
                     break label494;
                  }

                  return false;
               }

               label487: {
                  Object this$commissionClientName = this.getCommissionClientName();
                  Object other$commissionClientName = other.getCommissionClientName();
                  if (this$commissionClientName == null) {
                     if (other$commissionClientName == null) {
                        break label487;
                     }
                  } else if (this$commissionClientName.equals(other$commissionClientName)) {
                     break label487;
                  }

                  return false;
               }

               Object this$servCharge = this.getServCharge();
               Object other$servCharge = other.getServCharge();
               if (this$servCharge == null) {
                  if (other$servCharge != null) {
                     return false;
                  }
               } else if (!this$servCharge.equals(other$servCharge)) {
                  return false;
               }

               Object this$company = this.getCompany();
               Object other$company = other.getCompany();
               if (this$company == null) {
                  if (other$company != null) {
                     return false;
                  }
               } else if (!this$company.equals(other$company)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100122Out.TranHistArray;
      }
      public String toString() {
         return "Core1400100122Out.TranHistArray(seqNo=" + this.getSeqNo() + ", primaryTranSeqNo=" + this.getPrimaryTranSeqNo() + ", fhSeqNo=" + this.getFhSeqNo() + ", channelSeqNo=" + this.getChannelSeqNo() + ", documentId=" + this.getDocumentId() + ", documentType=" + this.getDocumentType() + ", clientNo=" + this.getClientNo() + ", chClientName=" + this.getChClientName() + ", clientType=" + this.getClientType() + ", prodType=" + this.getProdType() + ", baseAcctNo=" + this.getBaseAcctNo() + ", acctCcy=" + this.getAcctCcy() + ", acctSeqNo=" + this.getAcctSeqNo() + ", acctStatus=" + this.getAcctStatus() + ", internalKey=" + this.getInternalKey() + ", acctBranch=" + this.getAcctBranch() + ", acctTranFlag=" + this.getAcctTranFlag() + ", actualBal=" + this.getActualBal() + ", acctDesc=" + this.getAcctDesc() + ", othInternalKey=" + this.getOthInternalKey() + ", othAcctDesc=" + this.getOthAcctDesc() + ", reasonCode=" + this.getReasonCode() + ", effectDate=" + this.getEffectDate() + ", reversalDate=" + this.getReversalDate() + ", tranDate=" + this.getTranDate() + ", previousBalAmt=" + this.getPreviousBalAmt() + ", contraEquivAmt=" + this.getContraEquivAmt() + ", baseEquivAmt=" + this.getBaseEquivAmt() + ", tranAmt=" + this.getTranAmt() + ", fromAmount=" + this.getFromAmount() + ", toAmount=" + this.getToAmount() + ", ccy=" + this.getCcy() + ", crossRate=" + this.getCrossRate() + ", fromRateFlag=" + this.getFromRateFlag() + ", fromXrate=" + this.getFromXrate() + ", toRateFlag=" + this.getToRateFlag() + ", toXrate=" + this.getToXrate() + ", ovCrossRate=" + this.getOvCrossRate() + ", othDocumentType=" + this.getOthDocumentType() + ", withdrawalType=" + this.getWithdrawalType() + ", eventType=" + this.getEventType() + ", rateType=" + this.getRateType() + ", balType=" + this.getBalType() + ", bizType=" + this.getBizType() + ", rateFlag=" + this.getRateFlag() + ", quoteType=" + this.getQuoteType() + ", pbkUpdFlag=" + this.getPbkUpdFlag() + ", amtType=" + this.getAmtType() + ", sourceType=" + this.getSourceType() + ", tranCategory=" + this.getTranCategory() + ", tranDesc=" + this.getTranDesc() + ", tranNote=" + this.getTranNote() + ", othSeqNo=" + this.getOthSeqNo() + ", othProdType=" + this.getOthProdType() + ", othBaseAcctNo=" + this.getOthBaseAcctNo() + ", fromCcy=" + this.getFromCcy() + ", toCcy=" + this.getToCcy() + ", othAcctCcy=" + this.getOthAcctCcy() + ", contraAcctCcy=" + this.getContraAcctCcy() + ", othAcctSeqNo=" + this.getOthAcctSeqNo() + ", othDocumentId=" + this.getOthDocumentId() + ", bankSeqNo=" + this.getBankSeqNo() + ", terminalId=" + this.getTerminalId() + ", areaCode=" + this.getAreaCode() + ", printPageNo=" + this.getPrintPageNo() + ", printLineNo=" + this.getPrintLineNo() + ", traceId=" + this.getTraceId() + ", narrative=" + this.getNarrative() + ", cashItem=" + this.getCashItem() + ", tranStatus=" + this.getTranStatus() + ", profitCenter=" + this.getProfitCenter() + ", businessUnit=" + this.getBusinessUnit() + ", sourceModule=" + this.getSourceModule() + ", receiptNo=" + this.getReceiptNo() + ", sendPriority=" + this.getSendPriority() + ", crDrMaintInd=" + this.getCrDrMaintInd() + ", printCnt=" + this.getPrintCnt() + ", batchNo=" + this.getBatchNo() + ", toId=" + this.getToId() + ", lender=" + this.getLender() + ", accountingStatus=" + this.getAccountingStatus() + ", reversal=" + this.getReversal() + ", payUnit=" + this.getPayUnit() + ", commissionClientTel=" + this.getCommissionClientTel() + ", userId=" + this.getUserId() + ", authUserId=" + this.getAuthUserId() + ", apprUserId=" + this.getApprUserId() + ", tranTimestamp=" + this.getTranTimestamp() + ", commissionClientName=" + this.getCommissionClientName() + ", servCharge=" + this.getServCharge() + ", company=" + this.getCompany() + ")";
      }
   }
}
