package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14002612In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14002612Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14002612 {
   String URL = "/rb/nfin/northbound/inqury";


   @ApiRemark("北向通签约查询")
   @ApiDesc("北向通签约查询")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "2612"
   )
   @BusinessCategory("存款")
   @FunctionCategory("RB05-协议管理")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core14002612Out runService(Core14002612In var1);
}
