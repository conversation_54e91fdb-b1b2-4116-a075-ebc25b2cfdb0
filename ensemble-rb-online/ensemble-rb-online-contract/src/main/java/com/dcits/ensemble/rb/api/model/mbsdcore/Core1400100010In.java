package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;

@MessageIn
public class Core1400100010In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400100010In.Body body;

   public Core1400100010In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400100010In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400100010In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100010In)) {
         return false;
      } else {
         Core1400100010In other = (Core1400100010In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100010In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "流水类型",
         notNull = false,
         length = "1",
         inDesc = "0-全账户流水(包含所有子账户的流水) ,1-账户交易流水,2-客户回单类流水 （单账户）",
         remark = "流水范围类型",
         maxSize = 1
      )
      private String histKind;
      @V(
         desc = "查询所有信息（是否包含已销户状态）",
         notNull = false,
         length = "2",
         inDesc = "0-不包含已销户状态账户的交易信息,1-包含已销户状态账户的交易信息",
         remark = "查询所有信息（是否包含已销户状态）",
         maxSize = 2
      )
      private String statusFlag;
      @V(
         desc = "查询种类",
         notNull = false,
         length = "1",
         inDesc = "0-非客户,1-客户",
         remark = "查询种类",
         maxSize = 1
      )
      private String inquiryKind;
      @V(
         desc = "交易方式",
         notNull = false,
         length = "1",
         inDesc = "D-支取,C-存入,U-资金池,S-资金池下拨,A-全部",
         remark = "交易方式",
         maxSize = 1
      )
      private String tranKind;
      @V(
         desc = "查询明细状态",
         notNull = false,
         length = "1",
         inDesc = "C-活期,T-定期,A-活动",
         remark = "查询明细状态",
         maxSize = 1
      )
      private String queryStatus;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "渠道流水号",
         notNull = false,
         length = "50",
         remark = "渠道流水号",
         maxSize = 50
      )
      private String channelSeqNo;
      @V(
         desc = "开始日期",
         notNull = false,
         remark = "开始日期"
      )
      private String startDate;
      @V(
         desc = "结束日期",
         notNull = false,
         remark = "结束日期"
      )
      private String endDate;
      @V(
         desc = "交易类型",
         notNull = false,
         length = "10",
         remark = "交易类型",
         maxSize = 10
      )
      private String tranType;
      @V(
         desc = "是否冲正标志",
         notNull = false,
         length = "1",
         inDesc = "Y-是,N-否",
         remark = "是否冲正标志",
         maxSize = 1
      )
      private String reversal;
      @V(
         desc = "查询日期",
         notNull = false,
         remark = "查询日期"
      )
      private String queryDate;
      @V(
         desc = "是否补登存",
         notNull = false,
         length = "1",
         inDesc = "Y-是 ,N-否",
         remark = "是否补登存",
         maxSize = 1
      )
      private String pbkUpdFlag;
      @V(
         desc = "转账类型",
         notNull = false,
         length = "50",
         inDesc = "1-借,2-贷,*-总账特殊使用,REDEEM-赎回(贷款使用),PACSAL-封包发行(贷款使用),UNPACSAL-封包发行撤销(贷款使用)",
         remark = "转账类型",
         maxSize = 50
      )
      private String transferType;
      @V(
         desc = "交易参考号",
         notNull = false,
         length = "50",
         remark = "交易参考号",
         maxSize = 50
      )
      private String reference;
      @V(
         desc = "渠道类型",
         notNull = false,
         length = "10",
         remark = "渠道类型",
         maxSize = 10
      )
      private String sourceType;
      @V(
         desc = "存期期限",
         notNull = false,
         length = "5",
         remark = "期限",
         maxSize = 5
      )
      private String term;
      @V(
         desc = "期限类型",
         notNull = false,
         length = "1",
         inDesc = "Y-年,Q-季,M-月,W-周,D-日",
         remark = "期限类型",
         maxSize = 1
      )
      private String termType;
      @V(
         desc = "真实交易对手账号",
         notNull = false,
         length = "50",
         remark = "真实交易对手账号",
         maxSize = 50
      )
      private String othRealBaseAcctNo;
      @V(
         desc = "真实交易对手名称",
         notNull = false,
         length = "200",
         remark = "真实交易对手名称",
         maxSize = 200
      )
      private String othRealTranName;
      @V(
         desc = "对方账号/卡号",
         notNull = false,
         length = "50",
         remark = "对方账号/卡号",
         maxSize = 50
      )
      private String othBaseAcctNo;
      @V(
         desc = "对方账户名称",
         notNull = false,
         length = "200",
         remark = "对方账户名称",
         maxSize = 200
      )
      private String othAcctName;
      @V(
         desc = "是否打印",
         notNull = false,
         length = "1",
         remark = "是否打印",
         maxSize = 1
      )
      private String isPrint;
      @V(
         desc = "法人",
         notNull = false,
         length = "20",
         remark = "法人",
         maxSize = 20
      )
      private String company;
      @V(
         desc = "摘要分类",
         notNull = false,
         length = "2",
         remark = "摘要分类",
         maxSize = 2
      )
      private String narrativeClass;
      @V(
         desc = "余额类型",
         notNull = false,
         length = "2",
         inDesc = "TT-汇余额,CA-钞余额",
         remark = "余额类型",
         maxSize = 2
      )
      private String balType;
      @V(
         desc = "子流水号",
         notNull = false,
         length = "100",
         remark = "子流水号",
         maxSize = 100
      )
      private String subSeqNo;
      @V(
         desc = "源模块",
         notNull = false,
         length = "3",
         inDesc = "RB-存款,CL-贷款,GL-总账,IA-内部户,ALL-所有",
         remark = "源模块",
         maxSize = 3
      )
      private String sourceModule;
      @V(
         desc = "交易机构",
         notNull = false,
         length = "50",
         remark = "交易机构",
         maxSize = 50
      )
      private String tranBranch;
      @V(
         desc = "交易柜员",
         notNull = false,
         length = "30",
         remark = "交易柜员",
         maxSize = 30
      )
      private String userId;
      @V(
         desc = "交易发生额下限",
         notNull = false,
         length = "17",
         remark = "交易发生额下限",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal lowestTranAmt;
      @V(
         desc = "交易发生额上限",
         notNull = false,
         length = "17",
         remark = "交易发生额上限",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal tranHighestAmt;
      @V(
         desc = "交易日期排序方式",
         notNull = false,
         length = "3",
         remark = "交易日期排序方式",
         maxSize = 3
      )
      private String tranDateOrder;
      @V(
         desc = "过滤账号",
         notNull = false,
         length = "50",
         remark = "过滤账号",
         maxSize = 50
      )
      private String filterBaseAcctNo;
      @V(
         desc = "交易历史查询类型",
         notNull = false,
         length = "1",
         remark = "0-内部查询,1-客户查询，当客户查询时，查不到抹账信息",
         maxSize = 1
      )
      private String tranHistQueryType;

      public String getHistKind() {
         return this.histKind;
      }

      public String getStatusFlag() {
         return this.statusFlag;
      }

      public String getInquiryKind() {
         return this.inquiryKind;
      }

      public String getTranKind() {
         return this.tranKind;
      }

      public String getQueryStatus() {
         return this.queryStatus;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getChannelSeqNo() {
         return this.channelSeqNo;
      }

      public String getStartDate() {
         return this.startDate;
      }

      public String getEndDate() {
         return this.endDate;
      }

      public String getTranType() {
         return this.tranType;
      }

      public String getReversal() {
         return this.reversal;
      }

      public String getQueryDate() {
         return this.queryDate;
      }

      public String getPbkUpdFlag() {
         return this.pbkUpdFlag;
      }

      public String getTransferType() {
         return this.transferType;
      }

      public String getReference() {
         return this.reference;
      }

      public String getSourceType() {
         return this.sourceType;
      }

      public String getTerm() {
         return this.term;
      }

      public String getTermType() {
         return this.termType;
      }

      public String getOthRealBaseAcctNo() {
         return this.othRealBaseAcctNo;
      }

      public String getOthRealTranName() {
         return this.othRealTranName;
      }

      public String getOthBaseAcctNo() {
         return this.othBaseAcctNo;
      }

      public String getOthAcctName() {
         return this.othAcctName;
      }

      public String getIsPrint() {
         return this.isPrint;
      }

      public String getCompany() {
         return this.company;
      }

      public String getNarrativeClass() {
         return this.narrativeClass;
      }

      public String getBalType() {
         return this.balType;
      }

      public String getSubSeqNo() {
         return this.subSeqNo;
      }

      public String getSourceModule() {
         return this.sourceModule;
      }

      public String getTranBranch() {
         return this.tranBranch;
      }

      public String getUserId() {
         return this.userId;
      }

      public BigDecimal getLowestTranAmt() {
         return this.lowestTranAmt;
      }

      public BigDecimal getTranHighestAmt() {
         return this.tranHighestAmt;
      }

      public String getTranDateOrder() {
         return this.tranDateOrder;
      }

      public String getFilterBaseAcctNo() {
         return this.filterBaseAcctNo;
      }

      public String getTranHistQueryType() {
         return this.tranHistQueryType;
      }

      public void setHistKind(String histKind) {
         this.histKind = histKind;
      }

      public void setStatusFlag(String statusFlag) {
         this.statusFlag = statusFlag;
      }

      public void setInquiryKind(String inquiryKind) {
         this.inquiryKind = inquiryKind;
      }

      public void setTranKind(String tranKind) {
         this.tranKind = tranKind;
      }

      public void setQueryStatus(String queryStatus) {
         this.queryStatus = queryStatus;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setChannelSeqNo(String channelSeqNo) {
         this.channelSeqNo = channelSeqNo;
      }

      public void setStartDate(String startDate) {
         this.startDate = startDate;
      }

      public void setEndDate(String endDate) {
         this.endDate = endDate;
      }

      public void setTranType(String tranType) {
         this.tranType = tranType;
      }

      public void setReversal(String reversal) {
         this.reversal = reversal;
      }

      public void setQueryDate(String queryDate) {
         this.queryDate = queryDate;
      }

      public void setPbkUpdFlag(String pbkUpdFlag) {
         this.pbkUpdFlag = pbkUpdFlag;
      }

      public void setTransferType(String transferType) {
         this.transferType = transferType;
      }

      public void setReference(String reference) {
         this.reference = reference;
      }

      public void setSourceType(String sourceType) {
         this.sourceType = sourceType;
      }

      public void setTerm(String term) {
         this.term = term;
      }

      public void setTermType(String termType) {
         this.termType = termType;
      }

      public void setOthRealBaseAcctNo(String othRealBaseAcctNo) {
         this.othRealBaseAcctNo = othRealBaseAcctNo;
      }

      public void setOthRealTranName(String othRealTranName) {
         this.othRealTranName = othRealTranName;
      }

      public void setOthBaseAcctNo(String othBaseAcctNo) {
         this.othBaseAcctNo = othBaseAcctNo;
      }

      public void setOthAcctName(String othAcctName) {
         this.othAcctName = othAcctName;
      }

      public void setIsPrint(String isPrint) {
         this.isPrint = isPrint;
      }

      public void setCompany(String company) {
         this.company = company;
      }

      public void setNarrativeClass(String narrativeClass) {
         this.narrativeClass = narrativeClass;
      }

      public void setBalType(String balType) {
         this.balType = balType;
      }

      public void setSubSeqNo(String subSeqNo) {
         this.subSeqNo = subSeqNo;
      }

      public void setSourceModule(String sourceModule) {
         this.sourceModule = sourceModule;
      }

      public void setTranBranch(String tranBranch) {
         this.tranBranch = tranBranch;
      }

      public void setUserId(String userId) {
         this.userId = userId;
      }

      public void setLowestTranAmt(BigDecimal lowestTranAmt) {
         this.lowestTranAmt = lowestTranAmt;
      }

      public void setTranHighestAmt(BigDecimal tranHighestAmt) {
         this.tranHighestAmt = tranHighestAmt;
      }

      public void setTranDateOrder(String tranDateOrder) {
         this.tranDateOrder = tranDateOrder;
      }

      public void setFilterBaseAcctNo(String filterBaseAcctNo) {
         this.filterBaseAcctNo = filterBaseAcctNo;
      }

      public void setTranHistQueryType(String tranHistQueryType) {
         this.tranHistQueryType = tranHistQueryType;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100010In.Body)) {
            return false;
         } else {
            Core1400100010In.Body other = (Core1400100010In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label479: {
                  Object this$histKind = this.getHistKind();
                  Object other$histKind = other.getHistKind();
                  if (this$histKind == null) {
                     if (other$histKind == null) {
                        break label479;
                     }
                  } else if (this$histKind.equals(other$histKind)) {
                     break label479;
                  }

                  return false;
               }

               Object this$statusFlag = this.getStatusFlag();
               Object other$statusFlag = other.getStatusFlag();
               if (this$statusFlag == null) {
                  if (other$statusFlag != null) {
                     return false;
                  }
               } else if (!this$statusFlag.equals(other$statusFlag)) {
                  return false;
               }

               Object this$inquiryKind = this.getInquiryKind();
               Object other$inquiryKind = other.getInquiryKind();
               if (this$inquiryKind == null) {
                  if (other$inquiryKind != null) {
                     return false;
                  }
               } else if (!this$inquiryKind.equals(other$inquiryKind)) {
                  return false;
               }

               label458: {
                  Object this$tranKind = this.getTranKind();
                  Object other$tranKind = other.getTranKind();
                  if (this$tranKind == null) {
                     if (other$tranKind == null) {
                        break label458;
                     }
                  } else if (this$tranKind.equals(other$tranKind)) {
                     break label458;
                  }

                  return false;
               }

               label451: {
                  Object this$queryStatus = this.getQueryStatus();
                  Object other$queryStatus = other.getQueryStatus();
                  if (this$queryStatus == null) {
                     if (other$queryStatus == null) {
                        break label451;
                     }
                  } else if (this$queryStatus.equals(other$queryStatus)) {
                     break label451;
                  }

                  return false;
               }

               Object this$clientNo = this.getClientNo();
               Object other$clientNo = other.getClientNo();
               if (this$clientNo == null) {
                  if (other$clientNo != null) {
                     return false;
                  }
               } else if (!this$clientNo.equals(other$clientNo)) {
                  return false;
               }

               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo != null) {
                     return false;
                  }
               } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
                  return false;
               }

               label430: {
                  Object this$prodType = this.getProdType();
                  Object other$prodType = other.getProdType();
                  if (this$prodType == null) {
                     if (other$prodType == null) {
                        break label430;
                     }
                  } else if (this$prodType.equals(other$prodType)) {
                     break label430;
                  }

                  return false;
               }

               label423: {
                  Object this$acctCcy = this.getAcctCcy();
                  Object other$acctCcy = other.getAcctCcy();
                  if (this$acctCcy == null) {
                     if (other$acctCcy == null) {
                        break label423;
                     }
                  } else if (this$acctCcy.equals(other$acctCcy)) {
                     break label423;
                  }

                  return false;
               }

               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo != null) {
                     return false;
                  }
               } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                  return false;
               }

               label409: {
                  Object this$channelSeqNo = this.getChannelSeqNo();
                  Object other$channelSeqNo = other.getChannelSeqNo();
                  if (this$channelSeqNo == null) {
                     if (other$channelSeqNo == null) {
                        break label409;
                     }
                  } else if (this$channelSeqNo.equals(other$channelSeqNo)) {
                     break label409;
                  }

                  return false;
               }

               Object this$startDate = this.getStartDate();
               Object other$startDate = other.getStartDate();
               if (this$startDate == null) {
                  if (other$startDate != null) {
                     return false;
                  }
               } else if (!this$startDate.equals(other$startDate)) {
                  return false;
               }

               label395: {
                  Object this$endDate = this.getEndDate();
                  Object other$endDate = other.getEndDate();
                  if (this$endDate == null) {
                     if (other$endDate == null) {
                        break label395;
                     }
                  } else if (this$endDate.equals(other$endDate)) {
                     break label395;
                  }

                  return false;
               }

               Object this$tranType = this.getTranType();
               Object other$tranType = other.getTranType();
               if (this$tranType == null) {
                  if (other$tranType != null) {
                     return false;
                  }
               } else if (!this$tranType.equals(other$tranType)) {
                  return false;
               }

               Object this$reversal = this.getReversal();
               Object other$reversal = other.getReversal();
               if (this$reversal == null) {
                  if (other$reversal != null) {
                     return false;
                  }
               } else if (!this$reversal.equals(other$reversal)) {
                  return false;
               }

               label374: {
                  Object this$queryDate = this.getQueryDate();
                  Object other$queryDate = other.getQueryDate();
                  if (this$queryDate == null) {
                     if (other$queryDate == null) {
                        break label374;
                     }
                  } else if (this$queryDate.equals(other$queryDate)) {
                     break label374;
                  }

                  return false;
               }

               label367: {
                  Object this$pbkUpdFlag = this.getPbkUpdFlag();
                  Object other$pbkUpdFlag = other.getPbkUpdFlag();
                  if (this$pbkUpdFlag == null) {
                     if (other$pbkUpdFlag == null) {
                        break label367;
                     }
                  } else if (this$pbkUpdFlag.equals(other$pbkUpdFlag)) {
                     break label367;
                  }

                  return false;
               }

               Object this$transferType = this.getTransferType();
               Object other$transferType = other.getTransferType();
               if (this$transferType == null) {
                  if (other$transferType != null) {
                     return false;
                  }
               } else if (!this$transferType.equals(other$transferType)) {
                  return false;
               }

               Object this$reference = this.getReference();
               Object other$reference = other.getReference();
               if (this$reference == null) {
                  if (other$reference != null) {
                     return false;
                  }
               } else if (!this$reference.equals(other$reference)) {
                  return false;
               }

               label346: {
                  Object this$sourceType = this.getSourceType();
                  Object other$sourceType = other.getSourceType();
                  if (this$sourceType == null) {
                     if (other$sourceType == null) {
                        break label346;
                     }
                  } else if (this$sourceType.equals(other$sourceType)) {
                     break label346;
                  }

                  return false;
               }

               label339: {
                  Object this$term = this.getTerm();
                  Object other$term = other.getTerm();
                  if (this$term == null) {
                     if (other$term == null) {
                        break label339;
                     }
                  } else if (this$term.equals(other$term)) {
                     break label339;
                  }

                  return false;
               }

               Object this$termType = this.getTermType();
               Object other$termType = other.getTermType();
               if (this$termType == null) {
                  if (other$termType != null) {
                     return false;
                  }
               } else if (!this$termType.equals(other$termType)) {
                  return false;
               }

               Object this$othRealBaseAcctNo = this.getOthRealBaseAcctNo();
               Object other$othRealBaseAcctNo = other.getOthRealBaseAcctNo();
               if (this$othRealBaseAcctNo == null) {
                  if (other$othRealBaseAcctNo != null) {
                     return false;
                  }
               } else if (!this$othRealBaseAcctNo.equals(other$othRealBaseAcctNo)) {
                  return false;
               }

               label318: {
                  Object this$othRealTranName = this.getOthRealTranName();
                  Object other$othRealTranName = other.getOthRealTranName();
                  if (this$othRealTranName == null) {
                     if (other$othRealTranName == null) {
                        break label318;
                     }
                  } else if (this$othRealTranName.equals(other$othRealTranName)) {
                     break label318;
                  }

                  return false;
               }

               label311: {
                  Object this$othBaseAcctNo = this.getOthBaseAcctNo();
                  Object other$othBaseAcctNo = other.getOthBaseAcctNo();
                  if (this$othBaseAcctNo == null) {
                     if (other$othBaseAcctNo == null) {
                        break label311;
                     }
                  } else if (this$othBaseAcctNo.equals(other$othBaseAcctNo)) {
                     break label311;
                  }

                  return false;
               }

               Object this$othAcctName = this.getOthAcctName();
               Object other$othAcctName = other.getOthAcctName();
               if (this$othAcctName == null) {
                  if (other$othAcctName != null) {
                     return false;
                  }
               } else if (!this$othAcctName.equals(other$othAcctName)) {
                  return false;
               }

               label297: {
                  Object this$isPrint = this.getIsPrint();
                  Object other$isPrint = other.getIsPrint();
                  if (this$isPrint == null) {
                     if (other$isPrint == null) {
                        break label297;
                     }
                  } else if (this$isPrint.equals(other$isPrint)) {
                     break label297;
                  }

                  return false;
               }

               Object this$company = this.getCompany();
               Object other$company = other.getCompany();
               if (this$company == null) {
                  if (other$company != null) {
                     return false;
                  }
               } else if (!this$company.equals(other$company)) {
                  return false;
               }

               label283: {
                  Object this$narrativeClass = this.getNarrativeClass();
                  Object other$narrativeClass = other.getNarrativeClass();
                  if (this$narrativeClass == null) {
                     if (other$narrativeClass == null) {
                        break label283;
                     }
                  } else if (this$narrativeClass.equals(other$narrativeClass)) {
                     break label283;
                  }

                  return false;
               }

               Object this$balType = this.getBalType();
               Object other$balType = other.getBalType();
               if (this$balType == null) {
                  if (other$balType != null) {
                     return false;
                  }
               } else if (!this$balType.equals(other$balType)) {
                  return false;
               }

               Object this$subSeqNo = this.getSubSeqNo();
               Object other$subSeqNo = other.getSubSeqNo();
               if (this$subSeqNo == null) {
                  if (other$subSeqNo != null) {
                     return false;
                  }
               } else if (!this$subSeqNo.equals(other$subSeqNo)) {
                  return false;
               }

               label262: {
                  Object this$sourceModule = this.getSourceModule();
                  Object other$sourceModule = other.getSourceModule();
                  if (this$sourceModule == null) {
                     if (other$sourceModule == null) {
                        break label262;
                     }
                  } else if (this$sourceModule.equals(other$sourceModule)) {
                     break label262;
                  }

                  return false;
               }

               label255: {
                  Object this$tranBranch = this.getTranBranch();
                  Object other$tranBranch = other.getTranBranch();
                  if (this$tranBranch == null) {
                     if (other$tranBranch == null) {
                        break label255;
                     }
                  } else if (this$tranBranch.equals(other$tranBranch)) {
                     break label255;
                  }

                  return false;
               }

               Object this$userId = this.getUserId();
               Object other$userId = other.getUserId();
               if (this$userId == null) {
                  if (other$userId != null) {
                     return false;
                  }
               } else if (!this$userId.equals(other$userId)) {
                  return false;
               }

               Object this$lowestTranAmt = this.getLowestTranAmt();
               Object other$lowestTranAmt = other.getLowestTranAmt();
               if (this$lowestTranAmt == null) {
                  if (other$lowestTranAmt != null) {
                     return false;
                  }
               } else if (!this$lowestTranAmt.equals(other$lowestTranAmt)) {
                  return false;
               }

               label234: {
                  Object this$tranHighestAmt = this.getTranHighestAmt();
                  Object other$tranHighestAmt = other.getTranHighestAmt();
                  if (this$tranHighestAmt == null) {
                     if (other$tranHighestAmt == null) {
                        break label234;
                     }
                  } else if (this$tranHighestAmt.equals(other$tranHighestAmt)) {
                     break label234;
                  }

                  return false;
               }

               label227: {
                  Object this$tranDateOrder = this.getTranDateOrder();
                  Object other$tranDateOrder = other.getTranDateOrder();
                  if (this$tranDateOrder == null) {
                     if (other$tranDateOrder == null) {
                        break label227;
                     }
                  } else if (this$tranDateOrder.equals(other$tranDateOrder)) {
                     break label227;
                  }

                  return false;
               }

               Object this$filterBaseAcctNo = this.getFilterBaseAcctNo();
               Object other$filterBaseAcctNo = other.getFilterBaseAcctNo();
               if (this$filterBaseAcctNo == null) {
                  if (other$filterBaseAcctNo != null) {
                     return false;
                  }
               } else if (!this$filterBaseAcctNo.equals(other$filterBaseAcctNo)) {
                  return false;
               }

               Object this$tranHistQueryType = this.getTranHistQueryType();
               Object other$tranHistQueryType = other.getTranHistQueryType();
               if (this$tranHistQueryType == null) {
                  if (other$tranHistQueryType != null) {
                     return false;
                  }
               } else if (!this$tranHistQueryType.equals(other$tranHistQueryType)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100010In.Body;
      }
      public String toString() {
         return "Core1400100010In.Body(histKind=" + this.getHistKind() + ", statusFlag=" + this.getStatusFlag() + ", inquiryKind=" + this.getInquiryKind() + ", tranKind=" + this.getTranKind() + ", queryStatus=" + this.getQueryStatus() + ", clientNo=" + this.getClientNo() + ", baseAcctNo=" + this.getBaseAcctNo() + ", prodType=" + this.getProdType() + ", acctCcy=" + this.getAcctCcy() + ", acctSeqNo=" + this.getAcctSeqNo() + ", channelSeqNo=" + this.getChannelSeqNo() + ", startDate=" + this.getStartDate() + ", endDate=" + this.getEndDate() + ", tranType=" + this.getTranType() + ", reversal=" + this.getReversal() + ", queryDate=" + this.getQueryDate() + ", pbkUpdFlag=" + this.getPbkUpdFlag() + ", transferType=" + this.getTransferType() + ", reference=" + this.getReference() + ", sourceType=" + this.getSourceType() + ", term=" + this.getTerm() + ", termType=" + this.getTermType() + ", othRealBaseAcctNo=" + this.getOthRealBaseAcctNo() + ", othRealTranName=" + this.getOthRealTranName() + ", othBaseAcctNo=" + this.getOthBaseAcctNo() + ", othAcctName=" + this.getOthAcctName() + ", isPrint=" + this.getIsPrint() + ", company=" + this.getCompany() + ", narrativeClass=" + this.getNarrativeClass() + ", balType=" + this.getBalType() + ", subSeqNo=" + this.getSubSeqNo() + ", sourceModule=" + this.getSourceModule() + ", tranBranch=" + this.getTranBranch() + ", userId=" + this.getUserId() + ", lowestTranAmt=" + this.getLowestTranAmt() + ", tranHighestAmt=" + this.getTranHighestAmt() + ", tranDateOrder=" + this.getTranDateOrder() + ", filterBaseAcctNo=" + this.getFilterBaseAcctNo() + ", tranHistQueryType=" + this.getTranHistQueryType() + ")";
      }
   }
}
