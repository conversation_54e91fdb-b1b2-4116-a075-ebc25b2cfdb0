package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000155In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000155Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12000155 {
   String URL = "/rb/nfin/card/lucky/assign";


   @ApiRemark("吉祥卡分配")
   @ApiDesc("该接口用于分配已签收的吉祥卡")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0155"
   )
   @BusinessCategory("1200-非金融")
   @FunctionCategory("RB20-借记卡")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core12000155Out runService(Core12000155In var1);
}
