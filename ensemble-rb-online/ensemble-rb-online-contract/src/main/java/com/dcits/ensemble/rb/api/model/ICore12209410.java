package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12209410In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12209410Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12209410 {
   String URL = "/rb/inq/tran/excel";


   @ApiRemark("交易历史查询(文件)")
   @ApiDesc("用于将交易历史查询的结果生成文件")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1220",
      messageCode = "9410"
   )
   @BusinessCategory("1220-文件")
   @FunctionCategory("RB02-账户管理")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core12209410Out runService(Core12209410In var1);
}
