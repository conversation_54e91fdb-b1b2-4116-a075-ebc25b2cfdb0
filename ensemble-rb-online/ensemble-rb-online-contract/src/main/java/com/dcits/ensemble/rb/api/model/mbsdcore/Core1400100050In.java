package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400100050In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400100050In.Body body;

   public Core1400100050In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400100050In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400100050In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100050In)) {
         return false;
      } else {
         Core1400100050In other = (Core1400100050In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100050In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "协议状态",
         notNull = false,
         length = "2",
         inDesc = "A-生效,E-失效",
         remark = "普通协议使用，可应用于大部分场景",
         maxSize = 2
      )
      private String agreementStatus;
      @V(
         desc = "协议编号",
         notNull = false,
         length = "50",
         remark = "协议编号",
         maxSize = 50
      )
      private String agreementId;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public String getAgreementStatus() {
         return this.agreementStatus;
      }

      public String getAgreementId() {
         return this.agreementId;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setAgreementStatus(String agreementStatus) {
         this.agreementStatus = agreementStatus;
      }

      public void setAgreementId(String agreementId) {
         this.agreementId = agreementId;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100050In.Body)) {
            return false;
         } else {
            Core1400100050In.Body other = (Core1400100050In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label59: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label59;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label59;
                  }

                  return false;
               }

               Object this$clientNo = this.getClientNo();
               Object other$clientNo = other.getClientNo();
               if (this$clientNo == null) {
                  if (other$clientNo != null) {
                     return false;
                  }
               } else if (!this$clientNo.equals(other$clientNo)) {
                  return false;
               }

               Object this$agreementStatus = this.getAgreementStatus();
               Object other$agreementStatus = other.getAgreementStatus();
               if (this$agreementStatus == null) {
                  if (other$agreementStatus != null) {
                     return false;
                  }
               } else if (!this$agreementStatus.equals(other$agreementStatus)) {
                  return false;
               }

               Object this$agreementId = this.getAgreementId();
               Object other$agreementId = other.getAgreementId();
               if (this$agreementId == null) {
                  if (other$agreementId != null) {
                     return false;
                  }
               } else if (!this$agreementId.equals(other$agreementId)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100050In.Body;
      }
      public String toString() {
         return "Core1400100050In.Body(baseAcctNo=" + this.getBaseAcctNo() + ", clientNo=" + this.getClientNo() + ", agreementStatus=" + this.getAgreementStatus() + ", agreementId=" + this.getAgreementId() + ")";
      }
   }
}
