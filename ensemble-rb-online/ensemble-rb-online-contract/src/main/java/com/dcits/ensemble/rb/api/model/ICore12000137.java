package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000137In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000137Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12000137 {
   String URL = "/rb/nfin/batch/update/comtact";

   
   @ApiRemark("批量更新账户的地址信息（东亚POC）")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0137"
   )
   Core12000137Out runService(Core12000137In var1);
}
