package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10009405In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10009405Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore10009405 {
   String URL = "/rb/fin/inner/dep";


   @ApiRemark("新增")
   @ApiDesc("核心为内部户现金存入交易场景提供接口，业务逻辑类同跨法人活期存入")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "9405"
   )
   @BusinessCategory("RB03-金融交易")
   @FunctionCategory("RB15-内部账")
   @ConsumeSys("TLE/137")
   @ApiUseStatus("PROJECT-项目")
   Core10009405Out runService(Core10009405In var1);
}
