package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;

@MessageOut
public class Core1400023405Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "所属机构号",
      notNull = false,
      length = "50",
      remark = "所属机构号",
      maxSize = 50
   )
   private String branch;
   @V(
      desc = "凭证类型",
      notNull = false,
      length = "10",
      remark = "凭证类型",
      maxSize = 10
   )
   private String docType;
   @V(
      desc = "生效标识",
      notNull = false,
      length = "1",
      remark = "生效标识",
      maxSize = 1
   )
   private String tranValidFlag;
   @V(
      desc = "账户单笔交易限额",
      notNull = false,
      length = "17",
      remark = "账户单笔交易限额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal singleLimit;
   @V(
      desc = "上一柜员ID",
      notNull = false,
      length = "30",
      remark = "上一柜员ID",
      maxSize = 30
   )
   private String lastUserId;
   @V(
      desc = "最后修改日期",
      notNull = false,
      remark = "最后修改日期"
   )
   private String lastChangeDate;
   @V(
      desc = "上次修改时间",
      notNull = false,
      length = "26",
      remark = "上次修改时间",
      maxSize = 26
   )
   private String lastChangeTime;

   public String getBranch() {
      return this.branch;
   }

   public String getDocType() {
      return this.docType;
   }

   public String getTranValidFlag() {
      return this.tranValidFlag;
   }

   public BigDecimal getSingleLimit() {
      return this.singleLimit;
   }

   public String getLastUserId() {
      return this.lastUserId;
   }

   public String getLastChangeDate() {
      return this.lastChangeDate;
   }

   public String getLastChangeTime() {
      return this.lastChangeTime;
   }

   public void setBranch(String branch) {
      this.branch = branch;
   }

   public void setDocType(String docType) {
      this.docType = docType;
   }

   public void setTranValidFlag(String tranValidFlag) {
      this.tranValidFlag = tranValidFlag;
   }

   public void setSingleLimit(BigDecimal singleLimit) {
      this.singleLimit = singleLimit;
   }

   public void setLastUserId(String lastUserId) {
      this.lastUserId = lastUserId;
   }

   public void setLastChangeDate(String lastChangeDate) {
      this.lastChangeDate = lastChangeDate;
   }

   public void setLastChangeTime(String lastChangeTime) {
      this.lastChangeTime = lastChangeTime;
   }

   public String toString() {
      return "Core1400023405Out(branch=" + this.getBranch() + ", docType=" + this.getDocType() + ", tranValidFlag=" + this.getTranValidFlag() + ", singleLimit=" + this.getSingleLimit() + ", lastUserId=" + this.getLastUserId() + ", lastChangeDate=" + this.getLastChangeDate() + ", lastChangeTime=" + this.getLastChangeTime() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400023405Out)) {
         return false;
      } else {
         Core1400023405Out other = (Core1400023405Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            label97: {
               Object this$branch = this.getBranch();
               Object other$branch = other.getBranch();
               if (this$branch == null) {
                  if (other$branch == null) {
                     break label97;
                  }
               } else if (this$branch.equals(other$branch)) {
                  break label97;
               }

               return false;
            }

            Object this$docType = this.getDocType();
            Object other$docType = other.getDocType();
            if (this$docType == null) {
               if (other$docType != null) {
                  return false;
               }
            } else if (!this$docType.equals(other$docType)) {
               return false;
            }

            Object this$tranValidFlag = this.getTranValidFlag();
            Object other$tranValidFlag = other.getTranValidFlag();
            if (this$tranValidFlag == null) {
               if (other$tranValidFlag != null) {
                  return false;
               }
            } else if (!this$tranValidFlag.equals(other$tranValidFlag)) {
               return false;
            }

            label76: {
               Object this$singleLimit = this.getSingleLimit();
               Object other$singleLimit = other.getSingleLimit();
               if (this$singleLimit == null) {
                  if (other$singleLimit == null) {
                     break label76;
                  }
               } else if (this$singleLimit.equals(other$singleLimit)) {
                  break label76;
               }

               return false;
            }

            Object this$lastUserId = this.getLastUserId();
            Object other$lastUserId = other.getLastUserId();
            if (this$lastUserId == null) {
               if (other$lastUserId != null) {
                  return false;
               }
            } else if (!this$lastUserId.equals(other$lastUserId)) {
               return false;
            }

            Object this$lastChangeDate = this.getLastChangeDate();
            Object other$lastChangeDate = other.getLastChangeDate();
            if (this$lastChangeDate == null) {
               if (other$lastChangeDate != null) {
                  return false;
               }
            } else if (!this$lastChangeDate.equals(other$lastChangeDate)) {
               return false;
            }

            Object this$lastChangeTime = this.getLastChangeTime();
            Object other$lastChangeTime = other.getLastChangeTime();
            if (this$lastChangeTime == null) {
               if (other$lastChangeTime != null) {
                  return false;
               }
            } else if (!this$lastChangeTime.equals(other$lastChangeTime)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400023405Out;
   }
}
