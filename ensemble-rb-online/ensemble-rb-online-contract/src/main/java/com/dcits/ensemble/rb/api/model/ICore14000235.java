package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000235In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000235Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000235 {
   String URL = "/rb/inq/dc/leaveTrfBalQuery";


   @ApiRemark("大额存单剩余可转让本金查询")
   @ApiDesc("大额存单剩余可转让本金查询")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0235"
   )
   @FunctionCategory("RB10-大额存单")
   @ConsumeSys("EOS")
   @ApiUseStatus("PRODUCT-产品")
   Core14000235Out runService(Core14000235In var1);
}
