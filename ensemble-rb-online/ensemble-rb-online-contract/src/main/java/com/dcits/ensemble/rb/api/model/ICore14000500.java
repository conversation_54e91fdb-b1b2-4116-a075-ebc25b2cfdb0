package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000500In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000500Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000500 {
   String URL = "/rb/nfin/dc/interest/calculate";


   @ApiRemark("大额存单转让利息试算")
   @ApiDesc("根据转让本金、转让总对价、挂单开始日期、挂单结束日期、挂单天数、大额存单账号等计算挂单期间每天挂单利率以及到期利息、到期本息合计等")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0500"
   )
   @FunctionCategory("RB10-大额存单")
   @ConsumeSys("EOS")
   @ApiUseStatus("PRODUCT-产品")
   Core14000500Out runService(Core14000500In var1);
}
