package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000321In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000321Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000321 {
   String URL = "/rb/tran/print/query";


   @ApiRemark("贷款通过上送reference到存款查询要冲正的交易是否已经打印存折或回单")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0321"
   )
   @FunctionCategory("RB48-登记簿查询")
   @ConsumeSys("CBS")
   @ApiUseStatus("PROJECT-项目")
   Core14000321Out runService(Core14000321In var1);
}
