package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000135In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000135Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000135 {
   String URL = "/rb/inq/dc/transfer/register";


   @ApiRemark("转让申请查询")
   @ApiDesc("该功能用于已经进行转让申请但是未成交的记录进行查询，提供给电子渠道和柜面，供客户在电子渠道进行挑选或者在柜面进行查询。")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0135"
   )
   @BusinessCategory("1400-查询")
   @FunctionCategory("RB10-大额存单")
   @ConsumeSys("EOS")
   @ApiUseStatus("PRODUCT-产品")
   Core14000135Out runService(Core14000135In var1);
}
