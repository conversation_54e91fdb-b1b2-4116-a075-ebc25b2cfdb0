package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000028In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000028Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000028 {
   String URL = "/rb/inq/depositacct/list/client";


   @ApiDesc("可根据客户或账户查询活期账户相关信息，目前仅存抵贷使用，后续若有其他类似需求可扩充以供使用。20230516 新增该接口。")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0028"
   )
   @BusinessCategory("1400-查询")
   @FunctionCategory("RB47-客户账户查询")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core14000028Out runService(Core14000028In var1);
}
