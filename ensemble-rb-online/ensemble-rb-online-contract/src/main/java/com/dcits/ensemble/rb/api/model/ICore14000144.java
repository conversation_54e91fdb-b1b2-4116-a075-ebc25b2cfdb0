package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000144In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000144Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000144 {
   String URL = "/rb/inq/client/assetDetail";


   @ApiRemark("客户资产收支余额明细查询")
   @ApiDesc("客户资产收支余额明细查询")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0144"
   )
   @FunctionCategory("RB02-账户管理")
   @ConsumeSys("TLE/CIT")
   @ApiUseStatus("PRODUCT-产品")
   Core14000144Out runService(Core14000144In var1);
}
