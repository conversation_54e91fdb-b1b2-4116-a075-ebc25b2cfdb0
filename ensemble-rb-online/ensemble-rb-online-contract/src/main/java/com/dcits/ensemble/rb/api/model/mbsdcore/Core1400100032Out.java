package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400100032Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100032Out.LoanArray> loanArray;

   public List<Core1400100032Out.LoanArray> getLoanArray() {
      return this.loanArray;
   }

   public void setLoanArray(List<Core1400100032Out.LoanArray> loanArray) {
      this.loanArray = loanArray;
   }

   public String toString() {
      return "Core1400100032Out(loanArray=" + this.getLoanArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100032Out)) {
         return false;
      } else {
         Core1400100032Out other = (Core1400100032Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$loanArray = this.getLoanArray();
            Object other$loanArray = other.getLoanArray();
            if (this$loanArray == null) {
               if (other$loanArray != null) {
                  return false;
               }
            } else if (!this$loanArray.equals(other$loanArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100032Out;
   }
   public static class LoanArray {
      @V(
         desc = "账户名称",
         notNull = false,
         length = "200",
         remark = "账户名称，一般指中文账户名称",
         maxSize = 200
      )
      private String acctName;
      @V(
         desc = "贷款号",
         notNull = false,
         length = "50",
         remark = "贷款号",
         maxSize = 50
      )
      private String loanNo;
      @V(
         desc = "借据号",
         notNull = false,
         length = "50",
         remark = "借据号",
         maxSize = 50
      )
      private String cmisloanNo;
      @V(
         desc = "发放日期",
         notNull = false,
         remark = "发放日期"
      )
      private String ddDate;
      @V(
         desc = "到期日",
         notNull = false,
         remark = "到期日"
      )
      private String matureDate;
      @V(
         desc = "贷款状态",
         notNull = false,
         length = "1",
         remark = "贷款级的状态说明",
         maxSize = 1
      )
      private String loanStatus;
      @V(
         desc = "发放金额",
         notNull = false,
         length = "17",
         remark = "发放金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal ddAmt;
      @V(
         desc = "欠息金额",
         notNull = false,
         length = "17",
         remark = "欠息金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal intOutstanding;
      @V(
         desc = "合同号",
         notNull = false,
         length = "50",
         remark = "合同号",
         maxSize = 50
      )
      private String contractNo;
      @V(
         desc = "账户类型",
         notNull = false,
         length = "1",
         remark = "账户类型",
         maxSize = 1
      )
      private String acctType;
      @V(
         desc = "结算账号",
         notNull = false,
         length = "50",
         remark = "结算账号",
         maxSize = 50
      )
      private String settleBaseAcctNo;
      @V(
         desc = "贷款余额",
         notNull = false,
         length = "17",
         remark = "贷款余额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal loanAmt;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "本期利息",
         notNull = false,
         length = "17",
         remark = "本期利息",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal currStageIntAmt;

      public String getAcctName() {
         return this.acctName;
      }

      public String getLoanNo() {
         return this.loanNo;
      }

      public String getCmisloanNo() {
         return this.cmisloanNo;
      }

      public String getDdDate() {
         return this.ddDate;
      }

      public String getMatureDate() {
         return this.matureDate;
      }

      public String getLoanStatus() {
         return this.loanStatus;
      }

      public BigDecimal getDdAmt() {
         return this.ddAmt;
      }

      public BigDecimal getIntOutstanding() {
         return this.intOutstanding;
      }

      public String getContractNo() {
         return this.contractNo;
      }

      public String getAcctType() {
         return this.acctType;
      }

      public String getSettleBaseAcctNo() {
         return this.settleBaseAcctNo;
      }

      public BigDecimal getLoanAmt() {
         return this.loanAmt;
      }

      public String getCcy() {
         return this.ccy;
      }

      public BigDecimal getCurrStageIntAmt() {
         return this.currStageIntAmt;
      }

      public void setAcctName(String acctName) {
         this.acctName = acctName;
      }

      public void setLoanNo(String loanNo) {
         this.loanNo = loanNo;
      }

      public void setCmisloanNo(String cmisloanNo) {
         this.cmisloanNo = cmisloanNo;
      }

      public void setDdDate(String ddDate) {
         this.ddDate = ddDate;
      }

      public void setMatureDate(String matureDate) {
         this.matureDate = matureDate;
      }

      public void setLoanStatus(String loanStatus) {
         this.loanStatus = loanStatus;
      }

      public void setDdAmt(BigDecimal ddAmt) {
         this.ddAmt = ddAmt;
      }

      public void setIntOutstanding(BigDecimal intOutstanding) {
         this.intOutstanding = intOutstanding;
      }

      public void setContractNo(String contractNo) {
         this.contractNo = contractNo;
      }

      public void setAcctType(String acctType) {
         this.acctType = acctType;
      }

      public void setSettleBaseAcctNo(String settleBaseAcctNo) {
         this.settleBaseAcctNo = settleBaseAcctNo;
      }

      public void setLoanAmt(BigDecimal loanAmt) {
         this.loanAmt = loanAmt;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setCurrStageIntAmt(BigDecimal currStageIntAmt) {
         this.currStageIntAmt = currStageIntAmt;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100032Out.LoanArray)) {
            return false;
         } else {
            Core1400100032Out.LoanArray other = (Core1400100032Out.LoanArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$acctName = this.getAcctName();
               Object other$acctName = other.getAcctName();
               if (this$acctName == null) {
                  if (other$acctName != null) {
                     return false;
                  }
               } else if (!this$acctName.equals(other$acctName)) {
                  return false;
               }

               Object this$loanNo = this.getLoanNo();
               Object other$loanNo = other.getLoanNo();
               if (this$loanNo == null) {
                  if (other$loanNo != null) {
                     return false;
                  }
               } else if (!this$loanNo.equals(other$loanNo)) {
                  return false;
               }

               Object this$cmisloanNo = this.getCmisloanNo();
               Object other$cmisloanNo = other.getCmisloanNo();
               if (this$cmisloanNo == null) {
                  if (other$cmisloanNo != null) {
                     return false;
                  }
               } else if (!this$cmisloanNo.equals(other$cmisloanNo)) {
                  return false;
               }

               label158: {
                  Object this$ddDate = this.getDdDate();
                  Object other$ddDate = other.getDdDate();
                  if (this$ddDate == null) {
                     if (other$ddDate == null) {
                        break label158;
                     }
                  } else if (this$ddDate.equals(other$ddDate)) {
                     break label158;
                  }

                  return false;
               }

               label151: {
                  Object this$matureDate = this.getMatureDate();
                  Object other$matureDate = other.getMatureDate();
                  if (this$matureDate == null) {
                     if (other$matureDate == null) {
                        break label151;
                     }
                  } else if (this$matureDate.equals(other$matureDate)) {
                     break label151;
                  }

                  return false;
               }

               Object this$loanStatus = this.getLoanStatus();
               Object other$loanStatus = other.getLoanStatus();
               if (this$loanStatus == null) {
                  if (other$loanStatus != null) {
                     return false;
                  }
               } else if (!this$loanStatus.equals(other$loanStatus)) {
                  return false;
               }

               label137: {
                  Object this$ddAmt = this.getDdAmt();
                  Object other$ddAmt = other.getDdAmt();
                  if (this$ddAmt == null) {
                     if (other$ddAmt == null) {
                        break label137;
                     }
                  } else if (this$ddAmt.equals(other$ddAmt)) {
                     break label137;
                  }

                  return false;
               }

               label130: {
                  Object this$intOutstanding = this.getIntOutstanding();
                  Object other$intOutstanding = other.getIntOutstanding();
                  if (this$intOutstanding == null) {
                     if (other$intOutstanding == null) {
                        break label130;
                     }
                  } else if (this$intOutstanding.equals(other$intOutstanding)) {
                     break label130;
                  }

                  return false;
               }

               Object this$contractNo = this.getContractNo();
               Object other$contractNo = other.getContractNo();
               if (this$contractNo == null) {
                  if (other$contractNo != null) {
                     return false;
                  }
               } else if (!this$contractNo.equals(other$contractNo)) {
                  return false;
               }

               Object this$acctType = this.getAcctType();
               Object other$acctType = other.getAcctType();
               if (this$acctType == null) {
                  if (other$acctType != null) {
                     return false;
                  }
               } else if (!this$acctType.equals(other$acctType)) {
                  return false;
               }

               label109: {
                  Object this$settleBaseAcctNo = this.getSettleBaseAcctNo();
                  Object other$settleBaseAcctNo = other.getSettleBaseAcctNo();
                  if (this$settleBaseAcctNo == null) {
                     if (other$settleBaseAcctNo == null) {
                        break label109;
                     }
                  } else if (this$settleBaseAcctNo.equals(other$settleBaseAcctNo)) {
                     break label109;
                  }

                  return false;
               }

               label102: {
                  Object this$loanAmt = this.getLoanAmt();
                  Object other$loanAmt = other.getLoanAmt();
                  if (this$loanAmt == null) {
                     if (other$loanAmt == null) {
                        break label102;
                     }
                  } else if (this$loanAmt.equals(other$loanAmt)) {
                     break label102;
                  }

                  return false;
               }

               Object this$ccy = this.getCcy();
               Object other$ccy = other.getCcy();
               if (this$ccy == null) {
                  if (other$ccy != null) {
                     return false;
                  }
               } else if (!this$ccy.equals(other$ccy)) {
                  return false;
               }

               Object this$currStageIntAmt = this.getCurrStageIntAmt();
               Object other$currStageIntAmt = other.getCurrStageIntAmt();
               if (this$currStageIntAmt == null) {
                  if (other$currStageIntAmt != null) {
                     return false;
                  }
               } else if (!this$currStageIntAmt.equals(other$currStageIntAmt)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100032Out.LoanArray;
      }
      public String toString() {
         return "Core1400100032Out.LoanArray(acctName=" + this.getAcctName() + ", loanNo=" + this.getLoanNo() + ", cmisloanNo=" + this.getCmisloanNo() + ", ddDate=" + this.getDdDate() + ", matureDate=" + this.getMatureDate() + ", loanStatus=" + this.getLoanStatus() + ", ddAmt=" + this.getDdAmt() + ", intOutstanding=" + this.getIntOutstanding() + ", contractNo=" + this.getContractNo() + ", acctType=" + this.getAcctType() + ", settleBaseAcctNo=" + this.getSettleBaseAcctNo() + ", loanAmt=" + this.getLoanAmt() + ", ccy=" + this.getCcy() + ", currStageIntAmt=" + this.getCurrStageIntAmt() + ")";
      }
   }
}
