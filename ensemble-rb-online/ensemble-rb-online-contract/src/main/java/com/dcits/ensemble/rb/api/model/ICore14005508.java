package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14005508In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14005508Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14005508 {
   String URL = "/rb/inq/find/eco";


   @ApiRemark("深度优化")
   @ApiDesc("根据账户上送的起始日期、终止日期、客户号、账号进行该时间段内免收费用金额查询。")
   @EnsembleElements(
      serviceCode = "Core",
      messageType = "1400",
      messageCode = "5508"
   )
   @FunctionCategory("RB01-公共服务")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core14005508Out runService(Core14005508In var1);
}
