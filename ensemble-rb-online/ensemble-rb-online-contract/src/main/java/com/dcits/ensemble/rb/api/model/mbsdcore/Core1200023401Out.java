package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;

@MessageOut
public class Core1200023401Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "业务流水号",
      notNull = false,
      length = "50",
      remark = "支付流水号",
      maxSize = 50
   )
   private String serialNo;
   @V(
      desc = "票据密押",
      notNull = false,
      length = "200",
      remark = "票据密押",
      maxSize = 200
   )
   private String encrypKey;
   @V(
      desc = "出票行行号",
      notNull = false,
      length = "20",
      remark = "出票行行号",
      maxSize = 20
   )
   private String billBankNo;
   @V(
      desc = "出票行名称",
      notNull = false,
      length = "50",
      remark = "出票行名称",
      maxSize = 50
   )
   private String payerBankName;

   public String getSerialNo() {
      return this.serialNo;
   }

   public String getEncrypKey() {
      return this.encrypKey;
   }

   public String getBillBankNo() {
      return this.billBankNo;
   }

   public String getPayerBankName() {
      return this.payerBankName;
   }

   public void setSerialNo(String serialNo) {
      this.serialNo = serialNo;
   }

   public void setEncrypKey(String encrypKey) {
      this.encrypKey = encrypKey;
   }

   public void setBillBankNo(String billBankNo) {
      this.billBankNo = billBankNo;
   }

   public void setPayerBankName(String payerBankName) {
      this.payerBankName = payerBankName;
   }

   public String toString() {
      return "Core1200023401Out(serialNo=" + this.getSerialNo() + ", encrypKey=" + this.getEncrypKey() + ", billBankNo=" + this.getBillBankNo() + ", payerBankName=" + this.getPayerBankName() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200023401Out)) {
         return false;
      } else {
         Core1200023401Out other = (Core1200023401Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            label61: {
               Object this$serialNo = this.getSerialNo();
               Object other$serialNo = other.getSerialNo();
               if (this$serialNo == null) {
                  if (other$serialNo == null) {
                     break label61;
                  }
               } else if (this$serialNo.equals(other$serialNo)) {
                  break label61;
               }

               return false;
            }

            label54: {
               Object this$encrypKey = this.getEncrypKey();
               Object other$encrypKey = other.getEncrypKey();
               if (this$encrypKey == null) {
                  if (other$encrypKey == null) {
                     break label54;
                  }
               } else if (this$encrypKey.equals(other$encrypKey)) {
                  break label54;
               }

               return false;
            }

            Object this$billBankNo = this.getBillBankNo();
            Object other$billBankNo = other.getBillBankNo();
            if (this$billBankNo == null) {
               if (other$billBankNo != null) {
                  return false;
               }
            } else if (!this$billBankNo.equals(other$billBankNo)) {
               return false;
            }

            Object this$payerBankName = this.getPayerBankName();
            Object other$payerBankName = other.getPayerBankName();
            if (this$payerBankName == null) {
               if (other$payerBankName != null) {
                  return false;
               }
            } else if (!this$payerBankName.equals(other$payerBankName)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200023401Out;
   }
}
