package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10000105In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10000105Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore10000105 {
   String URL = "/rb/fin/tran/complement";


   @ApiRemark("华兴新增的再原有记账的基础上进行补账记账交易")
   @ApiDesc("华兴新增的再原有记账的基础上进行补账记账交易")
   @EnsembleElements(
      serviceCode = "Core",
      messageType = "1000",
      messageCode = "0105"
   )
   @BusinessCategory("华兴新增的再原有记账的基础上进行补账记账")
   @FunctionCategory("RB03-金融交易")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core10000105Out runService(Core10000105In var1);
}
