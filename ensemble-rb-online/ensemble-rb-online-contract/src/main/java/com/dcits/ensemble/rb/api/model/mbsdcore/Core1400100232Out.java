package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;

@MessageOut
public class Core1400100232Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "产品类型",
      notNull = false,
      length = "20",
      remark = "产品类型",
      maxSize = 20
   )
   private String prodType;
   @V(
      desc = "币种",
      notNull = false,
      length = "3",
      remark = "币种",
      maxSize = 3
   )
   private String ccy;
   @V(
      desc = "序号",
      notNull = false,
      length = "50",
      remark = "序号",
      maxSize = 50
   )
   private String seqNo;
   @V(
      desc = "账号/卡号",
      notNull = false,
      length = "50",
      remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
      maxSize = 50
   )
   private String baseAcctNo;
   @V(
      desc = "总金额",
      notNull = false,
      length = "17",
      remark = "总金额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal totalAmt;
   @V(
      desc = "转入费用",
      notNull = false,
      length = "17",
      remark = "转入费用",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal trfInFeeAmt;
   @V(
      desc = "转出费用",
      notNull = false,
      length = "17",
      remark = "转出费用",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal trfOutFeeAmt;
   @V(
      desc = "客户号",
      notNull = false,
      length = "20",
      remark = "客户号",
      maxSize = 20
   )
   private String clientNo;
   @V(
      desc = "状态",
      notNull = false,
      length = "1",
      remark = "状态",
      maxSize = 1
   )
   private String status;
   @V(
      desc = "生效日期",
      notNull = false,
      remark = "生效日期"
   )
   private String effectDate;
   @V(
      desc = "定期到期日",
      notNull = false,
      remark = "定期到期日"
   )
   private String tdaAcctEndDate;
   @V(
      desc = "存款付息方式",
      notNull = false,
      length = "2",
      remark = "存款付息方式",
      maxSize = 2
   )
   private String payIntMode;
   @V(
      desc = "转让本金",
      notNull = false,
      length = "17",
      remark = "转让本金",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal tranferPri;
   @V(
      desc = "受益人收益率",
      notNull = false,
      length = "17",
      remark = "受益人收益率",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal transferIntRate;
   @V(
      desc = "是否定向转让",
      notNull = false,
      length = "3",
      remark = "是否定向转让",
      maxSize = 3
   )
   private String isSpec;
   @V(
      desc = "受益人收益率",
      notNull = false,
      length = "17",
      remark = "受益人收益率",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal benefitIntRate;
   @V(
      desc = "挂单天数",
      notNull = false,
      length = "3",
      remark = "挂单天数",
      maxSize = 3
   )
   private String billDays;
   @V(
      desc = "转让类型",
      notNull = false,
      length = "3",
      remark = "转让类型",
      maxSize = 3
   )
   private String tranferMethod;

   public String getProdType() {
      return this.prodType;
   }

   public String getCcy() {
      return this.ccy;
   }

   public String getSeqNo() {
      return this.seqNo;
   }

   public String getBaseAcctNo() {
      return this.baseAcctNo;
   }

   public BigDecimal getTotalAmt() {
      return this.totalAmt;
   }

   public BigDecimal getTrfInFeeAmt() {
      return this.trfInFeeAmt;
   }

   public BigDecimal getTrfOutFeeAmt() {
      return this.trfOutFeeAmt;
   }

   public String getClientNo() {
      return this.clientNo;
   }

   public String getStatus() {
      return this.status;
   }

   public String getEffectDate() {
      return this.effectDate;
   }

   public String getTdaAcctEndDate() {
      return this.tdaAcctEndDate;
   }

   public String getPayIntMode() {
      return this.payIntMode;
   }

   public BigDecimal getTranferPri() {
      return this.tranferPri;
   }

   public BigDecimal getTransferIntRate() {
      return this.transferIntRate;
   }

   public String getIsSpec() {
      return this.isSpec;
   }

   public BigDecimal getBenefitIntRate() {
      return this.benefitIntRate;
   }

   public String getBillDays() {
      return this.billDays;
   }

   public String getTranferMethod() {
      return this.tranferMethod;
   }

   public void setProdType(String prodType) {
      this.prodType = prodType;
   }

   public void setCcy(String ccy) {
      this.ccy = ccy;
   }

   public void setSeqNo(String seqNo) {
      this.seqNo = seqNo;
   }

   public void setBaseAcctNo(String baseAcctNo) {
      this.baseAcctNo = baseAcctNo;
   }

   public void setTotalAmt(BigDecimal totalAmt) {
      this.totalAmt = totalAmt;
   }

   public void setTrfInFeeAmt(BigDecimal trfInFeeAmt) {
      this.trfInFeeAmt = trfInFeeAmt;
   }

   public void setTrfOutFeeAmt(BigDecimal trfOutFeeAmt) {
      this.trfOutFeeAmt = trfOutFeeAmt;
   }

   public void setClientNo(String clientNo) {
      this.clientNo = clientNo;
   }

   public void setStatus(String status) {
      this.status = status;
   }

   public void setEffectDate(String effectDate) {
      this.effectDate = effectDate;
   }

   public void setTdaAcctEndDate(String tdaAcctEndDate) {
      this.tdaAcctEndDate = tdaAcctEndDate;
   }

   public void setPayIntMode(String payIntMode) {
      this.payIntMode = payIntMode;
   }

   public void setTranferPri(BigDecimal tranferPri) {
      this.tranferPri = tranferPri;
   }

   public void setTransferIntRate(BigDecimal transferIntRate) {
      this.transferIntRate = transferIntRate;
   }

   public void setIsSpec(String isSpec) {
      this.isSpec = isSpec;
   }

   public void setBenefitIntRate(BigDecimal benefitIntRate) {
      this.benefitIntRate = benefitIntRate;
   }

   public void setBillDays(String billDays) {
      this.billDays = billDays;
   }

   public void setTranferMethod(String tranferMethod) {
      this.tranferMethod = tranferMethod;
   }

   public String toString() {
      return "Core1400100232Out(prodType=" + this.getProdType() + ", ccy=" + this.getCcy() + ", seqNo=" + this.getSeqNo() + ", baseAcctNo=" + this.getBaseAcctNo() + ", totalAmt=" + this.getTotalAmt() + ", trfInFeeAmt=" + this.getTrfInFeeAmt() + ", trfOutFeeAmt=" + this.getTrfOutFeeAmt() + ", clientNo=" + this.getClientNo() + ", status=" + this.getStatus() + ", effectDate=" + this.getEffectDate() + ", tdaAcctEndDate=" + this.getTdaAcctEndDate() + ", payIntMode=" + this.getPayIntMode() + ", tranferPri=" + this.getTranferPri() + ", transferIntRate=" + this.getTransferIntRate() + ", isSpec=" + this.getIsSpec() + ", benefitIntRate=" + this.getBenefitIntRate() + ", billDays=" + this.getBillDays() + ", tranferMethod=" + this.getTranferMethod() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100232Out)) {
         return false;
      } else {
         Core1400100232Out other = (Core1400100232Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$prodType = this.getProdType();
            Object other$prodType = other.getProdType();
            if (this$prodType == null) {
               if (other$prodType != null) {
                  return false;
               }
            } else if (!this$prodType.equals(other$prodType)) {
               return false;
            }

            Object this$ccy = this.getCcy();
            Object other$ccy = other.getCcy();
            if (this$ccy == null) {
               if (other$ccy != null) {
                  return false;
               }
            } else if (!this$ccy.equals(other$ccy)) {
               return false;
            }

            label215: {
               Object this$seqNo = this.getSeqNo();
               Object other$seqNo = other.getSeqNo();
               if (this$seqNo == null) {
                  if (other$seqNo == null) {
                     break label215;
                  }
               } else if (this$seqNo.equals(other$seqNo)) {
                  break label215;
               }

               return false;
            }

            label208: {
               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo == null) {
                     break label208;
                  }
               } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                  break label208;
               }

               return false;
            }

            Object this$totalAmt = this.getTotalAmt();
            Object other$totalAmt = other.getTotalAmt();
            if (this$totalAmt == null) {
               if (other$totalAmt != null) {
                  return false;
               }
            } else if (!this$totalAmt.equals(other$totalAmt)) {
               return false;
            }

            Object this$trfInFeeAmt = this.getTrfInFeeAmt();
            Object other$trfInFeeAmt = other.getTrfInFeeAmt();
            if (this$trfInFeeAmt == null) {
               if (other$trfInFeeAmt != null) {
                  return false;
               }
            } else if (!this$trfInFeeAmt.equals(other$trfInFeeAmt)) {
               return false;
            }

            label187: {
               Object this$trfOutFeeAmt = this.getTrfOutFeeAmt();
               Object other$trfOutFeeAmt = other.getTrfOutFeeAmt();
               if (this$trfOutFeeAmt == null) {
                  if (other$trfOutFeeAmt == null) {
                     break label187;
                  }
               } else if (this$trfOutFeeAmt.equals(other$trfOutFeeAmt)) {
                  break label187;
               }

               return false;
            }

            Object this$clientNo = this.getClientNo();
            Object other$clientNo = other.getClientNo();
            if (this$clientNo == null) {
               if (other$clientNo != null) {
                  return false;
               }
            } else if (!this$clientNo.equals(other$clientNo)) {
               return false;
            }

            Object this$status = this.getStatus();
            Object other$status = other.getStatus();
            if (this$status == null) {
               if (other$status != null) {
                  return false;
               }
            } else if (!this$status.equals(other$status)) {
               return false;
            }

            label166: {
               Object this$effectDate = this.getEffectDate();
               Object other$effectDate = other.getEffectDate();
               if (this$effectDate == null) {
                  if (other$effectDate == null) {
                     break label166;
                  }
               } else if (this$effectDate.equals(other$effectDate)) {
                  break label166;
               }

               return false;
            }

            label159: {
               Object this$tdaAcctEndDate = this.getTdaAcctEndDate();
               Object other$tdaAcctEndDate = other.getTdaAcctEndDate();
               if (this$tdaAcctEndDate == null) {
                  if (other$tdaAcctEndDate == null) {
                     break label159;
                  }
               } else if (this$tdaAcctEndDate.equals(other$tdaAcctEndDate)) {
                  break label159;
               }

               return false;
            }

            label152: {
               Object this$payIntMode = this.getPayIntMode();
               Object other$payIntMode = other.getPayIntMode();
               if (this$payIntMode == null) {
                  if (other$payIntMode == null) {
                     break label152;
                  }
               } else if (this$payIntMode.equals(other$payIntMode)) {
                  break label152;
               }

               return false;
            }

            Object this$tranferPri = this.getTranferPri();
            Object other$tranferPri = other.getTranferPri();
            if (this$tranferPri == null) {
               if (other$tranferPri != null) {
                  return false;
               }
            } else if (!this$tranferPri.equals(other$tranferPri)) {
               return false;
            }

            label138: {
               Object this$transferIntRate = this.getTransferIntRate();
               Object other$transferIntRate = other.getTransferIntRate();
               if (this$transferIntRate == null) {
                  if (other$transferIntRate == null) {
                     break label138;
                  }
               } else if (this$transferIntRate.equals(other$transferIntRate)) {
                  break label138;
               }

               return false;
            }

            Object this$isSpec = this.getIsSpec();
            Object other$isSpec = other.getIsSpec();
            if (this$isSpec == null) {
               if (other$isSpec != null) {
                  return false;
               }
            } else if (!this$isSpec.equals(other$isSpec)) {
               return false;
            }

            label124: {
               Object this$benefitIntRate = this.getBenefitIntRate();
               Object other$benefitIntRate = other.getBenefitIntRate();
               if (this$benefitIntRate == null) {
                  if (other$benefitIntRate == null) {
                     break label124;
                  }
               } else if (this$benefitIntRate.equals(other$benefitIntRate)) {
                  break label124;
               }

               return false;
            }

            Object this$billDays = this.getBillDays();
            Object other$billDays = other.getBillDays();
            if (this$billDays == null) {
               if (other$billDays != null) {
                  return false;
               }
            } else if (!this$billDays.equals(other$billDays)) {
               return false;
            }

            Object this$tranferMethod = this.getTranferMethod();
            Object other$tranferMethod = other.getTranferMethod();
            if (this$tranferMethod == null) {
               if (other$tranferMethod != null) {
                  return false;
               }
            } else if (!this$tranferMethod.equals(other$tranferMethod)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100232Out;
   }
}
