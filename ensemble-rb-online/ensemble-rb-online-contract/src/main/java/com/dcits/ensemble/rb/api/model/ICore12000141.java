package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000141In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000141Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12000141 {
   String URL = "/rb/nfin/csl/agreement/operate";

   
   @ApiRemark("联动扣款协议操作接口，进行签约、维护、解约")
   @ApiDesc("结账账户储蓄账户链接协议操作接口，进行签约、维护(支持维护扣划的储蓄账户、扣款方式)、解约")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0141"
   )
   Core12000141Out runService(Core12000141In var1);
}
