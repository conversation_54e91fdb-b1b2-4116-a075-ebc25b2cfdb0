package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000719In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000719Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000719 {
   String URL = "/rb/inq/passwordIsExist";


   @ApiRemark("查询账户密码是否存在")
   @ApiDesc("查询对应账户的指定密码是否存在，如果不指定密码类型，默认密码类型为查询密码")
   @EnsembleElements(
      serviceCode = "Core",
      messageType = "1400",
      messageCode = "0719"
   )
   @BusinessCategory("1200-非金融")
   @FunctionCategory("RB08-特殊业务")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core14000719Out runService(Core14000719In var1);
}
