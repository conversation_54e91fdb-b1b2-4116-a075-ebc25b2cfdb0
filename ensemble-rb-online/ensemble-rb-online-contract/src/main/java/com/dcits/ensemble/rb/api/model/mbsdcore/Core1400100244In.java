package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400100244In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400100244In.Body body;

   public Core1400100244In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400100244In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400100244In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100244In)) {
         return false;
      } else {
         Core1400100244In other = (Core1400100244In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100244In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "账号/卡号",
         notNull = true,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "序号",
         notNull = false,
         length = "50",
         remark = "序号",
         maxSize = 50
      )
      private String seqNo;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getSeqNo() {
         return this.seqNo;
      }

      public String getCcy() {
         return this.ccy;
      }

      public String getProdType() {
         return this.prodType;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setSeqNo(String seqNo) {
         this.seqNo = seqNo;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100244In.Body)) {
            return false;
         } else {
            Core1400100244In.Body other = (Core1400100244In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label59: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label59;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label59;
                  }

                  return false;
               }

               Object this$seqNo = this.getSeqNo();
               Object other$seqNo = other.getSeqNo();
               if (this$seqNo == null) {
                  if (other$seqNo != null) {
                     return false;
                  }
               } else if (!this$seqNo.equals(other$seqNo)) {
                  return false;
               }

               Object this$ccy = this.getCcy();
               Object other$ccy = other.getCcy();
               if (this$ccy == null) {
                  if (other$ccy != null) {
                     return false;
                  }
               } else if (!this$ccy.equals(other$ccy)) {
                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100244In.Body;
      }
      public String toString() {
         return "Core1400100244In.Body(baseAcctNo=" + this.getBaseAcctNo() + ", seqNo=" + this.getSeqNo() + ", ccy=" + this.getCcy() + ", prodType=" + this.getProdType() + ")";
      }
   }
}
