package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400100222Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "序号",
      notNull = false,
      length = "50",
      remark = "序号",
      maxSize = 50
   )
   private String seqNo;
   @V(
      desc = "限制编号",
      notNull = false,
      length = "50",
      remark = "限制编号",
      maxSize = 50
   )
   private String resSeqNo;
   @V(
      desc = "证件类型",
      notNull = false,
      length = "3",
      remark = "证件类型",
      maxSize = 3
   )
   private String documentType;
   @V(
      desc = "证件号码",
      notNull = false,
      length = "50",
      remark = "证件号码",
      maxSize = 50
   )
   private String documentId;
   @V(
      desc = "客户号",
      notNull = false,
      length = "20",
      remark = "客户号",
      maxSize = 20
   )
   private String clientNo;
   @V(
      desc = "客户中文名称",
      notNull = false,
      length = "200",
      remark = "客户中文名称",
      maxSize = 200
   )
   private String chClientName;
   @V(
      desc = "发证国家",
      notNull = false,
      length = "3",
      remark = "发证国家",
      maxSize = 3
   )
   private String issCountry;
   @V(
      desc = "客户名称",
      notNull = false,
      length = "200",
      remark = "客户名称",
      maxSize = 200
   )
   private String clientName;
   @V(
      desc = "产品类型",
      notNull = false,
      length = "20",
      remark = "产品类型",
      maxSize = 20
   )
   private String prodType;
   @V(
      desc = "账号/卡号",
      notNull = false,
      length = "50",
      remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
      maxSize = 50
   )
   private String baseAcctNo;
   @V(
      desc = "账户序号",
      notNull = false,
      length = "5",
      remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
      maxSize = 5
   )
   private String acctSeqNo;
   @V(
      desc = "卡号",
      notNull = false,
      length = "50",
      remark = "卡号",
      maxSize = 50
   )
   private String cardNo;
   @V(
      desc = "账户内部键值",
      notNull = false,
      length = "15",
      remark = "账户内部键值"
   )
   private Long internalKey;
   @V(
      desc = "账户用途",
      notNull = false,
      length = "10",
      remark = "账户用途",
      maxSize = 10
   )
   private String reasonCode;
   @V(
      desc = "发行起始日期",
      notNull = false,
      remark = "发行起始日期"
   )
   private String issueStartDate;
   @V(
      desc = "发行终止日期",
      notNull = false,
      remark = "发行终止日期"
   )
   private String issueEndDate;
   @V(
      desc = "删除日期",
      notNull = false,
      remark = "删除日期"
   )
   private String deleteDate;
   @V(
      desc = "开立日期",
      notNull = false,
      remark = "开立日期"
   )
   private String openDate;
   @V(
      desc = "预约登记日期",
      notNull = false,
      remark = "预约登记日期"
   )
   private String precontractDate;
   @V(
      desc = "预约开户日期",
      notNull = false,
      remark = "预约开户日期"
   )
   private String precontractOpenDate;
   @V(
      desc = "签发机构",
      notNull = false,
      length = "50",
      remark = "签发机构",
      maxSize = 50
   )
   private String issueBranch;
   @V(
      desc = "预约/认购机构",
      notNull = false,
      length = "50",
      remark = "预约/认购机构",
      maxSize = 50
   )
   private String precontractBranch;
   @V(
      desc = "预约号",
      notNull = false,
      length = "50",
      remark = "预约编号",
      maxSize = 50
   )
   private String precontractNo;
   @V(
      desc = "交易参考号",
      notNull = false,
      length = "50",
      remark = "交易参考号",
      maxSize = 50
   )
   private String reference;
   @V(
      desc = "期次发行金额",
      notNull = false,
      length = "17",
      remark = "期次发行金额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal issueAmt;
   @V(
      desc = "期次产品预约币种",
      notNull = false,
      length = "3",
      remark = "专指期次类产品预约币种",
      maxSize = 3
   )
   private String precontractCcy;
   @V(
      desc = "预约金额",
      notNull = false,
      length = "17",
      remark = "预约金额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal precontractAmt;
   @V(
      desc = "币种",
      notNull = false,
      length = "3",
      remark = "币种",
      maxSize = 3
   )
   private String ccy;
   @V(
      desc = "行内利率",
      notNull = false,
      length = "15",
      remark = "在人行基准利率调整后对客发布的行内利率",
      decimalLength = 8,
      precision = 8
   )
   private BigDecimal actualRate;
   @V(
      desc = "浮动利率",
      notNull = false,
      length = "15",
      remark = "浮动利率",
      decimalLength = 8,
      precision = 8
   )
   private BigDecimal floatRate;
   @V(
      desc = "执行利率",
      notNull = false,
      length = "15",
      remark = "执行利率",
      decimalLength = 8,
      precision = 8
   )
   private BigDecimal realRate;
   @V(
      desc = "利率类型",
      notNull = false,
      length = "5",
      remark = "利率类型",
      maxSize = 5
   )
   private String intType;
   @V(
      desc = "渠道类型",
      notNull = false,
      length = "10",
      remark = "渠道类型",
      maxSize = 10
   )
   private String sourceType;
   @V(
      desc = "预约/认购方式",
      notNull = false,
      length = "1",
      remark = "预约/认购方式",
      maxSize = 1
   )
   private String precontractStype;
   @V(
      desc = "期次代码",
      notNull = false,
      length = "50",
      remark = "期次代码",
      maxSize = 50
   )
   private String stageCode;
   @V(
      desc = "期次产品预约状态",
      notNull = false,
      length = "1",
      remark = "期次产品预约状态",
      maxSize = 1
   )
   private String precontractStatus;
   @V(
      desc = "删除原因",
      notNull = false,
      length = "200",
      remark = "删除原因",
      maxSize = 200
   )
   private String delReason;
   @V(
      desc = "失败原因",
      notNull = false,
      length = "200",
      remark = "失败原因",
      maxSize = 200
   )
   private String failureReason;
   @V(
      desc = "摘要",
      notNull = false,
      length = "500",
      remark = "开户时的账号用途，销户时的销户原因",
      maxSize = 500
   )
   private String narrative;
   @V(
      desc = "删除柜员",
      notNull = false,
      length = "30",
      remark = "删除柜员",
      maxSize = 30
   )
   private String delUserId;
   @V(
      desc = "删除授权柜员",
      notNull = false,
      length = "30",
      remark = "删除授权柜员",
      maxSize = 30
   )
   private String delAuthUserId;
   @V(
      desc = "交易柜员",
      notNull = false,
      length = "30",
      remark = "交易柜员",
      maxSize = 30
   )
   private String userId;
   @V(
      desc = "授权柜员",
      notNull = false,
      length = "30",
      remark = "授权柜员",
      maxSize = 30
   )
   private String authUserId;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100222Out.PrecontractArray> precontractArray;

   public String getSeqNo() {
      return this.seqNo;
   }

   public String getResSeqNo() {
      return this.resSeqNo;
   }

   public String getDocumentType() {
      return this.documentType;
   }

   public String getDocumentId() {
      return this.documentId;
   }

   public String getClientNo() {
      return this.clientNo;
   }

   public String getChClientName() {
      return this.chClientName;
   }

   public String getIssCountry() {
      return this.issCountry;
   }

   public String getClientName() {
      return this.clientName;
   }

   public String getProdType() {
      return this.prodType;
   }

   public String getBaseAcctNo() {
      return this.baseAcctNo;
   }

   public String getAcctSeqNo() {
      return this.acctSeqNo;
   }

   public String getCardNo() {
      return this.cardNo;
   }

   public Long getInternalKey() {
      return this.internalKey;
   }

   public String getReasonCode() {
      return this.reasonCode;
   }

   public String getIssueStartDate() {
      return this.issueStartDate;
   }

   public String getIssueEndDate() {
      return this.issueEndDate;
   }

   public String getDeleteDate() {
      return this.deleteDate;
   }

   public String getOpenDate() {
      return this.openDate;
   }

   public String getPrecontractDate() {
      return this.precontractDate;
   }

   public String getPrecontractOpenDate() {
      return this.precontractOpenDate;
   }

   public String getIssueBranch() {
      return this.issueBranch;
   }

   public String getPrecontractBranch() {
      return this.precontractBranch;
   }

   public String getPrecontractNo() {
      return this.precontractNo;
   }

   public String getReference() {
      return this.reference;
   }

   public BigDecimal getIssueAmt() {
      return this.issueAmt;
   }

   public String getPrecontractCcy() {
      return this.precontractCcy;
   }

   public BigDecimal getPrecontractAmt() {
      return this.precontractAmt;
   }

   public String getCcy() {
      return this.ccy;
   }

   public BigDecimal getActualRate() {
      return this.actualRate;
   }

   public BigDecimal getFloatRate() {
      return this.floatRate;
   }

   public BigDecimal getRealRate() {
      return this.realRate;
   }

   public String getIntType() {
      return this.intType;
   }

   public String getSourceType() {
      return this.sourceType;
   }

   public String getPrecontractStype() {
      return this.precontractStype;
   }

   public String getStageCode() {
      return this.stageCode;
   }

   public String getPrecontractStatus() {
      return this.precontractStatus;
   }

   public String getDelReason() {
      return this.delReason;
   }

   public String getFailureReason() {
      return this.failureReason;
   }

   public String getNarrative() {
      return this.narrative;
   }

   public String getDelUserId() {
      return this.delUserId;
   }

   public String getDelAuthUserId() {
      return this.delAuthUserId;
   }

   public String getUserId() {
      return this.userId;
   }

   public String getAuthUserId() {
      return this.authUserId;
   }

   public List<Core1400100222Out.PrecontractArray> getPrecontractArray() {
      return this.precontractArray;
   }

   public void setSeqNo(String seqNo) {
      this.seqNo = seqNo;
   }

   public void setResSeqNo(String resSeqNo) {
      this.resSeqNo = resSeqNo;
   }

   public void setDocumentType(String documentType) {
      this.documentType = documentType;
   }

   public void setDocumentId(String documentId) {
      this.documentId = documentId;
   }

   public void setClientNo(String clientNo) {
      this.clientNo = clientNo;
   }

   public void setChClientName(String chClientName) {
      this.chClientName = chClientName;
   }

   public void setIssCountry(String issCountry) {
      this.issCountry = issCountry;
   }

   public void setClientName(String clientName) {
      this.clientName = clientName;
   }

   public void setProdType(String prodType) {
      this.prodType = prodType;
   }

   public void setBaseAcctNo(String baseAcctNo) {
      this.baseAcctNo = baseAcctNo;
   }

   public void setAcctSeqNo(String acctSeqNo) {
      this.acctSeqNo = acctSeqNo;
   }

   public void setCardNo(String cardNo) {
      this.cardNo = cardNo;
   }

   public void setInternalKey(Long internalKey) {
      this.internalKey = internalKey;
   }

   public void setReasonCode(String reasonCode) {
      this.reasonCode = reasonCode;
   }

   public void setIssueStartDate(String issueStartDate) {
      this.issueStartDate = issueStartDate;
   }

   public void setIssueEndDate(String issueEndDate) {
      this.issueEndDate = issueEndDate;
   }

   public void setDeleteDate(String deleteDate) {
      this.deleteDate = deleteDate;
   }

   public void setOpenDate(String openDate) {
      this.openDate = openDate;
   }

   public void setPrecontractDate(String precontractDate) {
      this.precontractDate = precontractDate;
   }

   public void setPrecontractOpenDate(String precontractOpenDate) {
      this.precontractOpenDate = precontractOpenDate;
   }

   public void setIssueBranch(String issueBranch) {
      this.issueBranch = issueBranch;
   }

   public void setPrecontractBranch(String precontractBranch) {
      this.precontractBranch = precontractBranch;
   }

   public void setPrecontractNo(String precontractNo) {
      this.precontractNo = precontractNo;
   }

   public void setReference(String reference) {
      this.reference = reference;
   }

   public void setIssueAmt(BigDecimal issueAmt) {
      this.issueAmt = issueAmt;
   }

   public void setPrecontractCcy(String precontractCcy) {
      this.precontractCcy = precontractCcy;
   }

   public void setPrecontractAmt(BigDecimal precontractAmt) {
      this.precontractAmt = precontractAmt;
   }

   public void setCcy(String ccy) {
      this.ccy = ccy;
   }

   public void setActualRate(BigDecimal actualRate) {
      this.actualRate = actualRate;
   }

   public void setFloatRate(BigDecimal floatRate) {
      this.floatRate = floatRate;
   }

   public void setRealRate(BigDecimal realRate) {
      this.realRate = realRate;
   }

   public void setIntType(String intType) {
      this.intType = intType;
   }

   public void setSourceType(String sourceType) {
      this.sourceType = sourceType;
   }

   public void setPrecontractStype(String precontractStype) {
      this.precontractStype = precontractStype;
   }

   public void setStageCode(String stageCode) {
      this.stageCode = stageCode;
   }

   public void setPrecontractStatus(String precontractStatus) {
      this.precontractStatus = precontractStatus;
   }

   public void setDelReason(String delReason) {
      this.delReason = delReason;
   }

   public void setFailureReason(String failureReason) {
      this.failureReason = failureReason;
   }

   public void setNarrative(String narrative) {
      this.narrative = narrative;
   }

   public void setDelUserId(String delUserId) {
      this.delUserId = delUserId;
   }

   public void setDelAuthUserId(String delAuthUserId) {
      this.delAuthUserId = delAuthUserId;
   }

   public void setUserId(String userId) {
      this.userId = userId;
   }

   public void setAuthUserId(String authUserId) {
      this.authUserId = authUserId;
   }

   public void setPrecontractArray(List<Core1400100222Out.PrecontractArray> precontractArray) {
      this.precontractArray = precontractArray;
   }

   public String toString() {
      return "Core1400100222Out(seqNo=" + this.getSeqNo() + ", resSeqNo=" + this.getResSeqNo() + ", documentType=" + this.getDocumentType() + ", documentId=" + this.getDocumentId() + ", clientNo=" + this.getClientNo() + ", chClientName=" + this.getChClientName() + ", issCountry=" + this.getIssCountry() + ", clientName=" + this.getClientName() + ", prodType=" + this.getProdType() + ", baseAcctNo=" + this.getBaseAcctNo() + ", acctSeqNo=" + this.getAcctSeqNo() + ", cardNo=" + this.getCardNo() + ", internalKey=" + this.getInternalKey() + ", reasonCode=" + this.getReasonCode() + ", issueStartDate=" + this.getIssueStartDate() + ", issueEndDate=" + this.getIssueEndDate() + ", deleteDate=" + this.getDeleteDate() + ", openDate=" + this.getOpenDate() + ", precontractDate=" + this.getPrecontractDate() + ", precontractOpenDate=" + this.getPrecontractOpenDate() + ", issueBranch=" + this.getIssueBranch() + ", precontractBranch=" + this.getPrecontractBranch() + ", precontractNo=" + this.getPrecontractNo() + ", reference=" + this.getReference() + ", issueAmt=" + this.getIssueAmt() + ", precontractCcy=" + this.getPrecontractCcy() + ", precontractAmt=" + this.getPrecontractAmt() + ", ccy=" + this.getCcy() + ", actualRate=" + this.getActualRate() + ", floatRate=" + this.getFloatRate() + ", realRate=" + this.getRealRate() + ", intType=" + this.getIntType() + ", sourceType=" + this.getSourceType() + ", precontractStype=" + this.getPrecontractStype() + ", stageCode=" + this.getStageCode() + ", precontractStatus=" + this.getPrecontractStatus() + ", delReason=" + this.getDelReason() + ", failureReason=" + this.getFailureReason() + ", narrative=" + this.getNarrative() + ", delUserId=" + this.getDelUserId() + ", delAuthUserId=" + this.getDelAuthUserId() + ", userId=" + this.getUserId() + ", authUserId=" + this.getAuthUserId() + ", precontractArray=" + this.getPrecontractArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100222Out)) {
         return false;
      } else {
         Core1400100222Out other = (Core1400100222Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            label541: {
               Object this$seqNo = this.getSeqNo();
               Object other$seqNo = other.getSeqNo();
               if (this$seqNo == null) {
                  if (other$seqNo == null) {
                     break label541;
                  }
               } else if (this$seqNo.equals(other$seqNo)) {
                  break label541;
               }

               return false;
            }

            label534: {
               Object this$resSeqNo = this.getResSeqNo();
               Object other$resSeqNo = other.getResSeqNo();
               if (this$resSeqNo == null) {
                  if (other$resSeqNo == null) {
                     break label534;
                  }
               } else if (this$resSeqNo.equals(other$resSeqNo)) {
                  break label534;
               }

               return false;
            }

            Object this$documentType = this.getDocumentType();
            Object other$documentType = other.getDocumentType();
            if (this$documentType == null) {
               if (other$documentType != null) {
                  return false;
               }
            } else if (!this$documentType.equals(other$documentType)) {
               return false;
            }

            label520: {
               Object this$documentId = this.getDocumentId();
               Object other$documentId = other.getDocumentId();
               if (this$documentId == null) {
                  if (other$documentId == null) {
                     break label520;
                  }
               } else if (this$documentId.equals(other$documentId)) {
                  break label520;
               }

               return false;
            }

            Object this$clientNo = this.getClientNo();
            Object other$clientNo = other.getClientNo();
            if (this$clientNo == null) {
               if (other$clientNo != null) {
                  return false;
               }
            } else if (!this$clientNo.equals(other$clientNo)) {
               return false;
            }

            label506: {
               Object this$chClientName = this.getChClientName();
               Object other$chClientName = other.getChClientName();
               if (this$chClientName == null) {
                  if (other$chClientName == null) {
                     break label506;
                  }
               } else if (this$chClientName.equals(other$chClientName)) {
                  break label506;
               }

               return false;
            }

            Object this$issCountry = this.getIssCountry();
            Object other$issCountry = other.getIssCountry();
            if (this$issCountry == null) {
               if (other$issCountry != null) {
                  return false;
               }
            } else if (!this$issCountry.equals(other$issCountry)) {
               return false;
            }

            Object this$clientName = this.getClientName();
            Object other$clientName = other.getClientName();
            if (this$clientName == null) {
               if (other$clientName != null) {
                  return false;
               }
            } else if (!this$clientName.equals(other$clientName)) {
               return false;
            }

            Object this$prodType = this.getProdType();
            Object other$prodType = other.getProdType();
            if (this$prodType == null) {
               if (other$prodType != null) {
                  return false;
               }
            } else if (!this$prodType.equals(other$prodType)) {
               return false;
            }

            label478: {
               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo == null) {
                     break label478;
                  }
               } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                  break label478;
               }

               return false;
            }

            label471: {
               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo == null) {
                     break label471;
                  }
               } else if (this$acctSeqNo.equals(other$acctSeqNo)) {
                  break label471;
               }

               return false;
            }

            Object this$cardNo = this.getCardNo();
            Object other$cardNo = other.getCardNo();
            if (this$cardNo == null) {
               if (other$cardNo != null) {
                  return false;
               }
            } else if (!this$cardNo.equals(other$cardNo)) {
               return false;
            }

            label457: {
               Object this$internalKey = this.getInternalKey();
               Object other$internalKey = other.getInternalKey();
               if (this$internalKey == null) {
                  if (other$internalKey == null) {
                     break label457;
                  }
               } else if (this$internalKey.equals(other$internalKey)) {
                  break label457;
               }

               return false;
            }

            label450: {
               Object this$reasonCode = this.getReasonCode();
               Object other$reasonCode = other.getReasonCode();
               if (this$reasonCode == null) {
                  if (other$reasonCode == null) {
                     break label450;
                  }
               } else if (this$reasonCode.equals(other$reasonCode)) {
                  break label450;
               }

               return false;
            }

            Object this$issueStartDate = this.getIssueStartDate();
            Object other$issueStartDate = other.getIssueStartDate();
            if (this$issueStartDate == null) {
               if (other$issueStartDate != null) {
                  return false;
               }
            } else if (!this$issueStartDate.equals(other$issueStartDate)) {
               return false;
            }

            Object this$issueEndDate = this.getIssueEndDate();
            Object other$issueEndDate = other.getIssueEndDate();
            if (this$issueEndDate == null) {
               if (other$issueEndDate != null) {
                  return false;
               }
            } else if (!this$issueEndDate.equals(other$issueEndDate)) {
               return false;
            }

            label429: {
               Object this$deleteDate = this.getDeleteDate();
               Object other$deleteDate = other.getDeleteDate();
               if (this$deleteDate == null) {
                  if (other$deleteDate == null) {
                     break label429;
                  }
               } else if (this$deleteDate.equals(other$deleteDate)) {
                  break label429;
               }

               return false;
            }

            label422: {
               Object this$openDate = this.getOpenDate();
               Object other$openDate = other.getOpenDate();
               if (this$openDate == null) {
                  if (other$openDate == null) {
                     break label422;
                  }
               } else if (this$openDate.equals(other$openDate)) {
                  break label422;
               }

               return false;
            }

            Object this$precontractDate = this.getPrecontractDate();
            Object other$precontractDate = other.getPrecontractDate();
            if (this$precontractDate == null) {
               if (other$precontractDate != null) {
                  return false;
               }
            } else if (!this$precontractDate.equals(other$precontractDate)) {
               return false;
            }

            label408: {
               Object this$precontractOpenDate = this.getPrecontractOpenDate();
               Object other$precontractOpenDate = other.getPrecontractOpenDate();
               if (this$precontractOpenDate == null) {
                  if (other$precontractOpenDate == null) {
                     break label408;
                  }
               } else if (this$precontractOpenDate.equals(other$precontractOpenDate)) {
                  break label408;
               }

               return false;
            }

            Object this$issueBranch = this.getIssueBranch();
            Object other$issueBranch = other.getIssueBranch();
            if (this$issueBranch == null) {
               if (other$issueBranch != null) {
                  return false;
               }
            } else if (!this$issueBranch.equals(other$issueBranch)) {
               return false;
            }

            label394: {
               Object this$precontractBranch = this.getPrecontractBranch();
               Object other$precontractBranch = other.getPrecontractBranch();
               if (this$precontractBranch == null) {
                  if (other$precontractBranch == null) {
                     break label394;
                  }
               } else if (this$precontractBranch.equals(other$precontractBranch)) {
                  break label394;
               }

               return false;
            }

            Object this$precontractNo = this.getPrecontractNo();
            Object other$precontractNo = other.getPrecontractNo();
            if (this$precontractNo == null) {
               if (other$precontractNo != null) {
                  return false;
               }
            } else if (!this$precontractNo.equals(other$precontractNo)) {
               return false;
            }

            Object this$reference = this.getReference();
            Object other$reference = other.getReference();
            if (this$reference == null) {
               if (other$reference != null) {
                  return false;
               }
            } else if (!this$reference.equals(other$reference)) {
               return false;
            }

            Object this$issueAmt = this.getIssueAmt();
            Object other$issueAmt = other.getIssueAmt();
            if (this$issueAmt == null) {
               if (other$issueAmt != null) {
                  return false;
               }
            } else if (!this$issueAmt.equals(other$issueAmt)) {
               return false;
            }

            label366: {
               Object this$precontractCcy = this.getPrecontractCcy();
               Object other$precontractCcy = other.getPrecontractCcy();
               if (this$precontractCcy == null) {
                  if (other$precontractCcy == null) {
                     break label366;
                  }
               } else if (this$precontractCcy.equals(other$precontractCcy)) {
                  break label366;
               }

               return false;
            }

            label359: {
               Object this$precontractAmt = this.getPrecontractAmt();
               Object other$precontractAmt = other.getPrecontractAmt();
               if (this$precontractAmt == null) {
                  if (other$precontractAmt == null) {
                     break label359;
                  }
               } else if (this$precontractAmt.equals(other$precontractAmt)) {
                  break label359;
               }

               return false;
            }

            Object this$ccy = this.getCcy();
            Object other$ccy = other.getCcy();
            if (this$ccy == null) {
               if (other$ccy != null) {
                  return false;
               }
            } else if (!this$ccy.equals(other$ccy)) {
               return false;
            }

            label345: {
               Object this$actualRate = this.getActualRate();
               Object other$actualRate = other.getActualRate();
               if (this$actualRate == null) {
                  if (other$actualRate == null) {
                     break label345;
                  }
               } else if (this$actualRate.equals(other$actualRate)) {
                  break label345;
               }

               return false;
            }

            label338: {
               Object this$floatRate = this.getFloatRate();
               Object other$floatRate = other.getFloatRate();
               if (this$floatRate == null) {
                  if (other$floatRate == null) {
                     break label338;
                  }
               } else if (this$floatRate.equals(other$floatRate)) {
                  break label338;
               }

               return false;
            }

            Object this$realRate = this.getRealRate();
            Object other$realRate = other.getRealRate();
            if (this$realRate == null) {
               if (other$realRate != null) {
                  return false;
               }
            } else if (!this$realRate.equals(other$realRate)) {
               return false;
            }

            Object this$intType = this.getIntType();
            Object other$intType = other.getIntType();
            if (this$intType == null) {
               if (other$intType != null) {
                  return false;
               }
            } else if (!this$intType.equals(other$intType)) {
               return false;
            }

            label317: {
               Object this$sourceType = this.getSourceType();
               Object other$sourceType = other.getSourceType();
               if (this$sourceType == null) {
                  if (other$sourceType == null) {
                     break label317;
                  }
               } else if (this$sourceType.equals(other$sourceType)) {
                  break label317;
               }

               return false;
            }

            label310: {
               Object this$precontractStype = this.getPrecontractStype();
               Object other$precontractStype = other.getPrecontractStype();
               if (this$precontractStype == null) {
                  if (other$precontractStype == null) {
                     break label310;
                  }
               } else if (this$precontractStype.equals(other$precontractStype)) {
                  break label310;
               }

               return false;
            }

            Object this$stageCode = this.getStageCode();
            Object other$stageCode = other.getStageCode();
            if (this$stageCode == null) {
               if (other$stageCode != null) {
                  return false;
               }
            } else if (!this$stageCode.equals(other$stageCode)) {
               return false;
            }

            label296: {
               Object this$precontractStatus = this.getPrecontractStatus();
               Object other$precontractStatus = other.getPrecontractStatus();
               if (this$precontractStatus == null) {
                  if (other$precontractStatus == null) {
                     break label296;
                  }
               } else if (this$precontractStatus.equals(other$precontractStatus)) {
                  break label296;
               }

               return false;
            }

            Object this$delReason = this.getDelReason();
            Object other$delReason = other.getDelReason();
            if (this$delReason == null) {
               if (other$delReason != null) {
                  return false;
               }
            } else if (!this$delReason.equals(other$delReason)) {
               return false;
            }

            label282: {
               Object this$failureReason = this.getFailureReason();
               Object other$failureReason = other.getFailureReason();
               if (this$failureReason == null) {
                  if (other$failureReason == null) {
                     break label282;
                  }
               } else if (this$failureReason.equals(other$failureReason)) {
                  break label282;
               }

               return false;
            }

            Object this$narrative = this.getNarrative();
            Object other$narrative = other.getNarrative();
            if (this$narrative == null) {
               if (other$narrative != null) {
                  return false;
               }
            } else if (!this$narrative.equals(other$narrative)) {
               return false;
            }

            Object this$delUserId = this.getDelUserId();
            Object other$delUserId = other.getDelUserId();
            if (this$delUserId == null) {
               if (other$delUserId != null) {
                  return false;
               }
            } else if (!this$delUserId.equals(other$delUserId)) {
               return false;
            }

            Object this$delAuthUserId = this.getDelAuthUserId();
            Object other$delAuthUserId = other.getDelAuthUserId();
            if (this$delAuthUserId == null) {
               if (other$delAuthUserId != null) {
                  return false;
               }
            } else if (!this$delAuthUserId.equals(other$delAuthUserId)) {
               return false;
            }

            label254: {
               Object this$userId = this.getUserId();
               Object other$userId = other.getUserId();
               if (this$userId == null) {
                  if (other$userId == null) {
                     break label254;
                  }
               } else if (this$userId.equals(other$userId)) {
                  break label254;
               }

               return false;
            }

            label247: {
               Object this$authUserId = this.getAuthUserId();
               Object other$authUserId = other.getAuthUserId();
               if (this$authUserId == null) {
                  if (other$authUserId == null) {
                     break label247;
                  }
               } else if (this$authUserId.equals(other$authUserId)) {
                  break label247;
               }

               return false;
            }

            Object this$precontractArray = this.getPrecontractArray();
            Object other$precontractArray = other.getPrecontractArray();
            if (this$precontractArray == null) {
               if (other$precontractArray != null) {
                  return false;
               }
            } else if (!this$precontractArray.equals(other$precontractArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100222Out;
   }
   public static class PrecontractArray {
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "预约/认购机构",
         notNull = false,
         length = "50",
         remark = "预约/认购机构",
         maxSize = 50
      )
      private String precontractBranch;
      @V(
         desc = "预约登记日期",
         notNull = false,
         remark = "预约登记日期"
      )
      private String precontractDate;
      @V(
         desc = "预约号",
         notNull = false,
         length = "50",
         remark = "预约编号",
         maxSize = 50
      )
      private String precontractNo;
      @V(
         desc = "期次产品预约币种",
         notNull = false,
         length = "3",
         remark = "专指期次类产品预约币种",
         maxSize = 3
      )
      private String precontractCcy;
      @V(
         desc = "预约金额",
         notNull = false,
         length = "17",
         remark = "预约金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal precontractAmt;
      @V(
         desc = "期次代码",
         notNull = false,
         length = "50",
         remark = "期次代码",
         maxSize = 50
      )
      private String stageCode;
      @V(
         desc = "期次产品预约状态",
         notNull = false,
         length = "1",
         remark = "期次产品预约状态",
         maxSize = 1
      )
      private String precontractStatus;

      public String getProdType() {
         return this.prodType;
      }

      public String getPrecontractBranch() {
         return this.precontractBranch;
      }

      public String getPrecontractDate() {
         return this.precontractDate;
      }

      public String getPrecontractNo() {
         return this.precontractNo;
      }

      public String getPrecontractCcy() {
         return this.precontractCcy;
      }

      public BigDecimal getPrecontractAmt() {
         return this.precontractAmt;
      }

      public String getStageCode() {
         return this.stageCode;
      }

      public String getPrecontractStatus() {
         return this.precontractStatus;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setPrecontractBranch(String precontractBranch) {
         this.precontractBranch = precontractBranch;
      }

      public void setPrecontractDate(String precontractDate) {
         this.precontractDate = precontractDate;
      }

      public void setPrecontractNo(String precontractNo) {
         this.precontractNo = precontractNo;
      }

      public void setPrecontractCcy(String precontractCcy) {
         this.precontractCcy = precontractCcy;
      }

      public void setPrecontractAmt(BigDecimal precontractAmt) {
         this.precontractAmt = precontractAmt;
      }

      public void setStageCode(String stageCode) {
         this.stageCode = stageCode;
      }

      public void setPrecontractStatus(String precontractStatus) {
         this.precontractStatus = precontractStatus;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100222Out.PrecontractArray)) {
            return false;
         } else {
            Core1400100222Out.PrecontractArray other = (Core1400100222Out.PrecontractArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label107: {
                  Object this$prodType = this.getProdType();
                  Object other$prodType = other.getProdType();
                  if (this$prodType == null) {
                     if (other$prodType == null) {
                        break label107;
                     }
                  } else if (this$prodType.equals(other$prodType)) {
                     break label107;
                  }

                  return false;
               }

               Object this$precontractBranch = this.getPrecontractBranch();
               Object other$precontractBranch = other.getPrecontractBranch();
               if (this$precontractBranch == null) {
                  if (other$precontractBranch != null) {
                     return false;
                  }
               } else if (!this$precontractBranch.equals(other$precontractBranch)) {
                  return false;
               }

               Object this$precontractDate = this.getPrecontractDate();
               Object other$precontractDate = other.getPrecontractDate();
               if (this$precontractDate == null) {
                  if (other$precontractDate != null) {
                     return false;
                  }
               } else if (!this$precontractDate.equals(other$precontractDate)) {
                  return false;
               }

               label86: {
                  Object this$precontractNo = this.getPrecontractNo();
                  Object other$precontractNo = other.getPrecontractNo();
                  if (this$precontractNo == null) {
                     if (other$precontractNo == null) {
                        break label86;
                     }
                  } else if (this$precontractNo.equals(other$precontractNo)) {
                     break label86;
                  }

                  return false;
               }

               label79: {
                  Object this$precontractCcy = this.getPrecontractCcy();
                  Object other$precontractCcy = other.getPrecontractCcy();
                  if (this$precontractCcy == null) {
                     if (other$precontractCcy == null) {
                        break label79;
                     }
                  } else if (this$precontractCcy.equals(other$precontractCcy)) {
                     break label79;
                  }

                  return false;
               }

               label72: {
                  Object this$precontractAmt = this.getPrecontractAmt();
                  Object other$precontractAmt = other.getPrecontractAmt();
                  if (this$precontractAmt == null) {
                     if (other$precontractAmt == null) {
                        break label72;
                     }
                  } else if (this$precontractAmt.equals(other$precontractAmt)) {
                     break label72;
                  }

                  return false;
               }

               Object this$stageCode = this.getStageCode();
               Object other$stageCode = other.getStageCode();
               if (this$stageCode == null) {
                  if (other$stageCode != null) {
                     return false;
                  }
               } else if (!this$stageCode.equals(other$stageCode)) {
                  return false;
               }

               Object this$precontractStatus = this.getPrecontractStatus();
               Object other$precontractStatus = other.getPrecontractStatus();
               if (this$precontractStatus == null) {
                  if (other$precontractStatus != null) {
                     return false;
                  }
               } else if (!this$precontractStatus.equals(other$precontractStatus)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100222Out.PrecontractArray;
      }
      public String toString() {
         return "Core1400100222Out.PrecontractArray(prodType=" + this.getProdType() + ", precontractBranch=" + this.getPrecontractBranch() + ", precontractDate=" + this.getPrecontractDate() + ", precontractNo=" + this.getPrecontractNo() + ", precontractCcy=" + this.getPrecontractCcy() + ", precontractAmt=" + this.getPrecontractAmt() + ", stageCode=" + this.getStageCode() + ", precontractStatus=" + this.getPrecontractStatus() + ")";
      }
   }
}
