package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000242In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000242Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000242 {
   String URL = "/rb/inq/st/precontract";

   
   @ApiRemark("标准优化")
   @ApiDesc("用于结构性存款申购/认购查询")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0242"
   )
   @BusinessCategory("1400-查询")
   @FunctionCategory("RB11-结构性存款")
   @ConsumeSys("EOS/TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core14000242Out runService(Core14000242In var1);
}
