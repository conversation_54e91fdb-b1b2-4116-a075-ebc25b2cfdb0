package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400100127In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400100127In.Body body;

   public Core1400100127In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400100127In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400100127In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100127In)) {
         return false;
      } else {
         Core1400100127In other = (Core1400100127In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100127In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "所属机构号",
         notNull = true,
         length = "50",
         remark = "所属机构号",
         maxSize = 50
      )
      private String branch;
      @V(
         desc = "客户号",
         notNull = true,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户类型",
         notNull = false,
         length = "1",
         inDesc = "A-AIO账户,C-结算账户,D-垫款,E-委托贷款,L-转让贷款,M-普通贷款,S-储蓄账户,T-定期账户,U-贴现贷款,Y-银团贷款,Z-资产证券化",
         remark = "账户类型",
         maxSize = 1
      )
      private String acctType;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;

      public String getBranch() {
         return this.branch;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctType() {
         return this.acctType;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public void setBranch(String branch) {
         this.branch = branch;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctType(String acctType) {
         this.acctType = acctType;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100127In.Body)) {
            return false;
         } else {
            Core1400100127In.Body other = (Core1400100127In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label71: {
                  Object this$branch = this.getBranch();
                  Object other$branch = other.getBranch();
                  if (this$branch == null) {
                     if (other$branch == null) {
                        break label71;
                     }
                  } else if (this$branch.equals(other$branch)) {
                     break label71;
                  }

                  return false;
               }

               Object this$clientNo = this.getClientNo();
               Object other$clientNo = other.getClientNo();
               if (this$clientNo == null) {
                  if (other$clientNo != null) {
                     return false;
                  }
               } else if (!this$clientNo.equals(other$clientNo)) {
                  return false;
               }

               label57: {
                  Object this$prodType = this.getProdType();
                  Object other$prodType = other.getProdType();
                  if (this$prodType == null) {
                     if (other$prodType == null) {
                        break label57;
                     }
                  } else if (this$prodType.equals(other$prodType)) {
                     break label57;
                  }

                  return false;
               }

               Object this$acctType = this.getAcctType();
               Object other$acctType = other.getAcctType();
               if (this$acctType == null) {
                  if (other$acctType != null) {
                     return false;
                  }
               } else if (!this$acctType.equals(other$acctType)) {
                  return false;
               }

               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo == null) {
                     return true;
                  }
               } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                  return true;
               }

               return false;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100127In.Body;
      }
      public String toString() {
         return "Core1400100127In.Body(branch=" + this.getBranch() + ", clientNo=" + this.getClientNo() + ", prodType=" + this.getProdType() + ", acctType=" + this.getAcctType() + ", baseAcctNo=" + this.getBaseAcctNo() + ")";
      }
   }
}
