package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400033401Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400033401Out.PnBillTranArray> pnBillTranArray;

   public List<Core1400033401Out.PnBillTranArray> getPnBillTranArray() {
      return this.pnBillTranArray;
   }

   public void setPnBillTranArray(List<Core1400033401Out.PnBillTranArray> pnBillTranArray) {
      this.pnBillTranArray = pnBillTranArray;
   }

   public String toString() {
      return "Core1400033401Out(pnBillTranArray=" + this.getPnBillTranArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400033401Out)) {
         return false;
      } else {
         Core1400033401Out other = (Core1400033401Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$pnBillTranArray = this.getPnBillTranArray();
            Object other$pnBillTranArray = other.getPnBillTranArray();
            if (this$pnBillTranArray == null) {
               if (other$pnBillTranArray != null) {
                  return false;
               }
            } else if (!this$pnBillTranArray.equals(other$pnBillTranArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400033401Out;
   }
   public static class PnBillTranArray {
      @V(
         desc = "本票申请书日期",
         notNull = false,
         remark = "本票申请书日期"
      )
      private String billApplyDate;
      @V(
         desc = "票据登记日期",
         notNull = false,
         remark = "票据登记日期"
      )
      private String billSignDate;
      @V(
         desc = "兑付日期",
         notNull = false,
         remark = "兑付日期"
      )
      private String paymentDate;
      @V(
         desc = "票据签发行行号",
         notNull = false,
         length = "20",
         remark = "签发行行号",
         maxSize = 20
      )
      private String billSignBank;
      @V(
         desc = "签发行行名",
         notNull = false,
         length = "50",
         remark = "签发行行名",
         maxSize = 50
      )
      private String issueBankName;
      @V(
         desc = "票据签发机构",
         notNull = false,
         length = "50",
         remark = "签发机构",
         maxSize = 50
      )
      private String billSignBranch;
      @V(
         desc = "兑付行行号",
         notNull = false,
         length = "20",
         remark = "兑付行行号",
         maxSize = 20
      )
      private String paymentBankNo;
      @V(
         desc = "凭证类型",
         notNull = false,
         length = "10",
         remark = "凭证类型",
         maxSize = 10
      )
      private String docType;
      @V(
         desc = "本票申请书前缀",
         notNull = false,
         length = "10",
         remark = "本票申请书前缀",
         maxSize = 10
      )
      private String billApplyPrefix;
      @V(
         desc = "业务流水号",
         notNull = false,
         length = "50",
         remark = "支付流水号",
         maxSize = 50
      )
      private String serialNo;
      @V(
         desc = "出票金额",
         notNull = false,
         length = "17",
         remark = "出票金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal billTranAmt;
      @V(
         desc = "实际收取金额",
         notNull = false,
         length = "17",
         remark = "实际收取金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal feeRealAmt;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "付款人证件类型",
         notNull = false,
         length = "3",
         remark = "付款人证件类型",
         maxSize = 3
      )
      private String payerDocumentType;
      @V(
         desc = "票据类型",
         notNull = false,
         length = "5",
         in = "P,E",
         remark = "票据类型",
         maxSize = 5
      )
      private String billType;
      @V(
         desc = "票据介质类型",
         notNull = false,
         length = "3",
         in = "P,E",
         remark = "票据介质类型",
         maxSize = 3
      )
      private String billMediumType;
      @V(
         desc = "本票申请书类型",
         notNull = false,
         length = "10",
         remark = "本票申请书类型",
         maxSize = 10
      )
      private String billApplyType;
      @V(
         desc = "现转标识",
         notNull = false,
         length = "1",
         in = "0,1",
         remark = "现转标识",
         maxSize = 1
      )
      private String tranferCashFlag;
      @V(
         desc = "费用收取方式",
         notNull = false,
         length = "1",
         in = "T,C,F",
         remark = "费用收取方式",
         maxSize = 1
      )
      private String feeChargeType;
      @V(
         desc = "付款人账户产品类型",
         notNull = false,
         length = "20",
         remark = "付款人账户产品类型",
         maxSize = 20
      )
      private String payerProdType;
      @V(
         desc = "收款人账户产品类型",
         notNull = false,
         length = "20",
         remark = "收款人账户产品类型",
         maxSize = 20
      )
      private String payeeProdType;
      @V(
         desc = "付款人账号",
         notNull = false,
         length = "50",
         remark = "付款人账号",
         maxSize = 50
      )
      private String payerBaseAcctNo;
      @V(
         desc = "收款人账户",
         notNull = false,
         length = "50",
         remark = "收款人账户",
         maxSize = 50
      )
      private String payeeAcctNo;
      @V(
         desc = "缴存币种",
         notNull = false,
         length = "3",
         remark = "缴存币种",
         maxSize = 3
      )
      private String payCcy;
      @V(
         desc = "收款人账户币种",
         notNull = false,
         length = "3",
         remark = "收款人账户币种",
         maxSize = 3
      )
      private String payeeAcctCcy;
      @V(
         desc = "付款人账户序号",
         notNull = false,
         length = "5",
         remark = "付款人账户序号",
         maxSize = 5
      )
      private String payerAcctSeqNo;
      @V(
         desc = "收款人账户序列号",
         notNull = false,
         length = "5",
         remark = "收款人账户序列号",
         maxSize = 5
      )
      private String payeeAcctSeqNo;
      @V(
         desc = "收款人名称",
         notNull = false,
         length = "200",
         remark = "收款人名称",
         maxSize = 200
      )
      private String payeeAcctName;
      @V(
         desc = "付款人证件号码",
         notNull = false,
         length = "50",
         remark = "付款人证件号码",
         maxSize = 50
      )
      private String payerDocumentId;
      @V(
         desc = "备注",
         notNull = false,
         length = "200",
         remark = "备注",
         maxSize = 200
      )
      private String remark;
      @V(
         desc = "介质号码",
         notNull = false,
         length = "50",
         remark = "介质号码",
         maxSize = 50
      )
      private String mediumNo;
      @V(
         desc = "本票申请书号码",
         notNull = false,
         length = "50",
         remark = "本票申请书号码",
         maxSize = 50
      )
      private String billApplyNo;
      @V(
         desc = "票据号码",
         notNull = false,
         length = "50",
         remark = "票据号码",
         maxSize = 50
      )
      private String billNo;
      @V(
         desc = "收款人联系电话",
         notNull = false,
         length = "20",
         remark = "收款人联系电话",
         maxSize = 20
      )
      private String payerTele;
      @V(
         desc = "付款人地址",
         notNull = false,
         length = "500",
         remark = "付款人地址",
         maxSize = 500
      )
      private String payerAddr;
      @V(
         desc = "票据密押",
         notNull = false,
         length = "20",
         remark = "票据密押",
         maxSize = 20
      )
      private String billPswd;
      @V(
         desc = "票据状态",
         notNull = false,
         length = "2",
         in = "00,01,02,03,04,05,06",
         remark = "票据状态",
         maxSize = 2
      )
      private String billStatus;
      @V(
         desc = "签发柜员",
         notNull = false,
         length = "30",
         remark = "签发柜员",
         maxSize = 30
      )
      private String billSignUserId;
      @V(
         desc = "票据复核柜员",
         notNull = false,
         length = "30",
         remark = "票据复核柜员",
         maxSize = 30
      )
      private String billApproUserId;
      @V(
         desc = "法人",
         notNull = false,
         length = "20",
         remark = "法人",
         maxSize = 20
      )
      private String company;

      public String getBillApplyDate() {
         return this.billApplyDate;
      }

      public String getBillSignDate() {
         return this.billSignDate;
      }

      public String getPaymentDate() {
         return this.paymentDate;
      }

      public String getBillSignBank() {
         return this.billSignBank;
      }

      public String getIssueBankName() {
         return this.issueBankName;
      }

      public String getBillSignBranch() {
         return this.billSignBranch;
      }

      public String getPaymentBankNo() {
         return this.paymentBankNo;
      }

      public String getDocType() {
         return this.docType;
      }

      public String getBillApplyPrefix() {
         return this.billApplyPrefix;
      }

      public String getSerialNo() {
         return this.serialNo;
      }

      public BigDecimal getBillTranAmt() {
         return this.billTranAmt;
      }

      public BigDecimal getFeeRealAmt() {
         return this.feeRealAmt;
      }

      public String getCcy() {
         return this.ccy;
      }

      public String getPayerDocumentType() {
         return this.payerDocumentType;
      }

      public String getBillType() {
         return this.billType;
      }

      public String getBillMediumType() {
         return this.billMediumType;
      }

      public String getBillApplyType() {
         return this.billApplyType;
      }

      public String getTranferCashFlag() {
         return this.tranferCashFlag;
      }

      public String getFeeChargeType() {
         return this.feeChargeType;
      }

      public String getPayerProdType() {
         return this.payerProdType;
      }

      public String getPayeeProdType() {
         return this.payeeProdType;
      }

      public String getPayerBaseAcctNo() {
         return this.payerBaseAcctNo;
      }

      public String getPayeeAcctNo() {
         return this.payeeAcctNo;
      }

      public String getPayCcy() {
         return this.payCcy;
      }

      public String getPayeeAcctCcy() {
         return this.payeeAcctCcy;
      }

      public String getPayerAcctSeqNo() {
         return this.payerAcctSeqNo;
      }

      public String getPayeeAcctSeqNo() {
         return this.payeeAcctSeqNo;
      }

      public String getPayeeAcctName() {
         return this.payeeAcctName;
      }

      public String getPayerDocumentId() {
         return this.payerDocumentId;
      }

      public String getRemark() {
         return this.remark;
      }

      public String getMediumNo() {
         return this.mediumNo;
      }

      public String getBillApplyNo() {
         return this.billApplyNo;
      }

      public String getBillNo() {
         return this.billNo;
      }

      public String getPayerTele() {
         return this.payerTele;
      }

      public String getPayerAddr() {
         return this.payerAddr;
      }

      public String getBillPswd() {
         return this.billPswd;
      }

      public String getBillStatus() {
         return this.billStatus;
      }

      public String getBillSignUserId() {
         return this.billSignUserId;
      }

      public String getBillApproUserId() {
         return this.billApproUserId;
      }

      public String getCompany() {
         return this.company;
      }

      public void setBillApplyDate(String billApplyDate) {
         this.billApplyDate = billApplyDate;
      }

      public void setBillSignDate(String billSignDate) {
         this.billSignDate = billSignDate;
      }

      public void setPaymentDate(String paymentDate) {
         this.paymentDate = paymentDate;
      }

      public void setBillSignBank(String billSignBank) {
         this.billSignBank = billSignBank;
      }

      public void setIssueBankName(String issueBankName) {
         this.issueBankName = issueBankName;
      }

      public void setBillSignBranch(String billSignBranch) {
         this.billSignBranch = billSignBranch;
      }

      public void setPaymentBankNo(String paymentBankNo) {
         this.paymentBankNo = paymentBankNo;
      }

      public void setDocType(String docType) {
         this.docType = docType;
      }

      public void setBillApplyPrefix(String billApplyPrefix) {
         this.billApplyPrefix = billApplyPrefix;
      }

      public void setSerialNo(String serialNo) {
         this.serialNo = serialNo;
      }

      public void setBillTranAmt(BigDecimal billTranAmt) {
         this.billTranAmt = billTranAmt;
      }

      public void setFeeRealAmt(BigDecimal feeRealAmt) {
         this.feeRealAmt = feeRealAmt;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setPayerDocumentType(String payerDocumentType) {
         this.payerDocumentType = payerDocumentType;
      }

      public void setBillType(String billType) {
         this.billType = billType;
      }

      public void setBillMediumType(String billMediumType) {
         this.billMediumType = billMediumType;
      }

      public void setBillApplyType(String billApplyType) {
         this.billApplyType = billApplyType;
      }

      public void setTranferCashFlag(String tranferCashFlag) {
         this.tranferCashFlag = tranferCashFlag;
      }

      public void setFeeChargeType(String feeChargeType) {
         this.feeChargeType = feeChargeType;
      }

      public void setPayerProdType(String payerProdType) {
         this.payerProdType = payerProdType;
      }

      public void setPayeeProdType(String payeeProdType) {
         this.payeeProdType = payeeProdType;
      }

      public void setPayerBaseAcctNo(String payerBaseAcctNo) {
         this.payerBaseAcctNo = payerBaseAcctNo;
      }

      public void setPayeeAcctNo(String payeeAcctNo) {
         this.payeeAcctNo = payeeAcctNo;
      }

      public void setPayCcy(String payCcy) {
         this.payCcy = payCcy;
      }

      public void setPayeeAcctCcy(String payeeAcctCcy) {
         this.payeeAcctCcy = payeeAcctCcy;
      }

      public void setPayerAcctSeqNo(String payerAcctSeqNo) {
         this.payerAcctSeqNo = payerAcctSeqNo;
      }

      public void setPayeeAcctSeqNo(String payeeAcctSeqNo) {
         this.payeeAcctSeqNo = payeeAcctSeqNo;
      }

      public void setPayeeAcctName(String payeeAcctName) {
         this.payeeAcctName = payeeAcctName;
      }

      public void setPayerDocumentId(String payerDocumentId) {
         this.payerDocumentId = payerDocumentId;
      }

      public void setRemark(String remark) {
         this.remark = remark;
      }

      public void setMediumNo(String mediumNo) {
         this.mediumNo = mediumNo;
      }

      public void setBillApplyNo(String billApplyNo) {
         this.billApplyNo = billApplyNo;
      }

      public void setBillNo(String billNo) {
         this.billNo = billNo;
      }

      public void setPayerTele(String payerTele) {
         this.payerTele = payerTele;
      }

      public void setPayerAddr(String payerAddr) {
         this.payerAddr = payerAddr;
      }

      public void setBillPswd(String billPswd) {
         this.billPswd = billPswd;
      }

      public void setBillStatus(String billStatus) {
         this.billStatus = billStatus;
      }

      public void setBillSignUserId(String billSignUserId) {
         this.billSignUserId = billSignUserId;
      }

      public void setBillApproUserId(String billApproUserId) {
         this.billApproUserId = billApproUserId;
      }

      public void setCompany(String company) {
         this.company = company;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400033401Out.PnBillTranArray)) {
            return false;
         } else {
            Core1400033401Out.PnBillTranArray other = (Core1400033401Out.PnBillTranArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label491: {
                  Object this$billApplyDate = this.getBillApplyDate();
                  Object other$billApplyDate = other.getBillApplyDate();
                  if (this$billApplyDate == null) {
                     if (other$billApplyDate == null) {
                        break label491;
                     }
                  } else if (this$billApplyDate.equals(other$billApplyDate)) {
                     break label491;
                  }

                  return false;
               }

               Object this$billSignDate = this.getBillSignDate();
               Object other$billSignDate = other.getBillSignDate();
               if (this$billSignDate == null) {
                  if (other$billSignDate != null) {
                     return false;
                  }
               } else if (!this$billSignDate.equals(other$billSignDate)) {
                  return false;
               }

               Object this$paymentDate = this.getPaymentDate();
               Object other$paymentDate = other.getPaymentDate();
               if (this$paymentDate == null) {
                  if (other$paymentDate != null) {
                     return false;
                  }
               } else if (!this$paymentDate.equals(other$paymentDate)) {
                  return false;
               }

               label470: {
                  Object this$billSignBank = this.getBillSignBank();
                  Object other$billSignBank = other.getBillSignBank();
                  if (this$billSignBank == null) {
                     if (other$billSignBank == null) {
                        break label470;
                     }
                  } else if (this$billSignBank.equals(other$billSignBank)) {
                     break label470;
                  }

                  return false;
               }

               label463: {
                  Object this$issueBankName = this.getIssueBankName();
                  Object other$issueBankName = other.getIssueBankName();
                  if (this$issueBankName == null) {
                     if (other$issueBankName == null) {
                        break label463;
                     }
                  } else if (this$issueBankName.equals(other$issueBankName)) {
                     break label463;
                  }

                  return false;
               }

               label456: {
                  Object this$billSignBranch = this.getBillSignBranch();
                  Object other$billSignBranch = other.getBillSignBranch();
                  if (this$billSignBranch == null) {
                     if (other$billSignBranch == null) {
                        break label456;
                     }
                  } else if (this$billSignBranch.equals(other$billSignBranch)) {
                     break label456;
                  }

                  return false;
               }

               Object this$paymentBankNo = this.getPaymentBankNo();
               Object other$paymentBankNo = other.getPaymentBankNo();
               if (this$paymentBankNo == null) {
                  if (other$paymentBankNo != null) {
                     return false;
                  }
               } else if (!this$paymentBankNo.equals(other$paymentBankNo)) {
                  return false;
               }

               label442: {
                  Object this$docType = this.getDocType();
                  Object other$docType = other.getDocType();
                  if (this$docType == null) {
                     if (other$docType == null) {
                        break label442;
                     }
                  } else if (this$docType.equals(other$docType)) {
                     break label442;
                  }

                  return false;
               }

               Object this$billApplyPrefix = this.getBillApplyPrefix();
               Object other$billApplyPrefix = other.getBillApplyPrefix();
               if (this$billApplyPrefix == null) {
                  if (other$billApplyPrefix != null) {
                     return false;
                  }
               } else if (!this$billApplyPrefix.equals(other$billApplyPrefix)) {
                  return false;
               }

               label428: {
                  Object this$serialNo = this.getSerialNo();
                  Object other$serialNo = other.getSerialNo();
                  if (this$serialNo == null) {
                     if (other$serialNo == null) {
                        break label428;
                     }
                  } else if (this$serialNo.equals(other$serialNo)) {
                     break label428;
                  }

                  return false;
               }

               Object this$billTranAmt = this.getBillTranAmt();
               Object other$billTranAmt = other.getBillTranAmt();
               if (this$billTranAmt == null) {
                  if (other$billTranAmt != null) {
                     return false;
                  }
               } else if (!this$billTranAmt.equals(other$billTranAmt)) {
                  return false;
               }

               Object this$feeRealAmt = this.getFeeRealAmt();
               Object other$feeRealAmt = other.getFeeRealAmt();
               if (this$feeRealAmt == null) {
                  if (other$feeRealAmt != null) {
                     return false;
                  }
               } else if (!this$feeRealAmt.equals(other$feeRealAmt)) {
                  return false;
               }

               label407: {
                  Object this$ccy = this.getCcy();
                  Object other$ccy = other.getCcy();
                  if (this$ccy == null) {
                     if (other$ccy == null) {
                        break label407;
                     }
                  } else if (this$ccy.equals(other$ccy)) {
                     break label407;
                  }

                  return false;
               }

               label400: {
                  Object this$payerDocumentType = this.getPayerDocumentType();
                  Object other$payerDocumentType = other.getPayerDocumentType();
                  if (this$payerDocumentType == null) {
                     if (other$payerDocumentType == null) {
                        break label400;
                     }
                  } else if (this$payerDocumentType.equals(other$payerDocumentType)) {
                     break label400;
                  }

                  return false;
               }

               Object this$billType = this.getBillType();
               Object other$billType = other.getBillType();
               if (this$billType == null) {
                  if (other$billType != null) {
                     return false;
                  }
               } else if (!this$billType.equals(other$billType)) {
                  return false;
               }

               Object this$billMediumType = this.getBillMediumType();
               Object other$billMediumType = other.getBillMediumType();
               if (this$billMediumType == null) {
                  if (other$billMediumType != null) {
                     return false;
                  }
               } else if (!this$billMediumType.equals(other$billMediumType)) {
                  return false;
               }

               label379: {
                  Object this$billApplyType = this.getBillApplyType();
                  Object other$billApplyType = other.getBillApplyType();
                  if (this$billApplyType == null) {
                     if (other$billApplyType == null) {
                        break label379;
                     }
                  } else if (this$billApplyType.equals(other$billApplyType)) {
                     break label379;
                  }

                  return false;
               }

               Object this$tranferCashFlag = this.getTranferCashFlag();
               Object other$tranferCashFlag = other.getTranferCashFlag();
               if (this$tranferCashFlag == null) {
                  if (other$tranferCashFlag != null) {
                     return false;
                  }
               } else if (!this$tranferCashFlag.equals(other$tranferCashFlag)) {
                  return false;
               }

               Object this$feeChargeType = this.getFeeChargeType();
               Object other$feeChargeType = other.getFeeChargeType();
               if (this$feeChargeType == null) {
                  if (other$feeChargeType != null) {
                     return false;
                  }
               } else if (!this$feeChargeType.equals(other$feeChargeType)) {
                  return false;
               }

               label358: {
                  Object this$payerProdType = this.getPayerProdType();
                  Object other$payerProdType = other.getPayerProdType();
                  if (this$payerProdType == null) {
                     if (other$payerProdType == null) {
                        break label358;
                     }
                  } else if (this$payerProdType.equals(other$payerProdType)) {
                     break label358;
                  }

                  return false;
               }

               label351: {
                  Object this$payeeProdType = this.getPayeeProdType();
                  Object other$payeeProdType = other.getPayeeProdType();
                  if (this$payeeProdType == null) {
                     if (other$payeeProdType == null) {
                        break label351;
                     }
                  } else if (this$payeeProdType.equals(other$payeeProdType)) {
                     break label351;
                  }

                  return false;
               }

               label344: {
                  Object this$payerBaseAcctNo = this.getPayerBaseAcctNo();
                  Object other$payerBaseAcctNo = other.getPayerBaseAcctNo();
                  if (this$payerBaseAcctNo == null) {
                     if (other$payerBaseAcctNo == null) {
                        break label344;
                     }
                  } else if (this$payerBaseAcctNo.equals(other$payerBaseAcctNo)) {
                     break label344;
                  }

                  return false;
               }

               Object this$payeeAcctNo = this.getPayeeAcctNo();
               Object other$payeeAcctNo = other.getPayeeAcctNo();
               if (this$payeeAcctNo == null) {
                  if (other$payeeAcctNo != null) {
                     return false;
                  }
               } else if (!this$payeeAcctNo.equals(other$payeeAcctNo)) {
                  return false;
               }

               label330: {
                  Object this$payCcy = this.getPayCcy();
                  Object other$payCcy = other.getPayCcy();
                  if (this$payCcy == null) {
                     if (other$payCcy == null) {
                        break label330;
                     }
                  } else if (this$payCcy.equals(other$payCcy)) {
                     break label330;
                  }

                  return false;
               }

               Object this$payeeAcctCcy = this.getPayeeAcctCcy();
               Object other$payeeAcctCcy = other.getPayeeAcctCcy();
               if (this$payeeAcctCcy == null) {
                  if (other$payeeAcctCcy != null) {
                     return false;
                  }
               } else if (!this$payeeAcctCcy.equals(other$payeeAcctCcy)) {
                  return false;
               }

               label316: {
                  Object this$payerAcctSeqNo = this.getPayerAcctSeqNo();
                  Object other$payerAcctSeqNo = other.getPayerAcctSeqNo();
                  if (this$payerAcctSeqNo == null) {
                     if (other$payerAcctSeqNo == null) {
                        break label316;
                     }
                  } else if (this$payerAcctSeqNo.equals(other$payerAcctSeqNo)) {
                     break label316;
                  }

                  return false;
               }

               Object this$payeeAcctSeqNo = this.getPayeeAcctSeqNo();
               Object other$payeeAcctSeqNo = other.getPayeeAcctSeqNo();
               if (this$payeeAcctSeqNo == null) {
                  if (other$payeeAcctSeqNo != null) {
                     return false;
                  }
               } else if (!this$payeeAcctSeqNo.equals(other$payeeAcctSeqNo)) {
                  return false;
               }

               Object this$payeeAcctName = this.getPayeeAcctName();
               Object other$payeeAcctName = other.getPayeeAcctName();
               if (this$payeeAcctName == null) {
                  if (other$payeeAcctName != null) {
                     return false;
                  }
               } else if (!this$payeeAcctName.equals(other$payeeAcctName)) {
                  return false;
               }

               label295: {
                  Object this$payerDocumentId = this.getPayerDocumentId();
                  Object other$payerDocumentId = other.getPayerDocumentId();
                  if (this$payerDocumentId == null) {
                     if (other$payerDocumentId == null) {
                        break label295;
                     }
                  } else if (this$payerDocumentId.equals(other$payerDocumentId)) {
                     break label295;
                  }

                  return false;
               }

               label288: {
                  Object this$remark = this.getRemark();
                  Object other$remark = other.getRemark();
                  if (this$remark == null) {
                     if (other$remark == null) {
                        break label288;
                     }
                  } else if (this$remark.equals(other$remark)) {
                     break label288;
                  }

                  return false;
               }

               Object this$mediumNo = this.getMediumNo();
               Object other$mediumNo = other.getMediumNo();
               if (this$mediumNo == null) {
                  if (other$mediumNo != null) {
                     return false;
                  }
               } else if (!this$mediumNo.equals(other$mediumNo)) {
                  return false;
               }

               Object this$billApplyNo = this.getBillApplyNo();
               Object other$billApplyNo = other.getBillApplyNo();
               if (this$billApplyNo == null) {
                  if (other$billApplyNo != null) {
                     return false;
                  }
               } else if (!this$billApplyNo.equals(other$billApplyNo)) {
                  return false;
               }

               label267: {
                  Object this$billNo = this.getBillNo();
                  Object other$billNo = other.getBillNo();
                  if (this$billNo == null) {
                     if (other$billNo == null) {
                        break label267;
                     }
                  } else if (this$billNo.equals(other$billNo)) {
                     break label267;
                  }

                  return false;
               }

               Object this$payerTele = this.getPayerTele();
               Object other$payerTele = other.getPayerTele();
               if (this$payerTele == null) {
                  if (other$payerTele != null) {
                     return false;
                  }
               } else if (!this$payerTele.equals(other$payerTele)) {
                  return false;
               }

               Object this$payerAddr = this.getPayerAddr();
               Object other$payerAddr = other.getPayerAddr();
               if (this$payerAddr == null) {
                  if (other$payerAddr != null) {
                     return false;
                  }
               } else if (!this$payerAddr.equals(other$payerAddr)) {
                  return false;
               }

               label246: {
                  Object this$billPswd = this.getBillPswd();
                  Object other$billPswd = other.getBillPswd();
                  if (this$billPswd == null) {
                     if (other$billPswd == null) {
                        break label246;
                     }
                  } else if (this$billPswd.equals(other$billPswd)) {
                     break label246;
                  }

                  return false;
               }

               label239: {
                  Object this$billStatus = this.getBillStatus();
                  Object other$billStatus = other.getBillStatus();
                  if (this$billStatus == null) {
                     if (other$billStatus == null) {
                        break label239;
                     }
                  } else if (this$billStatus.equals(other$billStatus)) {
                     break label239;
                  }

                  return false;
               }

               label232: {
                  Object this$billSignUserId = this.getBillSignUserId();
                  Object other$billSignUserId = other.getBillSignUserId();
                  if (this$billSignUserId == null) {
                     if (other$billSignUserId == null) {
                        break label232;
                     }
                  } else if (this$billSignUserId.equals(other$billSignUserId)) {
                     break label232;
                  }

                  return false;
               }

               Object this$billApproUserId = this.getBillApproUserId();
               Object other$billApproUserId = other.getBillApproUserId();
               if (this$billApproUserId == null) {
                  if (other$billApproUserId != null) {
                     return false;
                  }
               } else if (!this$billApproUserId.equals(other$billApproUserId)) {
                  return false;
               }

               Object this$company = this.getCompany();
               Object other$company = other.getCompany();
               if (this$company == null) {
                  if (other$company != null) {
                     return false;
                  }
               } else if (!this$company.equals(other$company)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400033401Out.PnBillTranArray;
      }
      public String toString() {
         return "Core1400033401Out.PnBillTranArray(billApplyDate=" + this.getBillApplyDate() + ", billSignDate=" + this.getBillSignDate() + ", paymentDate=" + this.getPaymentDate() + ", billSignBank=" + this.getBillSignBank() + ", issueBankName=" + this.getIssueBankName() + ", billSignBranch=" + this.getBillSignBranch() + ", paymentBankNo=" + this.getPaymentBankNo() + ", docType=" + this.getDocType() + ", billApplyPrefix=" + this.getBillApplyPrefix() + ", serialNo=" + this.getSerialNo() + ", billTranAmt=" + this.getBillTranAmt() + ", feeRealAmt=" + this.getFeeRealAmt() + ", ccy=" + this.getCcy() + ", payerDocumentType=" + this.getPayerDocumentType() + ", billType=" + this.getBillType() + ", billMediumType=" + this.getBillMediumType() + ", billApplyType=" + this.getBillApplyType() + ", tranferCashFlag=" + this.getTranferCashFlag() + ", feeChargeType=" + this.getFeeChargeType() + ", payerProdType=" + this.getPayerProdType() + ", payeeProdType=" + this.getPayeeProdType() + ", payerBaseAcctNo=" + this.getPayerBaseAcctNo() + ", payeeAcctNo=" + this.getPayeeAcctNo() + ", payCcy=" + this.getPayCcy() + ", payeeAcctCcy=" + this.getPayeeAcctCcy() + ", payerAcctSeqNo=" + this.getPayerAcctSeqNo() + ", payeeAcctSeqNo=" + this.getPayeeAcctSeqNo() + ", payeeAcctName=" + this.getPayeeAcctName() + ", payerDocumentId=" + this.getPayerDocumentId() + ", remark=" + this.getRemark() + ", mediumNo=" + this.getMediumNo() + ", billApplyNo=" + this.getBillApplyNo() + ", billNo=" + this.getBillNo() + ", payerTele=" + this.getPayerTele() + ", payerAddr=" + this.getPayerAddr() + ", billPswd=" + this.getBillPswd() + ", billStatus=" + this.getBillStatus() + ", billSignUserId=" + this.getBillSignUserId() + ", billApproUserId=" + this.getBillApproUserId() + ", company=" + this.getCompany() + ")";
      }
   }
}
