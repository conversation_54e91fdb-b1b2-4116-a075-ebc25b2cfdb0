package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@MessageIn
public class Core1200029181In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1200029181In.Body body;

   public Core1200029181In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1200029181In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1200029181In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200029181In)) {
         return false;
      } else {
         Core1200029181In other = (Core1200029181In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200029181In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "签约级别",
         notNull = true,
         length = "2",
         in = "01,02",
         inDesc = "01-客户级,02-账户级",
         remark = "指客户级签约还是账户级签约(但是目前代码中只有账户级签约)",
         maxSize = 2
      )
      private String agreementLevel;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "账号/卡号",
         notNull = true,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号，根据该字段获取账户结构下的默认结算户，对其进行签约",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "签约操作类型",
         notNull = true,
         length = "2",
         in = "01,02,03",
         inDesc = "01-签约,02-解约,03-维护",
         remark = "签约操作类型",
         maxSize = 2
      )
      private String signOperateType;
      @V(
         desc = "协议类型",
         notNull = false,
         length = "10",
         in = "SMS,ES,PAS",
         inDesc = "CLD-存立得,DC-大额存单,DLS-贷利省,HQB-活期宝,JDL-加多利,KDT-卡贷通,KYD-卡易贷,PCP-资金池,WDL-稳得利,XDB-协定宝,XDCK-协定存款产品,XDL-先得利,YBWL-一本万利,YCD-英才贷,YDT-易贷通,YHT-一户通,ZHY-周享赢,ZXY-坐享其盈,ZZB-至尊宝,LOA-贷款,ODF-法人透支协议,FIN-卡理财协议,SMS-短信,PKG-费用套餐,FEE-暂不收费,PCD-周期性强制扣划,ACC-协定存款协议,SWP-账户清扫协议,ID-智能存款协议,SL-金额补足协议,REC-回单签约,ES-电票签约,YD-约定,NTE-活期智能存款,PAS-隐私账户签约,BXD-协定利率（无留存）",
         remark = "签约协议类型，来源于RB_SIGN_TYPE",
         maxSize = 10
      )
      private String agreementType;
      @V(
         desc = "开始日期",
         notNull = false,
         remark = "协议生效日期"
      )
      private String startDate;
      @V(
         desc = "结束日期",
         notNull = false,
         remark = "协议失效日期"
      )
      private String endDate;
      @V(
         desc = "协议编号",
         notNull = false,
         length = "50",
         remark = "签约操作类型是解约/删除时，协议编号必输(除票据解约外)；签约时，若渠道整合平台不上送，则核心默认生成",
         maxSize = 50
      )
      private String agreementId;
      @V(
         desc = "收费日",
         notNull = false,
         length = "2",
         remark = "签约的短信收费日，短信套餐费用预留使用功能",
         maxSize = 2
      )
      private String chargeDay;
      @V(
         desc = "收费频率",
         notNull = false,
         length = "5",
         remark = "签约的短信收费频率，短信套餐费用预留使用功能",
         maxSize = 5
      )
      private String chargePeriodFreq;
      @V(
         desc = "下一收费日期",
         notNull = false,
         remark = "通过频率和收费日，以及当前日期推算的下一收费日期，短信套餐费用预留使用功能"
      )
      private String nextChargeDate;
      @V(
         desc = "费用类型",
         notNull = false,
         length = "20",
         remark = "收费的费用类型，该场景为服务费，短信套餐费用预留使用功能；来源于MB_FEE_TYPE.FEE_TYPE",
         maxSize = 20
      )
      private String feeType;
      @V(
         desc = "费用金额",
         notNull = false,
         length = "17",
         remark = "每次收取短信服务费金额，短信套餐费用预留使用功能",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal feeAmt;
      @V(
         desc = "数组",
         notNull = false,
         remark = "当bussinessId=SMSs时，协议短信信息数组必输，目前只有一条"
      )
      private List<Core1200029181In.Body.SmsArray> smsArray;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "签约业务类型代码",
         notNull = false,
         length = "3",
         inDesc = "SMS-短信协议,EIR-企业网上银行",
         remark = "签约业务类型代码",
         maxSize = 3
      )
      private String businessId;

      public String getAgreementLevel() {
         return this.agreementLevel;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getSignOperateType() {
         return this.signOperateType;
      }

      public String getAgreementType() {
         return this.agreementType;
      }

      public String getStartDate() {
         return this.startDate;
      }

      public String getEndDate() {
         return this.endDate;
      }

      public String getAgreementId() {
         return this.agreementId;
      }

      public String getChargeDay() {
         return this.chargeDay;
      }

      public String getChargePeriodFreq() {
         return this.chargePeriodFreq;
      }

      public String getNextChargeDate() {
         return this.nextChargeDate;
      }

      public String getFeeType() {
         return this.feeType;
      }

      public BigDecimal getFeeAmt() {
         return this.feeAmt;
      }

      public List<Core1200029181In.Body.SmsArray> getSmsArray() {
         return this.smsArray;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getBusinessId() {
         return this.businessId;
      }

      public void setAgreementLevel(String agreementLevel) {
         this.agreementLevel = agreementLevel;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setSignOperateType(String signOperateType) {
         this.signOperateType = signOperateType;
      }

      public void setAgreementType(String agreementType) {
         this.agreementType = agreementType;
      }

      public void setStartDate(String startDate) {
         this.startDate = startDate;
      }

      public void setEndDate(String endDate) {
         this.endDate = endDate;
      }

      public void setAgreementId(String agreementId) {
         this.agreementId = agreementId;
      }

      public void setChargeDay(String chargeDay) {
         this.chargeDay = chargeDay;
      }

      public void setChargePeriodFreq(String chargePeriodFreq) {
         this.chargePeriodFreq = chargePeriodFreq;
      }

      public void setNextChargeDate(String nextChargeDate) {
         this.nextChargeDate = nextChargeDate;
      }

      public void setFeeType(String feeType) {
         this.feeType = feeType;
      }

      public void setFeeAmt(BigDecimal feeAmt) {
         this.feeAmt = feeAmt;
      }

      public void setSmsArray(List<Core1200029181In.Body.SmsArray> smsArray) {
         this.smsArray = smsArray;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setBusinessId(String businessId) {
         this.businessId = businessId;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1200029181In.Body)) {
            return false;
         } else {
            Core1200029181In.Body other = (Core1200029181In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$agreementLevel = this.getAgreementLevel();
               Object other$agreementLevel = other.getAgreementLevel();
               if (this$agreementLevel == null) {
                  if (other$agreementLevel != null) {
                     return false;
                  }
               } else if (!this$agreementLevel.equals(other$agreementLevel)) {
                  return false;
               }

               Object this$clientNo = this.getClientNo();
               Object other$clientNo = other.getClientNo();
               if (this$clientNo == null) {
                  if (other$clientNo != null) {
                     return false;
                  }
               } else if (!this$clientNo.equals(other$clientNo)) {
                  return false;
               }

               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo != null) {
                     return false;
                  }
               } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
                  return false;
               }

               label206: {
                  Object this$signOperateType = this.getSignOperateType();
                  Object other$signOperateType = other.getSignOperateType();
                  if (this$signOperateType == null) {
                     if (other$signOperateType == null) {
                        break label206;
                     }
                  } else if (this$signOperateType.equals(other$signOperateType)) {
                     break label206;
                  }

                  return false;
               }

               label199: {
                  Object this$agreementType = this.getAgreementType();
                  Object other$agreementType = other.getAgreementType();
                  if (this$agreementType == null) {
                     if (other$agreementType == null) {
                        break label199;
                     }
                  } else if (this$agreementType.equals(other$agreementType)) {
                     break label199;
                  }

                  return false;
               }

               Object this$startDate = this.getStartDate();
               Object other$startDate = other.getStartDate();
               if (this$startDate == null) {
                  if (other$startDate != null) {
                     return false;
                  }
               } else if (!this$startDate.equals(other$startDate)) {
                  return false;
               }

               label185: {
                  Object this$endDate = this.getEndDate();
                  Object other$endDate = other.getEndDate();
                  if (this$endDate == null) {
                     if (other$endDate == null) {
                        break label185;
                     }
                  } else if (this$endDate.equals(other$endDate)) {
                     break label185;
                  }

                  return false;
               }

               label178: {
                  Object this$agreementId = this.getAgreementId();
                  Object other$agreementId = other.getAgreementId();
                  if (this$agreementId == null) {
                     if (other$agreementId == null) {
                        break label178;
                     }
                  } else if (this$agreementId.equals(other$agreementId)) {
                     break label178;
                  }

                  return false;
               }

               Object this$chargeDay = this.getChargeDay();
               Object other$chargeDay = other.getChargeDay();
               if (this$chargeDay == null) {
                  if (other$chargeDay != null) {
                     return false;
                  }
               } else if (!this$chargeDay.equals(other$chargeDay)) {
                  return false;
               }

               Object this$chargePeriodFreq = this.getChargePeriodFreq();
               Object other$chargePeriodFreq = other.getChargePeriodFreq();
               if (this$chargePeriodFreq == null) {
                  if (other$chargePeriodFreq != null) {
                     return false;
                  }
               } else if (!this$chargePeriodFreq.equals(other$chargePeriodFreq)) {
                  return false;
               }

               label157: {
                  Object this$nextChargeDate = this.getNextChargeDate();
                  Object other$nextChargeDate = other.getNextChargeDate();
                  if (this$nextChargeDate == null) {
                     if (other$nextChargeDate == null) {
                        break label157;
                     }
                  } else if (this$nextChargeDate.equals(other$nextChargeDate)) {
                     break label157;
                  }

                  return false;
               }

               label150: {
                  Object this$feeType = this.getFeeType();
                  Object other$feeType = other.getFeeType();
                  if (this$feeType == null) {
                     if (other$feeType == null) {
                        break label150;
                     }
                  } else if (this$feeType.equals(other$feeType)) {
                     break label150;
                  }

                  return false;
               }

               Object this$feeAmt = this.getFeeAmt();
               Object other$feeAmt = other.getFeeAmt();
               if (this$feeAmt == null) {
                  if (other$feeAmt != null) {
                     return false;
                  }
               } else if (!this$feeAmt.equals(other$feeAmt)) {
                  return false;
               }

               label136: {
                  Object this$smsArray = this.getSmsArray();
                  Object other$smsArray = other.getSmsArray();
                  if (this$smsArray == null) {
                     if (other$smsArray == null) {
                        break label136;
                     }
                  } else if (this$smsArray.equals(other$smsArray)) {
                     break label136;
                  }

                  return false;
               }

               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo != null) {
                     return false;
                  }
               } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                  return false;
               }

               label122: {
                  Object this$acctCcy = this.getAcctCcy();
                  Object other$acctCcy = other.getAcctCcy();
                  if (this$acctCcy == null) {
                     if (other$acctCcy == null) {
                        break label122;
                     }
                  } else if (this$acctCcy.equals(other$acctCcy)) {
                     break label122;
                  }

                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               Object this$businessId = this.getBusinessId();
               Object other$businessId = other.getBusinessId();
               if (this$businessId == null) {
                  if (other$businessId != null) {
                     return false;
                  }
               } else if (!this$businessId.equals(other$businessId)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1200029181In.Body;
      }
      public String toString() {
         return "Core1200029181In.Body(agreementLevel=" + this.getAgreementLevel() + ", clientNo=" + this.getClientNo() + ", baseAcctNo=" + this.getBaseAcctNo() + ", signOperateType=" + this.getSignOperateType() + ", agreementType=" + this.getAgreementType() + ", startDate=" + this.getStartDate() + ", endDate=" + this.getEndDate() + ", agreementId=" + this.getAgreementId() + ", chargeDay=" + this.getChargeDay() + ", chargePeriodFreq=" + this.getChargePeriodFreq() + ", nextChargeDate=" + this.getNextChargeDate() + ", feeType=" + this.getFeeType() + ", feeAmt=" + this.getFeeAmt() + ", smsArray=" + this.getSmsArray() + ", acctSeqNo=" + this.getAcctSeqNo() + ", acctCcy=" + this.getAcctCcy() + ", prodType=" + this.getProdType() + ", businessId=" + this.getBusinessId() + ")";
      }

      public static class SmsArray {
         @V(
            desc = "移动电话",
            notNull = false,
            length = "50",
            remark = "短信发送的手机号码",
            maxSize = 50
         )
         private String mobilePhone;
         @V(
            desc = "短信发送转入最小现金金额",
            notNull = false,
            length = "17",
            remark = "指触发发送短信场景条件：当通过现金转入账户金额大于等于该值，则触发短信发送开关",
            decimalLength = 2,
            precision = 2
         )
         private BigDecimal cashMinAmtIn;
         @V(
            desc = "短信发送转出最小现金金额",
            notNull = false,
            length = "17",
            remark = "指触发发送短信场景条件：当通过现金转出账户金额大于等于该值，则触发短信发送开关",
            decimalLength = 2,
            precision = 2
         )
         private BigDecimal cashMinAmtOut;
         @V(
            desc = "短信发送转入最小转账金额",
            notNull = false,
            length = "17",
            remark = "指触发发送短信场景条件：当通过转账转入账户金额大于等于该值，则触发短信发送开关",
            decimalLength = 2,
            precision = 2
         )
         private BigDecimal tranMinAmtIn;
         @V(
            desc = "短信发送转出最小转账金额",
            notNull = false,
            length = "17",
            remark = "指触发发送短信场景条件：当通过转账转出账户金额大于等于该值，则触发短信发送开关",
            decimalLength = 2,
            precision = 2
         )
         private BigDecimal tranMinAmtOut;
         @V(
            desc = "职位",
            notNull = false,
            length = "10",
            remark = "账户所属客户职位，登记项",
            maxSize = 10
         )
         private String position;
         @V(
            desc = "名称",
            notNull = false,
            length = "200",
            remark = "账户所属客户姓名，登记项",
            maxSize = 200
         )
         private String name;
         @V(
            desc = "证件类型",
            notNull = false,
            length = "3",
            remark = "账户所属客户证件类型，登记项；来源于CIF_DOCUMENT_TYPE.DOCUMENT_TYPE",
            maxSize = 3
         )
         private String documentType;
         @V(
            desc = "证件号码",
            notNull = false,
            length = "50",
            remark = "账户所属客户证件号码，登记项",
            maxSize = 50
         )
         private String documentId;
         @V(
            desc = "证件失效日期",
            notNull = false,
            remark = "账户所属客户证件失效，登记项"
         )
         private String documentExpiryDate;
         @V(
            desc = "短信开通三位标识符",
            notNull = false,
            length = "3",
            remark = "短信开通三位标识符，第一位短信余额通知，第二位资金转出通知，第三位资金转入通知。如111表示全部开通，000表示全部不开通",
            maxSize = 3
         )
         private String smsOpenFlag;

         public String getMobilePhone() {
            return this.mobilePhone;
         }

         public BigDecimal getCashMinAmtIn() {
            return this.cashMinAmtIn;
         }

         public BigDecimal getCashMinAmtOut() {
            return this.cashMinAmtOut;
         }

         public BigDecimal getTranMinAmtIn() {
            return this.tranMinAmtIn;
         }

         public BigDecimal getTranMinAmtOut() {
            return this.tranMinAmtOut;
         }

         public String getPosition() {
            return this.position;
         }

         public String getName() {
            return this.name;
         }

         public String getDocumentType() {
            return this.documentType;
         }

         public String getDocumentId() {
            return this.documentId;
         }

         public String getDocumentExpiryDate() {
            return this.documentExpiryDate;
         }

         public String getSmsOpenFlag() {
            return this.smsOpenFlag;
         }

         public void setMobilePhone(String mobilePhone) {
            this.mobilePhone = mobilePhone;
         }

         public void setCashMinAmtIn(BigDecimal cashMinAmtIn) {
            this.cashMinAmtIn = cashMinAmtIn;
         }

         public void setCashMinAmtOut(BigDecimal cashMinAmtOut) {
            this.cashMinAmtOut = cashMinAmtOut;
         }

         public void setTranMinAmtIn(BigDecimal tranMinAmtIn) {
            this.tranMinAmtIn = tranMinAmtIn;
         }

         public void setTranMinAmtOut(BigDecimal tranMinAmtOut) {
            this.tranMinAmtOut = tranMinAmtOut;
         }

         public void setPosition(String position) {
            this.position = position;
         }

         public void setName(String name) {
            this.name = name;
         }

         public void setDocumentType(String documentType) {
            this.documentType = documentType;
         }

         public void setDocumentId(String documentId) {
            this.documentId = documentId;
         }

         public void setDocumentExpiryDate(String documentExpiryDate) {
            this.documentExpiryDate = documentExpiryDate;
         }

         public void setSmsOpenFlag(String smsOpenFlag) {
            this.smsOpenFlag = smsOpenFlag;
         }

         public boolean equals(Object o) {
            if (o == this) {
               return true;
            } else if (!(o instanceof Core1200029181In.Body.SmsArray)) {
               return false;
            } else {
               Core1200029181In.Body.SmsArray other = (Core1200029181In.Body.SmsArray)o;
               if (!other.canEqual(this)) {
                  return false;
               } else {
                  label143: {
                     Object this$mobilePhone = this.getMobilePhone();
                     Object other$mobilePhone = other.getMobilePhone();
                     if (this$mobilePhone == null) {
                        if (other$mobilePhone == null) {
                           break label143;
                        }
                     } else if (this$mobilePhone.equals(other$mobilePhone)) {
                        break label143;
                     }

                     return false;
                  }

                  Object this$cashMinAmtIn = this.getCashMinAmtIn();
                  Object other$cashMinAmtIn = other.getCashMinAmtIn();
                  if (this$cashMinAmtIn == null) {
                     if (other$cashMinAmtIn != null) {
                        return false;
                     }
                  } else if (!this$cashMinAmtIn.equals(other$cashMinAmtIn)) {
                     return false;
                  }

                  Object this$cashMinAmtOut = this.getCashMinAmtOut();
                  Object other$cashMinAmtOut = other.getCashMinAmtOut();
                  if (this$cashMinAmtOut == null) {
                     if (other$cashMinAmtOut != null) {
                        return false;
                     }
                  } else if (!this$cashMinAmtOut.equals(other$cashMinAmtOut)) {
                     return false;
                  }

                  label122: {
                     Object this$tranMinAmtIn = this.getTranMinAmtIn();
                     Object other$tranMinAmtIn = other.getTranMinAmtIn();
                     if (this$tranMinAmtIn == null) {
                        if (other$tranMinAmtIn == null) {
                           break label122;
                        }
                     } else if (this$tranMinAmtIn.equals(other$tranMinAmtIn)) {
                        break label122;
                     }

                     return false;
                  }

                  label115: {
                     Object this$tranMinAmtOut = this.getTranMinAmtOut();
                     Object other$tranMinAmtOut = other.getTranMinAmtOut();
                     if (this$tranMinAmtOut == null) {
                        if (other$tranMinAmtOut == null) {
                           break label115;
                        }
                     } else if (this$tranMinAmtOut.equals(other$tranMinAmtOut)) {
                        break label115;
                     }

                     return false;
                  }

                  Object this$position = this.getPosition();
                  Object other$position = other.getPosition();
                  if (this$position == null) {
                     if (other$position != null) {
                        return false;
                     }
                  } else if (!this$position.equals(other$position)) {
                     return false;
                  }

                  Object this$name = this.getName();
                  Object other$name = other.getName();
                  if (this$name == null) {
                     if (other$name != null) {
                        return false;
                     }
                  } else if (!this$name.equals(other$name)) {
                     return false;
                  }

                  label94: {
                     Object this$documentType = this.getDocumentType();
                     Object other$documentType = other.getDocumentType();
                     if (this$documentType == null) {
                        if (other$documentType == null) {
                           break label94;
                        }
                     } else if (this$documentType.equals(other$documentType)) {
                        break label94;
                     }

                     return false;
                  }

                  label87: {
                     Object this$documentId = this.getDocumentId();
                     Object other$documentId = other.getDocumentId();
                     if (this$documentId == null) {
                        if (other$documentId == null) {
                           break label87;
                        }
                     } else if (this$documentId.equals(other$documentId)) {
                        break label87;
                     }

                     return false;
                  }

                  Object this$documentExpiryDate = this.getDocumentExpiryDate();
                  Object other$documentExpiryDate = other.getDocumentExpiryDate();
                  if (this$documentExpiryDate == null) {
                     if (other$documentExpiryDate != null) {
                        return false;
                     }
                  } else if (!this$documentExpiryDate.equals(other$documentExpiryDate)) {
                     return false;
                  }

                  Object this$smsOpenFlag = this.getSmsOpenFlag();
                  Object other$smsOpenFlag = other.getSmsOpenFlag();
                  if (this$smsOpenFlag == null) {
                     if (other$smsOpenFlag != null) {
                        return false;
                     }
                  } else if (!this$smsOpenFlag.equals(other$smsOpenFlag)) {
                     return false;
                  }

                  return true;
               }
            }
         }

         protected boolean canEqual(Object other) {
            return other instanceof Core1200029181In.Body.SmsArray;
         }
         public String toString() {
            return "Core1200029181In.Body.SmsArray(mobilePhone=" + this.getMobilePhone() + ", cashMinAmtIn=" + this.getCashMinAmtIn() + ", cashMinAmtOut=" + this.getCashMinAmtOut() + ", tranMinAmtIn=" + this.getTranMinAmtIn() + ", tranMinAmtOut=" + this.getTranMinAmtOut() + ", position=" + this.getPosition() + ", name=" + this.getName() + ", documentType=" + this.getDocumentType() + ", documentId=" + this.getDocumentId() + ", documentExpiryDate=" + this.getDocumentExpiryDate() + ", smsOpenFlag=" + this.getSmsOpenFlag() + ")";
         }
      }
   }
}
