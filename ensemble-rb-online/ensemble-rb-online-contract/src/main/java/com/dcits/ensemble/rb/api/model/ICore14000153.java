package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000153In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000153Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000153 {
   String URL = "/rb/inq/doss/reg";


   @ApiRemark("标准优化")
   @ApiDesc("根据转不动户的时间区间查询对应的转不久悬和转营业外的账户信息，以及账户状态变动的明细记录")
   @EnsembleElements(
      serviceCode = "Core",
      messageType = "1400",
      messageCode = "0153"
   )
   @FunctionCategory("RB08-特殊业务")
   @ConsumeSys("EOS/PR/TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core14000153Out runService(Core14000153In var1);
}
