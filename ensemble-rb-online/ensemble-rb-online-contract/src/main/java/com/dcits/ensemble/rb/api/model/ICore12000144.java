package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000144In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000144Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12000144 {
   String URL = "/rb/nfin/msa/agreement/operate";


   @ApiRemark("MSA账户定期转账协议操作")
   @ApiDesc("MSA账户定期转账协议操作，主要功能有：签约、维护、解约")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0144"
   )
   Core12000144Out runService(Core12000144In var1);
}
