package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000251In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000251Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000251 {
   String URL = "/rb/inqinq/exchange/discRateQuery";


   @ApiRemark("查询客户下汇率优惠信息")
   @ApiDesc("查询客户下汇率优惠信息")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0251"
   )
   @BusinessCategory("1400-查询")
   @FunctionCategory("RB47-客户账户查询")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core14000251Out runService(Core14000251In var1);
}
