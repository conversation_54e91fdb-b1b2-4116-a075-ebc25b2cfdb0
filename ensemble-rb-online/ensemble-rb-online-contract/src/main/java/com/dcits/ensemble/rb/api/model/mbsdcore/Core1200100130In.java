package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1200100130In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1200100130In.Body body;

   public Core1200100130In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1200100130In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1200100130In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200100130In)) {
         return false;
      } else {
         Core1200100130In other = (Core1200100130In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200100130In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "序号",
         notNull = false,
         length = "50",
         remark = "序号",
         maxSize = 50
      )
      private String seqNo;
      @V(
         desc = "交易参考号",
         notNull = true,
         length = "50",
         remark = "交易参考号",
         maxSize = 50
      )
      private String reference;
      @V(
         desc = "交易序号",
         notNull = false,
         length = "50",
         remark = "交易序号",
         maxSize = 50
      )
      private String tranSeqNo;
      @V(
         desc = "交易日期",
         notNull = true,
         remark = "交易日期"
      )
      private String tranDate;
      @V(
         desc = "真实对方金融机构代码",
         notNull = false,
         length = "20",
         remark = "真实对方金融机构代码",
         maxSize = 20
      )
      private String othRealBankCode;
      @V(
         desc = "真实对方金融机构名称",
         notNull = false,
         length = "50",
         remark = "真实对方金融机构名称",
         maxSize = 50
      )
      private String othRealBankName;
      @V(
         desc = "真实交易对手账户类型",
         notNull = false,
         length = "20",
         remark = "真实交易对手账户类型",
         maxSize = 20
      )
      private String othRealProdType;
      @V(
         desc = "真实交易对手账号",
         notNull = false,
         length = "50",
         remark = "真实交易对手账号",
         maxSize = 50
      )
      private String othRealBaseAcctNo;
      @V(
         desc = "真实对方金融机构行政区划代码",
         notNull = false,
         length = "10",
         remark = "真实对方金融机构行政区划代码",
         maxSize = 10
      )
      private String othRealBranchRegionCode;
      @V(
         desc = "真实交易对手身份证件/证明文件号码",
         notNull = false,
         length = "50",
         remark = "真实交易对手身份证件/证明文件号码",
         maxSize = 50
      )
      private String othRealDocumentId;
      @V(
         desc = "真实交易对手身份证件/证明文件类型",
         notNull = false,
         length = "3",
         remark = "真实交易对手身份证件/证明文件类型",
         maxSize = 3
      )
      private String othRealDocumentType;
      @V(
         desc = "真实交易对手名称",
         notNull = false,
         length = "200",
         remark = "真实交易对手名称",
         maxSize = 200
      )
      private String othRealTranName;
      @V(
         desc = "真实交易发生地",
         notNull = false,
         length = "500",
         remark = "真实交易发生地",
         maxSize = 500
      )
      private String othRealTranAddr;
      @V(
         desc = "交易附言",
         notNull = false,
         length = "2000",
         remark = "交易附言",
         maxSize = 2000
      )
      private String tranNote;
      @V(
         desc = "摘要",
         notNull = false,
         length = "500",
         remark = "开户时的账号用途，销户时的销户原因",
         maxSize = 500
      )
      private String narrative;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getCcy() {
         return this.ccy;
      }

      public String getSeqNo() {
         return this.seqNo;
      }

      public String getReference() {
         return this.reference;
      }

      public String getTranSeqNo() {
         return this.tranSeqNo;
      }

      public String getTranDate() {
         return this.tranDate;
      }

      public String getOthRealBankCode() {
         return this.othRealBankCode;
      }

      public String getOthRealBankName() {
         return this.othRealBankName;
      }

      public String getOthRealProdType() {
         return this.othRealProdType;
      }

      public String getOthRealBaseAcctNo() {
         return this.othRealBaseAcctNo;
      }

      public String getOthRealBranchRegionCode() {
         return this.othRealBranchRegionCode;
      }

      public String getOthRealDocumentId() {
         return this.othRealDocumentId;
      }

      public String getOthRealDocumentType() {
         return this.othRealDocumentType;
      }

      public String getOthRealTranName() {
         return this.othRealTranName;
      }

      public String getOthRealTranAddr() {
         return this.othRealTranAddr;
      }

      public String getTranNote() {
         return this.tranNote;
      }

      public String getNarrative() {
         return this.narrative;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setSeqNo(String seqNo) {
         this.seqNo = seqNo;
      }

      public void setReference(String reference) {
         this.reference = reference;
      }

      public void setTranSeqNo(String tranSeqNo) {
         this.tranSeqNo = tranSeqNo;
      }

      public void setTranDate(String tranDate) {
         this.tranDate = tranDate;
      }

      public void setOthRealBankCode(String othRealBankCode) {
         this.othRealBankCode = othRealBankCode;
      }

      public void setOthRealBankName(String othRealBankName) {
         this.othRealBankName = othRealBankName;
      }

      public void setOthRealProdType(String othRealProdType) {
         this.othRealProdType = othRealProdType;
      }

      public void setOthRealBaseAcctNo(String othRealBaseAcctNo) {
         this.othRealBaseAcctNo = othRealBaseAcctNo;
      }

      public void setOthRealBranchRegionCode(String othRealBranchRegionCode) {
         this.othRealBranchRegionCode = othRealBranchRegionCode;
      }

      public void setOthRealDocumentId(String othRealDocumentId) {
         this.othRealDocumentId = othRealDocumentId;
      }

      public void setOthRealDocumentType(String othRealDocumentType) {
         this.othRealDocumentType = othRealDocumentType;
      }

      public void setOthRealTranName(String othRealTranName) {
         this.othRealTranName = othRealTranName;
      }

      public void setOthRealTranAddr(String othRealTranAddr) {
         this.othRealTranAddr = othRealTranAddr;
      }

      public void setTranNote(String tranNote) {
         this.tranNote = tranNote;
      }

      public void setNarrative(String narrative) {
         this.narrative = narrative;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1200100130In.Body)) {
            return false;
         } else {
            Core1200100130In.Body other = (Core1200100130In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo != null) {
                     return false;
                  }
               } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               Object this$ccy = this.getCcy();
               Object other$ccy = other.getCcy();
               if (this$ccy == null) {
                  if (other$ccy != null) {
                     return false;
                  }
               } else if (!this$ccy.equals(other$ccy)) {
                  return false;
               }

               label206: {
                  Object this$seqNo = this.getSeqNo();
                  Object other$seqNo = other.getSeqNo();
                  if (this$seqNo == null) {
                     if (other$seqNo == null) {
                        break label206;
                     }
                  } else if (this$seqNo.equals(other$seqNo)) {
                     break label206;
                  }

                  return false;
               }

               label199: {
                  Object this$reference = this.getReference();
                  Object other$reference = other.getReference();
                  if (this$reference == null) {
                     if (other$reference == null) {
                        break label199;
                     }
                  } else if (this$reference.equals(other$reference)) {
                     break label199;
                  }

                  return false;
               }

               Object this$tranSeqNo = this.getTranSeqNo();
               Object other$tranSeqNo = other.getTranSeqNo();
               if (this$tranSeqNo == null) {
                  if (other$tranSeqNo != null) {
                     return false;
                  }
               } else if (!this$tranSeqNo.equals(other$tranSeqNo)) {
                  return false;
               }

               label185: {
                  Object this$tranDate = this.getTranDate();
                  Object other$tranDate = other.getTranDate();
                  if (this$tranDate == null) {
                     if (other$tranDate == null) {
                        break label185;
                     }
                  } else if (this$tranDate.equals(other$tranDate)) {
                     break label185;
                  }

                  return false;
               }

               label178: {
                  Object this$othRealBankCode = this.getOthRealBankCode();
                  Object other$othRealBankCode = other.getOthRealBankCode();
                  if (this$othRealBankCode == null) {
                     if (other$othRealBankCode == null) {
                        break label178;
                     }
                  } else if (this$othRealBankCode.equals(other$othRealBankCode)) {
                     break label178;
                  }

                  return false;
               }

               Object this$othRealBankName = this.getOthRealBankName();
               Object other$othRealBankName = other.getOthRealBankName();
               if (this$othRealBankName == null) {
                  if (other$othRealBankName != null) {
                     return false;
                  }
               } else if (!this$othRealBankName.equals(other$othRealBankName)) {
                  return false;
               }

               Object this$othRealProdType = this.getOthRealProdType();
               Object other$othRealProdType = other.getOthRealProdType();
               if (this$othRealProdType == null) {
                  if (other$othRealProdType != null) {
                     return false;
                  }
               } else if (!this$othRealProdType.equals(other$othRealProdType)) {
                  return false;
               }

               label157: {
                  Object this$othRealBaseAcctNo = this.getOthRealBaseAcctNo();
                  Object other$othRealBaseAcctNo = other.getOthRealBaseAcctNo();
                  if (this$othRealBaseAcctNo == null) {
                     if (other$othRealBaseAcctNo == null) {
                        break label157;
                     }
                  } else if (this$othRealBaseAcctNo.equals(other$othRealBaseAcctNo)) {
                     break label157;
                  }

                  return false;
               }

               label150: {
                  Object this$othRealBranchRegionCode = this.getOthRealBranchRegionCode();
                  Object other$othRealBranchRegionCode = other.getOthRealBranchRegionCode();
                  if (this$othRealBranchRegionCode == null) {
                     if (other$othRealBranchRegionCode == null) {
                        break label150;
                     }
                  } else if (this$othRealBranchRegionCode.equals(other$othRealBranchRegionCode)) {
                     break label150;
                  }

                  return false;
               }

               Object this$othRealDocumentId = this.getOthRealDocumentId();
               Object other$othRealDocumentId = other.getOthRealDocumentId();
               if (this$othRealDocumentId == null) {
                  if (other$othRealDocumentId != null) {
                     return false;
                  }
               } else if (!this$othRealDocumentId.equals(other$othRealDocumentId)) {
                  return false;
               }

               label136: {
                  Object this$othRealDocumentType = this.getOthRealDocumentType();
                  Object other$othRealDocumentType = other.getOthRealDocumentType();
                  if (this$othRealDocumentType == null) {
                     if (other$othRealDocumentType == null) {
                        break label136;
                     }
                  } else if (this$othRealDocumentType.equals(other$othRealDocumentType)) {
                     break label136;
                  }

                  return false;
               }

               Object this$othRealTranName = this.getOthRealTranName();
               Object other$othRealTranName = other.getOthRealTranName();
               if (this$othRealTranName == null) {
                  if (other$othRealTranName != null) {
                     return false;
                  }
               } else if (!this$othRealTranName.equals(other$othRealTranName)) {
                  return false;
               }

               label122: {
                  Object this$othRealTranAddr = this.getOthRealTranAddr();
                  Object other$othRealTranAddr = other.getOthRealTranAddr();
                  if (this$othRealTranAddr == null) {
                     if (other$othRealTranAddr == null) {
                        break label122;
                     }
                  } else if (this$othRealTranAddr.equals(other$othRealTranAddr)) {
                     break label122;
                  }

                  return false;
               }

               Object this$tranNote = this.getTranNote();
               Object other$tranNote = other.getTranNote();
               if (this$tranNote == null) {
                  if (other$tranNote != null) {
                     return false;
                  }
               } else if (!this$tranNote.equals(other$tranNote)) {
                  return false;
               }

               Object this$narrative = this.getNarrative();
               Object other$narrative = other.getNarrative();
               if (this$narrative == null) {
                  if (other$narrative != null) {
                     return false;
                  }
               } else if (!this$narrative.equals(other$narrative)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1200100130In.Body;
      }
      public String toString() {
         return "Core1200100130In.Body(baseAcctNo=" + this.getBaseAcctNo() + ", prodType=" + this.getProdType() + ", ccy=" + this.getCcy() + ", seqNo=" + this.getSeqNo() + ", reference=" + this.getReference() + ", tranSeqNo=" + this.getTranSeqNo() + ", tranDate=" + this.getTranDate() + ", othRealBankCode=" + this.getOthRealBankCode() + ", othRealBankName=" + this.getOthRealBankName() + ", othRealProdType=" + this.getOthRealProdType() + ", othRealBaseAcctNo=" + this.getOthRealBaseAcctNo() + ", othRealBranchRegionCode=" + this.getOthRealBranchRegionCode() + ", othRealDocumentId=" + this.getOthRealDocumentId() + ", othRealDocumentType=" + this.getOthRealDocumentType() + ", othRealTranName=" + this.getOthRealTranName() + ", othRealTranAddr=" + this.getOthRealTranAddr() + ", tranNote=" + this.getTranNote() + ", narrative=" + this.getNarrative() + ")";
      }
   }
}
