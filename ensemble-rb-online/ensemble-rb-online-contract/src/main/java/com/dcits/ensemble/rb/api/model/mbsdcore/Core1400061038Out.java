package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400061038Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "客户号",
      notNull = false,
      length = "20",
      remark = "客户号",
      maxSize = 20
   )
   private String clientNo;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400061038Out.ItemArray> itemArray;

   public String getClientNo() {
      return this.clientNo;
   }

   public List<Core1400061038Out.ItemArray> getItemArray() {
      return this.itemArray;
   }

   public void setClientNo(String clientNo) {
      this.clientNo = clientNo;
   }

   public void setItemArray(List<Core1400061038Out.ItemArray> itemArray) {
      this.itemArray = itemArray;
   }

   public String toString() {
      return "Core1400061038Out(clientNo=" + this.getClientNo() + ", itemArray=" + this.getItemArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400061038Out)) {
         return false;
      } else {
         Core1400061038Out other = (Core1400061038Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$clientNo = this.getClientNo();
            Object other$clientNo = other.getClientNo();
            if (this$clientNo == null) {
               if (other$clientNo != null) {
                  return false;
               }
            } else if (!this$clientNo.equals(other$clientNo)) {
               return false;
            }

            Object this$itemArray = this.getItemArray();
            Object other$itemArray = other.getItemArray();
            if (this$itemArray == null) {
               if (other$itemArray != null) {
                  return false;
               }
            } else if (!this$itemArray.equals(other$itemArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400061038Out;
   }
   public static class ItemArray {
      @V(
         desc = "限额编码",
         notNull = false,
         length = "500",
         remark = "限额编码",
         maxSize = 500
      )
      private String limitRef;
      @V(
         desc = "限额类型",
         notNull = false,
         length = "2",
         remark = "限制金额或者限制笔数的类型",
         maxSize = 2
      )
      private String limitType;
      @V(
         desc = "限制说明",
         notNull = false,
         length = "200",
         remark = "限制说明",
         maxSize = 200
      )
      private String limitDesc;
      @V(
         desc = "借贷标识",
         notNull = false,
         length = "1",
         remark = "借贷标识",
         maxSize = 1
      )
      private String crDrMaintInd;
      @V(
         desc = "最大限额",
         notNull = false,
         length = "17",
         remark = "最大限额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal limitMaxAmt;
      @V(
         desc = "限额最小金额",
         notNull = false,
         length = "17",
         remark = "限额最小金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal limitMinAmt;
      @V(
         desc = "累计额度金额",
         notNull = false,
         length = "17",
         remark = "累计额度金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal limitAmt;

      public String getLimitRef() {
         return this.limitRef;
      }

      public String getLimitType() {
         return this.limitType;
      }

      public String getLimitDesc() {
         return this.limitDesc;
      }

      public String getCrDrMaintInd() {
         return this.crDrMaintInd;
      }

      public BigDecimal getLimitMaxAmt() {
         return this.limitMaxAmt;
      }

      public BigDecimal getLimitMinAmt() {
         return this.limitMinAmt;
      }

      public BigDecimal getLimitAmt() {
         return this.limitAmt;
      }

      public void setLimitRef(String limitRef) {
         this.limitRef = limitRef;
      }

      public void setLimitType(String limitType) {
         this.limitType = limitType;
      }

      public void setLimitDesc(String limitDesc) {
         this.limitDesc = limitDesc;
      }

      public void setCrDrMaintInd(String crDrMaintInd) {
         this.crDrMaintInd = crDrMaintInd;
      }

      public void setLimitMaxAmt(BigDecimal limitMaxAmt) {
         this.limitMaxAmt = limitMaxAmt;
      }

      public void setLimitMinAmt(BigDecimal limitMinAmt) {
         this.limitMinAmt = limitMinAmt;
      }

      public void setLimitAmt(BigDecimal limitAmt) {
         this.limitAmt = limitAmt;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400061038Out.ItemArray)) {
            return false;
         } else {
            Core1400061038Out.ItemArray other = (Core1400061038Out.ItemArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label95: {
                  Object this$limitRef = this.getLimitRef();
                  Object other$limitRef = other.getLimitRef();
                  if (this$limitRef == null) {
                     if (other$limitRef == null) {
                        break label95;
                     }
                  } else if (this$limitRef.equals(other$limitRef)) {
                     break label95;
                  }

                  return false;
               }

               Object this$limitType = this.getLimitType();
               Object other$limitType = other.getLimitType();
               if (this$limitType == null) {
                  if (other$limitType != null) {
                     return false;
                  }
               } else if (!this$limitType.equals(other$limitType)) {
                  return false;
               }

               Object this$limitDesc = this.getLimitDesc();
               Object other$limitDesc = other.getLimitDesc();
               if (this$limitDesc == null) {
                  if (other$limitDesc != null) {
                     return false;
                  }
               } else if (!this$limitDesc.equals(other$limitDesc)) {
                  return false;
               }

               label74: {
                  Object this$crDrMaintInd = this.getCrDrMaintInd();
                  Object other$crDrMaintInd = other.getCrDrMaintInd();
                  if (this$crDrMaintInd == null) {
                     if (other$crDrMaintInd == null) {
                        break label74;
                     }
                  } else if (this$crDrMaintInd.equals(other$crDrMaintInd)) {
                     break label74;
                  }

                  return false;
               }

               label67: {
                  Object this$limitMaxAmt = this.getLimitMaxAmt();
                  Object other$limitMaxAmt = other.getLimitMaxAmt();
                  if (this$limitMaxAmt == null) {
                     if (other$limitMaxAmt == null) {
                        break label67;
                     }
                  } else if (this$limitMaxAmt.equals(other$limitMaxAmt)) {
                     break label67;
                  }

                  return false;
               }

               Object this$limitMinAmt = this.getLimitMinAmt();
               Object other$limitMinAmt = other.getLimitMinAmt();
               if (this$limitMinAmt == null) {
                  if (other$limitMinAmt != null) {
                     return false;
                  }
               } else if (!this$limitMinAmt.equals(other$limitMinAmt)) {
                  return false;
               }

               Object this$limitAmt = this.getLimitAmt();
               Object other$limitAmt = other.getLimitAmt();
               if (this$limitAmt == null) {
                  if (other$limitAmt != null) {
                     return false;
                  }
               } else if (!this$limitAmt.equals(other$limitAmt)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400061038Out.ItemArray;
      }
      public String toString() {
         return "Core1400061038Out.ItemArray(limitRef=" + this.getLimitRef() + ", limitType=" + this.getLimitType() + ", limitDesc=" + this.getLimitDesc() + ", crDrMaintInd=" + this.getCrDrMaintInd() + ", limitMaxAmt=" + this.getLimitMaxAmt() + ", limitMinAmt=" + this.getLimitMinAmt() + ", limitAmt=" + this.getLimitAmt() + ")";
      }
   }
}
