package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;

@MessageIn
public class Core1200061076In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1200061076In.Body body;

   public Core1200061076In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1200061076In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1200061076In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200061076In)) {
         return false;
      } else {
         Core1200061076In other = (Core1200061076In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200061076In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "业务编号",
         notNull = false,
         length = "50",
         remark = "业务编号",
         maxSize = 50
      )
      private String busiNo;
      @V(
         desc = "登记日期",
         notNull = false,
         remark = "登记日期"
      )
      private String regTranDate;
      @V(
         desc = "外币币种",
         notNull = false,
         length = "3",
         remark = "外币币种",
         maxSize = 3
      )
      private String foreCcy;
      @V(
         desc = "合作银行报价（原）",
         notNull = false,
         length = "17",
         remark = "合作银行报价（原）",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal teamBankRateOld;
      @V(
         desc = "我行对客户报价（原）",
         notNull = false,
         length = "17",
         remark = "我行对客户报价（原）",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal offerLetterOld;
      @V(
         desc = "外币金额（原）",
         notNull = false,
         length = "17",
         remark = "外币金额（原）",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal foreAmtOld;
      @V(
         desc = "人民币金额（原）",
         notNull = false,
         length = "17",
         remark = "人民币金额（原）",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal cnyAmtOld;
      @V(
         desc = "客户中文名称",
         notNull = false,
         length = "200",
         remark = "客户中文名称",
         maxSize = 200
      )
      private String chClientName;
      @V(
         desc = "客户英文名称",
         notNull = false,
         length = "200",
         remark = "客户英文名称",
         maxSize = 200
      )
      private String enClientName;
      @V(
         desc = "合作银行报价（新）",
         notNull = false,
         length = "17",
         remark = "合作银行报价（新）",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal teamBankRateNew;
      @V(
         desc = "我行对客户报价（新）",
         notNull = false,
         length = "17",
         remark = "我行对客户报价（新）",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal offerLetterNew;
      @V(
         desc = "人民币金额（新）",
         notNull = false,
         length = "17",
         remark = "人民币金额（新）",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal cnyAmtNew;
      @V(
         desc = "损益标识",
         notNull = false,
         length = "1",
         remark = "损益标识",
         maxSize = 1
      )
      private String lossIncomeFlag;
      @V(
         desc = "重估损益金额",
         notNull = false,
         length = "17",
         remark = "重估损益金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal lossIncomeAmt;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "序号",
         notNull = false,
         length = "50",
         remark = "序号",
         maxSize = 50
      )
      private String seqNo;
      @V(
         desc = "外币金额（新）",
         notNull = false,
         length = "17",
         remark = "外币金额（新）",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal foreAmtNew;
      @V(
         desc = "兑换类型",
         notNull = false,
         length = "1",
         inDesc = "B-结汇,S-售汇,E-外币兑换",
         remark = "兑换类型",
         maxSize = 1
      )
      private String exType;

      public String getClientNo() {
         return this.clientNo;
      }

      public String getBusiNo() {
         return this.busiNo;
      }

      public String getRegTranDate() {
         return this.regTranDate;
      }

      public String getForeCcy() {
         return this.foreCcy;
      }

      public BigDecimal getTeamBankRateOld() {
         return this.teamBankRateOld;
      }

      public BigDecimal getOfferLetterOld() {
         return this.offerLetterOld;
      }

      public BigDecimal getForeAmtOld() {
         return this.foreAmtOld;
      }

      public BigDecimal getCnyAmtOld() {
         return this.cnyAmtOld;
      }

      public String getChClientName() {
         return this.chClientName;
      }

      public String getEnClientName() {
         return this.enClientName;
      }

      public BigDecimal getTeamBankRateNew() {
         return this.teamBankRateNew;
      }

      public BigDecimal getOfferLetterNew() {
         return this.offerLetterNew;
      }

      public BigDecimal getCnyAmtNew() {
         return this.cnyAmtNew;
      }

      public String getLossIncomeFlag() {
         return this.lossIncomeFlag;
      }

      public BigDecimal getLossIncomeAmt() {
         return this.lossIncomeAmt;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getSeqNo() {
         return this.seqNo;
      }

      public BigDecimal getForeAmtNew() {
         return this.foreAmtNew;
      }

      public String getExType() {
         return this.exType;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setBusiNo(String busiNo) {
         this.busiNo = busiNo;
      }

      public void setRegTranDate(String regTranDate) {
         this.regTranDate = regTranDate;
      }

      public void setForeCcy(String foreCcy) {
         this.foreCcy = foreCcy;
      }

      public void setTeamBankRateOld(BigDecimal teamBankRateOld) {
         this.teamBankRateOld = teamBankRateOld;
      }

      public void setOfferLetterOld(BigDecimal offerLetterOld) {
         this.offerLetterOld = offerLetterOld;
      }

      public void setForeAmtOld(BigDecimal foreAmtOld) {
         this.foreAmtOld = foreAmtOld;
      }

      public void setCnyAmtOld(BigDecimal cnyAmtOld) {
         this.cnyAmtOld = cnyAmtOld;
      }

      public void setChClientName(String chClientName) {
         this.chClientName = chClientName;
      }

      public void setEnClientName(String enClientName) {
         this.enClientName = enClientName;
      }

      public void setTeamBankRateNew(BigDecimal teamBankRateNew) {
         this.teamBankRateNew = teamBankRateNew;
      }

      public void setOfferLetterNew(BigDecimal offerLetterNew) {
         this.offerLetterNew = offerLetterNew;
      }

      public void setCnyAmtNew(BigDecimal cnyAmtNew) {
         this.cnyAmtNew = cnyAmtNew;
      }

      public void setLossIncomeFlag(String lossIncomeFlag) {
         this.lossIncomeFlag = lossIncomeFlag;
      }

      public void setLossIncomeAmt(BigDecimal lossIncomeAmt) {
         this.lossIncomeAmt = lossIncomeAmt;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setSeqNo(String seqNo) {
         this.seqNo = seqNo;
      }

      public void setForeAmtNew(BigDecimal foreAmtNew) {
         this.foreAmtNew = foreAmtNew;
      }

      public void setExType(String exType) {
         this.exType = exType;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1200061076In.Body)) {
            return false;
         } else {
            Core1200061076In.Body other = (Core1200061076In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label239: {
                  Object this$clientNo = this.getClientNo();
                  Object other$clientNo = other.getClientNo();
                  if (this$clientNo == null) {
                     if (other$clientNo == null) {
                        break label239;
                     }
                  } else if (this$clientNo.equals(other$clientNo)) {
                     break label239;
                  }

                  return false;
               }

               Object this$busiNo = this.getBusiNo();
               Object other$busiNo = other.getBusiNo();
               if (this$busiNo == null) {
                  if (other$busiNo != null) {
                     return false;
                  }
               } else if (!this$busiNo.equals(other$busiNo)) {
                  return false;
               }

               Object this$regTranDate = this.getRegTranDate();
               Object other$regTranDate = other.getRegTranDate();
               if (this$regTranDate == null) {
                  if (other$regTranDate != null) {
                     return false;
                  }
               } else if (!this$regTranDate.equals(other$regTranDate)) {
                  return false;
               }

               label218: {
                  Object this$foreCcy = this.getForeCcy();
                  Object other$foreCcy = other.getForeCcy();
                  if (this$foreCcy == null) {
                     if (other$foreCcy == null) {
                        break label218;
                     }
                  } else if (this$foreCcy.equals(other$foreCcy)) {
                     break label218;
                  }

                  return false;
               }

               label211: {
                  Object this$teamBankRateOld = this.getTeamBankRateOld();
                  Object other$teamBankRateOld = other.getTeamBankRateOld();
                  if (this$teamBankRateOld == null) {
                     if (other$teamBankRateOld == null) {
                        break label211;
                     }
                  } else if (this$teamBankRateOld.equals(other$teamBankRateOld)) {
                     break label211;
                  }

                  return false;
               }

               Object this$offerLetterOld = this.getOfferLetterOld();
               Object other$offerLetterOld = other.getOfferLetterOld();
               if (this$offerLetterOld == null) {
                  if (other$offerLetterOld != null) {
                     return false;
                  }
               } else if (!this$offerLetterOld.equals(other$offerLetterOld)) {
                  return false;
               }

               Object this$foreAmtOld = this.getForeAmtOld();
               Object other$foreAmtOld = other.getForeAmtOld();
               if (this$foreAmtOld == null) {
                  if (other$foreAmtOld != null) {
                     return false;
                  }
               } else if (!this$foreAmtOld.equals(other$foreAmtOld)) {
                  return false;
               }

               label190: {
                  Object this$cnyAmtOld = this.getCnyAmtOld();
                  Object other$cnyAmtOld = other.getCnyAmtOld();
                  if (this$cnyAmtOld == null) {
                     if (other$cnyAmtOld == null) {
                        break label190;
                     }
                  } else if (this$cnyAmtOld.equals(other$cnyAmtOld)) {
                     break label190;
                  }

                  return false;
               }

               label183: {
                  Object this$chClientName = this.getChClientName();
                  Object other$chClientName = other.getChClientName();
                  if (this$chClientName == null) {
                     if (other$chClientName == null) {
                        break label183;
                     }
                  } else if (this$chClientName.equals(other$chClientName)) {
                     break label183;
                  }

                  return false;
               }

               Object this$enClientName = this.getEnClientName();
               Object other$enClientName = other.getEnClientName();
               if (this$enClientName == null) {
                  if (other$enClientName != null) {
                     return false;
                  }
               } else if (!this$enClientName.equals(other$enClientName)) {
                  return false;
               }

               label169: {
                  Object this$teamBankRateNew = this.getTeamBankRateNew();
                  Object other$teamBankRateNew = other.getTeamBankRateNew();
                  if (this$teamBankRateNew == null) {
                     if (other$teamBankRateNew == null) {
                        break label169;
                     }
                  } else if (this$teamBankRateNew.equals(other$teamBankRateNew)) {
                     break label169;
                  }

                  return false;
               }

               Object this$offerLetterNew = this.getOfferLetterNew();
               Object other$offerLetterNew = other.getOfferLetterNew();
               if (this$offerLetterNew == null) {
                  if (other$offerLetterNew != null) {
                     return false;
                  }
               } else if (!this$offerLetterNew.equals(other$offerLetterNew)) {
                  return false;
               }

               label155: {
                  Object this$cnyAmtNew = this.getCnyAmtNew();
                  Object other$cnyAmtNew = other.getCnyAmtNew();
                  if (this$cnyAmtNew == null) {
                     if (other$cnyAmtNew == null) {
                        break label155;
                     }
                  } else if (this$cnyAmtNew.equals(other$cnyAmtNew)) {
                     break label155;
                  }

                  return false;
               }

               Object this$lossIncomeFlag = this.getLossIncomeFlag();
               Object other$lossIncomeFlag = other.getLossIncomeFlag();
               if (this$lossIncomeFlag == null) {
                  if (other$lossIncomeFlag != null) {
                     return false;
                  }
               } else if (!this$lossIncomeFlag.equals(other$lossIncomeFlag)) {
                  return false;
               }

               Object this$lossIncomeAmt = this.getLossIncomeAmt();
               Object other$lossIncomeAmt = other.getLossIncomeAmt();
               if (this$lossIncomeAmt == null) {
                  if (other$lossIncomeAmt != null) {
                     return false;
                  }
               } else if (!this$lossIncomeAmt.equals(other$lossIncomeAmt)) {
                  return false;
               }

               label134: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label134;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label134;
                  }

                  return false;
               }

               label127: {
                  Object this$seqNo = this.getSeqNo();
                  Object other$seqNo = other.getSeqNo();
                  if (this$seqNo == null) {
                     if (other$seqNo == null) {
                        break label127;
                     }
                  } else if (this$seqNo.equals(other$seqNo)) {
                     break label127;
                  }

                  return false;
               }

               Object this$foreAmtNew = this.getForeAmtNew();
               Object other$foreAmtNew = other.getForeAmtNew();
               if (this$foreAmtNew == null) {
                  if (other$foreAmtNew != null) {
                     return false;
                  }
               } else if (!this$foreAmtNew.equals(other$foreAmtNew)) {
                  return false;
               }

               Object this$exType = this.getExType();
               Object other$exType = other.getExType();
               if (this$exType == null) {
                  if (other$exType != null) {
                     return false;
                  }
               } else if (!this$exType.equals(other$exType)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1200061076In.Body;
      }
      public String toString() {
         return "Core1200061076In.Body(clientNo=" + this.getClientNo() + ", busiNo=" + this.getBusiNo() + ", regTranDate=" + this.getRegTranDate() + ", foreCcy=" + this.getForeCcy() + ", teamBankRateOld=" + this.getTeamBankRateOld() + ", offerLetterOld=" + this.getOfferLetterOld() + ", foreAmtOld=" + this.getForeAmtOld() + ", cnyAmtOld=" + this.getCnyAmtOld() + ", chClientName=" + this.getChClientName() + ", enClientName=" + this.getEnClientName() + ", teamBankRateNew=" + this.getTeamBankRateNew() + ", offerLetterNew=" + this.getOfferLetterNew() + ", cnyAmtNew=" + this.getCnyAmtNew() + ", lossIncomeFlag=" + this.getLossIncomeFlag() + ", lossIncomeAmt=" + this.getLossIncomeAmt() + ", baseAcctNo=" + this.getBaseAcctNo() + ", seqNo=" + this.getSeqNo() + ", foreAmtNew=" + this.getForeAmtNew() + ", exType=" + this.getExType() + ")";
      }
   }
}
