package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000223In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000223Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000223 {
   String URL = "/rb/inq/dc/acct/single";


   @ApiRemark("【过时】根据期次信息查询对应大额存单账户信息，循环覆盖了相应信息")
   @ApiDesc("用于单位大额存单赎回检查和单笔信息查询。")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0223"
   )
   @BusinessCategory("1400-查询")
   @FunctionCategory("RB10-大额存单")
   @ConsumeSys("EOS/PR/TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core14000223Out runService(Core14000223In var1);
}
