package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400100011Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100011Out.TranHistArray> tranHistArray;

   public List<Core1400100011Out.TranHistArray> getTranHistArray() {
      return this.tranHistArray;
   }

   public void setTranHistArray(List<Core1400100011Out.TranHistArray> tranHistArray) {
      this.tranHistArray = tranHistArray;
   }

   public String toString() {
      return "Core1400100011Out(tranHistArray=" + this.getTranHistArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100011Out)) {
         return false;
      } else {
         Core1400100011Out other = (Core1400100011Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$tranHistArray = this.getTranHistArray();
            Object other$tranHistArray = other.getTranHistArray();
            if (this$tranHistArray == null) {
               if (other$tranHistArray != null) {
                  return false;
               }
            } else if (!this$tranHistArray.equals(other$tranHistArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100011Out;
   }
   public static class TranHistArray {
      @V(
         desc = "证件类型",
         notNull = false,
         length = "3",
         remark = "证件类型",
         maxSize = 3
      )
      private String documentType;
      @V(
         desc = "证件号码",
         notNull = false,
         length = "50",
         remark = "证件号码",
         maxSize = 50
      )
      private String documentId;
      @V(
         desc = "客户中文名称",
         notNull = false,
         length = "200",
         remark = "客户中文名称",
         maxSize = 200
      )
      private String chClientName;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "客户类型",
         notNull = false,
         length = "3",
         remark = "客户大类，目前一般分为个人，公司，金融机构和内部客户。取之于CIF_CLIENT_TYPE.CLIENT_TYPE",
         maxSize = 3
      )
      private String clientType;
      @V(
         desc = "客户名称",
         notNull = false,
         length = "200",
         remark = "客户名称",
         maxSize = 200
      )
      private String clientName;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "账户开户行",
         notNull = false,
         length = "50",
         remark = "账户实际开户机构，柜面为实际网点机构，线上渠道一般为对应主账户的实际开户机构",
         maxSize = 50
      )
      private String acctBranch;
      @V(
         desc = "金额类型",
         notNull = false,
         length = "10",
         remark = "金额类型",
         maxSize = 10
      )
      private String amtType;
      @V(
         desc = "交易前余额",
         notNull = false,
         length = "17",
         remark = "交易前余额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal previousBalAmt;
      @V(
         desc = "实际余额",
         notNull = false,
         length = "17",
         remark = "实际余额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal actualBal;
      @V(
         desc = "账户描述",
         notNull = false,
         length = "200",
         remark = "账户描述,目前同账户名称",
         maxSize = 200
      )
      private String acctDesc;
      @V(
         desc = "交易金额",
         notNull = false,
         length = "17",
         remark = "交易金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal tranAmt;
      @V(
         desc = "渠道流水号",
         notNull = false,
         length = "50",
         remark = "渠道流水号",
         maxSize = 50
      )
      private String channelSeqNo;
      @V(
         desc = "交易类型",
         notNull = false,
         length = "10",
         remark = "交易类型",
         maxSize = 10
      )
      private String tranType;
      @V(
         desc = "转出交易类型",
         notNull = false,
         length = "10",
         remark = "转出交易类型",
         maxSize = 10
      )
      private String drTranType;
      @V(
         desc = "交易对手身份证件/证明文件类型",
         notNull = false,
         length = "3",
         remark = "交易对手身份证件/证明文件类型",
         maxSize = 3
      )
      private String othDocumentType;
      @V(
         desc = "交易对手身份证件/证明文件号码",
         notNull = false,
         length = "50",
         remark = "交易对手身份证件/证明文件号码",
         maxSize = 50
      )
      private String othDocumentId;
      @V(
         desc = "对方账号/卡号",
         notNull = false,
         length = "50",
         remark = "对方账号/卡号",
         maxSize = 50
      )
      private String othBaseAcctNo;
      @V(
         desc = "对方账户产品类型",
         notNull = false,
         length = "20",
         remark = "对方账户产品类型",
         maxSize = 20
      )
      private String othProdType;
      @V(
         desc = "对方账户币种",
         notNull = false,
         length = "3",
         remark = "对方账户币种",
         maxSize = 3
      )
      private String othAcctCcy;
      @V(
         desc = "对方账户序列号",
         notNull = false,
         length = "5",
         remark = "对方账户序列号",
         maxSize = 5
      )
      private String othAcctSeqNo;
      @V(
         desc = "对方账户描述",
         notNull = false,
         length = "200",
         remark = "对方账户描述",
         maxSize = 200
      )
      private String othAcctDesc;
      @V(
         desc = "对方账户开户机构",
         notNull = false,
         length = "50",
         remark = "对方账户开户机构",
         maxSize = 50
      )
      private String othBranch;
      @V(
         desc = "对方银行名称",
         notNull = false,
         length = "50",
         remark = "对方银行名称",
         maxSize = 50
      )
      private String othBankName;
      @V(
         desc = "对方银行代码",
         notNull = false,
         length = "20",
         remark = "对方银行代码",
         maxSize = 20
      )
      private String othBankCode;
      @V(
         desc = "对方交易流水号",
         notNull = false,
         length = "50",
         remark = "对方交易流水号",
         maxSize = 50
      )
      private String othSeqNo;
      @V(
         desc = "对方交易参考号",
         notNull = false,
         length = "50",
         remark = "对方交易参考号",
         maxSize = 50
      )
      private String othReference;
      @V(
         desc = "收款方账号",
         notNull = false,
         length = "50",
         remark = "收款方账号",
         maxSize = 50
      )
      private String toAcctNo;
      @V(
         desc = "转出户名",
         notNull = false,
         length = "200",
         remark = "转出户名",
         maxSize = 200
      )
      private String toAcctName;
      @V(
         desc = "付款方账号",
         notNull = false,
         length = "50",
         remark = "付款方账号",
         maxSize = 50
      )
      private String sourceAcctNo;
      @V(
         desc = "付款方名称",
         notNull = false,
         length = "200",
         remark = "付款方名称",
         maxSize = 200
      )
      private String sourceAcctName;
      @V(
         desc = "凭证类型",
         notNull = false,
         length = "10",
         remark = "凭证类型",
         maxSize = 10
      )
      private String docType;
      @V(
         desc = "前缀",
         notNull = false,
         length = "10",
         remark = "前缀",
         maxSize = 10
      )
      private String prefix;
      @V(
         desc = "凭证号码",
         notNull = false,
         length = "50",
         remark = "凭证号码",
         maxSize = 50
      )
      private String voucherNo;
      @V(
         desc = "支取方式",
         notNull = false,
         length = "1",
         remark = "支取方式",
         maxSize = 1
      )
      private String withdrawalType;
      @V(
         desc = "账户用途",
         notNull = false,
         length = "10",
         remark = "账户用途",
         maxSize = 10
      )
      private String reasonCode;
      @V(
         desc = "交易柜员",
         notNull = false,
         length = "30",
         remark = "交易柜员",
         maxSize = 30
      )
      private String userId;
      @V(
         desc = "银行交易序号",
         notNull = false,
         length = "50",
         remark = "银行交易序号,单一机构下发生交易序号，按顺序递增 格式为 \"机构_序号",
         maxSize = 50
      )
      private String bankSeqNo;
      @V(
         desc = "授权柜员",
         notNull = false,
         length = "30",
         remark = "授权柜员",
         maxSize = 30
      )
      private String authUserId;
      @V(
         desc = "复核柜员",
         notNull = false,
         length = "30",
         remark = "复核柜员",
         maxSize = 30
      )
      private String apprUserId;
      @V(
         desc = "银行名称",
         notNull = false,
         length = "50",
         remark = "银行名称",
         maxSize = 50
      )
      private String bankName;
      @V(
         desc = "银行代码",
         notNull = false,
         length = "20",
         remark = "银行代码",
         maxSize = 20
      )
      private String bankCode;
      @V(
         desc = "所属机构号",
         notNull = false,
         length = "50",
         remark = "所属机构号",
         maxSize = 50
      )
      private String branch;
      @V(
         desc = "交易参考号",
         notNull = false,
         length = "50",
         remark = "交易参考号",
         maxSize = 50
      )
      private String reference;
      @V(
         desc = "交易种类",
         notNull = false,
         length = "5",
         remark = "交易种类",
         maxSize = 5
      )
      private String tranCategory;
      @V(
         desc = "生效日期",
         notNull = false,
         remark = "生效日期"
      )
      private String effectDate;
      @V(
         desc = "冲正交易类型",
         notNull = false,
         length = "10",
         remark = "冲正交易类型",
         maxSize = 10
      )
      private String reversalTranType;
      @V(
         desc = "冲正日期",
         notNull = false,
         remark = "冲正日期"
      )
      private String reversalDate;
      @V(
         desc = "序号",
         notNull = false,
         length = "50",
         remark = "序号",
         maxSize = 50
      )
      private String seqNo;
      @V(
         desc = "交易日期",
         notNull = false,
         remark = "交易日期"
      )
      private String tranDate;
      @V(
         desc = "交易描述",
         notNull = false,
         length = "200",
         remark = "交易描述",
         maxSize = 200
      )
      private String tranDesc;
      @V(
         desc = "交易附言",
         notNull = false,
         length = "2000",
         remark = "交易附言",
         maxSize = 2000
      )
      private String tranNote;
      @V(
         desc = "主交易序号",
         notNull = false,
         length = "50",
         remark = "主交易序号",
         maxSize = 50
      )
      private String primaryTranSeqNo;
      @V(
         desc = "资金冻结流水号",
         notNull = false,
         length = "50",
         remark = "记录冻结编号信息",
         maxSize = 50
      )
      private String fhSeqNo;
      @V(
         desc = "清算日期",
         notNull = false,
         remark = "清算日期"
      )
      private String settlementDate;
      @V(
         desc = "他行等值金额",
         notNull = false,
         length = "17",
         remark = "他行等值金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal contraEquivAmt;
      @V(
         desc = "基础等值金额",
         notNull = false,
         length = "17",
         remark = "基础等值金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal baseEquivAmt;
      @V(
         desc = "交叉汇率",
         notNull = false,
         length = "15",
         remark = "交叉汇率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal crossRate;
      @V(
         desc = "起始币种",
         notNull = false,
         length = "3",
         remark = "源币种",
         maxSize = 3
      )
      private String fromCcy;
      @V(
         desc = "移出金额",
         notNull = false,
         length = "17",
         remark = "移出金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal fromAmount;
      @V(
         desc = "买方交易汇率标志",
         notNull = false,
         length = "1",
         remark = "买方交易汇率标志",
         maxSize = 1
      )
      private String fromRateFlag;
      @V(
         desc = "买方汇率值",
         notNull = false,
         length = "15",
         remark = "买方汇率值",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal fromXrate;
      @V(
         desc = "目的币种",
         notNull = false,
         length = "3",
         remark = "目标币种",
         maxSize = 3
      )
      private String toCcy;
      @V(
         desc = "移入金额",
         notNull = false,
         length = "17",
         remark = "移入金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal toAmount;
      @V(
         desc = "卖方交易汇率标志",
         notNull = false,
         length = "1",
         remark = "卖方交易汇率标志",
         maxSize = 1
      )
      private String toRateFlag;
      @V(
         desc = "卖方汇率值",
         notNull = false,
         length = "15",
         remark = "卖方汇率值",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal toXrate;
      @V(
         desc = "根据实际交易时修改交叉汇率计算的金额",
         notNull = false,
         length = "17",
         remark = "根据实际交易时修改交叉汇率计算的金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal ovToAmount;
      @V(
         desc = "汇率标志",
         notNull = false,
         length = "1",
         remark = "汇率标志",
         maxSize = 1
      )
      private String rateFlag;
      @V(
         desc = "牌价类型",
         notNull = false,
         length = "1",
         remark = "牌价类型",
         maxSize = 1
      )
      private String quoteType;
      @V(
         desc = "汇率类型",
         notNull = false,
         length = "10",
         remark = "汇率类型",
         maxSize = 10
      )
      private String rateType;
      @V(
         desc = "卖方牌价类型",
         notNull = false,
         length = "1",
         remark = "牌价类型",
         maxSize = 1
      )
      private String toId;
      @V(
         desc = "终端号",
         notNull = false,
         length = "50",
         remark = "终端号",
         maxSize = 50
      )
      private String terminalId;
      @V(
         desc = "跟踪ID",
         notNull = false,
         length = "200",
         remark = "跟踪ID",
         maxSize = 200
      )
      private String traceId;
      @V(
         desc = "摘要",
         notNull = false,
         length = "500",
         remark = "开户时的账号用途，销户时的销户原因",
         maxSize = 500
      )
      private String narrative;
      @V(
         desc = "现金项目",
         notNull = false,
         length = "10",
         remark = "现金项目",
         maxSize = 10
      )
      private String cashItem;
      @V(
         desc = "业务处理状态",
         notNull = false,
         length = "1",
         remark = "业务处理状态",
         maxSize = 1
      )
      private String tranStatus;
      @V(
         desc = "利润中心",
         notNull = false,
         length = "20",
         remark = "利润中心",
         maxSize = 20
      )
      private String profitCenter;
      @V(
         desc = "账套",
         notNull = false,
         length = "10",
         remark = "账套",
         maxSize = 10
      )
      private String businessUnit;
      @V(
         desc = "源模块",
         notNull = false,
         length = "3",
         remark = "源模块",
         maxSize = 3
      )
      private String sourceModule;
      @V(
         desc = "事件类型",
         notNull = false,
         length = "20",
         remark = "事件类型",
         maxSize = 20
      )
      private String eventType;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "借贷标识",
         notNull = false,
         length = "1",
         remark = "借贷标识",
         maxSize = 1
      )
      private String crDrMaintInd;
      @V(
         desc = "余额类型",
         notNull = false,
         length = "2",
         remark = "余额类型",
         maxSize = 2
      )
      private String balType;
      @V(
         desc = "打印次数",
         notNull = false,
         length = "5",
         remark = "银行承兑汇票登记簿凭证打印次数"
      )
      private Integer printCnt;
      @V(
         desc = "批次号",
         notNull = false,
         length = "50",
         remark = "批次号",
         maxSize = 50
      )
      private String batchNo;
      @V(
         desc = "中间业务类型",
         notNull = false,
         length = "10",
         remark = "中间业务类型",
         maxSize = 10
      )
      private String bizType;
      @V(
         desc = "是否补登存",
         notNull = false,
         length = "1",
         remark = "是否补登存",
         maxSize = 1
      )
      private String pbkUpdFlag;
      @V(
         desc = "渠道类型",
         notNull = false,
         length = "10",
         remark = "渠道类型",
         maxSize = 10
      )
      private String sourceType;
      @V(
         desc = "是否冲正标志",
         notNull = false,
         length = "1",
         remark = "是否冲正标志",
         maxSize = 1
      )
      private String reversal;
      @V(
         desc = "交款单位",
         notNull = false,
         length = "200",
         remark = "交款单位",
         maxSize = 200
      )
      private String payUnit;
      @V(
         desc = "交易时间戳",
         notNull = false,
         length = "26",
         remark = "交易时间戳",
         maxSize = 26
      )
      private String tranTimestamp;
      @V(
         desc = "服务费标识",
         notNull = false,
         length = "1",
         remark = "服务费标识",
         maxSize = 1
      )
      private String servCharge;
      @V(
         desc = "法人",
         notNull = false,
         length = "20",
         remark = "法人",
         maxSize = 20
      )
      private String company;
      @V(
         desc = "账户开户日期",
         notNull = false,
         remark = "账户开户日期"
      )
      private String acctOpenDate;
      @V(
         desc = "存期期限",
         notNull = false,
         length = "5",
         remark = "期限",
         maxSize = 5
      )
      private String term;
      @V(
         desc = "期限类型",
         notNull = false,
         length = "1",
         remark = "期限类型",
         maxSize = 1
      )
      private String termType;
      @V(
         desc = "对方账户名称",
         notNull = false,
         length = "200",
         remark = "对方账户名称",
         maxSize = 200
      )
      private String othAcctName;
      @V(
         desc = "执行利率",
         notNull = false,
         length = "15",
         remark = "执行利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal realRate;
      @V(
         desc = "行内利率",
         notNull = false,
         length = "15",
         remark = "在人行基准利率调整后对客发布的行内利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal actualRate;
      @V(
         desc = "真实交易对手账号",
         notNull = false,
         length = "50",
         remark = "真实交易对手账号",
         maxSize = 50
      )
      private String othRealBaseAcctNo;
      @V(
         desc = "真实交易对手名称",
         notNull = false,
         length = "200",
         remark = "真实交易对手名称",
         maxSize = 200
      )
      private String othRealTranName;
      @V(
         desc = "机构名称",
         notNull = false,
         length = "200",
         remark = "机构名称",
         maxSize = 200
      )
      private String branchName;
      @V(
         desc = "定期账号",
         notNull = false,
         length = "50",
         remark = "定期账号",
         maxSize = 50
      )
      private String tdaBaseAcctNo;
      @V(
         desc = "期次代码",
         notNull = false,
         length = "50",
         remark = "期次代码",
         maxSize = 50
      )
      private String stageCode;
      @V(
         desc = "小额免密标志",
         notNull = false,
         length = "1",
         remark = "小额免密标志",
         maxSize = 1
      )
      private String piFlag;
      @V(
         desc = "期次描述",
         notNull = false,
         length = "200",
         remark = "期次描述",
         maxSize = 200
      )
      private String stageCodeDesc;
      @V(
         desc = "真实金融交易机构",
         notNull = false,
         length = "100",
         remark = "真实金融交易机构",
         maxSize = 100
      )
      private String realTranBranchName;
      @V(
         desc = "转让日期",
         notNull = false,
         remark = "转让日期"
      )
      private String trfDate;
      @V(
         desc = "转让利率",
         notNull = false,
         length = "15",
         remark = "转让利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal trfRate;
      @V(
         desc = "到期日",
         notNull = false,
         remark = "到期日"
      )
      private String matureDate;
      @V(
         desc = "真实金融交易机构",
         notNull = false,
         length = "20",
         remark = "真实金融交易机构",
         maxSize = 20
      )
      private String realBranch;

      public String getDocumentType() {
         return this.documentType;
      }

      public String getDocumentId() {
         return this.documentId;
      }

      public String getChClientName() {
         return this.chClientName;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public String getClientType() {
         return this.clientType;
      }

      public String getClientName() {
         return this.clientName;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getAcctBranch() {
         return this.acctBranch;
      }

      public String getAmtType() {
         return this.amtType;
      }

      public BigDecimal getPreviousBalAmt() {
         return this.previousBalAmt;
      }

      public BigDecimal getActualBal() {
         return this.actualBal;
      }

      public String getAcctDesc() {
         return this.acctDesc;
      }

      public BigDecimal getTranAmt() {
         return this.tranAmt;
      }

      public String getChannelSeqNo() {
         return this.channelSeqNo;
      }

      public String getTranType() {
         return this.tranType;
      }

      public String getDrTranType() {
         return this.drTranType;
      }

      public String getOthDocumentType() {
         return this.othDocumentType;
      }

      public String getOthDocumentId() {
         return this.othDocumentId;
      }

      public String getOthBaseAcctNo() {
         return this.othBaseAcctNo;
      }

      public String getOthProdType() {
         return this.othProdType;
      }

      public String getOthAcctCcy() {
         return this.othAcctCcy;
      }

      public String getOthAcctSeqNo() {
         return this.othAcctSeqNo;
      }

      public String getOthAcctDesc() {
         return this.othAcctDesc;
      }

      public String getOthBranch() {
         return this.othBranch;
      }

      public String getOthBankName() {
         return this.othBankName;
      }

      public String getOthBankCode() {
         return this.othBankCode;
      }

      public String getOthSeqNo() {
         return this.othSeqNo;
      }

      public String getOthReference() {
         return this.othReference;
      }

      public String getToAcctNo() {
         return this.toAcctNo;
      }

      public String getToAcctName() {
         return this.toAcctName;
      }

      public String getSourceAcctNo() {
         return this.sourceAcctNo;
      }

      public String getSourceAcctName() {
         return this.sourceAcctName;
      }

      public String getDocType() {
         return this.docType;
      }

      public String getPrefix() {
         return this.prefix;
      }

      public String getVoucherNo() {
         return this.voucherNo;
      }

      public String getWithdrawalType() {
         return this.withdrawalType;
      }

      public String getReasonCode() {
         return this.reasonCode;
      }

      public String getUserId() {
         return this.userId;
      }

      public String getBankSeqNo() {
         return this.bankSeqNo;
      }

      public String getAuthUserId() {
         return this.authUserId;
      }

      public String getApprUserId() {
         return this.apprUserId;
      }

      public String getBankName() {
         return this.bankName;
      }

      public String getBankCode() {
         return this.bankCode;
      }

      public String getBranch() {
         return this.branch;
      }

      public String getReference() {
         return this.reference;
      }

      public String getTranCategory() {
         return this.tranCategory;
      }

      public String getEffectDate() {
         return this.effectDate;
      }

      public String getReversalTranType() {
         return this.reversalTranType;
      }

      public String getReversalDate() {
         return this.reversalDate;
      }

      public String getSeqNo() {
         return this.seqNo;
      }

      public String getTranDate() {
         return this.tranDate;
      }

      public String getTranDesc() {
         return this.tranDesc;
      }

      public String getTranNote() {
         return this.tranNote;
      }

      public String getPrimaryTranSeqNo() {
         return this.primaryTranSeqNo;
      }

      public String getFhSeqNo() {
         return this.fhSeqNo;
      }

      public String getSettlementDate() {
         return this.settlementDate;
      }

      public BigDecimal getContraEquivAmt() {
         return this.contraEquivAmt;
      }

      public BigDecimal getBaseEquivAmt() {
         return this.baseEquivAmt;
      }

      public BigDecimal getCrossRate() {
         return this.crossRate;
      }

      public String getFromCcy() {
         return this.fromCcy;
      }

      public BigDecimal getFromAmount() {
         return this.fromAmount;
      }

      public String getFromRateFlag() {
         return this.fromRateFlag;
      }

      public BigDecimal getFromXrate() {
         return this.fromXrate;
      }

      public String getToCcy() {
         return this.toCcy;
      }

      public BigDecimal getToAmount() {
         return this.toAmount;
      }

      public String getToRateFlag() {
         return this.toRateFlag;
      }

      public BigDecimal getToXrate() {
         return this.toXrate;
      }

      public BigDecimal getOvToAmount() {
         return this.ovToAmount;
      }

      public String getRateFlag() {
         return this.rateFlag;
      }

      public String getQuoteType() {
         return this.quoteType;
      }

      public String getRateType() {
         return this.rateType;
      }

      public String getToId() {
         return this.toId;
      }

      public String getTerminalId() {
         return this.terminalId;
      }

      public String getTraceId() {
         return this.traceId;
      }

      public String getNarrative() {
         return this.narrative;
      }

      public String getCashItem() {
         return this.cashItem;
      }

      public String getTranStatus() {
         return this.tranStatus;
      }

      public String getProfitCenter() {
         return this.profitCenter;
      }

      public String getBusinessUnit() {
         return this.businessUnit;
      }

      public String getSourceModule() {
         return this.sourceModule;
      }

      public String getEventType() {
         return this.eventType;
      }

      public String getCcy() {
         return this.ccy;
      }

      public String getCrDrMaintInd() {
         return this.crDrMaintInd;
      }

      public String getBalType() {
         return this.balType;
      }

      public Integer getPrintCnt() {
         return this.printCnt;
      }

      public String getBatchNo() {
         return this.batchNo;
      }

      public String getBizType() {
         return this.bizType;
      }

      public String getPbkUpdFlag() {
         return this.pbkUpdFlag;
      }

      public String getSourceType() {
         return this.sourceType;
      }

      public String getReversal() {
         return this.reversal;
      }

      public String getPayUnit() {
         return this.payUnit;
      }

      public String getTranTimestamp() {
         return this.tranTimestamp;
      }

      public String getServCharge() {
         return this.servCharge;
      }

      public String getCompany() {
         return this.company;
      }

      public String getAcctOpenDate() {
         return this.acctOpenDate;
      }

      public String getTerm() {
         return this.term;
      }

      public String getTermType() {
         return this.termType;
      }

      public String getOthAcctName() {
         return this.othAcctName;
      }

      public BigDecimal getRealRate() {
         return this.realRate;
      }

      public BigDecimal getActualRate() {
         return this.actualRate;
      }

      public String getOthRealBaseAcctNo() {
         return this.othRealBaseAcctNo;
      }

      public String getOthRealTranName() {
         return this.othRealTranName;
      }

      public String getBranchName() {
         return this.branchName;
      }

      public String getTdaBaseAcctNo() {
         return this.tdaBaseAcctNo;
      }

      public String getStageCode() {
         return this.stageCode;
      }

      public String getPiFlag() {
         return this.piFlag;
      }

      public String getStageCodeDesc() {
         return this.stageCodeDesc;
      }

      public String getRealTranBranchName() {
         return this.realTranBranchName;
      }

      public String getTrfDate() {
         return this.trfDate;
      }

      public BigDecimal getTrfRate() {
         return this.trfRate;
      }

      public String getMatureDate() {
         return this.matureDate;
      }

      public String getRealBranch() {
         return this.realBranch;
      }

      public void setDocumentType(String documentType) {
         this.documentType = documentType;
      }

      public void setDocumentId(String documentId) {
         this.documentId = documentId;
      }

      public void setChClientName(String chClientName) {
         this.chClientName = chClientName;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setClientType(String clientType) {
         this.clientType = clientType;
      }

      public void setClientName(String clientName) {
         this.clientName = clientName;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setAcctBranch(String acctBranch) {
         this.acctBranch = acctBranch;
      }

      public void setAmtType(String amtType) {
         this.amtType = amtType;
      }

      public void setPreviousBalAmt(BigDecimal previousBalAmt) {
         this.previousBalAmt = previousBalAmt;
      }

      public void setActualBal(BigDecimal actualBal) {
         this.actualBal = actualBal;
      }

      public void setAcctDesc(String acctDesc) {
         this.acctDesc = acctDesc;
      }

      public void setTranAmt(BigDecimal tranAmt) {
         this.tranAmt = tranAmt;
      }

      public void setChannelSeqNo(String channelSeqNo) {
         this.channelSeqNo = channelSeqNo;
      }

      public void setTranType(String tranType) {
         this.tranType = tranType;
      }

      public void setDrTranType(String drTranType) {
         this.drTranType = drTranType;
      }

      public void setOthDocumentType(String othDocumentType) {
         this.othDocumentType = othDocumentType;
      }

      public void setOthDocumentId(String othDocumentId) {
         this.othDocumentId = othDocumentId;
      }

      public void setOthBaseAcctNo(String othBaseAcctNo) {
         this.othBaseAcctNo = othBaseAcctNo;
      }

      public void setOthProdType(String othProdType) {
         this.othProdType = othProdType;
      }

      public void setOthAcctCcy(String othAcctCcy) {
         this.othAcctCcy = othAcctCcy;
      }

      public void setOthAcctSeqNo(String othAcctSeqNo) {
         this.othAcctSeqNo = othAcctSeqNo;
      }

      public void setOthAcctDesc(String othAcctDesc) {
         this.othAcctDesc = othAcctDesc;
      }

      public void setOthBranch(String othBranch) {
         this.othBranch = othBranch;
      }

      public void setOthBankName(String othBankName) {
         this.othBankName = othBankName;
      }

      public void setOthBankCode(String othBankCode) {
         this.othBankCode = othBankCode;
      }

      public void setOthSeqNo(String othSeqNo) {
         this.othSeqNo = othSeqNo;
      }

      public void setOthReference(String othReference) {
         this.othReference = othReference;
      }

      public void setToAcctNo(String toAcctNo) {
         this.toAcctNo = toAcctNo;
      }

      public void setToAcctName(String toAcctName) {
         this.toAcctName = toAcctName;
      }

      public void setSourceAcctNo(String sourceAcctNo) {
         this.sourceAcctNo = sourceAcctNo;
      }

      public void setSourceAcctName(String sourceAcctName) {
         this.sourceAcctName = sourceAcctName;
      }

      public void setDocType(String docType) {
         this.docType = docType;
      }

      public void setPrefix(String prefix) {
         this.prefix = prefix;
      }

      public void setVoucherNo(String voucherNo) {
         this.voucherNo = voucherNo;
      }

      public void setWithdrawalType(String withdrawalType) {
         this.withdrawalType = withdrawalType;
      }

      public void setReasonCode(String reasonCode) {
         this.reasonCode = reasonCode;
      }

      public void setUserId(String userId) {
         this.userId = userId;
      }

      public void setBankSeqNo(String bankSeqNo) {
         this.bankSeqNo = bankSeqNo;
      }

      public void setAuthUserId(String authUserId) {
         this.authUserId = authUserId;
      }

      public void setApprUserId(String apprUserId) {
         this.apprUserId = apprUserId;
      }

      public void setBankName(String bankName) {
         this.bankName = bankName;
      }

      public void setBankCode(String bankCode) {
         this.bankCode = bankCode;
      }

      public void setBranch(String branch) {
         this.branch = branch;
      }

      public void setReference(String reference) {
         this.reference = reference;
      }

      public void setTranCategory(String tranCategory) {
         this.tranCategory = tranCategory;
      }

      public void setEffectDate(String effectDate) {
         this.effectDate = effectDate;
      }

      public void setReversalTranType(String reversalTranType) {
         this.reversalTranType = reversalTranType;
      }

      public void setReversalDate(String reversalDate) {
         this.reversalDate = reversalDate;
      }

      public void setSeqNo(String seqNo) {
         this.seqNo = seqNo;
      }

      public void setTranDate(String tranDate) {
         this.tranDate = tranDate;
      }

      public void setTranDesc(String tranDesc) {
         this.tranDesc = tranDesc;
      }

      public void setTranNote(String tranNote) {
         this.tranNote = tranNote;
      }

      public void setPrimaryTranSeqNo(String primaryTranSeqNo) {
         this.primaryTranSeqNo = primaryTranSeqNo;
      }

      public void setFhSeqNo(String fhSeqNo) {
         this.fhSeqNo = fhSeqNo;
      }

      public void setSettlementDate(String settlementDate) {
         this.settlementDate = settlementDate;
      }

      public void setContraEquivAmt(BigDecimal contraEquivAmt) {
         this.contraEquivAmt = contraEquivAmt;
      }

      public void setBaseEquivAmt(BigDecimal baseEquivAmt) {
         this.baseEquivAmt = baseEquivAmt;
      }

      public void setCrossRate(BigDecimal crossRate) {
         this.crossRate = crossRate;
      }

      public void setFromCcy(String fromCcy) {
         this.fromCcy = fromCcy;
      }

      public void setFromAmount(BigDecimal fromAmount) {
         this.fromAmount = fromAmount;
      }

      public void setFromRateFlag(String fromRateFlag) {
         this.fromRateFlag = fromRateFlag;
      }

      public void setFromXrate(BigDecimal fromXrate) {
         this.fromXrate = fromXrate;
      }

      public void setToCcy(String toCcy) {
         this.toCcy = toCcy;
      }

      public void setToAmount(BigDecimal toAmount) {
         this.toAmount = toAmount;
      }

      public void setToRateFlag(String toRateFlag) {
         this.toRateFlag = toRateFlag;
      }

      public void setToXrate(BigDecimal toXrate) {
         this.toXrate = toXrate;
      }

      public void setOvToAmount(BigDecimal ovToAmount) {
         this.ovToAmount = ovToAmount;
      }

      public void setRateFlag(String rateFlag) {
         this.rateFlag = rateFlag;
      }

      public void setQuoteType(String quoteType) {
         this.quoteType = quoteType;
      }

      public void setRateType(String rateType) {
         this.rateType = rateType;
      }

      public void setToId(String toId) {
         this.toId = toId;
      }

      public void setTerminalId(String terminalId) {
         this.terminalId = terminalId;
      }

      public void setTraceId(String traceId) {
         this.traceId = traceId;
      }

      public void setNarrative(String narrative) {
         this.narrative = narrative;
      }

      public void setCashItem(String cashItem) {
         this.cashItem = cashItem;
      }

      public void setTranStatus(String tranStatus) {
         this.tranStatus = tranStatus;
      }

      public void setProfitCenter(String profitCenter) {
         this.profitCenter = profitCenter;
      }

      public void setBusinessUnit(String businessUnit) {
         this.businessUnit = businessUnit;
      }

      public void setSourceModule(String sourceModule) {
         this.sourceModule = sourceModule;
      }

      public void setEventType(String eventType) {
         this.eventType = eventType;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setCrDrMaintInd(String crDrMaintInd) {
         this.crDrMaintInd = crDrMaintInd;
      }

      public void setBalType(String balType) {
         this.balType = balType;
      }

      public void setPrintCnt(Integer printCnt) {
         this.printCnt = printCnt;
      }

      public void setBatchNo(String batchNo) {
         this.batchNo = batchNo;
      }

      public void setBizType(String bizType) {
         this.bizType = bizType;
      }

      public void setPbkUpdFlag(String pbkUpdFlag) {
         this.pbkUpdFlag = pbkUpdFlag;
      }

      public void setSourceType(String sourceType) {
         this.sourceType = sourceType;
      }

      public void setReversal(String reversal) {
         this.reversal = reversal;
      }

      public void setPayUnit(String payUnit) {
         this.payUnit = payUnit;
      }

      public void setTranTimestamp(String tranTimestamp) {
         this.tranTimestamp = tranTimestamp;
      }

      public void setServCharge(String servCharge) {
         this.servCharge = servCharge;
      }

      public void setCompany(String company) {
         this.company = company;
      }

      public void setAcctOpenDate(String acctOpenDate) {
         this.acctOpenDate = acctOpenDate;
      }

      public void setTerm(String term) {
         this.term = term;
      }

      public void setTermType(String termType) {
         this.termType = termType;
      }

      public void setOthAcctName(String othAcctName) {
         this.othAcctName = othAcctName;
      }

      public void setRealRate(BigDecimal realRate) {
         this.realRate = realRate;
      }

      public void setActualRate(BigDecimal actualRate) {
         this.actualRate = actualRate;
      }

      public void setOthRealBaseAcctNo(String othRealBaseAcctNo) {
         this.othRealBaseAcctNo = othRealBaseAcctNo;
      }

      public void setOthRealTranName(String othRealTranName) {
         this.othRealTranName = othRealTranName;
      }

      public void setBranchName(String branchName) {
         this.branchName = branchName;
      }

      public void setTdaBaseAcctNo(String tdaBaseAcctNo) {
         this.tdaBaseAcctNo = tdaBaseAcctNo;
      }

      public void setStageCode(String stageCode) {
         this.stageCode = stageCode;
      }

      public void setPiFlag(String piFlag) {
         this.piFlag = piFlag;
      }

      public void setStageCodeDesc(String stageCodeDesc) {
         this.stageCodeDesc = stageCodeDesc;
      }

      public void setRealTranBranchName(String realTranBranchName) {
         this.realTranBranchName = realTranBranchName;
      }

      public void setTrfDate(String trfDate) {
         this.trfDate = trfDate;
      }

      public void setTrfRate(BigDecimal trfRate) {
         this.trfRate = trfRate;
      }

      public void setMatureDate(String matureDate) {
         this.matureDate = matureDate;
      }

      public void setRealBranch(String realBranch) {
         this.realBranch = realBranch;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100011Out.TranHistArray)) {
            return false;
         } else {
            Core1400100011Out.TranHistArray other = (Core1400100011Out.TranHistArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label1391: {
                  Object this$documentType = this.getDocumentType();
                  Object other$documentType = other.getDocumentType();
                  if (this$documentType == null) {
                     if (other$documentType == null) {
                        break label1391;
                     }
                  } else if (this$documentType.equals(other$documentType)) {
                     break label1391;
                  }

                  return false;
               }

               Object this$documentId = this.getDocumentId();
               Object other$documentId = other.getDocumentId();
               if (this$documentId == null) {
                  if (other$documentId != null) {
                     return false;
                  }
               } else if (!this$documentId.equals(other$documentId)) {
                  return false;
               }

               Object this$chClientName = this.getChClientName();
               Object other$chClientName = other.getChClientName();
               if (this$chClientName == null) {
                  if (other$chClientName != null) {
                     return false;
                  }
               } else if (!this$chClientName.equals(other$chClientName)) {
                  return false;
               }

               label1370: {
                  Object this$clientNo = this.getClientNo();
                  Object other$clientNo = other.getClientNo();
                  if (this$clientNo == null) {
                     if (other$clientNo == null) {
                        break label1370;
                     }
                  } else if (this$clientNo.equals(other$clientNo)) {
                     break label1370;
                  }

                  return false;
               }

               label1363: {
                  Object this$clientType = this.getClientType();
                  Object other$clientType = other.getClientType();
                  if (this$clientType == null) {
                     if (other$clientType == null) {
                        break label1363;
                     }
                  } else if (this$clientType.equals(other$clientType)) {
                     break label1363;
                  }

                  return false;
               }

               Object this$clientName = this.getClientName();
               Object other$clientName = other.getClientName();
               if (this$clientName == null) {
                  if (other$clientName != null) {
                     return false;
                  }
               } else if (!this$clientName.equals(other$clientName)) {
                  return false;
               }

               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo != null) {
                     return false;
                  }
               } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
                  return false;
               }

               label1342: {
                  Object this$prodType = this.getProdType();
                  Object other$prodType = other.getProdType();
                  if (this$prodType == null) {
                     if (other$prodType == null) {
                        break label1342;
                     }
                  } else if (this$prodType.equals(other$prodType)) {
                     break label1342;
                  }

                  return false;
               }

               label1335: {
                  Object this$acctCcy = this.getAcctCcy();
                  Object other$acctCcy = other.getAcctCcy();
                  if (this$acctCcy == null) {
                     if (other$acctCcy == null) {
                        break label1335;
                     }
                  } else if (this$acctCcy.equals(other$acctCcy)) {
                     break label1335;
                  }

                  return false;
               }

               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo != null) {
                     return false;
                  }
               } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                  return false;
               }

               label1321: {
                  Object this$acctBranch = this.getAcctBranch();
                  Object other$acctBranch = other.getAcctBranch();
                  if (this$acctBranch == null) {
                     if (other$acctBranch == null) {
                        break label1321;
                     }
                  } else if (this$acctBranch.equals(other$acctBranch)) {
                     break label1321;
                  }

                  return false;
               }

               Object this$amtType = this.getAmtType();
               Object other$amtType = other.getAmtType();
               if (this$amtType == null) {
                  if (other$amtType != null) {
                     return false;
                  }
               } else if (!this$amtType.equals(other$amtType)) {
                  return false;
               }

               label1307: {
                  Object this$previousBalAmt = this.getPreviousBalAmt();
                  Object other$previousBalAmt = other.getPreviousBalAmt();
                  if (this$previousBalAmt == null) {
                     if (other$previousBalAmt == null) {
                        break label1307;
                     }
                  } else if (this$previousBalAmt.equals(other$previousBalAmt)) {
                     break label1307;
                  }

                  return false;
               }

               Object this$actualBal = this.getActualBal();
               Object other$actualBal = other.getActualBal();
               if (this$actualBal == null) {
                  if (other$actualBal != null) {
                     return false;
                  }
               } else if (!this$actualBal.equals(other$actualBal)) {
                  return false;
               }

               Object this$acctDesc = this.getAcctDesc();
               Object other$acctDesc = other.getAcctDesc();
               if (this$acctDesc == null) {
                  if (other$acctDesc != null) {
                     return false;
                  }
               } else if (!this$acctDesc.equals(other$acctDesc)) {
                  return false;
               }

               label1286: {
                  Object this$tranAmt = this.getTranAmt();
                  Object other$tranAmt = other.getTranAmt();
                  if (this$tranAmt == null) {
                     if (other$tranAmt == null) {
                        break label1286;
                     }
                  } else if (this$tranAmt.equals(other$tranAmt)) {
                     break label1286;
                  }

                  return false;
               }

               label1279: {
                  Object this$channelSeqNo = this.getChannelSeqNo();
                  Object other$channelSeqNo = other.getChannelSeqNo();
                  if (this$channelSeqNo == null) {
                     if (other$channelSeqNo == null) {
                        break label1279;
                     }
                  } else if (this$channelSeqNo.equals(other$channelSeqNo)) {
                     break label1279;
                  }

                  return false;
               }

               Object this$tranType = this.getTranType();
               Object other$tranType = other.getTranType();
               if (this$tranType == null) {
                  if (other$tranType != null) {
                     return false;
                  }
               } else if (!this$tranType.equals(other$tranType)) {
                  return false;
               }

               Object this$drTranType = this.getDrTranType();
               Object other$drTranType = other.getDrTranType();
               if (this$drTranType == null) {
                  if (other$drTranType != null) {
                     return false;
                  }
               } else if (!this$drTranType.equals(other$drTranType)) {
                  return false;
               }

               label1258: {
                  Object this$othDocumentType = this.getOthDocumentType();
                  Object other$othDocumentType = other.getOthDocumentType();
                  if (this$othDocumentType == null) {
                     if (other$othDocumentType == null) {
                        break label1258;
                     }
                  } else if (this$othDocumentType.equals(other$othDocumentType)) {
                     break label1258;
                  }

                  return false;
               }

               label1251: {
                  Object this$othDocumentId = this.getOthDocumentId();
                  Object other$othDocumentId = other.getOthDocumentId();
                  if (this$othDocumentId == null) {
                     if (other$othDocumentId == null) {
                        break label1251;
                     }
                  } else if (this$othDocumentId.equals(other$othDocumentId)) {
                     break label1251;
                  }

                  return false;
               }

               Object this$othBaseAcctNo = this.getOthBaseAcctNo();
               Object other$othBaseAcctNo = other.getOthBaseAcctNo();
               if (this$othBaseAcctNo == null) {
                  if (other$othBaseAcctNo != null) {
                     return false;
                  }
               } else if (!this$othBaseAcctNo.equals(other$othBaseAcctNo)) {
                  return false;
               }

               Object this$othProdType = this.getOthProdType();
               Object other$othProdType = other.getOthProdType();
               if (this$othProdType == null) {
                  if (other$othProdType != null) {
                     return false;
                  }
               } else if (!this$othProdType.equals(other$othProdType)) {
                  return false;
               }

               label1230: {
                  Object this$othAcctCcy = this.getOthAcctCcy();
                  Object other$othAcctCcy = other.getOthAcctCcy();
                  if (this$othAcctCcy == null) {
                     if (other$othAcctCcy == null) {
                        break label1230;
                     }
                  } else if (this$othAcctCcy.equals(other$othAcctCcy)) {
                     break label1230;
                  }

                  return false;
               }

               label1223: {
                  Object this$othAcctSeqNo = this.getOthAcctSeqNo();
                  Object other$othAcctSeqNo = other.getOthAcctSeqNo();
                  if (this$othAcctSeqNo == null) {
                     if (other$othAcctSeqNo == null) {
                        break label1223;
                     }
                  } else if (this$othAcctSeqNo.equals(other$othAcctSeqNo)) {
                     break label1223;
                  }

                  return false;
               }

               Object this$othAcctDesc = this.getOthAcctDesc();
               Object other$othAcctDesc = other.getOthAcctDesc();
               if (this$othAcctDesc == null) {
                  if (other$othAcctDesc != null) {
                     return false;
                  }
               } else if (!this$othAcctDesc.equals(other$othAcctDesc)) {
                  return false;
               }

               label1209: {
                  Object this$othBranch = this.getOthBranch();
                  Object other$othBranch = other.getOthBranch();
                  if (this$othBranch == null) {
                     if (other$othBranch == null) {
                        break label1209;
                     }
                  } else if (this$othBranch.equals(other$othBranch)) {
                     break label1209;
                  }

                  return false;
               }

               Object this$othBankName = this.getOthBankName();
               Object other$othBankName = other.getOthBankName();
               if (this$othBankName == null) {
                  if (other$othBankName != null) {
                     return false;
                  }
               } else if (!this$othBankName.equals(other$othBankName)) {
                  return false;
               }

               label1195: {
                  Object this$othBankCode = this.getOthBankCode();
                  Object other$othBankCode = other.getOthBankCode();
                  if (this$othBankCode == null) {
                     if (other$othBankCode == null) {
                        break label1195;
                     }
                  } else if (this$othBankCode.equals(other$othBankCode)) {
                     break label1195;
                  }

                  return false;
               }

               Object this$othSeqNo = this.getOthSeqNo();
               Object other$othSeqNo = other.getOthSeqNo();
               if (this$othSeqNo == null) {
                  if (other$othSeqNo != null) {
                     return false;
                  }
               } else if (!this$othSeqNo.equals(other$othSeqNo)) {
                  return false;
               }

               Object this$othReference = this.getOthReference();
               Object other$othReference = other.getOthReference();
               if (this$othReference == null) {
                  if (other$othReference != null) {
                     return false;
                  }
               } else if (!this$othReference.equals(other$othReference)) {
                  return false;
               }

               label1174: {
                  Object this$toAcctNo = this.getToAcctNo();
                  Object other$toAcctNo = other.getToAcctNo();
                  if (this$toAcctNo == null) {
                     if (other$toAcctNo == null) {
                        break label1174;
                     }
                  } else if (this$toAcctNo.equals(other$toAcctNo)) {
                     break label1174;
                  }

                  return false;
               }

               label1167: {
                  Object this$toAcctName = this.getToAcctName();
                  Object other$toAcctName = other.getToAcctName();
                  if (this$toAcctName == null) {
                     if (other$toAcctName == null) {
                        break label1167;
                     }
                  } else if (this$toAcctName.equals(other$toAcctName)) {
                     break label1167;
                  }

                  return false;
               }

               Object this$sourceAcctNo = this.getSourceAcctNo();
               Object other$sourceAcctNo = other.getSourceAcctNo();
               if (this$sourceAcctNo == null) {
                  if (other$sourceAcctNo != null) {
                     return false;
                  }
               } else if (!this$sourceAcctNo.equals(other$sourceAcctNo)) {
                  return false;
               }

               Object this$sourceAcctName = this.getSourceAcctName();
               Object other$sourceAcctName = other.getSourceAcctName();
               if (this$sourceAcctName == null) {
                  if (other$sourceAcctName != null) {
                     return false;
                  }
               } else if (!this$sourceAcctName.equals(other$sourceAcctName)) {
                  return false;
               }

               label1146: {
                  Object this$docType = this.getDocType();
                  Object other$docType = other.getDocType();
                  if (this$docType == null) {
                     if (other$docType == null) {
                        break label1146;
                     }
                  } else if (this$docType.equals(other$docType)) {
                     break label1146;
                  }

                  return false;
               }

               label1139: {
                  Object this$prefix = this.getPrefix();
                  Object other$prefix = other.getPrefix();
                  if (this$prefix == null) {
                     if (other$prefix == null) {
                        break label1139;
                     }
                  } else if (this$prefix.equals(other$prefix)) {
                     break label1139;
                  }

                  return false;
               }

               Object this$voucherNo = this.getVoucherNo();
               Object other$voucherNo = other.getVoucherNo();
               if (this$voucherNo == null) {
                  if (other$voucherNo != null) {
                     return false;
                  }
               } else if (!this$voucherNo.equals(other$voucherNo)) {
                  return false;
               }

               Object this$withdrawalType = this.getWithdrawalType();
               Object other$withdrawalType = other.getWithdrawalType();
               if (this$withdrawalType == null) {
                  if (other$withdrawalType != null) {
                     return false;
                  }
               } else if (!this$withdrawalType.equals(other$withdrawalType)) {
                  return false;
               }

               label1118: {
                  Object this$reasonCode = this.getReasonCode();
                  Object other$reasonCode = other.getReasonCode();
                  if (this$reasonCode == null) {
                     if (other$reasonCode == null) {
                        break label1118;
                     }
                  } else if (this$reasonCode.equals(other$reasonCode)) {
                     break label1118;
                  }

                  return false;
               }

               label1111: {
                  Object this$userId = this.getUserId();
                  Object other$userId = other.getUserId();
                  if (this$userId == null) {
                     if (other$userId == null) {
                        break label1111;
                     }
                  } else if (this$userId.equals(other$userId)) {
                     break label1111;
                  }

                  return false;
               }

               Object this$bankSeqNo = this.getBankSeqNo();
               Object other$bankSeqNo = other.getBankSeqNo();
               if (this$bankSeqNo == null) {
                  if (other$bankSeqNo != null) {
                     return false;
                  }
               } else if (!this$bankSeqNo.equals(other$bankSeqNo)) {
                  return false;
               }

               label1097: {
                  Object this$authUserId = this.getAuthUserId();
                  Object other$authUserId = other.getAuthUserId();
                  if (this$authUserId == null) {
                     if (other$authUserId == null) {
                        break label1097;
                     }
                  } else if (this$authUserId.equals(other$authUserId)) {
                     break label1097;
                  }

                  return false;
               }

               Object this$apprUserId = this.getApprUserId();
               Object other$apprUserId = other.getApprUserId();
               if (this$apprUserId == null) {
                  if (other$apprUserId != null) {
                     return false;
                  }
               } else if (!this$apprUserId.equals(other$apprUserId)) {
                  return false;
               }

               label1083: {
                  Object this$bankName = this.getBankName();
                  Object other$bankName = other.getBankName();
                  if (this$bankName == null) {
                     if (other$bankName == null) {
                        break label1083;
                     }
                  } else if (this$bankName.equals(other$bankName)) {
                     break label1083;
                  }

                  return false;
               }

               Object this$bankCode = this.getBankCode();
               Object other$bankCode = other.getBankCode();
               if (this$bankCode == null) {
                  if (other$bankCode != null) {
                     return false;
                  }
               } else if (!this$bankCode.equals(other$bankCode)) {
                  return false;
               }

               Object this$branch = this.getBranch();
               Object other$branch = other.getBranch();
               if (this$branch == null) {
                  if (other$branch != null) {
                     return false;
                  }
               } else if (!this$branch.equals(other$branch)) {
                  return false;
               }

               label1062: {
                  Object this$reference = this.getReference();
                  Object other$reference = other.getReference();
                  if (this$reference == null) {
                     if (other$reference == null) {
                        break label1062;
                     }
                  } else if (this$reference.equals(other$reference)) {
                     break label1062;
                  }

                  return false;
               }

               label1055: {
                  Object this$tranCategory = this.getTranCategory();
                  Object other$tranCategory = other.getTranCategory();
                  if (this$tranCategory == null) {
                     if (other$tranCategory == null) {
                        break label1055;
                     }
                  } else if (this$tranCategory.equals(other$tranCategory)) {
                     break label1055;
                  }

                  return false;
               }

               Object this$effectDate = this.getEffectDate();
               Object other$effectDate = other.getEffectDate();
               if (this$effectDate == null) {
                  if (other$effectDate != null) {
                     return false;
                  }
               } else if (!this$effectDate.equals(other$effectDate)) {
                  return false;
               }

               Object this$reversalTranType = this.getReversalTranType();
               Object other$reversalTranType = other.getReversalTranType();
               if (this$reversalTranType == null) {
                  if (other$reversalTranType != null) {
                     return false;
                  }
               } else if (!this$reversalTranType.equals(other$reversalTranType)) {
                  return false;
               }

               label1034: {
                  Object this$reversalDate = this.getReversalDate();
                  Object other$reversalDate = other.getReversalDate();
                  if (this$reversalDate == null) {
                     if (other$reversalDate == null) {
                        break label1034;
                     }
                  } else if (this$reversalDate.equals(other$reversalDate)) {
                     break label1034;
                  }

                  return false;
               }

               label1027: {
                  Object this$seqNo = this.getSeqNo();
                  Object other$seqNo = other.getSeqNo();
                  if (this$seqNo == null) {
                     if (other$seqNo == null) {
                        break label1027;
                     }
                  } else if (this$seqNo.equals(other$seqNo)) {
                     break label1027;
                  }

                  return false;
               }

               Object this$tranDate = this.getTranDate();
               Object other$tranDate = other.getTranDate();
               if (this$tranDate == null) {
                  if (other$tranDate != null) {
                     return false;
                  }
               } else if (!this$tranDate.equals(other$tranDate)) {
                  return false;
               }

               Object this$tranDesc = this.getTranDesc();
               Object other$tranDesc = other.getTranDesc();
               if (this$tranDesc == null) {
                  if (other$tranDesc != null) {
                     return false;
                  }
               } else if (!this$tranDesc.equals(other$tranDesc)) {
                  return false;
               }

               label1006: {
                  Object this$tranNote = this.getTranNote();
                  Object other$tranNote = other.getTranNote();
                  if (this$tranNote == null) {
                     if (other$tranNote == null) {
                        break label1006;
                     }
                  } else if (this$tranNote.equals(other$tranNote)) {
                     break label1006;
                  }

                  return false;
               }

               label999: {
                  Object this$primaryTranSeqNo = this.getPrimaryTranSeqNo();
                  Object other$primaryTranSeqNo = other.getPrimaryTranSeqNo();
                  if (this$primaryTranSeqNo == null) {
                     if (other$primaryTranSeqNo == null) {
                        break label999;
                     }
                  } else if (this$primaryTranSeqNo.equals(other$primaryTranSeqNo)) {
                     break label999;
                  }

                  return false;
               }

               Object this$fhSeqNo = this.getFhSeqNo();
               Object other$fhSeqNo = other.getFhSeqNo();
               if (this$fhSeqNo == null) {
                  if (other$fhSeqNo != null) {
                     return false;
                  }
               } else if (!this$fhSeqNo.equals(other$fhSeqNo)) {
                  return false;
               }

               label985: {
                  Object this$settlementDate = this.getSettlementDate();
                  Object other$settlementDate = other.getSettlementDate();
                  if (this$settlementDate == null) {
                     if (other$settlementDate == null) {
                        break label985;
                     }
                  } else if (this$settlementDate.equals(other$settlementDate)) {
                     break label985;
                  }

                  return false;
               }

               Object this$contraEquivAmt = this.getContraEquivAmt();
               Object other$contraEquivAmt = other.getContraEquivAmt();
               if (this$contraEquivAmt == null) {
                  if (other$contraEquivAmt != null) {
                     return false;
                  }
               } else if (!this$contraEquivAmt.equals(other$contraEquivAmt)) {
                  return false;
               }

               label971: {
                  Object this$baseEquivAmt = this.getBaseEquivAmt();
                  Object other$baseEquivAmt = other.getBaseEquivAmt();
                  if (this$baseEquivAmt == null) {
                     if (other$baseEquivAmt == null) {
                        break label971;
                     }
                  } else if (this$baseEquivAmt.equals(other$baseEquivAmt)) {
                     break label971;
                  }

                  return false;
               }

               Object this$crossRate = this.getCrossRate();
               Object other$crossRate = other.getCrossRate();
               if (this$crossRate == null) {
                  if (other$crossRate != null) {
                     return false;
                  }
               } else if (!this$crossRate.equals(other$crossRate)) {
                  return false;
               }

               Object this$fromCcy = this.getFromCcy();
               Object other$fromCcy = other.getFromCcy();
               if (this$fromCcy == null) {
                  if (other$fromCcy != null) {
                     return false;
                  }
               } else if (!this$fromCcy.equals(other$fromCcy)) {
                  return false;
               }

               label950: {
                  Object this$fromAmount = this.getFromAmount();
                  Object other$fromAmount = other.getFromAmount();
                  if (this$fromAmount == null) {
                     if (other$fromAmount == null) {
                        break label950;
                     }
                  } else if (this$fromAmount.equals(other$fromAmount)) {
                     break label950;
                  }

                  return false;
               }

               label943: {
                  Object this$fromRateFlag = this.getFromRateFlag();
                  Object other$fromRateFlag = other.getFromRateFlag();
                  if (this$fromRateFlag == null) {
                     if (other$fromRateFlag == null) {
                        break label943;
                     }
                  } else if (this$fromRateFlag.equals(other$fromRateFlag)) {
                     break label943;
                  }

                  return false;
               }

               Object this$fromXrate = this.getFromXrate();
               Object other$fromXrate = other.getFromXrate();
               if (this$fromXrate == null) {
                  if (other$fromXrate != null) {
                     return false;
                  }
               } else if (!this$fromXrate.equals(other$fromXrate)) {
                  return false;
               }

               Object this$toCcy = this.getToCcy();
               Object other$toCcy = other.getToCcy();
               if (this$toCcy == null) {
                  if (other$toCcy != null) {
                     return false;
                  }
               } else if (!this$toCcy.equals(other$toCcy)) {
                  return false;
               }

               label922: {
                  Object this$toAmount = this.getToAmount();
                  Object other$toAmount = other.getToAmount();
                  if (this$toAmount == null) {
                     if (other$toAmount == null) {
                        break label922;
                     }
                  } else if (this$toAmount.equals(other$toAmount)) {
                     break label922;
                  }

                  return false;
               }

               label915: {
                  Object this$toRateFlag = this.getToRateFlag();
                  Object other$toRateFlag = other.getToRateFlag();
                  if (this$toRateFlag == null) {
                     if (other$toRateFlag == null) {
                        break label915;
                     }
                  } else if (this$toRateFlag.equals(other$toRateFlag)) {
                     break label915;
                  }

                  return false;
               }

               Object this$toXrate = this.getToXrate();
               Object other$toXrate = other.getToXrate();
               if (this$toXrate == null) {
                  if (other$toXrate != null) {
                     return false;
                  }
               } else if (!this$toXrate.equals(other$toXrate)) {
                  return false;
               }

               Object this$ovToAmount = this.getOvToAmount();
               Object other$ovToAmount = other.getOvToAmount();
               if (this$ovToAmount == null) {
                  if (other$ovToAmount != null) {
                     return false;
                  }
               } else if (!this$ovToAmount.equals(other$ovToAmount)) {
                  return false;
               }

               label894: {
                  Object this$rateFlag = this.getRateFlag();
                  Object other$rateFlag = other.getRateFlag();
                  if (this$rateFlag == null) {
                     if (other$rateFlag == null) {
                        break label894;
                     }
                  } else if (this$rateFlag.equals(other$rateFlag)) {
                     break label894;
                  }

                  return false;
               }

               label887: {
                  Object this$quoteType = this.getQuoteType();
                  Object other$quoteType = other.getQuoteType();
                  if (this$quoteType == null) {
                     if (other$quoteType == null) {
                        break label887;
                     }
                  } else if (this$quoteType.equals(other$quoteType)) {
                     break label887;
                  }

                  return false;
               }

               Object this$rateType = this.getRateType();
               Object other$rateType = other.getRateType();
               if (this$rateType == null) {
                  if (other$rateType != null) {
                     return false;
                  }
               } else if (!this$rateType.equals(other$rateType)) {
                  return false;
               }

               label873: {
                  Object this$toId = this.getToId();
                  Object other$toId = other.getToId();
                  if (this$toId == null) {
                     if (other$toId == null) {
                        break label873;
                     }
                  } else if (this$toId.equals(other$toId)) {
                     break label873;
                  }

                  return false;
               }

               Object this$terminalId = this.getTerminalId();
               Object other$terminalId = other.getTerminalId();
               if (this$terminalId == null) {
                  if (other$terminalId != null) {
                     return false;
                  }
               } else if (!this$terminalId.equals(other$terminalId)) {
                  return false;
               }

               label859: {
                  Object this$traceId = this.getTraceId();
                  Object other$traceId = other.getTraceId();
                  if (this$traceId == null) {
                     if (other$traceId == null) {
                        break label859;
                     }
                  } else if (this$traceId.equals(other$traceId)) {
                     break label859;
                  }

                  return false;
               }

               Object this$narrative = this.getNarrative();
               Object other$narrative = other.getNarrative();
               if (this$narrative == null) {
                  if (other$narrative != null) {
                     return false;
                  }
               } else if (!this$narrative.equals(other$narrative)) {
                  return false;
               }

               Object this$cashItem = this.getCashItem();
               Object other$cashItem = other.getCashItem();
               if (this$cashItem == null) {
                  if (other$cashItem != null) {
                     return false;
                  }
               } else if (!this$cashItem.equals(other$cashItem)) {
                  return false;
               }

               label838: {
                  Object this$tranStatus = this.getTranStatus();
                  Object other$tranStatus = other.getTranStatus();
                  if (this$tranStatus == null) {
                     if (other$tranStatus == null) {
                        break label838;
                     }
                  } else if (this$tranStatus.equals(other$tranStatus)) {
                     break label838;
                  }

                  return false;
               }

               label831: {
                  Object this$profitCenter = this.getProfitCenter();
                  Object other$profitCenter = other.getProfitCenter();
                  if (this$profitCenter == null) {
                     if (other$profitCenter == null) {
                        break label831;
                     }
                  } else if (this$profitCenter.equals(other$profitCenter)) {
                     break label831;
                  }

                  return false;
               }

               Object this$businessUnit = this.getBusinessUnit();
               Object other$businessUnit = other.getBusinessUnit();
               if (this$businessUnit == null) {
                  if (other$businessUnit != null) {
                     return false;
                  }
               } else if (!this$businessUnit.equals(other$businessUnit)) {
                  return false;
               }

               Object this$sourceModule = this.getSourceModule();
               Object other$sourceModule = other.getSourceModule();
               if (this$sourceModule == null) {
                  if (other$sourceModule != null) {
                     return false;
                  }
               } else if (!this$sourceModule.equals(other$sourceModule)) {
                  return false;
               }

               label810: {
                  Object this$eventType = this.getEventType();
                  Object other$eventType = other.getEventType();
                  if (this$eventType == null) {
                     if (other$eventType == null) {
                        break label810;
                     }
                  } else if (this$eventType.equals(other$eventType)) {
                     break label810;
                  }

                  return false;
               }

               label803: {
                  Object this$ccy = this.getCcy();
                  Object other$ccy = other.getCcy();
                  if (this$ccy == null) {
                     if (other$ccy == null) {
                        break label803;
                     }
                  } else if (this$ccy.equals(other$ccy)) {
                     break label803;
                  }

                  return false;
               }

               Object this$crDrMaintInd = this.getCrDrMaintInd();
               Object other$crDrMaintInd = other.getCrDrMaintInd();
               if (this$crDrMaintInd == null) {
                  if (other$crDrMaintInd != null) {
                     return false;
                  }
               } else if (!this$crDrMaintInd.equals(other$crDrMaintInd)) {
                  return false;
               }

               Object this$balType = this.getBalType();
               Object other$balType = other.getBalType();
               if (this$balType == null) {
                  if (other$balType != null) {
                     return false;
                  }
               } else if (!this$balType.equals(other$balType)) {
                  return false;
               }

               label782: {
                  Object this$printCnt = this.getPrintCnt();
                  Object other$printCnt = other.getPrintCnt();
                  if (this$printCnt == null) {
                     if (other$printCnt == null) {
                        break label782;
                     }
                  } else if (this$printCnt.equals(other$printCnt)) {
                     break label782;
                  }

                  return false;
               }

               label775: {
                  Object this$batchNo = this.getBatchNo();
                  Object other$batchNo = other.getBatchNo();
                  if (this$batchNo == null) {
                     if (other$batchNo == null) {
                        break label775;
                     }
                  } else if (this$batchNo.equals(other$batchNo)) {
                     break label775;
                  }

                  return false;
               }

               Object this$bizType = this.getBizType();
               Object other$bizType = other.getBizType();
               if (this$bizType == null) {
                  if (other$bizType != null) {
                     return false;
                  }
               } else if (!this$bizType.equals(other$bizType)) {
                  return false;
               }

               label761: {
                  Object this$pbkUpdFlag = this.getPbkUpdFlag();
                  Object other$pbkUpdFlag = other.getPbkUpdFlag();
                  if (this$pbkUpdFlag == null) {
                     if (other$pbkUpdFlag == null) {
                        break label761;
                     }
                  } else if (this$pbkUpdFlag.equals(other$pbkUpdFlag)) {
                     break label761;
                  }

                  return false;
               }

               Object this$sourceType = this.getSourceType();
               Object other$sourceType = other.getSourceType();
               if (this$sourceType == null) {
                  if (other$sourceType != null) {
                     return false;
                  }
               } else if (!this$sourceType.equals(other$sourceType)) {
                  return false;
               }

               label747: {
                  Object this$reversal = this.getReversal();
                  Object other$reversal = other.getReversal();
                  if (this$reversal == null) {
                     if (other$reversal == null) {
                        break label747;
                     }
                  } else if (this$reversal.equals(other$reversal)) {
                     break label747;
                  }

                  return false;
               }

               Object this$payUnit = this.getPayUnit();
               Object other$payUnit = other.getPayUnit();
               if (this$payUnit == null) {
                  if (other$payUnit != null) {
                     return false;
                  }
               } else if (!this$payUnit.equals(other$payUnit)) {
                  return false;
               }

               Object this$tranTimestamp = this.getTranTimestamp();
               Object other$tranTimestamp = other.getTranTimestamp();
               if (this$tranTimestamp == null) {
                  if (other$tranTimestamp != null) {
                     return false;
                  }
               } else if (!this$tranTimestamp.equals(other$tranTimestamp)) {
                  return false;
               }

               label726: {
                  Object this$servCharge = this.getServCharge();
                  Object other$servCharge = other.getServCharge();
                  if (this$servCharge == null) {
                     if (other$servCharge == null) {
                        break label726;
                     }
                  } else if (this$servCharge.equals(other$servCharge)) {
                     break label726;
                  }

                  return false;
               }

               label719: {
                  Object this$company = this.getCompany();
                  Object other$company = other.getCompany();
                  if (this$company == null) {
                     if (other$company == null) {
                        break label719;
                     }
                  } else if (this$company.equals(other$company)) {
                     break label719;
                  }

                  return false;
               }

               Object this$acctOpenDate = this.getAcctOpenDate();
               Object other$acctOpenDate = other.getAcctOpenDate();
               if (this$acctOpenDate == null) {
                  if (other$acctOpenDate != null) {
                     return false;
                  }
               } else if (!this$acctOpenDate.equals(other$acctOpenDate)) {
                  return false;
               }

               Object this$term = this.getTerm();
               Object other$term = other.getTerm();
               if (this$term == null) {
                  if (other$term != null) {
                     return false;
                  }
               } else if (!this$term.equals(other$term)) {
                  return false;
               }

               label698: {
                  Object this$termType = this.getTermType();
                  Object other$termType = other.getTermType();
                  if (this$termType == null) {
                     if (other$termType == null) {
                        break label698;
                     }
                  } else if (this$termType.equals(other$termType)) {
                     break label698;
                  }

                  return false;
               }

               label691: {
                  Object this$othAcctName = this.getOthAcctName();
                  Object other$othAcctName = other.getOthAcctName();
                  if (this$othAcctName == null) {
                     if (other$othAcctName == null) {
                        break label691;
                     }
                  } else if (this$othAcctName.equals(other$othAcctName)) {
                     break label691;
                  }

                  return false;
               }

               Object this$realRate = this.getRealRate();
               Object other$realRate = other.getRealRate();
               if (this$realRate == null) {
                  if (other$realRate != null) {
                     return false;
                  }
               } else if (!this$realRate.equals(other$realRate)) {
                  return false;
               }

               Object this$actualRate = this.getActualRate();
               Object other$actualRate = other.getActualRate();
               if (this$actualRate == null) {
                  if (other$actualRate != null) {
                     return false;
                  }
               } else if (!this$actualRate.equals(other$actualRate)) {
                  return false;
               }

               label670: {
                  Object this$othRealBaseAcctNo = this.getOthRealBaseAcctNo();
                  Object other$othRealBaseAcctNo = other.getOthRealBaseAcctNo();
                  if (this$othRealBaseAcctNo == null) {
                     if (other$othRealBaseAcctNo == null) {
                        break label670;
                     }
                  } else if (this$othRealBaseAcctNo.equals(other$othRealBaseAcctNo)) {
                     break label670;
                  }

                  return false;
               }

               label663: {
                  Object this$othRealTranName = this.getOthRealTranName();
                  Object other$othRealTranName = other.getOthRealTranName();
                  if (this$othRealTranName == null) {
                     if (other$othRealTranName == null) {
                        break label663;
                     }
                  } else if (this$othRealTranName.equals(other$othRealTranName)) {
                     break label663;
                  }

                  return false;
               }

               Object this$branchName = this.getBranchName();
               Object other$branchName = other.getBranchName();
               if (this$branchName == null) {
                  if (other$branchName != null) {
                     return false;
                  }
               } else if (!this$branchName.equals(other$branchName)) {
                  return false;
               }

               label649: {
                  Object this$tdaBaseAcctNo = this.getTdaBaseAcctNo();
                  Object other$tdaBaseAcctNo = other.getTdaBaseAcctNo();
                  if (this$tdaBaseAcctNo == null) {
                     if (other$tdaBaseAcctNo == null) {
                        break label649;
                     }
                  } else if (this$tdaBaseAcctNo.equals(other$tdaBaseAcctNo)) {
                     break label649;
                  }

                  return false;
               }

               Object this$stageCode = this.getStageCode();
               Object other$stageCode = other.getStageCode();
               if (this$stageCode == null) {
                  if (other$stageCode != null) {
                     return false;
                  }
               } else if (!this$stageCode.equals(other$stageCode)) {
                  return false;
               }

               label635: {
                  Object this$piFlag = this.getPiFlag();
                  Object other$piFlag = other.getPiFlag();
                  if (this$piFlag == null) {
                     if (other$piFlag == null) {
                        break label635;
                     }
                  } else if (this$piFlag.equals(other$piFlag)) {
                     break label635;
                  }

                  return false;
               }

               Object this$stageCodeDesc = this.getStageCodeDesc();
               Object other$stageCodeDesc = other.getStageCodeDesc();
               if (this$stageCodeDesc == null) {
                  if (other$stageCodeDesc != null) {
                     return false;
                  }
               } else if (!this$stageCodeDesc.equals(other$stageCodeDesc)) {
                  return false;
               }

               Object this$realTranBranchName = this.getRealTranBranchName();
               Object other$realTranBranchName = other.getRealTranBranchName();
               if (this$realTranBranchName == null) {
                  if (other$realTranBranchName != null) {
                     return false;
                  }
               } else if (!this$realTranBranchName.equals(other$realTranBranchName)) {
                  return false;
               }

               label614: {
                  Object this$trfDate = this.getTrfDate();
                  Object other$trfDate = other.getTrfDate();
                  if (this$trfDate == null) {
                     if (other$trfDate == null) {
                        break label614;
                     }
                  } else if (this$trfDate.equals(other$trfDate)) {
                     break label614;
                  }

                  return false;
               }

               label607: {
                  Object this$trfRate = this.getTrfRate();
                  Object other$trfRate = other.getTrfRate();
                  if (this$trfRate == null) {
                     if (other$trfRate == null) {
                        break label607;
                     }
                  } else if (this$trfRate.equals(other$trfRate)) {
                     break label607;
                  }

                  return false;
               }

               Object this$matureDate = this.getMatureDate();
               Object other$matureDate = other.getMatureDate();
               if (this$matureDate == null) {
                  if (other$matureDate != null) {
                     return false;
                  }
               } else if (!this$matureDate.equals(other$matureDate)) {
                  return false;
               }

               Object this$realBranch = this.getRealBranch();
               Object other$realBranch = other.getRealBranch();
               if (this$realBranch == null) {
                  if (other$realBranch != null) {
                     return false;
                  }
               } else if (!this$realBranch.equals(other$realBranch)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100011Out.TranHistArray;
      }
      public String toString() {
         return "Core1400100011Out.TranHistArray(documentType=" + this.getDocumentType() + ", documentId=" + this.getDocumentId() + ", chClientName=" + this.getChClientName() + ", clientNo=" + this.getClientNo() + ", clientType=" + this.getClientType() + ", clientName=" + this.getClientName() + ", baseAcctNo=" + this.getBaseAcctNo() + ", prodType=" + this.getProdType() + ", acctCcy=" + this.getAcctCcy() + ", acctSeqNo=" + this.getAcctSeqNo() + ", acctBranch=" + this.getAcctBranch() + ", amtType=" + this.getAmtType() + ", previousBalAmt=" + this.getPreviousBalAmt() + ", actualBal=" + this.getActualBal() + ", acctDesc=" + this.getAcctDesc() + ", tranAmt=" + this.getTranAmt() + ", channelSeqNo=" + this.getChannelSeqNo() + ", tranType=" + this.getTranType() + ", drTranType=" + this.getDrTranType() + ", othDocumentType=" + this.getOthDocumentType() + ", othDocumentId=" + this.getOthDocumentId() + ", othBaseAcctNo=" + this.getOthBaseAcctNo() + ", othProdType=" + this.getOthProdType() + ", othAcctCcy=" + this.getOthAcctCcy() + ", othAcctSeqNo=" + this.getOthAcctSeqNo() + ", othAcctDesc=" + this.getOthAcctDesc() + ", othBranch=" + this.getOthBranch() + ", othBankName=" + this.getOthBankName() + ", othBankCode=" + this.getOthBankCode() + ", othSeqNo=" + this.getOthSeqNo() + ", othReference=" + this.getOthReference() + ", toAcctNo=" + this.getToAcctNo() + ", toAcctName=" + this.getToAcctName() + ", sourceAcctNo=" + this.getSourceAcctNo() + ", sourceAcctName=" + this.getSourceAcctName() + ", docType=" + this.getDocType() + ", prefix=" + this.getPrefix() + ", voucherNo=" + this.getVoucherNo() + ", withdrawalType=" + this.getWithdrawalType() + ", reasonCode=" + this.getReasonCode() + ", userId=" + this.getUserId() + ", bankSeqNo=" + this.getBankSeqNo() + ", authUserId=" + this.getAuthUserId() + ", apprUserId=" + this.getApprUserId() + ", bankName=" + this.getBankName() + ", bankCode=" + this.getBankCode() + ", branch=" + this.getBranch() + ", reference=" + this.getReference() + ", tranCategory=" + this.getTranCategory() + ", effectDate=" + this.getEffectDate() + ", reversalTranType=" + this.getReversalTranType() + ", reversalDate=" + this.getReversalDate() + ", seqNo=" + this.getSeqNo() + ", tranDate=" + this.getTranDate() + ", tranDesc=" + this.getTranDesc() + ", tranNote=" + this.getTranNote() + ", primaryTranSeqNo=" + this.getPrimaryTranSeqNo() + ", fhSeqNo=" + this.getFhSeqNo() + ", settlementDate=" + this.getSettlementDate() + ", contraEquivAmt=" + this.getContraEquivAmt() + ", baseEquivAmt=" + this.getBaseEquivAmt() + ", crossRate=" + this.getCrossRate() + ", fromCcy=" + this.getFromCcy() + ", fromAmount=" + this.getFromAmount() + ", fromRateFlag=" + this.getFromRateFlag() + ", fromXrate=" + this.getFromXrate() + ", toCcy=" + this.getToCcy() + ", toAmount=" + this.getToAmount() + ", toRateFlag=" + this.getToRateFlag() + ", toXrate=" + this.getToXrate() + ", ovToAmount=" + this.getOvToAmount() + ", rateFlag=" + this.getRateFlag() + ", quoteType=" + this.getQuoteType() + ", rateType=" + this.getRateType() + ", toId=" + this.getToId() + ", terminalId=" + this.getTerminalId() + ", traceId=" + this.getTraceId() + ", narrative=" + this.getNarrative() + ", cashItem=" + this.getCashItem() + ", tranStatus=" + this.getTranStatus() + ", profitCenter=" + this.getProfitCenter() + ", businessUnit=" + this.getBusinessUnit() + ", sourceModule=" + this.getSourceModule() + ", eventType=" + this.getEventType() + ", ccy=" + this.getCcy() + ", crDrMaintInd=" + this.getCrDrMaintInd() + ", balType=" + this.getBalType() + ", printCnt=" + this.getPrintCnt() + ", batchNo=" + this.getBatchNo() + ", bizType=" + this.getBizType() + ", pbkUpdFlag=" + this.getPbkUpdFlag() + ", sourceType=" + this.getSourceType() + ", reversal=" + this.getReversal() + ", payUnit=" + this.getPayUnit() + ", tranTimestamp=" + this.getTranTimestamp() + ", servCharge=" + this.getServCharge() + ", company=" + this.getCompany() + ", acctOpenDate=" + this.getAcctOpenDate() + ", term=" + this.getTerm() + ", termType=" + this.getTermType() + ", othAcctName=" + this.getOthAcctName() + ", realRate=" + this.getRealRate() + ", actualRate=" + this.getActualRate() + ", othRealBaseAcctNo=" + this.getOthRealBaseAcctNo() + ", othRealTranName=" + this.getOthRealTranName() + ", branchName=" + this.getBranchName() + ", tdaBaseAcctNo=" + this.getTdaBaseAcctNo() + ", stageCode=" + this.getStageCode() + ", piFlag=" + this.getPiFlag() + ", stageCodeDesc=" + this.getStageCodeDesc() + ", realTranBranchName=" + this.getRealTranBranchName() + ", trfDate=" + this.getTrfDate() + ", trfRate=" + this.getTrfRate() + ", matureDate=" + this.getMatureDate() + ", realBranch=" + this.getRealBranch() + ")";
      }
   }
}
