package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;

@MessageIn
public class Core1400100036In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400100036In.Body body;

   public Core1400100036In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400100036In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400100036In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100036In)) {
         return false;
      } else {
         Core1400100036In other = (Core1400100036In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100036In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "客户号",
         notNull = true,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "交易类型",
         notNull = false,
         length = "10",
         remark = "交易类型",
         maxSize = 10
      )
      private String tranType;
      @V(
         desc = "交易币种",
         notNull = false,
         length = "3",
         remark = "交易币种",
         maxSize = 3
      )
      private String tranCcy;
      @V(
         desc = "交易金额",
         notNull = false,
         length = "17",
         remark = "交易金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal tranAmt;

      public String getClientNo() {
         return this.clientNo;
      }

      public String getTranType() {
         return this.tranType;
      }

      public String getTranCcy() {
         return this.tranCcy;
      }

      public BigDecimal getTranAmt() {
         return this.tranAmt;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setTranType(String tranType) {
         this.tranType = tranType;
      }

      public void setTranCcy(String tranCcy) {
         this.tranCcy = tranCcy;
      }

      public void setTranAmt(BigDecimal tranAmt) {
         this.tranAmt = tranAmt;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100036In.Body)) {
            return false;
         } else {
            Core1400100036In.Body other = (Core1400100036In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label59: {
                  Object this$clientNo = this.getClientNo();
                  Object other$clientNo = other.getClientNo();
                  if (this$clientNo == null) {
                     if (other$clientNo == null) {
                        break label59;
                     }
                  } else if (this$clientNo.equals(other$clientNo)) {
                     break label59;
                  }

                  return false;
               }

               Object this$tranType = this.getTranType();
               Object other$tranType = other.getTranType();
               if (this$tranType == null) {
                  if (other$tranType != null) {
                     return false;
                  }
               } else if (!this$tranType.equals(other$tranType)) {
                  return false;
               }

               Object this$tranCcy = this.getTranCcy();
               Object other$tranCcy = other.getTranCcy();
               if (this$tranCcy == null) {
                  if (other$tranCcy != null) {
                     return false;
                  }
               } else if (!this$tranCcy.equals(other$tranCcy)) {
                  return false;
               }

               Object this$tranAmt = this.getTranAmt();
               Object other$tranAmt = other.getTranAmt();
               if (this$tranAmt == null) {
                  if (other$tranAmt != null) {
                     return false;
                  }
               } else if (!this$tranAmt.equals(other$tranAmt)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100036In.Body;
      }
      public String toString() {
         return "Core1400100036In.Body(clientNo=" + this.getClientNo() + ", tranType=" + this.getTranType() + ", tranCcy=" + this.getTranCcy() + ", tranAmt=" + this.getTranAmt() + ")";
      }
   }
}
