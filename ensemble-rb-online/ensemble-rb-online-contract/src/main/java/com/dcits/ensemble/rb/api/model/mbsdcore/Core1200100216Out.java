package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;

@MessageOut
public class Core1200100216Out extends EnsResponse {
   private static final long serialVersionUID = 1L;

   public String toString() {
      return "Core1200100216Out()";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200100216Out)) {
         return false;
      } else {
         Core1200100216Out other = (Core1200100216Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else {
            return super.equals(o);
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200100216Out;
   }
}
