package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10009406In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10009406Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore10009406 {
   String URL = "/rb/fin/inner/wtd";

   
   @ApiRemark("新增")
   @ApiDesc("核心为内部户现金支取交易场景提供接口，业务逻辑类同跨法人活期支取，资产账特处理")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1000",
      messageCode = "9406"
   )
   @BusinessCategory("RB03-金融交易")
   @FunctionCategory("RB15-内部账")
   @ConsumeSys("TLE/137")
   @ApiUseStatus("PROJECT-项目")
   Core10009406Out runService(Core10009406In var1);
}
