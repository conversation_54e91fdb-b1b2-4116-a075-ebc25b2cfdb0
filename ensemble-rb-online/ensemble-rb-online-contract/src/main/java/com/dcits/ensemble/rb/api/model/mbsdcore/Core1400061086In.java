package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;

@MessageIn
public class Core1400061086In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400061086In.Body body;

   public Core1400061086In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400061086In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400061086In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400061086In)) {
         return false;
      } else {
         Core1400061086In other = (Core1400061086In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400061086In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "所属机构号",
         notNull = false,
         length = "50",
         remark = "所属机构号",
         maxSize = 50
      )
      private String branch;
      @V(
         desc = "证件类型",
         notNull = false,
         length = "3",
         remark = "证件类型",
         maxSize = 3
      )
      private String documentType;
      @V(
         desc = "证件号码",
         notNull = false,
         length = "50",
         remark = "证件号码",
         maxSize = 50
      )
      private String documentId;
      @V(
         desc = "客户名称",
         notNull = false,
         length = "200",
         remark = "客户名称",
         maxSize = 200
      )
      private String clientName;
      @V(
         desc = "国籍",
         notNull = false,
         length = "10",
         remark = "国籍",
         maxSize = 10
      )
      private String national;
      @V(
         desc = "目的地",
         notNull = false,
         length = "100",
         remark = "目的地",
         maxSize = 100
      )
      private String bourn;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "金额",
         notNull = false,
         length = "17",
         remark = "金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal amt;
      @V(
         desc = "生效日期",
         notNull = false,
         remark = "生效日期"
      )
      private String effectDate;
      @V(
         desc = "凭证号码",
         notNull = false,
         length = "50",
         remark = "凭证号码",
         maxSize = 50
      )
      private String voucherNo;
      @V(
         desc = "凭证类型",
         notNull = false,
         length = "10",
         remark = "凭证类型",
         maxSize = 10
      )
      private String docType;
      @V(
         desc = "前缀",
         notNull = false,
         length = "10",
         remark = "前缀",
         maxSize = 10
      )
      private String prefix;
      @V(
         desc = "携带证编号",
         notNull = false,
         length = "10",
         remark = "携带证编号",
         maxSize = 10
      )
      private String takeDocId;

      public String getBranch() {
         return this.branch;
      }

      public String getDocumentType() {
         return this.documentType;
      }

      public String getDocumentId() {
         return this.documentId;
      }

      public String getClientName() {
         return this.clientName;
      }

      public String getNational() {
         return this.national;
      }

      public String getBourn() {
         return this.bourn;
      }

      public String getCcy() {
         return this.ccy;
      }

      public BigDecimal getAmt() {
         return this.amt;
      }

      public String getEffectDate() {
         return this.effectDate;
      }

      public String getVoucherNo() {
         return this.voucherNo;
      }

      public String getDocType() {
         return this.docType;
      }

      public String getPrefix() {
         return this.prefix;
      }

      public String getTakeDocId() {
         return this.takeDocId;
      }

      public void setBranch(String branch) {
         this.branch = branch;
      }

      public void setDocumentType(String documentType) {
         this.documentType = documentType;
      }

      public void setDocumentId(String documentId) {
         this.documentId = documentId;
      }

      public void setClientName(String clientName) {
         this.clientName = clientName;
      }

      public void setNational(String national) {
         this.national = national;
      }

      public void setBourn(String bourn) {
         this.bourn = bourn;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setAmt(BigDecimal amt) {
         this.amt = amt;
      }

      public void setEffectDate(String effectDate) {
         this.effectDate = effectDate;
      }

      public void setVoucherNo(String voucherNo) {
         this.voucherNo = voucherNo;
      }

      public void setDocType(String docType) {
         this.docType = docType;
      }

      public void setPrefix(String prefix) {
         this.prefix = prefix;
      }

      public void setTakeDocId(String takeDocId) {
         this.takeDocId = takeDocId;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400061086In.Body)) {
            return false;
         } else {
            Core1400061086In.Body other = (Core1400061086In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label167: {
                  Object this$branch = this.getBranch();
                  Object other$branch = other.getBranch();
                  if (this$branch == null) {
                     if (other$branch == null) {
                        break label167;
                     }
                  } else if (this$branch.equals(other$branch)) {
                     break label167;
                  }

                  return false;
               }

               Object this$documentType = this.getDocumentType();
               Object other$documentType = other.getDocumentType();
               if (this$documentType == null) {
                  if (other$documentType != null) {
                     return false;
                  }
               } else if (!this$documentType.equals(other$documentType)) {
                  return false;
               }

               label153: {
                  Object this$documentId = this.getDocumentId();
                  Object other$documentId = other.getDocumentId();
                  if (this$documentId == null) {
                     if (other$documentId == null) {
                        break label153;
                     }
                  } else if (this$documentId.equals(other$documentId)) {
                     break label153;
                  }

                  return false;
               }

               Object this$clientName = this.getClientName();
               Object other$clientName = other.getClientName();
               if (this$clientName == null) {
                  if (other$clientName != null) {
                     return false;
                  }
               } else if (!this$clientName.equals(other$clientName)) {
                  return false;
               }

               label139: {
                  Object this$national = this.getNational();
                  Object other$national = other.getNational();
                  if (this$national == null) {
                     if (other$national == null) {
                        break label139;
                     }
                  } else if (this$national.equals(other$national)) {
                     break label139;
                  }

                  return false;
               }

               Object this$bourn = this.getBourn();
               Object other$bourn = other.getBourn();
               if (this$bourn == null) {
                  if (other$bourn != null) {
                     return false;
                  }
               } else if (!this$bourn.equals(other$bourn)) {
                  return false;
               }

               label125: {
                  Object this$ccy = this.getCcy();
                  Object other$ccy = other.getCcy();
                  if (this$ccy == null) {
                     if (other$ccy == null) {
                        break label125;
                     }
                  } else if (this$ccy.equals(other$ccy)) {
                     break label125;
                  }

                  return false;
               }

               label118: {
                  Object this$amt = this.getAmt();
                  Object other$amt = other.getAmt();
                  if (this$amt == null) {
                     if (other$amt == null) {
                        break label118;
                     }
                  } else if (this$amt.equals(other$amt)) {
                     break label118;
                  }

                  return false;
               }

               Object this$effectDate = this.getEffectDate();
               Object other$effectDate = other.getEffectDate();
               if (this$effectDate == null) {
                  if (other$effectDate != null) {
                     return false;
                  }
               } else if (!this$effectDate.equals(other$effectDate)) {
                  return false;
               }

               label104: {
                  Object this$voucherNo = this.getVoucherNo();
                  Object other$voucherNo = other.getVoucherNo();
                  if (this$voucherNo == null) {
                     if (other$voucherNo == null) {
                        break label104;
                     }
                  } else if (this$voucherNo.equals(other$voucherNo)) {
                     break label104;
                  }

                  return false;
               }

               label97: {
                  Object this$docType = this.getDocType();
                  Object other$docType = other.getDocType();
                  if (this$docType == null) {
                     if (other$docType == null) {
                        break label97;
                     }
                  } else if (this$docType.equals(other$docType)) {
                     break label97;
                  }

                  return false;
               }

               Object this$prefix = this.getPrefix();
               Object other$prefix = other.getPrefix();
               if (this$prefix == null) {
                  if (other$prefix != null) {
                     return false;
                  }
               } else if (!this$prefix.equals(other$prefix)) {
                  return false;
               }

               Object this$takeDocId = this.getTakeDocId();
               Object other$takeDocId = other.getTakeDocId();
               if (this$takeDocId == null) {
                  if (other$takeDocId != null) {
                     return false;
                  }
               } else if (!this$takeDocId.equals(other$takeDocId)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400061086In.Body;
      }
      public String toString() {
         return "Core1400061086In.Body(branch=" + this.getBranch() + ", documentType=" + this.getDocumentType() + ", documentId=" + this.getDocumentId() + ", clientName=" + this.getClientName() + ", national=" + this.getNational() + ", bourn=" + this.getBourn() + ", ccy=" + this.getCcy() + ", amt=" + this.getAmt() + ", effectDate=" + this.getEffectDate() + ", voucherNo=" + this.getVoucherNo() + ", docType=" + this.getDocType() + ", prefix=" + this.getPrefix() + ", takeDocId=" + this.getTakeDocId() + ")";
      }
   }
}
