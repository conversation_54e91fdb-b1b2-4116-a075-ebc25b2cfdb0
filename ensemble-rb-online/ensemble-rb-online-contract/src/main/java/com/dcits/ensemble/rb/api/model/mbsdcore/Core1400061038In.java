package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400061038In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400061038In.Body body;

   public Core1400061038In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400061038In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400061038In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400061038In)) {
         return false;
      } else {
         Core1400061038In other = (Core1400061038In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400061038In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "限额编码",
         notNull = false,
         length = "500",
         remark = "限额编码",
         maxSize = 500
      )
      private String limitRef;
      @V(
         desc = "借贷标识",
         notNull = false,
         length = "1",
         inDesc = "C-贷 ,D-借",
         remark = "借贷标识",
         maxSize = 1
      )
      private String crDrMaintInd;
      @V(
         desc = "限额类型",
         notNull = false,
         length = "2",
         inDesc = "PD-每天累计金额限制,PT-单笔交易,PM-每月累计金额限制,PY-每年累计金额限制,PW-每周限额,PC-自定义限额,ND-每天累计笔数限制,NY-每年累计笔数限制",
         remark = "限制金额或者限制笔数的类型",
         maxSize = 2
      )
      private String limitType;

      public String getClientNo() {
         return this.clientNo;
      }

      public String getLimitRef() {
         return this.limitRef;
      }

      public String getCrDrMaintInd() {
         return this.crDrMaintInd;
      }

      public String getLimitType() {
         return this.limitType;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setLimitRef(String limitRef) {
         this.limitRef = limitRef;
      }

      public void setCrDrMaintInd(String crDrMaintInd) {
         this.crDrMaintInd = crDrMaintInd;
      }

      public void setLimitType(String limitType) {
         this.limitType = limitType;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400061038In.Body)) {
            return false;
         } else {
            Core1400061038In.Body other = (Core1400061038In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label59: {
                  Object this$clientNo = this.getClientNo();
                  Object other$clientNo = other.getClientNo();
                  if (this$clientNo == null) {
                     if (other$clientNo == null) {
                        break label59;
                     }
                  } else if (this$clientNo.equals(other$clientNo)) {
                     break label59;
                  }

                  return false;
               }

               Object this$limitRef = this.getLimitRef();
               Object other$limitRef = other.getLimitRef();
               if (this$limitRef == null) {
                  if (other$limitRef != null) {
                     return false;
                  }
               } else if (!this$limitRef.equals(other$limitRef)) {
                  return false;
               }

               Object this$crDrMaintInd = this.getCrDrMaintInd();
               Object other$crDrMaintInd = other.getCrDrMaintInd();
               if (this$crDrMaintInd == null) {
                  if (other$crDrMaintInd != null) {
                     return false;
                  }
               } else if (!this$crDrMaintInd.equals(other$crDrMaintInd)) {
                  return false;
               }

               Object this$limitType = this.getLimitType();
               Object other$limitType = other.getLimitType();
               if (this$limitType == null) {
                  if (other$limitType != null) {
                     return false;
                  }
               } else if (!this$limitType.equals(other$limitType)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400061038In.Body;
      }
      public String toString() {
         return "Core1400061038In.Body(clientNo=" + this.getClientNo() + ", limitRef=" + this.getLimitRef() + ", crDrMaintInd=" + this.getCrDrMaintInd() + ", limitType=" + this.getLimitType() + ")";
      }
   }
}
