package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;

@MessageOut
public class Core1400100036Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "支取金额",
      notNull = false,
      length = "17",
      remark = "支取金额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal debtAmt;
   @V(
      desc = "存入金额",
      notNull = false,
      length = "17",
      remark = "存入金额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal cretAmt;

   public BigDecimal getDebtAmt() {
      return this.debtAmt;
   }

   public BigDecimal getCretAmt() {
      return this.cretAmt;
   }

   public void setDebtAmt(BigDecimal debtAmt) {
      this.debtAmt = debtAmt;
   }

   public void setCretAmt(BigDecimal cretAmt) {
      this.cretAmt = cretAmt;
   }

   public String toString() {
      return "Core1400100036Out(debtAmt=" + this.getDebtAmt() + ", cretAmt=" + this.getCretAmt() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100036Out)) {
         return false;
      } else {
         Core1400100036Out other = (Core1400100036Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$debtAmt = this.getDebtAmt();
            Object other$debtAmt = other.getDebtAmt();
            if (this$debtAmt == null) {
               if (other$debtAmt != null) {
                  return false;
               }
            } else if (!this$debtAmt.equals(other$debtAmt)) {
               return false;
            }

            Object this$cretAmt = this.getCretAmt();
            Object other$cretAmt = other.getCretAmt();
            if (this$cretAmt == null) {
               if (other$cretAmt != null) {
                  return false;
               }
            } else if (!this$cretAmt.equals(other$cretAmt)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100036Out;
   }
}
