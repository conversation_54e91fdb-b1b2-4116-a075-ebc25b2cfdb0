package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000806In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000806Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12000806 {
   String URL = "/rb/nfin/tx/ageement/sub";


   @ApiRemark("华兴同兴赢子协议管理")
   @ApiDesc("华兴同兴赢子协议管理，包括：子协议签约，修改，删除等操作")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0806"
   )
   @BusinessCategory("1200-非金融")
   @FunctionCategory("RB05-协议管理")
   Core12000806Out runService(Core12000806In var1);
}
