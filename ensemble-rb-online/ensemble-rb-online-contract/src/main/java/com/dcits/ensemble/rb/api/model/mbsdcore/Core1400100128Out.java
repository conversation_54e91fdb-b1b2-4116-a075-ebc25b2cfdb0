package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400100128Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100128Out.ApplyInfoArray> applyInfoArray;

   public List<Core1400100128Out.ApplyInfoArray> getApplyInfoArray() {
      return this.applyInfoArray;
   }

   public void setApplyInfoArray(List<Core1400100128Out.ApplyInfoArray> applyInfoArray) {
      this.applyInfoArray = applyInfoArray;
   }

   public String toString() {
      return "Core1400100128Out(applyInfoArray=" + this.getApplyInfoArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100128Out)) {
         return false;
      } else {
         Core1400100128Out other = (Core1400100128Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$applyInfoArray = this.getApplyInfoArray();
            Object other$applyInfoArray = other.getApplyInfoArray();
            if (this$applyInfoArray == null) {
               if (other$applyInfoArray != null) {
                  return false;
               }
            } else if (!this$applyInfoArray.equals(other$applyInfoArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100128Out;
   }
   public static class ApplyInfoArray {
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "客户名称",
         notNull = false,
         length = "200",
         remark = "客户名称",
         maxSize = 200
      )
      private String clientName;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "发行起始日期",
         notNull = false,
         remark = "发行起始日期"
      )
      private String issueStartDate;
      @V(
         desc = "发行终止日期",
         notNull = false,
         remark = "发行终止日期"
      )
      private String issueEndDate;
      @V(
         desc = "预约登记日期",
         notNull = false,
         remark = "预约登记日期"
      )
      private String precontractDate;
      @V(
         desc = "预约开户日期",
         notNull = false,
         remark = "预约开户日期"
      )
      private String precontractOpenDate;
      @V(
         desc = "预约金额",
         notNull = false,
         length = "17",
         remark = "预约金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal precontractAmt;
      @V(
         desc = "预约币种",
         notNull = false,
         length = "3",
         remark = "预约币种",
         maxSize = 3
      )
      private String applyCcy;
      @V(
         desc = "行内利率",
         notNull = false,
         length = "15",
         remark = "在人行基准利率调整后对客发布的行内利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal actualRate;
      @V(
         desc = "执行利率",
         notNull = false,
         length = "15",
         remark = "执行利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal realRate;
      @V(
         desc = "限制编号",
         notNull = false,
         length = "50",
         remark = "限制编号",
         maxSize = 50
      )
      private String resSeqNo;
      @V(
         desc = "限制时间",
         notNull = false,
         length = "6",
         remark = "限制时间",
         maxSize = 6
      )
      private String restraintDate;
      @V(
         desc = "产品起息日",
         notNull = false,
         length = "10",
         remark = "产品起息日",
         maxSize = 10
      )
      private String prodBeginDate;
      @V(
         desc = "结息日期",
         notNull = false,
         remark = "结息日期"
      )
      private String captDate;
      @V(
         desc = "签发机构",
         notNull = false,
         length = "50",
         remark = "签发机构",
         maxSize = 50
      )
      private String issueBranch;
      @V(
         desc = "期次代码",
         notNull = false,
         length = "50",
         remark = "期次代码",
         maxSize = 50
      )
      private String stageCode;
      @V(
         desc = "期次描述",
         notNull = false,
         length = "200",
         remark = "期次描述",
         maxSize = 200
      )
      private String stageCodeDesc;
      @V(
         desc = "余额",
         notNull = false,
         length = "17",
         remark = "余额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal balance;
      @V(
         desc = "交易柜员",
         notNull = false,
         length = "30",
         remark = "交易柜员",
         maxSize = 30
      )
      private String userId;
      @V(
         desc = "账户状态",
         notNull = false,
         length = "1",
         remark = "描述账户生命周期不同阶段的划分",
         maxSize = 1
      )
      private String acctStatus;
      @V(
         desc = "认购账户账号",
         notNull = false,
         length = "50",
         remark = "认购账户账号",
         maxSize = 50
      )
      private String subsBaseAcctNo;
      @V(
         desc = "预约号",
         notNull = false,
         length = "50",
         remark = "预约编号",
         maxSize = 50
      )
      private String precontractNo;
      @V(
         desc = "期次产品预约状态",
         notNull = false,
         length = "1",
         remark = "期次产品预约状态",
         maxSize = 1
      )
      private String precontractStatus;
      @V(
         desc = "取消日期",
         notNull = false,
         remark = "指不同场景协议到期处理日期"
      )
      private String cancelDate;
      @V(
         desc = "认购账户序列号",
         notNull = false,
         length = "5",
         remark = "认购账户序列号",
         maxSize = 5
      )
      private String subsSeqNo;
      @V(
         desc = "收息账户",
         notNull = false,
         length = "50",
         remark = "收息账户，如果是按频率付息时，必输",
         maxSize = 50
      )
      private String chargeIntAcct;
      @V(
         desc = "收息账户序列号",
         notNull = false,
         length = "5",
         remark = "收息账户序列号",
         maxSize = 5
      )
      private String chargeIntAcctSeqNo;
      @V(
         desc = "到期日期",
         notNull = false,
         remark = "到期日期"
      )
      private String maturityDate;
      @V(
         desc = "起息标识",
         notNull = false,
         length = "1",
         remark = "起息标识",
         maxSize = 1
      )
      private String intStartFlag;
      @V(
         desc = "业务状态",
         notNull = false,
         length = "10",
         remark = "业务状态",
         maxSize = 10
      )
      private String busiStatus;

      public String getClientNo() {
         return this.clientNo;
      }

      public String getClientName() {
         return this.clientName;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getIssueStartDate() {
         return this.issueStartDate;
      }

      public String getIssueEndDate() {
         return this.issueEndDate;
      }

      public String getPrecontractDate() {
         return this.precontractDate;
      }

      public String getPrecontractOpenDate() {
         return this.precontractOpenDate;
      }

      public BigDecimal getPrecontractAmt() {
         return this.precontractAmt;
      }

      public String getApplyCcy() {
         return this.applyCcy;
      }

      public BigDecimal getActualRate() {
         return this.actualRate;
      }

      public BigDecimal getRealRate() {
         return this.realRate;
      }

      public String getResSeqNo() {
         return this.resSeqNo;
      }

      public String getRestraintDate() {
         return this.restraintDate;
      }

      public String getProdBeginDate() {
         return this.prodBeginDate;
      }

      public String getCaptDate() {
         return this.captDate;
      }

      public String getIssueBranch() {
         return this.issueBranch;
      }

      public String getStageCode() {
         return this.stageCode;
      }

      public String getStageCodeDesc() {
         return this.stageCodeDesc;
      }

      public BigDecimal getBalance() {
         return this.balance;
      }

      public String getUserId() {
         return this.userId;
      }

      public String getAcctStatus() {
         return this.acctStatus;
      }

      public String getSubsBaseAcctNo() {
         return this.subsBaseAcctNo;
      }

      public String getPrecontractNo() {
         return this.precontractNo;
      }

      public String getPrecontractStatus() {
         return this.precontractStatus;
      }

      public String getCancelDate() {
         return this.cancelDate;
      }

      public String getSubsSeqNo() {
         return this.subsSeqNo;
      }

      public String getChargeIntAcct() {
         return this.chargeIntAcct;
      }

      public String getChargeIntAcctSeqNo() {
         return this.chargeIntAcctSeqNo;
      }

      public String getMaturityDate() {
         return this.maturityDate;
      }

      public String getIntStartFlag() {
         return this.intStartFlag;
      }

      public String getBusiStatus() {
         return this.busiStatus;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setClientName(String clientName) {
         this.clientName = clientName;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setIssueStartDate(String issueStartDate) {
         this.issueStartDate = issueStartDate;
      }

      public void setIssueEndDate(String issueEndDate) {
         this.issueEndDate = issueEndDate;
      }

      public void setPrecontractDate(String precontractDate) {
         this.precontractDate = precontractDate;
      }

      public void setPrecontractOpenDate(String precontractOpenDate) {
         this.precontractOpenDate = precontractOpenDate;
      }

      public void setPrecontractAmt(BigDecimal precontractAmt) {
         this.precontractAmt = precontractAmt;
      }

      public void setApplyCcy(String applyCcy) {
         this.applyCcy = applyCcy;
      }

      public void setActualRate(BigDecimal actualRate) {
         this.actualRate = actualRate;
      }

      public void setRealRate(BigDecimal realRate) {
         this.realRate = realRate;
      }

      public void setResSeqNo(String resSeqNo) {
         this.resSeqNo = resSeqNo;
      }

      public void setRestraintDate(String restraintDate) {
         this.restraintDate = restraintDate;
      }

      public void setProdBeginDate(String prodBeginDate) {
         this.prodBeginDate = prodBeginDate;
      }

      public void setCaptDate(String captDate) {
         this.captDate = captDate;
      }

      public void setIssueBranch(String issueBranch) {
         this.issueBranch = issueBranch;
      }

      public void setStageCode(String stageCode) {
         this.stageCode = stageCode;
      }

      public void setStageCodeDesc(String stageCodeDesc) {
         this.stageCodeDesc = stageCodeDesc;
      }

      public void setBalance(BigDecimal balance) {
         this.balance = balance;
      }

      public void setUserId(String userId) {
         this.userId = userId;
      }

      public void setAcctStatus(String acctStatus) {
         this.acctStatus = acctStatus;
      }

      public void setSubsBaseAcctNo(String subsBaseAcctNo) {
         this.subsBaseAcctNo = subsBaseAcctNo;
      }

      public void setPrecontractNo(String precontractNo) {
         this.precontractNo = precontractNo;
      }

      public void setPrecontractStatus(String precontractStatus) {
         this.precontractStatus = precontractStatus;
      }

      public void setCancelDate(String cancelDate) {
         this.cancelDate = cancelDate;
      }

      public void setSubsSeqNo(String subsSeqNo) {
         this.subsSeqNo = subsSeqNo;
      }

      public void setChargeIntAcct(String chargeIntAcct) {
         this.chargeIntAcct = chargeIntAcct;
      }

      public void setChargeIntAcctSeqNo(String chargeIntAcctSeqNo) {
         this.chargeIntAcctSeqNo = chargeIntAcctSeqNo;
      }

      public void setMaturityDate(String maturityDate) {
         this.maturityDate = maturityDate;
      }

      public void setIntStartFlag(String intStartFlag) {
         this.intStartFlag = intStartFlag;
      }

      public void setBusiStatus(String busiStatus) {
         this.busiStatus = busiStatus;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100128Out.ApplyInfoArray)) {
            return false;
         } else {
            Core1400100128Out.ApplyInfoArray other = (Core1400100128Out.ApplyInfoArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$clientNo = this.getClientNo();
               Object other$clientNo = other.getClientNo();
               if (this$clientNo == null) {
                  if (other$clientNo != null) {
                     return false;
                  }
               } else if (!this$clientNo.equals(other$clientNo)) {
                  return false;
               }

               Object this$clientName = this.getClientName();
               Object other$clientName = other.getClientName();
               if (this$clientName == null) {
                  if (other$clientName != null) {
                     return false;
                  }
               } else if (!this$clientName.equals(other$clientName)) {
                  return false;
               }

               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo != null) {
                     return false;
                  }
               } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
                  return false;
               }

               label398: {
                  Object this$prodType = this.getProdType();
                  Object other$prodType = other.getProdType();
                  if (this$prodType == null) {
                     if (other$prodType == null) {
                        break label398;
                     }
                  } else if (this$prodType.equals(other$prodType)) {
                     break label398;
                  }

                  return false;
               }

               label391: {
                  Object this$acctSeqNo = this.getAcctSeqNo();
                  Object other$acctSeqNo = other.getAcctSeqNo();
                  if (this$acctSeqNo == null) {
                     if (other$acctSeqNo == null) {
                        break label391;
                     }
                  } else if (this$acctSeqNo.equals(other$acctSeqNo)) {
                     break label391;
                  }

                  return false;
               }

               Object this$acctCcy = this.getAcctCcy();
               Object other$acctCcy = other.getAcctCcy();
               if (this$acctCcy == null) {
                  if (other$acctCcy != null) {
                     return false;
                  }
               } else if (!this$acctCcy.equals(other$acctCcy)) {
                  return false;
               }

               label377: {
                  Object this$issueStartDate = this.getIssueStartDate();
                  Object other$issueStartDate = other.getIssueStartDate();
                  if (this$issueStartDate == null) {
                     if (other$issueStartDate == null) {
                        break label377;
                     }
                  } else if (this$issueStartDate.equals(other$issueStartDate)) {
                     break label377;
                  }

                  return false;
               }

               label370: {
                  Object this$issueEndDate = this.getIssueEndDate();
                  Object other$issueEndDate = other.getIssueEndDate();
                  if (this$issueEndDate == null) {
                     if (other$issueEndDate == null) {
                        break label370;
                     }
                  } else if (this$issueEndDate.equals(other$issueEndDate)) {
                     break label370;
                  }

                  return false;
               }

               Object this$precontractDate = this.getPrecontractDate();
               Object other$precontractDate = other.getPrecontractDate();
               if (this$precontractDate == null) {
                  if (other$precontractDate != null) {
                     return false;
                  }
               } else if (!this$precontractDate.equals(other$precontractDate)) {
                  return false;
               }

               Object this$precontractOpenDate = this.getPrecontractOpenDate();
               Object other$precontractOpenDate = other.getPrecontractOpenDate();
               if (this$precontractOpenDate == null) {
                  if (other$precontractOpenDate != null) {
                     return false;
                  }
               } else if (!this$precontractOpenDate.equals(other$precontractOpenDate)) {
                  return false;
               }

               label349: {
                  Object this$precontractAmt = this.getPrecontractAmt();
                  Object other$precontractAmt = other.getPrecontractAmt();
                  if (this$precontractAmt == null) {
                     if (other$precontractAmt == null) {
                        break label349;
                     }
                  } else if (this$precontractAmt.equals(other$precontractAmt)) {
                     break label349;
                  }

                  return false;
               }

               label342: {
                  Object this$applyCcy = this.getApplyCcy();
                  Object other$applyCcy = other.getApplyCcy();
                  if (this$applyCcy == null) {
                     if (other$applyCcy == null) {
                        break label342;
                     }
                  } else if (this$applyCcy.equals(other$applyCcy)) {
                     break label342;
                  }

                  return false;
               }

               Object this$actualRate = this.getActualRate();
               Object other$actualRate = other.getActualRate();
               if (this$actualRate == null) {
                  if (other$actualRate != null) {
                     return false;
                  }
               } else if (!this$actualRate.equals(other$actualRate)) {
                  return false;
               }

               label328: {
                  Object this$realRate = this.getRealRate();
                  Object other$realRate = other.getRealRate();
                  if (this$realRate == null) {
                     if (other$realRate == null) {
                        break label328;
                     }
                  } else if (this$realRate.equals(other$realRate)) {
                     break label328;
                  }

                  return false;
               }

               Object this$resSeqNo = this.getResSeqNo();
               Object other$resSeqNo = other.getResSeqNo();
               if (this$resSeqNo == null) {
                  if (other$resSeqNo != null) {
                     return false;
                  }
               } else if (!this$resSeqNo.equals(other$resSeqNo)) {
                  return false;
               }

               label314: {
                  Object this$restraintDate = this.getRestraintDate();
                  Object other$restraintDate = other.getRestraintDate();
                  if (this$restraintDate == null) {
                     if (other$restraintDate == null) {
                        break label314;
                     }
                  } else if (this$restraintDate.equals(other$restraintDate)) {
                     break label314;
                  }

                  return false;
               }

               Object this$prodBeginDate = this.getProdBeginDate();
               Object other$prodBeginDate = other.getProdBeginDate();
               if (this$prodBeginDate == null) {
                  if (other$prodBeginDate != null) {
                     return false;
                  }
               } else if (!this$prodBeginDate.equals(other$prodBeginDate)) {
                  return false;
               }

               Object this$captDate = this.getCaptDate();
               Object other$captDate = other.getCaptDate();
               if (this$captDate == null) {
                  if (other$captDate != null) {
                     return false;
                  }
               } else if (!this$captDate.equals(other$captDate)) {
                  return false;
               }

               Object this$issueBranch = this.getIssueBranch();
               Object other$issueBranch = other.getIssueBranch();
               if (this$issueBranch == null) {
                  if (other$issueBranch != null) {
                     return false;
                  }
               } else if (!this$issueBranch.equals(other$issueBranch)) {
                  return false;
               }

               label286: {
                  Object this$stageCode = this.getStageCode();
                  Object other$stageCode = other.getStageCode();
                  if (this$stageCode == null) {
                     if (other$stageCode == null) {
                        break label286;
                     }
                  } else if (this$stageCode.equals(other$stageCode)) {
                     break label286;
                  }

                  return false;
               }

               label279: {
                  Object this$stageCodeDesc = this.getStageCodeDesc();
                  Object other$stageCodeDesc = other.getStageCodeDesc();
                  if (this$stageCodeDesc == null) {
                     if (other$stageCodeDesc == null) {
                        break label279;
                     }
                  } else if (this$stageCodeDesc.equals(other$stageCodeDesc)) {
                     break label279;
                  }

                  return false;
               }

               Object this$balance = this.getBalance();
               Object other$balance = other.getBalance();
               if (this$balance == null) {
                  if (other$balance != null) {
                     return false;
                  }
               } else if (!this$balance.equals(other$balance)) {
                  return false;
               }

               label265: {
                  Object this$userId = this.getUserId();
                  Object other$userId = other.getUserId();
                  if (this$userId == null) {
                     if (other$userId == null) {
                        break label265;
                     }
                  } else if (this$userId.equals(other$userId)) {
                     break label265;
                  }

                  return false;
               }

               label258: {
                  Object this$acctStatus = this.getAcctStatus();
                  Object other$acctStatus = other.getAcctStatus();
                  if (this$acctStatus == null) {
                     if (other$acctStatus == null) {
                        break label258;
                     }
                  } else if (this$acctStatus.equals(other$acctStatus)) {
                     break label258;
                  }

                  return false;
               }

               Object this$subsBaseAcctNo = this.getSubsBaseAcctNo();
               Object other$subsBaseAcctNo = other.getSubsBaseAcctNo();
               if (this$subsBaseAcctNo == null) {
                  if (other$subsBaseAcctNo != null) {
                     return false;
                  }
               } else if (!this$subsBaseAcctNo.equals(other$subsBaseAcctNo)) {
                  return false;
               }

               Object this$precontractNo = this.getPrecontractNo();
               Object other$precontractNo = other.getPrecontractNo();
               if (this$precontractNo == null) {
                  if (other$precontractNo != null) {
                     return false;
                  }
               } else if (!this$precontractNo.equals(other$precontractNo)) {
                  return false;
               }

               label237: {
                  Object this$precontractStatus = this.getPrecontractStatus();
                  Object other$precontractStatus = other.getPrecontractStatus();
                  if (this$precontractStatus == null) {
                     if (other$precontractStatus == null) {
                        break label237;
                     }
                  } else if (this$precontractStatus.equals(other$precontractStatus)) {
                     break label237;
                  }

                  return false;
               }

               label230: {
                  Object this$cancelDate = this.getCancelDate();
                  Object other$cancelDate = other.getCancelDate();
                  if (this$cancelDate == null) {
                     if (other$cancelDate == null) {
                        break label230;
                     }
                  } else if (this$cancelDate.equals(other$cancelDate)) {
                     break label230;
                  }

                  return false;
               }

               Object this$subsSeqNo = this.getSubsSeqNo();
               Object other$subsSeqNo = other.getSubsSeqNo();
               if (this$subsSeqNo == null) {
                  if (other$subsSeqNo != null) {
                     return false;
                  }
               } else if (!this$subsSeqNo.equals(other$subsSeqNo)) {
                  return false;
               }

               label216: {
                  Object this$chargeIntAcct = this.getChargeIntAcct();
                  Object other$chargeIntAcct = other.getChargeIntAcct();
                  if (this$chargeIntAcct == null) {
                     if (other$chargeIntAcct == null) {
                        break label216;
                     }
                  } else if (this$chargeIntAcct.equals(other$chargeIntAcct)) {
                     break label216;
                  }

                  return false;
               }

               Object this$chargeIntAcctSeqNo = this.getChargeIntAcctSeqNo();
               Object other$chargeIntAcctSeqNo = other.getChargeIntAcctSeqNo();
               if (this$chargeIntAcctSeqNo == null) {
                  if (other$chargeIntAcctSeqNo != null) {
                     return false;
                  }
               } else if (!this$chargeIntAcctSeqNo.equals(other$chargeIntAcctSeqNo)) {
                  return false;
               }

               label202: {
                  Object this$maturityDate = this.getMaturityDate();
                  Object other$maturityDate = other.getMaturityDate();
                  if (this$maturityDate == null) {
                     if (other$maturityDate == null) {
                        break label202;
                     }
                  } else if (this$maturityDate.equals(other$maturityDate)) {
                     break label202;
                  }

                  return false;
               }

               Object this$intStartFlag = this.getIntStartFlag();
               Object other$intStartFlag = other.getIntStartFlag();
               if (this$intStartFlag == null) {
                  if (other$intStartFlag != null) {
                     return false;
                  }
               } else if (!this$intStartFlag.equals(other$intStartFlag)) {
                  return false;
               }

               Object this$busiStatus = this.getBusiStatus();
               Object other$busiStatus = other.getBusiStatus();
               if (this$busiStatus == null) {
                  if (other$busiStatus != null) {
                     return false;
                  }
               } else if (!this$busiStatus.equals(other$busiStatus)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100128Out.ApplyInfoArray;
      }
      public String toString() {
         return "Core1400100128Out.ApplyInfoArray(clientNo=" + this.getClientNo() + ", clientName=" + this.getClientName() + ", baseAcctNo=" + this.getBaseAcctNo() + ", prodType=" + this.getProdType() + ", acctSeqNo=" + this.getAcctSeqNo() + ", acctCcy=" + this.getAcctCcy() + ", issueStartDate=" + this.getIssueStartDate() + ", issueEndDate=" + this.getIssueEndDate() + ", precontractDate=" + this.getPrecontractDate() + ", precontractOpenDate=" + this.getPrecontractOpenDate() + ", precontractAmt=" + this.getPrecontractAmt() + ", applyCcy=" + this.getApplyCcy() + ", actualRate=" + this.getActualRate() + ", realRate=" + this.getRealRate() + ", resSeqNo=" + this.getResSeqNo() + ", restraintDate=" + this.getRestraintDate() + ", prodBeginDate=" + this.getProdBeginDate() + ", captDate=" + this.getCaptDate() + ", issueBranch=" + this.getIssueBranch() + ", stageCode=" + this.getStageCode() + ", stageCodeDesc=" + this.getStageCodeDesc() + ", balance=" + this.getBalance() + ", userId=" + this.getUserId() + ", acctStatus=" + this.getAcctStatus() + ", subsBaseAcctNo=" + this.getSubsBaseAcctNo() + ", precontractNo=" + this.getPrecontractNo() + ", precontractStatus=" + this.getPrecontractStatus() + ", cancelDate=" + this.getCancelDate() + ", subsSeqNo=" + this.getSubsSeqNo() + ", chargeIntAcct=" + this.getChargeIntAcct() + ", chargeIntAcctSeqNo=" + this.getChargeIntAcctSeqNo() + ", maturityDate=" + this.getMaturityDate() + ", intStartFlag=" + this.getIntStartFlag() + ", busiStatus=" + this.getBusiStatus() + ")";
      }
   }
}
