package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400100133In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400100133In.Body body;

   public Core1400100133In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400100133In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400100133In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100133In)) {
         return false;
      } else {
         Core1400100133In other = (Core1400100133In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100133In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "收费序号",
         notNull = false,
         length = "50",
         remark = "收费序号",
         maxSize = 50
      )
      private String scSeqNo;
      @V(
         desc = "开始日期",
         notNull = false,
         remark = "开始日期"
      )
      private String startDate;
      @V(
         desc = "结束日期",
         notNull = false,
         remark = "结束日期"
      )
      private String endDate;

      public String getScSeqNo() {
         return this.scSeqNo;
      }

      public String getStartDate() {
         return this.startDate;
      }

      public String getEndDate() {
         return this.endDate;
      }

      public void setScSeqNo(String scSeqNo) {
         this.scSeqNo = scSeqNo;
      }

      public void setStartDate(String startDate) {
         this.startDate = startDate;
      }

      public void setEndDate(String endDate) {
         this.endDate = endDate;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100133In.Body)) {
            return false;
         } else {
            Core1400100133In.Body other = (Core1400100133In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label47: {
                  Object this$scSeqNo = this.getScSeqNo();
                  Object other$scSeqNo = other.getScSeqNo();
                  if (this$scSeqNo == null) {
                     if (other$scSeqNo == null) {
                        break label47;
                     }
                  } else if (this$scSeqNo.equals(other$scSeqNo)) {
                     break label47;
                  }

                  return false;
               }

               Object this$startDate = this.getStartDate();
               Object other$startDate = other.getStartDate();
               if (this$startDate == null) {
                  if (other$startDate != null) {
                     return false;
                  }
               } else if (!this$startDate.equals(other$startDate)) {
                  return false;
               }

               Object this$endDate = this.getEndDate();
               Object other$endDate = other.getEndDate();
               if (this$endDate == null) {
                  if (other$endDate != null) {
                     return false;
                  }
               } else if (!this$endDate.equals(other$endDate)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100133In.Body;
      }
      public String toString() {
         return "Core1400100133In.Body(scSeqNo=" + this.getScSeqNo() + ", startDate=" + this.getStartDate() + ", endDate=" + this.getEndDate() + ")";
      }
   }
}
