package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400100035In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400100035In.Body body;

   public Core1400100035In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400100035In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400100035In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100035In)) {
         return false;
      } else {
         Core1400100035In other = (Core1400100035In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100035In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "交易流水识别号",
         notNull = false,
         length = "50",
         remark = "交易流水识别号",
         maxSize = 50
      )
      private String tradeNo;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;

      public String getTradeNo() {
         return this.tradeNo;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public void setTradeNo(String tradeNo) {
         this.tradeNo = tradeNo;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100035In.Body)) {
            return false;
         } else {
            Core1400100035In.Body other = (Core1400100035In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label47: {
                  Object this$tradeNo = this.getTradeNo();
                  Object other$tradeNo = other.getTradeNo();
                  if (this$tradeNo == null) {
                     if (other$tradeNo == null) {
                        break label47;
                     }
                  } else if (this$tradeNo.equals(other$tradeNo)) {
                     break label47;
                  }

                  return false;
               }

               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo != null) {
                     return false;
                  }
               } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
                  return false;
               }

               Object this$clientNo = this.getClientNo();
               Object other$clientNo = other.getClientNo();
               if (this$clientNo == null) {
                  if (other$clientNo != null) {
                     return false;
                  }
               } else if (!this$clientNo.equals(other$clientNo)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100035In.Body;
      }
      public String toString() {
         return "Core1400100035In.Body(tradeNo=" + this.getTradeNo() + ", baseAcctNo=" + this.getBaseAcctNo() + ", clientNo=" + this.getClientNo() + ")";
      }
   }
}
