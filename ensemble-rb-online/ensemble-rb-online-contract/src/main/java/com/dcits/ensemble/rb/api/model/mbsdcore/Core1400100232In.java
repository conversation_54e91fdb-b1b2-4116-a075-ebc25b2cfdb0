package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400100232In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400100232In.Body body;

   public Core1400100232In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400100232In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400100232In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100232In)) {
         return false;
      } else {
         Core1400100232In other = (Core1400100232In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100232In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "序号",
         notNull = false,
         length = "50",
         remark = "序号",
         maxSize = 50
      )
      private String seqNo;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "状态",
         notNull = false,
         length = "1",
         inDesc = "A-有效,F-无效,O-未过账,P-已过账,N-新增,U-修改,D-删除,C-非活动状态",
         remark = "状态",
         maxSize = 1
      )
      private String status;
      @V(
         desc = "转让编号",
         notNull = false,
         length = "30",
         remark = "转让编号",
         maxSize = 30
      )
      private String tranferRemarkNo;
      @V(
         desc = "受让客户号",
         notNull = false,
         length = "50",
         remark = "受让客户号",
         maxSize = 50
      )
      private String toClientNo;
      @V(
         desc = "是否定向转让",
         notNull = false,
         length = "3",
         remark = "是否定向转让",
         maxSize = 3
      )
      private String isSpec;
      @V(
         desc = "挂单起始日",
         notNull = false,
         length = "10",
         remark = "挂单起始日",
         maxSize = 10
      )
      private String startBillDate;
      @V(
         desc = "挂单结束日",
         notNull = false,
         length = "10",
         remark = "挂单结束日",
         maxSize = 10
      )
      private String endBillDate;

      public String getProdType() {
         return this.prodType;
      }

      public String getCcy() {
         return this.ccy;
      }

      public String getSeqNo() {
         return this.seqNo;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getStatus() {
         return this.status;
      }

      public String getTranferRemarkNo() {
         return this.tranferRemarkNo;
      }

      public String getToClientNo() {
         return this.toClientNo;
      }

      public String getIsSpec() {
         return this.isSpec;
      }

      public String getStartBillDate() {
         return this.startBillDate;
      }

      public String getEndBillDate() {
         return this.endBillDate;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setSeqNo(String seqNo) {
         this.seqNo = seqNo;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setStatus(String status) {
         this.status = status;
      }

      public void setTranferRemarkNo(String tranferRemarkNo) {
         this.tranferRemarkNo = tranferRemarkNo;
      }

      public void setToClientNo(String toClientNo) {
         this.toClientNo = toClientNo;
      }

      public void setIsSpec(String isSpec) {
         this.isSpec = isSpec;
      }

      public void setStartBillDate(String startBillDate) {
         this.startBillDate = startBillDate;
      }

      public void setEndBillDate(String endBillDate) {
         this.endBillDate = endBillDate;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100232In.Body)) {
            return false;
         } else {
            Core1400100232In.Body other = (Core1400100232In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               Object this$ccy = this.getCcy();
               Object other$ccy = other.getCcy();
               if (this$ccy == null) {
                  if (other$ccy != null) {
                     return false;
                  }
               } else if (!this$ccy.equals(other$ccy)) {
                  return false;
               }

               Object this$seqNo = this.getSeqNo();
               Object other$seqNo = other.getSeqNo();
               if (this$seqNo == null) {
                  if (other$seqNo != null) {
                     return false;
                  }
               } else if (!this$seqNo.equals(other$seqNo)) {
                  return false;
               }

               label110: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label110;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label110;
                  }

                  return false;
               }

               label103: {
                  Object this$status = this.getStatus();
                  Object other$status = other.getStatus();
                  if (this$status == null) {
                     if (other$status == null) {
                        break label103;
                     }
                  } else if (this$status.equals(other$status)) {
                     break label103;
                  }

                  return false;
               }

               Object this$tranferRemarkNo = this.getTranferRemarkNo();
               Object other$tranferRemarkNo = other.getTranferRemarkNo();
               if (this$tranferRemarkNo == null) {
                  if (other$tranferRemarkNo != null) {
                     return false;
                  }
               } else if (!this$tranferRemarkNo.equals(other$tranferRemarkNo)) {
                  return false;
               }

               label89: {
                  Object this$toClientNo = this.getToClientNo();
                  Object other$toClientNo = other.getToClientNo();
                  if (this$toClientNo == null) {
                     if (other$toClientNo == null) {
                        break label89;
                     }
                  } else if (this$toClientNo.equals(other$toClientNo)) {
                     break label89;
                  }

                  return false;
               }

               label82: {
                  Object this$isSpec = this.getIsSpec();
                  Object other$isSpec = other.getIsSpec();
                  if (this$isSpec == null) {
                     if (other$isSpec == null) {
                        break label82;
                     }
                  } else if (this$isSpec.equals(other$isSpec)) {
                     break label82;
                  }

                  return false;
               }

               Object this$startBillDate = this.getStartBillDate();
               Object other$startBillDate = other.getStartBillDate();
               if (this$startBillDate == null) {
                  if (other$startBillDate != null) {
                     return false;
                  }
               } else if (!this$startBillDate.equals(other$startBillDate)) {
                  return false;
               }

               Object this$endBillDate = this.getEndBillDate();
               Object other$endBillDate = other.getEndBillDate();
               if (this$endBillDate == null) {
                  if (other$endBillDate != null) {
                     return false;
                  }
               } else if (!this$endBillDate.equals(other$endBillDate)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100232In.Body;
      }
      public String toString() {
         return "Core1400100232In.Body(prodType=" + this.getProdType() + ", ccy=" + this.getCcy() + ", seqNo=" + this.getSeqNo() + ", baseAcctNo=" + this.getBaseAcctNo() + ", status=" + this.getStatus() + ", tranferRemarkNo=" + this.getTranferRemarkNo() + ", toClientNo=" + this.getToClientNo() + ", isSpec=" + this.getIsSpec() + ", startBillDate=" + this.getStartBillDate() + ", endBillDate=" + this.getEndBillDate() + ")";
      }
   }
}
