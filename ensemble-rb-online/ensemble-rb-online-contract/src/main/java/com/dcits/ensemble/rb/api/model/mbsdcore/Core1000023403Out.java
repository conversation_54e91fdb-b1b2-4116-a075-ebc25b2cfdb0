package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;

@MessageOut
public class Core1000023403Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "业务流水号",
      notNull = false,
      length = "50",
      remark = "支付流水号",
      maxSize = 50
   )
   private String serialNo;
   @V(
      desc = "交易参考号",
      notNull = false,
      length = "50",
      remark = "交易参考号",
      maxSize = 50
   )
   private String reference;
   @V(
      desc = "挂账序列号",
      notNull = false,
      length = "50",
      remark = "挂账账户序列号",
      maxSize = 50
   )
   private String hangSeqNo;

   public String getSerialNo() {
      return this.serialNo;
   }

   public String getReference() {
      return this.reference;
   }

   public String getHangSeqNo() {
      return this.hangSeqNo;
   }

   public void setSerialNo(String serialNo) {
      this.serialNo = serialNo;
   }

   public void setReference(String reference) {
      this.reference = reference;
   }

   public void setHangSeqNo(String hangSeqNo) {
      this.hangSeqNo = hangSeqNo;
   }

   public String toString() {
      return "Core1000023403Out(serialNo=" + this.getSerialNo() + ", reference=" + this.getReference() + ", hangSeqNo=" + this.getHangSeqNo() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1000023403Out)) {
         return false;
      } else {
         Core1000023403Out other = (Core1000023403Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            label49: {
               Object this$serialNo = this.getSerialNo();
               Object other$serialNo = other.getSerialNo();
               if (this$serialNo == null) {
                  if (other$serialNo == null) {
                     break label49;
                  }
               } else if (this$serialNo.equals(other$serialNo)) {
                  break label49;
               }

               return false;
            }

            Object this$reference = this.getReference();
            Object other$reference = other.getReference();
            if (this$reference == null) {
               if (other$reference != null) {
                  return false;
               }
            } else if (!this$reference.equals(other$reference)) {
               return false;
            }

            Object this$hangSeqNo = this.getHangSeqNo();
            Object other$hangSeqNo = other.getHangSeqNo();
            if (this$hangSeqNo == null) {
               if (other$hangSeqNo != null) {
                  return false;
               }
            } else if (!this$hangSeqNo.equals(other$hangSeqNo)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1000023403Out;
   }
}
