package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12206172In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12206172Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12206172 {
   String URL = "/rb/file/limit/batch/update";


   @ApiRemark("非柜面渠道限额批量调整")
   @ApiDesc("非柜面渠道限额批量调整")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1220",
      messageCode = "6172"
   )
   @ConsumeSys("CBS")
   @ApiUseStatus("PROJECT-项目")
   Core12206172Out runService(Core12206172In var1);
}
