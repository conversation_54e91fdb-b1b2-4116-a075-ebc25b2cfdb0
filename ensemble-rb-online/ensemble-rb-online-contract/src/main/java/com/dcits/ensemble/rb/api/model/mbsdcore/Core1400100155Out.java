package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400100155Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100155Out.AcctArray> acctArray;

   public List<Core1400100155Out.AcctArray> getAcctArray() {
      return this.acctArray;
   }

   public void setAcctArray(List<Core1400100155Out.AcctArray> acctArray) {
      this.acctArray = acctArray;
   }

   public String toString() {
      return "Core1400100155Out(acctArray=" + this.getAcctArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100155Out)) {
         return false;
      } else {
         Core1400100155Out other = (Core1400100155Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$acctArray = this.getAcctArray();
            Object other$acctArray = other.getAcctArray();
            if (this$acctArray == null) {
               if (other$acctArray != null) {
                  return false;
               }
            } else if (!this$acctArray.equals(other$acctArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100155Out;
   }
   public static class AcctArray {
      @V(
         desc = "备注",
         notNull = false,
         length = "200",
         remark = "备注",
         maxSize = 200
      )
      private String remark;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "账户开户日期",
         notNull = false,
         remark = "账户开户日期"
      )
      private String acctOpenDate;
      @V(
         desc = "账户名称",
         notNull = false,
         length = "200",
         remark = "账户名称，一般指中文账户名称",
         maxSize = 200
      )
      private String acctName;
      @V(
         desc = "账户类型",
         notNull = false,
         length = "1",
         remark = "账户类型",
         maxSize = 1
      )
      private String acctType;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "证件号码",
         notNull = false,
         length = "50",
         remark = "证件号码",
         maxSize = 50
      )
      private String documentId;
      @V(
         desc = "证件类型",
         notNull = false,
         length = "3",
         remark = "证件类型",
         maxSize = 3
      )
      private String documentType;
      @V(
         desc = "固定电话",
         notNull = false,
         length = "20",
         remark = "固定电话",
         maxSize = 20
      )
      private String phoneNo;
      @V(
         desc = "业务种类",
         notNull = false,
         length = "20",
         remark = "业务种类",
         maxSize = 20
      )
      private String busiType;
      @V(
         desc = "业务类型",
         notNull = false,
         length = "30",
         remark = "业务类型",
         maxSize = 30
      )
      private String tradeType;
      @V(
         desc = "利息金额",
         notNull = false,
         length = "17",
         remark = "利息金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal intAmt;
      @V(
         desc = "余额",
         notNull = false,
         length = "17",
         remark = "余额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal balance;
      @V(
         desc = "利息券金额",
         notNull = false,
         length = "17",
         remark = "利息券金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal ticketAmt;
      @V(
         desc = "账户开户行",
         notNull = false,
         length = "50",
         remark = "账户实际开户机构，柜面为实际网点机构，线上渠道一般为对应主账户的实际开户机构",
         maxSize = 50
      )
      private String acctBranch;
      @V(
         desc = "渠道类型",
         notNull = false,
         length = "10",
         remark = "渠道类型",
         maxSize = 10
      )
      private String sourceType;
      @V(
         desc = "转久悬日期",
         notNull = false,
         remark = "转久悬日期"
      )
      private String dossDate;
      @V(
         desc = "转营业外日期",
         notNull = false,
         remark = "转营业外日期"
      )
      private String outBusiDate;
      @V(
         desc = "冻结标志",
         notNull = false,
         length = "1",
         remark = "冻结标志",
         maxSize = 1
      )
      private String resFlag;
      @V(
         desc = "支取标记",
         notNull = false,
         length = "1",
         remark = "支取标记",
         maxSize = 1
      )
      private String amtWdrawnFlag;
      @V(
         desc = "交易柜员",
         notNull = false,
         length = "30",
         remark = "交易柜员",
         maxSize = 30
      )
      private String userId;
      @V(
         desc = "交易日期",
         notNull = false,
         remark = "交易日期"
      )
      private String tranDate;

      public String getRemark() {
         return this.remark;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getAcctOpenDate() {
         return this.acctOpenDate;
      }

      public String getAcctName() {
         return this.acctName;
      }

      public String getAcctType() {
         return this.acctType;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public String getDocumentId() {
         return this.documentId;
      }

      public String getDocumentType() {
         return this.documentType;
      }

      public String getPhoneNo() {
         return this.phoneNo;
      }

      public String getBusiType() {
         return this.busiType;
      }

      public String getTradeType() {
         return this.tradeType;
      }

      public BigDecimal getIntAmt() {
         return this.intAmt;
      }

      public BigDecimal getBalance() {
         return this.balance;
      }

      public BigDecimal getTicketAmt() {
         return this.ticketAmt;
      }

      public String getAcctBranch() {
         return this.acctBranch;
      }

      public String getSourceType() {
         return this.sourceType;
      }

      public String getDossDate() {
         return this.dossDate;
      }

      public String getOutBusiDate() {
         return this.outBusiDate;
      }

      public String getResFlag() {
         return this.resFlag;
      }

      public String getAmtWdrawnFlag() {
         return this.amtWdrawnFlag;
      }

      public String getUserId() {
         return this.userId;
      }

      public String getTranDate() {
         return this.tranDate;
      }

      public void setRemark(String remark) {
         this.remark = remark;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setAcctOpenDate(String acctOpenDate) {
         this.acctOpenDate = acctOpenDate;
      }

      public void setAcctName(String acctName) {
         this.acctName = acctName;
      }

      public void setAcctType(String acctType) {
         this.acctType = acctType;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setDocumentId(String documentId) {
         this.documentId = documentId;
      }

      public void setDocumentType(String documentType) {
         this.documentType = documentType;
      }

      public void setPhoneNo(String phoneNo) {
         this.phoneNo = phoneNo;
      }

      public void setBusiType(String busiType) {
         this.busiType = busiType;
      }

      public void setTradeType(String tradeType) {
         this.tradeType = tradeType;
      }

      public void setIntAmt(BigDecimal intAmt) {
         this.intAmt = intAmt;
      }

      public void setBalance(BigDecimal balance) {
         this.balance = balance;
      }

      public void setTicketAmt(BigDecimal ticketAmt) {
         this.ticketAmt = ticketAmt;
      }

      public void setAcctBranch(String acctBranch) {
         this.acctBranch = acctBranch;
      }

      public void setSourceType(String sourceType) {
         this.sourceType = sourceType;
      }

      public void setDossDate(String dossDate) {
         this.dossDate = dossDate;
      }

      public void setOutBusiDate(String outBusiDate) {
         this.outBusiDate = outBusiDate;
      }

      public void setResFlag(String resFlag) {
         this.resFlag = resFlag;
      }

      public void setAmtWdrawnFlag(String amtWdrawnFlag) {
         this.amtWdrawnFlag = amtWdrawnFlag;
      }

      public void setUserId(String userId) {
         this.userId = userId;
      }

      public void setTranDate(String tranDate) {
         this.tranDate = tranDate;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100155Out.AcctArray)) {
            return false;
         } else {
            Core1400100155Out.AcctArray other = (Core1400100155Out.AcctArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label311: {
                  Object this$remark = this.getRemark();
                  Object other$remark = other.getRemark();
                  if (this$remark == null) {
                     if (other$remark == null) {
                        break label311;
                     }
                  } else if (this$remark.equals(other$remark)) {
                     break label311;
                  }

                  return false;
               }

               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo != null) {
                     return false;
                  }
               } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
                  return false;
               }

               label297: {
                  Object this$acctSeqNo = this.getAcctSeqNo();
                  Object other$acctSeqNo = other.getAcctSeqNo();
                  if (this$acctSeqNo == null) {
                     if (other$acctSeqNo == null) {
                        break label297;
                     }
                  } else if (this$acctSeqNo.equals(other$acctSeqNo)) {
                     break label297;
                  }

                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               label283: {
                  Object this$acctCcy = this.getAcctCcy();
                  Object other$acctCcy = other.getAcctCcy();
                  if (this$acctCcy == null) {
                     if (other$acctCcy == null) {
                        break label283;
                     }
                  } else if (this$acctCcy.equals(other$acctCcy)) {
                     break label283;
                  }

                  return false;
               }

               Object this$acctOpenDate = this.getAcctOpenDate();
               Object other$acctOpenDate = other.getAcctOpenDate();
               if (this$acctOpenDate == null) {
                  if (other$acctOpenDate != null) {
                     return false;
                  }
               } else if (!this$acctOpenDate.equals(other$acctOpenDate)) {
                  return false;
               }

               label269: {
                  Object this$acctName = this.getAcctName();
                  Object other$acctName = other.getAcctName();
                  if (this$acctName == null) {
                     if (other$acctName == null) {
                        break label269;
                     }
                  } else if (this$acctName.equals(other$acctName)) {
                     break label269;
                  }

                  return false;
               }

               label262: {
                  Object this$acctType = this.getAcctType();
                  Object other$acctType = other.getAcctType();
                  if (this$acctType == null) {
                     if (other$acctType == null) {
                        break label262;
                     }
                  } else if (this$acctType.equals(other$acctType)) {
                     break label262;
                  }

                  return false;
               }

               Object this$clientNo = this.getClientNo();
               Object other$clientNo = other.getClientNo();
               if (this$clientNo == null) {
                  if (other$clientNo != null) {
                     return false;
                  }
               } else if (!this$clientNo.equals(other$clientNo)) {
                  return false;
               }

               label248: {
                  Object this$documentId = this.getDocumentId();
                  Object other$documentId = other.getDocumentId();
                  if (this$documentId == null) {
                     if (other$documentId == null) {
                        break label248;
                     }
                  } else if (this$documentId.equals(other$documentId)) {
                     break label248;
                  }

                  return false;
               }

               label241: {
                  Object this$documentType = this.getDocumentType();
                  Object other$documentType = other.getDocumentType();
                  if (this$documentType == null) {
                     if (other$documentType == null) {
                        break label241;
                     }
                  } else if (this$documentType.equals(other$documentType)) {
                     break label241;
                  }

                  return false;
               }

               Object this$phoneNo = this.getPhoneNo();
               Object other$phoneNo = other.getPhoneNo();
               if (this$phoneNo == null) {
                  if (other$phoneNo != null) {
                     return false;
                  }
               } else if (!this$phoneNo.equals(other$phoneNo)) {
                  return false;
               }

               Object this$busiType = this.getBusiType();
               Object other$busiType = other.getBusiType();
               if (this$busiType == null) {
                  if (other$busiType != null) {
                     return false;
                  }
               } else if (!this$busiType.equals(other$busiType)) {
                  return false;
               }

               label220: {
                  Object this$tradeType = this.getTradeType();
                  Object other$tradeType = other.getTradeType();
                  if (this$tradeType == null) {
                     if (other$tradeType == null) {
                        break label220;
                     }
                  } else if (this$tradeType.equals(other$tradeType)) {
                     break label220;
                  }

                  return false;
               }

               Object this$intAmt = this.getIntAmt();
               Object other$intAmt = other.getIntAmt();
               if (this$intAmt == null) {
                  if (other$intAmt != null) {
                     return false;
                  }
               } else if (!this$intAmt.equals(other$intAmt)) {
                  return false;
               }

               Object this$balance = this.getBalance();
               Object other$balance = other.getBalance();
               if (this$balance == null) {
                  if (other$balance != null) {
                     return false;
                  }
               } else if (!this$balance.equals(other$balance)) {
                  return false;
               }

               label199: {
                  Object this$ticketAmt = this.getTicketAmt();
                  Object other$ticketAmt = other.getTicketAmt();
                  if (this$ticketAmt == null) {
                     if (other$ticketAmt == null) {
                        break label199;
                     }
                  } else if (this$ticketAmt.equals(other$ticketAmt)) {
                     break label199;
                  }

                  return false;
               }

               Object this$acctBranch = this.getAcctBranch();
               Object other$acctBranch = other.getAcctBranch();
               if (this$acctBranch == null) {
                  if (other$acctBranch != null) {
                     return false;
                  }
               } else if (!this$acctBranch.equals(other$acctBranch)) {
                  return false;
               }

               label185: {
                  Object this$sourceType = this.getSourceType();
                  Object other$sourceType = other.getSourceType();
                  if (this$sourceType == null) {
                     if (other$sourceType == null) {
                        break label185;
                     }
                  } else if (this$sourceType.equals(other$sourceType)) {
                     break label185;
                  }

                  return false;
               }

               Object this$dossDate = this.getDossDate();
               Object other$dossDate = other.getDossDate();
               if (this$dossDate == null) {
                  if (other$dossDate != null) {
                     return false;
                  }
               } else if (!this$dossDate.equals(other$dossDate)) {
                  return false;
               }

               label171: {
                  Object this$outBusiDate = this.getOutBusiDate();
                  Object other$outBusiDate = other.getOutBusiDate();
                  if (this$outBusiDate == null) {
                     if (other$outBusiDate == null) {
                        break label171;
                     }
                  } else if (this$outBusiDate.equals(other$outBusiDate)) {
                     break label171;
                  }

                  return false;
               }

               Object this$resFlag = this.getResFlag();
               Object other$resFlag = other.getResFlag();
               if (this$resFlag == null) {
                  if (other$resFlag != null) {
                     return false;
                  }
               } else if (!this$resFlag.equals(other$resFlag)) {
                  return false;
               }

               label157: {
                  Object this$amtWdrawnFlag = this.getAmtWdrawnFlag();
                  Object other$amtWdrawnFlag = other.getAmtWdrawnFlag();
                  if (this$amtWdrawnFlag == null) {
                     if (other$amtWdrawnFlag == null) {
                        break label157;
                     }
                  } else if (this$amtWdrawnFlag.equals(other$amtWdrawnFlag)) {
                     break label157;
                  }

                  return false;
               }

               label150: {
                  Object this$userId = this.getUserId();
                  Object other$userId = other.getUserId();
                  if (this$userId == null) {
                     if (other$userId == null) {
                        break label150;
                     }
                  } else if (this$userId.equals(other$userId)) {
                     break label150;
                  }

                  return false;
               }

               Object this$tranDate = this.getTranDate();
               Object other$tranDate = other.getTranDate();
               if (this$tranDate == null) {
                  if (other$tranDate != null) {
                     return false;
                  }
               } else if (!this$tranDate.equals(other$tranDate)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100155Out.AcctArray;
      }
      public String toString() {
         return "Core1400100155Out.AcctArray(remark=" + this.getRemark() + ", baseAcctNo=" + this.getBaseAcctNo() + ", acctSeqNo=" + this.getAcctSeqNo() + ", prodType=" + this.getProdType() + ", acctCcy=" + this.getAcctCcy() + ", acctOpenDate=" + this.getAcctOpenDate() + ", acctName=" + this.getAcctName() + ", acctType=" + this.getAcctType() + ", clientNo=" + this.getClientNo() + ", documentId=" + this.getDocumentId() + ", documentType=" + this.getDocumentType() + ", phoneNo=" + this.getPhoneNo() + ", busiType=" + this.getBusiType() + ", tradeType=" + this.getTradeType() + ", intAmt=" + this.getIntAmt() + ", balance=" + this.getBalance() + ", ticketAmt=" + this.getTicketAmt() + ", acctBranch=" + this.getAcctBranch() + ", sourceType=" + this.getSourceType() + ", dossDate=" + this.getDossDate() + ", outBusiDate=" + this.getOutBusiDate() + ", resFlag=" + this.getResFlag() + ", amtWdrawnFlag=" + this.getAmtWdrawnFlag() + ", userId=" + this.getUserId() + ", tranDate=" + this.getTranDate() + ")";
      }
   }
}
