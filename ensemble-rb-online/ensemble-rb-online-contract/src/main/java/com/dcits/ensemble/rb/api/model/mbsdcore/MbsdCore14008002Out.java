package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.util.List;

@MessageOut
public class MbsdCore14008002Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<MbsdCore14008002Out.AcctArray> acctArray;
   @V(
      desc = "是否黑名单客户",
      notNull = false,
      length = "1",
      remark = "是否黑名单客户",
      maxSize = 1
   )
   private String blacklistIndFlag;

   public List<MbsdCore14008002Out.AcctArray> getAcctArray() {
      return this.acctArray;
   }

   public String getBlacklistIndFlag() {
      return this.blacklistIndFlag;
   }

   public void setAcctArray(List<MbsdCore14008002Out.AcctArray> acctArray) {
      this.acctArray = acctArray;
   }

   public void setBlacklistIndFlag(String blacklistIndFlag) {
      this.blacklistIndFlag = blacklistIndFlag;
   }

   public String toString() {
      return "MbsdCore14008002Out(acctArray=" + this.getAcctArray() + ", blacklistIndFlag=" + this.getBlacklistIndFlag() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof MbsdCore14008002Out)) {
         return false;
      } else {
         MbsdCore14008002Out other = (MbsdCore14008002Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$acctArray = this.getAcctArray();
            Object other$acctArray = other.getAcctArray();
            if (this$acctArray == null) {
               if (other$acctArray != null) {
                  return false;
               }
            } else if (!this$acctArray.equals(other$acctArray)) {
               return false;
            }

            Object this$blacklistIndFlag = this.getBlacklistIndFlag();
            Object other$blacklistIndFlag = other.getBlacklistIndFlag();
            if (this$blacklistIndFlag == null) {
               if (other$blacklistIndFlag != null) {
                  return false;
               }
            } else if (!this$blacklistIndFlag.equals(other$blacklistIndFlag)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof MbsdCore14008002Out;
   }
   public static class AcctArray {
      @V(
         desc = "对手账户内部键",
         notNull = false,
         length = "15",
         remark = "对手账户内部键"
      )
      private Long othInternalKey;
      @V(
         desc = "对方账号/卡号",
         notNull = false,
         length = "50",
         remark = "对方账号卡号",
         maxSize = 50
      )
      private String othBaseAcctNo;
      @V(
         desc = "对方账户产品类型",
         notNull = false,
         length = "20",
         remark = "对方账户产品类型",
         maxSize = 20
      )
      private String othProdType;
      @V(
         desc = "对手账户币种",
         notNull = false,
         length = "3",
         remark = "对手账户币种",
         maxSize = 3
      )
      private String othCcy;
      @V(
         desc = "对方账户序列号",
         notNull = false,
         length = "5",
         remark = "对方账户序列号",
         maxSize = 5
      )
      private String othAcctSeqNo;
      @V(
         desc = "对方账户名称",
         notNull = false,
         length = "200",
         remark = "对方账户名称",
         maxSize = 200
      )
      private String othAcctName;
      @V(
         desc = "渠道",
         notNull = false,
         length = "10",
         remark = "渠道细类",
         maxSize = 10
      )
      private String channel;
      @V(
         desc = "法人",
         notNull = false,
         length = "20",
         remark = "法人",
         maxSize = 20
      )
      private String company;

      public Long getOthInternalKey() {
         return this.othInternalKey;
      }

      public String getOthBaseAcctNo() {
         return this.othBaseAcctNo;
      }

      public String getOthProdType() {
         return this.othProdType;
      }

      public String getOthCcy() {
         return this.othCcy;
      }

      public String getOthAcctSeqNo() {
         return this.othAcctSeqNo;
      }

      public String getOthAcctName() {
         return this.othAcctName;
      }

      public String getChannel() {
         return this.channel;
      }

      public String getCompany() {
         return this.company;
      }

      public void setOthInternalKey(Long othInternalKey) {
         this.othInternalKey = othInternalKey;
      }

      public void setOthBaseAcctNo(String othBaseAcctNo) {
         this.othBaseAcctNo = othBaseAcctNo;
      }

      public void setOthProdType(String othProdType) {
         this.othProdType = othProdType;
      }

      public void setOthCcy(String othCcy) {
         this.othCcy = othCcy;
      }

      public void setOthAcctSeqNo(String othAcctSeqNo) {
         this.othAcctSeqNo = othAcctSeqNo;
      }

      public void setOthAcctName(String othAcctName) {
         this.othAcctName = othAcctName;
      }

      public void setChannel(String channel) {
         this.channel = channel;
      }

      public void setCompany(String company) {
         this.company = company;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof MbsdCore14008002Out.AcctArray)) {
            return false;
         } else {
            MbsdCore14008002Out.AcctArray other = (MbsdCore14008002Out.AcctArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label107: {
                  Object this$othInternalKey = this.getOthInternalKey();
                  Object other$othInternalKey = other.getOthInternalKey();
                  if (this$othInternalKey == null) {
                     if (other$othInternalKey == null) {
                        break label107;
                     }
                  } else if (this$othInternalKey.equals(other$othInternalKey)) {
                     break label107;
                  }

                  return false;
               }

               Object this$othBaseAcctNo = this.getOthBaseAcctNo();
               Object other$othBaseAcctNo = other.getOthBaseAcctNo();
               if (this$othBaseAcctNo == null) {
                  if (other$othBaseAcctNo != null) {
                     return false;
                  }
               } else if (!this$othBaseAcctNo.equals(other$othBaseAcctNo)) {
                  return false;
               }

               Object this$othProdType = this.getOthProdType();
               Object other$othProdType = other.getOthProdType();
               if (this$othProdType == null) {
                  if (other$othProdType != null) {
                     return false;
                  }
               } else if (!this$othProdType.equals(other$othProdType)) {
                  return false;
               }

               label86: {
                  Object this$othCcy = this.getOthCcy();
                  Object other$othCcy = other.getOthCcy();
                  if (this$othCcy == null) {
                     if (other$othCcy == null) {
                        break label86;
                     }
                  } else if (this$othCcy.equals(other$othCcy)) {
                     break label86;
                  }

                  return false;
               }

               label79: {
                  Object this$othAcctSeqNo = this.getOthAcctSeqNo();
                  Object other$othAcctSeqNo = other.getOthAcctSeqNo();
                  if (this$othAcctSeqNo == null) {
                     if (other$othAcctSeqNo == null) {
                        break label79;
                     }
                  } else if (this$othAcctSeqNo.equals(other$othAcctSeqNo)) {
                     break label79;
                  }

                  return false;
               }

               label72: {
                  Object this$othAcctName = this.getOthAcctName();
                  Object other$othAcctName = other.getOthAcctName();
                  if (this$othAcctName == null) {
                     if (other$othAcctName == null) {
                        break label72;
                     }
                  } else if (this$othAcctName.equals(other$othAcctName)) {
                     break label72;
                  }

                  return false;
               }

               Object this$channel = this.getChannel();
               Object other$channel = other.getChannel();
               if (this$channel == null) {
                  if (other$channel != null) {
                     return false;
                  }
               } else if (!this$channel.equals(other$channel)) {
                  return false;
               }

               Object this$company = this.getCompany();
               Object other$company = other.getCompany();
               if (this$company == null) {
                  if (other$company != null) {
                     return false;
                  }
               } else if (!this$company.equals(other$company)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof MbsdCore14008002Out.AcctArray;
      }
      public String toString() {
         return "MbsdCore14008002Out.AcctArray(othInternalKey=" + this.getOthInternalKey() + ", othBaseAcctNo=" + this.getOthBaseAcctNo() + ", othProdType=" + this.getOthProdType() + ", othCcy=" + this.getOthCcy() + ", othAcctSeqNo=" + this.getOthAcctSeqNo() + ", othAcctName=" + this.getOthAcctName() + ", channel=" + this.getChannel() + ", company=" + this.getCompany() + ")";
      }
   }
}
