package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400090136In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400090136In.Body body;

   public Core1400090136In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400090136In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400090136In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400090136In)) {
         return false;
      } else {
         Core1400090136In other = (Core1400090136In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400090136In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "客户号",
         notNull = true,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;

      public String getClientNo() {
         return this.clientNo;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400090136In.Body)) {
            return false;
         } else {
            Core1400090136In.Body other = (Core1400090136In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$clientNo = this.getClientNo();
               Object other$clientNo = other.getClientNo();
               if (this$clientNo == null) {
                  if (other$clientNo != null) {
                     return false;
                  }
               } else if (!this$clientNo.equals(other$clientNo)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400090136In.Body;
      }
      public String toString() {
         return "Core1400090136In.Body(clientNo=" + this.getClientNo() + ")";
      }
   }
}
