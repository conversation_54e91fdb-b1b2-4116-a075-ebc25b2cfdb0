package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1200100228In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1200100228In.Body body;

   public Core1200100228In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1200100228In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1200100228In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200100228In)) {
         return false;
      } else {
         Core1200100228In other = (Core1200100228In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200100228In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "预约号",
         notNull = false,
         length = "50",
         remark = "预约编号",
         maxSize = 50
      )
      private String precontractNo;

      public String getPrecontractNo() {
         return this.precontractNo;
      }

      public void setPrecontractNo(String precontractNo) {
         this.precontractNo = precontractNo;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1200100228In.Body)) {
            return false;
         } else {
            Core1200100228In.Body other = (Core1200100228In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$precontractNo = this.getPrecontractNo();
               Object other$precontractNo = other.getPrecontractNo();
               if (this$precontractNo == null) {
                  if (other$precontractNo != null) {
                     return false;
                  }
               } else if (!this$precontractNo.equals(other$precontractNo)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1200100228In.Body;
      }
      public String toString() {
         return "Core1200100228In.Body(precontractNo=" + this.getPrecontractNo() + ")";
      }
   }
}
