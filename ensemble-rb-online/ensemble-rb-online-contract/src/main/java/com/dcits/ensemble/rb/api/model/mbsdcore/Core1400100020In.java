package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400100020In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400100020In.Body body;

   public Core1400100020In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400100020In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400100020In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100020In)) {
         return false;
      } else {
         Core1400100020In other = (Core1400100020In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100020In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "协议类型",
         notNull = false,
         length = "10",
         inDesc = "CLD-存立得,DC-大额存单,DLS-贷利省,HQB-活期宝,JDL-加多利,KDT-卡贷通,KYD-卡易贷,PCP-资金池,WDL-稳得利,XDB-协定宝,XDCK-协定存款产品,XDL-先得利,YBWL-一本万利,YCD-英才贷,YDT-易贷通,YHT-一户通,ZHY-周享赢,ZXY-坐享其盈,ZZB-至尊宝,LOA-贷款,ODF-法人透支协议,FIN-卡理财协议,SMS-短信,PKG-费用套餐,FEE-暂不收费,PCD-周期性强制扣划,ACC-协定存款协议,SWP-账户清扫协议,ID-智能存款协议,SL-金额补足协议,REC-回单签约,ES-电票签约,YD-约定,NTE-活期智能存款,PAS-隐私账户签约,BXD-协定利率（无留存）",
         remark = "协议类型",
         maxSize = 10
      )
      private String agreementType;
      @V(
         desc = "协议编号",
         notNull = false,
         length = "50",
         remark = "协议编号",
         maxSize = 50
      )
      private String agreementId;
      @V(
         desc = "账户状态",
         notNull = false,
         length = "1",
         inDesc = "N-新建,H-待激活,A-活动,D-睡眠,S-久悬,O-转营业外,P-逾期,C-关闭,I-预开户,R-预销户",
         remark = "描述账户生命周期不同阶段的划分",
         maxSize = 1
      )
      private String acctStatus;
      @V(
         desc = "开户起始日期",
         notNull = false,
         length = "8",
         remark = "开户起始日期",
         maxSize = 8
      )
      private String acctOpenStartDate;
      @V(
         desc = "开户终止日期",
         notNull = false,
         length = "8",
         remark = "开户终止日期",
         maxSize = 8
      )
      private String acctOpenEndDate;
      @V(
         desc = "查询所有信息（是否包含已销户状态）",
         notNull = false,
         length = "2",
         inDesc = "0-不包含已销户状态账户的交易信息,1-包含已销户状态账户的交易信息",
         remark = "查询所有信息（是否包含已销户状态）",
         maxSize = 2
      )
      private String statusFlag;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getAgreementType() {
         return this.agreementType;
      }

      public String getAgreementId() {
         return this.agreementId;
      }

      public String getAcctStatus() {
         return this.acctStatus;
      }

      public String getAcctOpenStartDate() {
         return this.acctOpenStartDate;
      }

      public String getAcctOpenEndDate() {
         return this.acctOpenEndDate;
      }

      public String getStatusFlag() {
         return this.statusFlag;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setAgreementType(String agreementType) {
         this.agreementType = agreementType;
      }

      public void setAgreementId(String agreementId) {
         this.agreementId = agreementId;
      }

      public void setAcctStatus(String acctStatus) {
         this.acctStatus = acctStatus;
      }

      public void setAcctOpenStartDate(String acctOpenStartDate) {
         this.acctOpenStartDate = acctOpenStartDate;
      }

      public void setAcctOpenEndDate(String acctOpenEndDate) {
         this.acctOpenEndDate = acctOpenEndDate;
      }

      public void setStatusFlag(String statusFlag) {
         this.statusFlag = statusFlag;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100020In.Body)) {
            return false;
         } else {
            Core1400100020In.Body other = (Core1400100020In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label143: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label143;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label143;
                  }

                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo != null) {
                     return false;
                  }
               } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                  return false;
               }

               label122: {
                  Object this$acctCcy = this.getAcctCcy();
                  Object other$acctCcy = other.getAcctCcy();
                  if (this$acctCcy == null) {
                     if (other$acctCcy == null) {
                        break label122;
                     }
                  } else if (this$acctCcy.equals(other$acctCcy)) {
                     break label122;
                  }

                  return false;
               }

               label115: {
                  Object this$agreementType = this.getAgreementType();
                  Object other$agreementType = other.getAgreementType();
                  if (this$agreementType == null) {
                     if (other$agreementType == null) {
                        break label115;
                     }
                  } else if (this$agreementType.equals(other$agreementType)) {
                     break label115;
                  }

                  return false;
               }

               Object this$agreementId = this.getAgreementId();
               Object other$agreementId = other.getAgreementId();
               if (this$agreementId == null) {
                  if (other$agreementId != null) {
                     return false;
                  }
               } else if (!this$agreementId.equals(other$agreementId)) {
                  return false;
               }

               Object this$acctStatus = this.getAcctStatus();
               Object other$acctStatus = other.getAcctStatus();
               if (this$acctStatus == null) {
                  if (other$acctStatus != null) {
                     return false;
                  }
               } else if (!this$acctStatus.equals(other$acctStatus)) {
                  return false;
               }

               label94: {
                  Object this$acctOpenStartDate = this.getAcctOpenStartDate();
                  Object other$acctOpenStartDate = other.getAcctOpenStartDate();
                  if (this$acctOpenStartDate == null) {
                     if (other$acctOpenStartDate == null) {
                        break label94;
                     }
                  } else if (this$acctOpenStartDate.equals(other$acctOpenStartDate)) {
                     break label94;
                  }

                  return false;
               }

               label87: {
                  Object this$acctOpenEndDate = this.getAcctOpenEndDate();
                  Object other$acctOpenEndDate = other.getAcctOpenEndDate();
                  if (this$acctOpenEndDate == null) {
                     if (other$acctOpenEndDate == null) {
                        break label87;
                     }
                  } else if (this$acctOpenEndDate.equals(other$acctOpenEndDate)) {
                     break label87;
                  }

                  return false;
               }

               Object this$statusFlag = this.getStatusFlag();
               Object other$statusFlag = other.getStatusFlag();
               if (this$statusFlag == null) {
                  if (other$statusFlag != null) {
                     return false;
                  }
               } else if (!this$statusFlag.equals(other$statusFlag)) {
                  return false;
               }

               Object this$clientNo = this.getClientNo();
               Object other$clientNo = other.getClientNo();
               if (this$clientNo == null) {
                  if (other$clientNo != null) {
                     return false;
                  }
               } else if (!this$clientNo.equals(other$clientNo)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100020In.Body;
      }
      public String toString() {
         return "Core1400100020In.Body(baseAcctNo=" + this.getBaseAcctNo() + ", prodType=" + this.getProdType() + ", acctSeqNo=" + this.getAcctSeqNo() + ", acctCcy=" + this.getAcctCcy() + ", agreementType=" + this.getAgreementType() + ", agreementId=" + this.getAgreementId() + ", acctStatus=" + this.getAcctStatus() + ", acctOpenStartDate=" + this.getAcctOpenStartDate() + ", acctOpenEndDate=" + this.getAcctOpenEndDate() + ", statusFlag=" + this.getStatusFlag() + ", clientNo=" + this.getClientNo() + ")";
      }
   }
}
