package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;

@MessageOut
public class Core1000100155Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "交易参考号",
      notNull = false,
      length = "50",
      remark = "交易参考号",
      maxSize = 50
   )
   private String reference;
   @V(
      desc = "挂账序列号",
      notNull = false,
      length = "50",
      remark = "挂账账户序列号",
      maxSize = 50
   )
   private String hangSeqNo;

   public String getReference() {
      return this.reference;
   }

   public String getHangSeqNo() {
      return this.hangSeqNo;
   }

   public void setReference(String reference) {
      this.reference = reference;
   }

   public void setHangSeqNo(String hangSeqNo) {
      this.hangSeqNo = hangSeqNo;
   }

   public String toString() {
      return "Core1000100155Out(reference=" + this.getReference() + ", hangSeqNo=" + this.getHangSeqNo() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1000100155Out)) {
         return false;
      } else {
         Core1000100155Out other = (Core1000100155Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$reference = this.getReference();
            Object other$reference = other.getReference();
            if (this$reference == null) {
               if (other$reference != null) {
                  return false;
               }
            } else if (!this$reference.equals(other$reference)) {
               return false;
            }

            Object this$hangSeqNo = this.getHangSeqNo();
            Object other$hangSeqNo = other.getHangSeqNo();
            if (this$hangSeqNo == null) {
               if (other$hangSeqNo != null) {
                  return false;
               }
            } else if (!this$hangSeqNo.equals(other$hangSeqNo)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1000100155Out;
   }
}
