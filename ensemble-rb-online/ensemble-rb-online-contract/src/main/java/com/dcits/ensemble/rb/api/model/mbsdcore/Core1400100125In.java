package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400100125In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400100125In.Body body;

   public Core1400100125In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400100125In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400100125In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100125In)) {
         return false;
      } else {
         Core1400100125In other = (Core1400100125In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100125In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "协议类型",
         notNull = false,
         length = "10",
         inDesc = "CLD-存立得,DC-大额存单,DLS-贷利省,HQB-活期宝,JDL-加多利,KDT-卡贷通,KYD-卡易贷,PCP-资金池,WDL-稳得利,XDB-协定宝,XDCK-协定存款产品,XDL-先得利,YBWL-一本万利,YCD-英才贷,YDT-易贷通,YHT-一户通,ZHY-周享赢,ZXY-坐享其盈,ZZB-至尊宝,LOA-贷款,ODF-法人透支协议,FIN-卡理财协议,SMS-短信,PKG-费用套餐,FEE-暂不收费,PCD-周期性强制扣划,ACC-协定存款协议,SWP-账户清扫协议,ID-智能存款协议,SL-金额补足协议,REC-回单签约,ES-电票签约,YD-约定,NTE-活期智能存款,PAS-隐私账户签约,BXD-协定利率（无留存）",
         remark = "协议类型",
         maxSize = 10
      )
      private String agreementType;
      @V(
         desc = "协议状态",
         notNull = false,
         length = "2",
         inDesc = "A-生效,E-失效",
         remark = "普通协议使用，可应用于大部分场景",
         maxSize = 2
      )
      private String agreementStatus;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public String getAgreementType() {
         return this.agreementType;
      }

      public String getAgreementStatus() {
         return this.agreementStatus;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setAgreementType(String agreementType) {
         this.agreementType = agreementType;
      }

      public void setAgreementStatus(String agreementStatus) {
         this.agreementStatus = agreementStatus;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100125In.Body)) {
            return false;
         } else {
            Core1400100125In.Body other = (Core1400100125In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label95: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label95;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label95;
                  }

                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo != null) {
                     return false;
                  }
               } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                  return false;
               }

               label74: {
                  Object this$acctCcy = this.getAcctCcy();
                  Object other$acctCcy = other.getAcctCcy();
                  if (this$acctCcy == null) {
                     if (other$acctCcy == null) {
                        break label74;
                     }
                  } else if (this$acctCcy.equals(other$acctCcy)) {
                     break label74;
                  }

                  return false;
               }

               label67: {
                  Object this$clientNo = this.getClientNo();
                  Object other$clientNo = other.getClientNo();
                  if (this$clientNo == null) {
                     if (other$clientNo == null) {
                        break label67;
                     }
                  } else if (this$clientNo.equals(other$clientNo)) {
                     break label67;
                  }

                  return false;
               }

               Object this$agreementType = this.getAgreementType();
               Object other$agreementType = other.getAgreementType();
               if (this$agreementType == null) {
                  if (other$agreementType != null) {
                     return false;
                  }
               } else if (!this$agreementType.equals(other$agreementType)) {
                  return false;
               }

               Object this$agreementStatus = this.getAgreementStatus();
               Object other$agreementStatus = other.getAgreementStatus();
               if (this$agreementStatus == null) {
                  if (other$agreementStatus != null) {
                     return false;
                  }
               } else if (!this$agreementStatus.equals(other$agreementStatus)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100125In.Body;
      }
      public String toString() {
         return "Core1400100125In.Body(baseAcctNo=" + this.getBaseAcctNo() + ", prodType=" + this.getProdType() + ", acctSeqNo=" + this.getAcctSeqNo() + ", acctCcy=" + this.getAcctCcy() + ", clientNo=" + this.getClientNo() + ", agreementType=" + this.getAgreementType() + ", agreementStatus=" + this.getAgreementStatus() + ")";
      }
   }
}
