package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;

@MessageIn
public class Core1200061077In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1200061077In.Body body;

   public Core1200061077In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1200061077In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1200061077In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200061077In)) {
         return false;
      } else {
         Core1200061077In other = (Core1200061077In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200061077In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "操作类型",
         notNull = false,
         length = "2",
         inDesc = "D-删除,U-修改,A-新增",
         remark = "操作类型",
         maxSize = 2
      )
      private String options;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "业务编号",
         notNull = false,
         length = "50",
         remark = "业务编号",
         maxSize = 50
      )
      private String busiNo;
      @V(
         desc = "兑换类型",
         notNull = false,
         length = "1",
         inDesc = "B-结汇,S-售汇,E-外币兑换",
         remark = "兑换类型",
         maxSize = 1
      )
      private String exType;
      @V(
         desc = "保证金账号",
         notNull = false,
         length = "50",
         remark = "保证金账号",
         maxSize = 50
      )
      private String depBaseAcctNo;
      @V(
         desc = "当日存入金额",
         notNull = false,
         length = "17",
         remark = "当日存入金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal depAmt;
      @V(
         desc = "保证金币种",
         notNull = false,
         length = "3",
         remark = "保证金币种",
         maxSize = 3
      )
      private String depCcy;
      @V(
         desc = "追缴保证金冻结编号",
         notNull = false,
         length = "200",
         remark = "追缴保证金冻结编号",
         maxSize = 200
      )
      private String depBlockNo;
      @V(
         desc = "损失外币金额",
         notNull = false,
         length = "17",
         remark = "损失外币金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal lossForeAmt;
      @V(
         desc = "损失人民币金额",
         notNull = false,
         length = "17",
         remark = "损失人民币金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal lossCnyAmt;
      @V(
         desc = "初次预警",
         notNull = false,
         length = "1",
         inDesc = "0-0,1-1",
         remark = "初次预警",
         maxSize = 1
      )
      private String waringFlag;
      @V(
         desc = "再次预警",
         notNull = false,
         length = "1",
         inDesc = "0-0,1-1",
         remark = "再次预警 0-预警 1 -不预警",
         maxSize = 1
      )
      private String thenWaringFlag;
      @V(
         desc = "备注",
         notNull = false,
         length = "200",
         remark = "备注",
         maxSize = 200
      )
      private String remark;
      @V(
         desc = "备注1",
         notNull = false,
         length = "200",
         remark = "备注1",
         maxSize = 200
      )
      private String remark1;
      @V(
         desc = "备注2",
         notNull = false,
         length = "200",
         remark = "备注2",
         maxSize = 200
      )
      private String remark2;

      public String getOptions() {
         return this.options;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public String getBusiNo() {
         return this.busiNo;
      }

      public String getExType() {
         return this.exType;
      }

      public String getDepBaseAcctNo() {
         return this.depBaseAcctNo;
      }

      public BigDecimal getDepAmt() {
         return this.depAmt;
      }

      public String getDepCcy() {
         return this.depCcy;
      }

      public String getDepBlockNo() {
         return this.depBlockNo;
      }

      public BigDecimal getLossForeAmt() {
         return this.lossForeAmt;
      }

      public BigDecimal getLossCnyAmt() {
         return this.lossCnyAmt;
      }

      public String getWaringFlag() {
         return this.waringFlag;
      }

      public String getThenWaringFlag() {
         return this.thenWaringFlag;
      }

      public String getRemark() {
         return this.remark;
      }

      public String getRemark1() {
         return this.remark1;
      }

      public String getRemark2() {
         return this.remark2;
      }

      public void setOptions(String options) {
         this.options = options;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setBusiNo(String busiNo) {
         this.busiNo = busiNo;
      }

      public void setExType(String exType) {
         this.exType = exType;
      }

      public void setDepBaseAcctNo(String depBaseAcctNo) {
         this.depBaseAcctNo = depBaseAcctNo;
      }

      public void setDepAmt(BigDecimal depAmt) {
         this.depAmt = depAmt;
      }

      public void setDepCcy(String depCcy) {
         this.depCcy = depCcy;
      }

      public void setDepBlockNo(String depBlockNo) {
         this.depBlockNo = depBlockNo;
      }

      public void setLossForeAmt(BigDecimal lossForeAmt) {
         this.lossForeAmt = lossForeAmt;
      }

      public void setLossCnyAmt(BigDecimal lossCnyAmt) {
         this.lossCnyAmt = lossCnyAmt;
      }

      public void setWaringFlag(String waringFlag) {
         this.waringFlag = waringFlag;
      }

      public void setThenWaringFlag(String thenWaringFlag) {
         this.thenWaringFlag = thenWaringFlag;
      }

      public void setRemark(String remark) {
         this.remark = remark;
      }

      public void setRemark1(String remark1) {
         this.remark1 = remark1;
      }

      public void setRemark2(String remark2) {
         this.remark2 = remark2;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1200061077In.Body)) {
            return false;
         } else {
            Core1200061077In.Body other = (Core1200061077In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label191: {
                  Object this$options = this.getOptions();
                  Object other$options = other.getOptions();
                  if (this$options == null) {
                     if (other$options == null) {
                        break label191;
                     }
                  } else if (this$options.equals(other$options)) {
                     break label191;
                  }

                  return false;
               }

               Object this$clientNo = this.getClientNo();
               Object other$clientNo = other.getClientNo();
               if (this$clientNo == null) {
                  if (other$clientNo != null) {
                     return false;
                  }
               } else if (!this$clientNo.equals(other$clientNo)) {
                  return false;
               }

               Object this$busiNo = this.getBusiNo();
               Object other$busiNo = other.getBusiNo();
               if (this$busiNo == null) {
                  if (other$busiNo != null) {
                     return false;
                  }
               } else if (!this$busiNo.equals(other$busiNo)) {
                  return false;
               }

               label170: {
                  Object this$exType = this.getExType();
                  Object other$exType = other.getExType();
                  if (this$exType == null) {
                     if (other$exType == null) {
                        break label170;
                     }
                  } else if (this$exType.equals(other$exType)) {
                     break label170;
                  }

                  return false;
               }

               label163: {
                  Object this$depBaseAcctNo = this.getDepBaseAcctNo();
                  Object other$depBaseAcctNo = other.getDepBaseAcctNo();
                  if (this$depBaseAcctNo == null) {
                     if (other$depBaseAcctNo == null) {
                        break label163;
                     }
                  } else if (this$depBaseAcctNo.equals(other$depBaseAcctNo)) {
                     break label163;
                  }

                  return false;
               }

               Object this$depAmt = this.getDepAmt();
               Object other$depAmt = other.getDepAmt();
               if (this$depAmt == null) {
                  if (other$depAmt != null) {
                     return false;
                  }
               } else if (!this$depAmt.equals(other$depAmt)) {
                  return false;
               }

               Object this$depCcy = this.getDepCcy();
               Object other$depCcy = other.getDepCcy();
               if (this$depCcy == null) {
                  if (other$depCcy != null) {
                     return false;
                  }
               } else if (!this$depCcy.equals(other$depCcy)) {
                  return false;
               }

               label142: {
                  Object this$depBlockNo = this.getDepBlockNo();
                  Object other$depBlockNo = other.getDepBlockNo();
                  if (this$depBlockNo == null) {
                     if (other$depBlockNo == null) {
                        break label142;
                     }
                  } else if (this$depBlockNo.equals(other$depBlockNo)) {
                     break label142;
                  }

                  return false;
               }

               label135: {
                  Object this$lossForeAmt = this.getLossForeAmt();
                  Object other$lossForeAmt = other.getLossForeAmt();
                  if (this$lossForeAmt == null) {
                     if (other$lossForeAmt == null) {
                        break label135;
                     }
                  } else if (this$lossForeAmt.equals(other$lossForeAmt)) {
                     break label135;
                  }

                  return false;
               }

               Object this$lossCnyAmt = this.getLossCnyAmt();
               Object other$lossCnyAmt = other.getLossCnyAmt();
               if (this$lossCnyAmt == null) {
                  if (other$lossCnyAmt != null) {
                     return false;
                  }
               } else if (!this$lossCnyAmt.equals(other$lossCnyAmt)) {
                  return false;
               }

               label121: {
                  Object this$waringFlag = this.getWaringFlag();
                  Object other$waringFlag = other.getWaringFlag();
                  if (this$waringFlag == null) {
                     if (other$waringFlag == null) {
                        break label121;
                     }
                  } else if (this$waringFlag.equals(other$waringFlag)) {
                     break label121;
                  }

                  return false;
               }

               Object this$thenWaringFlag = this.getThenWaringFlag();
               Object other$thenWaringFlag = other.getThenWaringFlag();
               if (this$thenWaringFlag == null) {
                  if (other$thenWaringFlag != null) {
                     return false;
                  }
               } else if (!this$thenWaringFlag.equals(other$thenWaringFlag)) {
                  return false;
               }

               label107: {
                  Object this$remark = this.getRemark();
                  Object other$remark = other.getRemark();
                  if (this$remark == null) {
                     if (other$remark == null) {
                        break label107;
                     }
                  } else if (this$remark.equals(other$remark)) {
                     break label107;
                  }

                  return false;
               }

               Object this$remark1 = this.getRemark1();
               Object other$remark1 = other.getRemark1();
               if (this$remark1 == null) {
                  if (other$remark1 != null) {
                     return false;
                  }
               } else if (!this$remark1.equals(other$remark1)) {
                  return false;
               }

               Object this$remark2 = this.getRemark2();
               Object other$remark2 = other.getRemark2();
               if (this$remark2 == null) {
                  if (other$remark2 != null) {
                     return false;
                  }
               } else if (!this$remark2.equals(other$remark2)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1200061077In.Body;
      }
      public String toString() {
         return "Core1200061077In.Body(options=" + this.getOptions() + ", clientNo=" + this.getClientNo() + ", busiNo=" + this.getBusiNo() + ", exType=" + this.getExType() + ", depBaseAcctNo=" + this.getDepBaseAcctNo() + ", depAmt=" + this.getDepAmt() + ", depCcy=" + this.getDepCcy() + ", depBlockNo=" + this.getDepBlockNo() + ", lossForeAmt=" + this.getLossForeAmt() + ", lossCnyAmt=" + this.getLossCnyAmt() + ", waringFlag=" + this.getWaringFlag() + ", thenWaringFlag=" + this.getThenWaringFlag() + ", remark=" + this.getRemark() + ", remark1=" + this.getRemark1() + ", remark2=" + this.getRemark2() + ")";
      }
   }
}
