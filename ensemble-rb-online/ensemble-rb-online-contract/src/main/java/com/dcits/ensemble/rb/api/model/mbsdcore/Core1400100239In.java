package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;

@MessageIn
public class Core1400100239In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400100239In.Body body;

   public Core1400100239In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400100239In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400100239In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100239In)) {
         return false;
      } else {
         Core1400100239In other = (Core1400100239In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100239In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "发行年度",
         notNull = false,
         length = "5",
         remark = "发行年度",
         maxSize = 5
      )
      private String issueYear;
      @V(
         desc = "期次代码",
         notNull = false,
         length = "50",
         remark = "期次代码",
         maxSize = 50
      )
      private String stageCode;
      @V(
         desc = "客户等级",
         notNull = false,
         length = "2",
         remark = "客户等级",
         maxSize = 2
      )
      private String clientLevel;
      @V(
         desc = "客户类型",
         notNull = false,
         length = "3",
         remark = "客户大类，目前一般分为个人，公司，金融机构和内部客户。取之于CIF_CLIENT_TYPE.CLIENT_TYPE",
         maxSize = 3
      )
      private String clientType;
      @V(
         desc = "出售分行或者出售机构",
         notNull = false,
         length = "500",
         remark = "出售分行或者出售机构，多可以是一个或者多个，定义多个时，需要用分隔符 | 进行分开存储",
         maxSize = 500
      )
      private String sellBranch;
      @V(
         desc = "可售渠道",
         notNull = false,
         length = "100",
         remark = "可售渠道",
         maxSize = 100
      )
      private String onSaleChannel;
      @V(
         desc = "境内境外标志",
         notNull = false,
         length = "1",
         inDesc = "I-境内,O-境外",
         remark = "境内境外标志",
         maxSize = 1
      )
      private String inlandOffshore;
      @V(
         desc = "客户等级更新日期",
         notNull = false,
         remark = "客户等级更新日期"
      )
      private String clientLevelDate;
      @V(
         desc = "剩余额度",
         notNull = false,
         length = "17",
         remark = "剩余额度",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal leaveLimit;
      @V(
         desc = "已分配额度",
         notNull = false,
         length = "17",
         remark = "已经分配的下放额度，由年度分配、或分配给机构、或分配给渠道，可以由下游合法使用的额度",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal distributeLimit;
      @V(
         desc = "支持组合购买方式",
         notNull = false,
         length = "1",
         remark = "1-单独购买2-组合购买3-单买与组合买",
         maxSize = 1
      )
      private String allowBuyWayCd;

      public String getClientNo() {
         return this.clientNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getCcy() {
         return this.ccy;
      }

      public String getIssueYear() {
         return this.issueYear;
      }

      public String getStageCode() {
         return this.stageCode;
      }

      public String getClientLevel() {
         return this.clientLevel;
      }

      public String getClientType() {
         return this.clientType;
      }

      public String getSellBranch() {
         return this.sellBranch;
      }

      public String getOnSaleChannel() {
         return this.onSaleChannel;
      }

      public String getInlandOffshore() {
         return this.inlandOffshore;
      }

      public String getClientLevelDate() {
         return this.clientLevelDate;
      }

      public BigDecimal getLeaveLimit() {
         return this.leaveLimit;
      }

      public BigDecimal getDistributeLimit() {
         return this.distributeLimit;
      }

      public String getAllowBuyWayCd() {
         return this.allowBuyWayCd;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setIssueYear(String issueYear) {
         this.issueYear = issueYear;
      }

      public void setStageCode(String stageCode) {
         this.stageCode = stageCode;
      }

      public void setClientLevel(String clientLevel) {
         this.clientLevel = clientLevel;
      }

      public void setClientType(String clientType) {
         this.clientType = clientType;
      }

      public void setSellBranch(String sellBranch) {
         this.sellBranch = sellBranch;
      }

      public void setOnSaleChannel(String onSaleChannel) {
         this.onSaleChannel = onSaleChannel;
      }

      public void setInlandOffshore(String inlandOffshore) {
         this.inlandOffshore = inlandOffshore;
      }

      public void setClientLevelDate(String clientLevelDate) {
         this.clientLevelDate = clientLevelDate;
      }

      public void setLeaveLimit(BigDecimal leaveLimit) {
         this.leaveLimit = leaveLimit;
      }

      public void setDistributeLimit(BigDecimal distributeLimit) {
         this.distributeLimit = distributeLimit;
      }

      public void setAllowBuyWayCd(String allowBuyWayCd) {
         this.allowBuyWayCd = allowBuyWayCd;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100239In.Body)) {
            return false;
         } else {
            Core1400100239In.Body other = (Core1400100239In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$clientNo = this.getClientNo();
               Object other$clientNo = other.getClientNo();
               if (this$clientNo == null) {
                  if (other$clientNo != null) {
                     return false;
                  }
               } else if (!this$clientNo.equals(other$clientNo)) {
                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               Object this$ccy = this.getCcy();
               Object other$ccy = other.getCcy();
               if (this$ccy == null) {
                  if (other$ccy != null) {
                     return false;
                  }
               } else if (!this$ccy.equals(other$ccy)) {
                  return false;
               }

               label158: {
                  Object this$issueYear = this.getIssueYear();
                  Object other$issueYear = other.getIssueYear();
                  if (this$issueYear == null) {
                     if (other$issueYear == null) {
                        break label158;
                     }
                  } else if (this$issueYear.equals(other$issueYear)) {
                     break label158;
                  }

                  return false;
               }

               label151: {
                  Object this$stageCode = this.getStageCode();
                  Object other$stageCode = other.getStageCode();
                  if (this$stageCode == null) {
                     if (other$stageCode == null) {
                        break label151;
                     }
                  } else if (this$stageCode.equals(other$stageCode)) {
                     break label151;
                  }

                  return false;
               }

               Object this$clientLevel = this.getClientLevel();
               Object other$clientLevel = other.getClientLevel();
               if (this$clientLevel == null) {
                  if (other$clientLevel != null) {
                     return false;
                  }
               } else if (!this$clientLevel.equals(other$clientLevel)) {
                  return false;
               }

               label137: {
                  Object this$clientType = this.getClientType();
                  Object other$clientType = other.getClientType();
                  if (this$clientType == null) {
                     if (other$clientType == null) {
                        break label137;
                     }
                  } else if (this$clientType.equals(other$clientType)) {
                     break label137;
                  }

                  return false;
               }

               label130: {
                  Object this$sellBranch = this.getSellBranch();
                  Object other$sellBranch = other.getSellBranch();
                  if (this$sellBranch == null) {
                     if (other$sellBranch == null) {
                        break label130;
                     }
                  } else if (this$sellBranch.equals(other$sellBranch)) {
                     break label130;
                  }

                  return false;
               }

               Object this$onSaleChannel = this.getOnSaleChannel();
               Object other$onSaleChannel = other.getOnSaleChannel();
               if (this$onSaleChannel == null) {
                  if (other$onSaleChannel != null) {
                     return false;
                  }
               } else if (!this$onSaleChannel.equals(other$onSaleChannel)) {
                  return false;
               }

               Object this$inlandOffshore = this.getInlandOffshore();
               Object other$inlandOffshore = other.getInlandOffshore();
               if (this$inlandOffshore == null) {
                  if (other$inlandOffshore != null) {
                     return false;
                  }
               } else if (!this$inlandOffshore.equals(other$inlandOffshore)) {
                  return false;
               }

               label109: {
                  Object this$clientLevelDate = this.getClientLevelDate();
                  Object other$clientLevelDate = other.getClientLevelDate();
                  if (this$clientLevelDate == null) {
                     if (other$clientLevelDate == null) {
                        break label109;
                     }
                  } else if (this$clientLevelDate.equals(other$clientLevelDate)) {
                     break label109;
                  }

                  return false;
               }

               label102: {
                  Object this$leaveLimit = this.getLeaveLimit();
                  Object other$leaveLimit = other.getLeaveLimit();
                  if (this$leaveLimit == null) {
                     if (other$leaveLimit == null) {
                        break label102;
                     }
                  } else if (this$leaveLimit.equals(other$leaveLimit)) {
                     break label102;
                  }

                  return false;
               }

               Object this$distributeLimit = this.getDistributeLimit();
               Object other$distributeLimit = other.getDistributeLimit();
               if (this$distributeLimit == null) {
                  if (other$distributeLimit != null) {
                     return false;
                  }
               } else if (!this$distributeLimit.equals(other$distributeLimit)) {
                  return false;
               }

               Object this$allowBuyWayCd = this.getAllowBuyWayCd();
               Object other$allowBuyWayCd = other.getAllowBuyWayCd();
               if (this$allowBuyWayCd == null) {
                  if (other$allowBuyWayCd != null) {
                     return false;
                  }
               } else if (!this$allowBuyWayCd.equals(other$allowBuyWayCd)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100239In.Body;
      }
      public String toString() {
         return "Core1400100239In.Body(clientNo=" + this.getClientNo() + ", prodType=" + this.getProdType() + ", ccy=" + this.getCcy() + ", issueYear=" + this.getIssueYear() + ", stageCode=" + this.getStageCode() + ", clientLevel=" + this.getClientLevel() + ", clientType=" + this.getClientType() + ", sellBranch=" + this.getSellBranch() + ", onSaleChannel=" + this.getOnSaleChannel() + ", inlandOffshore=" + this.getInlandOffshore() + ", clientLevelDate=" + this.getClientLevelDate() + ", leaveLimit=" + this.getLeaveLimit() + ", distributeLimit=" + this.getDistributeLimit() + ", allowBuyWayCd=" + this.getAllowBuyWayCd() + ")";
      }
   }
}
