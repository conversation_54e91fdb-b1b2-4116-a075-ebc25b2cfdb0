package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;

@MessageOut
public class Core1400053050Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "所属机构号",
      notNull = false,
      length = "50",
      remark = "机构代码",
      maxSize = 50
   )
   private String branch;
   @V(
      desc = "对公对私标志",
      notNull = false,
      length = "1",
      remark = "对公对私标志",
      maxSize = 1
   )
   private String individualFlag;
   @V(
      desc = "存管现管标志",
      notNull = false,
      length = "1",
      remark = "存管现管标志",
      maxSize = 1
   )
   private String cmCdFlag;
   @V(
      desc = "法人",
      notNull = false,
      length = "20",
      remark = "法人",
      maxSize = 20
   )
   private String company;

   public String getBranch() {
      return this.branch;
   }

   public String getIndividualFlag() {
      return this.individualFlag;
   }

   public String getCmCdFlag() {
      return this.cmCdFlag;
   }

   public String getCompany() {
      return this.company;
   }

   public void setBranch(String branch) {
      this.branch = branch;
   }

   public void setIndividualFlag(String individualFlag) {
      this.individualFlag = individualFlag;
   }

   public void setCmCdFlag(String cmCdFlag) {
      this.cmCdFlag = cmCdFlag;
   }

   public void setCompany(String company) {
      this.company = company;
   }

   public String toString() {
      return "Core1400053050Out(branch=" + this.getBranch() + ", individualFlag=" + this.getIndividualFlag() + ", cmCdFlag=" + this.getCmCdFlag() + ", company=" + this.getCompany() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400053050Out)) {
         return false;
      } else {
         Core1400053050Out other = (Core1400053050Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            label61: {
               Object this$branch = this.getBranch();
               Object other$branch = other.getBranch();
               if (this$branch == null) {
                  if (other$branch == null) {
                     break label61;
                  }
               } else if (this$branch.equals(other$branch)) {
                  break label61;
               }

               return false;
            }

            label54: {
               Object this$individualFlag = this.getIndividualFlag();
               Object other$individualFlag = other.getIndividualFlag();
               if (this$individualFlag == null) {
                  if (other$individualFlag == null) {
                     break label54;
                  }
               } else if (this$individualFlag.equals(other$individualFlag)) {
                  break label54;
               }

               return false;
            }

            Object this$cmCdFlag = this.getCmCdFlag();
            Object other$cmCdFlag = other.getCmCdFlag();
            if (this$cmCdFlag == null) {
               if (other$cmCdFlag != null) {
                  return false;
               }
            } else if (!this$cmCdFlag.equals(other$cmCdFlag)) {
               return false;
            }

            Object this$company = this.getCompany();
            Object other$company = other.getCompany();
            if (this$company == null) {
               if (other$company != null) {
                  return false;
               }
            } else if (!this$company.equals(other$company)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400053050Out;
   }
}
