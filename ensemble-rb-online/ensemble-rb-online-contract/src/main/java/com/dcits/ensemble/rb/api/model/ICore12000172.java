package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000172In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000172Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12000172 {
   String URL = "/rb/nfin/book/print";


   @ApiRemark("标准优化")
   @ApiDesc("用于对卡配对账簿未补登交易流水的补登，包括渠道类来往交易和无卡来账交易记录的打印。")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0172"
   )
   @BusinessCategory("1200-非金融")
   @FunctionCategory("RB06-凭证处理")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core12000172Out runService(Core12000172In var1);
}
