package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000210In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000210Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000210 {
   String URL = "/rb/inq/card/lock";

   
   @ApiRemark("标准优化")
   @ApiDesc("卡安全锁信息查询")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0210"
   )
   @BusinessCategory("1400-查询")
   @FunctionCategory("RB08-特殊业务")
   @ApiUseStatus("PRODUCT-产品")
   Core14000210Out runService(Core14000210In var1);
}
