package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12006018In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12006018Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12006018 {
   String URL = "/rb/nfin/card/reserve/apply/withdraw";


   @ApiRemark("自选卡号撤回")
   @ApiDesc("用于还未制卡的自编卡号撤回")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "6018"
   )
   @FunctionCategory("RB20-借记卡")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core12006018Out runService(Core12006018In var1);
}
