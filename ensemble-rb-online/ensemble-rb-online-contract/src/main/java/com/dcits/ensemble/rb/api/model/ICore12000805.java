package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000805In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000805Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12000805 {
   String URL = "/rb/nfin/ind/ageement/maint";


   @ApiRemark("标准优化,teller:3575-智能通知存款签约/解约")
   @ApiDesc("智能通知存款协议处理，分为签约、维护和解约三个功能。")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0805"
   )
   @BusinessCategory("1200-非金融")
   @FunctionCategory("RB05-协议管理")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core12000805Out runService(Core12000805In var1);
}
