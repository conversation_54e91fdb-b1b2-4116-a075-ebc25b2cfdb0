package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12005801In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12005801Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12005801 {
   String URL = "/rb/nfin/channel/receipt/print/insert";


   @ApiRemark("回单打印信息登记")
   @ApiDesc("贷款日终批量自动回收，将需要打印数据插入回单表")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "5801"
   )
   @BusinessCategory("1200-非金融")
   @ConsumeSys("CBS")
   Core12005801Out runService(Core12005801In var1);
}
