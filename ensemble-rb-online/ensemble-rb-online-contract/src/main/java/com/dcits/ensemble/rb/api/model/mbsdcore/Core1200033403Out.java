package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;

@MessageOut
public class Core1200033403Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "挂失申请书编号",
      notNull = false,
      length = "50",
      remark = "挂失申请书编号",
      maxSize = 50
   )
   private String lossNo;
   @V(
      desc = "业务流水号",
      notNull = false,
      length = "50",
      remark = "支付流水号",
      maxSize = 50
   )
   private String serialNo;

   public String getLossNo() {
      return this.lossNo;
   }

   public String getSerialNo() {
      return this.serialNo;
   }

   public void setLossNo(String lossNo) {
      this.lossNo = lossNo;
   }

   public void setSerialNo(String serialNo) {
      this.serialNo = serialNo;
   }

   public String toString() {
      return "Core1200033403Out(lossNo=" + this.getLossNo() + ", serialNo=" + this.getSerialNo() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200033403Out)) {
         return false;
      } else {
         Core1200033403Out other = (Core1200033403Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$lossNo = this.getLossNo();
            Object other$lossNo = other.getLossNo();
            if (this$lossNo == null) {
               if (other$lossNo != null) {
                  return false;
               }
            } else if (!this$lossNo.equals(other$lossNo)) {
               return false;
            }

            Object this$serialNo = this.getSerialNo();
            Object other$serialNo = other.getSerialNo();
            if (this$serialNo == null) {
               if (other$serialNo != null) {
                  return false;
               }
            } else if (!this$serialNo.equals(other$serialNo)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200033403Out;
   }
}
