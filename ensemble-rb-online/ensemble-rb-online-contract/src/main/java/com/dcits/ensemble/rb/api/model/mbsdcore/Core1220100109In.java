package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1220100109In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1220100109In.Body body;

   public Core1220100109In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1220100109In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1220100109In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1220100109In)) {
         return false;
      } else {
         Core1220100109In other = (Core1220100109In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1220100109In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "批次号",
         notNull = false,
         length = "50",
         remark = "批次号",
         maxSize = 50
      )
      private String batchNo;
      @V(
         desc = "凭证类型",
         notNull = true,
         length = "10",
         remark = "凭证类型",
         maxSize = 10
      )
      private String docType;
      @V(
         desc = "支取方式",
         notNull = true,
         length = "1",
         inDesc = "S-凭印鉴支取,P-凭密码支取,W-无密码无印鉴支取,B-凭印鉴和密码支取,O-凭证件支取,R-支付密码器和印鉴",
         remark = "支取方式",
         maxSize = 1
      )
      private String withdrawalType;
      @V(
         desc = "批量开户币种",
         notNull = true,
         length = "3",
         remark = "批量开户币种",
         maxSize = 3
      )
      private String openCcy;
      @V(
         desc = "产品类型",
         notNull = true,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户属性",
         notNull = true,
         length = "10",
         remark = "账户属性",
         maxSize = 10
      )
      private String acctNature;

      public String getBatchNo() {
         return this.batchNo;
      }

      public String getDocType() {
         return this.docType;
      }

      public String getWithdrawalType() {
         return this.withdrawalType;
      }

      public String getOpenCcy() {
         return this.openCcy;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctNature() {
         return this.acctNature;
      }

      public void setBatchNo(String batchNo) {
         this.batchNo = batchNo;
      }

      public void setDocType(String docType) {
         this.docType = docType;
      }

      public void setWithdrawalType(String withdrawalType) {
         this.withdrawalType = withdrawalType;
      }

      public void setOpenCcy(String openCcy) {
         this.openCcy = openCcy;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctNature(String acctNature) {
         this.acctNature = acctNature;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1220100109In.Body)) {
            return false;
         } else {
            Core1220100109In.Body other = (Core1220100109In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$batchNo = this.getBatchNo();
               Object other$batchNo = other.getBatchNo();
               if (this$batchNo == null) {
                  if (other$batchNo != null) {
                     return false;
                  }
               } else if (!this$batchNo.equals(other$batchNo)) {
                  return false;
               }

               Object this$docType = this.getDocType();
               Object other$docType = other.getDocType();
               if (this$docType == null) {
                  if (other$docType != null) {
                     return false;
                  }
               } else if (!this$docType.equals(other$docType)) {
                  return false;
               }

               Object this$withdrawalType = this.getWithdrawalType();
               Object other$withdrawalType = other.getWithdrawalType();
               if (this$withdrawalType == null) {
                  if (other$withdrawalType != null) {
                     return false;
                  }
               } else if (!this$withdrawalType.equals(other$withdrawalType)) {
                  return false;
               }

               label62: {
                  Object this$openCcy = this.getOpenCcy();
                  Object other$openCcy = other.getOpenCcy();
                  if (this$openCcy == null) {
                     if (other$openCcy == null) {
                        break label62;
                     }
                  } else if (this$openCcy.equals(other$openCcy)) {
                     break label62;
                  }

                  return false;
               }

               label55: {
                  Object this$prodType = this.getProdType();
                  Object other$prodType = other.getProdType();
                  if (this$prodType == null) {
                     if (other$prodType == null) {
                        break label55;
                     }
                  } else if (this$prodType.equals(other$prodType)) {
                     break label55;
                  }

                  return false;
               }

               Object this$acctNature = this.getAcctNature();
               Object other$acctNature = other.getAcctNature();
               if (this$acctNature == null) {
                  if (other$acctNature != null) {
                     return false;
                  }
               } else if (!this$acctNature.equals(other$acctNature)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1220100109In.Body;
      }
      public String toString() {
         return "Core1220100109In.Body(batchNo=" + this.getBatchNo() + ", docType=" + this.getDocType() + ", withdrawalType=" + this.getWithdrawalType() + ", openCcy=" + this.getOpenCcy() + ", prodType=" + this.getProdType() + ", acctNature=" + this.getAcctNature() + ")";
      }
   }
}
