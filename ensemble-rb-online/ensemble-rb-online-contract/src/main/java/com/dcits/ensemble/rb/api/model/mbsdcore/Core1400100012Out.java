package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.util.List;

@MessageOut
public class Core1400100012Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100012Out.AcctArray> acctArray;

   public List<Core1400100012Out.AcctArray> getAcctArray() {
      return this.acctArray;
   }

   public void setAcctArray(List<Core1400100012Out.AcctArray> acctArray) {
      this.acctArray = acctArray;
   }

   public String toString() {
      return "Core1400100012Out(acctArray=" + this.getAcctArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100012Out)) {
         return false;
      } else {
         Core1400100012Out other = (Core1400100012Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$acctArray = this.getAcctArray();
            Object other$acctArray = other.getAcctArray();
            if (this$acctArray == null) {
               if (other$acctArray != null) {
                  return false;
               }
            } else if (!this$acctArray.equals(other$acctArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100012Out;
   }
   public static class AcctArray {
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "变更操作类型",
         notNull = false,
         length = "2",
         remark = "变更操作类型",
         maxSize = 2
      )
      private String amendOperateType;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "结算账号",
         notNull = false,
         length = "50",
         remark = "结算账号",
         maxSize = 50
      )
      private String settleBaseAcctNo;
      @V(
         desc = "结算账户序号",
         notNull = false,
         length = "5",
         remark = "结算账户序号",
         maxSize = 5
      )
      private String settleAcctSeqNo;
      @V(
         desc = "结算币种",
         notNull = false,
         length = "3",
         remark = "结算币种",
         maxSize = 3
      )
      private String settleCcy;
      @V(
         desc = "结算账户产品类型",
         notNull = false,
         length = "20",
         remark = "结算账户产品类型",
         maxSize = 20
      )
      private String settleProdType;
      @V(
         desc = "本行他行标志",
         notNull = false,
         length = "1",
         remark = "本行他行标志",
         maxSize = 1
      )
      private String bhthFlag;
      @V(
         desc = "账户开户行",
         notNull = false,
         length = "50",
         remark = "账户实际开户机构，柜面为实际网点机构，线上渠道一般为对应主账户的实际开户机构",
         maxSize = 50
      )
      private String acctBranch;
      @V(
         desc = "机构名称",
         notNull = false,
         length = "200",
         remark = "机构名称",
         maxSize = 200
      )
      private String branchName;
      @V(
         desc = "绑定账户手机号码",
         notNull = false,
         length = "20",
         remark = "绑定账户手机号码",
         maxSize = 20
      )
      private String settleMobilePhone;
      @V(
         desc = "开户银行金融机构编码",
         notNull = false,
         length = "20",
         remark = "开户银行金融机构编码",
         maxSize = 20
      )
      private String bindAcctBranch;
      @V(
         desc = "最后修改日期",
         notNull = false,
         remark = "最后修改日期"
      )
      private String lastChangeDate;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getAmendOperateType() {
         return this.amendOperateType;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getCcy() {
         return this.ccy;
      }

      public String getSettleBaseAcctNo() {
         return this.settleBaseAcctNo;
      }

      public String getSettleAcctSeqNo() {
         return this.settleAcctSeqNo;
      }

      public String getSettleCcy() {
         return this.settleCcy;
      }

      public String getSettleProdType() {
         return this.settleProdType;
      }

      public String getBhthFlag() {
         return this.bhthFlag;
      }

      public String getAcctBranch() {
         return this.acctBranch;
      }

      public String getBranchName() {
         return this.branchName;
      }

      public String getSettleMobilePhone() {
         return this.settleMobilePhone;
      }

      public String getBindAcctBranch() {
         return this.bindAcctBranch;
      }

      public String getLastChangeDate() {
         return this.lastChangeDate;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setAmendOperateType(String amendOperateType) {
         this.amendOperateType = amendOperateType;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setSettleBaseAcctNo(String settleBaseAcctNo) {
         this.settleBaseAcctNo = settleBaseAcctNo;
      }

      public void setSettleAcctSeqNo(String settleAcctSeqNo) {
         this.settleAcctSeqNo = settleAcctSeqNo;
      }

      public void setSettleCcy(String settleCcy) {
         this.settleCcy = settleCcy;
      }

      public void setSettleProdType(String settleProdType) {
         this.settleProdType = settleProdType;
      }

      public void setBhthFlag(String bhthFlag) {
         this.bhthFlag = bhthFlag;
      }

      public void setAcctBranch(String acctBranch) {
         this.acctBranch = acctBranch;
      }

      public void setBranchName(String branchName) {
         this.branchName = branchName;
      }

      public void setSettleMobilePhone(String settleMobilePhone) {
         this.settleMobilePhone = settleMobilePhone;
      }

      public void setBindAcctBranch(String bindAcctBranch) {
         this.bindAcctBranch = bindAcctBranch;
      }

      public void setLastChangeDate(String lastChangeDate) {
         this.lastChangeDate = lastChangeDate;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100012Out.AcctArray)) {
            return false;
         } else {
            Core1400100012Out.AcctArray other = (Core1400100012Out.AcctArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label191: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label191;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label191;
                  }

                  return false;
               }

               Object this$amendOperateType = this.getAmendOperateType();
               Object other$amendOperateType = other.getAmendOperateType();
               if (this$amendOperateType == null) {
                  if (other$amendOperateType != null) {
                     return false;
                  }
               } else if (!this$amendOperateType.equals(other$amendOperateType)) {
                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               label170: {
                  Object this$acctSeqNo = this.getAcctSeqNo();
                  Object other$acctSeqNo = other.getAcctSeqNo();
                  if (this$acctSeqNo == null) {
                     if (other$acctSeqNo == null) {
                        break label170;
                     }
                  } else if (this$acctSeqNo.equals(other$acctSeqNo)) {
                     break label170;
                  }

                  return false;
               }

               label163: {
                  Object this$ccy = this.getCcy();
                  Object other$ccy = other.getCcy();
                  if (this$ccy == null) {
                     if (other$ccy == null) {
                        break label163;
                     }
                  } else if (this$ccy.equals(other$ccy)) {
                     break label163;
                  }

                  return false;
               }

               Object this$settleBaseAcctNo = this.getSettleBaseAcctNo();
               Object other$settleBaseAcctNo = other.getSettleBaseAcctNo();
               if (this$settleBaseAcctNo == null) {
                  if (other$settleBaseAcctNo != null) {
                     return false;
                  }
               } else if (!this$settleBaseAcctNo.equals(other$settleBaseAcctNo)) {
                  return false;
               }

               Object this$settleAcctSeqNo = this.getSettleAcctSeqNo();
               Object other$settleAcctSeqNo = other.getSettleAcctSeqNo();
               if (this$settleAcctSeqNo == null) {
                  if (other$settleAcctSeqNo != null) {
                     return false;
                  }
               } else if (!this$settleAcctSeqNo.equals(other$settleAcctSeqNo)) {
                  return false;
               }

               label142: {
                  Object this$settleCcy = this.getSettleCcy();
                  Object other$settleCcy = other.getSettleCcy();
                  if (this$settleCcy == null) {
                     if (other$settleCcy == null) {
                        break label142;
                     }
                  } else if (this$settleCcy.equals(other$settleCcy)) {
                     break label142;
                  }

                  return false;
               }

               label135: {
                  Object this$settleProdType = this.getSettleProdType();
                  Object other$settleProdType = other.getSettleProdType();
                  if (this$settleProdType == null) {
                     if (other$settleProdType == null) {
                        break label135;
                     }
                  } else if (this$settleProdType.equals(other$settleProdType)) {
                     break label135;
                  }

                  return false;
               }

               Object this$bhthFlag = this.getBhthFlag();
               Object other$bhthFlag = other.getBhthFlag();
               if (this$bhthFlag == null) {
                  if (other$bhthFlag != null) {
                     return false;
                  }
               } else if (!this$bhthFlag.equals(other$bhthFlag)) {
                  return false;
               }

               label121: {
                  Object this$acctBranch = this.getAcctBranch();
                  Object other$acctBranch = other.getAcctBranch();
                  if (this$acctBranch == null) {
                     if (other$acctBranch == null) {
                        break label121;
                     }
                  } else if (this$acctBranch.equals(other$acctBranch)) {
                     break label121;
                  }

                  return false;
               }

               Object this$branchName = this.getBranchName();
               Object other$branchName = other.getBranchName();
               if (this$branchName == null) {
                  if (other$branchName != null) {
                     return false;
                  }
               } else if (!this$branchName.equals(other$branchName)) {
                  return false;
               }

               label107: {
                  Object this$settleMobilePhone = this.getSettleMobilePhone();
                  Object other$settleMobilePhone = other.getSettleMobilePhone();
                  if (this$settleMobilePhone == null) {
                     if (other$settleMobilePhone == null) {
                        break label107;
                     }
                  } else if (this$settleMobilePhone.equals(other$settleMobilePhone)) {
                     break label107;
                  }

                  return false;
               }

               Object this$bindAcctBranch = this.getBindAcctBranch();
               Object other$bindAcctBranch = other.getBindAcctBranch();
               if (this$bindAcctBranch == null) {
                  if (other$bindAcctBranch != null) {
                     return false;
                  }
               } else if (!this$bindAcctBranch.equals(other$bindAcctBranch)) {
                  return false;
               }

               Object this$lastChangeDate = this.getLastChangeDate();
               Object other$lastChangeDate = other.getLastChangeDate();
               if (this$lastChangeDate == null) {
                  if (other$lastChangeDate != null) {
                     return false;
                  }
               } else if (!this$lastChangeDate.equals(other$lastChangeDate)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100012Out.AcctArray;
      }
      public String toString() {
         return "Core1400100012Out.AcctArray(baseAcctNo=" + this.getBaseAcctNo() + ", amendOperateType=" + this.getAmendOperateType() + ", prodType=" + this.getProdType() + ", acctSeqNo=" + this.getAcctSeqNo() + ", ccy=" + this.getCcy() + ", settleBaseAcctNo=" + this.getSettleBaseAcctNo() + ", settleAcctSeqNo=" + this.getSettleAcctSeqNo() + ", settleCcy=" + this.getSettleCcy() + ", settleProdType=" + this.getSettleProdType() + ", bhthFlag=" + this.getBhthFlag() + ", acctBranch=" + this.getAcctBranch() + ", branchName=" + this.getBranchName() + ", settleMobilePhone=" + this.getSettleMobilePhone() + ", bindAcctBranch=" + this.getBindAcctBranch() + ", lastChangeDate=" + this.getLastChangeDate() + ")";
      }
   }
}
