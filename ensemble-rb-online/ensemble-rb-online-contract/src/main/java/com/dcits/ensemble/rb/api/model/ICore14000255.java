package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000255In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000255Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000255 {
   String URL = "/rb/inq/acct/register";


   @ApiRemark("账户登记簿查询")
   @ApiDesc("账户登记簿查询")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0255"
   )
   @BusinessCategory("1400-查询")
   @FunctionCategory("RB02-账户管理")
   @ConsumeSys("137")
   @ApiUseStatus("PRODUCT-产品")
   Core14000255Out runService(Core14000255In var1);
}
