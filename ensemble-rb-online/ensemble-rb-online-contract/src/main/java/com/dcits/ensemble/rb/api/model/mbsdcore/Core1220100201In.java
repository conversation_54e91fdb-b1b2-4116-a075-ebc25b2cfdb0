package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;

@MessageIn
public class Core1220100201In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1220100201In.Body body;

   public Core1220100201In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1220100201In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1220100201In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1220100201In)) {
         return false;
      } else {
         Core1220100201In other = (Core1220100201In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1220100201In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "账号/卡号",
         notNull = true,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户序号",
         notNull = true,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "交易金额",
         notNull = true,
         length = "17",
         remark = "交易金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal tranAmt;
      @V(
         desc = "交易类型",
         notNull = true,
         length = "10",
         remark = "交易类型",
         maxSize = 10
      )
      private String tranType;
      @V(
         desc = "交易币种",
         notNull = true,
         length = "3",
         remark = "交易币种",
         maxSize = 3
      )
      private String tranCcy;
      @V(
         desc = "费用金额",
         notNull = false,
         length = "17",
         remark = "费用金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal feeAmt;
      @V(
         desc = "收费币种",
         notNull = false,
         length = "3",
         remark = "收费币种",
         maxSize = 3
      )
      private String feeCcy;
      @V(
         desc = "摘要",
         notNull = false,
         length = "500",
         remark = "开户时的账号用途，销户时的销户原因",
         maxSize = 500
      )
      private String narrative;
      @V(
         desc = "摘要码",
         notNull = false,
         length = "30",
         remark = "摘要码",
         maxSize = 30
      )
      private String narrativeCode;
      @V(
         desc = "收取标志",
         notNull = false,
         length = "1",
         inDesc = "C-现金收取,T-转账收取,N-暂不收取,P-套餐内抵用",
         remark = "收取标志",
         maxSize = 1
      )
      private String chargeMode;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public BigDecimal getTranAmt() {
         return this.tranAmt;
      }

      public String getTranType() {
         return this.tranType;
      }

      public String getTranCcy() {
         return this.tranCcy;
      }

      public BigDecimal getFeeAmt() {
         return this.feeAmt;
      }

      public String getFeeCcy() {
         return this.feeCcy;
      }

      public String getNarrative() {
         return this.narrative;
      }

      public String getNarrativeCode() {
         return this.narrativeCode;
      }

      public String getChargeMode() {
         return this.chargeMode;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setTranAmt(BigDecimal tranAmt) {
         this.tranAmt = tranAmt;
      }

      public void setTranType(String tranType) {
         this.tranType = tranType;
      }

      public void setTranCcy(String tranCcy) {
         this.tranCcy = tranCcy;
      }

      public void setFeeAmt(BigDecimal feeAmt) {
         this.feeAmt = feeAmt;
      }

      public void setFeeCcy(String feeCcy) {
         this.feeCcy = feeCcy;
      }

      public void setNarrative(String narrative) {
         this.narrative = narrative;
      }

      public void setNarrativeCode(String narrativeCode) {
         this.narrativeCode = narrativeCode;
      }

      public void setChargeMode(String chargeMode) {
         this.chargeMode = chargeMode;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1220100201In.Body)) {
            return false;
         } else {
            Core1220100201In.Body other = (Core1220100201In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label155: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label155;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label155;
                  }

                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo != null) {
                     return false;
                  }
               } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                  return false;
               }

               label134: {
                  Object this$acctCcy = this.getAcctCcy();
                  Object other$acctCcy = other.getAcctCcy();
                  if (this$acctCcy == null) {
                     if (other$acctCcy == null) {
                        break label134;
                     }
                  } else if (this$acctCcy.equals(other$acctCcy)) {
                     break label134;
                  }

                  return false;
               }

               label127: {
                  Object this$tranAmt = this.getTranAmt();
                  Object other$tranAmt = other.getTranAmt();
                  if (this$tranAmt == null) {
                     if (other$tranAmt == null) {
                        break label127;
                     }
                  } else if (this$tranAmt.equals(other$tranAmt)) {
                     break label127;
                  }

                  return false;
               }

               label120: {
                  Object this$tranType = this.getTranType();
                  Object other$tranType = other.getTranType();
                  if (this$tranType == null) {
                     if (other$tranType == null) {
                        break label120;
                     }
                  } else if (this$tranType.equals(other$tranType)) {
                     break label120;
                  }

                  return false;
               }

               Object this$tranCcy = this.getTranCcy();
               Object other$tranCcy = other.getTranCcy();
               if (this$tranCcy == null) {
                  if (other$tranCcy != null) {
                     return false;
                  }
               } else if (!this$tranCcy.equals(other$tranCcy)) {
                  return false;
               }

               label106: {
                  Object this$feeAmt = this.getFeeAmt();
                  Object other$feeAmt = other.getFeeAmt();
                  if (this$feeAmt == null) {
                     if (other$feeAmt == null) {
                        break label106;
                     }
                  } else if (this$feeAmt.equals(other$feeAmt)) {
                     break label106;
                  }

                  return false;
               }

               Object this$feeCcy = this.getFeeCcy();
               Object other$feeCcy = other.getFeeCcy();
               if (this$feeCcy == null) {
                  if (other$feeCcy != null) {
                     return false;
                  }
               } else if (!this$feeCcy.equals(other$feeCcy)) {
                  return false;
               }

               label92: {
                  Object this$narrative = this.getNarrative();
                  Object other$narrative = other.getNarrative();
                  if (this$narrative == null) {
                     if (other$narrative == null) {
                        break label92;
                     }
                  } else if (this$narrative.equals(other$narrative)) {
                     break label92;
                  }

                  return false;
               }

               Object this$narrativeCode = this.getNarrativeCode();
               Object other$narrativeCode = other.getNarrativeCode();
               if (this$narrativeCode == null) {
                  if (other$narrativeCode != null) {
                     return false;
                  }
               } else if (!this$narrativeCode.equals(other$narrativeCode)) {
                  return false;
               }

               Object this$chargeMode = this.getChargeMode();
               Object other$chargeMode = other.getChargeMode();
               if (this$chargeMode == null) {
                  if (other$chargeMode != null) {
                     return false;
                  }
               } else if (!this$chargeMode.equals(other$chargeMode)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1220100201In.Body;
      }
      public String toString() {
         return "Core1220100201In.Body(baseAcctNo=" + this.getBaseAcctNo() + ", prodType=" + this.getProdType() + ", acctSeqNo=" + this.getAcctSeqNo() + ", acctCcy=" + this.getAcctCcy() + ", tranAmt=" + this.getTranAmt() + ", tranType=" + this.getTranType() + ", tranCcy=" + this.getTranCcy() + ", feeAmt=" + this.getFeeAmt() + ", feeCcy=" + this.getFeeCcy() + ", narrative=" + this.getNarrative() + ", narrativeCode=" + this.getNarrativeCode() + ", chargeMode=" + this.getChargeMode() + ")";
      }
   }
}
