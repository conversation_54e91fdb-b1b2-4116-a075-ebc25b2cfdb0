package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400100220In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400100220In.Body body;

   public Core1400100220In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400100220In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400100220In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100220In)) {
         return false;
      } else {
         Core1400100220In other = (Core1400100220In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100220In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "客户类型",
         notNull = true,
         length = "3",
         remark = "客户大类，目前一般分为个人，公司，金融机构和内部客户。取之于CIF_CLIENT_TYPE.CLIENT_TYPE",
         maxSize = 3
      )
      private String clientType;
      @V(
         desc = "发行年度",
         notNull = true,
         length = "5",
         remark = "发行年度",
         maxSize = 5
      )
      private String issueYear;
      @V(
         desc = "存期期限",
         notNull = true,
         length = "5",
         remark = "期限",
         maxSize = 5
      )
      private String term;
      @V(
         desc = "期限类型",
         notNull = true,
         length = "1",
         inDesc = "Y-年,Q-季,M-月,W-周,D-日",
         remark = "期限类型",
         maxSize = 1
      )
      private String termType;
      @V(
         desc = "出售分行或者出售机构",
         notNull = false,
         length = "500",
         remark = "出售分行或者出售机构，多可以是一个或者多个，定义多个时，需要用分隔符 | 进行分开存储",
         maxSize = 500
      )
      private String sellBranch;
      @V(
         desc = "发行日期",
         notNull = false,
         remark = "发行日期"
      )
      private String issueDate;
      @V(
         desc = "期次类型",
         notNull = true,
         length = "1",
         inDesc = "A-个人C,B-对公A,C-机构B",
         remark = "个人C，对公A，机构B",
         maxSize = 1
      )
      private String stageType;

      public String getClientType() {
         return this.clientType;
      }

      public String getIssueYear() {
         return this.issueYear;
      }

      public String getTerm() {
         return this.term;
      }

      public String getTermType() {
         return this.termType;
      }

      public String getSellBranch() {
         return this.sellBranch;
      }

      public String getIssueDate() {
         return this.issueDate;
      }

      public String getStageType() {
         return this.stageType;
      }

      public void setClientType(String clientType) {
         this.clientType = clientType;
      }

      public void setIssueYear(String issueYear) {
         this.issueYear = issueYear;
      }

      public void setTerm(String term) {
         this.term = term;
      }

      public void setTermType(String termType) {
         this.termType = termType;
      }

      public void setSellBranch(String sellBranch) {
         this.sellBranch = sellBranch;
      }

      public void setIssueDate(String issueDate) {
         this.issueDate = issueDate;
      }

      public void setStageType(String stageType) {
         this.stageType = stageType;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100220In.Body)) {
            return false;
         } else {
            Core1400100220In.Body other = (Core1400100220In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label95: {
                  Object this$clientType = this.getClientType();
                  Object other$clientType = other.getClientType();
                  if (this$clientType == null) {
                     if (other$clientType == null) {
                        break label95;
                     }
                  } else if (this$clientType.equals(other$clientType)) {
                     break label95;
                  }

                  return false;
               }

               Object this$issueYear = this.getIssueYear();
               Object other$issueYear = other.getIssueYear();
               if (this$issueYear == null) {
                  if (other$issueYear != null) {
                     return false;
                  }
               } else if (!this$issueYear.equals(other$issueYear)) {
                  return false;
               }

               Object this$term = this.getTerm();
               Object other$term = other.getTerm();
               if (this$term == null) {
                  if (other$term != null) {
                     return false;
                  }
               } else if (!this$term.equals(other$term)) {
                  return false;
               }

               label74: {
                  Object this$termType = this.getTermType();
                  Object other$termType = other.getTermType();
                  if (this$termType == null) {
                     if (other$termType == null) {
                        break label74;
                     }
                  } else if (this$termType.equals(other$termType)) {
                     break label74;
                  }

                  return false;
               }

               label67: {
                  Object this$sellBranch = this.getSellBranch();
                  Object other$sellBranch = other.getSellBranch();
                  if (this$sellBranch == null) {
                     if (other$sellBranch == null) {
                        break label67;
                     }
                  } else if (this$sellBranch.equals(other$sellBranch)) {
                     break label67;
                  }

                  return false;
               }

               Object this$issueDate = this.getIssueDate();
               Object other$issueDate = other.getIssueDate();
               if (this$issueDate == null) {
                  if (other$issueDate != null) {
                     return false;
                  }
               } else if (!this$issueDate.equals(other$issueDate)) {
                  return false;
               }

               Object this$stageType = this.getStageType();
               Object other$stageType = other.getStageType();
               if (this$stageType == null) {
                  if (other$stageType != null) {
                     return false;
                  }
               } else if (!this$stageType.equals(other$stageType)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100220In.Body;
      }
      public String toString() {
         return "Core1400100220In.Body(clientType=" + this.getClientType() + ", issueYear=" + this.getIssueYear() + ", term=" + this.getTerm() + ", termType=" + this.getTermType() + ", sellBranch=" + this.getSellBranch() + ", issueDate=" + this.getIssueDate() + ", stageType=" + this.getStageType() + ")";
      }
   }
}
