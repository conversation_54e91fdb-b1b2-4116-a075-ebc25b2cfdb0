package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400023401Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400023401Out.BillArray> billArray;
   @V(
      desc = "文件名称",
      notNull = false,
      length = "200",
      remark = "文件名称",
      maxSize = 200
   )
   private String fileName;
   @V(
      desc = "批次号",
      notNull = false,
      length = "50",
      remark = "批次号",
      maxSize = 50
   )
   private String batchNo;
   @V(
      desc = "总笔数",
      notNull = false,
      length = "5",
      remark = "总笔数"
   )
   private Integer totalCount;
   @V(
      desc = "多余金额/未兑付金额",
      notNull = false,
      length = "17",
      remark = "多余金额/未兑付金额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal excessAmt;
   @V(
      desc = "兑付笔数",
      notNull = false,
      length = "20",
      remark = "兑付笔数",
      maxSize = 20
   )
   private String payNum;
   @V(
      desc = "缴存金额",
      notNull = false,
      length = "17",
      remark = "缴存金额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal payAmt;
   @V(
      desc = "多余退回笔数",
      notNull = false,
      length = "20",
      remark = "多余退回笔数",
      maxSize = 20
   )
   private String excessNum;
   @V(
      desc = "总金额",
      notNull = false,
      length = "17",
      remark = "总金额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal totalAmt;

   public List<Core1400023401Out.BillArray> getBillArray() {
      return this.billArray;
   }

   public String getFileName() {
      return this.fileName;
   }

   public String getBatchNo() {
      return this.batchNo;
   }

   public Integer getTotalCount() {
      return this.totalCount;
   }

   public BigDecimal getExcessAmt() {
      return this.excessAmt;
   }

   public String getPayNum() {
      return this.payNum;
   }

   public BigDecimal getPayAmt() {
      return this.payAmt;
   }

   public String getExcessNum() {
      return this.excessNum;
   }

   public BigDecimal getTotalAmt() {
      return this.totalAmt;
   }

   public void setBillArray(List<Core1400023401Out.BillArray> billArray) {
      this.billArray = billArray;
   }

   public void setFileName(String fileName) {
      this.fileName = fileName;
   }

   public void setBatchNo(String batchNo) {
      this.batchNo = batchNo;
   }

   public void setTotalCount(Integer totalCount) {
      this.totalCount = totalCount;
   }

   public void setExcessAmt(BigDecimal excessAmt) {
      this.excessAmt = excessAmt;
   }

   public void setPayNum(String payNum) {
      this.payNum = payNum;
   }

   public void setPayAmt(BigDecimal payAmt) {
      this.payAmt = payAmt;
   }

   public void setExcessNum(String excessNum) {
      this.excessNum = excessNum;
   }

   public void setTotalAmt(BigDecimal totalAmt) {
      this.totalAmt = totalAmt;
   }

   public String toString() {
      return "Core1400023401Out(billArray=" + this.getBillArray() + ", fileName=" + this.getFileName() + ", batchNo=" + this.getBatchNo() + ", totalCount=" + this.getTotalCount() + ", excessAmt=" + this.getExcessAmt() + ", payNum=" + this.getPayNum() + ", payAmt=" + this.getPayAmt() + ", excessNum=" + this.getExcessNum() + ", totalAmt=" + this.getTotalAmt() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400023401Out)) {
         return false;
      } else {
         Core1400023401Out other = (Core1400023401Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            label121: {
               Object this$billArray = this.getBillArray();
               Object other$billArray = other.getBillArray();
               if (this$billArray == null) {
                  if (other$billArray == null) {
                     break label121;
                  }
               } else if (this$billArray.equals(other$billArray)) {
                  break label121;
               }

               return false;
            }

            Object this$fileName = this.getFileName();
            Object other$fileName = other.getFileName();
            if (this$fileName == null) {
               if (other$fileName != null) {
                  return false;
               }
            } else if (!this$fileName.equals(other$fileName)) {
               return false;
            }

            label107: {
               Object this$batchNo = this.getBatchNo();
               Object other$batchNo = other.getBatchNo();
               if (this$batchNo == null) {
                  if (other$batchNo == null) {
                     break label107;
                  }
               } else if (this$batchNo.equals(other$batchNo)) {
                  break label107;
               }

               return false;
            }

            Object this$totalCount = this.getTotalCount();
            Object other$totalCount = other.getTotalCount();
            if (this$totalCount == null) {
               if (other$totalCount != null) {
                  return false;
               }
            } else if (!this$totalCount.equals(other$totalCount)) {
               return false;
            }

            Object this$excessAmt = this.getExcessAmt();
            Object other$excessAmt = other.getExcessAmt();
            if (this$excessAmt == null) {
               if (other$excessAmt != null) {
                  return false;
               }
            } else if (!this$excessAmt.equals(other$excessAmt)) {
               return false;
            }

            label86: {
               Object this$payNum = this.getPayNum();
               Object other$payNum = other.getPayNum();
               if (this$payNum == null) {
                  if (other$payNum == null) {
                     break label86;
                  }
               } else if (this$payNum.equals(other$payNum)) {
                  break label86;
               }

               return false;
            }

            label79: {
               Object this$payAmt = this.getPayAmt();
               Object other$payAmt = other.getPayAmt();
               if (this$payAmt == null) {
                  if (other$payAmt == null) {
                     break label79;
                  }
               } else if (this$payAmt.equals(other$payAmt)) {
                  break label79;
               }

               return false;
            }

            Object this$excessNum = this.getExcessNum();
            Object other$excessNum = other.getExcessNum();
            if (this$excessNum == null) {
               if (other$excessNum != null) {
                  return false;
               }
            } else if (!this$excessNum.equals(other$excessNum)) {
               return false;
            }

            Object this$totalAmt = this.getTotalAmt();
            Object other$totalAmt = other.getTotalAmt();
            if (this$totalAmt == null) {
               if (other$totalAmt != null) {
                  return false;
               }
            } else if (!this$totalAmt.equals(other$totalAmt)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400023401Out;
   }
   public static class BillArray {
      @V(
         desc = "票据签发机构",
         notNull = false,
         length = "50",
         remark = "签发机构",
         maxSize = 50
      )
      private String billSignBranch;
      @V(
         desc = "票据类型",
         notNull = false,
         length = "5",
         remark = "票据类型",
         maxSize = 5
      )
      private String billType;
      @V(
         desc = "票据号码",
         notNull = false,
         length = "50",
         remark = "票据号码",
         maxSize = 50
      )
      private String billNo;
      @V(
         desc = "出票金额",
         notNull = false,
         length = "17",
         remark = "出票金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal billTranAmt;
      @V(
         desc = "票据状态",
         notNull = false,
         length = "2",
         remark = "票据状态",
         maxSize = 2
      )
      private String billStatus;
      @V(
         desc = "票据登记日期",
         notNull = false,
         remark = "票据登记日期"
      )
      private String billSignDate;
      @V(
         desc = "付款账户币种",
         notNull = false,
         length = "3",
         remark = "付款账户币种",
         maxSize = 3
      )
      private String payerAcctCcy;
      @V(
         desc = "付款人账号",
         notNull = false,
         length = "50",
         remark = "付款人账号",
         maxSize = 50
      )
      private String payerBaseAcctNo;
      @V(
         desc = "付款人名称",
         notNull = false,
         length = "200",
         remark = "付款人名称",
         maxSize = 200
      )
      private String payerName;
      @V(
         desc = "付款人证件号码",
         notNull = false,
         length = "50",
         remark = "付款人证件号码",
         maxSize = 50
      )
      private String payerDocumentId;
      @V(
         desc = "付款人证件类型",
         notNull = false,
         length = "3",
         remark = "付款人证件类型",
         maxSize = 3
      )
      private String payerDocumentType;
      @V(
         desc = "收款人名称",
         notNull = false,
         length = "200",
         remark = "收款人名称",
         maxSize = 200
      )
      private String payeeName;
      @V(
         desc = "签发柜员",
         notNull = false,
         length = "30",
         remark = "签发柜员",
         maxSize = 30
      )
      private String billSignUserId;
      @V(
         desc = "兑付柜员",
         notNull = false,
         length = "30",
         remark = "兑付柜员",
         maxSize = 30
      )
      private String paymentUser;
      @V(
         desc = "兑付日期",
         notNull = false,
         remark = "兑付日期"
      )
      private String paymentDate;
      @V(
         desc = "凭证类型",
         notNull = false,
         length = "10",
         remark = "凭证类型",
         maxSize = 10
      )
      private String docType;
      @V(
         desc = "业务种类",
         notNull = false,
         length = "20",
         remark = "业务种类",
         maxSize = 20
      )
      private String busiType;
      @V(
         desc = "业务流水号",
         notNull = false,
         length = "50",
         remark = "支付流水号",
         maxSize = 50
      )
      private String serialNo;
      @V(
         desc = "多余金额/未兑付金额",
         notNull = false,
         length = "17",
         remark = "多余金额/未兑付金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal excessAmt;
      @V(
         desc = "结算金额",
         notNull = false,
         length = "17",
         remark = "结算金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal settleAmt;
      @V(
         desc = "收款人账户",
         notNull = false,
         length = "50",
         remark = "收款人账户",
         maxSize = 50
      )
      private String payeeAcctNo;
      @V(
         desc = "备注",
         notNull = false,
         length = "200",
         remark = "备注",
         maxSize = 200
      )
      private String remark;
      @V(
         desc = "现金项目",
         notNull = false,
         length = "10",
         remark = "现金项目",
         maxSize = 10
      )
      private String cashItem;
      @V(
         desc = "过期标志",
         notNull = false,
         length = "10",
         remark = "过期标志",
         maxSize = 10
      )
      private String expireFlag;
      @V(
         desc = "票据密押",
         notNull = false,
         length = "200",
         remark = "票据密押",
         maxSize = 200
      )
      private String encrypKey;
      @V(
         desc = "复核柜员",
         notNull = false,
         length = "30",
         remark = "复核柜员",
         maxSize = 30
      )
      private String apprUserId;
      @V(
         desc = "兑付方式",
         notNull = false,
         length = "10",
         remark = "兑付方式",
         maxSize = 10
      )
      private String paymentType;
      @V(
         desc = "退回机构",
         notNull = false,
         length = "50",
         remark = "退回机构",
         maxSize = 50
      )
      private String backBranch;
      @V(
         desc = "退回柜员",
         notNull = false,
         length = "50",
         remark = "退回柜员",
         maxSize = 50
      )
      private String returnUser;
      @V(
         desc = "退回日期",
         notNull = false,
         remark = "退回日期"
      )
      private String returnDate;
      @V(
         desc = "退回方式1-现金2-转账3-销挂账",
         notNull = false,
         length = "1",
         remark = "退回方式1-现金2-转账3-销挂账",
         maxSize = 1
      )
      private String returnType;
      @V(
         desc = "收款人证件类型",
         notNull = false,
         length = "3",
         remark = "收款人证件类型",
         maxSize = 3
      )
      private String payeeDocumentType;
      @V(
         desc = "收款人证件号码",
         notNull = false,
         length = "50",
         remark = "收款人证件号码",
         maxSize = 50
      )
      private String payeeDocumentId;
      @V(
         desc = "总金额",
         notNull = false,
         length = "17",
         remark = "总金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal totalAmt;
      @V(
         desc = "限额维护类型",
         notNull = false,
         length = "1",
         remark = "限额维护类型",
         maxSize = 1
      )
      private String operType;
      @V(
         desc = "签约类型",
         notNull = false,
         length = "20",
         remark = "签约类型",
         maxSize = 20
      )
      private String signType;
      @V(
         desc = "是否挂失标志",
         notNull = false,
         length = "1",
         remark = "是否挂失标志",
         maxSize = 1
      )
      private String lostFlag;
      @V(
         desc = "兑付行行号",
         notNull = false,
         length = "20",
         remark = "兑付行行号",
         maxSize = 20
      )
      private String paymentBankNo;
      @V(
         desc = "多余资金划回挂账编号",
         notNull = false,
         length = "50",
         remark = "多余资金划回挂账编号",
         maxSize = 50
      )
      private String excessHangSeqNo;
      @V(
         desc = "拒绝原因",
         notNull = false,
         length = "200",
         remark = "拒绝原因",
         maxSize = 200
      )
      private String refuseReason;
      @V(
         desc = "退票行号",
         notNull = false,
         length = "50",
         remark = "退票行号",
         maxSize = 50
      )
      private String refundBankNo;
      @V(
         desc = "退回流水号",
         notNull = false,
         length = "50",
         remark = "退回流水号",
         maxSize = 50
      )
      private String returnSerialNo;
      @V(
         desc = "挂账序列号",
         notNull = false,
         length = "50",
         remark = "挂账账户序列号",
         maxSize = 50
      )
      private String hangSeqNo;

      public String getBillSignBranch() {
         return this.billSignBranch;
      }

      public String getBillType() {
         return this.billType;
      }

      public String getBillNo() {
         return this.billNo;
      }

      public BigDecimal getBillTranAmt() {
         return this.billTranAmt;
      }

      public String getBillStatus() {
         return this.billStatus;
      }

      public String getBillSignDate() {
         return this.billSignDate;
      }

      public String getPayerAcctCcy() {
         return this.payerAcctCcy;
      }

      public String getPayerBaseAcctNo() {
         return this.payerBaseAcctNo;
      }

      public String getPayerName() {
         return this.payerName;
      }

      public String getPayerDocumentId() {
         return this.payerDocumentId;
      }

      public String getPayerDocumentType() {
         return this.payerDocumentType;
      }

      public String getPayeeName() {
         return this.payeeName;
      }

      public String getBillSignUserId() {
         return this.billSignUserId;
      }

      public String getPaymentUser() {
         return this.paymentUser;
      }

      public String getPaymentDate() {
         return this.paymentDate;
      }

      public String getDocType() {
         return this.docType;
      }

      public String getBusiType() {
         return this.busiType;
      }

      public String getSerialNo() {
         return this.serialNo;
      }

      public BigDecimal getExcessAmt() {
         return this.excessAmt;
      }

      public BigDecimal getSettleAmt() {
         return this.settleAmt;
      }

      public String getPayeeAcctNo() {
         return this.payeeAcctNo;
      }

      public String getRemark() {
         return this.remark;
      }

      public String getCashItem() {
         return this.cashItem;
      }

      public String getExpireFlag() {
         return this.expireFlag;
      }

      public String getEncrypKey() {
         return this.encrypKey;
      }

      public String getApprUserId() {
         return this.apprUserId;
      }

      public String getPaymentType() {
         return this.paymentType;
      }

      public String getBackBranch() {
         return this.backBranch;
      }

      public String getReturnUser() {
         return this.returnUser;
      }

      public String getReturnDate() {
         return this.returnDate;
      }

      public String getReturnType() {
         return this.returnType;
      }

      public String getPayeeDocumentType() {
         return this.payeeDocumentType;
      }

      public String getPayeeDocumentId() {
         return this.payeeDocumentId;
      }

      public BigDecimal getTotalAmt() {
         return this.totalAmt;
      }

      public String getOperType() {
         return this.operType;
      }

      public String getSignType() {
         return this.signType;
      }

      public String getLostFlag() {
         return this.lostFlag;
      }

      public String getPaymentBankNo() {
         return this.paymentBankNo;
      }

      public String getExcessHangSeqNo() {
         return this.excessHangSeqNo;
      }

      public String getRefuseReason() {
         return this.refuseReason;
      }

      public String getRefundBankNo() {
         return this.refundBankNo;
      }

      public String getReturnSerialNo() {
         return this.returnSerialNo;
      }

      public String getHangSeqNo() {
         return this.hangSeqNo;
      }

      public void setBillSignBranch(String billSignBranch) {
         this.billSignBranch = billSignBranch;
      }

      public void setBillType(String billType) {
         this.billType = billType;
      }

      public void setBillNo(String billNo) {
         this.billNo = billNo;
      }

      public void setBillTranAmt(BigDecimal billTranAmt) {
         this.billTranAmt = billTranAmt;
      }

      public void setBillStatus(String billStatus) {
         this.billStatus = billStatus;
      }

      public void setBillSignDate(String billSignDate) {
         this.billSignDate = billSignDate;
      }

      public void setPayerAcctCcy(String payerAcctCcy) {
         this.payerAcctCcy = payerAcctCcy;
      }

      public void setPayerBaseAcctNo(String payerBaseAcctNo) {
         this.payerBaseAcctNo = payerBaseAcctNo;
      }

      public void setPayerName(String payerName) {
         this.payerName = payerName;
      }

      public void setPayerDocumentId(String payerDocumentId) {
         this.payerDocumentId = payerDocumentId;
      }

      public void setPayerDocumentType(String payerDocumentType) {
         this.payerDocumentType = payerDocumentType;
      }

      public void setPayeeName(String payeeName) {
         this.payeeName = payeeName;
      }

      public void setBillSignUserId(String billSignUserId) {
         this.billSignUserId = billSignUserId;
      }

      public void setPaymentUser(String paymentUser) {
         this.paymentUser = paymentUser;
      }

      public void setPaymentDate(String paymentDate) {
         this.paymentDate = paymentDate;
      }

      public void setDocType(String docType) {
         this.docType = docType;
      }

      public void setBusiType(String busiType) {
         this.busiType = busiType;
      }

      public void setSerialNo(String serialNo) {
         this.serialNo = serialNo;
      }

      public void setExcessAmt(BigDecimal excessAmt) {
         this.excessAmt = excessAmt;
      }

      public void setSettleAmt(BigDecimal settleAmt) {
         this.settleAmt = settleAmt;
      }

      public void setPayeeAcctNo(String payeeAcctNo) {
         this.payeeAcctNo = payeeAcctNo;
      }

      public void setRemark(String remark) {
         this.remark = remark;
      }

      public void setCashItem(String cashItem) {
         this.cashItem = cashItem;
      }

      public void setExpireFlag(String expireFlag) {
         this.expireFlag = expireFlag;
      }

      public void setEncrypKey(String encrypKey) {
         this.encrypKey = encrypKey;
      }

      public void setApprUserId(String apprUserId) {
         this.apprUserId = apprUserId;
      }

      public void setPaymentType(String paymentType) {
         this.paymentType = paymentType;
      }

      public void setBackBranch(String backBranch) {
         this.backBranch = backBranch;
      }

      public void setReturnUser(String returnUser) {
         this.returnUser = returnUser;
      }

      public void setReturnDate(String returnDate) {
         this.returnDate = returnDate;
      }

      public void setReturnType(String returnType) {
         this.returnType = returnType;
      }

      public void setPayeeDocumentType(String payeeDocumentType) {
         this.payeeDocumentType = payeeDocumentType;
      }

      public void setPayeeDocumentId(String payeeDocumentId) {
         this.payeeDocumentId = payeeDocumentId;
      }

      public void setTotalAmt(BigDecimal totalAmt) {
         this.totalAmt = totalAmt;
      }

      public void setOperType(String operType) {
         this.operType = operType;
      }

      public void setSignType(String signType) {
         this.signType = signType;
      }

      public void setLostFlag(String lostFlag) {
         this.lostFlag = lostFlag;
      }

      public void setPaymentBankNo(String paymentBankNo) {
         this.paymentBankNo = paymentBankNo;
      }

      public void setExcessHangSeqNo(String excessHangSeqNo) {
         this.excessHangSeqNo = excessHangSeqNo;
      }

      public void setRefuseReason(String refuseReason) {
         this.refuseReason = refuseReason;
      }

      public void setRefundBankNo(String refundBankNo) {
         this.refundBankNo = refundBankNo;
      }

      public void setReturnSerialNo(String returnSerialNo) {
         this.returnSerialNo = returnSerialNo;
      }

      public void setHangSeqNo(String hangSeqNo) {
         this.hangSeqNo = hangSeqNo;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400023401Out.BillArray)) {
            return false;
         } else {
            Core1400023401Out.BillArray other = (Core1400023401Out.BillArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label527: {
                  Object this$billSignBranch = this.getBillSignBranch();
                  Object other$billSignBranch = other.getBillSignBranch();
                  if (this$billSignBranch == null) {
                     if (other$billSignBranch == null) {
                        break label527;
                     }
                  } else if (this$billSignBranch.equals(other$billSignBranch)) {
                     break label527;
                  }

                  return false;
               }

               Object this$billType = this.getBillType();
               Object other$billType = other.getBillType();
               if (this$billType == null) {
                  if (other$billType != null) {
                     return false;
                  }
               } else if (!this$billType.equals(other$billType)) {
                  return false;
               }

               Object this$billNo = this.getBillNo();
               Object other$billNo = other.getBillNo();
               if (this$billNo == null) {
                  if (other$billNo != null) {
                     return false;
                  }
               } else if (!this$billNo.equals(other$billNo)) {
                  return false;
               }

               label506: {
                  Object this$billTranAmt = this.getBillTranAmt();
                  Object other$billTranAmt = other.getBillTranAmt();
                  if (this$billTranAmt == null) {
                     if (other$billTranAmt == null) {
                        break label506;
                     }
                  } else if (this$billTranAmt.equals(other$billTranAmt)) {
                     break label506;
                  }

                  return false;
               }

               label499: {
                  Object this$billStatus = this.getBillStatus();
                  Object other$billStatus = other.getBillStatus();
                  if (this$billStatus == null) {
                     if (other$billStatus == null) {
                        break label499;
                     }
                  } else if (this$billStatus.equals(other$billStatus)) {
                     break label499;
                  }

                  return false;
               }

               Object this$billSignDate = this.getBillSignDate();
               Object other$billSignDate = other.getBillSignDate();
               if (this$billSignDate == null) {
                  if (other$billSignDate != null) {
                     return false;
                  }
               } else if (!this$billSignDate.equals(other$billSignDate)) {
                  return false;
               }

               Object this$payerAcctCcy = this.getPayerAcctCcy();
               Object other$payerAcctCcy = other.getPayerAcctCcy();
               if (this$payerAcctCcy == null) {
                  if (other$payerAcctCcy != null) {
                     return false;
                  }
               } else if (!this$payerAcctCcy.equals(other$payerAcctCcy)) {
                  return false;
               }

               label478: {
                  Object this$payerBaseAcctNo = this.getPayerBaseAcctNo();
                  Object other$payerBaseAcctNo = other.getPayerBaseAcctNo();
                  if (this$payerBaseAcctNo == null) {
                     if (other$payerBaseAcctNo == null) {
                        break label478;
                     }
                  } else if (this$payerBaseAcctNo.equals(other$payerBaseAcctNo)) {
                     break label478;
                  }

                  return false;
               }

               label471: {
                  Object this$payerName = this.getPayerName();
                  Object other$payerName = other.getPayerName();
                  if (this$payerName == null) {
                     if (other$payerName == null) {
                        break label471;
                     }
                  } else if (this$payerName.equals(other$payerName)) {
                     break label471;
                  }

                  return false;
               }

               Object this$payerDocumentId = this.getPayerDocumentId();
               Object other$payerDocumentId = other.getPayerDocumentId();
               if (this$payerDocumentId == null) {
                  if (other$payerDocumentId != null) {
                     return false;
                  }
               } else if (!this$payerDocumentId.equals(other$payerDocumentId)) {
                  return false;
               }

               label457: {
                  Object this$payerDocumentType = this.getPayerDocumentType();
                  Object other$payerDocumentType = other.getPayerDocumentType();
                  if (this$payerDocumentType == null) {
                     if (other$payerDocumentType == null) {
                        break label457;
                     }
                  } else if (this$payerDocumentType.equals(other$payerDocumentType)) {
                     break label457;
                  }

                  return false;
               }

               Object this$payeeName = this.getPayeeName();
               Object other$payeeName = other.getPayeeName();
               if (this$payeeName == null) {
                  if (other$payeeName != null) {
                     return false;
                  }
               } else if (!this$payeeName.equals(other$payeeName)) {
                  return false;
               }

               label443: {
                  Object this$billSignUserId = this.getBillSignUserId();
                  Object other$billSignUserId = other.getBillSignUserId();
                  if (this$billSignUserId == null) {
                     if (other$billSignUserId == null) {
                        break label443;
                     }
                  } else if (this$billSignUserId.equals(other$billSignUserId)) {
                     break label443;
                  }

                  return false;
               }

               Object this$paymentUser = this.getPaymentUser();
               Object other$paymentUser = other.getPaymentUser();
               if (this$paymentUser == null) {
                  if (other$paymentUser != null) {
                     return false;
                  }
               } else if (!this$paymentUser.equals(other$paymentUser)) {
                  return false;
               }

               Object this$paymentDate = this.getPaymentDate();
               Object other$paymentDate = other.getPaymentDate();
               if (this$paymentDate == null) {
                  if (other$paymentDate != null) {
                     return false;
                  }
               } else if (!this$paymentDate.equals(other$paymentDate)) {
                  return false;
               }

               label422: {
                  Object this$docType = this.getDocType();
                  Object other$docType = other.getDocType();
                  if (this$docType == null) {
                     if (other$docType == null) {
                        break label422;
                     }
                  } else if (this$docType.equals(other$docType)) {
                     break label422;
                  }

                  return false;
               }

               label415: {
                  Object this$busiType = this.getBusiType();
                  Object other$busiType = other.getBusiType();
                  if (this$busiType == null) {
                     if (other$busiType == null) {
                        break label415;
                     }
                  } else if (this$busiType.equals(other$busiType)) {
                     break label415;
                  }

                  return false;
               }

               Object this$serialNo = this.getSerialNo();
               Object other$serialNo = other.getSerialNo();
               if (this$serialNo == null) {
                  if (other$serialNo != null) {
                     return false;
                  }
               } else if (!this$serialNo.equals(other$serialNo)) {
                  return false;
               }

               Object this$excessAmt = this.getExcessAmt();
               Object other$excessAmt = other.getExcessAmt();
               if (this$excessAmt == null) {
                  if (other$excessAmt != null) {
                     return false;
                  }
               } else if (!this$excessAmt.equals(other$excessAmt)) {
                  return false;
               }

               label394: {
                  Object this$settleAmt = this.getSettleAmt();
                  Object other$settleAmt = other.getSettleAmt();
                  if (this$settleAmt == null) {
                     if (other$settleAmt == null) {
                        break label394;
                     }
                  } else if (this$settleAmt.equals(other$settleAmt)) {
                     break label394;
                  }

                  return false;
               }

               label387: {
                  Object this$payeeAcctNo = this.getPayeeAcctNo();
                  Object other$payeeAcctNo = other.getPayeeAcctNo();
                  if (this$payeeAcctNo == null) {
                     if (other$payeeAcctNo == null) {
                        break label387;
                     }
                  } else if (this$payeeAcctNo.equals(other$payeeAcctNo)) {
                     break label387;
                  }

                  return false;
               }

               Object this$remark = this.getRemark();
               Object other$remark = other.getRemark();
               if (this$remark == null) {
                  if (other$remark != null) {
                     return false;
                  }
               } else if (!this$remark.equals(other$remark)) {
                  return false;
               }

               Object this$cashItem = this.getCashItem();
               Object other$cashItem = other.getCashItem();
               if (this$cashItem == null) {
                  if (other$cashItem != null) {
                     return false;
                  }
               } else if (!this$cashItem.equals(other$cashItem)) {
                  return false;
               }

               label366: {
                  Object this$expireFlag = this.getExpireFlag();
                  Object other$expireFlag = other.getExpireFlag();
                  if (this$expireFlag == null) {
                     if (other$expireFlag == null) {
                        break label366;
                     }
                  } else if (this$expireFlag.equals(other$expireFlag)) {
                     break label366;
                  }

                  return false;
               }

               label359: {
                  Object this$encrypKey = this.getEncrypKey();
                  Object other$encrypKey = other.getEncrypKey();
                  if (this$encrypKey == null) {
                     if (other$encrypKey == null) {
                        break label359;
                     }
                  } else if (this$encrypKey.equals(other$encrypKey)) {
                     break label359;
                  }

                  return false;
               }

               Object this$apprUserId = this.getApprUserId();
               Object other$apprUserId = other.getApprUserId();
               if (this$apprUserId == null) {
                  if (other$apprUserId != null) {
                     return false;
                  }
               } else if (!this$apprUserId.equals(other$apprUserId)) {
                  return false;
               }

               label345: {
                  Object this$paymentType = this.getPaymentType();
                  Object other$paymentType = other.getPaymentType();
                  if (this$paymentType == null) {
                     if (other$paymentType == null) {
                        break label345;
                     }
                  } else if (this$paymentType.equals(other$paymentType)) {
                     break label345;
                  }

                  return false;
               }

               Object this$backBranch = this.getBackBranch();
               Object other$backBranch = other.getBackBranch();
               if (this$backBranch == null) {
                  if (other$backBranch != null) {
                     return false;
                  }
               } else if (!this$backBranch.equals(other$backBranch)) {
                  return false;
               }

               label331: {
                  Object this$returnUser = this.getReturnUser();
                  Object other$returnUser = other.getReturnUser();
                  if (this$returnUser == null) {
                     if (other$returnUser == null) {
                        break label331;
                     }
                  } else if (this$returnUser.equals(other$returnUser)) {
                     break label331;
                  }

                  return false;
               }

               Object this$returnDate = this.getReturnDate();
               Object other$returnDate = other.getReturnDate();
               if (this$returnDate == null) {
                  if (other$returnDate != null) {
                     return false;
                  }
               } else if (!this$returnDate.equals(other$returnDate)) {
                  return false;
               }

               Object this$returnType = this.getReturnType();
               Object other$returnType = other.getReturnType();
               if (this$returnType == null) {
                  if (other$returnType != null) {
                     return false;
                  }
               } else if (!this$returnType.equals(other$returnType)) {
                  return false;
               }

               label310: {
                  Object this$payeeDocumentType = this.getPayeeDocumentType();
                  Object other$payeeDocumentType = other.getPayeeDocumentType();
                  if (this$payeeDocumentType == null) {
                     if (other$payeeDocumentType == null) {
                        break label310;
                     }
                  } else if (this$payeeDocumentType.equals(other$payeeDocumentType)) {
                     break label310;
                  }

                  return false;
               }

               label303: {
                  Object this$payeeDocumentId = this.getPayeeDocumentId();
                  Object other$payeeDocumentId = other.getPayeeDocumentId();
                  if (this$payeeDocumentId == null) {
                     if (other$payeeDocumentId == null) {
                        break label303;
                     }
                  } else if (this$payeeDocumentId.equals(other$payeeDocumentId)) {
                     break label303;
                  }

                  return false;
               }

               Object this$totalAmt = this.getTotalAmt();
               Object other$totalAmt = other.getTotalAmt();
               if (this$totalAmt == null) {
                  if (other$totalAmt != null) {
                     return false;
                  }
               } else if (!this$totalAmt.equals(other$totalAmt)) {
                  return false;
               }

               Object this$operType = this.getOperType();
               Object other$operType = other.getOperType();
               if (this$operType == null) {
                  if (other$operType != null) {
                     return false;
                  }
               } else if (!this$operType.equals(other$operType)) {
                  return false;
               }

               label282: {
                  Object this$signType = this.getSignType();
                  Object other$signType = other.getSignType();
                  if (this$signType == null) {
                     if (other$signType == null) {
                        break label282;
                     }
                  } else if (this$signType.equals(other$signType)) {
                     break label282;
                  }

                  return false;
               }

               label275: {
                  Object this$lostFlag = this.getLostFlag();
                  Object other$lostFlag = other.getLostFlag();
                  if (this$lostFlag == null) {
                     if (other$lostFlag == null) {
                        break label275;
                     }
                  } else if (this$lostFlag.equals(other$lostFlag)) {
                     break label275;
                  }

                  return false;
               }

               Object this$paymentBankNo = this.getPaymentBankNo();
               Object other$paymentBankNo = other.getPaymentBankNo();
               if (this$paymentBankNo == null) {
                  if (other$paymentBankNo != null) {
                     return false;
                  }
               } else if (!this$paymentBankNo.equals(other$paymentBankNo)) {
                  return false;
               }

               Object this$excessHangSeqNo = this.getExcessHangSeqNo();
               Object other$excessHangSeqNo = other.getExcessHangSeqNo();
               if (this$excessHangSeqNo == null) {
                  if (other$excessHangSeqNo != null) {
                     return false;
                  }
               } else if (!this$excessHangSeqNo.equals(other$excessHangSeqNo)) {
                  return false;
               }

               label254: {
                  Object this$refuseReason = this.getRefuseReason();
                  Object other$refuseReason = other.getRefuseReason();
                  if (this$refuseReason == null) {
                     if (other$refuseReason == null) {
                        break label254;
                     }
                  } else if (this$refuseReason.equals(other$refuseReason)) {
                     break label254;
                  }

                  return false;
               }

               label247: {
                  Object this$refundBankNo = this.getRefundBankNo();
                  Object other$refundBankNo = other.getRefundBankNo();
                  if (this$refundBankNo == null) {
                     if (other$refundBankNo == null) {
                        break label247;
                     }
                  } else if (this$refundBankNo.equals(other$refundBankNo)) {
                     break label247;
                  }

                  return false;
               }

               Object this$returnSerialNo = this.getReturnSerialNo();
               Object other$returnSerialNo = other.getReturnSerialNo();
               if (this$returnSerialNo == null) {
                  if (other$returnSerialNo != null) {
                     return false;
                  }
               } else if (!this$returnSerialNo.equals(other$returnSerialNo)) {
                  return false;
               }

               Object this$hangSeqNo = this.getHangSeqNo();
               Object other$hangSeqNo = other.getHangSeqNo();
               if (this$hangSeqNo == null) {
                  if (other$hangSeqNo != null) {
                     return false;
                  }
               } else if (!this$hangSeqNo.equals(other$hangSeqNo)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400023401Out.BillArray;
      }
      public String toString() {
         return "Core1400023401Out.BillArray(billSignBranch=" + this.getBillSignBranch() + ", billType=" + this.getBillType() + ", billNo=" + this.getBillNo() + ", billTranAmt=" + this.getBillTranAmt() + ", billStatus=" + this.getBillStatus() + ", billSignDate=" + this.getBillSignDate() + ", payerAcctCcy=" + this.getPayerAcctCcy() + ", payerBaseAcctNo=" + this.getPayerBaseAcctNo() + ", payerName=" + this.getPayerName() + ", payerDocumentId=" + this.getPayerDocumentId() + ", payerDocumentType=" + this.getPayerDocumentType() + ", payeeName=" + this.getPayeeName() + ", billSignUserId=" + this.getBillSignUserId() + ", paymentUser=" + this.getPaymentUser() + ", paymentDate=" + this.getPaymentDate() + ", docType=" + this.getDocType() + ", busiType=" + this.getBusiType() + ", serialNo=" + this.getSerialNo() + ", excessAmt=" + this.getExcessAmt() + ", settleAmt=" + this.getSettleAmt() + ", payeeAcctNo=" + this.getPayeeAcctNo() + ", remark=" + this.getRemark() + ", cashItem=" + this.getCashItem() + ", expireFlag=" + this.getExpireFlag() + ", encrypKey=" + this.getEncrypKey() + ", apprUserId=" + this.getApprUserId() + ", paymentType=" + this.getPaymentType() + ", backBranch=" + this.getBackBranch() + ", returnUser=" + this.getReturnUser() + ", returnDate=" + this.getReturnDate() + ", returnType=" + this.getReturnType() + ", payeeDocumentType=" + this.getPayeeDocumentType() + ", payeeDocumentId=" + this.getPayeeDocumentId() + ", totalAmt=" + this.getTotalAmt() + ", operType=" + this.getOperType() + ", signType=" + this.getSignType() + ", lostFlag=" + this.getLostFlag() + ", paymentBankNo=" + this.getPaymentBankNo() + ", excessHangSeqNo=" + this.getExcessHangSeqNo() + ", refuseReason=" + this.getRefuseReason() + ", refundBankNo=" + this.getRefundBankNo() + ", returnSerialNo=" + this.getReturnSerialNo() + ", hangSeqNo=" + this.getHangSeqNo() + ")";
      }
   }
}
