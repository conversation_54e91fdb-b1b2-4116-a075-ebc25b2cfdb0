package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400100155In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400100155In.Body body;

   public Core1400100155In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400100155In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400100155In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100155In)) {
         return false;
      } else {
         Core1400100155In other = (Core1400100155In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100155In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "业务类型",
         notNull = true,
         length = "30",
         remark = "业务类型",
         maxSize = 30
      )
      private String tradeType;
      @V(
         desc = "业务种类",
         notNull = false,
         length = "20",
         remark = "业务种类",
         maxSize = 20
      )
      private String busiType;
      @V(
         desc = "账户名称",
         notNull = false,
         length = "200",
         remark = "账户名称，一般指中文账户名称",
         maxSize = 200
      )
      private String acctName;
      @V(
         desc = "账户类型",
         notNull = false,
         length = "1",
         inDesc = "A-AIO账户,C-结算账户,D-垫款,E-委托贷款,L-转让贷款,M-普通贷款,S-储蓄账户,T-定期账户,U-贴现贷款,Y-银团贷款,Z-资产证券化",
         remark = "账户类型",
         maxSize = 1
      )
      private String acctType;
      @V(
         desc = "证件号码",
         notNull = false,
         length = "50",
         remark = "证件号码",
         maxSize = 50
      )
      private String documentId;
      @V(
         desc = "证件类型",
         notNull = false,
         length = "3",
         remark = "证件类型",
         maxSize = 3
      )
      private String documentType;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getTradeType() {
         return this.tradeType;
      }

      public String getBusiType() {
         return this.busiType;
      }

      public String getAcctName() {
         return this.acctName;
      }

      public String getAcctType() {
         return this.acctType;
      }

      public String getDocumentId() {
         return this.documentId;
      }

      public String getDocumentType() {
         return this.documentType;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setTradeType(String tradeType) {
         this.tradeType = tradeType;
      }

      public void setBusiType(String busiType) {
         this.busiType = busiType;
      }

      public void setAcctName(String acctName) {
         this.acctName = acctName;
      }

      public void setAcctType(String acctType) {
         this.acctType = acctType;
      }

      public void setDocumentId(String documentId) {
         this.documentId = documentId;
      }

      public void setDocumentType(String documentType) {
         this.documentType = documentType;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100155In.Body)) {
            return false;
         } else {
            Core1400100155In.Body other = (Core1400100155In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label95: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label95;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label95;
                  }

                  return false;
               }

               Object this$tradeType = this.getTradeType();
               Object other$tradeType = other.getTradeType();
               if (this$tradeType == null) {
                  if (other$tradeType != null) {
                     return false;
                  }
               } else if (!this$tradeType.equals(other$tradeType)) {
                  return false;
               }

               Object this$busiType = this.getBusiType();
               Object other$busiType = other.getBusiType();
               if (this$busiType == null) {
                  if (other$busiType != null) {
                     return false;
                  }
               } else if (!this$busiType.equals(other$busiType)) {
                  return false;
               }

               label74: {
                  Object this$acctName = this.getAcctName();
                  Object other$acctName = other.getAcctName();
                  if (this$acctName == null) {
                     if (other$acctName == null) {
                        break label74;
                     }
                  } else if (this$acctName.equals(other$acctName)) {
                     break label74;
                  }

                  return false;
               }

               label67: {
                  Object this$acctType = this.getAcctType();
                  Object other$acctType = other.getAcctType();
                  if (this$acctType == null) {
                     if (other$acctType == null) {
                        break label67;
                     }
                  } else if (this$acctType.equals(other$acctType)) {
                     break label67;
                  }

                  return false;
               }

               Object this$documentId = this.getDocumentId();
               Object other$documentId = other.getDocumentId();
               if (this$documentId == null) {
                  if (other$documentId != null) {
                     return false;
                  }
               } else if (!this$documentId.equals(other$documentId)) {
                  return false;
               }

               Object this$documentType = this.getDocumentType();
               Object other$documentType = other.getDocumentType();
               if (this$documentType == null) {
                  if (other$documentType != null) {
                     return false;
                  }
               } else if (!this$documentType.equals(other$documentType)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100155In.Body;
      }
      public String toString() {
         return "Core1400100155In.Body(baseAcctNo=" + this.getBaseAcctNo() + ", tradeType=" + this.getTradeType() + ", busiType=" + this.getBusiType() + ", acctName=" + this.getAcctName() + ", acctType=" + this.getAcctType() + ", documentId=" + this.getDocumentId() + ", documentType=" + this.getDocumentType() + ")";
      }
   }
}
