package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400100008Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100008Out.SubArray> subArray;

   public List<Core1400100008Out.SubArray> getSubArray() {
      return this.subArray;
   }

   public void setSubArray(List<Core1400100008Out.SubArray> subArray) {
      this.subArray = subArray;
   }

   public String toString() {
      return "Core1400100008Out(subArray=" + this.getSubArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100008Out)) {
         return false;
      } else {
         Core1400100008Out other = (Core1400100008Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$subArray = this.getSubArray();
            Object other$subArray = other.getSubArray();
            if (this$subArray == null) {
               if (other$subArray != null) {
                  return false;
               }
            } else if (!this$subArray.equals(other$subArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100008Out;
   }
   public static class SubArray {
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "账户名称",
         notNull = false,
         length = "200",
         remark = "账户名称，一般指中文账户名称",
         maxSize = 200
      )
      private String acctName;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "账户开户日期",
         notNull = false,
         remark = "账户开户日期"
      )
      private String acctOpenDate;
      @V(
         desc = "生效日期",
         notNull = false,
         remark = "生效日期"
      )
      private String effectDate;
      @V(
         desc = "销户日期",
         notNull = false,
         remark = "账户销户日期"
      )
      private String acctCloseDate;
      @V(
         desc = "账户状态",
         notNull = false,
         length = "1",
         in = "N,H,A,D,S,O,P,C,I,R",
         remark = "描述账户生命周期不同阶段的划分",
         maxSize = 1
      )
      private String acctStatus;
      @V(
         desc = "存期期限",
         notNull = false,
         length = "5",
         remark = "期限",
         maxSize = 5
      )
      private String term;
      @V(
         desc = "期限类型",
         notNull = false,
         length = "1",
         remark = "期限类型",
         maxSize = 1
      )
      private String termType;
      @V(
         desc = "账面余额",
         notNull = false,
         length = "17",
         remark = "账面余额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal ledgerBal;
      @V(
         desc = "可用余额",
         notNull = false,
         length = "17",
         remark = "可用余额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal availableAmt;
      @V(
         desc = "限制金额",
         notNull = false,
         length = "17",
         remark = "限制金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal pledgedAmt;
      @V(
         desc = "账户属性",
         notNull = false,
         length = "10",
         remark = "账户属性",
         maxSize = 10
      )
      private String acctNature;
      @V(
         desc = "账户开户行",
         notNull = false,
         length = "50",
         remark = "账户实际开户机构，柜面为实际网点机构，线上渠道一般为对应主账户的实际开户机构",
         maxSize = 50
      )
      private String acctBranch;
      @V(
         desc = "机构名称",
         notNull = false,
         length = "200",
         remark = "机构名称",
         maxSize = 200
      )
      private String branchName;
      @V(
         desc = "执行利率",
         notNull = false,
         length = "15",
         remark = "执行利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal realRate;
      @V(
         desc = "是否允许部分支取",
         notNull = false,
         length = "1",
         in = "Y,N",
         remark = "是否允许部分支取",
         maxSize = 1
      )
      private String partWithdrawFlag;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getAcctName() {
         return this.acctName;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public String getAcctOpenDate() {
         return this.acctOpenDate;
      }

      public String getEffectDate() {
         return this.effectDate;
      }

      public String getAcctCloseDate() {
         return this.acctCloseDate;
      }

      public String getAcctStatus() {
         return this.acctStatus;
      }

      public String getTerm() {
         return this.term;
      }

      public String getTermType() {
         return this.termType;
      }

      public BigDecimal getLedgerBal() {
         return this.ledgerBal;
      }

      public BigDecimal getAvailableAmt() {
         return this.availableAmt;
      }

      public BigDecimal getPledgedAmt() {
         return this.pledgedAmt;
      }

      public String getAcctNature() {
         return this.acctNature;
      }

      public String getAcctBranch() {
         return this.acctBranch;
      }

      public String getBranchName() {
         return this.branchName;
      }

      public BigDecimal getRealRate() {
         return this.realRate;
      }

      public String getPartWithdrawFlag() {
         return this.partWithdrawFlag;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setAcctName(String acctName) {
         this.acctName = acctName;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setAcctOpenDate(String acctOpenDate) {
         this.acctOpenDate = acctOpenDate;
      }

      public void setEffectDate(String effectDate) {
         this.effectDate = effectDate;
      }

      public void setAcctCloseDate(String acctCloseDate) {
         this.acctCloseDate = acctCloseDate;
      }

      public void setAcctStatus(String acctStatus) {
         this.acctStatus = acctStatus;
      }

      public void setTerm(String term) {
         this.term = term;
      }

      public void setTermType(String termType) {
         this.termType = termType;
      }

      public void setLedgerBal(BigDecimal ledgerBal) {
         this.ledgerBal = ledgerBal;
      }

      public void setAvailableAmt(BigDecimal availableAmt) {
         this.availableAmt = availableAmt;
      }

      public void setPledgedAmt(BigDecimal pledgedAmt) {
         this.pledgedAmt = pledgedAmt;
      }

      public void setAcctNature(String acctNature) {
         this.acctNature = acctNature;
      }

      public void setAcctBranch(String acctBranch) {
         this.acctBranch = acctBranch;
      }

      public void setBranchName(String branchName) {
         this.branchName = branchName;
      }

      public void setRealRate(BigDecimal realRate) {
         this.realRate = realRate;
      }

      public void setPartWithdrawFlag(String partWithdrawFlag) {
         this.partWithdrawFlag = partWithdrawFlag;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100008Out.SubArray)) {
            return false;
         } else {
            Core1400100008Out.SubArray other = (Core1400100008Out.SubArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label251: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label251;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label251;
                  }

                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo != null) {
                     return false;
                  }
               } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                  return false;
               }

               label230: {
                  Object this$acctCcy = this.getAcctCcy();
                  Object other$acctCcy = other.getAcctCcy();
                  if (this$acctCcy == null) {
                     if (other$acctCcy == null) {
                        break label230;
                     }
                  } else if (this$acctCcy.equals(other$acctCcy)) {
                     break label230;
                  }

                  return false;
               }

               label223: {
                  Object this$acctName = this.getAcctName();
                  Object other$acctName = other.getAcctName();
                  if (this$acctName == null) {
                     if (other$acctName == null) {
                        break label223;
                     }
                  } else if (this$acctName.equals(other$acctName)) {
                     break label223;
                  }

                  return false;
               }

               label216: {
                  Object this$clientNo = this.getClientNo();
                  Object other$clientNo = other.getClientNo();
                  if (this$clientNo == null) {
                     if (other$clientNo == null) {
                        break label216;
                     }
                  } else if (this$clientNo.equals(other$clientNo)) {
                     break label216;
                  }

                  return false;
               }

               Object this$acctOpenDate = this.getAcctOpenDate();
               Object other$acctOpenDate = other.getAcctOpenDate();
               if (this$acctOpenDate == null) {
                  if (other$acctOpenDate != null) {
                     return false;
                  }
               } else if (!this$acctOpenDate.equals(other$acctOpenDate)) {
                  return false;
               }

               label202: {
                  Object this$effectDate = this.getEffectDate();
                  Object other$effectDate = other.getEffectDate();
                  if (this$effectDate == null) {
                     if (other$effectDate == null) {
                        break label202;
                     }
                  } else if (this$effectDate.equals(other$effectDate)) {
                     break label202;
                  }

                  return false;
               }

               Object this$acctCloseDate = this.getAcctCloseDate();
               Object other$acctCloseDate = other.getAcctCloseDate();
               if (this$acctCloseDate == null) {
                  if (other$acctCloseDate != null) {
                     return false;
                  }
               } else if (!this$acctCloseDate.equals(other$acctCloseDate)) {
                  return false;
               }

               label188: {
                  Object this$acctStatus = this.getAcctStatus();
                  Object other$acctStatus = other.getAcctStatus();
                  if (this$acctStatus == null) {
                     if (other$acctStatus == null) {
                        break label188;
                     }
                  } else if (this$acctStatus.equals(other$acctStatus)) {
                     break label188;
                  }

                  return false;
               }

               Object this$term = this.getTerm();
               Object other$term = other.getTerm();
               if (this$term == null) {
                  if (other$term != null) {
                     return false;
                  }
               } else if (!this$term.equals(other$term)) {
                  return false;
               }

               Object this$termType = this.getTermType();
               Object other$termType = other.getTermType();
               if (this$termType == null) {
                  if (other$termType != null) {
                     return false;
                  }
               } else if (!this$termType.equals(other$termType)) {
                  return false;
               }

               label167: {
                  Object this$ledgerBal = this.getLedgerBal();
                  Object other$ledgerBal = other.getLedgerBal();
                  if (this$ledgerBal == null) {
                     if (other$ledgerBal == null) {
                        break label167;
                     }
                  } else if (this$ledgerBal.equals(other$ledgerBal)) {
                     break label167;
                  }

                  return false;
               }

               label160: {
                  Object this$availableAmt = this.getAvailableAmt();
                  Object other$availableAmt = other.getAvailableAmt();
                  if (this$availableAmt == null) {
                     if (other$availableAmt == null) {
                        break label160;
                     }
                  } else if (this$availableAmt.equals(other$availableAmt)) {
                     break label160;
                  }

                  return false;
               }

               Object this$pledgedAmt = this.getPledgedAmt();
               Object other$pledgedAmt = other.getPledgedAmt();
               if (this$pledgedAmt == null) {
                  if (other$pledgedAmt != null) {
                     return false;
                  }
               } else if (!this$pledgedAmt.equals(other$pledgedAmt)) {
                  return false;
               }

               Object this$acctNature = this.getAcctNature();
               Object other$acctNature = other.getAcctNature();
               if (this$acctNature == null) {
                  if (other$acctNature != null) {
                     return false;
                  }
               } else if (!this$acctNature.equals(other$acctNature)) {
                  return false;
               }

               label139: {
                  Object this$acctBranch = this.getAcctBranch();
                  Object other$acctBranch = other.getAcctBranch();
                  if (this$acctBranch == null) {
                     if (other$acctBranch == null) {
                        break label139;
                     }
                  } else if (this$acctBranch.equals(other$acctBranch)) {
                     break label139;
                  }

                  return false;
               }

               Object this$branchName = this.getBranchName();
               Object other$branchName = other.getBranchName();
               if (this$branchName == null) {
                  if (other$branchName != null) {
                     return false;
                  }
               } else if (!this$branchName.equals(other$branchName)) {
                  return false;
               }

               Object this$realRate = this.getRealRate();
               Object other$realRate = other.getRealRate();
               if (this$realRate == null) {
                  if (other$realRate != null) {
                     return false;
                  }
               } else if (!this$realRate.equals(other$realRate)) {
                  return false;
               }

               Object this$partWithdrawFlag = this.getPartWithdrawFlag();
               Object other$partWithdrawFlag = other.getPartWithdrawFlag();
               if (this$partWithdrawFlag == null) {
                  if (other$partWithdrawFlag != null) {
                     return false;
                  }
               } else if (!this$partWithdrawFlag.equals(other$partWithdrawFlag)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100008Out.SubArray;
      }
      public String toString() {
         return "Core1400100008Out.SubArray(baseAcctNo=" + this.getBaseAcctNo() + ", prodType=" + this.getProdType() + ", acctSeqNo=" + this.getAcctSeqNo() + ", acctCcy=" + this.getAcctCcy() + ", acctName=" + this.getAcctName() + ", clientNo=" + this.getClientNo() + ", acctOpenDate=" + this.getAcctOpenDate() + ", effectDate=" + this.getEffectDate() + ", acctCloseDate=" + this.getAcctCloseDate() + ", acctStatus=" + this.getAcctStatus() + ", term=" + this.getTerm() + ", termType=" + this.getTermType() + ", ledgerBal=" + this.getLedgerBal() + ", availableAmt=" + this.getAvailableAmt() + ", pledgedAmt=" + this.getPledgedAmt() + ", acctNature=" + this.getAcctNature() + ", acctBranch=" + this.getAcctBranch() + ", branchName=" + this.getBranchName() + ", realRate=" + this.getRealRate() + ", partWithdrawFlag=" + this.getPartWithdrawFlag() + ")";
      }
   }
}
