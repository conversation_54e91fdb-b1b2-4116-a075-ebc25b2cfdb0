package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;

@MessageIn
public class Core1200100125In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1200100125In.Body body;

   public Core1200100125In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1200100125In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1200100125In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200100125In)) {
         return false;
      } else {
         Core1200100125In other = (Core1200100125In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200100125In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "协议操作类型",
         notNull = true,
         length = "2",
         inDesc = "01-创建,02-修改,03-删除",
         remark = "协议操作类型",
         maxSize = 2
      )
      private String agreementOperateType;
      @V(
         desc = "账号/卡号",
         notNull = true,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "协议类型",
         notNull = false,
         length = "10",
         inDesc = "CLD-存立得,DC-大额存单,DLS-贷利省,HQB-活期宝,JDL-加多利,KDT-卡贷通,KYD-卡易贷,PCP-资金池,WDL-稳得利,XDB-协定宝,XDCK-协定存款产品,XDL-先得利,YBWL-一本万利,YCD-英才贷,YDT-易贷通,YHT-一户通,ZHY-周享赢,ZXY-坐享其盈,ZZB-至尊宝,LOA-贷款,ODF-法人透支协议,FIN-卡理财协议,SMS-短信,PKG-费用套餐,FEE-暂不收费,PCD-周期性强制扣划,ACC-协定存款协议,SWP-账户清扫协议,ID-智能存款协议,SL-金额补足协议,REC-回单签约,ES-电票签约,YD-约定,NTE-活期智能存款,PAS-隐私账户签约,BXD-协定利率（无留存）",
         remark = "协议类型",
         maxSize = 10
      )
      private String agreementType;
      @V(
         desc = "协议编号",
         notNull = false,
         length = "50",
         remark = "协议编号",
         maxSize = 50
      )
      private String agreementId;
      @V(
         desc = "协议状态",
         notNull = false,
         length = "2",
         inDesc = "A-生效,E-失效",
         remark = "普通协议使用，可应用于大部分场景",
         maxSize = 2
      )
      private String agreementStatus;
      @V(
         desc = "转存起始日期",
         notNull = false,
         remark = "理财签约后的转存开始日期"
      )
      private String transferStartDate;
      @V(
         desc = "转存结束日期或终止日期",
         notNull = false,
         remark = "理财协议转存的结束日期"
      )
      private String transferEndDate;
      @V(
         desc = "签约产品类型",
         notNull = false,
         length = "20",
         remark = "签约产品类型",
         maxSize = 20
      )
      private String signProdType;
      @V(
         desc = "协议留存金额",
         notNull = false,
         length = "17",
         remark = "协议留存金额（活期卡转存后最小剩余金额）",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal remainAmt;
      @V(
         desc = "理财固定金额",
         notNull = false,
         length = "17",
         remark = "理财固定金额（活期卡约定转存固定金额）",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal finFixedAmt;
      @V(
         desc = "最小起存金额",
         notNull = false,
         length = "17",
         remark = "最小起存金额（活期卡最小转存借记金额 小于此金额不进行转存）",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal intMinAmt;
      @V(
         desc = "划转频率",
         notNull = false,
         length = "5",
         remark = "划转频率",
         maxSize = 5
      )
      private String transferFreq;
      @V(
         desc = "划转频率类型",
         notNull = false,
         length = "5",
         remark = "理财划转频率了行，Y年/M-月/D-日/Q-季/W-周",
         maxSize = 5
      )
      private String transferFreqType;
      @V(
         desc = "划转日",
         notNull = false,
         length = "2",
         remark = "划转日",
         maxSize = 2
      )
      private String transferDay;
      @V(
         desc = "存期期限",
         notNull = false,
         length = "5",
         remark = "期限",
         maxSize = 5
      )
      private String term;
      @V(
         desc = "期限类型",
         notNull = false,
         length = "1",
         inDesc = "Y-年,Q-季,M-月,W-周,D-日",
         remark = "期限类型",
         maxSize = 1
      )
      private String termType;
      @V(
         desc = "客户经理",
         notNull = false,
         length = "30",
         remark = "客户经理",
         maxSize = 30
      )
      private String acctExec;
      @V(
         desc = "客户经理姓名",
         notNull = false,
         length = "200",
         remark = "客户经理姓名",
         maxSize = 200
      )
      private String acctExecName;
      @V(
         desc = "客户号",
         notNull = true,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "开始日期",
         notNull = false,
         remark = "开始日期"
      )
      private String startDate;
      @V(
         desc = "结束日期",
         notNull = false,
         remark = "结束日期"
      )
      private String endDate;
      @V(
         desc = "起始金额",
         notNull = false,
         length = "17",
         remark = "起始金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal startAmt;
      @V(
         desc = "转存交易日期",
         notNull = false,
         remark = "转存交易日期"
      )
      private String acctMovtDate;

      public String getAgreementOperateType() {
         return this.agreementOperateType;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getAgreementType() {
         return this.agreementType;
      }

      public String getAgreementId() {
         return this.agreementId;
      }

      public String getAgreementStatus() {
         return this.agreementStatus;
      }

      public String getTransferStartDate() {
         return this.transferStartDate;
      }

      public String getTransferEndDate() {
         return this.transferEndDate;
      }

      public String getSignProdType() {
         return this.signProdType;
      }

      public BigDecimal getRemainAmt() {
         return this.remainAmt;
      }

      public BigDecimal getFinFixedAmt() {
         return this.finFixedAmt;
      }

      public BigDecimal getIntMinAmt() {
         return this.intMinAmt;
      }

      public String getTransferFreq() {
         return this.transferFreq;
      }

      public String getTransferFreqType() {
         return this.transferFreqType;
      }

      public String getTransferDay() {
         return this.transferDay;
      }

      public String getTerm() {
         return this.term;
      }

      public String getTermType() {
         return this.termType;
      }

      public String getAcctExec() {
         return this.acctExec;
      }

      public String getAcctExecName() {
         return this.acctExecName;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public String getStartDate() {
         return this.startDate;
      }

      public String getEndDate() {
         return this.endDate;
      }

      public BigDecimal getStartAmt() {
         return this.startAmt;
      }

      public String getAcctMovtDate() {
         return this.acctMovtDate;
      }

      public void setAgreementOperateType(String agreementOperateType) {
         this.agreementOperateType = agreementOperateType;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setAgreementType(String agreementType) {
         this.agreementType = agreementType;
      }

      public void setAgreementId(String agreementId) {
         this.agreementId = agreementId;
      }

      public void setAgreementStatus(String agreementStatus) {
         this.agreementStatus = agreementStatus;
      }

      public void setTransferStartDate(String transferStartDate) {
         this.transferStartDate = transferStartDate;
      }

      public void setTransferEndDate(String transferEndDate) {
         this.transferEndDate = transferEndDate;
      }

      public void setSignProdType(String signProdType) {
         this.signProdType = signProdType;
      }

      public void setRemainAmt(BigDecimal remainAmt) {
         this.remainAmt = remainAmt;
      }

      public void setFinFixedAmt(BigDecimal finFixedAmt) {
         this.finFixedAmt = finFixedAmt;
      }

      public void setIntMinAmt(BigDecimal intMinAmt) {
         this.intMinAmt = intMinAmt;
      }

      public void setTransferFreq(String transferFreq) {
         this.transferFreq = transferFreq;
      }

      public void setTransferFreqType(String transferFreqType) {
         this.transferFreqType = transferFreqType;
      }

      public void setTransferDay(String transferDay) {
         this.transferDay = transferDay;
      }

      public void setTerm(String term) {
         this.term = term;
      }

      public void setTermType(String termType) {
         this.termType = termType;
      }

      public void setAcctExec(String acctExec) {
         this.acctExec = acctExec;
      }

      public void setAcctExecName(String acctExecName) {
         this.acctExecName = acctExecName;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setStartDate(String startDate) {
         this.startDate = startDate;
      }

      public void setEndDate(String endDate) {
         this.endDate = endDate;
      }

      public void setStartAmt(BigDecimal startAmt) {
         this.startAmt = startAmt;
      }

      public void setAcctMovtDate(String acctMovtDate) {
         this.acctMovtDate = acctMovtDate;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1200100125In.Body)) {
            return false;
         } else {
            Core1200100125In.Body other = (Core1200100125In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$agreementOperateType = this.getAgreementOperateType();
               Object other$agreementOperateType = other.getAgreementOperateType();
               if (this$agreementOperateType == null) {
                  if (other$agreementOperateType != null) {
                     return false;
                  }
               } else if (!this$agreementOperateType.equals(other$agreementOperateType)) {
                  return false;
               }

               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo != null) {
                     return false;
                  }
               } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               label302: {
                  Object this$acctCcy = this.getAcctCcy();
                  Object other$acctCcy = other.getAcctCcy();
                  if (this$acctCcy == null) {
                     if (other$acctCcy == null) {
                        break label302;
                     }
                  } else if (this$acctCcy.equals(other$acctCcy)) {
                     break label302;
                  }

                  return false;
               }

               label295: {
                  Object this$acctSeqNo = this.getAcctSeqNo();
                  Object other$acctSeqNo = other.getAcctSeqNo();
                  if (this$acctSeqNo == null) {
                     if (other$acctSeqNo == null) {
                        break label295;
                     }
                  } else if (this$acctSeqNo.equals(other$acctSeqNo)) {
                     break label295;
                  }

                  return false;
               }

               Object this$agreementType = this.getAgreementType();
               Object other$agreementType = other.getAgreementType();
               if (this$agreementType == null) {
                  if (other$agreementType != null) {
                     return false;
                  }
               } else if (!this$agreementType.equals(other$agreementType)) {
                  return false;
               }

               label281: {
                  Object this$agreementId = this.getAgreementId();
                  Object other$agreementId = other.getAgreementId();
                  if (this$agreementId == null) {
                     if (other$agreementId == null) {
                        break label281;
                     }
                  } else if (this$agreementId.equals(other$agreementId)) {
                     break label281;
                  }

                  return false;
               }

               label274: {
                  Object this$agreementStatus = this.getAgreementStatus();
                  Object other$agreementStatus = other.getAgreementStatus();
                  if (this$agreementStatus == null) {
                     if (other$agreementStatus == null) {
                        break label274;
                     }
                  } else if (this$agreementStatus.equals(other$agreementStatus)) {
                     break label274;
                  }

                  return false;
               }

               Object this$transferStartDate = this.getTransferStartDate();
               Object other$transferStartDate = other.getTransferStartDate();
               if (this$transferStartDate == null) {
                  if (other$transferStartDate != null) {
                     return false;
                  }
               } else if (!this$transferStartDate.equals(other$transferStartDate)) {
                  return false;
               }

               Object this$transferEndDate = this.getTransferEndDate();
               Object other$transferEndDate = other.getTransferEndDate();
               if (this$transferEndDate == null) {
                  if (other$transferEndDate != null) {
                     return false;
                  }
               } else if (!this$transferEndDate.equals(other$transferEndDate)) {
                  return false;
               }

               label253: {
                  Object this$signProdType = this.getSignProdType();
                  Object other$signProdType = other.getSignProdType();
                  if (this$signProdType == null) {
                     if (other$signProdType == null) {
                        break label253;
                     }
                  } else if (this$signProdType.equals(other$signProdType)) {
                     break label253;
                  }

                  return false;
               }

               label246: {
                  Object this$remainAmt = this.getRemainAmt();
                  Object other$remainAmt = other.getRemainAmt();
                  if (this$remainAmt == null) {
                     if (other$remainAmt == null) {
                        break label246;
                     }
                  } else if (this$remainAmt.equals(other$remainAmt)) {
                     break label246;
                  }

                  return false;
               }

               Object this$finFixedAmt = this.getFinFixedAmt();
               Object other$finFixedAmt = other.getFinFixedAmt();
               if (this$finFixedAmt == null) {
                  if (other$finFixedAmt != null) {
                     return false;
                  }
               } else if (!this$finFixedAmt.equals(other$finFixedAmt)) {
                  return false;
               }

               label232: {
                  Object this$intMinAmt = this.getIntMinAmt();
                  Object other$intMinAmt = other.getIntMinAmt();
                  if (this$intMinAmt == null) {
                     if (other$intMinAmt == null) {
                        break label232;
                     }
                  } else if (this$intMinAmt.equals(other$intMinAmt)) {
                     break label232;
                  }

                  return false;
               }

               Object this$transferFreq = this.getTransferFreq();
               Object other$transferFreq = other.getTransferFreq();
               if (this$transferFreq == null) {
                  if (other$transferFreq != null) {
                     return false;
                  }
               } else if (!this$transferFreq.equals(other$transferFreq)) {
                  return false;
               }

               label218: {
                  Object this$transferFreqType = this.getTransferFreqType();
                  Object other$transferFreqType = other.getTransferFreqType();
                  if (this$transferFreqType == null) {
                     if (other$transferFreqType == null) {
                        break label218;
                     }
                  } else if (this$transferFreqType.equals(other$transferFreqType)) {
                     break label218;
                  }

                  return false;
               }

               Object this$transferDay = this.getTransferDay();
               Object other$transferDay = other.getTransferDay();
               if (this$transferDay == null) {
                  if (other$transferDay != null) {
                     return false;
                  }
               } else if (!this$transferDay.equals(other$transferDay)) {
                  return false;
               }

               Object this$term = this.getTerm();
               Object other$term = other.getTerm();
               if (this$term == null) {
                  if (other$term != null) {
                     return false;
                  }
               } else if (!this$term.equals(other$term)) {
                  return false;
               }

               Object this$termType = this.getTermType();
               Object other$termType = other.getTermType();
               if (this$termType == null) {
                  if (other$termType != null) {
                     return false;
                  }
               } else if (!this$termType.equals(other$termType)) {
                  return false;
               }

               label190: {
                  Object this$acctExec = this.getAcctExec();
                  Object other$acctExec = other.getAcctExec();
                  if (this$acctExec == null) {
                     if (other$acctExec == null) {
                        break label190;
                     }
                  } else if (this$acctExec.equals(other$acctExec)) {
                     break label190;
                  }

                  return false;
               }

               label183: {
                  Object this$acctExecName = this.getAcctExecName();
                  Object other$acctExecName = other.getAcctExecName();
                  if (this$acctExecName == null) {
                     if (other$acctExecName == null) {
                        break label183;
                     }
                  } else if (this$acctExecName.equals(other$acctExecName)) {
                     break label183;
                  }

                  return false;
               }

               Object this$clientNo = this.getClientNo();
               Object other$clientNo = other.getClientNo();
               if (this$clientNo == null) {
                  if (other$clientNo != null) {
                     return false;
                  }
               } else if (!this$clientNo.equals(other$clientNo)) {
                  return false;
               }

               label169: {
                  Object this$startDate = this.getStartDate();
                  Object other$startDate = other.getStartDate();
                  if (this$startDate == null) {
                     if (other$startDate == null) {
                        break label169;
                     }
                  } else if (this$startDate.equals(other$startDate)) {
                     break label169;
                  }

                  return false;
               }

               label162: {
                  Object this$endDate = this.getEndDate();
                  Object other$endDate = other.getEndDate();
                  if (this$endDate == null) {
                     if (other$endDate == null) {
                        break label162;
                     }
                  } else if (this$endDate.equals(other$endDate)) {
                     break label162;
                  }

                  return false;
               }

               Object this$startAmt = this.getStartAmt();
               Object other$startAmt = other.getStartAmt();
               if (this$startAmt == null) {
                  if (other$startAmt != null) {
                     return false;
                  }
               } else if (!this$startAmt.equals(other$startAmt)) {
                  return false;
               }

               Object this$acctMovtDate = this.getAcctMovtDate();
               Object other$acctMovtDate = other.getAcctMovtDate();
               if (this$acctMovtDate == null) {
                  if (other$acctMovtDate != null) {
                     return false;
                  }
               } else if (!this$acctMovtDate.equals(other$acctMovtDate)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1200100125In.Body;
      }
      public String toString() {
         return "Core1200100125In.Body(agreementOperateType=" + this.getAgreementOperateType() + ", baseAcctNo=" + this.getBaseAcctNo() + ", prodType=" + this.getProdType() + ", acctCcy=" + this.getAcctCcy() + ", acctSeqNo=" + this.getAcctSeqNo() + ", agreementType=" + this.getAgreementType() + ", agreementId=" + this.getAgreementId() + ", agreementStatus=" + this.getAgreementStatus() + ", transferStartDate=" + this.getTransferStartDate() + ", transferEndDate=" + this.getTransferEndDate() + ", signProdType=" + this.getSignProdType() + ", remainAmt=" + this.getRemainAmt() + ", finFixedAmt=" + this.getFinFixedAmt() + ", intMinAmt=" + this.getIntMinAmt() + ", transferFreq=" + this.getTransferFreq() + ", transferFreqType=" + this.getTransferFreqType() + ", transferDay=" + this.getTransferDay() + ", term=" + this.getTerm() + ", termType=" + this.getTermType() + ", acctExec=" + this.getAcctExec() + ", acctExecName=" + this.getAcctExecName() + ", clientNo=" + this.getClientNo() + ", startDate=" + this.getStartDate() + ", endDate=" + this.getEndDate() + ", startAmt=" + this.getStartAmt() + ", acctMovtDate=" + this.getAcctMovtDate() + ")";
      }
   }
}
