package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000166In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000166Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000166 {
   String URL = "/rb/inq/acct/hangwriteoff";

   
   @ApiRemark("挂销账处理登记查询")
   @ApiDesc("挂销账处理登记查询，查询挂账和销账的账户信息")
   @EnsembleElements(
      serviceCode = "Core",
      messageType = "1400",
      messageCode = "0166"
   )
   @FunctionCategory("RB15-内部账")
   Core14000166Out runService(Core14000166In var1);
}
