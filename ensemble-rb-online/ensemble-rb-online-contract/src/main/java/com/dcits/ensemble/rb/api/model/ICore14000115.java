package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000115In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000115Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000115 {
   String URL = "/rb/inq/acct/voucher";


   @ApiRemark("标准优化")
   @ApiDesc("通过凭证号查询对应账户的信息")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0115"
   )
   @BusinessCategory("1400-查询")
   @FunctionCategory("RB47-客户账户查询")
   @ConsumeSys("CBS")
   @ApiUseStatus("PRODUCT-产品")
   Core14000115Out runService(Core14000115In var1);
}
