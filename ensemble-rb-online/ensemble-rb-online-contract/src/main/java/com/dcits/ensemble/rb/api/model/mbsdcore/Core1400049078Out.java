package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400049078Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400049078Out.OutArray> outArray;

   public List<Core1400049078Out.OutArray> getOutArray() {
      return this.outArray;
   }

   public void setOutArray(List<Core1400049078Out.OutArray> outArray) {
      this.outArray = outArray;
   }

   public String toString() {
      return "Core1400049078Out(outArray=" + this.getOutArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400049078Out)) {
         return false;
      } else {
         Core1400049078Out other = (Core1400049078Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$outArray = this.getOutArray();
            Object other$outArray = other.getOutArray();
            if (this$outArray == null) {
               if (other$outArray != null) {
                  return false;
               }
            } else if (!this$outArray.equals(other$outArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400049078Out;
   }
   public static class OutArray {
      @V(
         desc = "序号",
         notNull = false,
         length = "50",
         remark = "序号",
         maxSize = 50
      )
      private String seqNo;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "交易日期",
         notNull = false,
         remark = "交易日期"
      )
      private String tranDate;
      @V(
         desc = "对手业务编号",
         notNull = false,
         length = "200",
         remark = "对手业务编号",
         maxSize = 200
      )
      private String othBusinessNo;
      @V(
         desc = "原始费用金额,即折扣前的费用金额",
         notNull = false,
         length = "17",
         remark = "原始费用金额,即折扣前的费用金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal origFeeAmt;
      @V(
         desc = "折扣金额",
         notNull = false,
         length = "17",
         remark = "费用折扣金额，即优惠的金额，少收取的金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal discFeeAmt;
      @V(
         desc = "费用金额",
         notNull = false,
         length = "17",
         remark = "费用金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal feeAmt;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "收费方式",
         notNull = false,
         length = "1",
         in = "A,M,P,R,N",
         remark = "收费方式",
         maxSize = 1
      )
      private String chargeWay;
      @V(
         desc = "费用类型",
         notNull = false,
         length = "20",
         remark = "费率类型",
         maxSize = 20
      )
      private String feeType;
      @V(
         desc = "套餐代码",
         notNull = false,
         length = "50",
         remark = "套餐代码",
         maxSize = 50
      )
      private String packageId;
      @V(
         desc = "套餐描述",
         notNull = false,
         length = "50",
         remark = "套餐描述",
         maxSize = 50
      )
      private String packageDesc;
      @V(
         desc = "对手名称",
         notNull = false,
         length = "200",
         remark = "对手名称",
         maxSize = 200
      )
      private String othName;
      @V(
         desc = "法人",
         notNull = false,
         length = "20",
         remark = "法人",
         maxSize = 20
      )
      private String company;

      public String getSeqNo() {
         return this.seqNo;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getTranDate() {
         return this.tranDate;
      }

      public String getOthBusinessNo() {
         return this.othBusinessNo;
      }

      public BigDecimal getOrigFeeAmt() {
         return this.origFeeAmt;
      }

      public BigDecimal getDiscFeeAmt() {
         return this.discFeeAmt;
      }

      public BigDecimal getFeeAmt() {
         return this.feeAmt;
      }

      public String getCcy() {
         return this.ccy;
      }

      public String getChargeWay() {
         return this.chargeWay;
      }

      public String getFeeType() {
         return this.feeType;
      }

      public String getPackageId() {
         return this.packageId;
      }

      public String getPackageDesc() {
         return this.packageDesc;
      }

      public String getOthName() {
         return this.othName;
      }

      public String getCompany() {
         return this.company;
      }

      public void setSeqNo(String seqNo) {
         this.seqNo = seqNo;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setTranDate(String tranDate) {
         this.tranDate = tranDate;
      }

      public void setOthBusinessNo(String othBusinessNo) {
         this.othBusinessNo = othBusinessNo;
      }

      public void setOrigFeeAmt(BigDecimal origFeeAmt) {
         this.origFeeAmt = origFeeAmt;
      }

      public void setDiscFeeAmt(BigDecimal discFeeAmt) {
         this.discFeeAmt = discFeeAmt;
      }

      public void setFeeAmt(BigDecimal feeAmt) {
         this.feeAmt = feeAmt;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setChargeWay(String chargeWay) {
         this.chargeWay = chargeWay;
      }

      public void setFeeType(String feeType) {
         this.feeType = feeType;
      }

      public void setPackageId(String packageId) {
         this.packageId = packageId;
      }

      public void setPackageDesc(String packageDesc) {
         this.packageDesc = packageDesc;
      }

      public void setOthName(String othName) {
         this.othName = othName;
      }

      public void setCompany(String company) {
         this.company = company;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400049078Out.OutArray)) {
            return false;
         } else {
            Core1400049078Out.OutArray other = (Core1400049078Out.OutArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$seqNo = this.getSeqNo();
               Object other$seqNo = other.getSeqNo();
               if (this$seqNo == null) {
                  if (other$seqNo != null) {
                     return false;
                  }
               } else if (!this$seqNo.equals(other$seqNo)) {
                  return false;
               }

               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo != null) {
                     return false;
                  }
               } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
                  return false;
               }

               Object this$tranDate = this.getTranDate();
               Object other$tranDate = other.getTranDate();
               if (this$tranDate == null) {
                  if (other$tranDate != null) {
                     return false;
                  }
               } else if (!this$tranDate.equals(other$tranDate)) {
                  return false;
               }

               label158: {
                  Object this$othBusinessNo = this.getOthBusinessNo();
                  Object other$othBusinessNo = other.getOthBusinessNo();
                  if (this$othBusinessNo == null) {
                     if (other$othBusinessNo == null) {
                        break label158;
                     }
                  } else if (this$othBusinessNo.equals(other$othBusinessNo)) {
                     break label158;
                  }

                  return false;
               }

               label151: {
                  Object this$origFeeAmt = this.getOrigFeeAmt();
                  Object other$origFeeAmt = other.getOrigFeeAmt();
                  if (this$origFeeAmt == null) {
                     if (other$origFeeAmt == null) {
                        break label151;
                     }
                  } else if (this$origFeeAmt.equals(other$origFeeAmt)) {
                     break label151;
                  }

                  return false;
               }

               Object this$discFeeAmt = this.getDiscFeeAmt();
               Object other$discFeeAmt = other.getDiscFeeAmt();
               if (this$discFeeAmt == null) {
                  if (other$discFeeAmt != null) {
                     return false;
                  }
               } else if (!this$discFeeAmt.equals(other$discFeeAmt)) {
                  return false;
               }

               label137: {
                  Object this$feeAmt = this.getFeeAmt();
                  Object other$feeAmt = other.getFeeAmt();
                  if (this$feeAmt == null) {
                     if (other$feeAmt == null) {
                        break label137;
                     }
                  } else if (this$feeAmt.equals(other$feeAmt)) {
                     break label137;
                  }

                  return false;
               }

               label130: {
                  Object this$ccy = this.getCcy();
                  Object other$ccy = other.getCcy();
                  if (this$ccy == null) {
                     if (other$ccy == null) {
                        break label130;
                     }
                  } else if (this$ccy.equals(other$ccy)) {
                     break label130;
                  }

                  return false;
               }

               Object this$chargeWay = this.getChargeWay();
               Object other$chargeWay = other.getChargeWay();
               if (this$chargeWay == null) {
                  if (other$chargeWay != null) {
                     return false;
                  }
               } else if (!this$chargeWay.equals(other$chargeWay)) {
                  return false;
               }

               Object this$feeType = this.getFeeType();
               Object other$feeType = other.getFeeType();
               if (this$feeType == null) {
                  if (other$feeType != null) {
                     return false;
                  }
               } else if (!this$feeType.equals(other$feeType)) {
                  return false;
               }

               label109: {
                  Object this$packageId = this.getPackageId();
                  Object other$packageId = other.getPackageId();
                  if (this$packageId == null) {
                     if (other$packageId == null) {
                        break label109;
                     }
                  } else if (this$packageId.equals(other$packageId)) {
                     break label109;
                  }

                  return false;
               }

               label102: {
                  Object this$packageDesc = this.getPackageDesc();
                  Object other$packageDesc = other.getPackageDesc();
                  if (this$packageDesc == null) {
                     if (other$packageDesc == null) {
                        break label102;
                     }
                  } else if (this$packageDesc.equals(other$packageDesc)) {
                     break label102;
                  }

                  return false;
               }

               Object this$othName = this.getOthName();
               Object other$othName = other.getOthName();
               if (this$othName == null) {
                  if (other$othName != null) {
                     return false;
                  }
               } else if (!this$othName.equals(other$othName)) {
                  return false;
               }

               Object this$company = this.getCompany();
               Object other$company = other.getCompany();
               if (this$company == null) {
                  if (other$company != null) {
                     return false;
                  }
               } else if (!this$company.equals(other$company)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400049078Out.OutArray;
      }
      public String toString() {
         return "Core1400049078Out.OutArray(seqNo=" + this.getSeqNo() + ", baseAcctNo=" + this.getBaseAcctNo() + ", tranDate=" + this.getTranDate() + ", othBusinessNo=" + this.getOthBusinessNo() + ", origFeeAmt=" + this.getOrigFeeAmt() + ", discFeeAmt=" + this.getDiscFeeAmt() + ", feeAmt=" + this.getFeeAmt() + ", ccy=" + this.getCcy() + ", chargeWay=" + this.getChargeWay() + ", feeType=" + this.getFeeType() + ", packageId=" + this.getPackageId() + ", packageDesc=" + this.getPackageDesc() + ", othName=" + this.getOthName() + ", company=" + this.getCompany() + ")";
      }
   }
}
