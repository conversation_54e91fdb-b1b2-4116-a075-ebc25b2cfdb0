package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12003211In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12003211Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12003211 {
   String URL = "/rb/nfin/fin/agreement/avg/operate";


   @ApiDesc("日均余额靠档协议签约、维护、解约")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "3211"
   )
   @BusinessCategory("1200-非金融")
   @FunctionCategory("RB05-协议管理")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core12003211Out runService(Core12003211In var1);
}
