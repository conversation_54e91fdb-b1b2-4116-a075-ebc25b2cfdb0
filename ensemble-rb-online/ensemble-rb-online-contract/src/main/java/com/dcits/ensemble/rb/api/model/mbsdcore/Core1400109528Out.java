package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400109528Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400109528Out.ItemArray> itemArray;

   public List<Core1400109528Out.ItemArray> getItemArray() {
      return this.itemArray;
   }

   public void setItemArray(List<Core1400109528Out.ItemArray> itemArray) {
      this.itemArray = itemArray;
   }

   public String toString() {
      return "Core1400109528Out(itemArray=" + this.getItemArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400109528Out)) {
         return false;
      } else {
         Core1400109528Out other = (Core1400109528Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$itemArray = this.getItemArray();
            Object other$itemArray = other.getItemArray();
            if (this$itemArray == null) {
               if (other$itemArray != null) {
                  return false;
               }
            } else if (!this$itemArray.equals(other$itemArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400109528Out;
   }
   public static class ItemArray {
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "账户名称",
         notNull = false,
         length = "200",
         remark = "账户名称，一般指中文账户名称",
         maxSize = 200
      )
      private String acctName;
      @V(
         desc = "余额",
         notNull = false,
         length = "17",
         remark = "余额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal balance;
      @V(
         desc = "他行账号",
         notNull = false,
         length = "50",
         remark = "他行账号",
         maxSize = 50
      )
      private String contraBaseAcctNo;
      @V(
         desc = "备付金账户类型",
         notNull = false,
         length = "1",
         remark = "0-资金归集账户1-零余额账户",
         maxSize = 1
      )
      private String fundAcctType;
      @V(
         desc = "对方账号/卡号",
         notNull = false,
         length = "50",
         remark = "对方账号/卡号",
         maxSize = 50
      )
      private String othBaseAcctNo;
      @V(
         desc = "对方账户序列号",
         notNull = false,
         length = "5",
         remark = "对方账户序列号",
         maxSize = 5
      )
      private String othAcctSeqNo;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getAcctName() {
         return this.acctName;
      }

      public BigDecimal getBalance() {
         return this.balance;
      }

      public String getContraBaseAcctNo() {
         return this.contraBaseAcctNo;
      }

      public String getFundAcctType() {
         return this.fundAcctType;
      }

      public String getOthBaseAcctNo() {
         return this.othBaseAcctNo;
      }

      public String getOthAcctSeqNo() {
         return this.othAcctSeqNo;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setAcctName(String acctName) {
         this.acctName = acctName;
      }

      public void setBalance(BigDecimal balance) {
         this.balance = balance;
      }

      public void setContraBaseAcctNo(String contraBaseAcctNo) {
         this.contraBaseAcctNo = contraBaseAcctNo;
      }

      public void setFundAcctType(String fundAcctType) {
         this.fundAcctType = fundAcctType;
      }

      public void setOthBaseAcctNo(String othBaseAcctNo) {
         this.othBaseAcctNo = othBaseAcctNo;
      }

      public void setOthAcctSeqNo(String othAcctSeqNo) {
         this.othAcctSeqNo = othAcctSeqNo;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400109528Out.ItemArray)) {
            return false;
         } else {
            Core1400109528Out.ItemArray other = (Core1400109528Out.ItemArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label107: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label107;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label107;
                  }

                  return false;
               }

               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo != null) {
                     return false;
                  }
               } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                  return false;
               }

               Object this$acctName = this.getAcctName();
               Object other$acctName = other.getAcctName();
               if (this$acctName == null) {
                  if (other$acctName != null) {
                     return false;
                  }
               } else if (!this$acctName.equals(other$acctName)) {
                  return false;
               }

               label86: {
                  Object this$balance = this.getBalance();
                  Object other$balance = other.getBalance();
                  if (this$balance == null) {
                     if (other$balance == null) {
                        break label86;
                     }
                  } else if (this$balance.equals(other$balance)) {
                     break label86;
                  }

                  return false;
               }

               label79: {
                  Object this$contraBaseAcctNo = this.getContraBaseAcctNo();
                  Object other$contraBaseAcctNo = other.getContraBaseAcctNo();
                  if (this$contraBaseAcctNo == null) {
                     if (other$contraBaseAcctNo == null) {
                        break label79;
                     }
                  } else if (this$contraBaseAcctNo.equals(other$contraBaseAcctNo)) {
                     break label79;
                  }

                  return false;
               }

               label72: {
                  Object this$fundAcctType = this.getFundAcctType();
                  Object other$fundAcctType = other.getFundAcctType();
                  if (this$fundAcctType == null) {
                     if (other$fundAcctType == null) {
                        break label72;
                     }
                  } else if (this$fundAcctType.equals(other$fundAcctType)) {
                     break label72;
                  }

                  return false;
               }

               Object this$othBaseAcctNo = this.getOthBaseAcctNo();
               Object other$othBaseAcctNo = other.getOthBaseAcctNo();
               if (this$othBaseAcctNo == null) {
                  if (other$othBaseAcctNo != null) {
                     return false;
                  }
               } else if (!this$othBaseAcctNo.equals(other$othBaseAcctNo)) {
                  return false;
               }

               Object this$othAcctSeqNo = this.getOthAcctSeqNo();
               Object other$othAcctSeqNo = other.getOthAcctSeqNo();
               if (this$othAcctSeqNo == null) {
                  if (other$othAcctSeqNo != null) {
                     return false;
                  }
               } else if (!this$othAcctSeqNo.equals(other$othAcctSeqNo)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400109528Out.ItemArray;
      }
      public String toString() {
         return "Core1400109528Out.ItemArray(baseAcctNo=" + this.getBaseAcctNo() + ", acctSeqNo=" + this.getAcctSeqNo() + ", acctName=" + this.getAcctName() + ", balance=" + this.getBalance() + ", contraBaseAcctNo=" + this.getContraBaseAcctNo() + ", fundAcctType=" + this.getFundAcctType() + ", othBaseAcctNo=" + this.getOthBaseAcctNo() + ", othAcctSeqNo=" + this.getOthAcctSeqNo() + ")";
      }
   }
}
