package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;

@MessageOut
public class Core1000023404Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "出票金额",
      notNull = false,
      length = "17",
      remark = "出票金额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal billTranAmt;
   @V(
      desc = "票据签发机构",
      notNull = false,
      length = "50",
      remark = "签发机构",
      maxSize = 50
   )
   private String billSignBranch;
   @V(
      desc = "备注",
      notNull = false,
      length = "200",
      remark = "备注",
      maxSize = 200
   )
   private String remark;
   @V(
      desc = "业务流水号",
      notNull = false,
      length = "50",
      remark = "支付流水号",
      maxSize = 50
   )
   private String serialNo;
   @V(
      desc = "交易参考号",
      notNull = false,
      length = "50",
      remark = "交易参考号",
      maxSize = 50
   )
   private String reference;

   public BigDecimal getBillTranAmt() {
      return this.billTranAmt;
   }

   public String getBillSignBranch() {
      return this.billSignBranch;
   }

   public String getRemark() {
      return this.remark;
   }

   public String getSerialNo() {
      return this.serialNo;
   }

   public String getReference() {
      return this.reference;
   }

   public void setBillTranAmt(BigDecimal billTranAmt) {
      this.billTranAmt = billTranAmt;
   }

   public void setBillSignBranch(String billSignBranch) {
      this.billSignBranch = billSignBranch;
   }

   public void setRemark(String remark) {
      this.remark = remark;
   }

   public void setSerialNo(String serialNo) {
      this.serialNo = serialNo;
   }

   public void setReference(String reference) {
      this.reference = reference;
   }

   public String toString() {
      return "Core1000023404Out(billTranAmt=" + this.getBillTranAmt() + ", billSignBranch=" + this.getBillSignBranch() + ", remark=" + this.getRemark() + ", serialNo=" + this.getSerialNo() + ", reference=" + this.getReference() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1000023404Out)) {
         return false;
      } else {
         Core1000023404Out other = (Core1000023404Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            label73: {
               Object this$billTranAmt = this.getBillTranAmt();
               Object other$billTranAmt = other.getBillTranAmt();
               if (this$billTranAmt == null) {
                  if (other$billTranAmt == null) {
                     break label73;
                  }
               } else if (this$billTranAmt.equals(other$billTranAmt)) {
                  break label73;
               }

               return false;
            }

            Object this$billSignBranch = this.getBillSignBranch();
            Object other$billSignBranch = other.getBillSignBranch();
            if (this$billSignBranch == null) {
               if (other$billSignBranch != null) {
                  return false;
               }
            } else if (!this$billSignBranch.equals(other$billSignBranch)) {
               return false;
            }

            label59: {
               Object this$remark = this.getRemark();
               Object other$remark = other.getRemark();
               if (this$remark == null) {
                  if (other$remark == null) {
                     break label59;
                  }
               } else if (this$remark.equals(other$remark)) {
                  break label59;
               }

               return false;
            }

            Object this$serialNo = this.getSerialNo();
            Object other$serialNo = other.getSerialNo();
            if (this$serialNo == null) {
               if (other$serialNo != null) {
                  return false;
               }
            } else if (!this$serialNo.equals(other$serialNo)) {
               return false;
            }

            Object this$reference = this.getReference();
            Object other$reference = other.getReference();
            if (this$reference == null) {
               if (other$reference != null) {
                  return false;
               }
            } else if (!this$reference.equals(other$reference)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1000023404Out;
   }
}
