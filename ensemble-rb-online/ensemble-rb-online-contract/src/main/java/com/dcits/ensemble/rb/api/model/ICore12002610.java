package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12002610In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12002610Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12002610 {
   String URL = "/rb/nfin/northbound/sign";


   @ApiRemark("北向通签约")
   @ApiDesc("北向通签约")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "2610"
   )
   @BusinessCategory("存款")
   @FunctionCategory("RB05-协议管理")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core12002610Out runService(Core12002610In var1);
}
