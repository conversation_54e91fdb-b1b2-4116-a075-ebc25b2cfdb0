package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;

@MessageOut
public class Core1300106110Out extends EnsResponse {
   private static final long serialVersionUID = 1L;

   public String toString() {
      return "Core1300106110Out()";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1300106110Out)) {
         return false;
      } else {
         Core1300106110Out other = (Core1300106110Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else {
            return super.equals(o);
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1300106110Out;
   }
}
