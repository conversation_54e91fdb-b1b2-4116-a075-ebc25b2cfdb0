package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400100014In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400100014In.Body body;

   public Core1400100014In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400100014In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400100014In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100014In)) {
         return false;
      } else {
         Core1400100014In other = (Core1400100014In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100014In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "原业务编号",
         notNull = true,
         length = "50",
         remark = "原业务编号,如承兑汇票号码,信用证编号等",
         maxSize = 50
      )
      private String extTradeNo;
      @V(
         desc = "账户状态",
         notNull = false,
         length = "1",
         inDesc = "N-新建,H-待激活,A-活动,D-睡眠,S-久悬,O-转营业外,P-逾期,C-关闭,I-预开户,R-预销户",
         remark = "描述账户生命周期不同阶段的划分",
         maxSize = 1
      )
      private String acctStatus;

      public String getExtTradeNo() {
         return this.extTradeNo;
      }

      public String getAcctStatus() {
         return this.acctStatus;
      }

      public void setExtTradeNo(String extTradeNo) {
         this.extTradeNo = extTradeNo;
      }

      public void setAcctStatus(String acctStatus) {
         this.acctStatus = acctStatus;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100014In.Body)) {
            return false;
         } else {
            Core1400100014In.Body other = (Core1400100014In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$extTradeNo = this.getExtTradeNo();
               Object other$extTradeNo = other.getExtTradeNo();
               if (this$extTradeNo == null) {
                  if (other$extTradeNo != null) {
                     return false;
                  }
               } else if (!this$extTradeNo.equals(other$extTradeNo)) {
                  return false;
               }

               Object this$acctStatus = this.getAcctStatus();
               Object other$acctStatus = other.getAcctStatus();
               if (this$acctStatus == null) {
                  if (other$acctStatus != null) {
                     return false;
                  }
               } else if (!this$acctStatus.equals(other$acctStatus)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100014In.Body;
      }
      public String toString() {
         return "Core1400100014In.Body(extTradeNo=" + this.getExtTradeNo() + ", acctStatus=" + this.getAcctStatus() + ")";
      }
   }
}
