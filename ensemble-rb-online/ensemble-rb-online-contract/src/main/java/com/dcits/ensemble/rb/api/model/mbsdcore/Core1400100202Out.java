package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;

@MessageOut
public class Core1400100202Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "总金额",
      notNull = false,
      length = "17",
      remark = "总金额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal totalAmt;
   @V(
      desc = "账户条数",
      notNull = false,
      length = "5",
      remark = "账户条数"
   )
   private Integer acctCount;

   public BigDecimal getTotalAmt() {
      return this.totalAmt;
   }

   public Integer getAcctCount() {
      return this.acctCount;
   }

   public void setTotalAmt(BigDecimal totalAmt) {
      this.totalAmt = totalAmt;
   }

   public void setAcctCount(Integer acctCount) {
      this.acctCount = acctCount;
   }

   public String toString() {
      return "Core1400100202Out(totalAmt=" + this.getTotalAmt() + ", acctCount=" + this.getAcctCount() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100202Out)) {
         return false;
      } else {
         Core1400100202Out other = (Core1400100202Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$totalAmt = this.getTotalAmt();
            Object other$totalAmt = other.getTotalAmt();
            if (this$totalAmt == null) {
               if (other$totalAmt != null) {
                  return false;
               }
            } else if (!this$totalAmt.equals(other$totalAmt)) {
               return false;
            }

            Object this$acctCount = this.getAcctCount();
            Object other$acctCount = other.getAcctCount();
            if (this$acctCount == null) {
               if (other$acctCount != null) {
                  return false;
               }
            } else if (!this$acctCount.equals(other$acctCount)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100202Out;
   }
}
