package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;

@MessageOut
public class Core1200100500Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "转让总对价",
      notNull = false,
      length = "17",
      remark = "转让总对价",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal trfTotalSettleAmt;
   @V(
      desc = "受让人收益率",
      notNull = false,
      length = "15",
      remark = "受让人收益率",
      decimalLength = 8,
      precision = 8
   )
   private BigDecimal beneficiaryProfitRate;
   @V(
      desc = "转入费用",
      notNull = false,
      length = "17",
      remark = "转入费用",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal trfInFeeAmt;
   @V(
      desc = "转出费用",
      notNull = false,
      length = "17",
      remark = "转出费用",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal trfOutFeeAmt;

   public BigDecimal getTrfTotalSettleAmt() {
      return this.trfTotalSettleAmt;
   }

   public BigDecimal getBeneficiaryProfitRate() {
      return this.beneficiaryProfitRate;
   }

   public BigDecimal getTrfInFeeAmt() {
      return this.trfInFeeAmt;
   }

   public BigDecimal getTrfOutFeeAmt() {
      return this.trfOutFeeAmt;
   }

   public void setTrfTotalSettleAmt(BigDecimal trfTotalSettleAmt) {
      this.trfTotalSettleAmt = trfTotalSettleAmt;
   }

   public void setBeneficiaryProfitRate(BigDecimal beneficiaryProfitRate) {
      this.beneficiaryProfitRate = beneficiaryProfitRate;
   }

   public void setTrfInFeeAmt(BigDecimal trfInFeeAmt) {
      this.trfInFeeAmt = trfInFeeAmt;
   }

   public void setTrfOutFeeAmt(BigDecimal trfOutFeeAmt) {
      this.trfOutFeeAmt = trfOutFeeAmt;
   }

   public String toString() {
      return "Core1200100500Out(trfTotalSettleAmt=" + this.getTrfTotalSettleAmt() + ", beneficiaryProfitRate=" + this.getBeneficiaryProfitRate() + ", trfInFeeAmt=" + this.getTrfInFeeAmt() + ", trfOutFeeAmt=" + this.getTrfOutFeeAmt() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200100500Out)) {
         return false;
      } else {
         Core1200100500Out other = (Core1200100500Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            label61: {
               Object this$trfTotalSettleAmt = this.getTrfTotalSettleAmt();
               Object other$trfTotalSettleAmt = other.getTrfTotalSettleAmt();
               if (this$trfTotalSettleAmt == null) {
                  if (other$trfTotalSettleAmt == null) {
                     break label61;
                  }
               } else if (this$trfTotalSettleAmt.equals(other$trfTotalSettleAmt)) {
                  break label61;
               }

               return false;
            }

            label54: {
               Object this$beneficiaryProfitRate = this.getBeneficiaryProfitRate();
               Object other$beneficiaryProfitRate = other.getBeneficiaryProfitRate();
               if (this$beneficiaryProfitRate == null) {
                  if (other$beneficiaryProfitRate == null) {
                     break label54;
                  }
               } else if (this$beneficiaryProfitRate.equals(other$beneficiaryProfitRate)) {
                  break label54;
               }

               return false;
            }

            Object this$trfInFeeAmt = this.getTrfInFeeAmt();
            Object other$trfInFeeAmt = other.getTrfInFeeAmt();
            if (this$trfInFeeAmt == null) {
               if (other$trfInFeeAmt != null) {
                  return false;
               }
            } else if (!this$trfInFeeAmt.equals(other$trfInFeeAmt)) {
               return false;
            }

            Object this$trfOutFeeAmt = this.getTrfOutFeeAmt();
            Object other$trfOutFeeAmt = other.getTrfOutFeeAmt();
            if (this$trfOutFeeAmt == null) {
               if (other$trfOutFeeAmt != null) {
                  return false;
               }
            } else if (!this$trfOutFeeAmt.equals(other$trfOutFeeAmt)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200100500Out;
   }
}
