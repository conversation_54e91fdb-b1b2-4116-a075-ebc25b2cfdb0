package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;

@MessageIn
public class Core1200100712In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1200100712In.Body body;

   public Core1200100712In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1200100712In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1200100712In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200100712In)) {
         return false;
      } else {
         Core1200100712In other = (Core1200100712In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200100712In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "账号/卡号",
         notNull = true,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "审批单号",
         notNull = false,
         length = "50",
         remark = "审批单号",
         maxSize = 50
      )
      private String approvalNo;
      @V(
         desc = "利率类型",
         notNull = false,
         length = "5",
         remark = "利率类型",
         maxSize = 5
      )
      private String intType;
      @V(
         desc = "处理日期",
         notNull = true,
         remark = "处理日期"
      )
      private String dealDate;
      @V(
         desc = "结束日期",
         notNull = true,
         remark = "结束日期"
      )
      private String endDate;
      @V(
         desc = "协议编号",
         notNull = true,
         length = "50",
         remark = "协议编号",
         maxSize = 50
      )
      private String agreementId;
      @V(
         desc = "资金池利率",
         notNull = false,
         length = "15",
         inDesc = "Y-是,N-否",
         remark = "资金池利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal pcpRate;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getApprovalNo() {
         return this.approvalNo;
      }

      public String getIntType() {
         return this.intType;
      }

      public String getDealDate() {
         return this.dealDate;
      }

      public String getEndDate() {
         return this.endDate;
      }

      public String getAgreementId() {
         return this.agreementId;
      }

      public BigDecimal getPcpRate() {
         return this.pcpRate;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setApprovalNo(String approvalNo) {
         this.approvalNo = approvalNo;
      }

      public void setIntType(String intType) {
         this.intType = intType;
      }

      public void setDealDate(String dealDate) {
         this.dealDate = dealDate;
      }

      public void setEndDate(String endDate) {
         this.endDate = endDate;
      }

      public void setAgreementId(String agreementId) {
         this.agreementId = agreementId;
      }

      public void setPcpRate(BigDecimal pcpRate) {
         this.pcpRate = pcpRate;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1200100712In.Body)) {
            return false;
         } else {
            Core1200100712In.Body other = (Core1200100712In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo != null) {
                     return false;
                  }
               } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
                  return false;
               }

               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo != null) {
                     return false;
                  }
               } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               label110: {
                  Object this$acctCcy = this.getAcctCcy();
                  Object other$acctCcy = other.getAcctCcy();
                  if (this$acctCcy == null) {
                     if (other$acctCcy == null) {
                        break label110;
                     }
                  } else if (this$acctCcy.equals(other$acctCcy)) {
                     break label110;
                  }

                  return false;
               }

               label103: {
                  Object this$approvalNo = this.getApprovalNo();
                  Object other$approvalNo = other.getApprovalNo();
                  if (this$approvalNo == null) {
                     if (other$approvalNo == null) {
                        break label103;
                     }
                  } else if (this$approvalNo.equals(other$approvalNo)) {
                     break label103;
                  }

                  return false;
               }

               Object this$intType = this.getIntType();
               Object other$intType = other.getIntType();
               if (this$intType == null) {
                  if (other$intType != null) {
                     return false;
                  }
               } else if (!this$intType.equals(other$intType)) {
                  return false;
               }

               label89: {
                  Object this$dealDate = this.getDealDate();
                  Object other$dealDate = other.getDealDate();
                  if (this$dealDate == null) {
                     if (other$dealDate == null) {
                        break label89;
                     }
                  } else if (this$dealDate.equals(other$dealDate)) {
                     break label89;
                  }

                  return false;
               }

               label82: {
                  Object this$endDate = this.getEndDate();
                  Object other$endDate = other.getEndDate();
                  if (this$endDate == null) {
                     if (other$endDate == null) {
                        break label82;
                     }
                  } else if (this$endDate.equals(other$endDate)) {
                     break label82;
                  }

                  return false;
               }

               Object this$agreementId = this.getAgreementId();
               Object other$agreementId = other.getAgreementId();
               if (this$agreementId == null) {
                  if (other$agreementId != null) {
                     return false;
                  }
               } else if (!this$agreementId.equals(other$agreementId)) {
                  return false;
               }

               Object this$pcpRate = this.getPcpRate();
               Object other$pcpRate = other.getPcpRate();
               if (this$pcpRate == null) {
                  if (other$pcpRate != null) {
                     return false;
                  }
               } else if (!this$pcpRate.equals(other$pcpRate)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1200100712In.Body;
      }
      public String toString() {
         return "Core1200100712In.Body(baseAcctNo=" + this.getBaseAcctNo() + ", acctSeqNo=" + this.getAcctSeqNo() + ", prodType=" + this.getProdType() + ", acctCcy=" + this.getAcctCcy() + ", approvalNo=" + this.getApprovalNo() + ", intType=" + this.getIntType() + ", dealDate=" + this.getDealDate() + ", endDate=" + this.getEndDate() + ", agreementId=" + this.getAgreementId() + ", pcpRate=" + this.getPcpRate() + ")";
      }
   }
}
