package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.util.List;

@MessageIn
public class Core1220090005In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1220090005In.Body body;

   public Core1220090005In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1220090005In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1220090005In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1220090005In)) {
         return false;
      } else {
         Core1220090005In other = (Core1220090005In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1220090005In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "批次类型",
         notNull = true,
         length = "10",
         inDesc = "BATCH1-批量开立客户,BATCH2-批量开立账户,BATCH3-贷款批量开立/发放,BATCH4-批量司法查询,BATCH5-批量转账,BATCH6-批量开立内部账户（贷款贴现批量开立）,BATCH7-贷款批量核销,BATCH8-批量开立内部账户,BATCH9-批量开立存单,BATCH10-黑白名单批量导入,BATCH11-贷款批量发放核销,BATCH12-批量续开内部账,BATCH13-久悬户批量导入,BATCH14-批量冻结,BATCH15-批量解冻扣款,BATCH16-基金资金清算,BATCH17-社保卡批量开户开卡,BATCH18-CDCT对账,BATCH19-理财平台资金清算,BATCH20-日终签约文件同步,BATCH21-批量收取手续费,BATCH22-IC卡商户批量入账,BATCH23-委托关系验证,BATCH24-ic卡对账文件,BATCH25-IC卡挂失销户到期处理文件请求文件,BATCH26-生成指定表文件,BATCH27-批量冲正,BATCH39-批量开立一户通子账户",
         remark = "批次类型",
         maxSize = 10
      )
      private String batchClass;
      @V(
         desc = "客户号",
         notNull = true,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账号/卡号",
         notNull = true,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "数组",
         notNull = false,
         remark = "数组"
      )
      private List<Core1220090005In.Body.WithdrawArray> withdrawArray;
      @V(
         desc = "一户通账户结构模式",
         notNull = true,
         length = "2",
         inDesc = "RV-母实子虚,VR-母虚子实",
         remark = "一户通账户结构模式",
         maxSize = 2
      )
      private String yhtAcctOrgSchema;
      @V(
         desc = "当前账户层级",
         notNull = true,
         length = "2",
         remark = "当前账户层级",
         maxSize = 2
      )
      private String acctLevel;

      public String getBatchClass() {
         return this.batchClass;
      }

      public String getClientNo() {
         return this.clientNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public List<Core1220090005In.Body.WithdrawArray> getWithdrawArray() {
         return this.withdrawArray;
      }

      public String getYhtAcctOrgSchema() {
         return this.yhtAcctOrgSchema;
      }

      public String getAcctLevel() {
         return this.acctLevel;
      }

      public void setBatchClass(String batchClass) {
         this.batchClass = batchClass;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setWithdrawArray(List<Core1220090005In.Body.WithdrawArray> withdrawArray) {
         this.withdrawArray = withdrawArray;
      }

      public void setYhtAcctOrgSchema(String yhtAcctOrgSchema) {
         this.yhtAcctOrgSchema = yhtAcctOrgSchema;
      }

      public void setAcctLevel(String acctLevel) {
         this.acctLevel = acctLevel;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1220090005In.Body)) {
            return false;
         } else {
            Core1220090005In.Body other = (Core1220090005In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label119: {
                  Object this$batchClass = this.getBatchClass();
                  Object other$batchClass = other.getBatchClass();
                  if (this$batchClass == null) {
                     if (other$batchClass == null) {
                        break label119;
                     }
                  } else if (this$batchClass.equals(other$batchClass)) {
                     break label119;
                  }

                  return false;
               }

               Object this$clientNo = this.getClientNo();
               Object other$clientNo = other.getClientNo();
               if (this$clientNo == null) {
                  if (other$clientNo != null) {
                     return false;
                  }
               } else if (!this$clientNo.equals(other$clientNo)) {
                  return false;
               }

               label105: {
                  Object this$prodType = this.getProdType();
                  Object other$prodType = other.getProdType();
                  if (this$prodType == null) {
                     if (other$prodType == null) {
                        break label105;
                     }
                  } else if (this$prodType.equals(other$prodType)) {
                     break label105;
                  }

                  return false;
               }

               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo != null) {
                     return false;
                  }
               } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
                  return false;
               }

               label91: {
                  Object this$acctSeqNo = this.getAcctSeqNo();
                  Object other$acctSeqNo = other.getAcctSeqNo();
                  if (this$acctSeqNo == null) {
                     if (other$acctSeqNo == null) {
                        break label91;
                     }
                  } else if (this$acctSeqNo.equals(other$acctSeqNo)) {
                     break label91;
                  }

                  return false;
               }

               Object this$acctCcy = this.getAcctCcy();
               Object other$acctCcy = other.getAcctCcy();
               if (this$acctCcy == null) {
                  if (other$acctCcy != null) {
                     return false;
                  }
               } else if (!this$acctCcy.equals(other$acctCcy)) {
                  return false;
               }

               label77: {
                  Object this$withdrawArray = this.getWithdrawArray();
                  Object other$withdrawArray = other.getWithdrawArray();
                  if (this$withdrawArray == null) {
                     if (other$withdrawArray == null) {
                        break label77;
                     }
                  } else if (this$withdrawArray.equals(other$withdrawArray)) {
                     break label77;
                  }

                  return false;
               }

               label70: {
                  Object this$yhtAcctOrgSchema = this.getYhtAcctOrgSchema();
                  Object other$yhtAcctOrgSchema = other.getYhtAcctOrgSchema();
                  if (this$yhtAcctOrgSchema == null) {
                     if (other$yhtAcctOrgSchema == null) {
                        break label70;
                     }
                  } else if (this$yhtAcctOrgSchema.equals(other$yhtAcctOrgSchema)) {
                     break label70;
                  }

                  return false;
               }

               Object this$acctLevel = this.getAcctLevel();
               Object other$acctLevel = other.getAcctLevel();
               if (this$acctLevel == null) {
                  if (other$acctLevel != null) {
                     return false;
                  }
               } else if (!this$acctLevel.equals(other$acctLevel)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1220090005In.Body;
      }
      public String toString() {
         return "Core1220090005In.Body(batchClass=" + this.getBatchClass() + ", clientNo=" + this.getClientNo() + ", prodType=" + this.getProdType() + ", baseAcctNo=" + this.getBaseAcctNo() + ", acctSeqNo=" + this.getAcctSeqNo() + ", acctCcy=" + this.getAcctCcy() + ", withdrawArray=" + this.getWithdrawArray() + ", yhtAcctOrgSchema=" + this.getYhtAcctOrgSchema() + ", acctLevel=" + this.getAcctLevel() + ")";
      }

      public static class WithdrawArray {
         @V(
            desc = "支取方式",
            notNull = false,
            length = "1",
            inDesc = "S-凭印鉴支取,P-凭密码支取,W-无密码无印鉴支取,B-凭印鉴和密码支取,O-凭证件支取,R-支付密码器和印鉴",
            remark = "支取方式",
            maxSize = 1
         )
         private String withdrawalType;
         @V(
            desc = "渠道集合",
            notNull = false,
            length = "500",
            inDesc = "MT-柜面业务,MC-卡柜面业务,AC-本行自助,DP-大额支付,MP-小额支付,SF-SWIFT,CB-网上银行,PB-电话银行,CC-CALL CENTER,MB-短信银行,UC-银联交易,PC-卡支付业务,BC-柜面发起卡中间业务,ND-票据系统发起的业务,AB-自助终端系统发起的中间业务,BH-中间业务,UT-银联本代他业务,BI-批量接口业务,IF-一般文件接口业务,NI-内部业务处理,IG-内部清算,CS-财税库行业务,SR-(Security)安全加密渠道,GL-总账系统业务",
            remark = "渠道集合,多个渠道之间用逗号(,)分隔,若是所有渠道,则用ALL表示",
            maxSize = 500
         )
         private String channelMuster;

         public String getWithdrawalType() {
            return this.withdrawalType;
         }

         public String getChannelMuster() {
            return this.channelMuster;
         }

         public void setWithdrawalType(String withdrawalType) {
            this.withdrawalType = withdrawalType;
         }

         public void setChannelMuster(String channelMuster) {
            this.channelMuster = channelMuster;
         }

         public boolean equals(Object o) {
            if (o == this) {
               return true;
            } else if (!(o instanceof Core1220090005In.Body.WithdrawArray)) {
               return false;
            } else {
               Core1220090005In.Body.WithdrawArray other = (Core1220090005In.Body.WithdrawArray)o;
               if (!other.canEqual(this)) {
                  return false;
               } else {
                  Object this$withdrawalType = this.getWithdrawalType();
                  Object other$withdrawalType = other.getWithdrawalType();
                  if (this$withdrawalType == null) {
                     if (other$withdrawalType != null) {
                        return false;
                     }
                  } else if (!this$withdrawalType.equals(other$withdrawalType)) {
                     return false;
                  }

                  Object this$channelMuster = this.getChannelMuster();
                  Object other$channelMuster = other.getChannelMuster();
                  if (this$channelMuster == null) {
                     if (other$channelMuster != null) {
                        return false;
                     }
                  } else if (!this$channelMuster.equals(other$channelMuster)) {
                     return false;
                  }

                  return true;
               }
            }
         }

         protected boolean canEqual(Object other) {
            return other instanceof Core1220090005In.Body.WithdrawArray;
         }
         public String toString() {
            return "Core1220090005In.Body.WithdrawArray(withdrawalType=" + this.getWithdrawalType() + ", channelMuster=" + this.getChannelMuster() + ")";
         }
      }
   }
}
