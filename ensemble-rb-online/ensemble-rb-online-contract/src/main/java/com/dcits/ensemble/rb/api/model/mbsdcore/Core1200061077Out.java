package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;

@MessageOut
public class Core1200061077Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "交易参考号",
      notNull = false,
      length = "50",
      remark = "交易参考号",
      maxSize = 50
   )
   private String reference;
   @V(
      desc = "追缴保证金冻结编号",
      notNull = false,
      length = "200",
      remark = "追缴保证金冻结编号",
      maxSize = 200
   )
   private String depBlockNo;

   public String getReference() {
      return this.reference;
   }

   public String getDepBlockNo() {
      return this.depBlockNo;
   }

   public void setReference(String reference) {
      this.reference = reference;
   }

   public void setDepBlockNo(String depBlockNo) {
      this.depBlockNo = depBlockNo;
   }

   public String toString() {
      return "Core1200061077Out(reference=" + this.getReference() + ", depBlockNo=" + this.getDepBlockNo() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200061077Out)) {
         return false;
      } else {
         Core1200061077Out other = (Core1200061077Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$reference = this.getReference();
            Object other$reference = other.getReference();
            if (this$reference == null) {
               if (other$reference != null) {
                  return false;
               }
            } else if (!this$reference.equals(other$reference)) {
               return false;
            }

            Object this$depBlockNo = this.getDepBlockNo();
            Object other$depBlockNo = other.getDepBlockNo();
            if (this$depBlockNo == null) {
               if (other$depBlockNo != null) {
                  return false;
               }
            } else if (!this$depBlockNo.equals(other$depBlockNo)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200061077Out;
   }
}
