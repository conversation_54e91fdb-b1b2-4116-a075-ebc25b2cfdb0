package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core13000004In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core13000004Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore13000004 {
   String URL = "/rb/rev/tran/dc";


   @ApiDesc("提供大额存单开立和支取业务的冲正")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1300",
      messageCode = "0004"
   )
   @BusinessCategory("1300")
   @FunctionCategory("RB10-大额存单")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core13000004Out runService(Core13000004In var1);
}
