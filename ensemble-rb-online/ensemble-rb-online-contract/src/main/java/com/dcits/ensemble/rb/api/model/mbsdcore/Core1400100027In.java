package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400100027In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400100027In.Body body;

   public Core1400100027In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400100027In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400100027In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100027In)) {
         return false;
      } else {
         Core1400100027In other = (Core1400100027In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100027In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "审批单号",
         notNull = false,
         length = "50",
         remark = "审批单号",
         maxSize = 50
      )
      private String approvalNo;

      public String getClientNo() {
         return this.clientNo;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getApprovalNo() {
         return this.approvalNo;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setApprovalNo(String approvalNo) {
         this.approvalNo = approvalNo;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100027In.Body)) {
            return false;
         } else {
            Core1400100027In.Body other = (Core1400100027In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label47: {
                  Object this$clientNo = this.getClientNo();
                  Object other$clientNo = other.getClientNo();
                  if (this$clientNo == null) {
                     if (other$clientNo == null) {
                        break label47;
                     }
                  } else if (this$clientNo.equals(other$clientNo)) {
                     break label47;
                  }

                  return false;
               }

               Object this$acctCcy = this.getAcctCcy();
               Object other$acctCcy = other.getAcctCcy();
               if (this$acctCcy == null) {
                  if (other$acctCcy != null) {
                     return false;
                  }
               } else if (!this$acctCcy.equals(other$acctCcy)) {
                  return false;
               }

               Object this$approvalNo = this.getApprovalNo();
               Object other$approvalNo = other.getApprovalNo();
               if (this$approvalNo == null) {
                  if (other$approvalNo != null) {
                     return false;
                  }
               } else if (!this$approvalNo.equals(other$approvalNo)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100027In.Body;
      }
      public String toString() {
         return "Core1400100027In.Body(clientNo=" + this.getClientNo() + ", acctCcy=" + this.getAcctCcy() + ", approvalNo=" + this.getApprovalNo() + ")";
      }
   }
}
