package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14003211In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14003211Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14003211 {
   String URL = "/rb/inq/avg/agreement/";


   @ApiDesc("日均余额靠档协议信息查询")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "3211"
   )
   @BusinessCategory("1400-查询")
   @FunctionCategory("RB05-协议管理")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core14003211Out runService(Core14003211In var1);
}
