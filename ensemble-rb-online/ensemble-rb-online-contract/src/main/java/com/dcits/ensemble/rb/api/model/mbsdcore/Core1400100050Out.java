package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400100050Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100050Out.AgreementArray> agreementArray;

   public List<Core1400100050Out.AgreementArray> getAgreementArray() {
      return this.agreementArray;
   }

   public void setAgreementArray(List<Core1400100050Out.AgreementArray> agreementArray) {
      this.agreementArray = agreementArray;
   }

   public String toString() {
      return "Core1400100050Out(agreementArray=" + this.getAgreementArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100050Out)) {
         return false;
      } else {
         Core1400100050Out other = (Core1400100050Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$agreementArray = this.getAgreementArray();
            Object other$agreementArray = other.getAgreementArray();
            if (this$agreementArray == null) {
               if (other$agreementArray != null) {
                  return false;
               }
            } else if (!this$agreementArray.equals(other$agreementArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100050Out;
   }
   public static class AgreementArray {
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "协议编号",
         notNull = false,
         length = "50",
         remark = "协议编号",
         maxSize = 50
      )
      private String agreementId;
      @V(
         desc = "协议利率",
         notNull = false,
         length = "15",
         remark = "协议利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal agreeIntRate;
      @V(
         desc = "超档利率",
         notNull = false,
         length = "15",
         remark = "超过协议金额部分的金额计息利率。比如：协议金额50000，账户余额为55000超过50000 的部分5000，则按照此利率计算利息",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal overGradeRate;
      @V(
         desc = "违约利率",
         notNull = false,
         length = "15",
         remark = "违约利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal pastFadRate;
      @V(
         desc = "结息频率",
         notNull = false,
         length = "5",
         remark = "结息频率",
         maxSize = 5
      )
      private String cycleFreq;
      @V(
         desc = "渠道",
         notNull = false,
         length = "10",
         remark = "渠道细类",
         maxSize = 10
      )
      private String channel;
      @V(
         desc = "数组",
         notNull = false,
         remark = "数组"
      )
      private List<Core1400100050Out.AgreementArray.SubArray> subArray;
      @V(
         desc = "开始日期",
         notNull = false,
         remark = "开始日期"
      )
      private String startDate;
      @V(
         desc = "结束日期",
         notNull = false,
         remark = "结束日期"
      )
      private String endDate;
      @V(
         desc = "主、分账户类型标志",
         notNull = false,
         length = "1",
         remark = "主、分账户类型标志",
         maxSize = 1
      )
      private String mainFlag;
      @V(
         desc = "外围系统协议编号",
         notNull = false,
         length = "50",
         remark = "外围系统协议编号",
         maxSize = 50
      )
      private String signId;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getAgreementId() {
         return this.agreementId;
      }

      public BigDecimal getAgreeIntRate() {
         return this.agreeIntRate;
      }

      public BigDecimal getOverGradeRate() {
         return this.overGradeRate;
      }

      public BigDecimal getPastFadRate() {
         return this.pastFadRate;
      }

      public String getCycleFreq() {
         return this.cycleFreq;
      }

      public String getChannel() {
         return this.channel;
      }

      public List<Core1400100050Out.AgreementArray.SubArray> getSubArray() {
         return this.subArray;
      }

      public String getStartDate() {
         return this.startDate;
      }

      public String getEndDate() {
         return this.endDate;
      }

      public String getMainFlag() {
         return this.mainFlag;
      }

      public String getSignId() {
         return this.signId;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setAgreementId(String agreementId) {
         this.agreementId = agreementId;
      }

      public void setAgreeIntRate(BigDecimal agreeIntRate) {
         this.agreeIntRate = agreeIntRate;
      }

      public void setOverGradeRate(BigDecimal overGradeRate) {
         this.overGradeRate = overGradeRate;
      }

      public void setPastFadRate(BigDecimal pastFadRate) {
         this.pastFadRate = pastFadRate;
      }

      public void setCycleFreq(String cycleFreq) {
         this.cycleFreq = cycleFreq;
      }

      public void setChannel(String channel) {
         this.channel = channel;
      }

      public void setSubArray(List<Core1400100050Out.AgreementArray.SubArray> subArray) {
         this.subArray = subArray;
      }

      public void setStartDate(String startDate) {
         this.startDate = startDate;
      }

      public void setEndDate(String endDate) {
         this.endDate = endDate;
      }

      public void setMainFlag(String mainFlag) {
         this.mainFlag = mainFlag;
      }

      public void setSignId(String signId) {
         this.signId = signId;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100050Out.AgreementArray)) {
            return false;
         } else {
            Core1400100050Out.AgreementArray other = (Core1400100050Out.AgreementArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label191: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label191;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label191;
                  }

                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo != null) {
                     return false;
                  }
               } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                  return false;
               }

               label170: {
                  Object this$acctCcy = this.getAcctCcy();
                  Object other$acctCcy = other.getAcctCcy();
                  if (this$acctCcy == null) {
                     if (other$acctCcy == null) {
                        break label170;
                     }
                  } else if (this$acctCcy.equals(other$acctCcy)) {
                     break label170;
                  }

                  return false;
               }

               label163: {
                  Object this$agreementId = this.getAgreementId();
                  Object other$agreementId = other.getAgreementId();
                  if (this$agreementId == null) {
                     if (other$agreementId == null) {
                        break label163;
                     }
                  } else if (this$agreementId.equals(other$agreementId)) {
                     break label163;
                  }

                  return false;
               }

               Object this$agreeIntRate = this.getAgreeIntRate();
               Object other$agreeIntRate = other.getAgreeIntRate();
               if (this$agreeIntRate == null) {
                  if (other$agreeIntRate != null) {
                     return false;
                  }
               } else if (!this$agreeIntRate.equals(other$agreeIntRate)) {
                  return false;
               }

               Object this$overGradeRate = this.getOverGradeRate();
               Object other$overGradeRate = other.getOverGradeRate();
               if (this$overGradeRate == null) {
                  if (other$overGradeRate != null) {
                     return false;
                  }
               } else if (!this$overGradeRate.equals(other$overGradeRate)) {
                  return false;
               }

               label142: {
                  Object this$pastFadRate = this.getPastFadRate();
                  Object other$pastFadRate = other.getPastFadRate();
                  if (this$pastFadRate == null) {
                     if (other$pastFadRate == null) {
                        break label142;
                     }
                  } else if (this$pastFadRate.equals(other$pastFadRate)) {
                     break label142;
                  }

                  return false;
               }

               label135: {
                  Object this$cycleFreq = this.getCycleFreq();
                  Object other$cycleFreq = other.getCycleFreq();
                  if (this$cycleFreq == null) {
                     if (other$cycleFreq == null) {
                        break label135;
                     }
                  } else if (this$cycleFreq.equals(other$cycleFreq)) {
                     break label135;
                  }

                  return false;
               }

               Object this$channel = this.getChannel();
               Object other$channel = other.getChannel();
               if (this$channel == null) {
                  if (other$channel != null) {
                     return false;
                  }
               } else if (!this$channel.equals(other$channel)) {
                  return false;
               }

               label121: {
                  Object this$subArray = this.getSubArray();
                  Object other$subArray = other.getSubArray();
                  if (this$subArray == null) {
                     if (other$subArray == null) {
                        break label121;
                     }
                  } else if (this$subArray.equals(other$subArray)) {
                     break label121;
                  }

                  return false;
               }

               Object this$startDate = this.getStartDate();
               Object other$startDate = other.getStartDate();
               if (this$startDate == null) {
                  if (other$startDate != null) {
                     return false;
                  }
               } else if (!this$startDate.equals(other$startDate)) {
                  return false;
               }

               label107: {
                  Object this$endDate = this.getEndDate();
                  Object other$endDate = other.getEndDate();
                  if (this$endDate == null) {
                     if (other$endDate == null) {
                        break label107;
                     }
                  } else if (this$endDate.equals(other$endDate)) {
                     break label107;
                  }

                  return false;
               }

               Object this$mainFlag = this.getMainFlag();
               Object other$mainFlag = other.getMainFlag();
               if (this$mainFlag == null) {
                  if (other$mainFlag != null) {
                     return false;
                  }
               } else if (!this$mainFlag.equals(other$mainFlag)) {
                  return false;
               }

               Object this$signId = this.getSignId();
               Object other$signId = other.getSignId();
               if (this$signId == null) {
                  if (other$signId != null) {
                     return false;
                  }
               } else if (!this$signId.equals(other$signId)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100050Out.AgreementArray;
      }
      public String toString() {
         return "Core1400100050Out.AgreementArray(baseAcctNo=" + this.getBaseAcctNo() + ", prodType=" + this.getProdType() + ", acctSeqNo=" + this.getAcctSeqNo() + ", acctCcy=" + this.getAcctCcy() + ", agreementId=" + this.getAgreementId() + ", agreeIntRate=" + this.getAgreeIntRate() + ", overGradeRate=" + this.getOverGradeRate() + ", pastFadRate=" + this.getPastFadRate() + ", cycleFreq=" + this.getCycleFreq() + ", channel=" + this.getChannel() + ", subArray=" + this.getSubArray() + ", startDate=" + this.getStartDate() + ", endDate=" + this.getEndDate() + ", mainFlag=" + this.getMainFlag() + ", signId=" + this.getSignId() + ")";
      }

      public static class SubArray {
         @V(
            desc = "账号/卡号",
            notNull = false,
            length = "50",
            remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
            maxSize = 50
         )
         private String baseAcctNo;
         @V(
            desc = "产品类型",
            notNull = false,
            length = "20",
            remark = "产品类型",
            maxSize = 20
         )
         private String prodType;
         @V(
            desc = "账户序号",
            notNull = false,
            length = "5",
            remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
            maxSize = 5
         )
         private String acctSeqNo;
         @V(
            desc = "账户币种",
            notNull = false,
            length = "3",
            remark = "账户币种 对于AIO账户和一本通账户",
            maxSize = 3
         )
         private String acctCcy;
         @V(
            desc = "协议编号",
            notNull = false,
            length = "50",
            remark = "协议编号",
            maxSize = 50
         )
         private String agreementId;
         @V(
            desc = "协议利率",
            notNull = false,
            length = "15",
            remark = "协议利率",
            decimalLength = 8,
            precision = 8
         )
         private BigDecimal agreeIntRate;
         @V(
            desc = "超档利率",
            notNull = false,
            length = "15",
            remark = "超过协议金额部分的金额计息利率。比如：协议金额50000，账户余额为55000超过50000 的部分5000，则按照此利率计算利息",
            decimalLength = 8,
            precision = 8
         )
         private BigDecimal overGradeRate;
         @V(
            desc = "违约利率",
            notNull = false,
            length = "15",
            remark = "违约利率",
            decimalLength = 8,
            precision = 8
         )
         private BigDecimal pastFadRate;
         @V(
            desc = "结息频率",
            notNull = false,
            length = "5",
            remark = "结息频率",
            maxSize = 5
         )
         private String cycleFreq;
         @V(
            desc = "渠道",
            notNull = false,
            length = "10",
            remark = "渠道细类",
            maxSize = 10
         )
         private String channel;
         @V(
            desc = "开始日期",
            notNull = false,
            remark = "开始日期"
         )
         private String startDate;
         @V(
            desc = "结束日期",
            notNull = false,
            remark = "结束日期"
         )
         private String endDate;
         @V(
            desc = "主、分账户类型标志",
            notNull = false,
            length = "1",
            remark = "主、分账户类型标志",
            maxSize = 1
         )
         private String mainFlag;
         @V(
            desc = "外围系统协议编号",
            notNull = false,
            length = "50",
            remark = "外围系统协议编号",
            maxSize = 50
         )
         private String signId;

         public String getBaseAcctNo() {
            return this.baseAcctNo;
         }

         public String getProdType() {
            return this.prodType;
         }

         public String getAcctSeqNo() {
            return this.acctSeqNo;
         }

         public String getAcctCcy() {
            return this.acctCcy;
         }

         public String getAgreementId() {
            return this.agreementId;
         }

         public BigDecimal getAgreeIntRate() {
            return this.agreeIntRate;
         }

         public BigDecimal getOverGradeRate() {
            return this.overGradeRate;
         }

         public BigDecimal getPastFadRate() {
            return this.pastFadRate;
         }

         public String getCycleFreq() {
            return this.cycleFreq;
         }

         public String getChannel() {
            return this.channel;
         }

         public String getStartDate() {
            return this.startDate;
         }

         public String getEndDate() {
            return this.endDate;
         }

         public String getMainFlag() {
            return this.mainFlag;
         }

         public String getSignId() {
            return this.signId;
         }

         public void setBaseAcctNo(String baseAcctNo) {
            this.baseAcctNo = baseAcctNo;
         }

         public void setProdType(String prodType) {
            this.prodType = prodType;
         }

         public void setAcctSeqNo(String acctSeqNo) {
            this.acctSeqNo = acctSeqNo;
         }

         public void setAcctCcy(String acctCcy) {
            this.acctCcy = acctCcy;
         }

         public void setAgreementId(String agreementId) {
            this.agreementId = agreementId;
         }

         public void setAgreeIntRate(BigDecimal agreeIntRate) {
            this.agreeIntRate = agreeIntRate;
         }

         public void setOverGradeRate(BigDecimal overGradeRate) {
            this.overGradeRate = overGradeRate;
         }

         public void setPastFadRate(BigDecimal pastFadRate) {
            this.pastFadRate = pastFadRate;
         }

         public void setCycleFreq(String cycleFreq) {
            this.cycleFreq = cycleFreq;
         }

         public void setChannel(String channel) {
            this.channel = channel;
         }

         public void setStartDate(String startDate) {
            this.startDate = startDate;
         }

         public void setEndDate(String endDate) {
            this.endDate = endDate;
         }

         public void setMainFlag(String mainFlag) {
            this.mainFlag = mainFlag;
         }

         public void setSignId(String signId) {
            this.signId = signId;
         }

         public boolean equals(Object o) {
            if (o == this) {
               return true;
            } else if (!(o instanceof Core1400100050Out.AgreementArray.SubArray)) {
               return false;
            } else {
               Core1400100050Out.AgreementArray.SubArray other = (Core1400100050Out.AgreementArray.SubArray)o;
               if (!other.canEqual(this)) {
                  return false;
               } else {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo != null) {
                        return false;
                     }
                  } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
                     return false;
                  }

                  Object this$prodType = this.getProdType();
                  Object other$prodType = other.getProdType();
                  if (this$prodType == null) {
                     if (other$prodType != null) {
                        return false;
                     }
                  } else if (!this$prodType.equals(other$prodType)) {
                     return false;
                  }

                  Object this$acctSeqNo = this.getAcctSeqNo();
                  Object other$acctSeqNo = other.getAcctSeqNo();
                  if (this$acctSeqNo == null) {
                     if (other$acctSeqNo != null) {
                        return false;
                     }
                  } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                     return false;
                  }

                  label158: {
                     Object this$acctCcy = this.getAcctCcy();
                     Object other$acctCcy = other.getAcctCcy();
                     if (this$acctCcy == null) {
                        if (other$acctCcy == null) {
                           break label158;
                        }
                     } else if (this$acctCcy.equals(other$acctCcy)) {
                        break label158;
                     }

                     return false;
                  }

                  label151: {
                     Object this$agreementId = this.getAgreementId();
                     Object other$agreementId = other.getAgreementId();
                     if (this$agreementId == null) {
                        if (other$agreementId == null) {
                           break label151;
                        }
                     } else if (this$agreementId.equals(other$agreementId)) {
                        break label151;
                     }

                     return false;
                  }

                  Object this$agreeIntRate = this.getAgreeIntRate();
                  Object other$agreeIntRate = other.getAgreeIntRate();
                  if (this$agreeIntRate == null) {
                     if (other$agreeIntRate != null) {
                        return false;
                     }
                  } else if (!this$agreeIntRate.equals(other$agreeIntRate)) {
                     return false;
                  }

                  label137: {
                     Object this$overGradeRate = this.getOverGradeRate();
                     Object other$overGradeRate = other.getOverGradeRate();
                     if (this$overGradeRate == null) {
                        if (other$overGradeRate == null) {
                           break label137;
                        }
                     } else if (this$overGradeRate.equals(other$overGradeRate)) {
                        break label137;
                     }

                     return false;
                  }

                  label130: {
                     Object this$pastFadRate = this.getPastFadRate();
                     Object other$pastFadRate = other.getPastFadRate();
                     if (this$pastFadRate == null) {
                        if (other$pastFadRate == null) {
                           break label130;
                        }
                     } else if (this$pastFadRate.equals(other$pastFadRate)) {
                        break label130;
                     }

                     return false;
                  }

                  Object this$cycleFreq = this.getCycleFreq();
                  Object other$cycleFreq = other.getCycleFreq();
                  if (this$cycleFreq == null) {
                     if (other$cycleFreq != null) {
                        return false;
                     }
                  } else if (!this$cycleFreq.equals(other$cycleFreq)) {
                     return false;
                  }

                  Object this$channel = this.getChannel();
                  Object other$channel = other.getChannel();
                  if (this$channel == null) {
                     if (other$channel != null) {
                        return false;
                     }
                  } else if (!this$channel.equals(other$channel)) {
                     return false;
                  }

                  label109: {
                     Object this$startDate = this.getStartDate();
                     Object other$startDate = other.getStartDate();
                     if (this$startDate == null) {
                        if (other$startDate == null) {
                           break label109;
                        }
                     } else if (this$startDate.equals(other$startDate)) {
                        break label109;
                     }

                     return false;
                  }

                  label102: {
                     Object this$endDate = this.getEndDate();
                     Object other$endDate = other.getEndDate();
                     if (this$endDate == null) {
                        if (other$endDate == null) {
                           break label102;
                        }
                     } else if (this$endDate.equals(other$endDate)) {
                        break label102;
                     }

                     return false;
                  }

                  Object this$mainFlag = this.getMainFlag();
                  Object other$mainFlag = other.getMainFlag();
                  if (this$mainFlag == null) {
                     if (other$mainFlag != null) {
                        return false;
                     }
                  } else if (!this$mainFlag.equals(other$mainFlag)) {
                     return false;
                  }

                  Object this$signId = this.getSignId();
                  Object other$signId = other.getSignId();
                  if (this$signId == null) {
                     if (other$signId != null) {
                        return false;
                     }
                  } else if (!this$signId.equals(other$signId)) {
                     return false;
                  }

                  return true;
               }
            }
         }

         protected boolean canEqual(Object other) {
            return other instanceof Core1400100050Out.AgreementArray.SubArray;
         }
         public String toString() {
            return "Core1400100050Out.AgreementArray.SubArray(baseAcctNo=" + this.getBaseAcctNo() + ", prodType=" + this.getProdType() + ", acctSeqNo=" + this.getAcctSeqNo() + ", acctCcy=" + this.getAcctCcy() + ", agreementId=" + this.getAgreementId() + ", agreeIntRate=" + this.getAgreeIntRate() + ", overGradeRate=" + this.getOverGradeRate() + ", pastFadRate=" + this.getPastFadRate() + ", cycleFreq=" + this.getCycleFreq() + ", channel=" + this.getChannel() + ", startDate=" + this.getStartDate() + ", endDate=" + this.getEndDate() + ", mainFlag=" + this.getMainFlag() + ", signId=" + this.getSignId() + ")";
         }
      }
   }
}
