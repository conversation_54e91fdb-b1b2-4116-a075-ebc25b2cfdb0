package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400100711Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100711Out.ResultArray> resultArray;

   public List<Core1400100711Out.ResultArray> getResultArray() {
      return this.resultArray;
   }

   public void setResultArray(List<Core1400100711Out.ResultArray> resultArray) {
      this.resultArray = resultArray;
   }

   public String toString() {
      return "Core1400100711Out(resultArray=" + this.getResultArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100711Out)) {
         return false;
      } else {
         Core1400100711Out other = (Core1400100711Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$resultArray = this.getResultArray();
            Object other$resultArray = other.getResultArray();
            if (this$resultArray == null) {
               if (other$resultArray != null) {
                  return false;
               }
            } else if (!this$resultArray.equals(other$resultArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100711Out;
   }
   public static class ResultArray {
      @V(
         desc = "账户升降级表示符",
         notNull = false,
         length = "10",
         remark = "账户升降级表示符",
         maxSize = 10
      )
      private String upDownType;
      @V(
         desc = "交易日期",
         notNull = false,
         remark = "交易日期"
      )
      private String tranDate;
      @V(
         desc = "上级账户（组主账户）",
         notNull = false,
         length = "50",
         remark = "上级账户（组主账户）",
         maxSize = 50
      )
      private String upperBaseAcctNo;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "交易参考号",
         notNull = false,
         length = "50",
         remark = "交易参考号",
         maxSize = 50
      )
      private String reference;
      @V(
         desc = "总金额",
         notNull = false,
         length = "17",
         remark = "总金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal totalAmt;
      @V(
         desc = "数组",
         notNull = false,
         remark = "数组"
      )
      private List<Core1400100711Out.ResultArray.SubArray> subArray;

      public String getUpDownType() {
         return this.upDownType;
      }

      public String getTranDate() {
         return this.tranDate;
      }

      public String getUpperBaseAcctNo() {
         return this.upperBaseAcctNo;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getReference() {
         return this.reference;
      }

      public BigDecimal getTotalAmt() {
         return this.totalAmt;
      }

      public List<Core1400100711Out.ResultArray.SubArray> getSubArray() {
         return this.subArray;
      }

      public void setUpDownType(String upDownType) {
         this.upDownType = upDownType;
      }

      public void setTranDate(String tranDate) {
         this.tranDate = tranDate;
      }

      public void setUpperBaseAcctNo(String upperBaseAcctNo) {
         this.upperBaseAcctNo = upperBaseAcctNo;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setReference(String reference) {
         this.reference = reference;
      }

      public void setTotalAmt(BigDecimal totalAmt) {
         this.totalAmt = totalAmt;
      }

      public void setSubArray(List<Core1400100711Out.ResultArray.SubArray> subArray) {
         this.subArray = subArray;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100711Out.ResultArray)) {
            return false;
         } else {
            Core1400100711Out.ResultArray other = (Core1400100711Out.ResultArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label119: {
                  Object this$upDownType = this.getUpDownType();
                  Object other$upDownType = other.getUpDownType();
                  if (this$upDownType == null) {
                     if (other$upDownType == null) {
                        break label119;
                     }
                  } else if (this$upDownType.equals(other$upDownType)) {
                     break label119;
                  }

                  return false;
               }

               Object this$tranDate = this.getTranDate();
               Object other$tranDate = other.getTranDate();
               if (this$tranDate == null) {
                  if (other$tranDate != null) {
                     return false;
                  }
               } else if (!this$tranDate.equals(other$tranDate)) {
                  return false;
               }

               label105: {
                  Object this$upperBaseAcctNo = this.getUpperBaseAcctNo();
                  Object other$upperBaseAcctNo = other.getUpperBaseAcctNo();
                  if (this$upperBaseAcctNo == null) {
                     if (other$upperBaseAcctNo == null) {
                        break label105;
                     }
                  } else if (this$upperBaseAcctNo.equals(other$upperBaseAcctNo)) {
                     break label105;
                  }

                  return false;
               }

               Object this$acctCcy = this.getAcctCcy();
               Object other$acctCcy = other.getAcctCcy();
               if (this$acctCcy == null) {
                  if (other$acctCcy != null) {
                     return false;
                  }
               } else if (!this$acctCcy.equals(other$acctCcy)) {
                  return false;
               }

               label91: {
                  Object this$prodType = this.getProdType();
                  Object other$prodType = other.getProdType();
                  if (this$prodType == null) {
                     if (other$prodType == null) {
                        break label91;
                     }
                  } else if (this$prodType.equals(other$prodType)) {
                     break label91;
                  }

                  return false;
               }

               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo != null) {
                     return false;
                  }
               } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                  return false;
               }

               label77: {
                  Object this$reference = this.getReference();
                  Object other$reference = other.getReference();
                  if (this$reference == null) {
                     if (other$reference == null) {
                        break label77;
                     }
                  } else if (this$reference.equals(other$reference)) {
                     break label77;
                  }

                  return false;
               }

               label70: {
                  Object this$totalAmt = this.getTotalAmt();
                  Object other$totalAmt = other.getTotalAmt();
                  if (this$totalAmt == null) {
                     if (other$totalAmt == null) {
                        break label70;
                     }
                  } else if (this$totalAmt.equals(other$totalAmt)) {
                     break label70;
                  }

                  return false;
               }

               Object this$subArray = this.getSubArray();
               Object other$subArray = other.getSubArray();
               if (this$subArray == null) {
                  if (other$subArray != null) {
                     return false;
                  }
               } else if (!this$subArray.equals(other$subArray)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100711Out.ResultArray;
      }
      public String toString() {
         return "Core1400100711Out.ResultArray(upDownType=" + this.getUpDownType() + ", tranDate=" + this.getTranDate() + ", upperBaseAcctNo=" + this.getUpperBaseAcctNo() + ", acctCcy=" + this.getAcctCcy() + ", prodType=" + this.getProdType() + ", acctSeqNo=" + this.getAcctSeqNo() + ", reference=" + this.getReference() + ", totalAmt=" + this.getTotalAmt() + ", subArray=" + this.getSubArray() + ")";
      }

      public static class SubArray {
         @V(
            desc = "账号/卡号",
            notNull = false,
            length = "50",
            remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
            maxSize = 50
         )
         private String baseAcctNo;
         @V(
            desc = "账户序号",
            notNull = false,
            length = "5",
            remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
            maxSize = 5
         )
         private String acctSeqNo;
         @V(
            desc = "账户币种",
            notNull = false,
            length = "3",
            remark = "账户币种 对于AIO账户和一本通账户",
            maxSize = 3
         )
         private String acctCcy;
         @V(
            desc = "批次处理状态",
            notNull = false,
            length = "1",
            remark = "批次处理状态",
            maxSize = 1
         )
         private String batchStatus;
         @V(
            desc = "失败原因",
            notNull = false,
            length = "200",
            remark = "失败原因",
            maxSize = 200
         )
         private String failureReason;
         @V(
            desc = "交易参考号",
            notNull = false,
            length = "50",
            remark = "交易参考号",
            maxSize = 50
         )
         private String reference;
         @V(
            desc = "处理金额",
            notNull = false,
            length = "17",
            remark = "处理金额",
            decimalLength = 2,
            precision = 2
         )
         private BigDecimal dealAmt;

         public String getBaseAcctNo() {
            return this.baseAcctNo;
         }

         public String getAcctSeqNo() {
            return this.acctSeqNo;
         }

         public String getAcctCcy() {
            return this.acctCcy;
         }

         public String getBatchStatus() {
            return this.batchStatus;
         }

         public String getFailureReason() {
            return this.failureReason;
         }

         public String getReference() {
            return this.reference;
         }

         public BigDecimal getDealAmt() {
            return this.dealAmt;
         }

         public void setBaseAcctNo(String baseAcctNo) {
            this.baseAcctNo = baseAcctNo;
         }

         public void setAcctSeqNo(String acctSeqNo) {
            this.acctSeqNo = acctSeqNo;
         }

         public void setAcctCcy(String acctCcy) {
            this.acctCcy = acctCcy;
         }

         public void setBatchStatus(String batchStatus) {
            this.batchStatus = batchStatus;
         }

         public void setFailureReason(String failureReason) {
            this.failureReason = failureReason;
         }

         public void setReference(String reference) {
            this.reference = reference;
         }

         public void setDealAmt(BigDecimal dealAmt) {
            this.dealAmt = dealAmt;
         }

         public boolean equals(Object o) {
            if (o == this) {
               return true;
            } else if (!(o instanceof Core1400100711Out.ResultArray.SubArray)) {
               return false;
            } else {
               Core1400100711Out.ResultArray.SubArray other = (Core1400100711Out.ResultArray.SubArray)o;
               if (!other.canEqual(this)) {
                  return false;
               } else {
                  label95: {
                     Object this$baseAcctNo = this.getBaseAcctNo();
                     Object other$baseAcctNo = other.getBaseAcctNo();
                     if (this$baseAcctNo == null) {
                        if (other$baseAcctNo == null) {
                           break label95;
                        }
                     } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                        break label95;
                     }

                     return false;
                  }

                  Object this$acctSeqNo = this.getAcctSeqNo();
                  Object other$acctSeqNo = other.getAcctSeqNo();
                  if (this$acctSeqNo == null) {
                     if (other$acctSeqNo != null) {
                        return false;
                     }
                  } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                     return false;
                  }

                  Object this$acctCcy = this.getAcctCcy();
                  Object other$acctCcy = other.getAcctCcy();
                  if (this$acctCcy == null) {
                     if (other$acctCcy != null) {
                        return false;
                     }
                  } else if (!this$acctCcy.equals(other$acctCcy)) {
                     return false;
                  }

                  label74: {
                     Object this$batchStatus = this.getBatchStatus();
                     Object other$batchStatus = other.getBatchStatus();
                     if (this$batchStatus == null) {
                        if (other$batchStatus == null) {
                           break label74;
                        }
                     } else if (this$batchStatus.equals(other$batchStatus)) {
                        break label74;
                     }

                     return false;
                  }

                  label67: {
                     Object this$failureReason = this.getFailureReason();
                     Object other$failureReason = other.getFailureReason();
                     if (this$failureReason == null) {
                        if (other$failureReason == null) {
                           break label67;
                        }
                     } else if (this$failureReason.equals(other$failureReason)) {
                        break label67;
                     }

                     return false;
                  }

                  Object this$reference = this.getReference();
                  Object other$reference = other.getReference();
                  if (this$reference == null) {
                     if (other$reference != null) {
                        return false;
                     }
                  } else if (!this$reference.equals(other$reference)) {
                     return false;
                  }

                  Object this$dealAmt = this.getDealAmt();
                  Object other$dealAmt = other.getDealAmt();
                  if (this$dealAmt == null) {
                     if (other$dealAmt != null) {
                        return false;
                     }
                  } else if (!this$dealAmt.equals(other$dealAmt)) {
                     return false;
                  }

                  return true;
               }
            }
         }

         protected boolean canEqual(Object other) {
            return other instanceof Core1400100711Out.ResultArray.SubArray;
         }
         public String toString() {
            return "Core1400100711Out.ResultArray.SubArray(baseAcctNo=" + this.getBaseAcctNo() + ", acctSeqNo=" + this.getAcctSeqNo() + ", acctCcy=" + this.getAcctCcy() + ", batchStatus=" + this.getBatchStatus() + ", failureReason=" + this.getFailureReason() + ", reference=" + this.getReference() + ", dealAmt=" + this.getDealAmt() + ")";
         }
      }
   }
}
