package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@MessageIn
public class Core1220100101In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1220100101In.Body body;

   public Core1220100101In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1220100101In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1220100101In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1220100101In)) {
         return false;
      } else {
         Core1220100101In other = (Core1220100101In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1220100101In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "开立机构",
         notNull = false,
         length = "50",
         remark = "开立机构",
         maxSize = 50
      )
      private String openBranch;
      @V(
         desc = "支取方式",
         notNull = false,
         length = "1",
         inDesc = "S-凭印鉴支取,P-凭密码支取,W-无密码无印鉴支取,B-凭印鉴和密码支取,O-凭证件支取,R-支付密码器和印鉴",
         remark = "支取方式",
         maxSize = 1
      )
      private String withdrawalType;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "数组",
         notNull = false,
         remark = "数组"
      )
      private List<Core1220100101In.Body.TranArray> tranArray;
      @V(
         desc = "总数量",
         notNull = false,
         length = "5",
         remark = "总数量"
      )
      private Integer totalNum;
      @V(
         desc = "费用金额",
         notNull = false,
         length = "17",
         remark = "费用金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal feeAmt;
      @V(
         desc = "收费币种",
         notNull = false,
         length = "3",
         remark = "收费币种",
         maxSize = 3
      )
      private String feeCcy;
      @V(
         desc = "收取标志",
         notNull = false,
         length = "1",
         inDesc = "C-现金收取,T-转账收取,N-暂不收取,P-套餐内抵用",
         remark = "收取标志",
         maxSize = 1
      )
      private String chargeMode;

      public String getOpenBranch() {
         return this.openBranch;
      }

      public String getWithdrawalType() {
         return this.withdrawalType;
      }

      public String getCcy() {
         return this.ccy;
      }

      public List<Core1220100101In.Body.TranArray> getTranArray() {
         return this.tranArray;
      }

      public Integer getTotalNum() {
         return this.totalNum;
      }

      public BigDecimal getFeeAmt() {
         return this.feeAmt;
      }

      public String getFeeCcy() {
         return this.feeCcy;
      }

      public String getChargeMode() {
         return this.chargeMode;
      }

      public void setOpenBranch(String openBranch) {
         this.openBranch = openBranch;
      }

      public void setWithdrawalType(String withdrawalType) {
         this.withdrawalType = withdrawalType;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setTranArray(List<Core1220100101In.Body.TranArray> tranArray) {
         this.tranArray = tranArray;
      }

      public void setTotalNum(Integer totalNum) {
         this.totalNum = totalNum;
      }

      public void setFeeAmt(BigDecimal feeAmt) {
         this.feeAmt = feeAmt;
      }

      public void setFeeCcy(String feeCcy) {
         this.feeCcy = feeCcy;
      }

      public void setChargeMode(String chargeMode) {
         this.chargeMode = chargeMode;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1220100101In.Body)) {
            return false;
         } else {
            Core1220100101In.Body other = (Core1220100101In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label107: {
                  Object this$openBranch = this.getOpenBranch();
                  Object other$openBranch = other.getOpenBranch();
                  if (this$openBranch == null) {
                     if (other$openBranch == null) {
                        break label107;
                     }
                  } else if (this$openBranch.equals(other$openBranch)) {
                     break label107;
                  }

                  return false;
               }

               Object this$withdrawalType = this.getWithdrawalType();
               Object other$withdrawalType = other.getWithdrawalType();
               if (this$withdrawalType == null) {
                  if (other$withdrawalType != null) {
                     return false;
                  }
               } else if (!this$withdrawalType.equals(other$withdrawalType)) {
                  return false;
               }

               Object this$ccy = this.getCcy();
               Object other$ccy = other.getCcy();
               if (this$ccy == null) {
                  if (other$ccy != null) {
                     return false;
                  }
               } else if (!this$ccy.equals(other$ccy)) {
                  return false;
               }

               label86: {
                  Object this$tranArray = this.getTranArray();
                  Object other$tranArray = other.getTranArray();
                  if (this$tranArray == null) {
                     if (other$tranArray == null) {
                        break label86;
                     }
                  } else if (this$tranArray.equals(other$tranArray)) {
                     break label86;
                  }

                  return false;
               }

               label79: {
                  Object this$totalNum = this.getTotalNum();
                  Object other$totalNum = other.getTotalNum();
                  if (this$totalNum == null) {
                     if (other$totalNum == null) {
                        break label79;
                     }
                  } else if (this$totalNum.equals(other$totalNum)) {
                     break label79;
                  }

                  return false;
               }

               label72: {
                  Object this$feeAmt = this.getFeeAmt();
                  Object other$feeAmt = other.getFeeAmt();
                  if (this$feeAmt == null) {
                     if (other$feeAmt == null) {
                        break label72;
                     }
                  } else if (this$feeAmt.equals(other$feeAmt)) {
                     break label72;
                  }

                  return false;
               }

               Object this$feeCcy = this.getFeeCcy();
               Object other$feeCcy = other.getFeeCcy();
               if (this$feeCcy == null) {
                  if (other$feeCcy != null) {
                     return false;
                  }
               } else if (!this$feeCcy.equals(other$feeCcy)) {
                  return false;
               }

               Object this$chargeMode = this.getChargeMode();
               Object other$chargeMode = other.getChargeMode();
               if (this$chargeMode == null) {
                  if (other$chargeMode != null) {
                     return false;
                  }
               } else if (!this$chargeMode.equals(other$chargeMode)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1220100101In.Body;
      }
      public String toString() {
         return "Core1220100101In.Body(openBranch=" + this.getOpenBranch() + ", withdrawalType=" + this.getWithdrawalType() + ", ccy=" + this.getCcy() + ", tranArray=" + this.getTranArray() + ", totalNum=" + this.getTotalNum() + ", feeAmt=" + this.getFeeAmt() + ", feeCcy=" + this.getFeeCcy() + ", chargeMode=" + this.getChargeMode() + ")";
      }

      public static class TranArray {
         @V(
            desc = "账号/卡号",
            notNull = false,
            length = "50",
            remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
            maxSize = 50
         )
         private String baseAcctNo;
         @V(
            desc = "产品类型",
            notNull = false,
            length = "20",
            remark = "产品类型",
            maxSize = 20
         )
         private String prodType;
         @V(
            desc = "账户序号",
            notNull = false,
            length = "5",
            remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
            maxSize = 5
         )
         private String acctSeqNo;
         @V(
            desc = "凭证号码",
            notNull = false,
            length = "50",
            remark = "凭证号码",
            maxSize = 50
         )
         private String voucherNo;
         @V(
            desc = "凭证类型",
            notNull = false,
            length = "10",
            remark = "凭证类型",
            maxSize = 10
         )
         private String docType;
         @V(
            desc = "密码",
            notNull = false,
            length = "200",
            remark = "密码",
            maxSize = 200
         )
         private String password;
         @V(
            desc = "支取方式",
            notNull = false,
            length = "1",
            inDesc = "S-凭印鉴支取,P-凭密码支取,W-无密码无印鉴支取,B-凭印鉴和密码支取,O-凭证件支取,R-支付密码器和印鉴",
            remark = "支取方式",
            maxSize = 1
         )
         private String withdrawalType;
         @V(
            desc = "交易类型",
            notNull = false,
            length = "10",
            remark = "交易类型",
            maxSize = 10
         )
         private String tranType;
         @V(
            desc = "签约日期",
            notNull = false,
            remark = "签约日期"
         )
         private String signDate;
         @V(
            desc = "交易金额",
            notNull = false,
            length = "17",
            remark = "交易金额",
            decimalLength = 2,
            precision = 2
         )
         private BigDecimal tranAmt;
         @V(
            desc = "生效日期",
            notNull = false,
            remark = "生效日期"
         )
         private String effectDate;
         @V(
            desc = "摘要",
            notNull = false,
            length = "500",
            remark = "开户时的账号用途，销户时的销户原因",
            maxSize = 500
         )
         private String narrative;
         @V(
            desc = "币种",
            notNull = false,
            length = "3",
            remark = "币种",
            maxSize = 3
         )
         private String ccy;
         @V(
            desc = "摘要码",
            notNull = false,
            length = "30",
            remark = "摘要码",
            maxSize = 30
         )
         private String narrativeCode;

         public String getBaseAcctNo() {
            return this.baseAcctNo;
         }

         public String getProdType() {
            return this.prodType;
         }

         public String getAcctSeqNo() {
            return this.acctSeqNo;
         }

         public String getVoucherNo() {
            return this.voucherNo;
         }

         public String getDocType() {
            return this.docType;
         }

         public String getPassword() {
            return this.password;
         }

         public String getWithdrawalType() {
            return this.withdrawalType;
         }

         public String getTranType() {
            return this.tranType;
         }

         public String getSignDate() {
            return this.signDate;
         }

         public BigDecimal getTranAmt() {
            return this.tranAmt;
         }

         public String getEffectDate() {
            return this.effectDate;
         }

         public String getNarrative() {
            return this.narrative;
         }

         public String getCcy() {
            return this.ccy;
         }

         public String getNarrativeCode() {
            return this.narrativeCode;
         }

         public void setBaseAcctNo(String baseAcctNo) {
            this.baseAcctNo = baseAcctNo;
         }

         public void setProdType(String prodType) {
            this.prodType = prodType;
         }

         public void setAcctSeqNo(String acctSeqNo) {
            this.acctSeqNo = acctSeqNo;
         }

         public void setVoucherNo(String voucherNo) {
            this.voucherNo = voucherNo;
         }

         public void setDocType(String docType) {
            this.docType = docType;
         }

         public void setPassword(String password) {
            this.password = password;
         }

         public void setWithdrawalType(String withdrawalType) {
            this.withdrawalType = withdrawalType;
         }

         public void setTranType(String tranType) {
            this.tranType = tranType;
         }

         public void setSignDate(String signDate) {
            this.signDate = signDate;
         }

         public void setTranAmt(BigDecimal tranAmt) {
            this.tranAmt = tranAmt;
         }

         public void setEffectDate(String effectDate) {
            this.effectDate = effectDate;
         }

         public void setNarrative(String narrative) {
            this.narrative = narrative;
         }

         public void setCcy(String ccy) {
            this.ccy = ccy;
         }

         public void setNarrativeCode(String narrativeCode) {
            this.narrativeCode = narrativeCode;
         }

         public boolean equals(Object o) {
            if (o == this) {
               return true;
            } else if (!(o instanceof Core1220100101In.Body.TranArray)) {
               return false;
            } else {
               Core1220100101In.Body.TranArray other = (Core1220100101In.Body.TranArray)o;
               if (!other.canEqual(this)) {
                  return false;
               } else {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo != null) {
                        return false;
                     }
                  } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
                     return false;
                  }

                  Object this$prodType = this.getProdType();
                  Object other$prodType = other.getProdType();
                  if (this$prodType == null) {
                     if (other$prodType != null) {
                        return false;
                     }
                  } else if (!this$prodType.equals(other$prodType)) {
                     return false;
                  }

                  Object this$acctSeqNo = this.getAcctSeqNo();
                  Object other$acctSeqNo = other.getAcctSeqNo();
                  if (this$acctSeqNo == null) {
                     if (other$acctSeqNo != null) {
                        return false;
                     }
                  } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                     return false;
                  }

                  label158: {
                     Object this$voucherNo = this.getVoucherNo();
                     Object other$voucherNo = other.getVoucherNo();
                     if (this$voucherNo == null) {
                        if (other$voucherNo == null) {
                           break label158;
                        }
                     } else if (this$voucherNo.equals(other$voucherNo)) {
                        break label158;
                     }

                     return false;
                  }

                  label151: {
                     Object this$docType = this.getDocType();
                     Object other$docType = other.getDocType();
                     if (this$docType == null) {
                        if (other$docType == null) {
                           break label151;
                        }
                     } else if (this$docType.equals(other$docType)) {
                        break label151;
                     }

                     return false;
                  }

                  Object this$password = this.getPassword();
                  Object other$password = other.getPassword();
                  if (this$password == null) {
                     if (other$password != null) {
                        return false;
                     }
                  } else if (!this$password.equals(other$password)) {
                     return false;
                  }

                  label137: {
                     Object this$withdrawalType = this.getWithdrawalType();
                     Object other$withdrawalType = other.getWithdrawalType();
                     if (this$withdrawalType == null) {
                        if (other$withdrawalType == null) {
                           break label137;
                        }
                     } else if (this$withdrawalType.equals(other$withdrawalType)) {
                        break label137;
                     }

                     return false;
                  }

                  label130: {
                     Object this$tranType = this.getTranType();
                     Object other$tranType = other.getTranType();
                     if (this$tranType == null) {
                        if (other$tranType == null) {
                           break label130;
                        }
                     } else if (this$tranType.equals(other$tranType)) {
                        break label130;
                     }

                     return false;
                  }

                  Object this$signDate = this.getSignDate();
                  Object other$signDate = other.getSignDate();
                  if (this$signDate == null) {
                     if (other$signDate != null) {
                        return false;
                     }
                  } else if (!this$signDate.equals(other$signDate)) {
                     return false;
                  }

                  Object this$tranAmt = this.getTranAmt();
                  Object other$tranAmt = other.getTranAmt();
                  if (this$tranAmt == null) {
                     if (other$tranAmt != null) {
                        return false;
                     }
                  } else if (!this$tranAmt.equals(other$tranAmt)) {
                     return false;
                  }

                  label109: {
                     Object this$effectDate = this.getEffectDate();
                     Object other$effectDate = other.getEffectDate();
                     if (this$effectDate == null) {
                        if (other$effectDate == null) {
                           break label109;
                        }
                     } else if (this$effectDate.equals(other$effectDate)) {
                        break label109;
                     }

                     return false;
                  }

                  label102: {
                     Object this$narrative = this.getNarrative();
                     Object other$narrative = other.getNarrative();
                     if (this$narrative == null) {
                        if (other$narrative == null) {
                           break label102;
                        }
                     } else if (this$narrative.equals(other$narrative)) {
                        break label102;
                     }

                     return false;
                  }

                  Object this$ccy = this.getCcy();
                  Object other$ccy = other.getCcy();
                  if (this$ccy == null) {
                     if (other$ccy != null) {
                        return false;
                     }
                  } else if (!this$ccy.equals(other$ccy)) {
                     return false;
                  }

                  Object this$narrativeCode = this.getNarrativeCode();
                  Object other$narrativeCode = other.getNarrativeCode();
                  if (this$narrativeCode == null) {
                     if (other$narrativeCode != null) {
                        return false;
                     }
                  } else if (!this$narrativeCode.equals(other$narrativeCode)) {
                     return false;
                  }

                  return true;
               }
            }
         }

         protected boolean canEqual(Object other) {
            return other instanceof Core1220100101In.Body.TranArray;
         }
         public String toString() {
            return "Core1220100101In.Body.TranArray(baseAcctNo=" + this.getBaseAcctNo() + ", prodType=" + this.getProdType() + ", acctSeqNo=" + this.getAcctSeqNo() + ", voucherNo=" + this.getVoucherNo() + ", docType=" + this.getDocType() + ", password=" + this.getPassword() + ", withdrawalType=" + this.getWithdrawalType() + ", tranType=" + this.getTranType() + ", signDate=" + this.getSignDate() + ", tranAmt=" + this.getTranAmt() + ", effectDate=" + this.getEffectDate() + ", narrative=" + this.getNarrative() + ", ccy=" + this.getCcy() + ", narrativeCode=" + this.getNarrativeCode() + ")";
         }
      }
   }
}
