package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10009404In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core10009404Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore10009404 {
   String URL = "/rb/fin/inner/special/account";


   @ApiDesc("内部账特殊记账，支持内部账户至科目间的划转功能")
   @EnsembleElements(
      serviceCode = "Core",
      messageType = "1000",
      messageCode = "9404"
   )
   @FunctionCategory("RB15-内部账")
   @ConsumeSys("TLE")
   Core10009404Out runService(Core10009404In var1);
}
