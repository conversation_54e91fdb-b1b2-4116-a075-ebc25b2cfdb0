package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@MessageIn
public class Core1200100218In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1200100218In.Body body;

   public Core1200100218In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1200100218In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1200100218In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200100218In)) {
         return false;
      } else {
         Core1200100218In other = (Core1200100218In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200100218In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "发行年度",
         notNull = true,
         length = "5",
         remark = "发行年度",
         maxSize = 5
      )
      private String issueYear;
      @V(
         desc = "客户类型",
         notNull = true,
         length = "3",
         remark = "客户大类，目前一般分为个人，公司，金融机构和内部客户。取之于CIF_CLIENT_TYPE.CLIENT_TYPE",
         maxSize = 3
      )
      private String clientType;
      @V(
         desc = "开始日期",
         notNull = true,
         remark = "开始日期"
      )
      private String startDate;
      @V(
         desc = "结束日期",
         notNull = true,
         remark = "结束日期"
      )
      private String endDate;
      @V(
         desc = "利率类型",
         notNull = false,
         length = "5",
         remark = "利率类型",
         maxSize = 5
      )
      private String intType;
      @V(
         desc = "是否可赎回",
         notNull = true,
         length = "1",
         inDesc = "Y-是,N-否",
         remark = "是否可赎回",
         maxSize = 1
      )
      private String redemptionFlag;
      @V(
         desc = "是否扣划利息标志",
         notNull = false,
         length = "1",
         inDesc = "Y-是,N-否",
         remark = "是否扣划利息标志",
         maxSize = 1
      )
      private String intFlag;
      @V(
         desc = "浮动利率",
         notNull = false,
         length = "15",
         remark = "浮动利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal floatRate;
      @V(
         desc = "客户级密码交易操作类型",
         notNull = false,
         length = "1",
         inDesc = "A-新增,U-修改,D-删除,R-重置,C-校验",
         remark = "客户级密码交易操作类型",
         maxSize = 1
      )
      private String operationType;
      @V(
         desc = "所属机构号",
         notNull = false,
         length = "50",
         remark = "机构代码",
         maxSize = 50
      )
      private String branch;
      @V(
         desc = "期次发行金额",
         notNull = true,
         length = "17",
         remark = "期次发行金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal issueAmt;
      @V(
         desc = "起售时间",
         notNull = false,
         length = "26",
         remark = "起售时间",
         maxSize = 26
      )
      private String saleStartTime;
      @V(
         desc = "止售时间",
         notNull = false,
         length = "26",
         remark = "止售时间",
         maxSize = 26
      )
      private String saleEndTime;
      @V(
         desc = "期次起存金额",
         notNull = true,
         length = "17",
         remark = "期次起存金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal stageMinAmt;
      @V(
         desc = "单笔认购最大金额",
         notNull = false,
         length = "17",
         remark = "单笔认购最大金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal sgMaxAmt;
      @V(
         desc = "最小留存金额",
         notNull = true,
         length = "17",
         remark = "最小留存金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal keepMinBal;
      @V(
         desc = "存期期限",
         notNull = true,
         length = "5",
         remark = "期限",
         maxSize = 5
      )
      private String term;
      @V(
         desc = "付息方式",
         notNull = true,
         length = "3",
         inDesc = "1-一次还本付息,2-定期按频率付息到期还本付息",
         remark = "付息方式",
         maxSize = 3
      )
      private String payIntType;
      @V(
         desc = "是否允许提前支取",
         notNull = true,
         length = "1",
         inDesc = "Y-是,N-否",
         remark = "是否允许提前支取",
         maxSize = 1
      )
      private String preWithdrawFlag;
      @V(
         desc = "提前支取次数",
         notNull = false,
         length = "5",
         remark = "提前支取次数",
         restraint = "preWithdrawFlag=Y"
      )
      private Integer preWithdrawNum;
      @V(
         desc = "是否全额转让",
         notNull = true,
         length = "1",
         remark = "定期转让是否全额转让 Y -是 N -否",
         maxSize = 1
      )
      private String isFullTransfer;
      @V(
         desc = "赎回日期",
         notNull = false,
         remark = "赎回日期"
      )
      private String tohonorDate;
      @V(
         desc = "赎回利率",
         notNull = false,
         length = "15",
         remark = "赎回利率",
         restraint = "redemptionIntType=FIX",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal tohonorRate;
      @V(
         desc = "转出费用",
         notNull = false,
         length = "17",
         remark = "转出费用",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal trfOutFeeAmt;
      @V(
         desc = "电子邮件",
         notNull = false,
         length = "200",
         remark = "电子邮件",
         maxSize = 200
      )
      private String email;
      @V(
         desc = "赎回利率类型",
         notNull = true,
         length = "5",
         inDesc = "MRT-按产品利率,PRE-按活期利率,FIX-固定利率",
         remark = "赎回利率类型",
         maxSize = 5
      )
      private String redemptionIntType;
      @V(
         desc = "转入费用类型",
         notNull = false,
         length = "20",
         remark = "转入费用类型",
         maxSize = 20
      )
      private String trfInFeeType;
      @V(
         desc = "转出费用类型",
         notNull = false,
         length = "20",
         remark = "转出费用类型",
         maxSize = 20
      )
      private String trfOutFeeType;
      @V(
         desc = "转入手续费",
         notNull = false,
         length = "17",
         remark = "转入手续费",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal inFee;
      @V(
         desc = "资金来源是否基于内部户",
         notNull = false,
         length = "2",
         remark = "资金来源是否基于内部户",
         maxSize = 2
      )
      private String isCashFromInnerAcct;
      @V(
         desc = "交易密码操作类型",
         notNull = false,
         length = "1",
         remark = "交易密码操作类型",
         maxSize = 1
      )
      private String tranPasswordOperateType;
      @V(
         desc = "最小变动额",
         notNull = true,
         length = "17",
         remark = "最小变动额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal minChangeBalance;
      @V(
         desc = "产品类型",
         notNull = true,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "币种",
         notNull = true,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "起息日",
         notNull = false,
         remark = "起息日",
         restraint = "intStartFlag=1"
      )
      private String intStartDate;
      @V(
         desc = "到期日",
         notNull = false,
         remark = "到期日",
         restraint = "intStartFlag=1"
      )
      private String matureDate;
      @V(
         desc = "境内境外标志",
         notNull = true,
         length = "1",
         inDesc = "I-境内,O-境外",
         remark = "境内境外标志",
         maxSize = 1
      )
      private String inlandOffshore;
      @V(
         desc = "期限类型",
         notNull = true,
         length = "1",
         inDesc = "Y-年,Q-季,M-月,W-周,D-日",
         remark = "期限类型",
         maxSize = 1
      )
      private String termType;
      @V(
         desc = "期次代码",
         notNull = true,
         length = "50",
         remark = "期次代码",
         maxSize = 50
      )
      private String stageCode;
      @V(
         desc = "期次描述",
         notNull = true,
         length = "200",
         remark = "中文期次描述",
         maxSize = 200
      )
      private String stageCodeDesc;
      @V(
         desc = "结息频率",
         notNull = false,
         length = "5",
         remark = "结息频率",
         restraint = "payIntType=2",
         maxSize = 5
      )
      private String cycleFreq;
      @V(
         desc = "起息标识",
         notNull = true,
         length = "1",
         inDesc = "0-立即起息,1-募集结束日起息",
         remark = "起息标识",
         maxSize = 1
      )
      private String intStartFlag;
      @V(
         desc = "是否指定收息",
         notNull = false,
         length = "1",
         inDesc = "Y-是,N-否",
         remark = "是否指定收息",
         maxSize = 1
      )
      private String directionChargeIntFlag;
      @V(
         desc = "数组",
         notNull = false,
         remark = "数组"
      )
      private List<Core1200100218In.Body.ClientListArray> clientListArray;
      @V(
         desc = "冷静期",
         notNull = false,
         length = "5",
         remark = "大额存单兑付冷静期"
      )
      private Integer calmDays;
      @V(
         desc = "数组",
         notNull = false,
         remark = "数组"
      )
      private List<Core1200100218In.Body.ChannelArray> channelArray;
      @V(
         desc = "客户群体",
         notNull = false,
         length = "20",
         remark = "客户群体",
         maxSize = 20
      )
      private String customerBase;
      @V(
         desc = "固定利率",
         notNull = false,
         length = "15",
         remark = "固定利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal fixedRate;
      @V(
         desc = "允许购买的机构",
         notNull = true,
         length = "20",
         remark = "允许购买的机构",
         maxSize = 20
      )
      private String allowBranch;
      @V(
         desc = "允许购买的账户分类",
         notNull = true,
         length = "1",
         remark = "允许购买的账户分类 | 1-一类户，2-二类户，3-不限制",
         maxSize = 1
      )
      private String allowAcctClass;
      @V(
         desc = "转让截止日 | 正整数的数字",
         notNull = false,
         length = "5",
         remark = "转让截止日 | 正整数的数字",
         maxSize = 5
      )
      private String trfThruDate;
      @V(
         desc = "转让挂单截止日",
         notNull = false,
         length = "5",
         remark = "转让挂单截止日 | 正整数的数字",
         maxSize = 5
      )
      private String trfOrderThruDate;
      @V(
         desc = "客户群购买最大金额(包含：客户群体 、最高购买上限)",
         notNull = false,
         remark = "客户群购买最大金额(包含：客户群体 、最高购买上限)。一个客户群体只允许设置一条最高购买上限。"
      )
      private List<Core1200100218In.Body.CustomerBaseArrays> customerBaseArrays;
      @V(
         desc = "新旧资金标识  | Y-新资金 N-不区分",
         notNull = true,
         length = "1",
         remark = "新旧资金标识  | Y-不区分 N-新资金",
         maxSize = 1
      )
      private String newAndOldFundFlag;
      @V(
         desc = "期次状态",
         notNull = false,
         length = "2",
         inDesc = "N-发行,R-认购期,RE-认购结束期,D-封闭期（删除）,G-结息（申购期）,PS-提前终止,RS-发行失败,S-终止",
         remark = "期次状态",
         maxSize = 2
      )
      private String stageStatus;
      @V(
         desc = "第二语言期次描述",
         notNull = false,
         length = "50",
         remark = "第二语言期次描述",
         maxSize = 50
      )
      private String stageCodeDescSecond;
      @V(
         desc = "第二语言期次详细备注",
         notNull = false,
         length = "200",
         remark = "第二语言期次详细备注",
         maxSize = 200
      )
      private String stageRemarkSecond;
      @V(
         desc = "期次详细备注",
         notNull = false,
         length = "200",
         remark = "期次详细备注",
         maxSize = 200
      )
      private String stageRemark;
      @V(
         desc = "执行利率",
         notNull = false,
         length = "15",
         remark = "执行利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal realRate;
      @V(
         desc = "客户群体 | 支持大类多选，大类下面选小类",
         notNull = false,
         remark = "客户群体 | 支持大类多选，大类下面选小类"
      )
      private List<Core1200100218In.Body.CustomerBaseClass> customerBaseClass;
      @V(
         desc = "英文期次描述",
         notNull = false,
         length = "200",
         remark = "英文期次描述",
         maxSize = 200
      )
      private String stageCodeDescEn;
      @V(
         desc = "出售分行或者出售机构",
         notNull = false,
         length = "500",
         remark = "出售分行或者出售机构，多可以是一个或者多个，定义多个时，需要用分隔符 | 进行分开存储",
         maxSize = 500
      )
      private String sellBranch;
      @V(
         desc = "渠道",
         notNull = false,
         length = "10",
         inDesc = "JZ-集中作业,MT-柜面,ZD-智能网点自助交易平台,VT-远程智能自助银行,QT-XBankT10,QE-快窗智能高柜,CP-手机银行,EB-个人网上银行,CB-企业网上银行,CM-现金管理,WB-微信银行A ,PT-支付",
         remark = "渠道细类",
         maxSize = 10
      )
      private String channel;
      @V(
         desc = "自动续存资金来源账户付息频率",
         notNull = false,
         length = "5",
         remark = "自动续存资金来源账户付息频率",
         maxSize = 5
      )
      private String aupCycle;
      @V(
         desc = "赎回起息日期",
         notNull = false,
         remark = "赎回起息日期"
      )
      private String redeemIntDate;
      @V(
         desc = "结息日期",
         notNull = false,
         remark = "结息日期"
      )
      private String captDate;
      @V(
         desc = "到期本金在宽限期是否收息",
         notNull = false,
         length = "1",
         inDesc = "Y-是,N-否",
         remark = "到期本金在宽限期是否收息",
         maxSize = 1
      )
      private String graceChargeIntFlag;
      @V(
         desc = "支持组合购买方式",
         notNull = false,
         length = "1",
         remark = "1-单独购买2-组合购买3-单买与组合买",
         maxSize = 1
      )
      private String allowBuyWayCd;
      @V(
         desc = "是否白名单发售",
         notNull = false,
         length = "1",
         inDesc = "Y-是,N-否",
         remark = "是否白名单发售Y-是，N-否",
         maxSize = 1
      )
      private String whiteSellFlag;

      public String getIssueYear() {
         return this.issueYear;
      }

      public String getClientType() {
         return this.clientType;
      }

      public String getStartDate() {
         return this.startDate;
      }

      public String getEndDate() {
         return this.endDate;
      }

      public String getIntType() {
         return this.intType;
      }

      public String getRedemptionFlag() {
         return this.redemptionFlag;
      }

      public String getIntFlag() {
         return this.intFlag;
      }

      public BigDecimal getFloatRate() {
         return this.floatRate;
      }

      public String getOperationType() {
         return this.operationType;
      }

      public String getBranch() {
         return this.branch;
      }

      public BigDecimal getIssueAmt() {
         return this.issueAmt;
      }

      public String getSaleStartTime() {
         return this.saleStartTime;
      }

      public String getSaleEndTime() {
         return this.saleEndTime;
      }

      public BigDecimal getStageMinAmt() {
         return this.stageMinAmt;
      }

      public BigDecimal getSgMaxAmt() {
         return this.sgMaxAmt;
      }

      public BigDecimal getKeepMinBal() {
         return this.keepMinBal;
      }

      public String getTerm() {
         return this.term;
      }

      public String getPayIntType() {
         return this.payIntType;
      }

      public String getPreWithdrawFlag() {
         return this.preWithdrawFlag;
      }

      public Integer getPreWithdrawNum() {
         return this.preWithdrawNum;
      }

      public String getIsFullTransfer() {
         return this.isFullTransfer;
      }

      public String getTohonorDate() {
         return this.tohonorDate;
      }

      public BigDecimal getTohonorRate() {
         return this.tohonorRate;
      }

      public BigDecimal getTrfOutFeeAmt() {
         return this.trfOutFeeAmt;
      }

      public String getEmail() {
         return this.email;
      }

      public String getRedemptionIntType() {
         return this.redemptionIntType;
      }

      public String getTrfInFeeType() {
         return this.trfInFeeType;
      }

      public String getTrfOutFeeType() {
         return this.trfOutFeeType;
      }

      public BigDecimal getInFee() {
         return this.inFee;
      }

      public String getIsCashFromInnerAcct() {
         return this.isCashFromInnerAcct;
      }

      public String getTranPasswordOperateType() {
         return this.tranPasswordOperateType;
      }

      public BigDecimal getMinChangeBalance() {
         return this.minChangeBalance;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getCcy() {
         return this.ccy;
      }

      public String getIntStartDate() {
         return this.intStartDate;
      }

      public String getMatureDate() {
         return this.matureDate;
      }

      public String getInlandOffshore() {
         return this.inlandOffshore;
      }

      public String getTermType() {
         return this.termType;
      }

      public String getStageCode() {
         return this.stageCode;
      }

      public String getStageCodeDesc() {
         return this.stageCodeDesc;
      }

      public String getCycleFreq() {
         return this.cycleFreq;
      }

      public String getIntStartFlag() {
         return this.intStartFlag;
      }

      public String getDirectionChargeIntFlag() {
         return this.directionChargeIntFlag;
      }

      public List<Core1200100218In.Body.ClientListArray> getClientListArray() {
         return this.clientListArray;
      }

      public Integer getCalmDays() {
         return this.calmDays;
      }

      public List<Core1200100218In.Body.ChannelArray> getChannelArray() {
         return this.channelArray;
      }

      public String getCustomerBase() {
         return this.customerBase;
      }

      public BigDecimal getFixedRate() {
         return this.fixedRate;
      }

      public String getAllowBranch() {
         return this.allowBranch;
      }

      public String getAllowAcctClass() {
         return this.allowAcctClass;
      }

      public String getTrfThruDate() {
         return this.trfThruDate;
      }

      public String getTrfOrderThruDate() {
         return this.trfOrderThruDate;
      }

      public List<Core1200100218In.Body.CustomerBaseArrays> getCustomerBaseArrays() {
         return this.customerBaseArrays;
      }

      public String getNewAndOldFundFlag() {
         return this.newAndOldFundFlag;
      }

      public String getStageStatus() {
         return this.stageStatus;
      }

      public String getStageCodeDescSecond() {
         return this.stageCodeDescSecond;
      }

      public String getStageRemarkSecond() {
         return this.stageRemarkSecond;
      }

      public String getStageRemark() {
         return this.stageRemark;
      }

      public BigDecimal getRealRate() {
         return this.realRate;
      }

      public List<Core1200100218In.Body.CustomerBaseClass> getCustomerBaseClass() {
         return this.customerBaseClass;
      }

      public String getStageCodeDescEn() {
         return this.stageCodeDescEn;
      }

      public String getSellBranch() {
         return this.sellBranch;
      }

      public String getChannel() {
         return this.channel;
      }

      public String getAupCycle() {
         return this.aupCycle;
      }

      public String getRedeemIntDate() {
         return this.redeemIntDate;
      }

      public String getCaptDate() {
         return this.captDate;
      }

      public String getGraceChargeIntFlag() {
         return this.graceChargeIntFlag;
      }

      public String getAllowBuyWayCd() {
         return this.allowBuyWayCd;
      }

      public String getWhiteSellFlag() {
         return this.whiteSellFlag;
      }

      public void setIssueYear(String issueYear) {
         this.issueYear = issueYear;
      }

      public void setClientType(String clientType) {
         this.clientType = clientType;
      }

      public void setStartDate(String startDate) {
         this.startDate = startDate;
      }

      public void setEndDate(String endDate) {
         this.endDate = endDate;
      }

      public void setIntType(String intType) {
         this.intType = intType;
      }

      public void setRedemptionFlag(String redemptionFlag) {
         this.redemptionFlag = redemptionFlag;
      }

      public void setIntFlag(String intFlag) {
         this.intFlag = intFlag;
      }

      public void setFloatRate(BigDecimal floatRate) {
         this.floatRate = floatRate;
      }

      public void setOperationType(String operationType) {
         this.operationType = operationType;
      }

      public void setBranch(String branch) {
         this.branch = branch;
      }

      public void setIssueAmt(BigDecimal issueAmt) {
         this.issueAmt = issueAmt;
      }

      public void setSaleStartTime(String saleStartTime) {
         this.saleStartTime = saleStartTime;
      }

      public void setSaleEndTime(String saleEndTime) {
         this.saleEndTime = saleEndTime;
      }

      public void setStageMinAmt(BigDecimal stageMinAmt) {
         this.stageMinAmt = stageMinAmt;
      }

      public void setSgMaxAmt(BigDecimal sgMaxAmt) {
         this.sgMaxAmt = sgMaxAmt;
      }

      public void setKeepMinBal(BigDecimal keepMinBal) {
         this.keepMinBal = keepMinBal;
      }

      public void setTerm(String term) {
         this.term = term;
      }

      public void setPayIntType(String payIntType) {
         this.payIntType = payIntType;
      }

      public void setPreWithdrawFlag(String preWithdrawFlag) {
         this.preWithdrawFlag = preWithdrawFlag;
      }

      public void setPreWithdrawNum(Integer preWithdrawNum) {
         this.preWithdrawNum = preWithdrawNum;
      }

      public void setIsFullTransfer(String isFullTransfer) {
         this.isFullTransfer = isFullTransfer;
      }

      public void setTohonorDate(String tohonorDate) {
         this.tohonorDate = tohonorDate;
      }

      public void setTohonorRate(BigDecimal tohonorRate) {
         this.tohonorRate = tohonorRate;
      }

      public void setTrfOutFeeAmt(BigDecimal trfOutFeeAmt) {
         this.trfOutFeeAmt = trfOutFeeAmt;
      }

      public void setEmail(String email) {
         this.email = email;
      }

      public void setRedemptionIntType(String redemptionIntType) {
         this.redemptionIntType = redemptionIntType;
      }

      public void setTrfInFeeType(String trfInFeeType) {
         this.trfInFeeType = trfInFeeType;
      }

      public void setTrfOutFeeType(String trfOutFeeType) {
         this.trfOutFeeType = trfOutFeeType;
      }

      public void setInFee(BigDecimal inFee) {
         this.inFee = inFee;
      }

      public void setIsCashFromInnerAcct(String isCashFromInnerAcct) {
         this.isCashFromInnerAcct = isCashFromInnerAcct;
      }

      public void setTranPasswordOperateType(String tranPasswordOperateType) {
         this.tranPasswordOperateType = tranPasswordOperateType;
      }

      public void setMinChangeBalance(BigDecimal minChangeBalance) {
         this.minChangeBalance = minChangeBalance;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setIntStartDate(String intStartDate) {
         this.intStartDate = intStartDate;
      }

      public void setMatureDate(String matureDate) {
         this.matureDate = matureDate;
      }

      public void setInlandOffshore(String inlandOffshore) {
         this.inlandOffshore = inlandOffshore;
      }

      public void setTermType(String termType) {
         this.termType = termType;
      }

      public void setStageCode(String stageCode) {
         this.stageCode = stageCode;
      }

      public void setStageCodeDesc(String stageCodeDesc) {
         this.stageCodeDesc = stageCodeDesc;
      }

      public void setCycleFreq(String cycleFreq) {
         this.cycleFreq = cycleFreq;
      }

      public void setIntStartFlag(String intStartFlag) {
         this.intStartFlag = intStartFlag;
      }

      public void setDirectionChargeIntFlag(String directionChargeIntFlag) {
         this.directionChargeIntFlag = directionChargeIntFlag;
      }

      public void setClientListArray(List<Core1200100218In.Body.ClientListArray> clientListArray) {
         this.clientListArray = clientListArray;
      }

      public void setCalmDays(Integer calmDays) {
         this.calmDays = calmDays;
      }

      public void setChannelArray(List<Core1200100218In.Body.ChannelArray> channelArray) {
         this.channelArray = channelArray;
      }

      public void setCustomerBase(String customerBase) {
         this.customerBase = customerBase;
      }

      public void setFixedRate(BigDecimal fixedRate) {
         this.fixedRate = fixedRate;
      }

      public void setAllowBranch(String allowBranch) {
         this.allowBranch = allowBranch;
      }

      public void setAllowAcctClass(String allowAcctClass) {
         this.allowAcctClass = allowAcctClass;
      }

      public void setTrfThruDate(String trfThruDate) {
         this.trfThruDate = trfThruDate;
      }

      public void setTrfOrderThruDate(String trfOrderThruDate) {
         this.trfOrderThruDate = trfOrderThruDate;
      }

      public void setCustomerBaseArrays(List<Core1200100218In.Body.CustomerBaseArrays> customerBaseArrays) {
         this.customerBaseArrays = customerBaseArrays;
      }

      public void setNewAndOldFundFlag(String newAndOldFundFlag) {
         this.newAndOldFundFlag = newAndOldFundFlag;
      }

      public void setStageStatus(String stageStatus) {
         this.stageStatus = stageStatus;
      }

      public void setStageCodeDescSecond(String stageCodeDescSecond) {
         this.stageCodeDescSecond = stageCodeDescSecond;
      }

      public void setStageRemarkSecond(String stageRemarkSecond) {
         this.stageRemarkSecond = stageRemarkSecond;
      }

      public void setStageRemark(String stageRemark) {
         this.stageRemark = stageRemark;
      }

      public void setRealRate(BigDecimal realRate) {
         this.realRate = realRate;
      }

      public void setCustomerBaseClass(List<Core1200100218In.Body.CustomerBaseClass> customerBaseClass) {
         this.customerBaseClass = customerBaseClass;
      }

      public void setStageCodeDescEn(String stageCodeDescEn) {
         this.stageCodeDescEn = stageCodeDescEn;
      }

      public void setSellBranch(String sellBranch) {
         this.sellBranch = sellBranch;
      }

      public void setChannel(String channel) {
         this.channel = channel;
      }

      public void setAupCycle(String aupCycle) {
         this.aupCycle = aupCycle;
      }

      public void setRedeemIntDate(String redeemIntDate) {
         this.redeemIntDate = redeemIntDate;
      }

      public void setCaptDate(String captDate) {
         this.captDate = captDate;
      }

      public void setGraceChargeIntFlag(String graceChargeIntFlag) {
         this.graceChargeIntFlag = graceChargeIntFlag;
      }

      public void setAllowBuyWayCd(String allowBuyWayCd) {
         this.allowBuyWayCd = allowBuyWayCd;
      }

      public void setWhiteSellFlag(String whiteSellFlag) {
         this.whiteSellFlag = whiteSellFlag;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1200100218In.Body)) {
            return false;
         } else {
            Core1200100218In.Body other = (Core1200100218In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label839: {
                  Object this$issueYear = this.getIssueYear();
                  Object other$issueYear = other.getIssueYear();
                  if (this$issueYear == null) {
                     if (other$issueYear == null) {
                        break label839;
                     }
                  } else if (this$issueYear.equals(other$issueYear)) {
                     break label839;
                  }

                  return false;
               }

               Object this$clientType = this.getClientType();
               Object other$clientType = other.getClientType();
               if (this$clientType == null) {
                  if (other$clientType != null) {
                     return false;
                  }
               } else if (!this$clientType.equals(other$clientType)) {
                  return false;
               }

               label825: {
                  Object this$startDate = this.getStartDate();
                  Object other$startDate = other.getStartDate();
                  if (this$startDate == null) {
                     if (other$startDate == null) {
                        break label825;
                     }
                  } else if (this$startDate.equals(other$startDate)) {
                     break label825;
                  }

                  return false;
               }

               Object this$endDate = this.getEndDate();
               Object other$endDate = other.getEndDate();
               if (this$endDate == null) {
                  if (other$endDate != null) {
                     return false;
                  }
               } else if (!this$endDate.equals(other$endDate)) {
                  return false;
               }

               label811: {
                  Object this$intType = this.getIntType();
                  Object other$intType = other.getIntType();
                  if (this$intType == null) {
                     if (other$intType == null) {
                        break label811;
                     }
                  } else if (this$intType.equals(other$intType)) {
                     break label811;
                  }

                  return false;
               }

               Object this$redemptionFlag = this.getRedemptionFlag();
               Object other$redemptionFlag = other.getRedemptionFlag();
               if (this$redemptionFlag == null) {
                  if (other$redemptionFlag != null) {
                     return false;
                  }
               } else if (!this$redemptionFlag.equals(other$redemptionFlag)) {
                  return false;
               }

               label797: {
                  Object this$intFlag = this.getIntFlag();
                  Object other$intFlag = other.getIntFlag();
                  if (this$intFlag == null) {
                     if (other$intFlag == null) {
                        break label797;
                     }
                  } else if (this$intFlag.equals(other$intFlag)) {
                     break label797;
                  }

                  return false;
               }

               label790: {
                  Object this$floatRate = this.getFloatRate();
                  Object other$floatRate = other.getFloatRate();
                  if (this$floatRate == null) {
                     if (other$floatRate == null) {
                        break label790;
                     }
                  } else if (this$floatRate.equals(other$floatRate)) {
                     break label790;
                  }

                  return false;
               }

               Object this$operationType = this.getOperationType();
               Object other$operationType = other.getOperationType();
               if (this$operationType == null) {
                  if (other$operationType != null) {
                     return false;
                  }
               } else if (!this$operationType.equals(other$operationType)) {
                  return false;
               }

               label776: {
                  Object this$branch = this.getBranch();
                  Object other$branch = other.getBranch();
                  if (this$branch == null) {
                     if (other$branch == null) {
                        break label776;
                     }
                  } else if (this$branch.equals(other$branch)) {
                     break label776;
                  }

                  return false;
               }

               label769: {
                  Object this$issueAmt = this.getIssueAmt();
                  Object other$issueAmt = other.getIssueAmt();
                  if (this$issueAmt == null) {
                     if (other$issueAmt == null) {
                        break label769;
                     }
                  } else if (this$issueAmt.equals(other$issueAmt)) {
                     break label769;
                  }

                  return false;
               }

               Object this$saleStartTime = this.getSaleStartTime();
               Object other$saleStartTime = other.getSaleStartTime();
               if (this$saleStartTime == null) {
                  if (other$saleStartTime != null) {
                     return false;
                  }
               } else if (!this$saleStartTime.equals(other$saleStartTime)) {
                  return false;
               }

               Object this$saleEndTime = this.getSaleEndTime();
               Object other$saleEndTime = other.getSaleEndTime();
               if (this$saleEndTime == null) {
                  if (other$saleEndTime != null) {
                     return false;
                  }
               } else if (!this$saleEndTime.equals(other$saleEndTime)) {
                  return false;
               }

               label748: {
                  Object this$stageMinAmt = this.getStageMinAmt();
                  Object other$stageMinAmt = other.getStageMinAmt();
                  if (this$stageMinAmt == null) {
                     if (other$stageMinAmt == null) {
                        break label748;
                     }
                  } else if (this$stageMinAmt.equals(other$stageMinAmt)) {
                     break label748;
                  }

                  return false;
               }

               Object this$sgMaxAmt = this.getSgMaxAmt();
               Object other$sgMaxAmt = other.getSgMaxAmt();
               if (this$sgMaxAmt == null) {
                  if (other$sgMaxAmt != null) {
                     return false;
                  }
               } else if (!this$sgMaxAmt.equals(other$sgMaxAmt)) {
                  return false;
               }

               Object this$keepMinBal = this.getKeepMinBal();
               Object other$keepMinBal = other.getKeepMinBal();
               if (this$keepMinBal == null) {
                  if (other$keepMinBal != null) {
                     return false;
                  }
               } else if (!this$keepMinBal.equals(other$keepMinBal)) {
                  return false;
               }

               label727: {
                  Object this$term = this.getTerm();
                  Object other$term = other.getTerm();
                  if (this$term == null) {
                     if (other$term == null) {
                        break label727;
                     }
                  } else if (this$term.equals(other$term)) {
                     break label727;
                  }

                  return false;
               }

               Object this$payIntType = this.getPayIntType();
               Object other$payIntType = other.getPayIntType();
               if (this$payIntType == null) {
                  if (other$payIntType != null) {
                     return false;
                  }
               } else if (!this$payIntType.equals(other$payIntType)) {
                  return false;
               }

               label713: {
                  Object this$preWithdrawFlag = this.getPreWithdrawFlag();
                  Object other$preWithdrawFlag = other.getPreWithdrawFlag();
                  if (this$preWithdrawFlag == null) {
                     if (other$preWithdrawFlag == null) {
                        break label713;
                     }
                  } else if (this$preWithdrawFlag.equals(other$preWithdrawFlag)) {
                     break label713;
                  }

                  return false;
               }

               Object this$preWithdrawNum = this.getPreWithdrawNum();
               Object other$preWithdrawNum = other.getPreWithdrawNum();
               if (this$preWithdrawNum == null) {
                  if (other$preWithdrawNum != null) {
                     return false;
                  }
               } else if (!this$preWithdrawNum.equals(other$preWithdrawNum)) {
                  return false;
               }

               label699: {
                  Object this$isFullTransfer = this.getIsFullTransfer();
                  Object other$isFullTransfer = other.getIsFullTransfer();
                  if (this$isFullTransfer == null) {
                     if (other$isFullTransfer == null) {
                        break label699;
                     }
                  } else if (this$isFullTransfer.equals(other$isFullTransfer)) {
                     break label699;
                  }

                  return false;
               }

               Object this$tohonorDate = this.getTohonorDate();
               Object other$tohonorDate = other.getTohonorDate();
               if (this$tohonorDate == null) {
                  if (other$tohonorDate != null) {
                     return false;
                  }
               } else if (!this$tohonorDate.equals(other$tohonorDate)) {
                  return false;
               }

               label685: {
                  Object this$tohonorRate = this.getTohonorRate();
                  Object other$tohonorRate = other.getTohonorRate();
                  if (this$tohonorRate == null) {
                     if (other$tohonorRate == null) {
                        break label685;
                     }
                  } else if (this$tohonorRate.equals(other$tohonorRate)) {
                     break label685;
                  }

                  return false;
               }

               label678: {
                  Object this$trfOutFeeAmt = this.getTrfOutFeeAmt();
                  Object other$trfOutFeeAmt = other.getTrfOutFeeAmt();
                  if (this$trfOutFeeAmt == null) {
                     if (other$trfOutFeeAmt == null) {
                        break label678;
                     }
                  } else if (this$trfOutFeeAmt.equals(other$trfOutFeeAmt)) {
                     break label678;
                  }

                  return false;
               }

               Object this$email = this.getEmail();
               Object other$email = other.getEmail();
               if (this$email == null) {
                  if (other$email != null) {
                     return false;
                  }
               } else if (!this$email.equals(other$email)) {
                  return false;
               }

               label664: {
                  Object this$redemptionIntType = this.getRedemptionIntType();
                  Object other$redemptionIntType = other.getRedemptionIntType();
                  if (this$redemptionIntType == null) {
                     if (other$redemptionIntType == null) {
                        break label664;
                     }
                  } else if (this$redemptionIntType.equals(other$redemptionIntType)) {
                     break label664;
                  }

                  return false;
               }

               label657: {
                  Object this$trfInFeeType = this.getTrfInFeeType();
                  Object other$trfInFeeType = other.getTrfInFeeType();
                  if (this$trfInFeeType == null) {
                     if (other$trfInFeeType == null) {
                        break label657;
                     }
                  } else if (this$trfInFeeType.equals(other$trfInFeeType)) {
                     break label657;
                  }

                  return false;
               }

               Object this$trfOutFeeType = this.getTrfOutFeeType();
               Object other$trfOutFeeType = other.getTrfOutFeeType();
               if (this$trfOutFeeType == null) {
                  if (other$trfOutFeeType != null) {
                     return false;
                  }
               } else if (!this$trfOutFeeType.equals(other$trfOutFeeType)) {
                  return false;
               }

               Object this$inFee = this.getInFee();
               Object other$inFee = other.getInFee();
               if (this$inFee == null) {
                  if (other$inFee != null) {
                     return false;
                  }
               } else if (!this$inFee.equals(other$inFee)) {
                  return false;
               }

               label636: {
                  Object this$isCashFromInnerAcct = this.getIsCashFromInnerAcct();
                  Object other$isCashFromInnerAcct = other.getIsCashFromInnerAcct();
                  if (this$isCashFromInnerAcct == null) {
                     if (other$isCashFromInnerAcct == null) {
                        break label636;
                     }
                  } else if (this$isCashFromInnerAcct.equals(other$isCashFromInnerAcct)) {
                     break label636;
                  }

                  return false;
               }

               Object this$tranPasswordOperateType = this.getTranPasswordOperateType();
               Object other$tranPasswordOperateType = other.getTranPasswordOperateType();
               if (this$tranPasswordOperateType == null) {
                  if (other$tranPasswordOperateType != null) {
                     return false;
                  }
               } else if (!this$tranPasswordOperateType.equals(other$tranPasswordOperateType)) {
                  return false;
               }

               Object this$minChangeBalance = this.getMinChangeBalance();
               Object other$minChangeBalance = other.getMinChangeBalance();
               if (this$minChangeBalance == null) {
                  if (other$minChangeBalance != null) {
                     return false;
                  }
               } else if (!this$minChangeBalance.equals(other$minChangeBalance)) {
                  return false;
               }

               label615: {
                  Object this$prodType = this.getProdType();
                  Object other$prodType = other.getProdType();
                  if (this$prodType == null) {
                     if (other$prodType == null) {
                        break label615;
                     }
                  } else if (this$prodType.equals(other$prodType)) {
                     break label615;
                  }

                  return false;
               }

               Object this$ccy = this.getCcy();
               Object other$ccy = other.getCcy();
               if (this$ccy == null) {
                  if (other$ccy != null) {
                     return false;
                  }
               } else if (!this$ccy.equals(other$ccy)) {
                  return false;
               }

               label601: {
                  Object this$intStartDate = this.getIntStartDate();
                  Object other$intStartDate = other.getIntStartDate();
                  if (this$intStartDate == null) {
                     if (other$intStartDate == null) {
                        break label601;
                     }
                  } else if (this$intStartDate.equals(other$intStartDate)) {
                     break label601;
                  }

                  return false;
               }

               Object this$matureDate = this.getMatureDate();
               Object other$matureDate = other.getMatureDate();
               if (this$matureDate == null) {
                  if (other$matureDate != null) {
                     return false;
                  }
               } else if (!this$matureDate.equals(other$matureDate)) {
                  return false;
               }

               label587: {
                  Object this$inlandOffshore = this.getInlandOffshore();
                  Object other$inlandOffshore = other.getInlandOffshore();
                  if (this$inlandOffshore == null) {
                     if (other$inlandOffshore == null) {
                        break label587;
                     }
                  } else if (this$inlandOffshore.equals(other$inlandOffshore)) {
                     break label587;
                  }

                  return false;
               }

               Object this$termType = this.getTermType();
               Object other$termType = other.getTermType();
               if (this$termType == null) {
                  if (other$termType != null) {
                     return false;
                  }
               } else if (!this$termType.equals(other$termType)) {
                  return false;
               }

               label573: {
                  Object this$stageCode = this.getStageCode();
                  Object other$stageCode = other.getStageCode();
                  if (this$stageCode == null) {
                     if (other$stageCode == null) {
                        break label573;
                     }
                  } else if (this$stageCode.equals(other$stageCode)) {
                     break label573;
                  }

                  return false;
               }

               label566: {
                  Object this$stageCodeDesc = this.getStageCodeDesc();
                  Object other$stageCodeDesc = other.getStageCodeDesc();
                  if (this$stageCodeDesc == null) {
                     if (other$stageCodeDesc == null) {
                        break label566;
                     }
                  } else if (this$stageCodeDesc.equals(other$stageCodeDesc)) {
                     break label566;
                  }

                  return false;
               }

               Object this$cycleFreq = this.getCycleFreq();
               Object other$cycleFreq = other.getCycleFreq();
               if (this$cycleFreq == null) {
                  if (other$cycleFreq != null) {
                     return false;
                  }
               } else if (!this$cycleFreq.equals(other$cycleFreq)) {
                  return false;
               }

               label552: {
                  Object this$intStartFlag = this.getIntStartFlag();
                  Object other$intStartFlag = other.getIntStartFlag();
                  if (this$intStartFlag == null) {
                     if (other$intStartFlag == null) {
                        break label552;
                     }
                  } else if (this$intStartFlag.equals(other$intStartFlag)) {
                     break label552;
                  }

                  return false;
               }

               label545: {
                  Object this$directionChargeIntFlag = this.getDirectionChargeIntFlag();
                  Object other$directionChargeIntFlag = other.getDirectionChargeIntFlag();
                  if (this$directionChargeIntFlag == null) {
                     if (other$directionChargeIntFlag == null) {
                        break label545;
                     }
                  } else if (this$directionChargeIntFlag.equals(other$directionChargeIntFlag)) {
                     break label545;
                  }

                  return false;
               }

               Object this$clientListArray = this.getClientListArray();
               Object other$clientListArray = other.getClientListArray();
               if (this$clientListArray == null) {
                  if (other$clientListArray != null) {
                     return false;
                  }
               } else if (!this$clientListArray.equals(other$clientListArray)) {
                  return false;
               }

               Object this$calmDays = this.getCalmDays();
               Object other$calmDays = other.getCalmDays();
               if (this$calmDays == null) {
                  if (other$calmDays != null) {
                     return false;
                  }
               } else if (!this$calmDays.equals(other$calmDays)) {
                  return false;
               }

               label524: {
                  Object this$channelArray = this.getChannelArray();
                  Object other$channelArray = other.getChannelArray();
                  if (this$channelArray == null) {
                     if (other$channelArray == null) {
                        break label524;
                     }
                  } else if (this$channelArray.equals(other$channelArray)) {
                     break label524;
                  }

                  return false;
               }

               Object this$customerBase = this.getCustomerBase();
               Object other$customerBase = other.getCustomerBase();
               if (this$customerBase == null) {
                  if (other$customerBase != null) {
                     return false;
                  }
               } else if (!this$customerBase.equals(other$customerBase)) {
                  return false;
               }

               Object this$fixedRate = this.getFixedRate();
               Object other$fixedRate = other.getFixedRate();
               if (this$fixedRate == null) {
                  if (other$fixedRate != null) {
                     return false;
                  }
               } else if (!this$fixedRate.equals(other$fixedRate)) {
                  return false;
               }

               label503: {
                  Object this$allowBranch = this.getAllowBranch();
                  Object other$allowBranch = other.getAllowBranch();
                  if (this$allowBranch == null) {
                     if (other$allowBranch == null) {
                        break label503;
                     }
                  } else if (this$allowBranch.equals(other$allowBranch)) {
                     break label503;
                  }

                  return false;
               }

               Object this$allowAcctClass = this.getAllowAcctClass();
               Object other$allowAcctClass = other.getAllowAcctClass();
               if (this$allowAcctClass == null) {
                  if (other$allowAcctClass != null) {
                     return false;
                  }
               } else if (!this$allowAcctClass.equals(other$allowAcctClass)) {
                  return false;
               }

               label489: {
                  Object this$trfThruDate = this.getTrfThruDate();
                  Object other$trfThruDate = other.getTrfThruDate();
                  if (this$trfThruDate == null) {
                     if (other$trfThruDate == null) {
                        break label489;
                     }
                  } else if (this$trfThruDate.equals(other$trfThruDate)) {
                     break label489;
                  }

                  return false;
               }

               Object this$trfOrderThruDate = this.getTrfOrderThruDate();
               Object other$trfOrderThruDate = other.getTrfOrderThruDate();
               if (this$trfOrderThruDate == null) {
                  if (other$trfOrderThruDate != null) {
                     return false;
                  }
               } else if (!this$trfOrderThruDate.equals(other$trfOrderThruDate)) {
                  return false;
               }

               label475: {
                  Object this$customerBaseArrays = this.getCustomerBaseArrays();
                  Object other$customerBaseArrays = other.getCustomerBaseArrays();
                  if (this$customerBaseArrays == null) {
                     if (other$customerBaseArrays == null) {
                        break label475;
                     }
                  } else if (this$customerBaseArrays.equals(other$customerBaseArrays)) {
                     break label475;
                  }

                  return false;
               }

               Object this$newAndOldFundFlag = this.getNewAndOldFundFlag();
               Object other$newAndOldFundFlag = other.getNewAndOldFundFlag();
               if (this$newAndOldFundFlag == null) {
                  if (other$newAndOldFundFlag != null) {
                     return false;
                  }
               } else if (!this$newAndOldFundFlag.equals(other$newAndOldFundFlag)) {
                  return false;
               }

               label461: {
                  Object this$stageStatus = this.getStageStatus();
                  Object other$stageStatus = other.getStageStatus();
                  if (this$stageStatus == null) {
                     if (other$stageStatus == null) {
                        break label461;
                     }
                  } else if (this$stageStatus.equals(other$stageStatus)) {
                     break label461;
                  }

                  return false;
               }

               label454: {
                  Object this$stageCodeDescSecond = this.getStageCodeDescSecond();
                  Object other$stageCodeDescSecond = other.getStageCodeDescSecond();
                  if (this$stageCodeDescSecond == null) {
                     if (other$stageCodeDescSecond == null) {
                        break label454;
                     }
                  } else if (this$stageCodeDescSecond.equals(other$stageCodeDescSecond)) {
                     break label454;
                  }

                  return false;
               }

               Object this$stageRemarkSecond = this.getStageRemarkSecond();
               Object other$stageRemarkSecond = other.getStageRemarkSecond();
               if (this$stageRemarkSecond == null) {
                  if (other$stageRemarkSecond != null) {
                     return false;
                  }
               } else if (!this$stageRemarkSecond.equals(other$stageRemarkSecond)) {
                  return false;
               }

               label440: {
                  Object this$stageRemark = this.getStageRemark();
                  Object other$stageRemark = other.getStageRemark();
                  if (this$stageRemark == null) {
                     if (other$stageRemark == null) {
                        break label440;
                     }
                  } else if (this$stageRemark.equals(other$stageRemark)) {
                     break label440;
                  }

                  return false;
               }

               label433: {
                  Object this$realRate = this.getRealRate();
                  Object other$realRate = other.getRealRate();
                  if (this$realRate == null) {
                     if (other$realRate == null) {
                        break label433;
                     }
                  } else if (this$realRate.equals(other$realRate)) {
                     break label433;
                  }

                  return false;
               }

               Object this$customerBaseClass = this.getCustomerBaseClass();
               Object other$customerBaseClass = other.getCustomerBaseClass();
               if (this$customerBaseClass == null) {
                  if (other$customerBaseClass != null) {
                     return false;
                  }
               } else if (!this$customerBaseClass.equals(other$customerBaseClass)) {
                  return false;
               }

               Object this$stageCodeDescEn = this.getStageCodeDescEn();
               Object other$stageCodeDescEn = other.getStageCodeDescEn();
               if (this$stageCodeDescEn == null) {
                  if (other$stageCodeDescEn != null) {
                     return false;
                  }
               } else if (!this$stageCodeDescEn.equals(other$stageCodeDescEn)) {
                  return false;
               }

               label412: {
                  Object this$sellBranch = this.getSellBranch();
                  Object other$sellBranch = other.getSellBranch();
                  if (this$sellBranch == null) {
                     if (other$sellBranch == null) {
                        break label412;
                     }
                  } else if (this$sellBranch.equals(other$sellBranch)) {
                     break label412;
                  }

                  return false;
               }

               Object this$channel = this.getChannel();
               Object other$channel = other.getChannel();
               if (this$channel == null) {
                  if (other$channel != null) {
                     return false;
                  }
               } else if (!this$channel.equals(other$channel)) {
                  return false;
               }

               Object this$aupCycle = this.getAupCycle();
               Object other$aupCycle = other.getAupCycle();
               if (this$aupCycle == null) {
                  if (other$aupCycle != null) {
                     return false;
                  }
               } else if (!this$aupCycle.equals(other$aupCycle)) {
                  return false;
               }

               label391: {
                  Object this$redeemIntDate = this.getRedeemIntDate();
                  Object other$redeemIntDate = other.getRedeemIntDate();
                  if (this$redeemIntDate == null) {
                     if (other$redeemIntDate == null) {
                        break label391;
                     }
                  } else if (this$redeemIntDate.equals(other$redeemIntDate)) {
                     break label391;
                  }

                  return false;
               }

               Object this$captDate = this.getCaptDate();
               Object other$captDate = other.getCaptDate();
               if (this$captDate == null) {
                  if (other$captDate != null) {
                     return false;
                  }
               } else if (!this$captDate.equals(other$captDate)) {
                  return false;
               }

               label377: {
                  Object this$graceChargeIntFlag = this.getGraceChargeIntFlag();
                  Object other$graceChargeIntFlag = other.getGraceChargeIntFlag();
                  if (this$graceChargeIntFlag == null) {
                     if (other$graceChargeIntFlag == null) {
                        break label377;
                     }
                  } else if (this$graceChargeIntFlag.equals(other$graceChargeIntFlag)) {
                     break label377;
                  }

                  return false;
               }

               Object this$allowBuyWayCd = this.getAllowBuyWayCd();
               Object other$allowBuyWayCd = other.getAllowBuyWayCd();
               if (this$allowBuyWayCd == null) {
                  if (other$allowBuyWayCd != null) {
                     return false;
                  }
               } else if (!this$allowBuyWayCd.equals(other$allowBuyWayCd)) {
                  return false;
               }

               Object this$whiteSellFlag = this.getWhiteSellFlag();
               Object other$whiteSellFlag = other.getWhiteSellFlag();
               if (this$whiteSellFlag == null) {
                  if (other$whiteSellFlag == null) {
                     return true;
                  }
               } else if (this$whiteSellFlag.equals(other$whiteSellFlag)) {
                  return true;
               }

               return false;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1200100218In.Body;
      }
      public String toString() {
         return "Core1200100218In.Body(issueYear=" + this.getIssueYear() + ", clientType=" + this.getClientType() + ", startDate=" + this.getStartDate() + ", endDate=" + this.getEndDate() + ", intType=" + this.getIntType() + ", redemptionFlag=" + this.getRedemptionFlag() + ", intFlag=" + this.getIntFlag() + ", floatRate=" + this.getFloatRate() + ", operationType=" + this.getOperationType() + ", branch=" + this.getBranch() + ", issueAmt=" + this.getIssueAmt() + ", saleStartTime=" + this.getSaleStartTime() + ", saleEndTime=" + this.getSaleEndTime() + ", stageMinAmt=" + this.getStageMinAmt() + ", sgMaxAmt=" + this.getSgMaxAmt() + ", keepMinBal=" + this.getKeepMinBal() + ", term=" + this.getTerm() + ", payIntType=" + this.getPayIntType() + ", preWithdrawFlag=" + this.getPreWithdrawFlag() + ", preWithdrawNum=" + this.getPreWithdrawNum() + ", isFullTransfer=" + this.getIsFullTransfer() + ", tohonorDate=" + this.getTohonorDate() + ", tohonorRate=" + this.getTohonorRate() + ", trfOutFeeAmt=" + this.getTrfOutFeeAmt() + ", email=" + this.getEmail() + ", redemptionIntType=" + this.getRedemptionIntType() + ", trfInFeeType=" + this.getTrfInFeeType() + ", trfOutFeeType=" + this.getTrfOutFeeType() + ", inFee=" + this.getInFee() + ", isCashFromInnerAcct=" + this.getIsCashFromInnerAcct() + ", tranPasswordOperateType=" + this.getTranPasswordOperateType() + ", minChangeBalance=" + this.getMinChangeBalance() + ", prodType=" + this.getProdType() + ", ccy=" + this.getCcy() + ", intStartDate=" + this.getIntStartDate() + ", matureDate=" + this.getMatureDate() + ", inlandOffshore=" + this.getInlandOffshore() + ", termType=" + this.getTermType() + ", stageCode=" + this.getStageCode() + ", stageCodeDesc=" + this.getStageCodeDesc() + ", cycleFreq=" + this.getCycleFreq() + ", intStartFlag=" + this.getIntStartFlag() + ", directionChargeIntFlag=" + this.getDirectionChargeIntFlag() + ", clientListArray=" + this.getClientListArray() + ", calmDays=" + this.getCalmDays() + ", channelArray=" + this.getChannelArray() + ", customerBase=" + this.getCustomerBase() + ", fixedRate=" + this.getFixedRate() + ", allowBranch=" + this.getAllowBranch() + ", allowAcctClass=" + this.getAllowAcctClass() + ", trfThruDate=" + this.getTrfThruDate() + ", trfOrderThruDate=" + this.getTrfOrderThruDate() + ", customerBaseArrays=" + this.getCustomerBaseArrays() + ", newAndOldFundFlag=" + this.getNewAndOldFundFlag() + ", stageStatus=" + this.getStageStatus() + ", stageCodeDescSecond=" + this.getStageCodeDescSecond() + ", stageRemarkSecond=" + this.getStageRemarkSecond() + ", stageRemark=" + this.getStageRemark() + ", realRate=" + this.getRealRate() + ", customerBaseClass=" + this.getCustomerBaseClass() + ", stageCodeDescEn=" + this.getStageCodeDescEn() + ", sellBranch=" + this.getSellBranch() + ", channel=" + this.getChannel() + ", aupCycle=" + this.getAupCycle() + ", redeemIntDate=" + this.getRedeemIntDate() + ", captDate=" + this.getCaptDate() + ", graceChargeIntFlag=" + this.getGraceChargeIntFlag() + ", allowBuyWayCd=" + this.getAllowBuyWayCd() + ", whiteSellFlag=" + this.getWhiteSellFlag() + ")";
      }

      public static class CustomerBaseClass {
         @V(
            desc = "客户群体大类",
            notNull = false,
            length = "20",
            remark = "客户群体大类",
            maxSize = 20
         )
         private String customerParent;
         @V(
            desc = "客户群体小类",
            notNull = false,
            length = "20",
            remark = "客户群体小类",
            maxSize = 20
         )
         private String customerSon;

         public String getCustomerParent() {
            return this.customerParent;
         }

         public String getCustomerSon() {
            return this.customerSon;
         }

         public void setCustomerParent(String customerParent) {
            this.customerParent = customerParent;
         }

         public void setCustomerSon(String customerSon) {
            this.customerSon = customerSon;
         }

         public boolean equals(Object o) {
            if (o == this) {
               return true;
            } else if (!(o instanceof Core1200100218In.Body.CustomerBaseClass)) {
               return false;
            } else {
               Core1200100218In.Body.CustomerBaseClass other = (Core1200100218In.Body.CustomerBaseClass)o;
               if (!other.canEqual(this)) {
                  return false;
               } else {
                  Object this$customerParent = this.getCustomerParent();
                  Object other$customerParent = other.getCustomerParent();
                  if (this$customerParent == null) {
                     if (other$customerParent != null) {
                        return false;
                     }
                  } else if (!this$customerParent.equals(other$customerParent)) {
                     return false;
                  }

                  Object this$customerSon = this.getCustomerSon();
                  Object other$customerSon = other.getCustomerSon();
                  if (this$customerSon == null) {
                     if (other$customerSon != null) {
                        return false;
                     }
                  } else if (!this$customerSon.equals(other$customerSon)) {
                     return false;
                  }

                  return true;
               }
            }
         }

         protected boolean canEqual(Object other) {
            return other instanceof Core1200100218In.Body.CustomerBaseClass;
         }
         public String toString() {
            return "Core1200100218In.Body.CustomerBaseClass(customerParent=" + this.getCustomerParent() + ", customerSon=" + this.getCustomerSon() + ")";
         }
      }

      public static class CustomerBaseArrays {
         @V(
            desc = "客户群体",
            notNull = false,
            length = "20",
            remark = "客户群体",
            maxSize = 20
         )
         private String customerBase;
         @V(
            desc = "客户群体最高购买上限",
            notNull = false,
            length = "17",
            remark = "客户群体最高购买上限",
            decimalLength = 2,
            precision = 2
         )
         private BigDecimal customerMaxAmt;

         public String getCustomerBase() {
            return this.customerBase;
         }

         public BigDecimal getCustomerMaxAmt() {
            return this.customerMaxAmt;
         }

         public void setCustomerBase(String customerBase) {
            this.customerBase = customerBase;
         }

         public void setCustomerMaxAmt(BigDecimal customerMaxAmt) {
            this.customerMaxAmt = customerMaxAmt;
         }

         public boolean equals(Object o) {
            if (o == this) {
               return true;
            } else if (!(o instanceof Core1200100218In.Body.CustomerBaseArrays)) {
               return false;
            } else {
               Core1200100218In.Body.CustomerBaseArrays other = (Core1200100218In.Body.CustomerBaseArrays)o;
               if (!other.canEqual(this)) {
                  return false;
               } else {
                  Object this$customerBase = this.getCustomerBase();
                  Object other$customerBase = other.getCustomerBase();
                  if (this$customerBase == null) {
                     if (other$customerBase != null) {
                        return false;
                     }
                  } else if (!this$customerBase.equals(other$customerBase)) {
                     return false;
                  }

                  Object this$customerMaxAmt = this.getCustomerMaxAmt();
                  Object other$customerMaxAmt = other.getCustomerMaxAmt();
                  if (this$customerMaxAmt == null) {
                     if (other$customerMaxAmt != null) {
                        return false;
                     }
                  } else if (!this$customerMaxAmt.equals(other$customerMaxAmt)) {
                     return false;
                  }

                  return true;
               }
            }
         }

         protected boolean canEqual(Object other) {
            return other instanceof Core1200100218In.Body.CustomerBaseArrays;
         }
         public String toString() {
            return "Core1200100218In.Body.CustomerBaseArrays(customerBase=" + this.getCustomerBase() + ", customerMaxAmt=" + this.getCustomerMaxAmt() + ")";
         }
      }

      public static class ChannelArray {
         @V(
            desc = "渠道",
            notNull = false,
            length = "10",
            inDesc = "JZ-集中作业,MT-柜面,ZD-智能网点自助交易平台,VT-远程智能自助银行,QT-XBankT10,QE-快窗智能高柜,CP-手机银行,EB-个人网上银行,CB-企业网上银行,CM-现金管理,WB-微信银行A ,PT-支付",
            remark = "渠道细类",
            maxSize = 10
         )
         private String channel;

         public String getChannel() {
            return this.channel;
         }

         public void setChannel(String channel) {
            this.channel = channel;
         }

         public boolean equals(Object o) {
            if (o == this) {
               return true;
            } else if (!(o instanceof Core1200100218In.Body.ChannelArray)) {
               return false;
            } else {
               Core1200100218In.Body.ChannelArray other = (Core1200100218In.Body.ChannelArray)o;
               if (!other.canEqual(this)) {
                  return false;
               } else {
                  Object this$channel = this.getChannel();
                  Object other$channel = other.getChannel();
                  if (this$channel == null) {
                     if (other$channel != null) {
                        return false;
                     }
                  } else if (!this$channel.equals(other$channel)) {
                     return false;
                  }

                  return true;
               }
            }
         }

         protected boolean canEqual(Object other) {
            return other instanceof Core1200100218In.Body.ChannelArray;
         }
         public String toString() {
            return "Core1200100218In.Body.ChannelArray(channel=" + this.getChannel() + ")";
         }
      }

      public static class ClientListArray {
         @V(
            desc = "客户名称",
            notNull = false,
            length = "200",
            remark = "客户名称",
            maxSize = 200
         )
         private String clientName;
         @V(
            desc = "客户号",
            notNull = false,
            length = "20",
            remark = "客户号",
            maxSize = 20
         )
         private String clientNo;
         @V(
            desc = "证件号码",
            notNull = false,
            length = "50",
            remark = "证件号码",
            maxSize = 50
         )
         private String documentId;
         @V(
            desc = "证件类型",
            notNull = false,
            length = "3",
            remark = "证件类型",
            maxSize = 3
         )
         private String documentType;
         @V(
            desc = "是否白名单发售",
            notNull = false,
            length = "1",
            inDesc = "Y-是,N-否",
            remark = "是否白名单发售Y-是，N-否",
            maxSize = 1
         )
         private String whiteSellFlag;
         @V(
            desc = "对客户信息操作类型",
            notNull = false,
            length = "2",
            inDesc = "A-增,U-改,D-删",
            remark = "对客户信息操作类型",
            maxSize = 2
         )
         private String optionCli;

         public String getClientName() {
            return this.clientName;
         }

         public String getClientNo() {
            return this.clientNo;
         }

         public String getDocumentId() {
            return this.documentId;
         }

         public String getDocumentType() {
            return this.documentType;
         }

         public String getWhiteSellFlag() {
            return this.whiteSellFlag;
         }

         public String getOptionCli() {
            return this.optionCli;
         }

         public void setClientName(String clientName) {
            this.clientName = clientName;
         }

         public void setClientNo(String clientNo) {
            this.clientNo = clientNo;
         }

         public void setDocumentId(String documentId) {
            this.documentId = documentId;
         }

         public void setDocumentType(String documentType) {
            this.documentType = documentType;
         }

         public void setWhiteSellFlag(String whiteSellFlag) {
            this.whiteSellFlag = whiteSellFlag;
         }

         public void setOptionCli(String optionCli) {
            this.optionCli = optionCli;
         }

         public boolean equals(Object o) {
            if (o == this) {
               return true;
            } else if (!(o instanceof Core1200100218In.Body.ClientListArray)) {
               return false;
            } else {
               Core1200100218In.Body.ClientListArray other = (Core1200100218In.Body.ClientListArray)o;
               if (!other.canEqual(this)) {
                  return false;
               } else {
                  Object this$clientName = this.getClientName();
                  Object other$clientName = other.getClientName();
                  if (this$clientName == null) {
                     if (other$clientName != null) {
                        return false;
                     }
                  } else if (!this$clientName.equals(other$clientName)) {
                     return false;
                  }

                  Object this$clientNo = this.getClientNo();
                  Object other$clientNo = other.getClientNo();
                  if (this$clientNo == null) {
                     if (other$clientNo != null) {
                        return false;
                     }
                  } else if (!this$clientNo.equals(other$clientNo)) {
                     return false;
                  }

                  Object this$documentId = this.getDocumentId();
                  Object other$documentId = other.getDocumentId();
                  if (this$documentId == null) {
                     if (other$documentId != null) {
                        return false;
                     }
                  } else if (!this$documentId.equals(other$documentId)) {
                     return false;
                  }

                  label62: {
                     Object this$documentType = this.getDocumentType();
                     Object other$documentType = other.getDocumentType();
                     if (this$documentType == null) {
                        if (other$documentType == null) {
                           break label62;
                        }
                     } else if (this$documentType.equals(other$documentType)) {
                        break label62;
                     }

                     return false;
                  }

                  label55: {
                     Object this$whiteSellFlag = this.getWhiteSellFlag();
                     Object other$whiteSellFlag = other.getWhiteSellFlag();
                     if (this$whiteSellFlag == null) {
                        if (other$whiteSellFlag == null) {
                           break label55;
                        }
                     } else if (this$whiteSellFlag.equals(other$whiteSellFlag)) {
                        break label55;
                     }

                     return false;
                  }

                  Object this$optionCli = this.getOptionCli();
                  Object other$optionCli = other.getOptionCli();
                  if (this$optionCli == null) {
                     if (other$optionCli != null) {
                        return false;
                     }
                  } else if (!this$optionCli.equals(other$optionCli)) {
                     return false;
                  }

                  return true;
               }
            }
         }

         protected boolean canEqual(Object other) {
            return other instanceof Core1200100218In.Body.ClientListArray;
         }
         public String toString() {
            return "Core1200100218In.Body.ClientListArray(clientName=" + this.getClientName() + ", clientNo=" + this.getClientNo() + ", documentId=" + this.getDocumentId() + ", documentType=" + this.getDocumentType() + ", whiteSellFlag=" + this.getWhiteSellFlag() + ", optionCli=" + this.getOptionCli() + ")";
         }
      }
   }
}
