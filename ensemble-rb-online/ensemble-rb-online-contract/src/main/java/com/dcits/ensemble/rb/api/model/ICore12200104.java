package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12200104In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12200104Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12200104 {
   String URL = "/rb/file/acct/total/check";


   @ApiRemark("标准优化")
   @ApiDesc("由核算批处理系统发起，上送产品、币种、机构信息，接口生成批次号插入分户级总分核对登记簿中，调起相关批量任务生成分户级总分核对文件后再通知核算系统，该接口主要用于日终总分核对不平时，核算用于检查账户数据时使用")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1220",
      messageCode = "0104"
   )
   @BusinessCategory("1200-文件")
   @FunctionCategory("RB21-风险管理")
   @ConsumeSys("GLS")
   @ApiUseStatus("PRODUCT-产品")
   Core12200104Out runService(Core12200104In var1);
}
