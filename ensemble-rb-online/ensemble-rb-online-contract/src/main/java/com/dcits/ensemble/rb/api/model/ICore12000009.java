package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000009In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000009Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12000009 {
   String URL = "/rb/nfin/file/note";


   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0009"
   )
   @FunctionCategory("RB10-大额存单")
   @ConsumeSys("CBS")
   @ApiUseStatus("PRODUCT-产品")
   Core12000009Out runService(Core12000009In var1);
}
