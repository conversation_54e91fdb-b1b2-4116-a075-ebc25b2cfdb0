package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;

@MessageOut
public class Core1400100241Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "产品类型",
      notNull = false,
      length = "20",
      remark = "产品类型",
      maxSize = 20
   )
   private String prodType;
   @V(
      desc = "生效日期",
      notNull = false,
      remark = "生效日期"
   )
   private String effectDate;
   @V(
      desc = "到期日",
      notNull = false,
      remark = "到期日"
   )
   private String matureDate;
   @V(
      desc = "付息方式",
      notNull = false,
      length = "3",
      remark = "付息方式",
      maxSize = 3
   )
   private String payIntType;
   @V(
      desc = "是否可赎回",
      notNull = false,
      length = "1",
      remark = "是否可赎回",
      maxSize = 1
   )
   private String redemptionFlag;
   @V(
      desc = "总余额",
      notNull = false,
      length = "17",
      remark = "总余额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal totalBal;
   @V(
      desc = "账户条数",
      notNull = false,
      length = "5",
      remark = "账户条数"
   )
   private Integer acctCount;
   @V(
      desc = "是否开立纸质存单",
      notNull = false,
      length = "1",
      remark = "是否开立纸质存单",
      maxSize = 1
   )
   private String printCdFlag;
   @V(
      desc = "开立纸质存单金额",
      notNull = false,
      length = "17",
      remark = "开立纸质存单金额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal printCdAmt;
   @V(
      desc = "账户余额止付标志",
      notNull = false,
      length = "1",
      remark = "描述账户在受到余额止付控制的状态",
      maxSize = 1
   )
   private String acctStopPay;
   @V(
      desc = "冻结止付总笔数",
      notNull = false,
      length = "10",
      remark = "冻结止付总笔数"
   )
   private Long stopCount;
   @V(
      desc = "止付金额",
      notNull = false,
      length = "17",
      remark = "止付金额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal restraintAmt;
   @V(
      desc = "是否存在无法入账状态",
      notNull = false,
      length = "1",
      remark = "是否存在无法入账状态",
      maxSize = 1
   )
   private String inErrorFlag;
   @V(
      desc = "无法入账总户数",
      notNull = false,
      length = "10",
      remark = "无法入账总户数"
   )
   private Long inErrorCount;
   @V(
      desc = "质押标志",
      notNull = false,
      length = "1",
      remark = "质押标志",
      maxSize = 1
   )
   private String pledgedFlag;
   @V(
      desc = "质押总笔数",
      notNull = false,
      length = "10",
      remark = "质押总笔数"
   )
   private Long pledgedCount;
   @V(
      desc = "质押总金额",
      notNull = false,
      length = "17",
      remark = "质押总金额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal pledgedAmount;
   @V(
      desc = "期次描述",
      notNull = false,
      length = "200",
      remark = "期次描述",
      maxSize = 200
   )
   private String stageCodeDesc;
   @V(
      desc = "质押总笔数",
      notNull = false,
      length = "10",
      remark = "质押总笔数",
      maxSize = 10
   )
   private String mortgageNumber;
   @V(
      desc = "持有客户数",
      notNull = false,
      length = "10",
      remark = "持有客户数",
      maxSize = 10
   )
   private String clientNumber;

   public String getProdType() {
      return this.prodType;
   }

   public String getEffectDate() {
      return this.effectDate;
   }

   public String getMatureDate() {
      return this.matureDate;
   }

   public String getPayIntType() {
      return this.payIntType;
   }

   public String getRedemptionFlag() {
      return this.redemptionFlag;
   }

   public BigDecimal getTotalBal() {
      return this.totalBal;
   }

   public Integer getAcctCount() {
      return this.acctCount;
   }

   public String getPrintCdFlag() {
      return this.printCdFlag;
   }

   public BigDecimal getPrintCdAmt() {
      return this.printCdAmt;
   }

   public String getAcctStopPay() {
      return this.acctStopPay;
   }

   public Long getStopCount() {
      return this.stopCount;
   }

   public BigDecimal getRestraintAmt() {
      return this.restraintAmt;
   }

   public String getInErrorFlag() {
      return this.inErrorFlag;
   }

   public Long getInErrorCount() {
      return this.inErrorCount;
   }

   public String getPledgedFlag() {
      return this.pledgedFlag;
   }

   public Long getPledgedCount() {
      return this.pledgedCount;
   }

   public BigDecimal getPledgedAmount() {
      return this.pledgedAmount;
   }

   public String getStageCodeDesc() {
      return this.stageCodeDesc;
   }

   public String getMortgageNumber() {
      return this.mortgageNumber;
   }

   public String getClientNumber() {
      return this.clientNumber;
   }

   public void setProdType(String prodType) {
      this.prodType = prodType;
   }

   public void setEffectDate(String effectDate) {
      this.effectDate = effectDate;
   }

   public void setMatureDate(String matureDate) {
      this.matureDate = matureDate;
   }

   public void setPayIntType(String payIntType) {
      this.payIntType = payIntType;
   }

   public void setRedemptionFlag(String redemptionFlag) {
      this.redemptionFlag = redemptionFlag;
   }

   public void setTotalBal(BigDecimal totalBal) {
      this.totalBal = totalBal;
   }

   public void setAcctCount(Integer acctCount) {
      this.acctCount = acctCount;
   }

   public void setPrintCdFlag(String printCdFlag) {
      this.printCdFlag = printCdFlag;
   }

   public void setPrintCdAmt(BigDecimal printCdAmt) {
      this.printCdAmt = printCdAmt;
   }

   public void setAcctStopPay(String acctStopPay) {
      this.acctStopPay = acctStopPay;
   }

   public void setStopCount(Long stopCount) {
      this.stopCount = stopCount;
   }

   public void setRestraintAmt(BigDecimal restraintAmt) {
      this.restraintAmt = restraintAmt;
   }

   public void setInErrorFlag(String inErrorFlag) {
      this.inErrorFlag = inErrorFlag;
   }

   public void setInErrorCount(Long inErrorCount) {
      this.inErrorCount = inErrorCount;
   }

   public void setPledgedFlag(String pledgedFlag) {
      this.pledgedFlag = pledgedFlag;
   }

   public void setPledgedCount(Long pledgedCount) {
      this.pledgedCount = pledgedCount;
   }

   public void setPledgedAmount(BigDecimal pledgedAmount) {
      this.pledgedAmount = pledgedAmount;
   }

   public void setStageCodeDesc(String stageCodeDesc) {
      this.stageCodeDesc = stageCodeDesc;
   }

   public void setMortgageNumber(String mortgageNumber) {
      this.mortgageNumber = mortgageNumber;
   }

   public void setClientNumber(String clientNumber) {
      this.clientNumber = clientNumber;
   }

   public String toString() {
      return "Core1400100241Out(prodType=" + this.getProdType() + ", effectDate=" + this.getEffectDate() + ", matureDate=" + this.getMatureDate() + ", payIntType=" + this.getPayIntType() + ", redemptionFlag=" + this.getRedemptionFlag() + ", totalBal=" + this.getTotalBal() + ", acctCount=" + this.getAcctCount() + ", printCdFlag=" + this.getPrintCdFlag() + ", printCdAmt=" + this.getPrintCdAmt() + ", acctStopPay=" + this.getAcctStopPay() + ", stopCount=" + this.getStopCount() + ", restraintAmt=" + this.getRestraintAmt() + ", inErrorFlag=" + this.getInErrorFlag() + ", inErrorCount=" + this.getInErrorCount() + ", pledgedFlag=" + this.getPledgedFlag() + ", pledgedCount=" + this.getPledgedCount() + ", pledgedAmount=" + this.getPledgedAmount() + ", stageCodeDesc=" + this.getStageCodeDesc() + ", mortgageNumber=" + this.getMortgageNumber() + ", clientNumber=" + this.getClientNumber() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100241Out)) {
         return false;
      } else {
         Core1400100241Out other = (Core1400100241Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            label253: {
               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType == null) {
                     break label253;
                  }
               } else if (this$prodType.equals(other$prodType)) {
                  break label253;
               }

               return false;
            }

            label246: {
               Object this$effectDate = this.getEffectDate();
               Object other$effectDate = other.getEffectDate();
               if (this$effectDate == null) {
                  if (other$effectDate == null) {
                     break label246;
                  }
               } else if (this$effectDate.equals(other$effectDate)) {
                  break label246;
               }

               return false;
            }

            Object this$matureDate = this.getMatureDate();
            Object other$matureDate = other.getMatureDate();
            if (this$matureDate == null) {
               if (other$matureDate != null) {
                  return false;
               }
            } else if (!this$matureDate.equals(other$matureDate)) {
               return false;
            }

            label232: {
               Object this$payIntType = this.getPayIntType();
               Object other$payIntType = other.getPayIntType();
               if (this$payIntType == null) {
                  if (other$payIntType == null) {
                     break label232;
                  }
               } else if (this$payIntType.equals(other$payIntType)) {
                  break label232;
               }

               return false;
            }

            Object this$redemptionFlag = this.getRedemptionFlag();
            Object other$redemptionFlag = other.getRedemptionFlag();
            if (this$redemptionFlag == null) {
               if (other$redemptionFlag != null) {
                  return false;
               }
            } else if (!this$redemptionFlag.equals(other$redemptionFlag)) {
               return false;
            }

            label218: {
               Object this$totalBal = this.getTotalBal();
               Object other$totalBal = other.getTotalBal();
               if (this$totalBal == null) {
                  if (other$totalBal == null) {
                     break label218;
                  }
               } else if (this$totalBal.equals(other$totalBal)) {
                  break label218;
               }

               return false;
            }

            Object this$acctCount = this.getAcctCount();
            Object other$acctCount = other.getAcctCount();
            if (this$acctCount == null) {
               if (other$acctCount != null) {
                  return false;
               }
            } else if (!this$acctCount.equals(other$acctCount)) {
               return false;
            }

            Object this$printCdFlag = this.getPrintCdFlag();
            Object other$printCdFlag = other.getPrintCdFlag();
            if (this$printCdFlag == null) {
               if (other$printCdFlag != null) {
                  return false;
               }
            } else if (!this$printCdFlag.equals(other$printCdFlag)) {
               return false;
            }

            Object this$printCdAmt = this.getPrintCdAmt();
            Object other$printCdAmt = other.getPrintCdAmt();
            if (this$printCdAmt == null) {
               if (other$printCdAmt != null) {
                  return false;
               }
            } else if (!this$printCdAmt.equals(other$printCdAmt)) {
               return false;
            }

            label190: {
               Object this$acctStopPay = this.getAcctStopPay();
               Object other$acctStopPay = other.getAcctStopPay();
               if (this$acctStopPay == null) {
                  if (other$acctStopPay == null) {
                     break label190;
                  }
               } else if (this$acctStopPay.equals(other$acctStopPay)) {
                  break label190;
               }

               return false;
            }

            label183: {
               Object this$stopCount = this.getStopCount();
               Object other$stopCount = other.getStopCount();
               if (this$stopCount == null) {
                  if (other$stopCount == null) {
                     break label183;
                  }
               } else if (this$stopCount.equals(other$stopCount)) {
                  break label183;
               }

               return false;
            }

            Object this$restraintAmt = this.getRestraintAmt();
            Object other$restraintAmt = other.getRestraintAmt();
            if (this$restraintAmt == null) {
               if (other$restraintAmt != null) {
                  return false;
               }
            } else if (!this$restraintAmt.equals(other$restraintAmt)) {
               return false;
            }

            label169: {
               Object this$inErrorFlag = this.getInErrorFlag();
               Object other$inErrorFlag = other.getInErrorFlag();
               if (this$inErrorFlag == null) {
                  if (other$inErrorFlag == null) {
                     break label169;
                  }
               } else if (this$inErrorFlag.equals(other$inErrorFlag)) {
                  break label169;
               }

               return false;
            }

            label162: {
               Object this$inErrorCount = this.getInErrorCount();
               Object other$inErrorCount = other.getInErrorCount();
               if (this$inErrorCount == null) {
                  if (other$inErrorCount == null) {
                     break label162;
                  }
               } else if (this$inErrorCount.equals(other$inErrorCount)) {
                  break label162;
               }

               return false;
            }

            Object this$pledgedFlag = this.getPledgedFlag();
            Object other$pledgedFlag = other.getPledgedFlag();
            if (this$pledgedFlag == null) {
               if (other$pledgedFlag != null) {
                  return false;
               }
            } else if (!this$pledgedFlag.equals(other$pledgedFlag)) {
               return false;
            }

            Object this$pledgedCount = this.getPledgedCount();
            Object other$pledgedCount = other.getPledgedCount();
            if (this$pledgedCount == null) {
               if (other$pledgedCount != null) {
                  return false;
               }
            } else if (!this$pledgedCount.equals(other$pledgedCount)) {
               return false;
            }

            label141: {
               Object this$pledgedAmount = this.getPledgedAmount();
               Object other$pledgedAmount = other.getPledgedAmount();
               if (this$pledgedAmount == null) {
                  if (other$pledgedAmount == null) {
                     break label141;
                  }
               } else if (this$pledgedAmount.equals(other$pledgedAmount)) {
                  break label141;
               }

               return false;
            }

            label134: {
               Object this$stageCodeDesc = this.getStageCodeDesc();
               Object other$stageCodeDesc = other.getStageCodeDesc();
               if (this$stageCodeDesc == null) {
                  if (other$stageCodeDesc == null) {
                     break label134;
                  }
               } else if (this$stageCodeDesc.equals(other$stageCodeDesc)) {
                  break label134;
               }

               return false;
            }

            Object this$mortgageNumber = this.getMortgageNumber();
            Object other$mortgageNumber = other.getMortgageNumber();
            if (this$mortgageNumber == null) {
               if (other$mortgageNumber != null) {
                  return false;
               }
            } else if (!this$mortgageNumber.equals(other$mortgageNumber)) {
               return false;
            }

            Object this$clientNumber = this.getClientNumber();
            Object other$clientNumber = other.getClientNumber();
            if (this$clientNumber == null) {
               if (other$clientNumber != null) {
                  return false;
               }
            } else if (!this$clientNumber.equals(other$clientNumber)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100241Out;
   }
}
