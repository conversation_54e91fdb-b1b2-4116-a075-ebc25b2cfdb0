package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;

@MessageOut
public class Core1200100002Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "费用计提编号",
      notNull = false,
      length = "50",
      remark = "费用计提编号",
      maxSize = 50
   )
   private String feeIntNo;

   public String getFeeIntNo() {
      return this.feeIntNo;
   }

   public void setFeeIntNo(String feeIntNo) {
      this.feeIntNo = feeIntNo;
   }

   public String toString() {
      return "Core1200100002Out(feeIntNo=" + this.getFeeIntNo() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200100002Out)) {
         return false;
      } else {
         Core1200100002Out other = (Core1200100002Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$feeIntNo = this.getFeeIntNo();
            Object other$feeIntNo = other.getFeeIntNo();
            if (this$feeIntNo == null) {
               if (other$feeIntNo != null) {
                  return false;
               }
            } else if (!this$feeIntNo.equals(other$feeIntNo)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200100002Out;
   }
}
