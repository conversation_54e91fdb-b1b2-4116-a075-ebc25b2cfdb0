package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000137In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000137Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000137 {
   String URL = "/rb/inq/interest/adjinfo";


   @ApiRemark("华兴项目组新增用于查询账户计提调整记录包括计提金额调整和积数调增")
   @ApiDesc("华兴项目组新增用于查询账户计提调整记录包括计提金额调整和积数调增")
   @EnsembleElements(
      serviceCode = "Core",
      messageType = "1400",
      messageCode = "0137"
   )
   @BusinessCategory("华兴项目组新增用于查询账户计提调整记录包")
   @FunctionCategory("RB04-计结息")
   @ConsumeSys("TLE")
   @ApiUseStatus("PRODUCT-产品")
   Core14000137Out runService(Core14000137In var1);
}
