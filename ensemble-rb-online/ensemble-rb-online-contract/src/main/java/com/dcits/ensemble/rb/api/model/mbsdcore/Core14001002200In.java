package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core14001002200In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core14001002200In.Body body;

   public Core14001002200In.Body getBody() {
      return this.body;
   }

   public void setBody(Core14001002200In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core14001002200In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core14001002200In)) {
         return false;
      } else {
         Core14001002200In other = (Core14001002200In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core14001002200In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "发行年度",
         notNull = true,
         length = "5",
         remark = "发行年度",
         maxSize = 5
      )
      private String issueYear;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;

      public String getIssueYear() {
         return this.issueYear;
      }

      public String getCcy() {
         return this.ccy;
      }

      public void setIssueYear(String issueYear) {
         this.issueYear = issueYear;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core14001002200In.Body)) {
            return false;
         } else {
            Core14001002200In.Body other = (Core14001002200In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               Object this$issueYear = this.getIssueYear();
               Object other$issueYear = other.getIssueYear();
               if (this$issueYear == null) {
                  if (other$issueYear != null) {
                     return false;
                  }
               } else if (!this$issueYear.equals(other$issueYear)) {
                  return false;
               }

               Object this$ccy = this.getCcy();
               Object other$ccy = other.getCcy();
               if (this$ccy == null) {
                  if (other$ccy != null) {
                     return false;
                  }
               } else if (!this$ccy.equals(other$ccy)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core14001002200In.Body;
      }
      public String toString() {
         return "Core14001002200In.Body(issueYear=" + this.getIssueYear() + ", ccy=" + this.getCcy() + ")";
      }
   }
}
