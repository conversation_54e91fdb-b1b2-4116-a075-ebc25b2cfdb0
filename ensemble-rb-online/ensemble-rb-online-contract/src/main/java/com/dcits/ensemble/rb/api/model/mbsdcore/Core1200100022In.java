package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1200100022In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1200100022In.Body body;

   public Core1200100022In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1200100022In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1200100022In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1200100022In)) {
         return false;
      } else {
         Core1200100022In other = (Core1200100022In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1200100022In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "币种",
         notNull = false,
         length = "3",
         remark = "币种",
         maxSize = 3
      )
      private String ccy;
      @V(
         desc = "设置类型",
         notNull = false,
         length = "2",
         remark = "设置类型",
         maxSize = 2
      )
      private String msgSetType;
      @V(
         desc = "开关状态",
         notNull = false,
         length = "1",
         inDesc = "O-打开,S-关闭",
         remark = "开关状态",
         maxSize = 1
      )
      private String tbSwitchState;

      public String getClientNo() {
         return this.clientNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getCcy() {
         return this.ccy;
      }

      public String getMsgSetType() {
         return this.msgSetType;
      }

      public String getTbSwitchState() {
         return this.tbSwitchState;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setCcy(String ccy) {
         this.ccy = ccy;
      }

      public void setMsgSetType(String msgSetType) {
         this.msgSetType = msgSetType;
      }

      public void setTbSwitchState(String tbSwitchState) {
         this.tbSwitchState = tbSwitchState;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1200100022In.Body)) {
            return false;
         } else {
            Core1200100022In.Body other = (Core1200100022In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label95: {
                  Object this$clientNo = this.getClientNo();
                  Object other$clientNo = other.getClientNo();
                  if (this$clientNo == null) {
                     if (other$clientNo == null) {
                        break label95;
                     }
                  } else if (this$clientNo.equals(other$clientNo)) {
                     break label95;
                  }

                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo != null) {
                     return false;
                  }
               } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
                  return false;
               }

               label74: {
                  Object this$acctSeqNo = this.getAcctSeqNo();
                  Object other$acctSeqNo = other.getAcctSeqNo();
                  if (this$acctSeqNo == null) {
                     if (other$acctSeqNo == null) {
                        break label74;
                     }
                  } else if (this$acctSeqNo.equals(other$acctSeqNo)) {
                     break label74;
                  }

                  return false;
               }

               label67: {
                  Object this$ccy = this.getCcy();
                  Object other$ccy = other.getCcy();
                  if (this$ccy == null) {
                     if (other$ccy == null) {
                        break label67;
                     }
                  } else if (this$ccy.equals(other$ccy)) {
                     break label67;
                  }

                  return false;
               }

               Object this$msgSetType = this.getMsgSetType();
               Object other$msgSetType = other.getMsgSetType();
               if (this$msgSetType == null) {
                  if (other$msgSetType != null) {
                     return false;
                  }
               } else if (!this$msgSetType.equals(other$msgSetType)) {
                  return false;
               }

               Object this$tbSwitchState = this.getTbSwitchState();
               Object other$tbSwitchState = other.getTbSwitchState();
               if (this$tbSwitchState == null) {
                  if (other$tbSwitchState != null) {
                     return false;
                  }
               } else if (!this$tbSwitchState.equals(other$tbSwitchState)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1200100022In.Body;
      }
      public String toString() {
         return "Core1200100022In.Body(clientNo=" + this.getClientNo() + ", prodType=" + this.getProdType() + ", baseAcctNo=" + this.getBaseAcctNo() + ", acctSeqNo=" + this.getAcctSeqNo() + ", ccy=" + this.getCcy() + ", msgSetType=" + this.getMsgSetType() + ", tbSwitchState=" + this.getTbSwitchState() + ")";
      }
   }
}
