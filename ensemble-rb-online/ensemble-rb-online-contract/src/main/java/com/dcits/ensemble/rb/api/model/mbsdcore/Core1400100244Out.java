package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;

@MessageOut
public class Core1400100244Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "总金额",
      notNull = false,
      length = "17",
      remark = "总金额",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal totalAmt;

   public BigDecimal getTotalAmt() {
      return this.totalAmt;
   }

   public void setTotalAmt(BigDecimal totalAmt) {
      this.totalAmt = totalAmt;
   }

   public String toString() {
      return "Core1400100244Out(totalAmt=" + this.getTotalAmt() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100244Out)) {
         return false;
      } else {
         Core1400100244Out other = (Core1400100244Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$totalAmt = this.getTotalAmt();
            Object other$totalAmt = other.getTotalAmt();
            if (this$totalAmt == null) {
               if (other$totalAmt != null) {
                  return false;
               }
            } else if (!this$totalAmt.equals(other$totalAmt)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100244Out;
   }
}
