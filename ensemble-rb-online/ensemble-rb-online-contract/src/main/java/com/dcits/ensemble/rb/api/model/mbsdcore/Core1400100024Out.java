package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.util.List;

@MessageOut
public class Core1400100024Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100024Out.AcctArray> acctArray;

   public List<Core1400100024Out.AcctArray> getAcctArray() {
      return this.acctArray;
   }

   public void setAcctArray(List<Core1400100024Out.AcctArray> acctArray) {
      this.acctArray = acctArray;
   }

   public String toString() {
      return "Core1400100024Out(acctArray=" + this.getAcctArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100024Out)) {
         return false;
      } else {
         Core1400100024Out other = (Core1400100024Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$acctArray = this.getAcctArray();
            Object other$acctArray = other.getAcctArray();
            if (this$acctArray == null) {
               if (other$acctArray != null) {
                  return false;
               }
            } else if (!this$acctArray.equals(other$acctArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100024Out;
   }
   public static class AcctArray {
      @V(
         desc = "客户号",
         notNull = false,
         length = "20",
         remark = "客户号",
         maxSize = 20
      )
      private String clientNo;
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "暂记非柜面限制类型",
         notNull = false,
         length = "3",
         remark = "暂记非柜面限制类型",
         maxSize = 3
      )
      private String uncounterRestraintType;
      @V(
         desc = "上一操作时间",
         notNull = false,
         length = "26",
         remark = "上一操作时间",
         maxSize = 26
      )
      private String lastTimeStamp;
      @V(
         desc = "操作柜员/接收柜员",
         notNull = false,
         length = "50",
         remark = "操作柜员/接收柜员",
         maxSize = 50
      )
      private String tellername;
      @V(
         desc = "备注",
         notNull = false,
         length = "500",
         remark = "备注",
         maxSize = 500
      )
      private String narrative1;
      @V(
         desc = "客户涉案日期",
         notNull = false,
         remark = "客户涉案日期"
      )
      private String caseInvolvedDate;
      @V(
         desc = "涉案标识",
         notNull = false,
         length = "10",
         remark = "用于华兴银行涉案标识处理",
         maxSize = 10
      )
      private String caseInvolvedFlag;
      @V(
         desc = "客户名称",
         notNull = false,
         length = "200",
         remark = "客户名称",
         maxSize = 200
      )
      private String clientName;
      @V(
         desc = "交易渠道状态",
         notNull = false,
         length = "3",
         remark = "交易渠道状态",
         maxSize = 3
      )
      private String tranChannelStatus;

      public String getClientNo() {
         return this.clientNo;
      }

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getUncounterRestraintType() {
         return this.uncounterRestraintType;
      }

      public String getLastTimeStamp() {
         return this.lastTimeStamp;
      }

      public String getTellername() {
         return this.tellername;
      }

      public String getNarrative1() {
         return this.narrative1;
      }

      public String getCaseInvolvedDate() {
         return this.caseInvolvedDate;
      }

      public String getCaseInvolvedFlag() {
         return this.caseInvolvedFlag;
      }

      public String getClientName() {
         return this.clientName;
      }

      public String getTranChannelStatus() {
         return this.tranChannelStatus;
      }

      public void setClientNo(String clientNo) {
         this.clientNo = clientNo;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setUncounterRestraintType(String uncounterRestraintType) {
         this.uncounterRestraintType = uncounterRestraintType;
      }

      public void setLastTimeStamp(String lastTimeStamp) {
         this.lastTimeStamp = lastTimeStamp;
      }

      public void setTellername(String tellername) {
         this.tellername = tellername;
      }

      public void setNarrative1(String narrative1) {
         this.narrative1 = narrative1;
      }

      public void setCaseInvolvedDate(String caseInvolvedDate) {
         this.caseInvolvedDate = caseInvolvedDate;
      }

      public void setCaseInvolvedFlag(String caseInvolvedFlag) {
         this.caseInvolvedFlag = caseInvolvedFlag;
      }

      public void setClientName(String clientName) {
         this.clientName = clientName;
      }

      public void setTranChannelStatus(String tranChannelStatus) {
         this.tranChannelStatus = tranChannelStatus;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100024Out.AcctArray)) {
            return false;
         } else {
            Core1400100024Out.AcctArray other = (Core1400100024Out.AcctArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label167: {
                  Object this$clientNo = this.getClientNo();
                  Object other$clientNo = other.getClientNo();
                  if (this$clientNo == null) {
                     if (other$clientNo == null) {
                        break label167;
                     }
                  } else if (this$clientNo.equals(other$clientNo)) {
                     break label167;
                  }

                  return false;
               }

               Object this$baseAcctNo = this.getBaseAcctNo();
               Object other$baseAcctNo = other.getBaseAcctNo();
               if (this$baseAcctNo == null) {
                  if (other$baseAcctNo != null) {
                     return false;
                  }
               } else if (!this$baseAcctNo.equals(other$baseAcctNo)) {
                  return false;
               }

               label153: {
                  Object this$prodType = this.getProdType();
                  Object other$prodType = other.getProdType();
                  if (this$prodType == null) {
                     if (other$prodType == null) {
                        break label153;
                     }
                  } else if (this$prodType.equals(other$prodType)) {
                     break label153;
                  }

                  return false;
               }

               Object this$acctSeqNo = this.getAcctSeqNo();
               Object other$acctSeqNo = other.getAcctSeqNo();
               if (this$acctSeqNo == null) {
                  if (other$acctSeqNo != null) {
                     return false;
                  }
               } else if (!this$acctSeqNo.equals(other$acctSeqNo)) {
                  return false;
               }

               label139: {
                  Object this$acctCcy = this.getAcctCcy();
                  Object other$acctCcy = other.getAcctCcy();
                  if (this$acctCcy == null) {
                     if (other$acctCcy == null) {
                        break label139;
                     }
                  } else if (this$acctCcy.equals(other$acctCcy)) {
                     break label139;
                  }

                  return false;
               }

               Object this$uncounterRestraintType = this.getUncounterRestraintType();
               Object other$uncounterRestraintType = other.getUncounterRestraintType();
               if (this$uncounterRestraintType == null) {
                  if (other$uncounterRestraintType != null) {
                     return false;
                  }
               } else if (!this$uncounterRestraintType.equals(other$uncounterRestraintType)) {
                  return false;
               }

               label125: {
                  Object this$lastTimeStamp = this.getLastTimeStamp();
                  Object other$lastTimeStamp = other.getLastTimeStamp();
                  if (this$lastTimeStamp == null) {
                     if (other$lastTimeStamp == null) {
                        break label125;
                     }
                  } else if (this$lastTimeStamp.equals(other$lastTimeStamp)) {
                     break label125;
                  }

                  return false;
               }

               label118: {
                  Object this$tellername = this.getTellername();
                  Object other$tellername = other.getTellername();
                  if (this$tellername == null) {
                     if (other$tellername == null) {
                        break label118;
                     }
                  } else if (this$tellername.equals(other$tellername)) {
                     break label118;
                  }

                  return false;
               }

               Object this$narrative1 = this.getNarrative1();
               Object other$narrative1 = other.getNarrative1();
               if (this$narrative1 == null) {
                  if (other$narrative1 != null) {
                     return false;
                  }
               } else if (!this$narrative1.equals(other$narrative1)) {
                  return false;
               }

               label104: {
                  Object this$caseInvolvedDate = this.getCaseInvolvedDate();
                  Object other$caseInvolvedDate = other.getCaseInvolvedDate();
                  if (this$caseInvolvedDate == null) {
                     if (other$caseInvolvedDate == null) {
                        break label104;
                     }
                  } else if (this$caseInvolvedDate.equals(other$caseInvolvedDate)) {
                     break label104;
                  }

                  return false;
               }

               label97: {
                  Object this$caseInvolvedFlag = this.getCaseInvolvedFlag();
                  Object other$caseInvolvedFlag = other.getCaseInvolvedFlag();
                  if (this$caseInvolvedFlag == null) {
                     if (other$caseInvolvedFlag == null) {
                        break label97;
                     }
                  } else if (this$caseInvolvedFlag.equals(other$caseInvolvedFlag)) {
                     break label97;
                  }

                  return false;
               }

               Object this$clientName = this.getClientName();
               Object other$clientName = other.getClientName();
               if (this$clientName == null) {
                  if (other$clientName != null) {
                     return false;
                  }
               } else if (!this$clientName.equals(other$clientName)) {
                  return false;
               }

               Object this$tranChannelStatus = this.getTranChannelStatus();
               Object other$tranChannelStatus = other.getTranChannelStatus();
               if (this$tranChannelStatus == null) {
                  if (other$tranChannelStatus != null) {
                     return false;
                  }
               } else if (!this$tranChannelStatus.equals(other$tranChannelStatus)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100024Out.AcctArray;
      }
      public String toString() {
         return "Core1400100024Out.AcctArray(clientNo=" + this.getClientNo() + ", baseAcctNo=" + this.getBaseAcctNo() + ", prodType=" + this.getProdType() + ", acctSeqNo=" + this.getAcctSeqNo() + ", acctCcy=" + this.getAcctCcy() + ", uncounterRestraintType=" + this.getUncounterRestraintType() + ", lastTimeStamp=" + this.getLastTimeStamp() + ", tellername=" + this.getTellername() + ", narrative1=" + this.getNarrative1() + ", caseInvolvedDate=" + this.getCaseInvolvedDate() + ", caseInvolvedFlag=" + this.getCaseInvolvedFlag() + ", clientName=" + this.getClientName() + ", tranChannelStatus=" + this.getTranChannelStatus() + ")";
      }
   }
}
