package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000142In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000142Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000142 {
   String URL = "/rb/nfin/msa/transfer/query";


   @ApiRemark("msa账户转账明细查询")
   @ApiDesc("msa账户转账明细查询")
   @EnsembleElements(
      serviceCode = "Core",
      messageType = "1400",
      messageCode = "0142"
   )
   Core14000142Out runService(Core14000142In var1);
}
