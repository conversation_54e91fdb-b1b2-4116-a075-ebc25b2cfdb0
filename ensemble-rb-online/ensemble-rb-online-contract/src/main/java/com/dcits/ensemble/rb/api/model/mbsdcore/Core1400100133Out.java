package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.util.List;

@MessageOut
public class Core1400100133Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100133Out.AgreementArray> agreementArray;

   public List<Core1400100133Out.AgreementArray> getAgreementArray() {
      return this.agreementArray;
   }

   public void setAgreementArray(List<Core1400100133Out.AgreementArray> agreementArray) {
      this.agreementArray = agreementArray;
   }

   public String toString() {
      return "Core1400100133Out(agreementArray=" + this.getAgreementArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100133Out)) {
         return false;
      } else {
         Core1400100133Out other = (Core1400100133Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$agreementArray = this.getAgreementArray();
            Object other$agreementArray = other.getAgreementArray();
            if (this$agreementArray == null) {
               if (other$agreementArray != null) {
                  return false;
               }
            } else if (!this$agreementArray.equals(other$agreementArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100133Out;
   }
   public static class AgreementArray {
      @V(
         desc = "收费序号",
         notNull = false,
         length = "50",
         remark = "收费序号",
         maxSize = 50
      )
      private String scSeqNo;
      @V(
         desc = "原摊销起始日期",
         notNull = false,
         remark = "原摊销起始日期"
      )
      private String originAmortStart;
      @V(
         desc = "原摊销截止日期",
         notNull = false,
         remark = "原摊销截止日期"
      )
      private String originAmortEnd;
      @V(
         desc = "摊销起始日期",
         notNull = false,
         remark = "摊销起始日期"
      )
      private String amortStart;
      @V(
         desc = "摊销截止日期",
         notNull = false,
         remark = "摊销截止日期"
      )
      private String amortEnd;
      @V(
         desc = "交易参考号",
         notNull = false,
         length = "50",
         remark = "交易参考号",
         maxSize = 50
      )
      private String reference;
      @V(
         desc = "最后修改日期",
         notNull = false,
         remark = "最后修改日期"
      )
      private String lastChangeDate;
      @V(
         desc = "最后修改机构",
         notNull = false,
         length = "20",
         remark = "最后修改机构",
         maxSize = 20
      )
      private String lastChangeBranch;
      @V(
         desc = "最后修改柜员",
         notNull = false,
         length = "30",
         remark = "最后修改柜员",
         maxSize = 30
      )
      private String lastChangeUserId;

      public String getScSeqNo() {
         return this.scSeqNo;
      }

      public String getOriginAmortStart() {
         return this.originAmortStart;
      }

      public String getOriginAmortEnd() {
         return this.originAmortEnd;
      }

      public String getAmortStart() {
         return this.amortStart;
      }

      public String getAmortEnd() {
         return this.amortEnd;
      }

      public String getReference() {
         return this.reference;
      }

      public String getLastChangeDate() {
         return this.lastChangeDate;
      }

      public String getLastChangeBranch() {
         return this.lastChangeBranch;
      }

      public String getLastChangeUserId() {
         return this.lastChangeUserId;
      }

      public void setScSeqNo(String scSeqNo) {
         this.scSeqNo = scSeqNo;
      }

      public void setOriginAmortStart(String originAmortStart) {
         this.originAmortStart = originAmortStart;
      }

      public void setOriginAmortEnd(String originAmortEnd) {
         this.originAmortEnd = originAmortEnd;
      }

      public void setAmortStart(String amortStart) {
         this.amortStart = amortStart;
      }

      public void setAmortEnd(String amortEnd) {
         this.amortEnd = amortEnd;
      }

      public void setReference(String reference) {
         this.reference = reference;
      }

      public void setLastChangeDate(String lastChangeDate) {
         this.lastChangeDate = lastChangeDate;
      }

      public void setLastChangeBranch(String lastChangeBranch) {
         this.lastChangeBranch = lastChangeBranch;
      }

      public void setLastChangeUserId(String lastChangeUserId) {
         this.lastChangeUserId = lastChangeUserId;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100133Out.AgreementArray)) {
            return false;
         } else {
            Core1400100133Out.AgreementArray other = (Core1400100133Out.AgreementArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label119: {
                  Object this$scSeqNo = this.getScSeqNo();
                  Object other$scSeqNo = other.getScSeqNo();
                  if (this$scSeqNo == null) {
                     if (other$scSeqNo == null) {
                        break label119;
                     }
                  } else if (this$scSeqNo.equals(other$scSeqNo)) {
                     break label119;
                  }

                  return false;
               }

               Object this$originAmortStart = this.getOriginAmortStart();
               Object other$originAmortStart = other.getOriginAmortStart();
               if (this$originAmortStart == null) {
                  if (other$originAmortStart != null) {
                     return false;
                  }
               } else if (!this$originAmortStart.equals(other$originAmortStart)) {
                  return false;
               }

               label105: {
                  Object this$originAmortEnd = this.getOriginAmortEnd();
                  Object other$originAmortEnd = other.getOriginAmortEnd();
                  if (this$originAmortEnd == null) {
                     if (other$originAmortEnd == null) {
                        break label105;
                     }
                  } else if (this$originAmortEnd.equals(other$originAmortEnd)) {
                     break label105;
                  }

                  return false;
               }

               Object this$amortStart = this.getAmortStart();
               Object other$amortStart = other.getAmortStart();
               if (this$amortStart == null) {
                  if (other$amortStart != null) {
                     return false;
                  }
               } else if (!this$amortStart.equals(other$amortStart)) {
                  return false;
               }

               label91: {
                  Object this$amortEnd = this.getAmortEnd();
                  Object other$amortEnd = other.getAmortEnd();
                  if (this$amortEnd == null) {
                     if (other$amortEnd == null) {
                        break label91;
                     }
                  } else if (this$amortEnd.equals(other$amortEnd)) {
                     break label91;
                  }

                  return false;
               }

               Object this$reference = this.getReference();
               Object other$reference = other.getReference();
               if (this$reference == null) {
                  if (other$reference != null) {
                     return false;
                  }
               } else if (!this$reference.equals(other$reference)) {
                  return false;
               }

               label77: {
                  Object this$lastChangeDate = this.getLastChangeDate();
                  Object other$lastChangeDate = other.getLastChangeDate();
                  if (this$lastChangeDate == null) {
                     if (other$lastChangeDate == null) {
                        break label77;
                     }
                  } else if (this$lastChangeDate.equals(other$lastChangeDate)) {
                     break label77;
                  }

                  return false;
               }

               label70: {
                  Object this$lastChangeBranch = this.getLastChangeBranch();
                  Object other$lastChangeBranch = other.getLastChangeBranch();
                  if (this$lastChangeBranch == null) {
                     if (other$lastChangeBranch == null) {
                        break label70;
                     }
                  } else if (this$lastChangeBranch.equals(other$lastChangeBranch)) {
                     break label70;
                  }

                  return false;
               }

               Object this$lastChangeUserId = this.getLastChangeUserId();
               Object other$lastChangeUserId = other.getLastChangeUserId();
               if (this$lastChangeUserId == null) {
                  if (other$lastChangeUserId != null) {
                     return false;
                  }
               } else if (!this$lastChangeUserId.equals(other$lastChangeUserId)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100133Out.AgreementArray;
      }
      public String toString() {
         return "Core1400100133Out.AgreementArray(scSeqNo=" + this.getScSeqNo() + ", originAmortStart=" + this.getOriginAmortStart() + ", originAmortEnd=" + this.getOriginAmortEnd() + ", amortStart=" + this.getAmortStart() + ", amortEnd=" + this.getAmortEnd() + ", reference=" + this.getReference() + ", lastChangeDate=" + this.getLastChangeDate() + ", lastChangeBranch=" + this.getLastChangeBranch() + ", lastChangeUserId=" + this.getLastChangeUserId() + ")";
      }
   }
}
