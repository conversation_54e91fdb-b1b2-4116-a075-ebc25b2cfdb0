package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12202618In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12202618Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12202618 {
   String URL = "/rb/file/account/check";


   @ApiDesc("tae日间对账请求存款系统，异步生成该场次的流水文件及汇总供tae对账")
   @EnsembleElements(
      serviceCode = "Core",
      messageType = "1220",
      messageCode = "2618"
   )
   Core12202618Out runService(Core12202618In var1);
}
