package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;
import java.util.List;

@MessageIn
public class Core1400032401In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400032401In.Body body;

   public Core1400032401In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400032401In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400032401In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400032401In)) {
         return false;
      } else {
         Core1400032401In other = (Core1400032401In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400032401In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "开始日期",
         notNull = false,
         remark = "开始日期"
      )
      private String startDate;
      @V(
         desc = "结束日期",
         notNull = false,
         remark = "结束日期"
      )
      private String endDate;
      @V(
         desc = "票据代理分行",
         notNull = false,
         length = "50",
         remark = "票据代理分行",
         maxSize = 50
      )
      private String agentBranch;
      @V(
         desc = "银承合同编号",
         notNull = false,
         length = "50",
         remark = "银行承兑汇票协议编号",
         maxSize = 50
      )
      private String acceptContractNo;
      @V(
         desc = "交易参考号",
         notNull = false,
         length = "50",
         remark = "交易参考号",
         maxSize = 50
      )
      private String reference;
      @V(
         desc = "垫款标志",
         notNull = false,
         length = "1",
         in = "Y,N",
         inDesc = "Y-是,N-否",
         remark = "垫款标志",
         maxSize = 1
      )
      private String advanceFlag;
      @V(
         desc = "票据类型",
         notNull = false,
         length = "5",
         in = "P,E",
         inDesc = "P-纸质,E-电子,CT00-可转让汇票,CT01-不可转让汇票,CT02-现金汇票",
         remark = "票据类型",
         maxSize = 5
      )
      private String billType;
      @V(
         desc = "出票人/承兑人账号",
         notNull = false,
         length = "50",
         remark = "专指出票人/承兑人对应账号或卡号",
         maxSize = 50
      )
      private String acceptBaseAcctNo;
      @V(
         desc = "合同号",
         notNull = false,
         length = "50",
         remark = "合同号",
         maxSize = 50
      )
      private String contractNo;
      @V(
         desc = "银承状态",
         notNull = false,
         length = "10",
         in = "A,B,C,D,E,F,G,H",
         inDesc = "A-预登记,B-预登记取消,C-出票(承兑),D-备款,E-已兑付,F-兑付取消,G-退回,H-承兑取消",
         remark = "银承状态",
         maxSize = 10
      )
      private String acceptStatus;
      @V(
         desc = "票据号码",
         notNull = false,
         length = "50",
         remark = "票据号码",
         maxSize = 50
      )
      private String billNo;
      @V(
         desc = "票据到期日",
         notNull = false,
         remark = "票据到期日"
      )
      private String billMaturityDate;
      @V(
         desc = "数组",
         notNull = false,
         remark = "数组"
      )
      private List<Core1400032401In.Body.BillQureyArray> billQureyArray;

      public String getStartDate() {
         return this.startDate;
      }

      public String getEndDate() {
         return this.endDate;
      }

      public String getAgentBranch() {
         return this.agentBranch;
      }

      public String getAcceptContractNo() {
         return this.acceptContractNo;
      }

      public String getReference() {
         return this.reference;
      }

      public String getAdvanceFlag() {
         return this.advanceFlag;
      }

      public String getBillType() {
         return this.billType;
      }

      public String getAcceptBaseAcctNo() {
         return this.acceptBaseAcctNo;
      }

      public String getContractNo() {
         return this.contractNo;
      }

      public String getAcceptStatus() {
         return this.acceptStatus;
      }

      public String getBillNo() {
         return this.billNo;
      }

      public String getBillMaturityDate() {
         return this.billMaturityDate;
      }

      public List<Core1400032401In.Body.BillQureyArray> getBillQureyArray() {
         return this.billQureyArray;
      }

      public void setStartDate(String startDate) {
         this.startDate = startDate;
      }

      public void setEndDate(String endDate) {
         this.endDate = endDate;
      }

      public void setAgentBranch(String agentBranch) {
         this.agentBranch = agentBranch;
      }

      public void setAcceptContractNo(String acceptContractNo) {
         this.acceptContractNo = acceptContractNo;
      }

      public void setReference(String reference) {
         this.reference = reference;
      }

      public void setAdvanceFlag(String advanceFlag) {
         this.advanceFlag = advanceFlag;
      }

      public void setBillType(String billType) {
         this.billType = billType;
      }

      public void setAcceptBaseAcctNo(String acceptBaseAcctNo) {
         this.acceptBaseAcctNo = acceptBaseAcctNo;
      }

      public void setContractNo(String contractNo) {
         this.contractNo = contractNo;
      }

      public void setAcceptStatus(String acceptStatus) {
         this.acceptStatus = acceptStatus;
      }

      public void setBillNo(String billNo) {
         this.billNo = billNo;
      }

      public void setBillMaturityDate(String billMaturityDate) {
         this.billMaturityDate = billMaturityDate;
      }

      public void setBillQureyArray(List<Core1400032401In.Body.BillQureyArray> billQureyArray) {
         this.billQureyArray = billQureyArray;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400032401In.Body)) {
            return false;
         } else {
            Core1400032401In.Body other = (Core1400032401In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label167: {
                  Object this$startDate = this.getStartDate();
                  Object other$startDate = other.getStartDate();
                  if (this$startDate == null) {
                     if (other$startDate == null) {
                        break label167;
                     }
                  } else if (this$startDate.equals(other$startDate)) {
                     break label167;
                  }

                  return false;
               }

               Object this$endDate = this.getEndDate();
               Object other$endDate = other.getEndDate();
               if (this$endDate == null) {
                  if (other$endDate != null) {
                     return false;
                  }
               } else if (!this$endDate.equals(other$endDate)) {
                  return false;
               }

               label153: {
                  Object this$agentBranch = this.getAgentBranch();
                  Object other$agentBranch = other.getAgentBranch();
                  if (this$agentBranch == null) {
                     if (other$agentBranch == null) {
                        break label153;
                     }
                  } else if (this$agentBranch.equals(other$agentBranch)) {
                     break label153;
                  }

                  return false;
               }

               Object this$acceptContractNo = this.getAcceptContractNo();
               Object other$acceptContractNo = other.getAcceptContractNo();
               if (this$acceptContractNo == null) {
                  if (other$acceptContractNo != null) {
                     return false;
                  }
               } else if (!this$acceptContractNo.equals(other$acceptContractNo)) {
                  return false;
               }

               label139: {
                  Object this$reference = this.getReference();
                  Object other$reference = other.getReference();
                  if (this$reference == null) {
                     if (other$reference == null) {
                        break label139;
                     }
                  } else if (this$reference.equals(other$reference)) {
                     break label139;
                  }

                  return false;
               }

               Object this$advanceFlag = this.getAdvanceFlag();
               Object other$advanceFlag = other.getAdvanceFlag();
               if (this$advanceFlag == null) {
                  if (other$advanceFlag != null) {
                     return false;
                  }
               } else if (!this$advanceFlag.equals(other$advanceFlag)) {
                  return false;
               }

               label125: {
                  Object this$billType = this.getBillType();
                  Object other$billType = other.getBillType();
                  if (this$billType == null) {
                     if (other$billType == null) {
                        break label125;
                     }
                  } else if (this$billType.equals(other$billType)) {
                     break label125;
                  }

                  return false;
               }

               label118: {
                  Object this$acceptBaseAcctNo = this.getAcceptBaseAcctNo();
                  Object other$acceptBaseAcctNo = other.getAcceptBaseAcctNo();
                  if (this$acceptBaseAcctNo == null) {
                     if (other$acceptBaseAcctNo == null) {
                        break label118;
                     }
                  } else if (this$acceptBaseAcctNo.equals(other$acceptBaseAcctNo)) {
                     break label118;
                  }

                  return false;
               }

               Object this$contractNo = this.getContractNo();
               Object other$contractNo = other.getContractNo();
               if (this$contractNo == null) {
                  if (other$contractNo != null) {
                     return false;
                  }
               } else if (!this$contractNo.equals(other$contractNo)) {
                  return false;
               }

               label104: {
                  Object this$acceptStatus = this.getAcceptStatus();
                  Object other$acceptStatus = other.getAcceptStatus();
                  if (this$acceptStatus == null) {
                     if (other$acceptStatus == null) {
                        break label104;
                     }
                  } else if (this$acceptStatus.equals(other$acceptStatus)) {
                     break label104;
                  }

                  return false;
               }

               label97: {
                  Object this$billNo = this.getBillNo();
                  Object other$billNo = other.getBillNo();
                  if (this$billNo == null) {
                     if (other$billNo == null) {
                        break label97;
                     }
                  } else if (this$billNo.equals(other$billNo)) {
                     break label97;
                  }

                  return false;
               }

               Object this$billMaturityDate = this.getBillMaturityDate();
               Object other$billMaturityDate = other.getBillMaturityDate();
               if (this$billMaturityDate == null) {
                  if (other$billMaturityDate != null) {
                     return false;
                  }
               } else if (!this$billMaturityDate.equals(other$billMaturityDate)) {
                  return false;
               }

               Object this$billQureyArray = this.getBillQureyArray();
               Object other$billQureyArray = other.getBillQureyArray();
               if (this$billQureyArray == null) {
                  if (other$billQureyArray != null) {
                     return false;
                  }
               } else if (!this$billQureyArray.equals(other$billQureyArray)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400032401In.Body;
      }
      public String toString() {
         return "Core1400032401In.Body(startDate=" + this.getStartDate() + ", endDate=" + this.getEndDate() + ", agentBranch=" + this.getAgentBranch() + ", acceptContractNo=" + this.getAcceptContractNo() + ", reference=" + this.getReference() + ", advanceFlag=" + this.getAdvanceFlag() + ", billType=" + this.getBillType() + ", acceptBaseAcctNo=" + this.getAcceptBaseAcctNo() + ", contractNo=" + this.getContractNo() + ", acceptStatus=" + this.getAcceptStatus() + ", billNo=" + this.getBillNo() + ", billMaturityDate=" + this.getBillMaturityDate() + ", billQureyArray=" + this.getBillQureyArray() + ")";
      }

      public static class BillQureyArray {
         @V(
            desc = "票据类型",
            notNull = false,
            length = "5",
            in = "P,E",
            inDesc = "P-纸质,E-电子,CT00-可转让汇票,CT01-不可转让汇票,CT02-现金汇票",
            remark = "票据类型",
            maxSize = 5
         )
         private String billType;
         @V(
            desc = "票据号码",
            notNull = false,
            length = "50",
            remark = "票据号码",
            maxSize = 50
         )
         private String billNo;

         public String getBillType() {
            return this.billType;
         }

         public String getBillNo() {
            return this.billNo;
         }

         public void setBillType(String billType) {
            this.billType = billType;
         }

         public void setBillNo(String billNo) {
            this.billNo = billNo;
         }

         public boolean equals(Object o) {
            if (o == this) {
               return true;
            } else if (!(o instanceof Core1400032401In.Body.BillQureyArray)) {
               return false;
            } else {
               Core1400032401In.Body.BillQureyArray other = (Core1400032401In.Body.BillQureyArray)o;
               if (!other.canEqual(this)) {
                  return false;
               } else {
                  Object this$billType = this.getBillType();
                  Object other$billType = other.getBillType();
                  if (this$billType == null) {
                     if (other$billType != null) {
                        return false;
                     }
                  } else if (!this$billType.equals(other$billType)) {
                     return false;
                  }

                  Object this$billNo = this.getBillNo();
                  Object other$billNo = other.getBillNo();
                  if (this$billNo == null) {
                     if (other$billNo != null) {
                        return false;
                     }
                  } else if (!this$billNo.equals(other$billNo)) {
                     return false;
                  }

                  return true;
               }
            }
         }

         protected boolean canEqual(Object other) {
            return other instanceof Core1400032401In.Body.BillQureyArray;
         }
         public String toString() {
            return "Core1400032401In.Body.BillQureyArray(billType=" + this.getBillType() + ", billNo=" + this.getBillNo() + ")";
         }
      }
   }
}
