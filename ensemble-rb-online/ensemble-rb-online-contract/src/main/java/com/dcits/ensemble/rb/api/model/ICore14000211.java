package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000211In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core14000211Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore14000211 {
   String URL = "/rb/inq/term/agre";


   @ApiDesc("协议存款信息查询")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1400",
      messageCode = "0211"
   )
   @BusinessCategory("1400-查询")
   @FunctionCategory("RB02-账户管理")
   @ConsumeSys("EOS")
   @ApiUseStatus("PRODUCT-产品")
   Core14000211Out runService(Core14000211In var1);
}
