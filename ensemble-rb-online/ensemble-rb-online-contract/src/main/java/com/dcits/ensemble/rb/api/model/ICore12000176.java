package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000176In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000176Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12000176 {
   String URL = "/rb/nfin/acct/risk/update";


   @ApiDesc("根据客户风险等级更新账户风险等级（rb提供cif调用）。")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0176"
   )
   @BusinessCategory("1200-非金融")
   @FunctionCategory("RB02-账户管理")
   @ConsumeSys("CBS")
   @ApiUseStatus("PRODUCT-产品")
   Core12000176Out runService(Core12000176In var1);
}
