package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.ConsumeSys;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000804In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12000804Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12000804 {
   String URL = "/rb/nfin/tx/agreement/main";


   @ApiRemark("同兴赢主协议签约管理")
   @ApiDesc("华兴同兴赢签约主协议接口管理包括：签约，解约，维护")
   @EnsembleElements(
      serviceCode = "MbsdCore",
      messageType = "1200",
      messageCode = "0804"
   )
   @BusinessCategory("1200-非金融")
   @FunctionCategory("RB05-协议管理")
   @ConsumeSys("EOS")
   Core12000804Out runService(Core12000804In var1);
}
