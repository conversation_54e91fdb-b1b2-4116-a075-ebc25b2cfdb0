package com.dcits.ensemble.rb.api.model;

import com.dcits.comet.flow.annotation.ApiDesc;
import com.dcits.comet.flow.annotation.ApiName;
import com.dcits.comet.flow.annotation.ApiRemark;
import com.dcits.comet.flow.annotation.ApiUseStatus;
import com.dcits.comet.flow.annotation.BusinessCategory;
import com.dcits.comet.flow.annotation.EnsembleElements;
import com.dcits.comet.flow.annotation.FunctionCategory;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12001005In;
import com.dcits.ensemble.rb.api.model.mbsdcore.Core12001005Out;
import com.dcits.gravity.api.annotation.ServiceApi;

@ServiceApi
public interface ICore12001005 {
   String URL = "/rb/nfin/acct/propertychange";


   @ApiRemark("/rb/nfin/acct/propertychange")
   @ApiDesc("对公账户属性维护接口")
   @EnsembleElements(
      serviceCode = "Core",
      messageType = "1200",
      messageCode = "1005"
   )
   @BusinessCategory("1200-非金融")
   @FunctionCategory("RB02-账户管理")
   @ApiUseStatus("PRODUCT-产品")
   Core12001005Out runService(Core12001005In var1);
}
