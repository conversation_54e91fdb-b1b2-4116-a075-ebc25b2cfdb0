package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsResponse;
import com.dcits.gravity.api.annotation.MessageOut;
import java.math.BigDecimal;
import java.util.List;

@MessageOut
public class Core1400100026Out extends EnsResponse {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "客户号",
      notNull = false,
      length = "20",
      remark = "客户号",
      maxSize = 20
   )
   private String clientNo;
   @V(
      desc = "汇总金额（发行中产品额度）",
      notNull = false,
      length = "17",
      remark = "汇总金额（发行中产品额度）",
      decimalLength = 2,
      precision = 2
   )
   private BigDecimal mainBalance;
   @V(
      desc = "数组",
      notNull = false,
      remark = "数组"
   )
   private List<Core1400100026Out.SubArray> subArray;

   public String getClientNo() {
      return this.clientNo;
   }

   public BigDecimal getMainBalance() {
      return this.mainBalance;
   }

   public List<Core1400100026Out.SubArray> getSubArray() {
      return this.subArray;
   }

   public void setClientNo(String clientNo) {
      this.clientNo = clientNo;
   }

   public void setMainBalance(BigDecimal mainBalance) {
      this.mainBalance = mainBalance;
   }

   public void setSubArray(List<Core1400100026Out.SubArray> subArray) {
      this.subArray = subArray;
   }

   public String toString() {
      return "Core1400100026Out(clientNo=" + this.getClientNo() + ", mainBalance=" + this.getMainBalance() + ", subArray=" + this.getSubArray() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400100026Out)) {
         return false;
      } else {
         Core1400100026Out other = (Core1400100026Out)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            label49: {
               Object this$clientNo = this.getClientNo();
               Object other$clientNo = other.getClientNo();
               if (this$clientNo == null) {
                  if (other$clientNo == null) {
                     break label49;
                  }
               } else if (this$clientNo.equals(other$clientNo)) {
                  break label49;
               }

               return false;
            }

            Object this$mainBalance = this.getMainBalance();
            Object other$mainBalance = other.getMainBalance();
            if (this$mainBalance == null) {
               if (other$mainBalance != null) {
                  return false;
               }
            } else if (!this$mainBalance.equals(other$mainBalance)) {
               return false;
            }

            Object this$subArray = this.getSubArray();
            Object other$subArray = other.getSubArray();
            if (this$subArray == null) {
               if (other$subArray != null) {
                  return false;
               }
            } else if (!this$subArray.equals(other$subArray)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400100026Out;
   }
   public static class SubArray {
      @V(
         desc = "账号/卡号",
         notNull = false,
         length = "50",
         remark = "用于描述不同账户结构下的账号，如果是卡的话代表卡号，否则代表账号",
         maxSize = 50
      )
      private String baseAcctNo;
      @V(
         desc = "产品类型",
         notNull = false,
         length = "20",
         remark = "产品类型",
         maxSize = 20
      )
      private String prodType;
      @V(
         desc = "产品描述",
         notNull = false,
         length = "200",
         remark = "解释产品具体特性",
         maxSize = 200
      )
      private String prodDesc;
      @V(
         desc = "账户序号",
         notNull = false,
         length = "5",
         remark = "账户序列号，采用顺序数字，表示在同一账号、账户类型、币种下的不同子账户，比如定期存款序列号，卡下选择账户",
         maxSize = 5
      )
      private String acctSeqNo;
      @V(
         desc = "账户币种",
         notNull = false,
         length = "3",
         remark = "账户币种 对于AIO账户和一本通账户",
         maxSize = 3
      )
      private String acctCcy;
      @V(
         desc = "账户状态",
         notNull = false,
         length = "1",
         remark = "描述账户生命周期不同阶段的划分",
         maxSize = 1
      )
      private String acctStatus;
      @V(
         desc = "账户名称",
         notNull = false,
         length = "200",
         remark = "账户名称，一般指中文账户名称",
         maxSize = 200
      )
      private String acctName;
      @V(
         desc = "生效日期",
         notNull = false,
         remark = "生效日期"
      )
      private String effectDate;
      @V(
         desc = "结息日期",
         notNull = false,
         remark = "结息日期"
      )
      private String captDate;
      @V(
         desc = "销户日期",
         notNull = false,
         remark = "账户销户日期"
      )
      private String acctCloseDate;
      @V(
         desc = "执行汇率",
         notNull = false,
         length = "15",
         remark = "执行汇率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal exchRate;
      @V(
         desc = "行内利率",
         notNull = false,
         length = "15",
         remark = "在人行基准利率调整后对客发布的行内利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal actualRate;
      @V(
         desc = "浮动利率",
         notNull = false,
         length = "15",
         remark = "浮动利率",
         decimalLength = 8,
         precision = 8
      )
      private BigDecimal floatRate;
      @V(
         desc = "个体客户标志",
         notNull = false,
         length = "1",
         remark = "是否个体客户",
         maxSize = 1
      )
      private String isIndividual;
      @V(
         desc = "账户类型",
         notNull = false,
         length = "1",
         remark = "账户类型",
         maxSize = 1
      )
      private String acctType;
      @V(
         desc = "余额类型",
         notNull = false,
         length = "2",
         remark = "余额类型",
         maxSize = 2
      )
      private String balType;
      @V(
         desc = "账户余额",
         notNull = false,
         length = "17",
         remark = "账户余额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal acctBalance;
      @V(
         desc = "可用余额",
         notNull = false,
         length = "17",
         remark = "可用余额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal availableAmt;
      @V(
         desc = "冻结金额",
         notNull = false,
         length = "17",
         remark = "冻结金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal pldAmount;
      @V(
         desc = "存期期限",
         notNull = false,
         length = "5",
         remark = "期限",
         maxSize = 5
      )
      private String term;
      @V(
         desc = "期限类型",
         notNull = false,
         length = "1",
         remark = "期限类型",
         maxSize = 1
      )
      private String termType;
      @V(
         desc = "凭证类型",
         notNull = false,
         length = "10",
         remark = "凭证类型",
         maxSize = 10
      )
      private String docType;
      @V(
         desc = "凭证号码",
         notNull = false,
         length = "50",
         remark = "凭证号码",
         maxSize = 50
      )
      private String voucherNo;
      @V(
         desc = "凭证前缀",
         notNull = false,
         length = "10",
         remark = "凭证前缀",
         maxSize = 10
      )
      private String voucherPrefix;
      @V(
         desc = "是否自动转存",
         notNull = false,
         length = "10",
         remark = "定期是否自动转存",
         maxSize = 10
      )
      private String autoRenewRollover;
      @V(
         desc = "可转让金额",
         notNull = false,
         length = "17",
         remark = "可转让金额",
         decimalLength = 2,
         precision = 2
      )
      private BigDecimal allowBalance;
      @V(
         desc = "到期日",
         notNull = false,
         remark = "到期日"
      )
      private String matureDate;
      @V(
         desc = "是否允许提前支取",
         notNull = false,
         length = "1",
         remark = "是否允许提前支取",
         maxSize = 1
      )
      private String preWithdrawFlag;
      @V(
         desc = "是否允许部分支取",
         notNull = false,
         length = "1",
         remark = "是否允许部分支取",
         maxSize = 1
      )
      private String partWithdrawFlag;
      @V(
         desc = "部提次数",
         notNull = false,
         length = "5",
         remark = "部提次数"
      )
      private Integer partWithdrawNum;
      @V(
         desc = "是否指定收息",
         notNull = false,
         length = "1",
         remark = "是否指定收息",
         maxSize = 1
      )
      private String directionChargeIntFlag;
      @V(
         desc = "开户起始日期",
         notNull = false,
         length = "8",
         remark = "开户起始日期",
         maxSize = 8
      )
      private String acctOpenStartDate;
      @V(
         desc = "数组",
         notNull = false,
         remark = "数组"
      )
      private List<Core1400100026Out.SubArray.SettleArray> settleArray;
      @V(
         desc = "付息方式",
         notNull = false,
         length = "3",
         remark = "付息方式",
         maxSize = 3
      )
      private String payIntType;
      @V(
         desc = "期次代码",
         notNull = false,
         length = "50",
         remark = "期次代码",
         maxSize = 50
      )
      private String stageCode;
      @V(
         desc = "转让标志",
         notNull = false,
         length = "1",
         remark = "转让标志",
         maxSize = 1
      )
      private String trfFlag;
      @V(
         desc = "起息日",
         notNull = false,
         remark = "起息日"
      )
      private String intStartDate;
      @V(
         desc = "预约登记日期",
         notNull = false,
         remark = "预约登记日期"
      )
      private String precontractDate;
      @V(
         desc = "转让日期",
         notNull = false,
         remark = "转让日期"
      )
      private String trfDate;
      @V(
         desc = "期次描述",
         notNull = false,
         length = "200",
         remark = "期次描述",
         maxSize = 200
      )
      private String stageCodeDesc;
      @V(
         desc = "转让申请时间",
         notNull = false,
         length = "10",
         remark = "转让申请时间",
         maxSize = 10
      )
      private String trfApplyDate;
      @V(
         desc = "可部提次数",
         notNull = false,
         length = "5",
         remark = "可部提次数"
      )
      private Integer allowWithdrawNum;
      @V(
         desc = "大额存单转让，状态为已处理时，购买方的购买时间",
         notNull = false,
         length = "10",
         remark = "大额存单转让，状态为已处理时，购买方的购买时间",
         maxSize = 10
      )
      private String dcChangeBuyDate;
      @V(
         desc = "开户起始日期",
         notNull = false,
         length = "10",
         remark = "开户起始日期",
         maxSize = 10
      )
      private String openStartDate;

      public String getBaseAcctNo() {
         return this.baseAcctNo;
      }

      public String getProdType() {
         return this.prodType;
      }

      public String getProdDesc() {
         return this.prodDesc;
      }

      public String getAcctSeqNo() {
         return this.acctSeqNo;
      }

      public String getAcctCcy() {
         return this.acctCcy;
      }

      public String getAcctStatus() {
         return this.acctStatus;
      }

      public String getAcctName() {
         return this.acctName;
      }

      public String getEffectDate() {
         return this.effectDate;
      }

      public String getCaptDate() {
         return this.captDate;
      }

      public String getAcctCloseDate() {
         return this.acctCloseDate;
      }

      public BigDecimal getExchRate() {
         return this.exchRate;
      }

      public BigDecimal getActualRate() {
         return this.actualRate;
      }

      public BigDecimal getFloatRate() {
         return this.floatRate;
      }

      public String getIsIndividual() {
         return this.isIndividual;
      }

      public String getAcctType() {
         return this.acctType;
      }

      public String getBalType() {
         return this.balType;
      }

      public BigDecimal getAcctBalance() {
         return this.acctBalance;
      }

      public BigDecimal getAvailableAmt() {
         return this.availableAmt;
      }

      public BigDecimal getPldAmount() {
         return this.pldAmount;
      }

      public String getTerm() {
         return this.term;
      }

      public String getTermType() {
         return this.termType;
      }

      public String getDocType() {
         return this.docType;
      }

      public String getVoucherNo() {
         return this.voucherNo;
      }

      public String getVoucherPrefix() {
         return this.voucherPrefix;
      }

      public String getAutoRenewRollover() {
         return this.autoRenewRollover;
      }

      public BigDecimal getAllowBalance() {
         return this.allowBalance;
      }

      public String getMatureDate() {
         return this.matureDate;
      }

      public String getPreWithdrawFlag() {
         return this.preWithdrawFlag;
      }

      public String getPartWithdrawFlag() {
         return this.partWithdrawFlag;
      }

      public Integer getPartWithdrawNum() {
         return this.partWithdrawNum;
      }

      public String getDirectionChargeIntFlag() {
         return this.directionChargeIntFlag;
      }

      public String getAcctOpenStartDate() {
         return this.acctOpenStartDate;
      }

      public List<Core1400100026Out.SubArray.SettleArray> getSettleArray() {
         return this.settleArray;
      }

      public String getPayIntType() {
         return this.payIntType;
      }

      public String getStageCode() {
         return this.stageCode;
      }

      public String getTrfFlag() {
         return this.trfFlag;
      }

      public String getIntStartDate() {
         return this.intStartDate;
      }

      public String getPrecontractDate() {
         return this.precontractDate;
      }

      public String getTrfDate() {
         return this.trfDate;
      }

      public String getStageCodeDesc() {
         return this.stageCodeDesc;
      }

      public String getTrfApplyDate() {
         return this.trfApplyDate;
      }

      public Integer getAllowWithdrawNum() {
         return this.allowWithdrawNum;
      }

      public String getDcChangeBuyDate() {
         return this.dcChangeBuyDate;
      }

      public String getOpenStartDate() {
         return this.openStartDate;
      }

      public void setBaseAcctNo(String baseAcctNo) {
         this.baseAcctNo = baseAcctNo;
      }

      public void setProdType(String prodType) {
         this.prodType = prodType;
      }

      public void setProdDesc(String prodDesc) {
         this.prodDesc = prodDesc;
      }

      public void setAcctSeqNo(String acctSeqNo) {
         this.acctSeqNo = acctSeqNo;
      }

      public void setAcctCcy(String acctCcy) {
         this.acctCcy = acctCcy;
      }

      public void setAcctStatus(String acctStatus) {
         this.acctStatus = acctStatus;
      }

      public void setAcctName(String acctName) {
         this.acctName = acctName;
      }

      public void setEffectDate(String effectDate) {
         this.effectDate = effectDate;
      }

      public void setCaptDate(String captDate) {
         this.captDate = captDate;
      }

      public void setAcctCloseDate(String acctCloseDate) {
         this.acctCloseDate = acctCloseDate;
      }

      public void setExchRate(BigDecimal exchRate) {
         this.exchRate = exchRate;
      }

      public void setActualRate(BigDecimal actualRate) {
         this.actualRate = actualRate;
      }

      public void setFloatRate(BigDecimal floatRate) {
         this.floatRate = floatRate;
      }

      public void setIsIndividual(String isIndividual) {
         this.isIndividual = isIndividual;
      }

      public void setAcctType(String acctType) {
         this.acctType = acctType;
      }

      public void setBalType(String balType) {
         this.balType = balType;
      }

      public void setAcctBalance(BigDecimal acctBalance) {
         this.acctBalance = acctBalance;
      }

      public void setAvailableAmt(BigDecimal availableAmt) {
         this.availableAmt = availableAmt;
      }

      public void setPldAmount(BigDecimal pldAmount) {
         this.pldAmount = pldAmount;
      }

      public void setTerm(String term) {
         this.term = term;
      }

      public void setTermType(String termType) {
         this.termType = termType;
      }

      public void setDocType(String docType) {
         this.docType = docType;
      }

      public void setVoucherNo(String voucherNo) {
         this.voucherNo = voucherNo;
      }

      public void setVoucherPrefix(String voucherPrefix) {
         this.voucherPrefix = voucherPrefix;
      }

      public void setAutoRenewRollover(String autoRenewRollover) {
         this.autoRenewRollover = autoRenewRollover;
      }

      public void setAllowBalance(BigDecimal allowBalance) {
         this.allowBalance = allowBalance;
      }

      public void setMatureDate(String matureDate) {
         this.matureDate = matureDate;
      }

      public void setPreWithdrawFlag(String preWithdrawFlag) {
         this.preWithdrawFlag = preWithdrawFlag;
      }

      public void setPartWithdrawFlag(String partWithdrawFlag) {
         this.partWithdrawFlag = partWithdrawFlag;
      }

      public void setPartWithdrawNum(Integer partWithdrawNum) {
         this.partWithdrawNum = partWithdrawNum;
      }

      public void setDirectionChargeIntFlag(String directionChargeIntFlag) {
         this.directionChargeIntFlag = directionChargeIntFlag;
      }

      public void setAcctOpenStartDate(String acctOpenStartDate) {
         this.acctOpenStartDate = acctOpenStartDate;
      }

      public void setSettleArray(List<Core1400100026Out.SubArray.SettleArray> settleArray) {
         this.settleArray = settleArray;
      }

      public void setPayIntType(String payIntType) {
         this.payIntType = payIntType;
      }

      public void setStageCode(String stageCode) {
         this.stageCode = stageCode;
      }

      public void setTrfFlag(String trfFlag) {
         this.trfFlag = trfFlag;
      }

      public void setIntStartDate(String intStartDate) {
         this.intStartDate = intStartDate;
      }

      public void setPrecontractDate(String precontractDate) {
         this.precontractDate = precontractDate;
      }

      public void setTrfDate(String trfDate) {
         this.trfDate = trfDate;
      }

      public void setStageCodeDesc(String stageCodeDesc) {
         this.stageCodeDesc = stageCodeDesc;
      }

      public void setTrfApplyDate(String trfApplyDate) {
         this.trfApplyDate = trfApplyDate;
      }

      public void setAllowWithdrawNum(Integer allowWithdrawNum) {
         this.allowWithdrawNum = allowWithdrawNum;
      }

      public void setDcChangeBuyDate(String dcChangeBuyDate) {
         this.dcChangeBuyDate = dcChangeBuyDate;
      }

      public void setOpenStartDate(String openStartDate) {
         this.openStartDate = openStartDate;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400100026Out.SubArray)) {
            return false;
         } else {
            Core1400100026Out.SubArray other = (Core1400100026Out.SubArray)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label539: {
                  Object this$baseAcctNo = this.getBaseAcctNo();
                  Object other$baseAcctNo = other.getBaseAcctNo();
                  if (this$baseAcctNo == null) {
                     if (other$baseAcctNo == null) {
                        break label539;
                     }
                  } else if (this$baseAcctNo.equals(other$baseAcctNo)) {
                     break label539;
                  }

                  return false;
               }

               Object this$prodType = this.getProdType();
               Object other$prodType = other.getProdType();
               if (this$prodType == null) {
                  if (other$prodType != null) {
                     return false;
                  }
               } else if (!this$prodType.equals(other$prodType)) {
                  return false;
               }

               Object this$prodDesc = this.getProdDesc();
               Object other$prodDesc = other.getProdDesc();
               if (this$prodDesc == null) {
                  if (other$prodDesc != null) {
                     return false;
                  }
               } else if (!this$prodDesc.equals(other$prodDesc)) {
                  return false;
               }

               label518: {
                  Object this$acctSeqNo = this.getAcctSeqNo();
                  Object other$acctSeqNo = other.getAcctSeqNo();
                  if (this$acctSeqNo == null) {
                     if (other$acctSeqNo == null) {
                        break label518;
                     }
                  } else if (this$acctSeqNo.equals(other$acctSeqNo)) {
                     break label518;
                  }

                  return false;
               }

               label511: {
                  Object this$acctCcy = this.getAcctCcy();
                  Object other$acctCcy = other.getAcctCcy();
                  if (this$acctCcy == null) {
                     if (other$acctCcy == null) {
                        break label511;
                     }
                  } else if (this$acctCcy.equals(other$acctCcy)) {
                     break label511;
                  }

                  return false;
               }

               label504: {
                  Object this$acctStatus = this.getAcctStatus();
                  Object other$acctStatus = other.getAcctStatus();
                  if (this$acctStatus == null) {
                     if (other$acctStatus == null) {
                        break label504;
                     }
                  } else if (this$acctStatus.equals(other$acctStatus)) {
                     break label504;
                  }

                  return false;
               }

               Object this$acctName = this.getAcctName();
               Object other$acctName = other.getAcctName();
               if (this$acctName == null) {
                  if (other$acctName != null) {
                     return false;
                  }
               } else if (!this$acctName.equals(other$acctName)) {
                  return false;
               }

               label490: {
                  Object this$effectDate = this.getEffectDate();
                  Object other$effectDate = other.getEffectDate();
                  if (this$effectDate == null) {
                     if (other$effectDate == null) {
                        break label490;
                     }
                  } else if (this$effectDate.equals(other$effectDate)) {
                     break label490;
                  }

                  return false;
               }

               Object this$captDate = this.getCaptDate();
               Object other$captDate = other.getCaptDate();
               if (this$captDate == null) {
                  if (other$captDate != null) {
                     return false;
                  }
               } else if (!this$captDate.equals(other$captDate)) {
                  return false;
               }

               label476: {
                  Object this$acctCloseDate = this.getAcctCloseDate();
                  Object other$acctCloseDate = other.getAcctCloseDate();
                  if (this$acctCloseDate == null) {
                     if (other$acctCloseDate == null) {
                        break label476;
                     }
                  } else if (this$acctCloseDate.equals(other$acctCloseDate)) {
                     break label476;
                  }

                  return false;
               }

               Object this$exchRate = this.getExchRate();
               Object other$exchRate = other.getExchRate();
               if (this$exchRate == null) {
                  if (other$exchRate != null) {
                     return false;
                  }
               } else if (!this$exchRate.equals(other$exchRate)) {
                  return false;
               }

               Object this$actualRate = this.getActualRate();
               Object other$actualRate = other.getActualRate();
               if (this$actualRate == null) {
                  if (other$actualRate != null) {
                     return false;
                  }
               } else if (!this$actualRate.equals(other$actualRate)) {
                  return false;
               }

               label455: {
                  Object this$floatRate = this.getFloatRate();
                  Object other$floatRate = other.getFloatRate();
                  if (this$floatRate == null) {
                     if (other$floatRate == null) {
                        break label455;
                     }
                  } else if (this$floatRate.equals(other$floatRate)) {
                     break label455;
                  }

                  return false;
               }

               label448: {
                  Object this$isIndividual = this.getIsIndividual();
                  Object other$isIndividual = other.getIsIndividual();
                  if (this$isIndividual == null) {
                     if (other$isIndividual == null) {
                        break label448;
                     }
                  } else if (this$isIndividual.equals(other$isIndividual)) {
                     break label448;
                  }

                  return false;
               }

               Object this$acctType = this.getAcctType();
               Object other$acctType = other.getAcctType();
               if (this$acctType == null) {
                  if (other$acctType != null) {
                     return false;
                  }
               } else if (!this$acctType.equals(other$acctType)) {
                  return false;
               }

               Object this$balType = this.getBalType();
               Object other$balType = other.getBalType();
               if (this$balType == null) {
                  if (other$balType != null) {
                     return false;
                  }
               } else if (!this$balType.equals(other$balType)) {
                  return false;
               }

               label427: {
                  Object this$acctBalance = this.getAcctBalance();
                  Object other$acctBalance = other.getAcctBalance();
                  if (this$acctBalance == null) {
                     if (other$acctBalance == null) {
                        break label427;
                     }
                  } else if (this$acctBalance.equals(other$acctBalance)) {
                     break label427;
                  }

                  return false;
               }

               Object this$availableAmt = this.getAvailableAmt();
               Object other$availableAmt = other.getAvailableAmt();
               if (this$availableAmt == null) {
                  if (other$availableAmt != null) {
                     return false;
                  }
               } else if (!this$availableAmt.equals(other$availableAmt)) {
                  return false;
               }

               Object this$pldAmount = this.getPldAmount();
               Object other$pldAmount = other.getPldAmount();
               if (this$pldAmount == null) {
                  if (other$pldAmount != null) {
                     return false;
                  }
               } else if (!this$pldAmount.equals(other$pldAmount)) {
                  return false;
               }

               label406: {
                  Object this$term = this.getTerm();
                  Object other$term = other.getTerm();
                  if (this$term == null) {
                     if (other$term == null) {
                        break label406;
                     }
                  } else if (this$term.equals(other$term)) {
                     break label406;
                  }

                  return false;
               }

               label399: {
                  Object this$termType = this.getTermType();
                  Object other$termType = other.getTermType();
                  if (this$termType == null) {
                     if (other$termType == null) {
                        break label399;
                     }
                  } else if (this$termType.equals(other$termType)) {
                     break label399;
                  }

                  return false;
               }

               label392: {
                  Object this$docType = this.getDocType();
                  Object other$docType = other.getDocType();
                  if (this$docType == null) {
                     if (other$docType == null) {
                        break label392;
                     }
                  } else if (this$docType.equals(other$docType)) {
                     break label392;
                  }

                  return false;
               }

               Object this$voucherNo = this.getVoucherNo();
               Object other$voucherNo = other.getVoucherNo();
               if (this$voucherNo == null) {
                  if (other$voucherNo != null) {
                     return false;
                  }
               } else if (!this$voucherNo.equals(other$voucherNo)) {
                  return false;
               }

               label378: {
                  Object this$voucherPrefix = this.getVoucherPrefix();
                  Object other$voucherPrefix = other.getVoucherPrefix();
                  if (this$voucherPrefix == null) {
                     if (other$voucherPrefix == null) {
                        break label378;
                     }
                  } else if (this$voucherPrefix.equals(other$voucherPrefix)) {
                     break label378;
                  }

                  return false;
               }

               Object this$autoRenewRollover = this.getAutoRenewRollover();
               Object other$autoRenewRollover = other.getAutoRenewRollover();
               if (this$autoRenewRollover == null) {
                  if (other$autoRenewRollover != null) {
                     return false;
                  }
               } else if (!this$autoRenewRollover.equals(other$autoRenewRollover)) {
                  return false;
               }

               label364: {
                  Object this$allowBalance = this.getAllowBalance();
                  Object other$allowBalance = other.getAllowBalance();
                  if (this$allowBalance == null) {
                     if (other$allowBalance == null) {
                        break label364;
                     }
                  } else if (this$allowBalance.equals(other$allowBalance)) {
                     break label364;
                  }

                  return false;
               }

               Object this$matureDate = this.getMatureDate();
               Object other$matureDate = other.getMatureDate();
               if (this$matureDate == null) {
                  if (other$matureDate != null) {
                     return false;
                  }
               } else if (!this$matureDate.equals(other$matureDate)) {
                  return false;
               }

               Object this$preWithdrawFlag = this.getPreWithdrawFlag();
               Object other$preWithdrawFlag = other.getPreWithdrawFlag();
               if (this$preWithdrawFlag == null) {
                  if (other$preWithdrawFlag != null) {
                     return false;
                  }
               } else if (!this$preWithdrawFlag.equals(other$preWithdrawFlag)) {
                  return false;
               }

               label343: {
                  Object this$partWithdrawFlag = this.getPartWithdrawFlag();
                  Object other$partWithdrawFlag = other.getPartWithdrawFlag();
                  if (this$partWithdrawFlag == null) {
                     if (other$partWithdrawFlag == null) {
                        break label343;
                     }
                  } else if (this$partWithdrawFlag.equals(other$partWithdrawFlag)) {
                     break label343;
                  }

                  return false;
               }

               label336: {
                  Object this$partWithdrawNum = this.getPartWithdrawNum();
                  Object other$partWithdrawNum = other.getPartWithdrawNum();
                  if (this$partWithdrawNum == null) {
                     if (other$partWithdrawNum == null) {
                        break label336;
                     }
                  } else if (this$partWithdrawNum.equals(other$partWithdrawNum)) {
                     break label336;
                  }

                  return false;
               }

               Object this$directionChargeIntFlag = this.getDirectionChargeIntFlag();
               Object other$directionChargeIntFlag = other.getDirectionChargeIntFlag();
               if (this$directionChargeIntFlag == null) {
                  if (other$directionChargeIntFlag != null) {
                     return false;
                  }
               } else if (!this$directionChargeIntFlag.equals(other$directionChargeIntFlag)) {
                  return false;
               }

               Object this$acctOpenStartDate = this.getAcctOpenStartDate();
               Object other$acctOpenStartDate = other.getAcctOpenStartDate();
               if (this$acctOpenStartDate == null) {
                  if (other$acctOpenStartDate != null) {
                     return false;
                  }
               } else if (!this$acctOpenStartDate.equals(other$acctOpenStartDate)) {
                  return false;
               }

               label315: {
                  Object this$settleArray = this.getSettleArray();
                  Object other$settleArray = other.getSettleArray();
                  if (this$settleArray == null) {
                     if (other$settleArray == null) {
                        break label315;
                     }
                  } else if (this$settleArray.equals(other$settleArray)) {
                     break label315;
                  }

                  return false;
               }

               Object this$payIntType = this.getPayIntType();
               Object other$payIntType = other.getPayIntType();
               if (this$payIntType == null) {
                  if (other$payIntType != null) {
                     return false;
                  }
               } else if (!this$payIntType.equals(other$payIntType)) {
                  return false;
               }

               Object this$stageCode = this.getStageCode();
               Object other$stageCode = other.getStageCode();
               if (this$stageCode == null) {
                  if (other$stageCode != null) {
                     return false;
                  }
               } else if (!this$stageCode.equals(other$stageCode)) {
                  return false;
               }

               label294: {
                  Object this$trfFlag = this.getTrfFlag();
                  Object other$trfFlag = other.getTrfFlag();
                  if (this$trfFlag == null) {
                     if (other$trfFlag == null) {
                        break label294;
                     }
                  } else if (this$trfFlag.equals(other$trfFlag)) {
                     break label294;
                  }

                  return false;
               }

               label287: {
                  Object this$intStartDate = this.getIntStartDate();
                  Object other$intStartDate = other.getIntStartDate();
                  if (this$intStartDate == null) {
                     if (other$intStartDate == null) {
                        break label287;
                     }
                  } else if (this$intStartDate.equals(other$intStartDate)) {
                     break label287;
                  }

                  return false;
               }

               label280: {
                  Object this$precontractDate = this.getPrecontractDate();
                  Object other$precontractDate = other.getPrecontractDate();
                  if (this$precontractDate == null) {
                     if (other$precontractDate == null) {
                        break label280;
                     }
                  } else if (this$precontractDate.equals(other$precontractDate)) {
                     break label280;
                  }

                  return false;
               }

               Object this$trfDate = this.getTrfDate();
               Object other$trfDate = other.getTrfDate();
               if (this$trfDate == null) {
                  if (other$trfDate != null) {
                     return false;
                  }
               } else if (!this$trfDate.equals(other$trfDate)) {
                  return false;
               }

               label266: {
                  Object this$stageCodeDesc = this.getStageCodeDesc();
                  Object other$stageCodeDesc = other.getStageCodeDesc();
                  if (this$stageCodeDesc == null) {
                     if (other$stageCodeDesc == null) {
                        break label266;
                     }
                  } else if (this$stageCodeDesc.equals(other$stageCodeDesc)) {
                     break label266;
                  }

                  return false;
               }

               Object this$trfApplyDate = this.getTrfApplyDate();
               Object other$trfApplyDate = other.getTrfApplyDate();
               if (this$trfApplyDate == null) {
                  if (other$trfApplyDate != null) {
                     return false;
                  }
               } else if (!this$trfApplyDate.equals(other$trfApplyDate)) {
                  return false;
               }

               label252: {
                  Object this$allowWithdrawNum = this.getAllowWithdrawNum();
                  Object other$allowWithdrawNum = other.getAllowWithdrawNum();
                  if (this$allowWithdrawNum == null) {
                     if (other$allowWithdrawNum == null) {
                        break label252;
                     }
                  } else if (this$allowWithdrawNum.equals(other$allowWithdrawNum)) {
                     break label252;
                  }

                  return false;
               }

               Object this$dcChangeBuyDate = this.getDcChangeBuyDate();
               Object other$dcChangeBuyDate = other.getDcChangeBuyDate();
               if (this$dcChangeBuyDate == null) {
                  if (other$dcChangeBuyDate != null) {
                     return false;
                  }
               } else if (!this$dcChangeBuyDate.equals(other$dcChangeBuyDate)) {
                  return false;
               }

               Object this$openStartDate = this.getOpenStartDate();
               Object other$openStartDate = other.getOpenStartDate();
               if (this$openStartDate == null) {
                  if (other$openStartDate != null) {
                     return false;
                  }
               } else if (!this$openStartDate.equals(other$openStartDate)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400100026Out.SubArray;
      }
      public String toString() {
         return "Core1400100026Out.SubArray(baseAcctNo=" + this.getBaseAcctNo() + ", prodType=" + this.getProdType() + ", prodDesc=" + this.getProdDesc() + ", acctSeqNo=" + this.getAcctSeqNo() + ", acctCcy=" + this.getAcctCcy() + ", acctStatus=" + this.getAcctStatus() + ", acctName=" + this.getAcctName() + ", effectDate=" + this.getEffectDate() + ", captDate=" + this.getCaptDate() + ", acctCloseDate=" + this.getAcctCloseDate() + ", exchRate=" + this.getExchRate() + ", actualRate=" + this.getActualRate() + ", floatRate=" + this.getFloatRate() + ", isIndividual=" + this.getIsIndividual() + ", acctType=" + this.getAcctType() + ", balType=" + this.getBalType() + ", acctBalance=" + this.getAcctBalance() + ", availableAmt=" + this.getAvailableAmt() + ", pldAmount=" + this.getPldAmount() + ", term=" + this.getTerm() + ", termType=" + this.getTermType() + ", docType=" + this.getDocType() + ", voucherNo=" + this.getVoucherNo() + ", voucherPrefix=" + this.getVoucherPrefix() + ", autoRenewRollover=" + this.getAutoRenewRollover() + ", allowBalance=" + this.getAllowBalance() + ", matureDate=" + this.getMatureDate() + ", preWithdrawFlag=" + this.getPreWithdrawFlag() + ", partWithdrawFlag=" + this.getPartWithdrawFlag() + ", partWithdrawNum=" + this.getPartWithdrawNum() + ", directionChargeIntFlag=" + this.getDirectionChargeIntFlag() + ", acctOpenStartDate=" + this.getAcctOpenStartDate() + ", settleArray=" + this.getSettleArray() + ", payIntType=" + this.getPayIntType() + ", stageCode=" + this.getStageCode() + ", trfFlag=" + this.getTrfFlag() + ", intStartDate=" + this.getIntStartDate() + ", precontractDate=" + this.getPrecontractDate() + ", trfDate=" + this.getTrfDate() + ", stageCodeDesc=" + this.getStageCodeDesc() + ", trfApplyDate=" + this.getTrfApplyDate() + ", allowWithdrawNum=" + this.getAllowWithdrawNum() + ", dcChangeBuyDate=" + this.getDcChangeBuyDate() + ", openStartDate=" + this.getOpenStartDate() + ")";
      }

      public static class SettleArray {
         @V(
            desc = "结算账户分类",
            notNull = false,
            length = "3",
            remark = "结算账户分类",
            maxSize = 3
         )
         private String settleAcctClass;
         @V(
            desc = "结算账号",
            notNull = false,
            length = "50",
            remark = "结算账号",
            maxSize = 50
         )
         private String settleBaseAcctNo;
         @V(
            desc = "利息返还结算账户产品类型",
            notNull = false,
            length = "20",
            remark = "利息返还结算账户产品类型",
            maxSize = 20
         )
         private String settleAcctProdType;
         @V(
            desc = "结算账户币种",
            notNull = false,
            length = "3",
            remark = "结算账户币种",
            maxSize = 3
         )
         private String settleAcctCcy;
         @V(
            desc = "结算账户序号",
            notNull = false,
            length = "5",
            remark = "结算账户序号",
            maxSize = 5
         )
         private String settleAcctSeqNo;
         @V(
            desc = "结算金额",
            notNull = false,
            length = "17",
            remark = "结算金额",
            decimalLength = 2,
            precision = 2
         )
         private BigDecimal settleAmt;

         public String getSettleAcctClass() {
            return this.settleAcctClass;
         }

         public String getSettleBaseAcctNo() {
            return this.settleBaseAcctNo;
         }

         public String getSettleAcctProdType() {
            return this.settleAcctProdType;
         }

         public String getSettleAcctCcy() {
            return this.settleAcctCcy;
         }

         public String getSettleAcctSeqNo() {
            return this.settleAcctSeqNo;
         }

         public BigDecimal getSettleAmt() {
            return this.settleAmt;
         }

         public void setSettleAcctClass(String settleAcctClass) {
            this.settleAcctClass = settleAcctClass;
         }

         public void setSettleBaseAcctNo(String settleBaseAcctNo) {
            this.settleBaseAcctNo = settleBaseAcctNo;
         }

         public void setSettleAcctProdType(String settleAcctProdType) {
            this.settleAcctProdType = settleAcctProdType;
         }

         public void setSettleAcctCcy(String settleAcctCcy) {
            this.settleAcctCcy = settleAcctCcy;
         }

         public void setSettleAcctSeqNo(String settleAcctSeqNo) {
            this.settleAcctSeqNo = settleAcctSeqNo;
         }

         public void setSettleAmt(BigDecimal settleAmt) {
            this.settleAmt = settleAmt;
         }

         public boolean equals(Object o) {
            if (o == this) {
               return true;
            } else if (!(o instanceof Core1400100026Out.SubArray.SettleArray)) {
               return false;
            } else {
               Core1400100026Out.SubArray.SettleArray other = (Core1400100026Out.SubArray.SettleArray)o;
               if (!other.canEqual(this)) {
                  return false;
               } else {
                  Object this$settleAcctClass = this.getSettleAcctClass();
                  Object other$settleAcctClass = other.getSettleAcctClass();
                  if (this$settleAcctClass == null) {
                     if (other$settleAcctClass != null) {
                        return false;
                     }
                  } else if (!this$settleAcctClass.equals(other$settleAcctClass)) {
                     return false;
                  }

                  Object this$settleBaseAcctNo = this.getSettleBaseAcctNo();
                  Object other$settleBaseAcctNo = other.getSettleBaseAcctNo();
                  if (this$settleBaseAcctNo == null) {
                     if (other$settleBaseAcctNo != null) {
                        return false;
                     }
                  } else if (!this$settleBaseAcctNo.equals(other$settleBaseAcctNo)) {
                     return false;
                  }

                  Object this$settleAcctProdType = this.getSettleAcctProdType();
                  Object other$settleAcctProdType = other.getSettleAcctProdType();
                  if (this$settleAcctProdType == null) {
                     if (other$settleAcctProdType != null) {
                        return false;
                     }
                  } else if (!this$settleAcctProdType.equals(other$settleAcctProdType)) {
                     return false;
                  }

                  label62: {
                     Object this$settleAcctCcy = this.getSettleAcctCcy();
                     Object other$settleAcctCcy = other.getSettleAcctCcy();
                     if (this$settleAcctCcy == null) {
                        if (other$settleAcctCcy == null) {
                           break label62;
                        }
                     } else if (this$settleAcctCcy.equals(other$settleAcctCcy)) {
                        break label62;
                     }

                     return false;
                  }

                  label55: {
                     Object this$settleAcctSeqNo = this.getSettleAcctSeqNo();
                     Object other$settleAcctSeqNo = other.getSettleAcctSeqNo();
                     if (this$settleAcctSeqNo == null) {
                        if (other$settleAcctSeqNo == null) {
                           break label55;
                        }
                     } else if (this$settleAcctSeqNo.equals(other$settleAcctSeqNo)) {
                        break label55;
                     }

                     return false;
                  }

                  Object this$settleAmt = this.getSettleAmt();
                  Object other$settleAmt = other.getSettleAmt();
                  if (this$settleAmt == null) {
                     if (other$settleAmt != null) {
                        return false;
                     }
                  } else if (!this$settleAmt.equals(other$settleAmt)) {
                     return false;
                  }

                  return true;
               }
            }
         }

         protected boolean canEqual(Object other) {
            return other instanceof Core1400100026Out.SubArray.SettleArray;
         }
         public String toString() {
            return "Core1400100026Out.SubArray.SettleArray(settleAcctClass=" + this.getSettleAcctClass() + ", settleBaseAcctNo=" + this.getSettleBaseAcctNo() + ", settleAcctProdType=" + this.getSettleAcctProdType() + ", settleAcctCcy=" + this.getSettleAcctCcy() + ", settleAcctSeqNo=" + this.getSettleAcctSeqNo() + ", settleAmt=" + this.getSettleAmt() + ")";
         }
      }
   }
}
