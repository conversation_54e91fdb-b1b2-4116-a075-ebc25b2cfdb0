package com.dcits.ensemble.rb.api.model.mbsdcore;

import com.dcits.comet.flow.annotation.V;
import com.dcits.ensemble.base.data.EnsRequest;
import com.dcits.gravity.api.annotation.MessageIn;
import java.io.Serializable;

@MessageIn
public class Core1400023403In extends EnsRequest {
   private static final long serialVersionUID = 1L;
   @V(
      desc = "body"
   )
   private Core1400023403In.Body body;

   public Core1400023403In.Body getBody() {
      return this.body;
   }

   public void setBody(Core1400023403In.Body body) {
      this.body = body;
   }

   public String toString() {
      return "Core1400023403In(body=" + this.getBody() + ")";
   }

   public boolean equals(Object o) {
      if (o == this) {
         return true;
      } else if (!(o instanceof Core1400023403In)) {
         return false;
      } else {
         Core1400023403In other = (Core1400023403In)o;
         if (!other.canEqual(this)) {
            return false;
         } else if (!super.equals(o)) {
            return false;
         } else {
            Object this$body = this.getBody();
            Object other$body = other.getBody();
            if (this$body == null) {
               if (other$body != null) {
                  return false;
               }
            } else if (!this$body.equals(other$body)) {
               return false;
            }

            return true;
         }
      }
   }

   protected boolean canEqual(Object other) {
      return other instanceof Core1400023403In;
   }
   public static class Body implements Serializable {
      private static final long serialVersionUID = 1L;
      @V(
         desc = "所属机构号",
         notNull = false,
         length = "50",
         remark = "所属机构号",
         maxSize = 50
      )
      private String branch;
      @V(
         desc = "交易柜员",
         notNull = false,
         length = "30",
         remark = "交易柜员",
         maxSize = 30
      )
      private String userId;
      @V(
         desc = "交易日期",
         notNull = false,
         remark = "交易日期"
      )
      private String tranDate;
      @V(
         desc = "票据状态",
         notNull = false,
         length = "2",
         inDesc = "00-录入,01-签发,02-兑付,03-退回,04-挂失,05-解挂,06-删除,08-挂失止付,09-公示催告,11-未复核,12-已复核,13-已打印,14-已签章核对,15-已签发冲正,16-已移存",
         remark = "票据状态00-录入,01-签发,02-兑付,03-退回,04-挂失,05-解挂,06-删除,08-挂失止付,09-公示催告",
         restraint = "00-录入,01-签发,02-兑付,03-退回,04-挂失,05-解挂,06-删除,08-挂失止付,09-公示催告",
         maxSize = 2
      )
      private String billStatus;

      public String getBranch() {
         return this.branch;
      }

      public String getUserId() {
         return this.userId;
      }

      public String getTranDate() {
         return this.tranDate;
      }

      public String getBillStatus() {
         return this.billStatus;
      }

      public void setBranch(String branch) {
         this.branch = branch;
      }

      public void setUserId(String userId) {
         this.userId = userId;
      }

      public void setTranDate(String tranDate) {
         this.tranDate = tranDate;
      }

      public void setBillStatus(String billStatus) {
         this.billStatus = billStatus;
      }

      public boolean equals(Object o) {
         if (o == this) {
            return true;
         } else if (!(o instanceof Core1400023403In.Body)) {
            return false;
         } else {
            Core1400023403In.Body other = (Core1400023403In.Body)o;
            if (!other.canEqual(this)) {
               return false;
            } else {
               label59: {
                  Object this$branch = this.getBranch();
                  Object other$branch = other.getBranch();
                  if (this$branch == null) {
                     if (other$branch == null) {
                        break label59;
                     }
                  } else if (this$branch.equals(other$branch)) {
                     break label59;
                  }

                  return false;
               }

               Object this$userId = this.getUserId();
               Object other$userId = other.getUserId();
               if (this$userId == null) {
                  if (other$userId != null) {
                     return false;
                  }
               } else if (!this$userId.equals(other$userId)) {
                  return false;
               }

               Object this$tranDate = this.getTranDate();
               Object other$tranDate = other.getTranDate();
               if (this$tranDate == null) {
                  if (other$tranDate != null) {
                     return false;
                  }
               } else if (!this$tranDate.equals(other$tranDate)) {
                  return false;
               }

               Object this$billStatus = this.getBillStatus();
               Object other$billStatus = other.getBillStatus();
               if (this$billStatus == null) {
                  if (other$billStatus != null) {
                     return false;
                  }
               } else if (!this$billStatus.equals(other$billStatus)) {
                  return false;
               }

               return true;
            }
         }
      }

      protected boolean canEqual(Object other) {
         return other instanceof Core1400023403In.Body;
      }
      public String toString() {
         return "Core1400023403In.Body(branch=" + this.getBranch() + ", userId=" + this.getUserId() + ", tranDate=" + this.getTranDate() + ", billStatus=" + this.getBillStatus() + ")";
      }
   }
}
